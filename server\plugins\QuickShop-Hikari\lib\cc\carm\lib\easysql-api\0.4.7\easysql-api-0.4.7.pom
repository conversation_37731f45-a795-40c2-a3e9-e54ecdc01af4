<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>cc.carm.lib</groupId>
        <artifactId>easysql-parent</artifactId>
        <version>0.4.7</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <properties>
        <maven.compiler.source>${jdk.version}</maven.compiler.source>
        <maven.compiler.target>${jdk.version}</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.encoding>UTF-8</maven.compiler.encoding>
    </properties>

    <artifactId>easysql-api</artifactId>
    <packaging>jar</packaging>

    <name>EasySQL-API</name>
    <description>EasySQL的接口部分。用于打包到公共项目的API中，避免项目过大。</description>
    <url>https://github.com/CarmJos/EasySQL</url>

    <developers>
        <developer>
            <id>CarmJos</id>
            <name>Carm Jos</name>
            <email><EMAIL></email>
            <url>https://www.carm.cc</url>
            <roles>
                <role>Main Developer</role>
            </roles>
        </developer>
    </developers>

    <licenses>
        <license>
            <name>The MIT License</name>
            <url>https://opensource.org/licenses/MIT</url>
        </license>
    </licenses>

    <issueManagement>
        <system>GitHub Issues</system>
        <url>https://github.com/CarmJos/EasySQL/issues</url>
    </issueManagement>

    <ciManagement>
        <system>GitHub Actions</system>
        <url>https://github.com/CarmJos/EasySQL/actions/workflows/maven.yml</url>
    </ciManagement>

    <dependencies>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>1.7.36</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
            </plugin>
        </plugins>
    </build>


</project>