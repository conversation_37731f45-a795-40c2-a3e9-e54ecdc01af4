{"skinsrestorer.prefix_format": "<yellow>[<dark_green>SkinsRestorer</dark_green>] <message>", "skinsrestorer.help_skin": "更改您自己的皮肤。", "skinsrestorer.help_skins": "打开皮肤菜单", "skinsrestorer.help_sr": "SkinsRestorer的管理员指令", "skinsrestorer.help_skin_help": "显示此帮助命令。", "skinsrestorer.help_skin_set": "更改您的皮肤。", "skinsrestorer.help_skin_set_other": "为目标玩家设置皮肤。", "skinsrestorer.help_skin_set_url": "从URL更改您的皮肤。", "skinsrestorer.help_skin_clear": "清除你的皮肤。", "skinsrestorer.help_skin_clear_other": "清除目标玩家的皮肤。", "skinsrestorer.help_skin_random": "提供一个随机的皮肤。", "skinsrestorer.help_skin_random_other": "为目标玩家设置随机皮肤。", "skinsrestorer.help_skin_search": "搜索你想要的皮肤", "skinsrestorer.help_skin_edit": "在线编辑您当前的皮肤。", "skinsrestorer.help_skin_update": "更新你的皮肤。", "skinsrestorer.help_skin_update_other": "更新目标玩家的皮肤。", "skinsrestorer.help_skin_undo": "将您的皮肤还原到上一个皮肤。", "skinsrestorer.help_skin_undo_other": "将目标玩家的皮肤还原到前一个皮肤。", "skinsrestorer.help_skin_favourite": "将您的皮肤保存为收藏。", "skinsrestorer.help_skin_favourite_other": "将目标玩家的皮肤保存为收藏。", "skinsrestorer.help_skull": "给予你一个头颅。", "skinsrestorer.help_skull_help": "SkinsRestorer关于头颅的指令。", "skinsrestorer.help_skull_get": "给予你一个头颅。", "skinsrestorer.help_skull_get_other": "给予另一个玩家一个头颅。", "skinsrestorer.help_skull_get_url": "基于皮肤URL给予头颅。", "skinsrestorer.help_skull_random": "给予你一个随机头颅。", "skinsrestorer.help_skull_random_other": "给予另一个玩家一个随机的头颅。", "skinsrestorer.help_sr_reload": "重新加载配置文件。", "skinsrestorer.help_sr_status": "检查需要的插件 API 服务。", "skinsrestorer.help_sr_drop": "从数据库中删除玩家或皮肤数据。", "skinsrestorer.help_sr_info": "显示关于玩家或皮肤的信息。", "skinsrestorer.help_sr_apply_skin": "为目标玩家重新应用皮肤。", "skinsrestorer.help_sr_create_custom": "创建一个服务器范围内的自定义皮肤。", "skinsrestorer.help_sr_purge_old_data": "清除超过 x 天前的旧皮肤数据。", "skinsrestorer.help_sr_dump": "上传支持数据到 bytebin.lucko.me。", "skinsrestorer.success_generic": "<dark_green><message>", "skinsrestorer.info_use_quotes": "必须引用URL。例如： <yellow>/skin set \"https://example.com/skin.png\"</yellow> (您可以按Tab键自动填写)", "skinsrestorer.success_skin_change": "你的皮肤已改变。", "skinsrestorer.success_skin_change_other": "你改变了 <yellow><name></yellow> 的皮肤。", "skinsrestorer.success_skin_undo": "皮肤<yellow><skin></yellow>已从 <yellow><timestamp></yellow> 恢复。", "skinsrestorer.success_skin_undo_other": "<yellow><name></yellow>的皮肤<yellow><skin></yellow>已从 <yellow><timestamp></yellow> 恢复。", "skinsrestorer.success_skin_favourite": "您的皮肤 <yellow><skin></yellow> 已加入收藏。", "skinsrestorer.success_skin_favourite_other": "<yellow><name></yellow>的皮肤 <yellow><skin></yellow> 已加入收藏。", "skinsrestorer.success_skin_unfavourite": "您在 <yellow><timestamp></yellow> 收藏的皮肤 <yellow><skin></yellow> 已被取消收藏。", "skinsrestorer.success_skin_unfavourite_other": "<yellow><name></yellow>的在<yellow><timestamp></yellow>收藏的皮肤 <yellow><skin></yellow> 已被取消收藏。", "skinsrestorer.success_skin_clear": "你的皮肤已清除。", "skinsrestorer.success_skin_clear_other": "玩家 <yellow><name></yellow> 的皮肤已清除。", "skinsrestorer.success_updating_skin": "你的皮肤已更新。", "skinsrestorer.success_updating_skin_other": "玩家 <yellow><name></yellow> 的皮肤已更新。", "skinsrestorer.success_skull_get": "你收到了一个头颅。", "skinsrestorer.success_skull_get_other": "你给予<yellow><name></yellow> 了一个头颅。", "skinsrestorer.success_admin_applyskin": "玩家皮肤已刷新！", "skinsrestorer.success_admin_createcustom": "已创建皮肤 <yellow><skin></yellow> ！", "skinsrestorer.success_admin_setcustomname": "皮肤 <yellow></yellow><skin> 的名称已设置为 <yellow><display_name></yellow>", "skinsrestorer.success_admin_drop": "<type> 数据已丢失到 <target>", "skinsrestorer.success_admin_reload": "配置文件和区域设置已重新加载。", "skinsrestorer.success_history_line": "<dark_green>-<hover:show_text:'<dark_green>点击使用<yellow><skin></yellow>'><click:run_command:/skin set \"<skin_id>\"><yellow><skin></yellow></click></hover> 在 <yellow><timestamp></yellow> 时", "skinsrestorer.success_favourites_line": "<dark_green>-<hover:show_text:'<dark_green>点击使用<yellow><skin></yellow>'><click:run_command:/skin set \"<skin_id>\"><yellow><skin></yellow></click></hover> 在 <yellow><timestamp></yellow> 时", "skinsrestorer.error_generic": "<dark_red>错误<dark_gray>: <red><message>", "skinsrestorer.error_generic_skin": "在请求数据时出错，请稍后再试！", "skinsrestorer.error_no_undo": "你没有皮肤可以还原！", "skinsrestorer.error_no_skin_to_favourite": "您没有皮肤可以设置为收藏！", "skinsrestorer.error_skin_disabled": "&c你没有权限去设置这个皮肤。\";", "skinsrestorer.error_skinurl_disallowed": "错误: 该域名没有被管理员列入白名单中。", "skinsrestorer.error_updating_skin": "在更新你的皮肤时出错，请稍后再试！", "skinsrestorer.error_updating_url": "你不能更新自定义链接的皮肤！ <newline><red>请使用 /skin url 命令", "skinsrestorer.error_updating_customskin": "不能更新自定义皮肤。", "skinsrestorer.error_invalid_urlskin": "无效的皮肤URL或格式，<newline><red>请尝试将皮肤上传到Imgur后右键选择“复制图片地址”<newline><red>如需指南，<red><underlined><hover:show_text:'<dark_green>点击即可打开'><click:open_url:'https://skinsrestorer.net/skinurl'>https://skinsrestorer.net/skinurl</click></hover></underlined>。", "skinsrestorer.error_admin_applyskin": "无法刷新玩家的皮肤！", "skinsrestorer.error_ms_full": "通过MineSkin API 上传你的皮肤时超时。请稍后再试。", "skinsrestorer.error_ms_api_failed": "MineSkin API 过载，请稍后再试！", "skinsrestorer.error_ms_api_key_invalid": "无效的Mineskin API 密钥!, 请与服务器所有者联系！", "skinsrestorer.error_ms_unknown": "未知的MineSkin 错误！", "skinsrestorer.error_no_history": "你没有皮肤历史记录！", "skinsrestorer.error_no_favourites": "你没有收藏的皮肤！", "skinsrestorer.error_player_refresh_no_mapping": "无法刷新你的皮肤，因为 SkinsRestorer 不支持此 Minecraft 版本。请通知服务器管理员更新 SkinsRestorer 插件。", "skinsrestorer.not_connected_to_server": "<red>您没有连接到任何服务器。", "skinsrestorer.divider": "<dark_aqua>----------------------------------------------", "skinsrestorer.admincommand_status_checking": "<gray>正在检查SR所需的服务以正常工作...", "skinsrestorer.admincommand_status_uuid_api": "<gray>有效的 UUID API：<count>/<total>", "skinsrestorer.admincommand_status_profile_api": "<gray>有效的配置 API：<count>/<total>", "skinsrestorer.admincommand_status_working": "<green>此插件目前处于工作状态。", "skinsrestorer.admincommand_status_degraded": "<green>该插件处于降级状态，某些功能可能无法完全运行。", "skinsrestorer.admincommand_status_broken": "<red>插件目前无法获取新皮肤。<newline> 连接可能因防火墙而被阻止。<newline> 请访问 https://skinsresturer.net/firewall 获取更多信息", "skinsrestorer.admincommand_status_firewall": "<red>由于防火墙，连接可能被阻止。<newline>请阅读 https://skinsrestorer.net/firewall 了解更多信息。", "skinsrestorer.admincommand_status_summary_server": "<gray>服务器: <gold><version>", "skinsrestorer.admincommand_status_summary_proxymode": "<gray>代理模式: <gold><proxy_mode>", "skinsrestorer.admincommand_status_summary_commit": "<gray>提交: <gold><hash>", "skinsrestorer.admincommand_drop_player_not_found": "<red>未找到玩家 <yellow><player></yellow>。", "skinsrestorer.admincommand_drop_skin_not_found": "<red>未找到皮肤 <yellow><skin></yellow>。", "skinsrestorer.admincommand_drop_uuid_error": "<red>我们无法连接到Mojang以获取玩家 UUID", "skinsrestorer.admincommand_info_checking": "<gray>正在收集请求的数据...", "skinsrestorer.admincommand_info_player": "<gray>玩家UUID: <gold><uuid><newline><gray>皮肤标识符: <gold><identifier><newline><gray>皮肤变体: <gold><variant><newline><gray>皮肤类型: <gold><type>", "skinsrestorer.admincommand_info_invalid_uuid": "<red>您必须指定玩家的UUID。", "skinsrestorer.admincommand_info_no_set_skin": "<red>玩家没有明确设置皮肤。", "skinsrestorer.admincommand_info_url_skin": "<gray>URL 皮肤：<gold><click:open_url:'<url>'><url></click><newline><gray>MineSkin ID：<gold><mine_skin_id>", "skinsrestorer.admincommand_info_hardcoded_skin": "<gray>硬编码皮肤： <gold><skin>", "skinsrestorer.admincommand_info_custom_skin": "<gray>自定义皮肤: <gold><skin>", "skinsrestorer.admincommand_info_player_skin": "<gray>玩家皮肤： <gold><skin><newline><gray>时间戳： <gold><timestamp><newline><gray>到期日期： <gold><expires>", "skinsrestorer.admincommand_info_generic": "<gray>材质 URL：<gold><click:open_url:'<url>'><url></click><newline><gray>变种：<gold><variant><newline><gray>配置 UUID：<gold><uuid><newline><gray>配置名称：<gold><name><newline><gray>请求时间：<gold><request_time>", "skinsrestorer.admincommand_purgeolddata_success": "<green>成功清除旧皮肤！", "skinsrestorer.admincommand_purgeolddata_error": "<dark_red>在清理旧皮肤时发生错误！", "skinsrestorer.admincommand_dump_uploading": "<green>上传数据到 bytebin.lucko.me...", "skinsrestorer.admincommand_dump_success": "<green>上传成功！<yellow><click:open_url:'<url>'><url></click>", "skinsrestorer.admincommand_dump_error": "上传数据到 bytebin.lucko.me 时发生错误", "skinsrestorer.command_server_not_allowed_message": "<red>服务器 <server> 已禁用指令。", "skinsrestorer.command_unknown_player": "未知玩家: <name>", "skinsrestorer.command_no_targets_supplied": "没有提供目标玩家。", "skinsrestorer.player_has_no_permission_skin": "<dark_red>错误<dark_gray>：<red>你没有权限使用该皮肤。", "skinsrestorer.player_has_no_permission_url": "<dark_red>错误<dark_gray>: <red>你没有权限通过链接设置皮肤。", "skinsrestorer.not_premium": "<dark_red>错误<dark_gray>: <red>使用该名称的正版玩家不存在。", "skinsrestorer.only_allowed_on_console": "<dark_red>错误: <red>此命令只能在控制台执行。", "skinsrestorer.only_allowed_on_player": "<dark_red>错误: <red>此命令只能由玩家执行。", "skinsrestorer.invalid_player": "<dark_red>错误<dark_gray>: <red><input> 不是有效的用户名或链接。", "skinsrestorer.skin_cooldown": "<dark_red>错误<dark_gray>: <red>你需等候 <yellow><time></yellow> 秒后才能更换皮肤。", "skinsrestorer.ms_uploading_skin": "<dark_green>正在上传皮肤，请稍候...（这可能需要一些时间）", "skinsrestorer.wait_a_minute": "<dark_red>错误<dark_gray>: <red>请稍等一分钟后再请求皮肤 (访问频率限制)", "skinsrestorer.skinsmenu_open": "<dark_green>正在打开皮肤菜单", "skinsrestorer.skinsmenu_title_select": "<blue>选择菜单", "skinsrestorer.skinsmenu_title_main": "<blue>皮肤菜单 - 第 <page_number> 页", "skinsrestorer.skinsmenu_title_history": "<blue>历史菜单 - 第 <page_number> 页", "skinsrestorer.skinsmenu_title_favourites": "<blue>收藏菜单 - 第 <page_number> 页", "skinsrestorer.skinsmenu_next_page": "<green><bold>»<gray> 下一页</gray><bold> »</bold>", "skinsrestorer.skinsmenu_previous_page": "<yellow><bold>«</bold><gray> 上一页</gray> <bold>«</bold>", "skinsrestorer.skinsmenu_clear_skin": "<red><bold>[ <gray>删除皮肤</gray><bold> ]</bold>", "skinsrestorer.skinsmenu_select_skin": "<gray>点击选择该皮肤", "skinsrestorer.skinsmenu_history_lore": "<blue><time>曾使用", "skinsrestorer.skinsmenu_set_favourite_lore": "<gray>Shift + 单击添加到收藏夹", "skinsrestorer.skinsmenu_remove_favourite_lore": "<gray>Shift + 点击从收藏夹中删除", "skinsrestorer.skinsmenu_favourite_since_lore": "<blue>收藏时间：<time>", "skinsrestorer.skinsmenu_no_permission": "<red>你没有权限设置该皮肤。", "skinsrestorer.skinsmenu_main_button": "<aqua><bold>[</bold> <gray>皮肤菜单</gray> <bold>]</bold>", "skinsrestorer.skinsmenu_history_button": "<aqua><bold>[</bold> <gray>历史菜单</gray> <bold>]</bold>", "skinsrestorer.skinsmenu_favourites_button": "<aqua><bold>[</bold> <gray>收藏菜单</gray> <bold>]</bold>", "skinsrestorer.skinsmenu_back_select_button": "<yellow><bold>«</bold> <gray>菜单选择</gray> <bold>«</bold>", "skinsrestorer.skin_search_message": "<dark_green>你可以在此处找到与<green><search></green>匹配的皮肤：<newline><green><hover:show_text:'<dark_green>点击打开'><click:open_url:'https://namemc.com/minecraft-skins/tag/<search>'>https://namemc.com/minecraft-skins/tag/<search></click></hover><newline><newline><aqua>如果你没找到什么喜欢的皮肤，也可以试试在 https://namemc.com/minecraft-skins/tag 中找找看<newline>你可以通过皮肤URL设置皮肤：<newline>/skin https://namemc.com/skin/", "skinsrestorer.skin_edit_message": "<dark_green>你可以在 <u><aqua><hover:show_text:'<dark_green>点击打开'><click:open_url:'<url>'>这个网站</click></hover></aqua></u> 编辑你的皮肤<newline><dark_green>了解如何应用编辑后的皮肤，请访问：<newline><green><hover:show_text:'<dark_green>点击打开'><click:open_url:'https://skinsrestorer.net/skinedit'>https://skinsrestorer.net/skinedit</click></hover>", "skinsrestorer.no_skin_data": "<dark_red>错误<dark_gray>：<red>没有获取到皮肤数据！确定这个玩家有皮肤吗？", "skinsrestorer.outdated": "<dark_red>你在 <platform> 上运行着过时版本的 SkinsRestorer ！<newline><red>请更新到 Modrinth 上的最新版本：<newline><yellow><hover:show_text:'<dark_green>点击打开'><click:open_url:'https://modrinth.com/plugin/skinsrestorer'>https://modrinth.com/plugin/skinsrestorer</click></hover>", "skinsrestorer.unsupported_java": "<dark_red>SkinsResotorer不支持您的 <platform> 的 Java 版本 (<version>) ！<newline><red>请更新到 Java 17或更高版本来使用 SkinsRestorer 而不存在问题。 较新的 Java 版本也可以运行旧服务器，所以Minecraft 1.8 服务器可以在 Java 17 上运行。阅读控制台信息以了解更多详情。", "skinsrestorer.permission_player_wildcard": "玩家通配符权限", "skinsrestorer.permission_command": "允许使用\"/skin\"命令。", "skinsrestorer.permission_command_set": "允许更改你的皮肤。", "skinsrestorer.permission_command_set_url": "允许通过URL更改您的皮肤。", "skinsrestorer.permission_command_clear": "允许清除您的皮肤。", "skinsrestorer.permission_command_random": "允许设置一个随机皮肤。", "skinsrestorer.permission_command_update": "允许访问更新您的皮肤。", "skinsrestorer.permission_command_undo": "允许访问权限将皮肤还原到您以前的皮肤。", "skinsrestorer.permission_command_favourite": "允许设置皮肤为收藏。", "skinsrestorer.permission_command_search": "允许搜索你的皮肤。", "skinsrestorer.permission_command_edit": "允许编辑你的皮肤。", "skinsrestorer.permission_command_gui": "允许打开皮肤菜单。", "skinsrestorer.permission_admin_wildcard": "管理员通配符权限", "skinsrestorer.permission_admincommand": "允许使用\"/sr\"命令。", "skinsrestorer.permission_command_set_other": "允许设置其他玩家的皮肤。", "skinsrestorer.permission_command_clear_other": "允许清除其他玩家的皮肤。", "skinsrestorer.permission_command_random_other": "允许为其他玩家设置随机皮肤。", "skinsrestorer.permission_command_update_other": "允许更新其他玩家的皮肤。", "skinsrestorer.permission_command_favourite_other": "允许为其他玩家设置收藏皮肤。", "skinsrestorer.permission_command_undo_other": "允许将皮肤还原到玩家以前的皮肤。", "skinsrestorer.permission_admincommand_skull": "允许使用主要的“/skull”指令。", "skinsrestorer.permission_admincommand_skull_get": "允许获得头颅。", "skinsrestorer.permission_admincommand_skull_get_url": "允许通过URL获取头颅。", "skinsrestorer.permission_admincommand_skull_random": "允许获得随机的头颅。", "skinsrestorer.permission_admincommand_skull_get_other": "允许给予其他玩家头颅", "skinsrestorer.permission_admincommand_skull_random_other": "允许使用给其他玩家一个随机头颅。", "skinsrestorer.permission_admincommand_reload": "允许使用\"/sr reload\"命令", "skinsrestorer.permission_admincommand_status": "允许使用\"/sr status\"命令", "skinsrestorer.permission_admincommand_drop": "允许删除一个.SKIN文件。", "skinsrestorer.permission_admincommand_info": "允许获取玩家或皮肤的信息。", "skinsrestorer.permission_admincommand_applyskin": "允许重新应用其他玩家皮肤。", "skinsrestorer.permission_admincommand_createcustom": "允许通过URL创建自定义全局皮肤。", "skinsrestorer.permission_admincommand_purgeolddata": "允许清除旧皮肤数据。", "skinsrestorer.permission_admincommand_dump": "允许通过\"/sr dump\"上传服务器信息。", "skinsrestorer.permission_bypasscooldown": "绕过配置中设置的任何命令冷却时间。", "skinsrestorer.permission_bypassdisabled": "绕过配置中设置的任何禁用皮肤。", "skinsrestorer.permission_ownskin": "允许设置自己的皮肤。", "skinsrestorer.duration_day": " 天", "skinsrestorer.duration_days": " 天", "skinsrestorer.duration_hour": " 小时", "skinsrestorer.duration_hours": " 小时", "skinsrestorer.duration_minute": " 分钟", "skinsrestorer.duration_minutes": " 分钟", "skinsrestorer.duration_second": " 秒", "skinsrestorer.duration_seconds": " 秒"}