<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <!-- This module was also published with a richer model, Gradle metadata,  -->
  <!-- which should be used instead. Do not delete the following line which  -->
  <!-- is to indicate to <PERSON>rad<PERSON> or any Gradle module metadata file consumer  -->
  <!-- that they should prefer consuming it instead. -->
  <!-- do_not_remove: published-with-gradle-metadata -->
  <modelVersion>4.0.0</modelVersion>
  <groupId>org.spongepowered</groupId>
  <artifactId>configurate-core</artifactId>
  <version>4.1.2</version>
  <name>core</name>
  <description>A simple configuration library for Java applications that can handle a variety of formats and provides a node-based data structure able to handle a wide variety of configuration schemas</description>
  <url>https://github.com/SpongePowered/Configurate</url>
  <inceptionYear>2014</inceptionYear>
  <licenses>
    <license>
      <name>Apache License, Version 2.0</name>
      <url>https://opensource.org/licenses/Apache-2.0</url>
    </license>
  </licenses>
  <developers>
    <developer>
      <name>zml</name>
      <email><EMAIL></email>
    </developer>
  </developers>
  <scm>
    <connection>scm:git:https://github.com/SpongePowered/Configurate.git</connection>
    <developerConnection>scm:git:ssh://**************/SpongePowered/Configurate.git</developerConnection>
    <url>https://github.com/SpongePowered/Configurate</url>
  </scm>
  <issueManagement>
    <system>GitHub</system>
    <url>https://github.com/SpongePowered/Configurate/issues</url>
  </issueManagement>
  <ciManagement>
    <system>GitHub Actions</system>
    <url>https://github.com/SpongePowered/Configurate/actions</url>
  </ciManagement>
  <dependencies>
    <dependency>
      <groupId>io.leangen.geantyref</groupId>
      <artifactId>geantyref</artifactId>
      <version>1.3.11</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.google.errorprone</groupId>
      <artifactId>error_prone_annotations</artifactId>
      <version>2.6.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.checkerframework</groupId>
      <artifactId>checker-qual</artifactId>
      <version>3.12.0</version>
      <scope>compile</scope>
    </dependency>
  </dependencies>
</project>
