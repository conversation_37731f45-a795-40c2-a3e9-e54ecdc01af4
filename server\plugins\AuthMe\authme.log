[08-11 18:45:14]: [INFO] Created column 'regdate' and set the current timestamp, 1723373114791, to all 0 rows
[08-11 18:45:14]: [INFO] SQLite Setup finished
[08-11 18:45:14]: [INFO] Hooked into LuckPerms!
[08-11 18:45:15]: [INFO] Hooked successfully into Essentials
[08-11 18:45:15]: [INFO] Essentials spawn file not found: 'C:\Users\<USER>\Desktop\我的世界服务器更新\1.21.1\Tian1.21.1服务端\plugins\Essentials\spawn.yml'
[08-11 18:45:15]: [WARN] WARNING! The protectInventory feature requires ProtocolLib! Disabling it...
[08-11 18:45:15]: [INFO] AuthMe 5.6.0-SNAPSHOT build n.2623 successfully enabled!
[08-11 18:45:20]: [INFO] Downloading GEO IP database, because the old database is older than 30 days or doesn't exist
[08-11 18:45:20]: [WARN] No MaxMind credentials found in the configuration file! GeoIp protections will be disabled.
[08-11 18:45:20]: [INFO] There is no newer GEO IP database uploaded to MaxMind. Using the old one for now.
[08-11 18:45:20]: [WARN] Could not download GeoLiteAPI database [FileNotFoundException]: plugins\AuthMe\GeoLite2-Country.mmdb (系统找不到指定的文件。)
[08-11 18:45:20]: java.io.FileNotFoundException: plugins\AuthMe\GeoLite2-Country.mmdb (系统找不到指定的文件。)
	at java.base/java.io.RandomAccessFile.open0(Native Method)
	at java.base/java.io.RandomAccessFile.open(RandomAccessFile.java:356)
	at java.base/java.io.RandomAccessFile.<init>(RandomAccessFile.java:273)
	at java.base/java.io.RandomAccessFile.<init>(RandomAccessFile.java:223)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.libs.com.maxmind.db.BufferHolder.<init>(BufferHolder.java:20)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.libs.com.maxmind.db.Reader.<init>(Reader.java:121)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.service.GeoIpService.startReading(GeoIpService.java:179)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.service.GeoIpService.updateDatabase(GeoIpService.java:150)
	at org.bukkit.craftbukkit.scheduler.CraftTask.run(CraftTask.java:86)
	at org.bukkit.craftbukkit.scheduler.CraftAsyncTask.run(CraftAsyncTask.java:57)
	at com.destroystokyo.paper.ServerSchedulerReportingWrapper.run(ServerSchedulerReportingWrapper.java:22)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)

[08-11 18:48:22]: [INFO] SQLite Setup finished
[08-11 18:48:22]: [INFO] Hooked into LuckPerms!
[08-11 18:48:22]: [INFO] Hooked successfully into Essentials
[08-11 18:48:22]: [INFO] Essentials spawn file not found: 'C:\Users\<USER>\Desktop\我的世界服务器更新\1.21.1\Tian1.21.1服务端\plugins\Essentials\spawn.yml'
[08-11 18:48:23]: [INFO] AuthMe 5.6.0-SNAPSHOT build n.2623 successfully enabled!
[08-11 18:48:28]: [INFO] Downloading GEO IP database, because the old database is older than 30 days or doesn't exist
[08-11 18:48:28]: [WARN] No MaxMind credentials found in the configuration file! GeoIp protections will be disabled.
[08-11 18:48:28]: [INFO] There is no newer GEO IP database uploaded to MaxMind. Using the old one for now.
[08-11 18:48:28]: [WARN] Could not download GeoLiteAPI database [FileNotFoundException]: plugins\AuthMe\GeoLite2-Country.mmdb (系统找不到指定的文件。)
[08-11 18:48:28]: java.io.FileNotFoundException: plugins\AuthMe\GeoLite2-Country.mmdb (系统找不到指定的文件。)
	at java.base/java.io.RandomAccessFile.open0(Native Method)
	at java.base/java.io.RandomAccessFile.open(RandomAccessFile.java:356)
	at java.base/java.io.RandomAccessFile.<init>(RandomAccessFile.java:273)
	at java.base/java.io.RandomAccessFile.<init>(RandomAccessFile.java:223)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.libs.com.maxmind.db.BufferHolder.<init>(BufferHolder.java:20)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.libs.com.maxmind.db.Reader.<init>(Reader.java:121)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.service.GeoIpService.startReading(GeoIpService.java:179)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.service.GeoIpService.updateDatabase(GeoIpService.java:150)
	at org.bukkit.craftbukkit.scheduler.CraftTask.run(CraftTask.java:86)
	at org.bukkit.craftbukkit.scheduler.CraftAsyncTask.run(CraftAsyncTask.java:57)
	at com.destroystokyo.paper.ServerSchedulerReportingWrapper.run(ServerSchedulerReportingWrapper.java:22)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)

[08-11 18:49:36]: [FINE] tiancuo registered 127.0.0.1
[08-11 18:49:36]: [FINE] tiancuo logged in 127.0.0.1
[08-11 18:50:12]: [INFO] AuthMe 5.6.0-SNAPSHOT-b2623 disabled!
[08-11 18:50:51]: [INFO] SQLite Setup finished
[08-11 18:50:51]: [INFO] Hooked into LuckPerms!
[08-11 18:50:51]: [INFO] Hooked successfully into Essentials
[08-11 18:50:51]: [INFO] Essentials spawn file not found: 'C:\Users\<USER>\Desktop\我的世界服务器更新\1.21.1\Tian1.21.1服务端\plugins\Essentials\spawn.yml'
[08-11 18:50:51]: [INFO] AuthMe 5.6.0-SNAPSHOT build n.2623 successfully enabled!
[08-11 18:50:56]: [INFO] Downloading GEO IP database, because the old database is older than 30 days or doesn't exist
[08-11 18:50:56]: [WARN] No MaxMind credentials found in the configuration file! GeoIp protections will be disabled.
[08-11 18:50:56]: [INFO] There is no newer GEO IP database uploaded to MaxMind. Using the old one for now.
[08-11 18:50:56]: [WARN] Could not download GeoLiteAPI database [FileNotFoundException]: plugins\AuthMe\GeoLite2-Country.mmdb (系统找不到指定的文件。)
[08-11 18:50:56]: java.io.FileNotFoundException: plugins\AuthMe\GeoLite2-Country.mmdb (系统找不到指定的文件。)
	at java.base/java.io.RandomAccessFile.open0(Native Method)
	at java.base/java.io.RandomAccessFile.open(RandomAccessFile.java:356)
	at java.base/java.io.RandomAccessFile.<init>(RandomAccessFile.java:273)
	at java.base/java.io.RandomAccessFile.<init>(RandomAccessFile.java:223)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.libs.com.maxmind.db.BufferHolder.<init>(BufferHolder.java:20)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.libs.com.maxmind.db.Reader.<init>(Reader.java:121)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.service.GeoIpService.startReading(GeoIpService.java:179)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.service.GeoIpService.updateDatabase(GeoIpService.java:150)
	at org.bukkit.craftbukkit.scheduler.CraftTask.run(CraftTask.java:86)
	at org.bukkit.craftbukkit.scheduler.CraftAsyncTask.run(CraftAsyncTask.java:57)
	at com.destroystokyo.paper.ServerSchedulerReportingWrapper.run(ServerSchedulerReportingWrapper.java:22)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)

[08-11 18:51:08]: [FINE] tiancuo logged in 127.0.0.1
[08-11 18:51:35]: [INFO] AuthMe 5.6.0-SNAPSHOT-b2623 disabled!
[08-11 18:53:22]: [INFO] SQLite Setup finished
[08-11 18:53:22]: [INFO] Hooked into LuckPerms!
[08-11 18:53:22]: [INFO] Hooked successfully into Essentials
[08-11 18:53:22]: [INFO] Essentials spawn file not found: 'C:\Users\<USER>\Desktop\我的世界服务器更新\1.21.1\Tian1.21.1服务端\plugins\Essentials\spawn.yml'
[08-11 18:53:23]: [INFO] AuthMe 5.6.0-SNAPSHOT build n.2623 successfully enabled!
[08-11 18:53:27]: [INFO] Downloading GEO IP database, because the old database is older than 30 days or doesn't exist
[08-11 18:53:27]: [WARN] No MaxMind credentials found in the configuration file! GeoIp protections will be disabled.
[08-11 18:53:27]: [INFO] There is no newer GEO IP database uploaded to MaxMind. Using the old one for now.
[08-11 18:53:27]: [WARN] Could not download GeoLiteAPI database [FileNotFoundException]: plugins\AuthMe\GeoLite2-Country.mmdb (系统找不到指定的文件。)
[08-11 18:53:27]: java.io.FileNotFoundException: plugins\AuthMe\GeoLite2-Country.mmdb (系统找不到指定的文件。)
	at java.base/java.io.RandomAccessFile.open0(Native Method)
	at java.base/java.io.RandomAccessFile.open(RandomAccessFile.java:356)
	at java.base/java.io.RandomAccessFile.<init>(RandomAccessFile.java:273)
	at java.base/java.io.RandomAccessFile.<init>(RandomAccessFile.java:223)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.libs.com.maxmind.db.BufferHolder.<init>(BufferHolder.java:20)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.libs.com.maxmind.db.Reader.<init>(Reader.java:121)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.service.GeoIpService.startReading(GeoIpService.java:179)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.service.GeoIpService.updateDatabase(GeoIpService.java:150)
	at org.bukkit.craftbukkit.scheduler.CraftTask.run(CraftTask.java:86)
	at org.bukkit.craftbukkit.scheduler.CraftAsyncTask.run(CraftAsyncTask.java:57)
	at com.destroystokyo.paper.ServerSchedulerReportingWrapper.run(ServerSchedulerReportingWrapper.java:22)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)

[08-12 13:13:16]: [INFO] SQLite Setup finished
[08-12 13:13:16]: [INFO] Hooked into LuckPerms!
[08-12 13:13:16]: [INFO] Hooked successfully into Essentials
[08-12 13:13:16]: [INFO] Essentials spawn file not found: 'C:\Users\<USER>\Desktop\我的世界服务器更新\1.21.1\Tian1.21.1服务端\plugins\Essentials\spawn.yml'
[08-12 13:13:17]: [INFO] AuthMe 5.6.0-SNAPSHOT build n.2623 successfully enabled!
[08-12 13:13:21]: [INFO] Downloading GEO IP database, because the old database is older than 30 days or doesn't exist
[08-12 13:13:21]: [WARN] No MaxMind credentials found in the configuration file! GeoIp protections will be disabled.
[08-12 13:13:21]: [INFO] There is no newer GEO IP database uploaded to MaxMind. Using the old one for now.
[08-12 13:13:21]: [WARN] Could not download GeoLiteAPI database [FileNotFoundException]: plugins\AuthMe\GeoLite2-Country.mmdb (系统找不到指定的文件。)
[08-12 13:13:21]: java.io.FileNotFoundException: plugins\AuthMe\GeoLite2-Country.mmdb (系统找不到指定的文件。)
	at java.base/java.io.RandomAccessFile.open0(Native Method)
	at java.base/java.io.RandomAccessFile.open(RandomAccessFile.java:356)
	at java.base/java.io.RandomAccessFile.<init>(RandomAccessFile.java:273)
	at java.base/java.io.RandomAccessFile.<init>(RandomAccessFile.java:223)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.libs.com.maxmind.db.BufferHolder.<init>(BufferHolder.java:20)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.libs.com.maxmind.db.Reader.<init>(Reader.java:121)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.service.GeoIpService.startReading(GeoIpService.java:179)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.service.GeoIpService.updateDatabase(GeoIpService.java:150)
	at org.bukkit.craftbukkit.scheduler.CraftTask.run(CraftTask.java:86)
	at org.bukkit.craftbukkit.scheduler.CraftAsyncTask.run(CraftAsyncTask.java:57)
	at com.destroystokyo.paper.ServerSchedulerReportingWrapper.run(ServerSchedulerReportingWrapper.java:22)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)

[08-12 13:14:05]: [INFO] SQLite Setup finished
[08-12 13:14:05]: [INFO] Hooked into LuckPerms!
[08-12 13:14:05]: [INFO] Hooked successfully into Essentials
[08-12 13:14:05]: [INFO] Essentials spawn file not found: 'C:\Users\<USER>\Desktop\我的世界服务器更新\1.21.1\Tian1.21.1服务端\plugins\Essentials\spawn.yml'
[08-12 13:14:05]: [INFO] AuthMe 5.6.0-SNAPSHOT build n.2623 successfully enabled!
[08-12 13:14:10]: [INFO] Downloading GEO IP database, because the old database is older than 30 days or doesn't exist
[08-12 13:14:10]: [WARN] No MaxMind credentials found in the configuration file! GeoIp protections will be disabled.
[08-12 13:14:10]: [INFO] There is no newer GEO IP database uploaded to MaxMind. Using the old one for now.
[08-12 13:14:10]: [WARN] Could not download GeoLiteAPI database [FileNotFoundException]: plugins\AuthMe\GeoLite2-Country.mmdb (系统找不到指定的文件。)
[08-12 13:14:10]: java.io.FileNotFoundException: plugins\AuthMe\GeoLite2-Country.mmdb (系统找不到指定的文件。)
	at java.base/java.io.RandomAccessFile.open0(Native Method)
	at java.base/java.io.RandomAccessFile.open(RandomAccessFile.java:356)
	at java.base/java.io.RandomAccessFile.<init>(RandomAccessFile.java:273)
	at java.base/java.io.RandomAccessFile.<init>(RandomAccessFile.java:223)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.libs.com.maxmind.db.BufferHolder.<init>(BufferHolder.java:20)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.libs.com.maxmind.db.Reader.<init>(Reader.java:121)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.service.GeoIpService.startReading(GeoIpService.java:179)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.service.GeoIpService.updateDatabase(GeoIpService.java:150)
	at org.bukkit.craftbukkit.scheduler.CraftTask.run(CraftTask.java:86)
	at org.bukkit.craftbukkit.scheduler.CraftAsyncTask.run(CraftAsyncTask.java:57)
	at com.destroystokyo.paper.ServerSchedulerReportingWrapper.run(ServerSchedulerReportingWrapper.java:22)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)

[08-12 13:17:02]: [FINE] tiancuo used the wrong password
[08-12 13:17:09]: [FINE] tiancuo logged in 127.0.0.1
[08-12 13:19:57]: [INFO] SQLite Setup finished
[08-12 13:19:57]: [INFO] Hooked into LuckPerms!
[08-12 13:19:57]: [INFO] Hooked successfully into Essentials
[08-12 13:19:57]: [INFO] Essentials spawn file not found: 'C:\Users\<USER>\Desktop\我的世界服务器更新\1.21.1\Tian1.21.1服务端\plugins\Essentials\spawn.yml'
[08-12 13:19:57]: [INFO] AuthMe 5.6.0-SNAPSHOT build n.2623 successfully enabled!
[08-12 13:20:02]: [INFO] Downloading GEO IP database, because the old database is older than 30 days or doesn't exist
[08-12 13:20:02]: [WARN] No MaxMind credentials found in the configuration file! GeoIp protections will be disabled.
[08-12 13:20:02]: [INFO] There is no newer GEO IP database uploaded to MaxMind. Using the old one for now.
[08-12 13:20:02]: [WARN] Could not download GeoLiteAPI database [FileNotFoundException]: plugins\AuthMe\GeoLite2-Country.mmdb (系统找不到指定的文件。)
[08-12 13:20:02]: java.io.FileNotFoundException: plugins\AuthMe\GeoLite2-Country.mmdb (系统找不到指定的文件。)
	at java.base/java.io.RandomAccessFile.open0(Native Method)
	at java.base/java.io.RandomAccessFile.open(RandomAccessFile.java:356)
	at java.base/java.io.RandomAccessFile.<init>(RandomAccessFile.java:273)
	at java.base/java.io.RandomAccessFile.<init>(RandomAccessFile.java:223)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.libs.com.maxmind.db.BufferHolder.<init>(BufferHolder.java:20)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.libs.com.maxmind.db.Reader.<init>(Reader.java:121)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.service.GeoIpService.startReading(GeoIpService.java:179)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.service.GeoIpService.updateDatabase(GeoIpService.java:150)
	at org.bukkit.craftbukkit.scheduler.CraftTask.run(CraftTask.java:86)
	at org.bukkit.craftbukkit.scheduler.CraftAsyncTask.run(CraftAsyncTask.java:57)
	at com.destroystokyo.paper.ServerSchedulerReportingWrapper.run(ServerSchedulerReportingWrapper.java:22)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)

[08-12 13:22:54]: [INFO] SQLite Setup finished
[08-12 13:22:54]: [INFO] Hooked into LuckPerms!
[08-12 13:22:54]: [INFO] Hooked successfully into Essentials
[08-12 13:22:54]: [INFO] Essentials spawn file not found: 'C:\Users\<USER>\Desktop\我的世界服务器更新\1.21.1\Tian1.21.1服务端\plugins\Essentials\spawn.yml'
[08-12 13:22:54]: [INFO] AuthMe 5.6.0-SNAPSHOT build n.2623 successfully enabled!
[08-12 13:22:59]: [INFO] Downloading GEO IP database, because the old database is older than 30 days or doesn't exist
[08-12 13:22:59]: [WARN] No MaxMind credentials found in the configuration file! GeoIp protections will be disabled.
[08-12 13:22:59]: [INFO] There is no newer GEO IP database uploaded to MaxMind. Using the old one for now.
[08-12 13:22:59]: [WARN] Could not download GeoLiteAPI database [FileNotFoundException]: plugins\AuthMe\GeoLite2-Country.mmdb (系统找不到指定的文件。)
[08-12 13:22:59]: java.io.FileNotFoundException: plugins\AuthMe\GeoLite2-Country.mmdb (系统找不到指定的文件。)
	at java.base/java.io.RandomAccessFile.open0(Native Method)
	at java.base/java.io.RandomAccessFile.open(RandomAccessFile.java:356)
	at java.base/java.io.RandomAccessFile.<init>(RandomAccessFile.java:273)
	at java.base/java.io.RandomAccessFile.<init>(RandomAccessFile.java:223)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.libs.com.maxmind.db.BufferHolder.<init>(BufferHolder.java:20)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.libs.com.maxmind.db.Reader.<init>(Reader.java:121)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.service.GeoIpService.startReading(GeoIpService.java:179)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.service.GeoIpService.updateDatabase(GeoIpService.java:150)
	at org.bukkit.craftbukkit.scheduler.CraftTask.run(CraftTask.java:86)
	at org.bukkit.craftbukkit.scheduler.CraftAsyncTask.run(CraftAsyncTask.java:57)
	at com.destroystokyo.paper.ServerSchedulerReportingWrapper.run(ServerSchedulerReportingWrapper.java:22)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)

[08-12 13:23:52]: [INFO] AuthMe 5.6.0-SNAPSHOT-b2623 disabled!
[08-12 13:24:22]: [INFO] SQLite Setup finished
[08-12 13:24:22]: [INFO] Hooked into LuckPerms!
[08-12 13:24:22]: [INFO] Hooked successfully into Essentials
[08-12 13:24:22]: [INFO] Essentials spawn file not found: 'C:\Users\<USER>\Desktop\我的世界服务器更新\1.21.1\Tian1.21.1服务端\plugins\Essentials\spawn.yml'
[08-12 13:24:23]: [INFO] AuthMe 5.6.0-SNAPSHOT build n.2623 successfully enabled!
[08-12 13:24:27]: [INFO] Downloading GEO IP database, because the old database is older than 30 days or doesn't exist
[08-12 13:24:27]: [WARN] No MaxMind credentials found in the configuration file! GeoIp protections will be disabled.
[08-12 13:24:27]: [INFO] There is no newer GEO IP database uploaded to MaxMind. Using the old one for now.
[08-12 13:24:28]: [WARN] Could not download GeoLiteAPI database [FileNotFoundException]: plugins\AuthMe\GeoLite2-Country.mmdb (系统找不到指定的文件。)
[08-12 13:24:28]: java.io.FileNotFoundException: plugins\AuthMe\GeoLite2-Country.mmdb (系统找不到指定的文件。)
	at java.base/java.io.RandomAccessFile.open0(Native Method)
	at java.base/java.io.RandomAccessFile.open(RandomAccessFile.java:356)
	at java.base/java.io.RandomAccessFile.<init>(RandomAccessFile.java:273)
	at java.base/java.io.RandomAccessFile.<init>(RandomAccessFile.java:223)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.libs.com.maxmind.db.BufferHolder.<init>(BufferHolder.java:20)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.libs.com.maxmind.db.Reader.<init>(Reader.java:121)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.service.GeoIpService.startReading(GeoIpService.java:179)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.service.GeoIpService.updateDatabase(GeoIpService.java:150)
	at org.bukkit.craftbukkit.scheduler.CraftTask.run(CraftTask.java:86)
	at org.bukkit.craftbukkit.scheduler.CraftAsyncTask.run(CraftAsyncTask.java:57)
	at com.destroystokyo.paper.ServerSchedulerReportingWrapper.run(ServerSchedulerReportingWrapper.java:22)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)

[08-12 13:24:48]: [FINE] tiancuo logged in 127.0.0.1
[08-12 13:30:05]: [INFO] SQLite Setup finished
[08-12 13:30:05]: [INFO] Hooked into LuckPerms!
[08-12 13:30:05]: [INFO] Hooked successfully into Essentials
[08-12 13:30:05]: [INFO] Essentials spawn file not found: 'C:\Users\<USER>\Desktop\我的世界服务器更新\1.21.1\Tian1.21.1服务端\plugins\Essentials\spawn.yml'
[08-12 13:30:05]: [INFO] AuthMe 5.6.0-SNAPSHOT build n.2623 successfully enabled!
[08-12 13:30:10]: [INFO] Downloading GEO IP database, because the old database is older than 30 days or doesn't exist
[08-12 13:30:10]: [WARN] No MaxMind credentials found in the configuration file! GeoIp protections will be disabled.
[08-12 13:30:10]: [INFO] There is no newer GEO IP database uploaded to MaxMind. Using the old one for now.
[08-12 13:30:10]: [WARN] Could not download GeoLiteAPI database [FileNotFoundException]: plugins\AuthMe\GeoLite2-Country.mmdb (系统找不到指定的文件。)
[08-12 13:30:10]: java.io.FileNotFoundException: plugins\AuthMe\GeoLite2-Country.mmdb (系统找不到指定的文件。)
	at java.base/java.io.RandomAccessFile.open0(Native Method)
	at java.base/java.io.RandomAccessFile.open(RandomAccessFile.java:356)
	at java.base/java.io.RandomAccessFile.<init>(RandomAccessFile.java:273)
	at java.base/java.io.RandomAccessFile.<init>(RandomAccessFile.java:223)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.libs.com.maxmind.db.BufferHolder.<init>(BufferHolder.java:20)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.libs.com.maxmind.db.Reader.<init>(Reader.java:121)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.service.GeoIpService.startReading(GeoIpService.java:179)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.service.GeoIpService.updateDatabase(GeoIpService.java:150)
	at org.bukkit.craftbukkit.scheduler.CraftTask.run(CraftTask.java:86)
	at org.bukkit.craftbukkit.scheduler.CraftAsyncTask.run(CraftAsyncTask.java:57)
	at com.destroystokyo.paper.ServerSchedulerReportingWrapper.run(ServerSchedulerReportingWrapper.java:22)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)

[08-12 13:30:36]: [FINE] tiancuo logged in 127.0.0.1
[01-16 21:44:07]: [INFO] SQLite Setup finished
[01-16 21:44:07]: [INFO] Hooked into LuckPerms!
[01-16 21:44:07]: [INFO] Hooked successfully into Essentials
[01-16 21:44:07]: [INFO] Essentials spawn file not found: 'D:\下载\【1.21】[mienbbs]Tian服务端 (1)\Tian1.21.1服务端\plugins\Essentials\spawn.yml'
[01-16 21:44:08]: [INFO] AuthMe 5.6.0-SNAPSHOT build n.2623 successfully enabled!
[01-16 21:44:10]: [INFO] Downloading GEO IP database, because the old database is older than 30 days or doesn't exist
[01-16 21:44:10]: [WARN] No MaxMind credentials found in the configuration file! GeoIp protections will be disabled.
[01-16 21:44:10]: [INFO] There is no newer GEO IP database uploaded to MaxMind. Using the old one for now.
[01-16 21:44:10]: [WARN] Could not download GeoLiteAPI database [FileNotFoundException]: plugins\AuthMe\GeoLite2-Country.mmdb (系统找不到指定的文件。)
[01-16 21:44:10]: java.io.FileNotFoundException: plugins\AuthMe\GeoLite2-Country.mmdb (系统找不到指定的文件。)
	at java.base/java.io.RandomAccessFile.open0(Native Method)
	at java.base/java.io.RandomAccessFile.open(RandomAccessFile.java:356)
	at java.base/java.io.RandomAccessFile.<init>(RandomAccessFile.java:273)
	at java.base/java.io.RandomAccessFile.<init>(RandomAccessFile.java:223)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.libs.com.maxmind.db.BufferHolder.<init>(BufferHolder.java:20)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.libs.com.maxmind.db.Reader.<init>(Reader.java:121)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.service.GeoIpService.startReading(GeoIpService.java:179)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.service.GeoIpService.updateDatabase(GeoIpService.java:150)
	at org.bukkit.craftbukkit.scheduler.CraftTask.run(CraftTask.java:78)
	at org.bukkit.craftbukkit.scheduler.CraftAsyncTask.run(CraftAsyncTask.java:57)
	at com.destroystokyo.paper.ServerSchedulerReportingWrapper.run(ServerSchedulerReportingWrapper.java:22)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)

[01-16 21:51:41]: [INFO] SQLite Setup finished
[01-16 21:51:41]: [INFO] Hooked into LuckPerms!
[01-16 21:51:41]: [INFO] Hooked successfully into Essentials
[01-16 21:51:41]: [INFO] Essentials spawn file not found: 'D:\下载\【1.21】[mienbbs]Tian服务端 (1)\Tian1.21.1服务端\plugins\Essentials\spawn.yml'
[01-16 21:51:41]: [INFO] AuthMe 5.6.0-SNAPSHOT build n.2623 successfully enabled!
[01-16 21:51:44]: [INFO] Downloading GEO IP database, because the old database is older than 30 days or doesn't exist
[01-16 21:51:44]: [WARN] No MaxMind credentials found in the configuration file! GeoIp protections will be disabled.
[01-16 21:51:44]: [INFO] There is no newer GEO IP database uploaded to MaxMind. Using the old one for now.
[01-16 21:51:44]: [WARN] Could not download GeoLiteAPI database [FileNotFoundException]: plugins\AuthMe\GeoLite2-Country.mmdb (系统找不到指定的文件。)
[01-16 21:51:44]: java.io.FileNotFoundException: plugins\AuthMe\GeoLite2-Country.mmdb (系统找不到指定的文件。)
	at java.base/java.io.RandomAccessFile.open0(Native Method)
	at java.base/java.io.RandomAccessFile.open(RandomAccessFile.java:356)
	at java.base/java.io.RandomAccessFile.<init>(RandomAccessFile.java:273)
	at java.base/java.io.RandomAccessFile.<init>(RandomAccessFile.java:223)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.libs.com.maxmind.db.BufferHolder.<init>(BufferHolder.java:20)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.libs.com.maxmind.db.Reader.<init>(Reader.java:121)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.service.GeoIpService.startReading(GeoIpService.java:179)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.service.GeoIpService.updateDatabase(GeoIpService.java:150)
	at org.bukkit.craftbukkit.scheduler.CraftTask.run(CraftTask.java:78)
	at org.bukkit.craftbukkit.scheduler.CraftAsyncTask.run(CraftAsyncTask.java:57)
	at com.destroystokyo.paper.ServerSchedulerReportingWrapper.run(ServerSchedulerReportingWrapper.java:22)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)

[01-16 21:54:37]: [INFO] SQLite Setup finished
[01-16 21:54:37]: [INFO] Hooked into LuckPerms!
[01-16 21:54:37]: [INFO] Hooked successfully into Essentials
[01-16 21:54:37]: [INFO] Essentials spawn file not found: 'D:\下载\【1.21】[mienbbs]Tian服务端 (1)\Tian1.21.1服务端\plugins\Essentials\spawn.yml'
[01-16 21:54:37]: [INFO] AuthMe 5.6.0-SNAPSHOT build n.2623 successfully enabled!
[01-16 21:54:40]: [INFO] Downloading GEO IP database, because the old database is older than 30 days or doesn't exist
[01-16 21:54:40]: [WARN] No MaxMind credentials found in the configuration file! GeoIp protections will be disabled.
[01-16 21:54:40]: [INFO] There is no newer GEO IP database uploaded to MaxMind. Using the old one for now.
[01-16 21:54:40]: [WARN] Could not download GeoLiteAPI database [FileNotFoundException]: plugins\AuthMe\GeoLite2-Country.mmdb (系统找不到指定的文件。)
[01-16 21:54:40]: java.io.FileNotFoundException: plugins\AuthMe\GeoLite2-Country.mmdb (系统找不到指定的文件。)
	at java.base/java.io.RandomAccessFile.open0(Native Method)
	at java.base/java.io.RandomAccessFile.open(RandomAccessFile.java:356)
	at java.base/java.io.RandomAccessFile.<init>(RandomAccessFile.java:273)
	at java.base/java.io.RandomAccessFile.<init>(RandomAccessFile.java:223)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.libs.com.maxmind.db.BufferHolder.<init>(BufferHolder.java:20)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.libs.com.maxmind.db.Reader.<init>(Reader.java:121)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.service.GeoIpService.startReading(GeoIpService.java:179)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.service.GeoIpService.updateDatabase(GeoIpService.java:150)
	at org.bukkit.craftbukkit.scheduler.CraftTask.run(CraftTask.java:78)
	at org.bukkit.craftbukkit.scheduler.CraftAsyncTask.run(CraftAsyncTask.java:57)
	at com.destroystokyo.paper.ServerSchedulerReportingWrapper.run(ServerSchedulerReportingWrapper.java:22)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)

[01-16 21:56:37]: [INFO] SQLite Setup finished
[01-16 21:56:37]: [INFO] Hooked into LuckPerms!
[01-16 21:56:37]: [INFO] Hooked successfully into Essentials
[01-16 21:56:37]: [INFO] Essentials spawn file not found: 'D:\下载\【1.21】[mienbbs]Tian服务端 (1)\Tian1.21.1服务端\plugins\Essentials\spawn.yml'
[01-16 21:56:37]: [INFO] AuthMe 5.6.0-SNAPSHOT build n.2623 successfully enabled!
[01-16 21:56:40]: [INFO] Downloading GEO IP database, because the old database is older than 30 days or doesn't exist
[01-16 21:56:40]: [WARN] No MaxMind credentials found in the configuration file! GeoIp protections will be disabled.
[01-16 21:56:40]: [INFO] There is no newer GEO IP database uploaded to MaxMind. Using the old one for now.
[01-16 21:56:40]: [WARN] Could not download GeoLiteAPI database [FileNotFoundException]: plugins\AuthMe\GeoLite2-Country.mmdb (系统找不到指定的文件。)
[01-16 21:56:40]: java.io.FileNotFoundException: plugins\AuthMe\GeoLite2-Country.mmdb (系统找不到指定的文件。)
	at java.base/java.io.RandomAccessFile.open0(Native Method)
	at java.base/java.io.RandomAccessFile.open(RandomAccessFile.java:356)
	at java.base/java.io.RandomAccessFile.<init>(RandomAccessFile.java:273)
	at java.base/java.io.RandomAccessFile.<init>(RandomAccessFile.java:223)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.libs.com.maxmind.db.BufferHolder.<init>(BufferHolder.java:20)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.libs.com.maxmind.db.Reader.<init>(Reader.java:121)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.service.GeoIpService.startReading(GeoIpService.java:179)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.service.GeoIpService.updateDatabase(GeoIpService.java:150)
	at org.bukkit.craftbukkit.scheduler.CraftTask.run(CraftTask.java:78)
	at org.bukkit.craftbukkit.scheduler.CraftAsyncTask.run(CraftAsyncTask.java:57)
	at com.destroystokyo.paper.ServerSchedulerReportingWrapper.run(ServerSchedulerReportingWrapper.java:22)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)

[01-16 23:28:00]: [INFO] SQLite Setup finished
[01-16 23:28:00]: [INFO] Hooked into LuckPerms!
[01-16 23:28:00]: [INFO] Hooked successfully into Essentials
[01-16 23:28:00]: [INFO] Essentials spawn file not found: 'D:\下载\【1.21.4】[mienbbs]Tian服务端 (1)\Tian1.21.4服务端\plugins\Essentials\spawn.yml'
[01-16 23:28:00]: [INFO] AuthMe 5.6.0-SNAPSHOT build n.2623 successfully enabled!
[01-16 23:28:04]: [INFO] Downloading GEO IP database, because the old database is older than 30 days or doesn't exist
[01-16 23:28:04]: [WARN] No MaxMind credentials found in the configuration file! GeoIp protections will be disabled.
[01-16 23:28:04]: [INFO] There is no newer GEO IP database uploaded to MaxMind. Using the old one for now.
[01-16 23:28:04]: [WARN] Could not download GeoLiteAPI database [FileNotFoundException]: plugins\AuthMe\GeoLite2-Country.mmdb (系统找不到指定的文件。)
[01-16 23:28:04]: java.io.FileNotFoundException: plugins\AuthMe\GeoLite2-Country.mmdb (系统找不到指定的文件。)
	at java.base/java.io.RandomAccessFile.open0(Native Method)
	at java.base/java.io.RandomAccessFile.open(RandomAccessFile.java:356)
	at java.base/java.io.RandomAccessFile.<init>(RandomAccessFile.java:273)
	at java.base/java.io.RandomAccessFile.<init>(RandomAccessFile.java:223)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.libs.com.maxmind.db.BufferHolder.<init>(BufferHolder.java:20)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.libs.com.maxmind.db.Reader.<init>(Reader.java:121)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.service.GeoIpService.startReading(GeoIpService.java:179)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.service.GeoIpService.updateDatabase(GeoIpService.java:150)
	at org.bukkit.craftbukkit.scheduler.CraftTask.run(CraftTask.java:78)
	at org.bukkit.craftbukkit.scheduler.CraftAsyncTask.run(CraftAsyncTask.java:57)
	at com.destroystokyo.paper.ServerSchedulerReportingWrapper.run(ServerSchedulerReportingWrapper.java:22)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)

[01-19 00:56:00]: [INFO] SQLite Setup finished
[01-19 00:56:00]: [INFO] Hooked into LuckPerms!
[01-19 00:56:00]: [INFO] Hooked successfully into Essentials
[01-19 00:56:00]: [INFO] Essentials spawn file not found: 'D:\下载\【1.21.4】[mienbbs]Tian服务端 (1)\Tian1.21.4服务端\plugins\Essentials\spawn.yml'
[01-19 00:56:01]: [INFO] AuthMe 5.6.0-SNAPSHOT build n.2623 successfully enabled!
[01-19 00:56:04]: [INFO] Downloading GEO IP database, because the old database is older than 30 days or doesn't exist
[01-19 00:56:04]: [WARN] No MaxMind credentials found in the configuration file! GeoIp protections will be disabled.
[01-19 00:56:04]: [INFO] There is no newer GEO IP database uploaded to MaxMind. Using the old one for now.
[01-19 00:56:04]: [WARN] Could not download GeoLiteAPI database [FileNotFoundException]: plugins\AuthMe\GeoLite2-Country.mmdb (系统找不到指定的文件。)
[01-19 00:56:04]: java.io.FileNotFoundException: plugins\AuthMe\GeoLite2-Country.mmdb (系统找不到指定的文件。)
	at java.base/java.io.RandomAccessFile.open0(Native Method)
	at java.base/java.io.RandomAccessFile.open(RandomAccessFile.java:356)
	at java.base/java.io.RandomAccessFile.<init>(RandomAccessFile.java:273)
	at java.base/java.io.RandomAccessFile.<init>(RandomAccessFile.java:223)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.libs.com.maxmind.db.BufferHolder.<init>(BufferHolder.java:20)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.libs.com.maxmind.db.Reader.<init>(Reader.java:121)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.service.GeoIpService.startReading(GeoIpService.java:179)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.service.GeoIpService.updateDatabase(GeoIpService.java:150)
	at org.bukkit.craftbukkit.scheduler.CraftTask.run(CraftTask.java:78)
	at org.bukkit.craftbukkit.scheduler.CraftAsyncTask.run(CraftAsyncTask.java:57)
	at com.destroystokyo.paper.ServerSchedulerReportingWrapper.run(ServerSchedulerReportingWrapper.java:22)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)

[01-19 00:58:18]: [FINE] tiancuo used the wrong password
[01-19 00:58:22]: [FINE] tiancuo logged in 127.0.0.1
[01-19 08:38:51]: [INFO] SQLite Setup finished
[01-19 08:38:51]: [INFO] Hooked into LuckPerms!
[01-19 08:38:51]: [INFO] Hooked successfully into Essentials
[01-19 08:38:51]: [INFO] Essentials spawn file not found: 'D:\下载\【1.21.4】[mienbbs]Tian服务端\Tian1.21.4服务端\plugins\Essentials\spawn.yml'
[01-19 08:38:51]: [INFO] AuthMe 5.6.0-SNAPSHOT build n.2623 successfully enabled!
[01-19 08:38:54]: [INFO] Downloading GEO IP database, because the old database is older than 30 days or doesn't exist
[01-19 08:38:54]: [WARN] No MaxMind credentials found in the configuration file! GeoIp protections will be disabled.
[01-19 08:38:54]: [INFO] There is no newer GEO IP database uploaded to MaxMind. Using the old one for now.
[01-19 08:38:54]: [WARN] Could not download GeoLiteAPI database [FileNotFoundException]: plugins\AuthMe\GeoLite2-Country.mmdb (系统找不到指定的文件。)
[01-19 08:38:54]: java.io.FileNotFoundException: plugins\AuthMe\GeoLite2-Country.mmdb (系统找不到指定的文件。)
	at java.base/java.io.RandomAccessFile.open0(Native Method)
	at java.base/java.io.RandomAccessFile.open(RandomAccessFile.java:356)
	at java.base/java.io.RandomAccessFile.<init>(RandomAccessFile.java:273)
	at java.base/java.io.RandomAccessFile.<init>(RandomAccessFile.java:223)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.libs.com.maxmind.db.BufferHolder.<init>(BufferHolder.java:20)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.libs.com.maxmind.db.Reader.<init>(Reader.java:121)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.service.GeoIpService.startReading(GeoIpService.java:179)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.service.GeoIpService.updateDatabase(GeoIpService.java:150)
	at org.bukkit.craftbukkit.scheduler.CraftTask.run(CraftTask.java:78)
	at org.bukkit.craftbukkit.scheduler.CraftAsyncTask.run(CraftAsyncTask.java:57)
	at com.destroystokyo.paper.ServerSchedulerReportingWrapper.run(ServerSchedulerReportingWrapper.java:22)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)

[01-19 08:43:40]: [INFO] SQLite Setup finished
[01-19 08:43:40]: [INFO] Hooked into LuckPerms!
[01-19 08:43:40]: [INFO] Hooked successfully into Essentials
[01-19 08:43:40]: [INFO] Essentials spawn file not found: 'D:\下载\【1.21.4】[mienbbs]Tian服务端\Tian1.21.4服务端\plugins\Essentials\spawn.yml'
[01-19 08:43:40]: [INFO] AuthMe 5.6.0-SNAPSHOT build n.2623 successfully enabled!
[01-19 08:43:43]: [INFO] Downloading GEO IP database, because the old database is older than 30 days or doesn't exist
[01-19 08:43:43]: [WARN] No MaxMind credentials found in the configuration file! GeoIp protections will be disabled.
[01-19 08:43:43]: [INFO] There is no newer GEO IP database uploaded to MaxMind. Using the old one for now.
[01-19 08:43:43]: [WARN] Could not download GeoLiteAPI database [FileNotFoundException]: plugins\AuthMe\GeoLite2-Country.mmdb (系统找不到指定的文件。)
[01-19 08:43:43]: java.io.FileNotFoundException: plugins\AuthMe\GeoLite2-Country.mmdb (系统找不到指定的文件。)
	at java.base/java.io.RandomAccessFile.open0(Native Method)
	at java.base/java.io.RandomAccessFile.open(RandomAccessFile.java:356)
	at java.base/java.io.RandomAccessFile.<init>(RandomAccessFile.java:273)
	at java.base/java.io.RandomAccessFile.<init>(RandomAccessFile.java:223)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.libs.com.maxmind.db.BufferHolder.<init>(BufferHolder.java:20)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.libs.com.maxmind.db.Reader.<init>(Reader.java:121)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.service.GeoIpService.startReading(GeoIpService.java:179)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.service.GeoIpService.updateDatabase(GeoIpService.java:150)
	at org.bukkit.craftbukkit.scheduler.CraftTask.run(CraftTask.java:78)
	at org.bukkit.craftbukkit.scheduler.CraftAsyncTask.run(CraftAsyncTask.java:57)
	at com.destroystokyo.paper.ServerSchedulerReportingWrapper.run(ServerSchedulerReportingWrapper.java:22)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)

[01-19 08:44:07]: [FINE] tiancuocuo registered 127.0.0.1
[01-19 08:44:07]: [FINE] The user tiancuocuo has 2 accounts:
[01-19 08:44:07]: [FINE] §7tiancuo, §atiancuocuo§7.
[01-19 08:44:07]: [FINE] tiancuocuo logged in 127.0.0.1
[01-19 08:47:37]: [INFO] SQLite Setup finished
[01-19 08:47:37]: [INFO] Hooked into LuckPerms!
[01-19 08:47:37]: [INFO] Hooked successfully into Essentials
[01-19 08:47:37]: [INFO] Essentials spawn file not found: 'D:\下载\【1.21.4】[mienbbs]Tian服务端\Tian1.21.4服务端\plugins\Essentials\spawn.yml'
[01-19 08:47:37]: [INFO] AuthMe 5.6.0-SNAPSHOT build n.2623 successfully enabled!
[01-19 08:47:40]: [INFO] Downloading GEO IP database, because the old database is older than 30 days or doesn't exist
[01-19 08:47:40]: [WARN] No MaxMind credentials found in the configuration file! GeoIp protections will be disabled.
[01-19 08:47:40]: [INFO] There is no newer GEO IP database uploaded to MaxMind. Using the old one for now.
[01-19 08:47:40]: [WARN] Could not download GeoLiteAPI database [FileNotFoundException]: plugins\AuthMe\GeoLite2-Country.mmdb (系统找不到指定的文件。)
[01-19 08:47:40]: java.io.FileNotFoundException: plugins\AuthMe\GeoLite2-Country.mmdb (系统找不到指定的文件。)
	at java.base/java.io.RandomAccessFile.open0(Native Method)
	at java.base/java.io.RandomAccessFile.open(RandomAccessFile.java:356)
	at java.base/java.io.RandomAccessFile.<init>(RandomAccessFile.java:273)
	at java.base/java.io.RandomAccessFile.<init>(RandomAccessFile.java:223)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.libs.com.maxmind.db.BufferHolder.<init>(BufferHolder.java:20)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.libs.com.maxmind.db.Reader.<init>(Reader.java:121)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.service.GeoIpService.startReading(GeoIpService.java:179)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.service.GeoIpService.updateDatabase(GeoIpService.java:150)
	at org.bukkit.craftbukkit.scheduler.CraftTask.run(CraftTask.java:78)
	at org.bukkit.craftbukkit.scheduler.CraftAsyncTask.run(CraftAsyncTask.java:57)
	at com.destroystokyo.paper.ServerSchedulerReportingWrapper.run(ServerSchedulerReportingWrapper.java:22)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)

[01-19 08:47:53]: [FINE] tiancuocuo used the wrong password
[01-19 08:47:57]: [FINE] The user tiancuocuo has 2 accounts:
[01-19 08:47:57]: [FINE] §7tiancuo, §atiancuocuo§7.
[01-19 08:47:57]: [FINE] tiancuocuo logged in 127.0.0.1
[07-26 20:55:17]: [INFO] SQLite Setup finished
[07-26 20:55:17]: [INFO] Hooked into LuckPerms!
[07-26 20:55:17]: [INFO] Hooked successfully into Essentials
[07-26 20:55:17]: [INFO] Essentials spawn file not found: 'C:\Users\<USER>\Downloads\【1.21.4】[mienbbs]Tian服务端\Tian1.21.4服务端\plugins\Essentials\spawn.yml'
[07-26 20:55:17]: [WARN] WARNING! The protectInventory feature requires ProtocolLib! Disabling it...
[07-26 20:55:17]: [INFO] AuthMe 5.6.0-SNAPSHOT build n.2623 successfully enabled!
[07-26 20:55:21]: [INFO] Downloading GEO IP database, because the old database is older than 30 days or doesn't exist
[07-26 20:55:21]: [WARN] No MaxMind credentials found in the configuration file! GeoIp protections will be disabled.
[07-26 20:55:21]: [INFO] There is no newer GEO IP database uploaded to MaxMind. Using the old one for now.
[07-26 20:55:21]: [WARN] Could not download GeoLiteAPI database [FileNotFoundException]: plugins\AuthMe\GeoLite2-Country.mmdb (系统找不到指定的文件。)
[07-26 20:55:21]: java.io.FileNotFoundException: plugins\AuthMe\GeoLite2-Country.mmdb (系统找不到指定的文件。)
	at java.base/java.io.RandomAccessFile.open0(Native Method)
	at java.base/java.io.RandomAccessFile.open(RandomAccessFile.java:356)
	at java.base/java.io.RandomAccessFile.<init>(RandomAccessFile.java:273)
	at java.base/java.io.RandomAccessFile.<init>(RandomAccessFile.java:223)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.libs.com.maxmind.db.BufferHolder.<init>(BufferHolder.java:20)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.libs.com.maxmind.db.Reader.<init>(Reader.java:121)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.service.GeoIpService.startReading(GeoIpService.java:179)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.service.GeoIpService.updateDatabase(GeoIpService.java:150)
	at org.bukkit.craftbukkit.scheduler.CraftTask.run(CraftTask.java:78)
	at org.bukkit.craftbukkit.scheduler.CraftAsyncTask.run(CraftAsyncTask.java:57)
	at com.destroystokyo.paper.ServerSchedulerReportingWrapper.run(ServerSchedulerReportingWrapper.java:22)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)

[07-26 21:04:15]: [INFO] SQLite Setup finished
[07-26 21:04:15]: [INFO] Hooked into LuckPerms!
[07-26 21:04:15]: [INFO] Hooked successfully into Essentials
[07-26 21:04:15]: [INFO] Essentials spawn file not found: 'C:\Users\<USER>\Downloads\【1.21.4】[mienbbs]Tian服务端\Tian1.21.4服务端\plugins\Essentials\spawn.yml'
[07-26 21:04:15]: [WARN] WARNING! The protectInventory feature requires ProtocolLib! Disabling it...
[07-26 21:04:15]: [INFO] AuthMe 5.6.0-SNAPSHOT build n.2623 successfully enabled!
[07-26 21:04:20]: [INFO] Downloading GEO IP database, because the old database is older than 30 days or doesn't exist
[07-26 21:04:20]: [WARN] No MaxMind credentials found in the configuration file! GeoIp protections will be disabled.
[07-26 21:04:20]: [INFO] There is no newer GEO IP database uploaded to MaxMind. Using the old one for now.
[07-26 21:04:20]: [WARN] Could not download GeoLiteAPI database [FileNotFoundException]: plugins\AuthMe\GeoLite2-Country.mmdb (系统找不到指定的文件。)
[07-26 21:04:20]: java.io.FileNotFoundException: plugins\AuthMe\GeoLite2-Country.mmdb (系统找不到指定的文件。)
	at java.base/java.io.RandomAccessFile.open0(Native Method)
	at java.base/java.io.RandomAccessFile.open(RandomAccessFile.java:356)
	at java.base/java.io.RandomAccessFile.<init>(RandomAccessFile.java:273)
	at java.base/java.io.RandomAccessFile.<init>(RandomAccessFile.java:223)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.libs.com.maxmind.db.BufferHolder.<init>(BufferHolder.java:20)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.libs.com.maxmind.db.Reader.<init>(Reader.java:121)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.service.GeoIpService.startReading(GeoIpService.java:179)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.service.GeoIpService.updateDatabase(GeoIpService.java:150)
	at org.bukkit.craftbukkit.scheduler.CraftTask.run(CraftTask.java:78)
	at org.bukkit.craftbukkit.scheduler.CraftAsyncTask.run(CraftAsyncTask.java:57)
	at com.destroystokyo.paper.ServerSchedulerReportingWrapper.run(ServerSchedulerReportingWrapper.java:22)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)

[07-26 21:09:28]: [INFO] SQLite Setup finished
[07-26 21:09:28]: [INFO] Hooked into LuckPerms!
[07-26 21:09:28]: [INFO] Hooked successfully into Essentials
[07-26 21:09:28]: [INFO] Essentials spawn file not found: 'C:\Users\<USER>\Downloads\【1.21.4】[mienbbs]Tian服务端\Tian1.21.4服务端\plugins\Essentials\spawn.yml'
[07-26 21:09:28]: [WARN] WARNING! The protectInventory feature requires ProtocolLib! Disabling it...
[07-26 21:09:28]: [INFO] AuthMe 5.6.0-SNAPSHOT build n.2623 successfully enabled!
[07-26 21:09:32]: [INFO] Downloading GEO IP database, because the old database is older than 30 days or doesn't exist
[07-26 21:09:32]: [WARN] No MaxMind credentials found in the configuration file! GeoIp protections will be disabled.
[07-26 21:09:32]: [INFO] There is no newer GEO IP database uploaded to MaxMind. Using the old one for now.
[07-26 21:09:32]: [WARN] Could not download GeoLiteAPI database [FileNotFoundException]: plugins\AuthMe\GeoLite2-Country.mmdb (系统找不到指定的文件。)
[07-26 21:09:32]: java.io.FileNotFoundException: plugins\AuthMe\GeoLite2-Country.mmdb (系统找不到指定的文件。)
	at java.base/java.io.RandomAccessFile.open0(Native Method)
	at java.base/java.io.RandomAccessFile.open(RandomAccessFile.java:356)
	at java.base/java.io.RandomAccessFile.<init>(RandomAccessFile.java:273)
	at java.base/java.io.RandomAccessFile.<init>(RandomAccessFile.java:223)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.libs.com.maxmind.db.BufferHolder.<init>(BufferHolder.java:20)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.libs.com.maxmind.db.Reader.<init>(Reader.java:121)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.service.GeoIpService.startReading(GeoIpService.java:179)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.service.GeoIpService.updateDatabase(GeoIpService.java:150)
	at org.bukkit.craftbukkit.scheduler.CraftTask.run(CraftTask.java:78)
	at org.bukkit.craftbukkit.scheduler.CraftAsyncTask.run(CraftAsyncTask.java:57)
	at com.destroystokyo.paper.ServerSchedulerReportingWrapper.run(ServerSchedulerReportingWrapper.java:22)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)

[07-26 21:09:58]: [FINE] tian_cuo registered **************
[07-26 21:09:58]: [FINE] tian_cuo logged in **************
[07-26 21:11:35]: [INFO] AuthMe 5.6.0-SNAPSHOT-b2623 disabled!
[07-26 21:12:01]: [INFO] SQLite Setup finished
[07-26 21:12:01]: [INFO] Hooked into LuckPerms!
[07-26 21:12:01]: [INFO] Hooked successfully into Essentials
[07-26 21:12:01]: [INFO] Essentials spawn file not found: 'C:\Users\<USER>\Downloads\【1.21.4】[mienbbs]Tian服务端\Tian1.21.4服务端\plugins\Essentials\spawn.yml'
[07-26 21:12:01]: [WARN] WARNING! The protectInventory feature requires ProtocolLib! Disabling it...
[07-26 21:12:01]: [INFO] AuthMe 5.6.0-SNAPSHOT build n.2623 successfully enabled!
[07-26 21:12:06]: [INFO] Downloading GEO IP database, because the old database is older than 30 days or doesn't exist
[07-26 21:12:06]: [WARN] No MaxMind credentials found in the configuration file! GeoIp protections will be disabled.
[07-26 21:12:06]: [INFO] There is no newer GEO IP database uploaded to MaxMind. Using the old one for now.
[07-26 21:12:06]: [WARN] Could not download GeoLiteAPI database [FileNotFoundException]: plugins\AuthMe\GeoLite2-Country.mmdb (系统找不到指定的文件。)
[07-26 21:12:06]: java.io.FileNotFoundException: plugins\AuthMe\GeoLite2-Country.mmdb (系统找不到指定的文件。)
	at java.base/java.io.RandomAccessFile.open0(Native Method)
	at java.base/java.io.RandomAccessFile.open(RandomAccessFile.java:356)
	at java.base/java.io.RandomAccessFile.<init>(RandomAccessFile.java:273)
	at java.base/java.io.RandomAccessFile.<init>(RandomAccessFile.java:223)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.libs.com.maxmind.db.BufferHolder.<init>(BufferHolder.java:20)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.libs.com.maxmind.db.Reader.<init>(Reader.java:121)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.service.GeoIpService.startReading(GeoIpService.java:179)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.service.GeoIpService.updateDatabase(GeoIpService.java:150)
	at org.bukkit.craftbukkit.scheduler.CraftTask.run(CraftTask.java:78)
	at org.bukkit.craftbukkit.scheduler.CraftAsyncTask.run(CraftAsyncTask.java:57)
	at com.destroystokyo.paper.ServerSchedulerReportingWrapper.run(ServerSchedulerReportingWrapper.java:22)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)

[07-29 01:32:57]: [INFO] SQLite Setup finished
[07-29 01:32:57]: [INFO] Hooked into LuckPerms!
[07-29 01:32:57]: [INFO] Hooked successfully into Essentials
[07-29 01:32:57]: [INFO] Essentials spawn file not found: 'D:\cursor\mc\server\plugins\Essentials\spawn.yml'
[07-29 01:32:57]: [WARN] WARNING! The protectInventory feature requires ProtocolLib! Disabling it...
[07-29 01:32:57]: [INFO] AuthMe 5.6.0-SNAPSHOT build n.2623 successfully enabled!
[07-29 01:33:01]: [INFO] Downloading GEO IP database, because the old database is older than 30 days or doesn't exist
[07-29 01:33:01]: [WARN] No MaxMind credentials found in the configuration file! GeoIp protections will be disabled.
[07-29 01:33:01]: [INFO] There is no newer GEO IP database uploaded to MaxMind. Using the old one for now.
[07-29 01:33:01]: [WARN] Could not download GeoLiteAPI database [FileNotFoundException]: plugins\AuthMe\GeoLite2-Country.mmdb (系统找不到指定的文件。)
[07-29 01:33:01]: java.io.FileNotFoundException: plugins\AuthMe\GeoLite2-Country.mmdb (系统找不到指定的文件。)
	at java.base/java.io.RandomAccessFile.open0(Native Method)
	at java.base/java.io.RandomAccessFile.open(RandomAccessFile.java:356)
	at java.base/java.io.RandomAccessFile.<init>(RandomAccessFile.java:273)
	at java.base/java.io.RandomAccessFile.<init>(RandomAccessFile.java:223)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.libs.com.maxmind.db.BufferHolder.<init>(BufferHolder.java:20)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.libs.com.maxmind.db.Reader.<init>(Reader.java:121)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.service.GeoIpService.startReading(GeoIpService.java:179)
	at [登录]AuthMe-5.6.0-SNAPSHOT.jar//fr.xephi.authme.service.GeoIpService.updateDatabase(GeoIpService.java:150)
	at org.bukkit.craftbukkit.scheduler.CraftTask.run(CraftTask.java:78)
	at org.bukkit.craftbukkit.scheduler.CraftAsyncTask.run(CraftAsyncTask.java:57)
	at com.destroystokyo.paper.ServerSchedulerReportingWrapper.run(ServerSchedulerReportingWrapper.java:22)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)

