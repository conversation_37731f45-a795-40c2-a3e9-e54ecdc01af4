# MiniMOTD Plugin Configuration

# Settings only applicable when running the plugin on a proxy (Velocity or Waterfall/Bungeecord)
proxy-settings {
    # Here you can assign configs in the 'extra-configs' folder to specific virtual hosts
    # Either use the name of the config in 'extra-configs', or use "default" to use the configuration in main.conf
    # 
    # Format is "hostname:port"="configName|default"
    # Parts of domains can be substituted for wildcards, i.e. "*.mydomain.com:25565". Wildcard-containing configs are
    # checked in the order they are declared if there are no exact matches.
    virtual-host-configs {
        "minigames.example.com:25565"=default
        "skyblock.example.com:25565"=skyblock
        "survival.example.com:25565"=survival
    }
    # Set whether to enable virtual host testing mode.
    # When enabled, MiniMOTD will print virtual host debug info to the console on each server ping.
    virtual-host-test-mode=false
}
# Do you want the plugin to check for updates on GitHub at launch?
# https://github.com/jpenilla/MiniMOTD
update-checker=true
