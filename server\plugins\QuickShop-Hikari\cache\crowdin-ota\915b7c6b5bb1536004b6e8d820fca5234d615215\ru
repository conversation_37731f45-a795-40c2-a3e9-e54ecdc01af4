break-shop-use-supertool: <yellow>Вы можете сломать магазин с помощью супер-инструмента.
fee-charged-for-price-change: <green>Вы заплатили <red>{0}<green> за изменение цены.
not-allowed-to-create: <red>Вы не можете создать магазин здесь.
disabled-in-this-world: <red>QuickShop отключен в этом мире
how-much-to-trade-for: <green>Введите в чат, за сколько вы хотите продавать <yellow>{1}x {0}</yellow>.
client-language-changed: <green>QuickShop обнаружил, что вы изменили язык. Теперь для вас используется {0} язык.
shops-backingup: Создание резервной копии магазина из базы данных...
_comment: Привет, переводчик! Если ты редактируешь это через GitHub или исходный код, тебе стоит делать это здесь https://crowdin.com/project/quickshop-hikari.
unlimited-shop-owner-changed: '<yellow>Владелец бесконечного магазина был изменен на: {0}.'
bad-command-usage-detailed: '<red>Неправильные аргументы для команды! Принимаются только эти параметры: <gray>{0}'
thats-not-a-number: <red>Неверное число
shop-name-disallowed: <red>Название магазина <yellow>{0}</yellow> запрещено. Выберите другое!
console-only-danger: <red>Это опасная команда, поэтому она доступна только для консоли.
not-a-number: <red>Можно ввести только число, но вы ввели {0}.
not-looking-at-valid-shop-block: <red>Не удалось создать магазин. Посмотрите поддерживаемые типы блоков и повторите попытку.
shop-removed-cause-ongoing-fee: <red>Ваш магазин на {0} был удалён, потому что у вас нет денег для продления аренды!
tabcomplete:
  amount: '[количество]'
  item: '[товар]'
  price: '[цена]'
  name: '[название]'
  range: '[диапазон]'
  currency: '[название валюты]'
  percentage: '[процент%]'
taxaccount-unset: <green>Налоговый счет магазина теперь соблюдает глобальные настройки сервера.
blacklisted-item: <red>Этот предмет нельзя продать, поскольку он находится в черном списке
command-type-mismatch: <red>Эта команда может быть выполнена только <aqua>{0}</aqua>.
server-crash-warning: '<red>ВНИМАНИЕ: Использование команды /qs reload для замены/удаления файла QuickShop.jar может привести к сбою сервера.'
you-cant-afford-to-change-price: <red>Для изменения цены в вашем магазине требуется {0}.
safe-mode: <red>QuickShop сейчас находится в безопасном режиме, вы не можете открыть этот магазин. Пожалуйста, свяжитесь с администратором сервера, чтобы исправить ошибку.
forbidden-vanilla-behavior: <red>Операция отклонена - она не соответствует ванильному поведению
shop-out-of-space-name: <dark_purple>Ваш магазин {0} полон!
paste-disabled: |-
  <red>Функция вставки отключена! Вы не можете запросить техническую поддержку.<newline>Причина: {0}
quick-fill:
  entry-own: ' <yellow>- <red>[собственный] <aqua>{0} <light_purple>{1}'
  hover:
    - '<yellow>Название: <aqua>{0}'
    - '<yellow>Владелец: <aqua>{0}'
    - '<yellow>Тип: <aqua>{0}'
    - '<yellow>Цена: <aqua>{0}'
    - '<yellow>Предмет: <aqua>{0}'
    - '<yellow>Локация: <aqua>{0}'
  hover-arg-filled:
    - '<yellow>Название: <aqua>{name}'
    - '<yellow>Владелец: <aqua>{owner}'
    - '<yellow>Тип: <aqua>{type}'
    - '<yellow>Цена: <aqua>{price}'
    - '<yellow>Предмет: <aqua>{item}'
    - '<yellow>Локация: <aqua>{location}'
  header: '<yellow>You have multiple shops with name "<green>{0}<yellow>", choose one to continue:'
  entry-normal: ' <yellow>- <aqua>{0} <light_purple>{1}'
  entry-cooperation: ' <yellow>- <red>[совместный] <aqua>{0} <light_purple>{1}'
3rd-plugin-build-check-failed-admin: '<gray>(Админ) <light_purple>{0} <dark_gray>ошибка при проверке разрешения. Если это не ожидаемое поведение попробуйте добавить <light_purple>{1} <gray>в чёрный список. <gray>Гайд по настройке: https://ghost-chu.github.io/QuickShop-Hikari-Documents/docs/modules/shops/protection-checker'
average-price-nearby: '<green>Средняя цена: <yellow>{0}'
inventory-check-global-alert: "<red>[Проверка Инвентаря] <gray> Внимание! Найден отображаемый предмет БыстрогоМагазина <gold>{2}</gold> в инвентаре в <aqua>{0}</aqua> <dark_gray>[{1}]</dark_gray>, что не должно было произойти, обычно это значит, что кто-то эксплуатирует уязвимость для дублирования отображаемого предмета."
digits-reach-the-limit: <red>Вы достигли пределов десятичных знаков в цене.
currency-unset: <green>Валюта магазина сброшена. Будет использована по умолчанию.
you-cant-create-shop-in-there: <red>У вас нет прав на создание магазины в текущем месте.
no-pending-action: <red>У вас нет ожидающих действий
refill-success: <green>Пополнение успешно
failed-to-paste: <red>Не удалось загрузить данные в Pastebin. Проверьте подключение к Интернету и повторите попытку. (Подробнее в консоли)
shop-out-of-stock-name: <dark_purple>В вашем магазине {0} кончилось {1}!
shop-name-invalid: <red>Название магазина <yellow>{0}</yellow> недействительно. Выберите другое!
how-many-buy-stack: <yellow>Введите в чат, сколько партий вы хотите <aqua>КУПИТЬ<green>. Всего <yellow>{0}<green> предметов в каждой партии и вы можете купить <yellow>{1}<green> партий. Введите <aqua>{2}<green> в чате, чтобы купить все.
exceeded-maximum: <red>Значение превысило максимальное значение в Java.
unlimited-shop-owner-keeped: '<yellow>Внимание: Владелец магазина всё ещё владеет бесконечным магазином - вам нужно установить нового владельца самостоятельно.'
no-enough-money-to-keep-shops: <red>У вас нет средств для аренды магазинов! Все магазины были удалены...
3rd-plugin-build-check-failed: <red>Сторонний плагин <bold>{0}</bold> запретил проверку прав доступа - вы имеете разрешение на это действие?
not-a-integer: <red>Вы должны ввести целое число, но вы ввели {0}.
translation-country: 'Языковой регион: Русский (ru_RU)'
buying-more-than-selling: '<red>ВНИМАНИЕ: Вы покупаете товары дороже, чем продаёте!'
purchase-failed: '<red>Покупка не удалась: внутренняя ошибка. Пожалуйста, свяжитесь с администратором сервера.'
denied-put-in-item: <red>Вы не можете поместить этот товар в свой магазин!
shop-has-changed: <red>Магазин, который вы пытались использовать, был изменен!
flush-finished: <green>Сообщения успешно очищены.
no-price-given: <red>Пожалуйста, укажите корректную цену.
shop-already-owned: <red>Вы уже создали этот магазин.
backup-success: <green>Резервное копирование успешно.
not-looking-at-shop: <red>Не удалось найти магазин, возможно не зарегистрирован.
you-cant-afford-a-new-shop: <red>Создание магазина стоит {0}.
success-created-shop: <red>Магазин создан.
shop-creation-cancelled: <red>Создание магазина отменено.
shop-owner-self-trade: <yellow>Вы торгуете с собственным магазином и не получите никакой прибыли.
purchase-out-of-space: <red>В этом магазине закончилось место, свяжитесь с владельцем магазина или сотрудниками, чтобы забрать из него предметы.
reloading-status:
  success: <green>Перезагрузка завершена без ошибок.
  scheduled: <green>Перезагрузка завершена. <gray>(Для вступления в силу некоторых изменений потребуется время)
  require-restart: <green>Перезагрузка завершена. <yellow>(Для изменения интерфейса потребуется перезапуск сервера)
  failed: <red>Перезагрузка не удалась, проверьте консоль сервера
player-bought-from-your-store-tax: <green>{0} купил {1} {2} в вашем магазине, вы заработали {3} (налог {4}).
not-enough-space: <red>В вашем инвентаре осталось место только для {0} предметов!
shop-name-success: <green>Название магазина успешно изменено на <yellow>{0}<green>.
shop-staff-added: <green>{0} успешно добавлен в качестве сотрудника вашего магазина.
shop-staff-empty: <yellow>В этом магазине нет сотрудников.
shops-recovering: Восстановление магазинов из резервной копии...
virtual-player-component-hover: "<gray> Является виртуальным игроком. \n<gray> Обращается к системному аккаунту этого имени с таким же именем. </gray>\n<green>UUID: <yellow>{0}</yellow></green>\n<green>Имя Пользователя: <yellow>{1}</yellow></green>\n<green> Показывать как: <yellow>{2}</yellow></green>"
real-player-component-hover: "<gray>Это реальный игрок.\n<green>UUID: <yellow>{0}</yellow></green>\n<green>Имя пользователя: <yellow>{1}</yellow></green>\n<green>Отображение как: <yellow>{2}</yellow></green>\n<gray>Если вы хотите использовать виртуальный аккаунт системы с тем же именем, добавьте <dark_gray>\"[]\"</dark_gray> к обе стороны имени пользователя: <dark_gray>[{1}]</dark_gray>."
menu:
  sell-tax: <green>Вы заплатили <yellow>{0} <green>налогов.
  owner: '<green>Владелец: {0}'
  preview: <aqua>[Просмотреть товар]
  enchants: <dark_purple>Чары
  sell-tax-self: <green>Вы не платите налогов, вы владеете магазином.
  shop-information: '<green>Информация о магазине:'
  item: '<green>Товар: <yellow>{0}'
  price-per: <green>Цена за <yellow>{0} <green>- <yellow>{1}
  item-name-and-price: <yellow>{0} {1} <green>за <yellow>{2}
  item-name-and-price-tax: <yellow>{0} {1} <green>за</green> {2} <gray>(налог <green>{3}</green>)</gray>
  successful-purchase: '<green>Успешно куплено:'
  price-per-stack: <green>Цена за <yellow>{2}x {0} - {1}
  stored-enchants: <dark_purple>Хранимые чары
  item-holochat-error: <red>[Ошибка]
  this-shop-is-selling: <green>Этот магазин <aqua>ПРОДАЕТ</aqua> предметы.
  shop-stack: '<green>Кол-во партий: <yellow>{0}'
  space: '<green>Свободное место: <yellow>{0}'
  effects: <green>Эффекты
  damage-percent-remaining: <green>Осталось <yellow>{0}%.
  item-holochat-data-too-large: <red>[Error] Элемент NBT слишком большой для отображения
  stock: '<green>Доступно: <yellow>{0}'
  this-shop-is-buying: <green>Этот магазин <light_purple>ПОКУПАЕТ<green> предметы.
  successfully-sold: '<green>Успешно продано:'
  total-value-of-chest: '<green>Общая цена сундука: <yellow>{0}'
currency-not-exists: <red>Не удается найти валюту, которую вы хотите установить. Возможно, вы ошиблись, или эта валюта недоступна в данном мире.
no-nearby-shop: <red>Нет близлежащих магазинов, соответствующих {0}.
translation-author: 'Xellero'
integrations-check-failed-trade: <red>Интеграция {0} не позволяет провести сделку
shop-transaction-failed: <red>Извините, но при обработке вашей покупки произошла внутренняя ошибка. Покупка была отменена. Пожалуйста, свяжитесь с администраторами сервера, если эта ошибка повторяется.
success-change-owner-to-server: <green>Теперь сам сервер владеет этим магазином.
shop-name-not-found: <red>Магазин с названием <yellow>{0}</yellow> не существует.
shop-name-too-long: <red>Это имя магазина слишком длинное (максимальная длина {0}) - пожалуйста, выберете другое!
metric:
  header-player: '<yellow>Транзакции игрока {0} {1} {2}:'
  action-hover: <yellow>{0}
  price-hover: <yellow>Всего {0}, включая {1} налогов.
  unknown: <gray>(неизвестный)
  undefined: <gray>(без имени)
  no-results: <red>Транзакции не найдены.
  action-description:
    DELETE: <yellow>Игрок удалил магазин. И по возможности возвратил владельцу комиссию за создание магазина.
    ONGOING_FEE: <yellow>Игрок заплатил комиссию за период оплаты.
    PURCHASE_BUYING_SHOP: <yellow>Игрок продал предметы в магазине.
    CREATE: <yellow>Игрок создал магазин.
    PURCHASE_SELLING_SHOP: <yellow>Игрок купил предметы в магазине
    PURCHASE: <yellow>Купил предмет в магазине
  query-argument: 'Аргумент запроса: {0}'
  amount-hover: <yellow>{0}х
  header-shop: '<yellow>Транзакции магазина {0} {1} {2}:'
  player-hover: |-
    <yellow>{0}<newline><gold>UUID: <gray>{1}
  looking-up: <yellow>Высчитывается статистика. Пожалуйста, подождите...
  tax-hover: <yellow>{0} налогов
  header-global: '<yellow>Сервер {0} {1} транзакций:'
  na: <gray>Н/Д
  transaction-count: <yellow>{0} всего
  shop-hover: |-
    <yellow>{0}<newline><gold>Позиция: <gray>{1} {2} {3}, Мир: {4}<newline><gold>Владелец: <gray>{5}<newline><gold>Тип магазина: <gray>{6}<newline><gold>Предмет: <gray>{7}<newline><gold>Цена: <gray>{8}
  time-hover: '<yellow>Время: {0}'
  amount-stack-hover: <yellow>{0}x стаков
permission-denied-3rd-party: <red>Доступ запрещен сторонним плагином {0}.
you-dont-have-that-many-items: <red>У вас всего {0} {1}.
complete: <green>Выполнено!
translate-not-completed-yet-url: 'Перевод языка {0} ещё не завершен пользователями {1}. Хотите помочь улучшить перевод? Перейдите по ссылке: {2}'
success-removed-shop: <green>Магазин удален.
currency-set: <green>Валюта магазина успешна установлена на {0}.
shop-purged-start: <green>Начата очистка магазина, проверьте консоль для получения подробностей.
economy-transaction-failed: <red>Извините, при обработке транзакции произошла внутренняя ошибка. Транзакция и все экономические изменения отменены. Если ошибка повторяется, пожалуйста, свяжитесь с администрацией сервера.
nothing-to-flush: <green>У вас нет новых сообщений в магазине.
no-price-change: <red>Указанная вами цена идентична предыдущей!
edition-confilct: QuickShop-Hikari и QuickShop-Remake могут конфликтовать. Удалите один из этих плагинов.
inventory-unavailable: |-
  <red>Несуществующий или неверный InventoryWrapper. Вы используете аддон для повторной привязки инвентаря магазина?<newline> Информация: InventoryWrapper={0}, WrapperProvider={1}, SymbolLink={2}. Пожалуйста, свяжитесь с администрацией сервера.
file-test: Это тестовый текстовый файл. Мы используем его для проверки повреждения файла messages.json. Сюда вы можете написать что угодно :) Как же так, ректор Кудрявцев?; Всполох - плохой перевод для Блейза.
unknown-player: <red>Такого игрока не существует. Пожалуйста, проверьте введенное вами имя игрока.
player-offline: <red>Этот игрок сейчас офлайн.
player-profile-format: <aqua>{0}</aqua><gold>(<yellow>{1}</yellow>)</gold>
shop-type:
  selling: ПРОДАЖА
  buying: ПОКУПКА
language:
  qa-issues: '<yellow>Вопросы обеспечения качества: <aqua>{0}%'
  code: '<yellow>Код: <gold>{0}'
  approval-progress: '<yellow>Прогресс утверждения: <aqua>{0}%'
  translate-progress: '<yellow>Прогресс перевода: <aqua>{0}%'
  name: '<yellow>Название: <gold>{0}'
  help-us: <green>[Помогите нам улучшить качество перевода]
warn-to-paste: |-
  <yellow>Сбор данных и загрузка их в Pastebin, это может занять некоторое время. <red><bold>Предупреждение:<red> Данные хранятся в течение одной недели! Это может привести к утечке конфигурации вашего сервера и другой конфиденциальной информации. Убедитесь, что вы отправляете его только доверенным персоналу/разработчикам.
how-many-sell-stack: <yellow>Введите в чат, сколько массу вы хотите <aqua>ПРОДАТЬ&а. <yellow>{0} товар в каждой массой и вы можете продать за <yellow>{1}<green>. Введите <aqua>{2}<green> в чате, чтобы купить все.
updatenotify:
  buttontitle: '[Обновить]'
  onekeybuttontitle: '[Обновить в один клик]'
  label:
    github: '[GitHub]'
    ore: '[Ore]'
    lts: '[LTS]'
    nbtapi: '[+NBTAPI]'
    qualityverifyed: '[Quality]'
    master: '[Master]'
    unstable: '[Unstable]'
    paper: '[+Paper Optimized]'
    stable: '[Stable]'
    spigotmc: '[SpigotMC]'
    papermc: '[PaperMC]'
    bukkitdev: '[BukkitDev]'
    polymart: '[Polymart]'
    modrinth: '[Modrinth]'
    basic: '[Basic]'
  list:
    - 'Вышла версия {0}. Вы всё ещё используете устаревшую {1}!'
    - Вжух! Вышла новая версия {0}. Обновитесь!
    - Сюрприз! Вышла версия {0}. Вы используете {1}
    - Пора бы обновиться. Вышла версия {0}!
    - Опа! Версия {0} уже вышла. Вы используете {1}!
    - Клянусь, QS был обновлен до {0}. Почему вы ещё не обновились?
    - Исправления и пере... Извините, но версия {0} была выпущен!
    - Ба! Нет, не баг. Версия {0} уже вышла!
    - Боже мой! Версия {0} уже вышла! Почему вы используете {1}?
    - 'Новости: Появились новое обновление QuickShop на {0}!'
    - Плагин ослабляет. Вы должны обновить до {0}!
    - Обновление {0} запущено. Сохранено обновление!
    - Доступно обновление QuickShop. {0} только что вышел!
    - Смотрите мой стиль---{0} обновление. Вы все еще используете {1}
    - Аххххх! Новое обновление {0}! Обновитесь!
    - Что вы думаете? {0} был выпущен! Обновитесь!
    - Доктор, QuickShop уже есть новое обновление {0}! Вы должны обновить~
    - Ко~ко~да~ё~ QuickShop появились новое обновление {0} ~
    - Paimon хочет сообщить, что QuickShop имеет новое обновление {0}!
  remote-disable-warning: '<red>Текущая версия QuickShop отмечена как отключенная удалённым сервером, означая, что эта версия может иметь серьезные проблемы, получить подробную информацию с нашей страницы плагина SpigotMC: {0}. Это предупреждение будет отображаться до тех пор, пока вы не переключитесь на стабильную версию, но это не повлияет на перфорирование вашего сервера.'
purchase-out-of-stock: <red>В этом магазине закончился товар. Попросите его владельца или сотрудников пополнить его.
nearby-shop-entry: '<green>- Информация:{0} <green>Цена:<aqua>{1} <green>x:<aqua>{2} <green>y:<aqua>{3} <green>z:<aqua>{4} <green>Расстояние: <aqua>{5} <green>блок(а/ов)'
chest-title: Магазин QuickShop
console-only: <red>Эта команда может быть выполнена только консолью.
failed-to-put-sign: <red>Недостаточно места вокруг магазина для размещения информации на табличке.
shop-name-unset: <red>Название этого магазина теперь удалено
shop-nolonger-freezed: <green>Вы разморозили магазин. Теперь он работает в обычном режиме!
no-permission-build: <red>Вы не можете построить магазин здесь.
tableformat:
  left_half_line: <dark_purple>+--------------------
  right_half_line: <dark_purple>--------------------+
  full_line: <dark_purple>+---------------------------------------------------+
  left_begin: '<dark_purple>| '
quickshop-gui-preview: Просмотр GUI товара
translate-not-completed-yet-click: Перевод языка {0} ещё не завершен пользователями {1}. Хотите помочь улучшить перевод? Нажмите сюда!
taxaccount-invalid: <red>Текущий аккаунт налогового счета неизвестен, пожалуйста, введите правильное имя игрока или uuid(с тире).
player-bought-from-your-store: <green>{0} купил {1} {2} из вашего магазины, вы заработали {3}.
reached-maximum-can-create: <red>Вы уже создали максимум {0}/{1} магазинов!
reached-maximum-create-limit: <red>Вы достигли максимального количества созданных магазинов
translation-version: 'Поддерживаемая версия: Hikari'
no-double-chests: <red>Вы не можете создать магазин из двойных сундуков.
price-too-cheap: <red>Цена должна быть больше <yellow>${0}
shop-not-exist: <red>Такого магазина нет.
bad-command-usage: <red>Некорректные аргументы команды!
cleanghost-warning: <yellow>This command will purge <red>all</red> shops if the shop is corrupted, was created in not allowed worlds, is selling/buying not allowed items or <red><bold>EXISTS IN A UNLOADED WORLD</bold></red>. Make sure to create a full backup of your shop data first and use <aqua>/qs cleanghost confirm</aqua> to continue.
cleanghost-starting: <green>Начало проверки на наличие призрачных магазинов (недостающие блоки контейнеров). Все несуществующие магазины будут удалены...
cleanghost-deleting: <yellow>Нашли испорченный магазин <aqua>{0}</aqua> потому что {1}, отметьте его для удаления...
cleanghost-deleted: <green>Всего <yellow>{0}</yellow> магазины были удалены.
shop-purchase-cancelled: <red>Покупка в магазине отменена.
bypassing-lock: <red>Обход блокировки магазина!
bungee-cross-server-msg: '<yellow>QuickShop CSM: <green>{0}'
saved-to-path: Резервная копия сохранена в {0}.
shop-now-freezed: <green>Вы заморозили магазин. Теперь никто не может торговать с этим магазином!
price-is-now: <green>Новая цена магазина <yellow>{0}
shops-arent-locked: <red>Помните - магазины НЕ защищены от кражи! Если вы хотите остановить воров, заприватьте его другим плагином!
that-is-locked: <red>Этот магазин заперт.
shop-has-no-space: <red>Магазин можно пополнять ещё {0} {1}.
safe-mode-admin: '<red><bold>Внимание: <red>QuickShop на этом сервере работает в безопасном режиме, никакие функции не работают, пожалуйста, введите команду <yellow>/qs <red> для проверки ошибок.'
shop-stock-too-low: <red>В магазине осталось {0} {1}!
world-not-exists: <red>Мир <yellow>{0}<red> не существует
how-many-sell: <green>Введите в чат, сколько предметов вы хотите <light_purple>ПРОДАТЬ</light_purple>. Вы можете продать <yellow>{0}</yellow>. Введите <aqua>{1}</aqua> в чате, чтобы продать все.
shop-freezed-at-location: <yellow>Ваш магазин {0} на {1} был заморожен!
translation-contributors: 'Участники: Timtower, Netherfoam, KaiNoMood, Mgazul, JackTheChicken и Andre_601'
empty-success: <green>Магазин успешно опустошен
taxaccount-set: <green>Налоговый счет этого магазина установлен на <yellow>{0}
support-disable-reason:
  hot-reload: <yellow>Неподдерживаемая перезагрузка. Советуем перезапустить сервер.
  outdated: <yellow>Эта версия QuickShop уже устарела. Обновите прежде, чем запросить поддержку!
  bad-hosts: |-
    <yellow>Хосты сервера были изменены и некоторые QuickShop-функции требуют подключения к Mojang API. Пожалуйста, исправьте настройки HOSTS перед тем, как запрашивать поддержку.<newline>Windows: C:\windows\system32\drivers\etc\hosts<newline>Linux: /etc/hosts
  privacy: <yellow>Этот сервер запущен в оффлайн режиме. Если вы запускаете сервер под прокси и в режиме онлайн, правильно настройте связанные с прокси настройки.
  modified: <yellow>Ошибка проверки целостности файлов. Эта сборка QuickShop была изменена третьими лицами.
  consolespamfix-installed: <yellow>ConsoleSpamFix установлен. Он скроет ошибки плагина. Отключите его временно, если захотите обратиться в поддержку.
  authlib-injector-detected: <yellow>Этот сервер работает со сторонней библиотекой авторизации, вроде AuthLib-Injector.
  unsupported-server-software: <yellow>Неподдерживаемое серверное программное обеспечение. Любые модифицированные программные средства не поддерживаются, включая MCPC, Cauldron, CatServer, Mohist, Magma, Fukkit, Cardboard, и т. д.
supertool-is-disabled: <red>Супер-инструмент отключен, вы не можете сломать чужой магазин.
unknown-owner: Неизвестно
restricted-prices: '<red>Цена ограничена {0}: от {1} до {2}'
nearby-shop-this-way: <green>Магазин находится в {0} блоках от вас.
owner-bypass-check: <yellow>Вы прошли все проверки, вы можете торговать! (Теперь вы владелец магазина!)
april-rick-and-roll-easter-egg: "<hover:show_text:'Click to open in browser for time limited rewards!'><click:open_url:'https://www.youtube.com/watch?v=dQw4w9WgXcQ'><green>Нажмите здесь, чтобы получить ограниченное количество наград, предоставленных разработчиком QuickShop-Hikari!</green></click></hover>"
signs:
  item-right: ''
  out-of-space: Нет места
  unlimited: Неограниченно
  stack-selling: Продажа {0}
  stack-price: '{0} за {1}x {2}'
  status-unavailable: <red>
  out-of-stock: Нет товара
  stack-buying: Покупка {0}
  freeze: Обмен отключен
  price: 'по {0}'
  buying: Покупает {0}
  header: '{1}{0}'
  header-available: <green>{0}
  header-unavailable: <red>{0}
  selling: Продает {0}
  status-available: <green>
  item-left: ''
negative-amount: <red>Вы не можете обменивать отрицательное число предметов
display-turn-on: <green>Отображение товара успешно включено.
shop-staff-deleted: <green>Сотрудник {0} успешно удален из вашего магазина.
nearby-shop-header: '<green>Ближайший магазин, соответствующий <aqua>{0}</aqua>:'
backup-failed: Не удается сделать резервную копию базы данных. Проверьте консоль для деталей.
shop-staff-cleared: <green>Все сотрудники успешно удалены из вашего магазина.
price-too-high: <red>Цена слишком высока! Вы не можете создать магазин с ценой выше {0}.
plugin-cancelled: '<red>Операция отменена. Причина: {0}'
player-sold-to-your-store: <green>{0} продал {1}x {2} в ваш магазин.
shop-out-of-stock: <dark_purple>В вашем магазине на {0}, {1}, {2} не осталось {3}!
how-many-buy: <green>Введите в чат, сколько вы хотите <aqua>КУПИТЬ<green>. Вы можете купить <yellow>{0}<green>. Введите <aqua>{1}<green> чтобы купить их все.
language-info-panel:
  help: 'Помогите нам: '
  code: 'Код: '
  name: 'Язык: '
  progress: 'Прогресс: '
  translate-on-crowdin: '[Перевести на Crowdin]'
item-not-exist: <red>Товар <yellow>{0} <red>не существует, проверьте правильность написания.
shop-creation-failed: <red>Не удалось создать магазин, свяжитесь с администратором сервера.
inventory-space-full: <red>Ваш инвентарь может вместить только <green>{1}x</green> предметов. Освободите место!
no-creative-break: <red>Вы не можете сломать чужой магазин в творческом режиме, переключитесь на режим выживания или попробуйте использовать супер-инструментом {0}.
booleanformat:
  success: <green>✔
  failed: <red>✘
controlpanel:
  stack: '<green>Цена за партию: <aqua>{0} <yellow>[<light_purple><bold>Изменить<yellow>]'
  price-hover: <yellow>Нажмите, чтобы установить новую цену для товара.
  remove: <red><bold>[Удалить магазин]
  mode-buying-hover: <yellow>Нажмите, чтобы изменить режим магазина на ПРОДАЖИ.
  empty: '<green>Опустошить: убрать все предметы <yellow>[<light_purple><bold>ОК<yellow>]'
  stack-hover: <yellow>Нажмите, чтобы установить количество товаров массу на единицу. Установите значение 1 для нормального поведения.
  alwayscounting-hover: <yellow>Нажмите, чтобы магазин всегда считал содержимое контейнера.
  alwayscounting: '<green>Всегда считать: {0} <yellow>[<light_purple><bold>Изменить<yellow>]'
  setowner: '<green>Владелец: <aqua>{0} <yellow>[<light_purple><bold>Изменить<yellow>]'
  freeze: '<yellow>Заморожен: <aqua>{0} <yellow>[<light_purple><bold>Изменить<yellow>]'
  price: '<green>Цена: <aqua>{0} <yellow>[<light_purple><bold>Изменить<yellow>]'
  currency-hover: <yellow>Нажмите, чтобы установить или удалить валюту, которую этот магазин использует
  lock: '<yellow>Заблокирован: <aqua>{0} <yellow>[<light_purple><bold>Переключить<yellow>]'
  mode-selling: '<green>Режим: <aqua>Продажа <yellow>[<light_purple><bold>Изменить<yellow>]'
  currency: '<green>Валюта: <aqua>{0} <yellow>[<light_purple><bold>Установить<yellow>]'
  setowner-hover: <yellow>Нажмите для смены владельца.
  mode-buying: '<green>Режим работы: <aqua>Покупка <yellow>[<light_purple><bold>Изменить<yellow>]'
  item: '<green>Товар: {0} <yellow>[<light_purple><bold>Изменить<yellow>]'
  unlimited: '<green>Бесконечный запас: {0} <yellow>[<light_purple><bold>Изменить<yellow>]'
  unlimited-hover: <yellow>Нажмите, чтобы переключить неограниченность магазина.
  refill-hover: <yellow>Нажмите, чтобы пополнить магазин.
  remove-hover: <yellow>Нажмите, чтобы удалить магазин.
  toggledisplay-hover: <yellow>Переключает состояние отображения товара над магазином
  refill: '<green>Пополнение: добавить предметов <yellow>[<light_purple><bold>OK<yellow>]'
  freeze-hover: <yellow>Переключить состояние заморозки магазина.
  lock-hover: <yellow>Переключить состояние блокировки магазина.
  item-hover: <yellow>Нажмите, чтобы изменить товар
  infomation: '<green>Панель управления магазином:'
  mode-selling-hover: <yellow>Нажмите, чтобы изменить режим магазина на ПОКУПКИ.
  empty-hover: <yellow>Нажмите, чтобы очистить инвентарь магазина.
  toggledisplay: '<green>Отображение предмета: <aqua>{0}<yellow>[<light_purple><bold>Переключить</bold></light_purple>]'
  history: '<green>История: <yellow>[<bold><light_purple>Вид</light_purple></bold>]</yellow>'
  history-hover: <yellow>Нажмите, чтобы просмотреть журналы истории магазина
timeunit:
  behind: назад
  week: "{0} неделя"
  weeks: "{0} недель"
  year: "{0} год"
  before: до
  scheduled: запланировано
  years: "{0} лет"
  scheduled-in: запланировано через {0}
  second: "{0} секунда"
  std-past-format: '{5}{4}{3}{2}{1}{0}назад'
  std-time-format: HH:mm:ss
  seconds: "{0} секунд"
  hour: "{0} час"
  scheduled-at: запланировано на {0}
  after: после
  day: "{0} день"
  recent: недавно
  between: между
  hours: "{0} часов"
  months: "{0} месяцев"
  longtimeago: давно
  between-format: между {0} и {1}
  minutes: "{0} минут"
  justnow: только что
  minute: "{0} минута"
  std-format: dd/MM/yyyy HH:mm:ss
  future-plain-text: будущее
  month: "{0} месяц"
  future: через {0}
  days: "{0} дней"
command:
  reloading: '<green>Конфигурация перезагружена. <yellow>Некоторые изменения могут потребовать рестарта. <newline><gray>(Примечание: перезагрузка была изменена после ********, теперь мы только перезагружаем конфигурацию, но не весь плагин, чтобы убедиться, что сервер не крашнется)'
  description:
    buy: <yellow>Изменяет режим магазина на <light_purple>КУПИТЬ<yellow>
    about: <yellow>Показывает информацию о плагине
    language: <yellow>Изменяет используемый язык
    purge: <green>Запускает задачи очистки магазина в фоновом режиме
    paste: <yellow>Загружает данные сервера на Pastebin
    title: <green>Справка по QuickShop
    remove: <yellow>Удаляет магазин, на который вы смотрите
    ban: <yellow>Запрещает игроку использовать магазин
    empty: <yellow>Удаляет все товары из магазина
    alwayscounting: <yellow>Установите, чтобы магазин всегда считал количество предметов в магазине
    setowner: <yellow>Меняет владельца магазина
    reload: <yellow>Перезагружает конфигурацию config.yml
    freeze: <yellow>Переключает торговлю магазином
    price: <yellow>Изменяет цену покупки/продажи магазина
    find: <yellow>Ищет ближайший магазин определенного типа
    create: <yellow>Создает новый магазин из сундука, на который вы смотрите
    lock: <yellow>Переключает состояние блокировки магазина
    currency: <yellow>Устанавливает или сбрасывает настройки валюты магазина
    removeworld: <yellow>Удаляет ВСЕ магазины в указанном мире
    info: <yellow>Показывает статистику плагина
    owner: <yellow>Меняет владельца магазина.
    amount: <yellow>Устанавливает заданное количество товара (при возникновении проблем с чатом)
    item: <yellow>Изменяет товар магазина
    debug: <yellow>Включает режим разработчика
    unlimited: <yellow>Дает магазину неограниченный товар
    sell: <yellow>Изменяет режим магазина на <aqua>ПРОДАТЬ<yellow>
    fetchmessage: <yellow>Показывает непрочитанные сообщения магазина
    staff: <yellow>Управляет персоналов вашего магазина
    clean: <yellow>Удаляет все (загруженные) магазины без товаров
    refill: <yellow>Добавляет заданное число товаров в магазин
    help: <yellow>Показывает справку плагина
    removeall: <yellow>Удаляет ВСЕ магазины указанного игрока
    unban: <yellow>Разрешает игроку использовать магазин
    transfer: <yellow>Переносит ВСЕ чьи-то магазины в другие
    transferall: <yellow>Переносит ВСЕ чьи-то магазины в другие
    transferownership: <yellow>Передайте магазин, который вы просматриваете, другому человеку
    size: <yellow>Изменяет заданного количество объем массы магазина
    supercreate: <yellow>Создает магазин проходя всех проверок защиты
    taxaccount: <yellow>Устанавливает налоговый счет, который магазин использует
    name: <yellow>Назначение имени магазину
    toggledisplay: <yellow>Переключает отображение товара в магазине (над сундука)
    permission: <yellow>Управление разрешениями магазина
    lookup: <yellow>Управление справочной таблицей предметов
    database: <yellow>Просмотр и обслуживание базы данных QuickShop
    benefit: <yellow>Настройки разделения выгоды между владельцем магазина и другими игроками
    tag: <yellow>Добавление, удаление или запрос тегов магазина
    suggestprice: <yellow>Предложить рекомендуемую цену для поиска магазина, основываясь на других магазинах
    history: <yellow>Просмотреть историю транзакций магазина
    sign: <yellow>Изменение материала вывески магазина
  bulk-size-not-set: '<red>Используйте: /qs size <количество>'
  no-type-given: '<red>Используйте: /qs find <предмет>'
  feature-not-enabled: Функция не включена в конфигурации.
  now-debuging: <green>Режим разработчика успешно включен. Перезагружаем QuickShop...
  no-amount-given: '<red>Количество не указано. Используйте: <green>/qs refill <количество><red>'
  no-owner-given: <red>Владелец не указан
  disabled: '<red>Данная команда недоступна: <yellow>{0}'
  bulk-size-now: <green>Сейчас обменивает с объемом <yellow>{0}x {1}
  toggle-always-counting:
    counting: <green>Магазин теперь всегда учитывает содержимое контейнера, даже когда тот неограничен
    not-counting: <green>Теперь магазин неограничен
  cleaning: <green>Удаление магазинов без каких-либо товаров...
  now-nolonger-debuging: <green>Режим разработчика успешно отключен. Перезагружаю QuickShop...
  toggle-unlimited:
    limited: <green>Магазин теперь ограничен
    unlimited: <green>Магазин теперь неограничен
  transfer-success-other: <green>Перемещено <yellow>{0} {1}<green> магазин(ов) в <yellow>{2}
  no-trade-item: <green>Пожалуйста, держите товар в руках
  wrong-args: <red>Неверный аргумент. Используйте <bold>/qs help <red>чтобы узнать список команд.
  some-shops-removed: <yellow>{0} <green>магазин(ов) удален
  new-owner: '<green>Новый владелец: <yellow>{0}'
  format: <green>/{0} {1} <yellow>- {2}
  transfer-success: <green>Перемещено <yellow>{0} <green>магазин(ов) в <yellow>{1}
  now-buying: <green>Магазин теперь <light_purple>ПОКУПАЕТ</light_purple> {0}
  now-selling: <green>Магазин теперь <aqua>ПРОДАЕТ</aqua> {0}
  cleaned: <green>Удалено <yellow>{0}</yellow> магазинов.
  trade-item-now: <green>Сейчас обменивает <yellow>{0}x {1}
  no-world-given: <red>Пожалуйста, укажите название мира
  format-disabled: <red>/{0} {1} <gray>- {2}
  invalid-bulk-amount: <red>Данное значение {0} больше чем максимальный объем стака или меньше единицы
currency-not-support: <red>Плагин экономики не поддерживает функцию мульти-экономики.
trading-in-creative-mode-is-disabled: <red>Вы не можете торговать в творческом режиме.
the-owner-cant-afford-to-buy-from-you: <red>Предмет стоит {0}, но у владельца только {1}
you-cant-afford-shop-naming: <red>У вас недостаточно денег. Дать название магазину стоит {0}.
inventory-error: |-
  <red>Ошибка при обработке InventoryWrapper. Вы используете аддон для повторной привязки инвентаря магазина?<newline> Информация: Exception={0}, InventoryWrapper={1}, WrapperType={2}, WrapperProvider={3}, SymbolLink={4}. Пожалуйста, свяжитесь с администрацией сервера.
integrations-check-failed-create: <red>Интеграция {0} запретила создание магазина
shop-out-of-space: <dark_purple>Ваш магазин на {0}, {1}, {2} переполнен!
admin-shop: Сервер
no-anythings-in-your-hand: <red>В вашей руке ничего нет.
no-permission: <red>У вас нет прав на это действие.
chest-was-removed: <red>Сундук удален.
you-cant-afford-to-buy: <red>Это стоит {0}, но у вас осталось лишь {1}
shops-removed-in-world: <yellow><aqua>{0}</aqua> магазинов было удалено в мире <aqua>{1}</aqua>.
display-turn-off: <green>Отображение товара успешно выключено.
client-language-unsupported: <yellow>QuickShop не поддерживает язык вашего клиента. Возвращаем {0}...
language-version: '63'
not-managed-shop: <red>Вы не владелец или сотрудник этого магазина
shop-cannot-trade-when-freezing: <red>Вы не можете торговать с этим магазином, потому что он заморожен.
invalid-container: <red>Недопустимый контейнер. Вы можете создать магазин только с блоком, у которого есть инвентарь.
permission:
  header: <green>Детали доступа к магазину
  header-player: <green>Детали доступа к магазину для {0}
  header-group: <green>Детали доступа к магазину для группы {0}
  table: <gold><b>{0}</b>:</gold> <gray>{1}</gray>
  item:
    purchase: <yellow>Разрешить покупку и продажу магазина.
    show-information: <yellow>Разрешить пользователям просматривать информацию о магазине. (Открыть информационную панель магазина)
    preview-shop: <yellow>Разрешить пользователям предпросмотр магазина. (предпросмотр предметов)
    search: <yellow>Разрешить пользователям поиск в данном магазине. (Без разрешения магазин будет скрыт из результатов поиска)
    delete: <yellow>Разрешить пользователям удалять магазины.
    receive-alert: <yellow>Разрешить пользователям получать оповещения (например, "нет товара в наличии" или "новая сделка").
    access-inventory: <yellow>Разрешить пользователям доступ к инвентарю магазина.
    ownership-transfer: <yellow>Разрешить передачу прав собственности на магазин.
    management-permission: <yellow>Разрешить управление группами и их редактирование.
    toggle-display: <yellow>Разрешить переключение отображение предмета над магазином.
    set-shoptype: <yellow>Разрешить менять тип магазина (переключить покупку или продажу).
    set-price: <yellow>Разрешить установку цены в магазине.
    set-item: <yellow>Разрешить смену товара в магазине.
    set-stack-amount: <yellow>Разрешить смену объема продаваемой партии в магазине.
    set-currency: <yellow>Разрешить устанавливать валюту магазина.
    set-name: <yellow>Разрешить менять название магазина.
    set-sign-type: <yellow>Измените материал вывески, которая прикреплена на магазине.
    view-purchase-logs: <yellow>Разрешение на просмотр журналов покупки магазина.
  group:
    everyone: <yellow>Группа по умолчанию для всех пользователей, исключая владельца магазина.
    staff: <yellow>Системная группа для сотрудников магазина.
    administrator: <red>Системная группа для администраторов, пользователи этой группы будут иметь права почти одинаковые с владельцем магазина.
invalid-group: <red>Неверное название группы.
invalid-permission: <red>Неверное право.
invalid-operation: <red>Недопустимая операция, разрешена только {0}.
player-no-group: <yellow>Игрок {0} не состоит в группе этого магазина.
player-in-group: <green>Игрок {0} находится в группе <aqua>{1}</aqua> в этом магазине.
permission-required: <red>У вас нет разрешения {0} в этом магазине, чтобы это сделать.
no-permission-detailed: <red>У вас нет разрешения <yellow>{0}</yellow>, чтобы сделать это.
paste-notice: "<yellow>Примечание: Если вы создаете вставку для целей устранения неполадок, не забудьте воспроизвести ошибку перед созданием вставки как можно скорее; для устранения неполадок нам нужно вкратце хранить в буфере журналы. Если вы создаете Paste слишком медленно или без предварительного воспроизведения ошибки или перезапуска сервера, то Paste ничего не будет записывать и бесполезно."
paste-uploading: <aqua>Пожалуйста, подождите... Идет загрузка на pastebin......
paste-created: '<green>Копия создана, нажмите, чтобы открыть в браузере: <yellow>{0}</yellow><newline><red>Предупреждение: <gray>Никогда не отправляйте тем, кому не доверяете.'
paste-created-local: |-
  <green>Копия сохранена на ваш диск в: {0}<newline><red>Предупреждение: <gray>Никогда не отправляйте это тем, кому вы не доверяете.
paste-created-local-failed: <red>Не удалось сохранить на ваш диск. Пожалуйста, проверьте свой диск.
paste-451: |-
  <gray><b>ПОДСКАЗКА: </b> Ваша страна или регион заблокировали сервис CloudFlare Workers и QuickShop Paste может быть загружен некорректно.<newline><gray>Если последующая операция не удается, попробуйте добавить дополнительный параметр --file для генерации локальной копии: <dark_gray>/qs paste --file</dark_gray>
paste-upload-failed: <red>Не удалось загрузить пасту в {0}, пробую другие сервисы...
paste-upload-failed-local: <red>Не удалось выгрузить пасту, пытаюсь сгенерировать ее локально...
command-incorrect: '<red>Неправильное использование команды, введите /qs help для справки. Использование: {0}.'
successfully-set-player-group: <green>Игрок {0} помещен в группу <aqua>{1}</aqua>.
successfully-unset-player-group: <green>Группа успешно удалена из этого магазина.
successfully-set-player-permission: <green>Игроку {0} разрешено <aqua>{1}</aqua> в магазине <aqua>{2}</aqua>.
lookup-item-created: <green>Предмет с названием <aqua>{0}</aqua> был создан в справочной таблице. Теперь вы можете ссылаться на этот элемент в конфигурациях.
lookup-item-exists: <red>Элемент с именем <yellow>{0}</yellow> уже существует в справочной таблице. Удалите его или выберите другое имя.
lookup-item-not-found: <red>Предмет с именем <yellow>{0}</yellow> не существует.
lookup-item-name-illegal: <red>Имя предмета неверное. Разрешены только буквенно-цифровые символы и подчеркивание.
lookup-item-name-regex: '<red>Имя должно соответствовать этому регулярному выражению: <aqua>{0}</aqua>.'
lookup-item-test-not-found: '<gold>Тест: <yellow>предмет в вашей руке не зарегистрирован в справочной таблице.'
lookup-item-test-found: '<gold>Тест: <green>предмет в вашей руке был зарегистрирован в качестве имени <aqua>{0}</aqua> в справочной таблице.'
lookup-item-removed: <green>Предмет <aqua>{0}</aqua> успешно удален из справочной таблицы.
internal-error: <red>Произошла внутренняя ошибка. Пожалуйста, свяжитесь с администрацией сервера.
argument-cannot-be: <red>Аргумент <aqua>{0}</aqua> не может быть <yellow>{1}</yellow>.
argument-must-between: <red>Значение <aqua>{0}</aqua> должно быть между <yellow>{1}</yellow> и <yellow>{2}</yellow>
invalid-percentage: <red>Неправильное значение - вы должны добавить '%' после числа процентов.
display-fallback: |-
  <red>В связи с внутренней ошибкой отображается резервное сообщение.
  Значение этого элемента может быть локализовано или обработано неправильно.
  Обратитесь к администратору сервера.
not-a-valid-time: |-
  <red>The string <yellow>{0}</yellow> not a valid timestamp, please enter a <aqua>Zulu Time (ISO 8601)</aqua> or <aqua>Unix Epoch Time in seconds</aqua>.
  <gold>Valid timestamp example: (for Sat, 17 Dec 2022 10:31:37 GMT)</gold>
  <aqua>-<aqua> <yellow>2022-12-17T10:31:37Z</yellow> <grey>(Zulu Time)</grey>
  <aqua>-<aqua> <yellow>1671273097</yellow> <grey>(Unix Epoch Time in seconds)</grey>
invalid-past-time: <red>Вы не можете указать время в прошлом.
debug:
  arguments-invalid: <red>Provided arguments <yellow>{0}</yellow> invalid.
  sign-located: '<green>Sign valid: <yellow>{0}</yellow>.'
  operation-missing: <red>You must specific an operation.
  operation-invalid: <red>You must specific an valid operation.
  invalid-base64-encoded-sql: <red>The SQL provided must be base64 encoded.
  warning-sql: |-
    <red><bold>Warning:</bold></red><yellow>You're executing a SQL statement, this may corrupt your database or destroy any data in database even it belongs to other plugins.
    <red>Don't confirm if you don't trust who send you this command.
  warning-sql-confirm: <yellow>To confirm this dangerous operation, please type <aqua>/qs debug database sql confirm {0}</aqua> in 60 seconds.
  warning-sql-confirm-hover: <yellow>Нажмите для подтверждения этой опасной операции.
  sql-confirm-not-found: <yellow>Didn't found the operation that you given, it may invalid or expired.
  sql-executing: '<yellow>Выполнение SQL запроса: <aqua>{0}</aqua>'
  sql-completed: <green>Completed, {0} rows affected.
  sql-exception: <red>An error occurred while executing the SQL query, check the Console for details!
  sql-disabled: '<red>For security reason, SQL queries are disabled in this server, If you really need this feature, you can add the following flag to startup arguments: <aqua>{0}</aqua> with `true` value to enable it.'
  force-shop-reload: <yellow>Вынуждение перезагрузить все загруженные магазины...
  force-shop-reload-complete: <green>Принудительные <aqua>{0}</aqua> магазины, которые будут перезагружены.
  force-shop-loader-reload: <yellow>Повторная загрузка загрузчика магазина...
  force-shop-loader-reload-unloading-shops: <yellow>Выгрузка <aqua>{0}</aqua> загруженные магазины...
  force-shop-loader-reload-unloading-shops-from-memory: <yellow>Удаление из памяти <aqua>{0}</aqua> магазинов...
  force-shop-loader-reload-reloading-shop-loader: <yellow>Перезвон системному загрузчику для перезагрузки всех магазинов из базы данных...
  force-shop-loader-reload-complete: <green>Shop-loader был перезагружен все магазины!
  toggle-shop-loaded-status: <aqua>Toggle this shop loaded status to <gold>{0}</gold>
  shop-internal-data: '<yellow>The internal data of this shop: <reset>{0}</reset>'
  handler-list-not-valid-bukkit-event-class: <red>Предоставляемый класс <yellow>{0}</yellow> не является допустимым классом событий Bukkit.
  update-player-shops-signs-no-username-given: <red>Вы должны ввести правильное имя пользователя.
  update-player-shops-signs-create-async-task: <yellow>Создание асинхронных задач для принудительного обновления знаков...
  update-player-shops-player-selected: '<yellow>Player selected: <gold>{0}</gold>'
  update-player-shops-player-shops: <yellow>Всего <gold>{0}</gold> магазинов, ожидающих обновлений.
  update-player-shops-per-tick-threshold: '<yellow>Max shops can be update per tick: <gold>{0}</gold>'
  update-player-shops-complete: <green>Задача выполнена, использована <yellow>{0}госпожа</yellow> для обновления.
  update-player-shops-task-started: <gold>Задачи запущены, пожалуйста, дождитесь их завершения.
  item-info-store-as-string: "<green>Магазин, на который вы смотрите: <gold>{0}</gold> Мешанина: <white>{1}</white>"
  item-info-hand-as-string: "<green>Предмет в руках: <gold>{0}</gold> Мешанина: <white>{1}</white>"
  item-matching-result: "<green>Hand2Store: <aqua>{0}</aqua>, Store2Hand: <aqua>{1}</aqua>"
  hikari-cp-size-tweak: "<green>HikariCP MaximumPoolSize и MinimumIdle установлены в <white>{0}</white>"
  hikari-cp-testing: "<green>Пожалуйста, подождите, проверяйте соединение HikariCP..."
  hikari-cp-working: "<green>Проходить! HikariCP работает хорошо!"
  hikari-cp-not-working: "<red>Failed! The connection that returned by HikariCP is dead! (Not passed test in 1 second)"
  hikari-cp-timeout: "<red>HikariCP is timed out while getting a valid connection, please clean up all active queries to release connection resources."
  queries-stopped: "<green>Stopped <white>{0}</white> active queries."
  queries-dumping: "<yellow>Dumping active queries..."
  restart-database-manager: "<yellow>Restarting SQLManager..."
  restart-database-manager-clear-executors: "<yellow>Clearing executors..."
  restart-database-manager-unfinished-task: "<yellow>Unfinished task: <white>{0}</white>"
  restart-database-manager-unfinished-task-history-query: "<yellow>Unfinished task (History Query): <white>{0}</white>"
  restart-database-manager-reconnect: "<yellow>Re-launching SQLManager via initial sequence (via async executor)"
  restart-database-manager-done: "<green>Done!"
  property-incorrect: "<yellow>You must enter a (and only one) property key=value set. E.g   aaa=bbb"
  property-security-block: "<red>Request was rejected, for security reason, you can only change the property that startsWith <aqua>com.ghostchu.quickshop</aqua> or <aqua>quickshop</aqua>."
  property-removed: "<green>Removed property key <white>{0}</white>"
  property-changed: "<green>Property key <white>{0}</white> was changed from <white>{1}</white> to <white>{2}</white>"
  marked-as-dirty: "<green>Marked all stores as dirty status, they will be force save in next auto-save task. (Restart the server to force execute store saving task)"
  display-removed: "<green>Successfully removed <yellow>{0}</yellow> QuickShop display items/entities from the worlds."
database:
  scanning: <green>Scanning the isolated data in QuickShop databases, the database load may increase in scan progress, this might need a while...
  scanning-async: <yellow>Scanning the isolated data in QuickShop databases on async task thread, the database load may increase in scan progress, this might need a while, try again later.
  already-scanning: <red>Сканирование начато. Подожди завершения.
  trim-warning: '<yellow><red><bold>Warning: </red>Backup your database before continue database trim to avoid the data loss. Once you get ready, execute <aqua>/qs database trim confirm</aqua> to continue.'
  status: '<yellow>Статус: {0}'
  status-good: <green>Хорошо
  status-bad: <yellow>Требуется обслуживание
  isolated: '<yellow>Изолированные данные:'
  isolated-data-ids: '<yellow><aqua>└</aqua> Data Records: <gold>{0}</gold>'
  isolated-shop-ids: '<yellow><aqua>└</aqua> Shop Indexes: <gold>{0}</gold>'
  isolated-logs: '<yellow><aqua>└</aqua> Logs: <gold>{0}</gold>'
  isolated-external-caches: '<yellow><aqua>└</aqua> External Caches: <gold>{0}</gold>'
  last-purge-time: <yellow>Последнее время сокращения {0}
  report-time: <yellow>Последнее время сканирования {0}
  auto-scan-alert: <yellow>The QuickShop database on this server require a maintenance. Found <gold>{0}</gold> isolated data wait for trimming.
  auto-trim: <green>Auto trim has been enabled on this server, no manually trim required.
  trim-complete: <green>Database trim completed, <yellow>{0}</yellow> isolated trimmed.
  auto-trim-started: <green>Auto trim has been started, please wait...
  trim-start: <green>Database trim started, please wait...
  trim-exception: <red>Database trim failed, an exception raised during database trimming, check the server console.
  generated-at: '<yellow>Сгенерировано в: <gold>{0}</gold>'
  purge-date: <red>Вы должны указать дату.
  purge-warning: <yellow>Эта операция сотрет всю историю, хранящуюся в базе данных, включая создание/изменение/удаление магазинов, покупки товаров, транзакции, а также системные логи. Удаление этих данных поможет освободить место на диске, но вся статистика будет стерта, и плагины, зависящие от неё, перестанут работать. Чтобы продолжить выполнение операции, введите `/qs database purgelogs <кол-во_дней> confirm`
  purge-task-created: <green>Задача создана! База данных очищает историю записей в фоновом режиме.
  purge-done-with-line: <green>Задача очистки выполнена. Из базы данных удалено <gold>{0}</gold> строк.
  purge-done-with-error: <red>Очистка не удалась, проверьте консоль сервера.
  purge-players-cache: <yellow>Please wait, purging players caches...
  purge-players-completed: |-
    <green>Successfully purged {0} players caches from both memory and database.<newline><aqua>Note: You server performance may impact by this operation.
  purge-players-error: <red>Failed to purge players caches, please check the server console.
  suggestion:
    trim: <yellow>This database require a isolated data trim, execute command <aqua>/qs database trim</aqua> to trim the database.
always-counting-removal-early-warning: <red>Always Counting feature is scheduled for removal, you shouldn't use it anymore and will stop work in future.
exporting-database: <green>Экспорт базы данных. Пожалуйста, подождите...
exporting-failed: <red>Не удалось экспортировать базу данных. Пожалуйста, проверьте консоль сервера.
exported-database: <green>База данных была экспортирована в <yellow>{0}</yellow>.
importing-not-found: <red>The <yellow>{0}</yellow> file not found, please check the file path.
importing-early-warning: |-
  <yellow><red><bold>Warning: </bold></red>The backup will be imported to the current database, any exists data will be purged and permanently lost if you don't have a backup.<newline><red>Are you sure to continue the import progress?</red><yellow>Type <aqua>/qs recovery confirm</aqua> to continue.
importing-database: <green>Импорт базы данных из резервной копии. Пожалуйста, подождите...
importing-failed: <red>Не удалось импортировать базу данных. Пожалуйста, проверьте консоль сервера.
imported-database: <green>База данных была импортирована из <yellow>{0}</yellow>.
transfer-sent: <green>Shops transfer request sent to <yellow>{0}</yellow>.
transfer-request: <yellow>Player <aqua>{0}</aqua> requested to transfer their shops to you. Did you want accept the transfer?
transfer-single-request: <yellow>Player <aqua>{0}</aqua> would like to transfer a shop to you. Do you want to accept the request?
transfer-ask: |-
  <gold>Type <red>/qs transfer accept</red> to accept.</gold><newline><gold>Type <red>/qs transfer deny</red> to deny.</gold><newline><gold>Request will expire after <red>{0}</red> seconds.
transferall-ask: |-
  <gold>Type <red>/quickshop transferall accept</red> to accept or <red>/quickshop transferall deny</red> to deny.
  The request will expire after <red>{0}</red> seconds.
transfer-single-ask: |-
  <gold>Type <red>/quickshop transferownership accept</red> to accept or <red>/quickshop transferownership deny</red> to deny.
  The request will expire after <red>{0}</red> seconds.
transfer-accepted-fromside: <green>Player <aqua>{0}</aqua> accepted your shop transfer request.
transfer-accepted-toside: <green>You accepted <aqua>{0}</aqua>'s transfer request.
transfer-rejected-fromside: <red>Player <aqua>{0}</aqua> rejected your shop transfer request.
transfer-rejected-toside: <red>You rejected <aqua>{0}</aqua>'s shop transfer request.
transfer-no-pending-operation: <red>You don't have pending transfer request yet.
transfer-no-self: <red>You can't transfer your shops to yourself.
benefit-overflow: <red>Сумма всех выплат не может быть больше или равна 100%.
benefit-exists: <red>Игроку уже выплачивается процент с магазина.
benefit-removed: <red>Этот игрок был удален из выплат магазина.
benefit-added: <green>Игрок <aqua>{0}</aqua> был добавлен в выплаты магазина!
benefit-updated: <green>Выплаты с магазина игроку <aqua>{0}</aqua> были обновлены!
benefit-query: <green>У этого магазина <yellow>{0}</yellow> игроков, получающих выплаты!
benefit-query-list: <yellow> - </yellow><white>Игрок <gold>{0}</gold>, Выплата <gold>{1}%</gold></white>
tag-added: '<green>Successfully added <aqua>#{0}</aqua> to this shop!'
tag-add-duplicate: '<red>The tag <aqua>#{0}</aqua> already exists at this shop!'
tag-removed: '<green>Successfully removed <aqua>#{0}</aqua> from this shop!'
tag-remove-not-exists: 'The tag <aqua>#{0}</aqua> not exists at this shop!'
tag-cleared: <green>Successfully cleared all tags from this shop!
tag-shops-cleared: '<green>Successfully cleared <aqua>#{0}</aqua> from all your tagged shops!'
tag-query: '<green>This shop have <yellow>{0}</yellow> tags:'
tag-query-listing: '<yellow> - <aqua>#{0}</aqua>'
tag-query-no-tag: <red>This shop have no tags.
tag-query-shops: '<green>This tag contains <yellow>{0}</yellow> shops:'
tag-query-shops-listing: <yellow> - <aqua>{0}</aqua>
batch-operations-based-on-tags-no-failure: <green>Successfully batch processed <yellow>{0}</yellow> shops.
batch-operations-based-on-tags-have-failure: <yellow>Total <yellow>{0}</yellow> shops in batch processing successfully completed, but <red>{1}</red> requests not completed.
batch-operations-based-on-tags-have-failure-with-reason: '<yellow>Total <yellow>{0}</yellow> shops in batch processing successfully completed, but <red>{1}</red> requests not completed. Reason: <gold>{2}</gold>'
batch-operations-based-on-tags-have-failure-error-messages-may-appears-above: <gray>The error messages will send to you above this message in the chat.
addon:
  towny:
    commands:
      town: <yellow>Установка или отмена привязки магазина к городу
      nation: <yellow>Установка или отмена привязки магазина к стране
    make-shop-owned-by-town: <green>Вы установили город <yellow>{0}</yellow> в качестве владельца магазина.
    make-shop-no-longer-owned-by-town: <green>Вы сбросили право собственности на магазин. Оно вернулось первоначальному владельцу магазина.
    make-shop-owned-by-nation: <green>Вы установили страну <yellow>{0}</yellow> в качестве владельца магазина.
    make-shop-no-longer-owned-by-nation: <green>Вы сбросили право собственности на магазин. Оно вернулось первоначальному владельцу магазина.
    shop-owning-changing-notice: <grey>Этот магазин теперь принадлежит городу/стране. Изначальный владелец магазина добавлен в список его администраторов. Вы можете добавить новых владельцев или персонал командой /qs permission
    target-shop-already-is-town-shop: <red>Этот магазин уже принадлежит городу.
    target-shop-already-is-nation-shop: <red>Этот магазин уже принадлежит стране.
    target-shop-not-in-town-region: <red>Этот магазин не находится в городе.
    target-shop-not-in-nation-region: <red>Этот магазин не находится в стране.
    item-not-allowed: <red>Товар из этого магазина запрещен в этом городе/этой стране, выберите другой!
    operation-disabled-due-shop-status: <red>Эта операция над магазином была отключена, потому что магазин уже является городским/национальным.
    plot-type-disallowed: <red>Вы не можете создать городской/национальный магазин на этом типе участка.
    flags:
      own: <red>Вы можете создавать магазины только на принадлежащем Вам участках города с типом магазин.
      modify: <red>У вас нет прав на строительство на этом городском участке.
      shop-type: <red>Вы можете создать магазин в городе только на участке для магазинов.
  residence:
    creation-flag-denied: <red>У вас нет прав создавать магазины в этой резиденции.
    trade-flag-denied: <red>У вас нет прав покупать товары в этой резиденции.
    you-cannot-create-shop-in-wildness: <red>Вы не можете создать магазин в дикой природе.
  griefprevention:
    creation-denied: <red>У вас нет прав на создание магазинов в этом привате.
    trade-denied: <red>У вас нет прав покупать товары в этом привате.
  lands:
    world-not-enabled: <red>Вы не можете создавать магазины или покупать товары в этом мире.
    creation-denied: <red>У вас нет прав на создание магазинов на этой территории Land.
  plotsquared:
    no-plot-whitelist-creation: <red>Вы не можете создать магазин вне участка.
    no-plot-whitelist-trade: <red>Вы не можете приобрести магазин за вне участка.
    creation-denied: <red>У вас нет прав на создание магазинов на этом участке.
    trade-denied: <red>У вас нет прав для приобретения магазинов на этом участке.
    flag:
      create: Create QuickShop-Hikari shops
      trade: Purchase QuickShop-Hikari shops
  superiorskyblock:
    owner-create-only: <red>Только владелец острова может создать магазин здесь.
    owner-member-create-only: <red>Только владелец или участник острова может создать магазин здесь.
  worldguard:
    creation-flag-test-failed: <red>У вас нет прав на создание магазинов на этой территории WorldGuard.
    trade-flag-test-failed: <red>У вас нет прав на торговлю на этой территории WorldGuard.
    reached-per-region-amount-limit: "<red>You have reached the maximum amount of shops in this region."
  discord:
    __to_translators: 'To translators: `addon.discord.discord-messages` use a special variable in strings, %%LANG_KEY%% instead of QuickShop''s style {LANG_INDEX}, do not translate anything inside double ''%'' section. BUT for any other section texts, it respect the normal QuickShop Text System syntax ({0},{1} etc.) This may be difficult to understand because two different variable forms are used in the same program at the same time, but trust me, it is reasonable. (you don''t need translate this string, it''s a comment)'
    __to_users: Как и текстовая система QuickShop, аддон Discord будет автоматически определять и использовать язык пользователя для отправки сообщений, следуя настройкам языковой системы QuickShop-Hikari.
    __to_message_designer: 'Настройте своё Discord сообщение через интерфейс: https://glitchii.github.io/embedbuilder/, затем скопируйте получившийся JSON и просто вставьте его в перевод!'
    discord-enabled: <aqua>Сообщения для Discord от QuickShop были <green>включены</green> - теперь вы будете получать уведомления о взаимодействии с вашими магазинами.
    discord-disabled: <aqua>Сообщения для Discord от QuickShop были <red>отключены</red> - вы больше не будете получать уведомления о взаимодействии с вашими магазинами.
    discord-not-integrated: <red>Вы ещё не привязали свой Discord! Пожалуйста, сначала привяжите свой аккаунт!
    feature-enabled-for-user: <aqua>You have <green>enabled</green> <gold>{0}</gold> notification.
    feature-disabled-for-user: <aqua>You have <red>disabled</red> <gold>{0}</gold> notification.
    link-help: <yellow>Этот сервер использует <gold>{0}</gold> для работы с Discord. Пожалуйста, используйте <green>{0}</green> для привязки своего аккаунта.
    save-notifaction-exception: <red>An error occurred while saving your Discord notifaction settings, please contact the server administrator.
    feature-status-changed: <green>Successfully set the notifaction <aqua>{0}</aqua> status to <gold>{1}</gold>
    commands:
      discord:
        description: <yellow>Managing your QuickShop Discord settings
    discord-messages:
      sold-to-your-shop: |
        {
         "embed":
           {
             "title": ":inbox_tray: Кто-то продал предметы в ваш магазин",
             "description": "Игрок %%purchase.name%% продал x%%purchase.amount%% %%shop.item.name%% в ваш магазин.",
             "color": 52084,
             "author": {
               "name": "QuickShop-Hikari",
               "icon_url": ""
             },
             "thumbnail": {
               "url": ""
             },
             "image": {
               "url": ""
             },
             "footer": {
               "text": "Уведомление QuickShop-Hikari Discord",
               "icon_url": ""
             },
             "fields": [
               {
                 "name": "Магазин",
                 "value": "%%shop.display-name%%",
                 "inline": false
               },
               {
                 "name": "Покупатель",
                 "value": "%%purchase.name%%",
                 "inline": true
               },
               {
                 "name": "Предмет",
                 "value": "%%shop.item.name%%",
                 "inline": true
               },
               {
                 "name": "Количество",
                 "value": "%%purchase.amount%% (x%%shop.stacking-amount%%)",
                 "inline": true
               },
               {
                 "name": "Заработок",
                 "value": "%%purchase.balance-formatted%%",
                 "inline": true
               },
               {
                 "name": "Налог",
                 "value": "%%purchase.taxes-formatted%%",
                 "inline": true
               }
             ]
           }
        }
      bought-from-your-shop: |
        {
           "embed":
             {
               "title": ":outbox_tray: Кто-то купил предметы из вашего магазина",
               "description": "Игрок %%purchase.name%% купил x%%purchase.amount%% %%shop.item.name%% из вашего магазина.",
               "color": 52084,
               "author": {
                   "name": "QuickShop-Hikari",
                   "icon_url": ""
               },
               "thumbnail": {
                   "url": ""
               },
               "image": {
                   "url": ""
               },
               "footer": {
                   "text": "Уведомление QuickShop-Hikari Discord",
                   "icon_url": ""
               },
               "fields": [
                   {
                       "name": "Магазин",
                       "value": "%%shop.display-name%%",
                       "inline": false
                   },
                   {
                       "name": "Покупатель",
                       "value": "%%purchase.name%%",
                       "inline": true
                   },
                   {
                       "name": "Предмет",
                       "value": "%%shop.item.name%%",
                       "inline": true
                   },
                   {
                       "name": "Количество",
                       "value": "%%purchase.amount%%(x%%shop.stacking-amount%%)",
                       "inline": true
                   },
                   {
                       "name": "Ваш заработок",
                       "value": "%%purchase.balance-formatted%%",
                       "inline": true
                   },
                   {
                       "name": "Налоги",
                       "value": "%%purchase.taxes-formatted%%",
                       "inline": true
                   }
               ]
           }
         }
      mod-shop-purchase: |
        {
        "embed":
          {
              "title": ":dollar: Somebody purchased in a shop",
              "description": "The player %%purchase.name%% purchase x%%purchase.amount%% %%shop.item.name%% from the shop.",
              "color": 6061450,
              "author": {
                  "name": "QuickShop-Hikari",
                  "icon_url": ""
              },
              "thumbnail": {
                  "url": ""
              },
              "image": {
                  "url": ""
              },
              "footer": {
                  "text": "QuickShop-Hikari Discord Notification",
                  "icon_url": ""
              },
              "fields": [
                  {
                      "name": "Shop",
                      "value": "%%shop.display-name%%",
                      "inline": false
                  },
                  {
                      "name": "Purchaser",
                      "value": "%%purchase.name%%",
                      "inline": true
                  },
                  {
                      "name": "Item",
                      "value": "%%shop.item.name%%",
                      "inline": true
                  },
                  {
                      "name": "Amount",
                      "value": "%%purchase.amount%% (x%%shop.stacking-amount%%)",
                      "inline": true
                  },
                  {
                      "name": "Balance",
                      "value": "%%purchase.balance-formatted%%",
                      "inline": true
                  },
                  {
                      "name": "Taxes",
                      "value": "%%purchase.taxes-formatted%%",
                      "inline": true
                  }
              ]
          }
        }
      out-of-space: |
        {
        "embed":
          {
              "title": ":mailbox_with_mail: Your shop has run out of space",
              "description": "Your shop inventory is full of items!\nYou need empty your shop container to free the space then you can continue to accept new items.",
              "color": 16065893,
              "author": {
                  "name": "QuickShop-Hikari",
                  "icon_url": ""
              },
              "thumbnail": {
                  "url": ""
              },
              "image": {
                  "url": ""
              },
              "footer": {
                  "text": "QuickShop-Hikari Discord Notification",
                  "icon_url": ""
              },
              "fields": [
                  {
                      "name": "Shop",
                      "value": "%%shop.display-name%%",
                      "inline": false
                  }
              ]
          }
        }
      out-of-stock: |
        {
            "embed":
                {
                    "title": ":mailbox_with_no_mail: Your shop has run out of stock",
                    "description": "Your shop inventory is empty now!\nYou need to refill the shop container with items to continue selling items!",
                    "color": 16065893,
                    "author": {
                        "name": "QuickShop-Hikari",
                        "icon_url": ""
                    },
                    "thumbnail": {
                        "url": ""
                    },
                    "image": {
                        "url": ""
                    },
                    "footer": {
                        "text": "QuickShop-Hikari Discord Notification",
                        "icon_url": ""
                    },
                    "fields": [
                        {
                            "name": "Shop",
                            "value": "%%shop.display-name%%",
                            "inline": false
                        }
                    ]
                }
        }
      mod-shop-created: |
        {
          "embed": {
            "title": ":classical_building: A new shop has been created",
            "description": "Player created a new shop on your server!",
            "color": 6061450,
            "author": {
              "name": "QuickShop-Hikari",
              "icon_url": ""
            },
            "thumbnail": {
              "url": ""
            },
            "image": {
              "url": ""
            },
            "footer": {
              "text": "QuickShop-Hikari Discord Notification",
              "icon_url": ""
            },
            "fields": [
              {
                "name": "Shop",
                "value": "%%shop.display-name%%",
                "inline": false
              },
              {
                "name": "Owner",
                "value": "%%shop.owner.name%%",
                "inline": true
              },
              {
                "name": "Item",
                "value": "%%shop.item.name%%",
                "inline": true
              },
              {
                "name": "Amount",
                "value": "%%shop.stacking-amount%%",
                "inline": true
              },
              {
                "name": "Type",
                "value": "%%shop.type%%",
                "inline": true
              }
            ]
          }
        }
      mod-remove-shop: |
        {
            "embed": {
                "title": ":recycle: A shop was removed from this server",
                "description": "A shop was removed from this server.",
                "color": 6061450,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "Reason",
                        "value": "%%delete.reason%%",
                        "inline": false
                    }
                ]
            }
        }
      shop-transfer-to-you: |
        {
            "embed": {
                "title": ":placard: A shop was transfer to you",
                "description": "A shop transfer to you from another player.",
                "color": 16172079,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "From",
                        "value": "%%transfer.from%%",
                        "inline": false
                    }
                ]
            }
        }
      mod-shop-transfer: |
        {
            "embed": {
                "title": ":placard: A shop was transferred",
                "description": "A shop transfer to a player from another player.",
                "color": 6061450,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "From",
                        "value": "%%transfer.from%%",
                        "inline": true
                    },
                    {
                        "name": "To",
                        "value": "%%transfer.to%%",
                        "inline": true
                    }
                ]
            }
        }
      shop-price-changed: |
        {
            "embed": {
                "title": ":money_with_wings: Your shop price was changed",
                "description": "You or your shop staff changed your shop price.",
                "color": 16172079,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "From",
                        "value": "%%change-price.from%%",
                        "inline": true
                    },
                    {
                        "name": "To",
                        "value": "%%change-price.to%%",
                        "inline": true
                    }
                ]
            }
        }
      mod-shop-price-changed: |
        {
            "embed": {
                "title": ":money_with_wings: A shop price has been changed",
                "description": "A shop changed its price settings.",
                "color": 6061450,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": true
                    },
                    {
                        "name": "Owner",
                        "value": "%%shop.owner.name%%",
                        "inline": true
                    },
                    {
                        "name": "",
                        "value": "",
                        "inline": true
                    },
                    {
                        "name": "From",
                        "value": "%%change-price.from%%",
                        "inline": true
                    },
                    {
                        "name": "To",
                        "value": "%%change-price.to%%",
                        "inline": true
                    }
                ]
            }
        }
      shop-permission-changed: |
        {
            "embed": {
                "title": ":closed_lock_with_key: Your shop permission settings has been changed",
                "description": "One of your shop's permission setting has been changed.",
                "color": 15879747,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "Player",
                        "value": "%%change-permission.player%%",
                        "inline": true
                    },
                    {
                        "name": "Assign to group",
                        "value": "%%change-permission.to-group%%",
                        "inline": true
                    },
                    {
                        "name": "",
                        "value": "",
                        "inline": true
                    },
                    {
                        "name": "Permission granted (Inherit from group)",
                        "value": "```\n%%change-permission.perms-list%%\n```",
                        "inline": false
                    }
                ]
            }
        }
    field:
      player: Игрок
      item: Предмет
      amount: Количество
      balance: Баланс
      balance-after-tax: Баланс (после налогов)
      account: Баланс вашего счета
      taxes: Налоги
      cost: Стоимость
  discount:
    commands:
      discount:
        description: <yellow>Apply a discount code or manage your discount codes.
    tab-complete:
      discount:
        general:
          code: <code>
        create:
          rate: |
            Command Hint:
            Argument: <rate>
            Description: The actual percentage or money you will earn
            Input `30%` = price * 0.3
            Input `50` = price - 50
          max-usage: |
            Command Hint:
            Argument: [max-usage]
            Description: The times that code can be used
            `-1` for unlimited.
          threshold: |
            Command Hint:
            Argument: [threshold]
            Description: The minimum price that code can be applied
            `-1` for unlimited
          expired: |
            Command Hint
            Argument: [expired]
            Description: The code expired time.
            `-1` for unlimited duration.
            Accept both Zulu time and UNIX timestamp in seconds.
            Zulu Example: 2022-12-17T10:31:37Z
            UNIX Example: 1671273097
    discount-code-already-exists: <red>Sorry, the name of your discount code already in use.
    invalid-discount-code-regex: '<red>The discount code must matches the regex: <yellow>{0}</yellow>'
    invalid-discount-code: <red>This discount code are invalid.
    discount-code-added: <green>Your discount code <yellow>{0}</yellow> has been added into shop <aqua>{1}</aqua>
    discount-code-removed: <green>Your discount code <yellow>{0}</yellow> has been removed from shop <aqua>{1}</aqua>
    invalid-code-type: <red>The code type <yellow>{0}</yellow> are invalid.
    invalid-usage-restriction: <red>The usage restriction <yellow>{0}</yellow> are invalid.
    invalid-threshold-restriction: <red>The threshold restriction <yellow>{0}</yellow> are invalid.
    invalid-effect-scope: <red>The scope <yellow>{0}</yellow> are invalid.
    invalid-expire-time: <red>Вы не можете указать время в прошлом.
    invalid-discount-rate: <red>The discount rate <yellow>{0}</yellow> are invalid, it can be a fixed number or percentage.
    discount-code-expired: <red>Ouch! Your discount code <yellow>{0}</yellow> has been expired!
    discount-code-installed: <green>You have installed discount code <gold>{0}</gold>, the discount code will be automatically applied to the available purchase in all the following purchases during this session. To uninstall discount code, execute <aqua>/qs discount uninstall {0}</aqua>.
    discount-code-uninstalled: <green>You have uninstalled your discount code.
    discount-code-query-nothing: <red>You hadn't install a discount code yet!
    discount-code-query: '<green>You are using discount code: <yellow>{0}</yellow>.'
    discount-code-applicable: '<color:#bcef26>Your discount code <yellow><bold>{0}</bold></yellow> are <bold>applicable</bold> in this shop!'
    discount-code-applicable-with-threshold: '<color:#bcef26>Your discount code <yellow><bold>{0}</bold></yellow> are <bold>applicable</bold> in this shop, but only your purchase cost over <yellow>{1}</yellow> in single transaction.'
    discount-code-not-applicable: <red>Your discount code <yellow><bold>{0}</bold></yellow> are <bold>not applicable</bold> in this shop!
    discount-code-reach-the-limit: <red>You have reach the usage limit of discount code <yellow><bold>{0}</bold></yellow>, discount are not applied.
    discount-code-no-permission: <red>You didn't have permission to use any discount code in this shop!
    discount-code-has-been-expired: <red>Your discount has been expired!
    discount-code-config-shop-added: <green>Successfully to add this shop into your discount code allow list.
    discount-code-config-shop-add-failure: <red>This shop already in your discount code allow list.
    discount-code-config-shop-removed: <green>Successfully to remove this shop from your discount code allow list.
    discount-code-config-shop-remove-failure: <red>This shop not exists in your discount code allow list.
    discount-code-config-expire: <green>Successfully changed your code expire time.
    discount-code-config-applied: <green>Config the discount code successfully!
    discount-code-created-successfully: |
      <green>Your discount code <yellow>{0}</yellow> has been successfully created!
      <gold>Scope: <aqua>{1}</aqua></gold>.
      <gold>Share with others: <color:#bcef26>{2}</color:#bcef26></gold>.
      <yellow>For <gray>Specific Shops</gray> scope only: To add shops to your discount code allow list, please looking an quickshop and execute <aqua>{3}</aqua> command.
      <yellow>You can use <aqua>/qs discount config {0}</aqua> to edit your discount code anytime.
    discount-code-under-threshold: <red>The discount not applied to your purchase because the total value under the discount code threshold <yellow>{0}</yellow>.
    percentage-off: '<bold><color:#bcef26>-{0}%</color:#bcef26></bold>'
    fixed-off: '<bold><color:#bcef26>-{0}</color:#bcef26></bold>'
    discount-code-list: '<gold>Listing your discount codes:</gold>'
    discount-code-applied-in-purchase: '<color:#bcef26>Your discount code <yellow>{0}</yellow> has been applied to this purchase and you saved <gold>{1}</gold>!'
    scope:
      this-shop: '{0}'
      your-shops-owned: all your shops (owned)
      your-shops-managed: all your shops (managed)
      server: whole server
    code-type:
      SERVER_ALL_SHOPS: All shops on this server
      PLAYER_ALL_SHOPS: All shops that created by code owner
      SPECIFIC_SHOPS: Specific shops
    discount-code-details: |-
      <gold>Code: <yellow>{0}</yellow>
      <gold>Creator: <yellow>{1}</yellow>
      <gold>Applied to: <yellow>{2}</yellow>
      <gold>Remaining usage: <yellow>{3}</yellow>
      <gold>Expired on: <yellow>{4}</yellow>
      <gold>Threshold: <yellow>{5}</yellow>
      <gold>Discount: <yellow>{6}</yellow>
  list:
    commands:
      list: <yellow>Listing all shops owned by your or specific player
    table-prefix: '<yellow><green>{0}</green>''s shops <gray>(Total {1})</gray>: '
    table-prefix-pageable: '<yellow><green>{0}</green>''s shops <gray>(Page {1}/{2})</gray>: '
    entry: "<hover:show_text:'<yellow>{1}</yellow>\n<gray>{2} {3},{4},{5}</gray>\n<aqua>Price <yellow>{6}</yellow> - <light_purple>x{7}</light_purple> <yellow>{8}</yellow>\n<green>{9}'><yellow>{0}. <aqua>{1}</aqua></hover>"
  shopitemonly:
    message: <red>Вы не можете поместить в контейнер магазина предметы, которые не являются его товаром. Эти предметы будут выложены.
  limited:
    commands:
      limit: <yellow>Set a limit that restrict the purchase that player can make in period
    titles:
      title: <green>Успешная покупка
      subtitle: <aqua>You can also purchase <gold>{0}</gold> more in this shop
    reach-the-limit: <red>You have reached the limit in this shop. You can also purchase <green>{0}</green> items, but you trying purchase <yellow>{1}</yellow> items.
    success-reset: <green>Reset the restriction of this shop successfully
    success-remove: <green>Successfully remove the all restriction of this shop
    success-setup: <green>Restriction settings for this shop were saved successfully
    trade-limit-reached-cancel-reason: <red>Reached the restriction of this shop
    remains-limits: '<gold>Your remaining purchase quota in this shop: <yellow>{0}</yellow>'
    remains-limits-before-reset-period-note: '<gold>This shop will reset their purchase quota every: <yellow>{0}</yellow>'
  dynmap:
    markerset-title: QuickShop-Hikari Shops
    marker-name: '{0} @ {2} @ {3} for x{4} @ {5}'
    marker-description: |
      Название: {0}
      Владелец: {1}
      Предмет: {2}
      Цена: {3} за x{4} предмет(а/ов)
      Тип: {5}
      Неограничен: {6}
      Локация: {7}
  bluemap:
    markerset-title: QuickShop-Hikari Shops
    marker-name: '{0} @ {2} @ {3} for x{4} @ {5}'
    marker-description: |
      Название: {0}
      Владелец: {1}
      Предмет: {2}
      Цена: {3} за x{4} предмет(а/ов)
      Тип: {5}
      Неограничен: {6}
      Локация: {7}
  chestprotect:
    protection-exists: <red>This region already protected by ChestProtect and you don't have permission to create shops at here.
    shops-exsts: <red>The region that you want to protect contains other player's shops and you don't have permission to access them.
  displaycontrol:
    toggle: |-
      <green>Successfully toggle your QuickShop display showcasing to <aqua>{0}</aqua>.
      <yellow>You may need re-join to take effect.
    toggle-exception: <red>Failed to toggle your display showcase settings due an internal error, please contact server administrator.
    command:
      displaycontrol: <yellow>Toggle your QuickShop display showcasing
  reremake-migrator:
    commands:
      migratefromreremake: Migrate QuickShop-Reremake's data to QuickShop-Hikari
    server-not-empty: "<red>No players are allowed to be online on the server during the conversion progress, please enable server whitelist or maintenance mode."
    starting-convert-progress: "<gold>Starting the convert progress, <red>DO NOT SHUTDOWN YOUR SERVER!</red>"
    executing: "<gold>Executing migrate component <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
    completed: "<green>Done! Migrate completed, please restart your server and remove QuickShop-Reremake from plugins directory."
    join_blocking_converting: "<red>[QuickShop-Hikari Migrator] This server is performing a data conversion and you cannot join the server at this time, try again later!"
    join_blocking_finished: "<red>[QuickShop-Hikari Migrator] This server has just completed a data conversion and is waiting for a reboot to apply the changes, try again later!"
    failed: "<red>Migration progress exited with error, please check the server console."
    modules:
      config:
        copy-values: "<yellow>Copying values (total {0} entries)..."
        copying-value: "<gray> - Copying <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        migrate-price-restriction: "<yellow>Migrating price-restriction related settings..."
        migrate-price-restriction-entry: "<gray> - Migrating <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
      shop:
        start-migrate: "<yellow>Migrating shops (total {0} entries)..."
        migrate-entry: "<gray> - Migrating <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        unloading-reremake: "<gold>Unloading Reremake to avoid data overwrite..."
        register-entry: "<gray> - Registering <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        save-entry: "<gray> - Saving <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        saving-shops: "<yellow>Saving <gold>{0}</gold> shops, this may need a while™ (the time based on shops amounts)..."
        conflict: "<gray> - CONFLICT > Detects a conflict between a Reremake store and an existing Hikari store location, taking a predefined behavior: <dark_gray>{0}</dark_gray>"
      translation:
        start-migrate: "<yellow>Migrating translation files...</yellow>"
        copy-values: "<yellow>Copying values in locale <gold>{0}</gold> (total {1} entries)..."
        migrate-entry: "<gray> - Migrating <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        copying-value: "<gray> - Copying <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
      shop-logs:
        start-migrate: "<yellow>Migrating shop logs (details in console), please wait...</yellow>"
        extract-history-files: "<gray>Please wait for us to unzip and append the history logs..."
        filter-history-files: "<gray>Please wait for us to filter the history log..."
        filtered-history-files: "<gray>Filtered {0} lines out of queue."
        import-entry: "<gray> - Migrating <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
compat:
  advancedchests:
    created: <green>You're successfully created a shop on AdvancedChests container!
    permission-denied: <red>Sorry! You don't have the permission to create a shop on a AdvancedChests container!
suite:
  dynmap:
    title: '{0} - {1} - {2}'
    description: |-
      Предмет: {0}
      Владелец: {1}
      Тип: {2} {3}
      Цена: {4}
      Локация: {5} {6}, {7}, {8}
      Место: {9}
      В наличии: {10}
  limited:
    command-description: <yellow>Установить лимит на количество покупок за определенный период.
    reach-the-quota-limit: <red>Вы достигли лимита покупок в этом магазине ({0}/{1}).
    quota-reset-countdown: <yellow>Квота в этом магазине сбросится через {0}.
    quota-reset-player-successfully: <green>Квота для игрока {0} в этом магазине успешно сброшена.
    quota-reset-everybody-successfully: <green>Квота для всех в этом магазине успешно сброшена.
    quota-setup: <green>Ограничение на покупку наложено на этот магазин!
    quota-remove: <green>Ограничение на покупку снято с этого магазина!
    subtitles:
      title: <green>Успешная покупка
      subtitle: <aqua>Вы можете купить <yellow>{0}</yellow> в этом магазине
  list:
    command-description: <yellow>Выводит список магазинов, принадлежащих вам или другому игроку.
    table-prefix: <yellow>Вы владеете <aqua>{0}</aqua> магазинами на этом сервере.
    entry-syntax: <aqua>- <yellow>{0}
    entry-syntax-fallback-name: '<yellow>Предмет:{0} X:{1}, Y:{2}, Z:{3}, Мир: {4}'
    hover-text: |-
      <yellow>{0}
      <gray>{1} {2}, {3}, {4}
      <aqua>{5} - <yellow>{6}
      <yellow>{7} предметов в каждом стеке.
  shopitemonly:
    message: <red>Вы не можете поместить в контейнер магазина предметы, которые не являются его товаром. Эти предметы будут выложены.
compatibility:
  elitemobs:
    soulbound-disallowed: You can't trade item which have EliteMobs Soulbound enchantment.
internet-paste-forbidden-privacy-reason: "<red>Failed! According your privacy setting, QuickShop-Hikari are not allowed upload your paste to Internet, turn on DIAGNOSTIC permission in privacy setting in config.yml or use <aqua>/quickshop paste --file</aqua> instead."
no-sign-type-given: "<red>You need give a sign material, the sign materials that available on this server: {0}"
sign-type-invalid: "<red>The type <yellow>{0}</yellow> not a valid sign material."
delete-controlpanel-button-confirm: "<red>Do you really want remove this shop? Click the <bold>[Remove Shop]</bold> button again in {0} seconds to confirm."
cannot-suggest-price: "<red>Sorry, For the time being, no more people are trading the same item as you and there is not enough data to generate a suggested price."
price-suggest: "<green>Based on data from <aqua>{0}</aqua> stores, the highest priced store is priced at <light_purple>{1}</light_purple>, the lowest priced store is priced at <light_purple>{2}</light_purple>, the average price is <light_purple>{3}</light_purple>, and the median price is <light_purple>{4}</light_purple>. <newline><yellow>It's recommended that you set your price around <gold>{5}</gold>.</yellow>"
suggest-wait: "<green>Please wait... Calculating recommend price."
history:
  shop:
    gui-title: "View purchase history"
    header-icon-multiple-shop: "<white>The query result across {0} shops</white>"
    header-icon-description:
      - "<white>Type: <yellow>{0}</yellow></white>"
      - "<white>Owner: <yellow>{1}</yellow></white>"
      - "<white>Item: <yellow>{2}</yellow></white>"
      - "<white>Price: <yellow>{3} <aqua>x{4}</aqua></yellow></white>"
      - "<white>Location: <yellow>{5}</yellow></white>"
    log-icon-title: "<reset><green>Time: {0}</green>"
    log-icon-description:
      - "<white>Purchaser: <yellow>{0}</yellow></white>"
      - "<white>Item: <yellow>{1} <aqua>x{2}</aqua></yellow></white>"
      - "<white>Balance: <yellow>{3}</yellow></white>"
      - "<white>Tax: <yellow>{4}</yellow></white>"
    log-icon-description-with-store-name:
      - "<white>Store: <yellow>{0}</yellow></white>"
      - "<white>Purchaser: <yellow>{1}</yellow></white>"
      - "<white>Item: <yellow>{2} <aqua>x{3}</aqua></yellow></white>"
      - "<white>Balance: <yellow>{4}</yellow></white>"
      - "<white>Tax: <yellow>{5}</yellow></white>"
    query-icon: "<gray>Please wait, querying...</gray>"
    previous-page: "<white><< Previous Page</white>"
    next-page: "<white>Next Page >></white>"
    current-page: "<white>Page {0}</white>"
    summary-icon-title: "<green>Shop Summary"
    recent-purchases: "<white>Recent <aqua>{0}</aqua> purchases: <yellow>{1}</yellow></white>"
    recent-purchase-balance: "<white>Recent <aqua>{0}</aqua> turnover: <yellow>{1}</yellow></white>"
    total-purchases: "<white>Total purchases: <yellow>{0}</yellow></white>"
    total-balances: "<white>Total turnover: <yellow>{0}</yellow></white>"
    total-unique-purchasers: "<white>Total unique purchasers: <yellow>{0}</yellow></white>"
    top-n-valuable-customers-title: "<gold>Top {0} valuable customers</gold>"
    top-n-valuable-customers-entry: "<white>- <yellow>{0}</yellow> <aqua>{1}</aqua></white>"
    no-result: "<white>No result</white>"
about:
  text:
    - "<b><aqua>QuickShop <yellow>{0}</yellow></aqua></b>"
    - "<aqua>Version <yellow>>></yellow> <green>{1}</green></aqua>"
    - "<aqua>Release <yellow>>></yellow> <green>{2}</green></aqua>"
    - "<aqua>Developers <yellow>>></yellow> <color:#218bff><hover:show_text:'<yellow>Click to check contributors'><click:open_url:'https://github.com/Ghost-chu/QuickShop-Hikari/graphs/contributors'>[View contributors on GitHub]</click></hover></color></aqua>"
    - "<aqua>Localized members <gold>({4})</gold>: <yellow>>></yellow> <green>{5}</green></aqua>"
    - "  <yellow>+ <color:#43A047><hover:show_text:'<yellow>Click to open Crowdin translate page'><click:open_url:'https://crowdin.com/project/quickshop-hikari'>[Help translate on Crowdin]</click></hover></color></yellow>"
    - "<aqua>Donation Key <yellow>>></yellow> <green>{6}</green></aqua>"
    - "<gold>Powered by community</gold> <red>Made with ❤</red>"
  valid-donation-key: "<color:#00AFF1>Bound to <color:#F9C23C>{0}</color></color>"
  invalid-donation-key: "<gray>Invalid donation key</gra>"
  kofi-thanks: "<gold>Special thanks to those who support QuickShop on Ko-fi :)</gold>"
history-command-leave-blank: "<Leave blank for seeing store>"
shop-information-not-shown-due-an-internal-error: "<red>An internal error occurred. The store information panel may be displayed incompletely, please contact the server administrator."
