# of this file and modify that instead. This file will be updated automatically by Residence
# when a newer version is detected, and your changes will be overwritten.  Once you 
# have a copy of this file, change the Language: option under the Residence config.yml
# to whatever you named your copy.

Language:
  Invalid:
    Player: '&cInvalid player name...'
    PlayerOffline: '&cPlayer is offline'
    World: '&cInvalid world...'
    Residence: '&cInvalid Residence...'
    Subzone: '&cInvalid Subzone...'
    Direction: '&cInvalid Direction...'
    Amount: '&cInvalid Amount...'
    Cost: '&cInvalid Cost...'
    Days: '&cInvalid number of days...'
    Material: '&cInvalid Material...'
    Boolean: '&cInvalid value, must be &6true(t) &cor &6false(f)'
    Area: '&cInvalid Area...'
    Group: '&cInvalid Group...'
    Location: '&cInvalid Location...'
    MessageType: '&cMessage type must be enter or remove.'
    Flag: '&cInvalid Flag...'
    FlagType:
      Fail: '&cInvalid Flag... This flag can only be used on %1'
      Player: Player
      Residence: Residence
    FlagState: '&cInvalid flag state, must be &6true(t)&c, &6false(f)&c, or &6remove(r)'
    List: '&eUnknown list type, must be &6blacklist &eor &6ignorelist.'
    Page: '&eInvalid Page...'
    Help: '&cInvalid Help Page...'
    NameCharacters: '&cName contained unallowed characters...'
    PortalDestination: '&cPortal destination is in restricted zone. Portal creation
      canceled. &7Find new location'
    FromConsole: '&cYou can only use this in the console!'
    Ingame: '&cYou can only use this in game!'
  Area:
    Exists: '&cArea name already exists.'
    Create: '&eResidence Area created, ID &6%1'
    DiffWorld: '&cArea is in a different world from residence.'
    Collision: '&cArea collides with residence &6%1'
    TooClose: '&cToo close to another residence. You need atleast &e%1 &cblock gap.'
    SubzoneCollision: '&cArea collides with subzone &6%1'
    NonExist: '&cNo such area exists.'
    InvalidName: '&cInvalid Area Name...'
    ToSmallX: '&cYour &6X &cselection length (&6%1&c) is too small. &eAllowed &6%2
      &eand more.'
    ToSmallY: '&cYour selection height (&6%1&c) is too small. &eAllowed &6%2 &eand
      more.'
    ToSmallZ: '&cYour &6Z &cselection length (&6%1&c) is too small. &eAllowed &6%2
      &eand more.'
    ToBigX: '&cYour &6X &cselection length (&6%1&c) is too big. &eAllowed &6%2 &eand
      less.'
    ToBigY: '&cYour selection height (&6%1&c) is too big. &eAllowed &6%2 &eand less.'
    ToBigZ: '&cYour &6Z &cselection length (&6%1&c) is too big. &eAllowed &6%2 &eand
      less.'
    Rename: '&eRenamed area &6%1 &eto &6%2'
    Remove: '&eRemoved area &6%1...'
    Name: '&eName: &2%1'
    ListAll: '&a{&eID:&c%1 &eP1:&c(%2,%3,%4) &eP2:&c(%5,%6,%7) &e(Size:&c%8&e)&a}'
    RemoveLast: '&cCannot remove the last area in a residence.'
    NotWithinParent: '&cArea is not within parent area.'
    Update: '&eArea Updated...'
    MaxPhysical: '&eYou''ve reached the max physical areas allowed for your residence.'
    SizeLimit: '&eArea size is not within your allowed limits.'
    HighLimit: '&cYou cannot protect this high up, your limit is &6%1'
    LowLimit: '&cYou cannot protect this deep, your limit is &6%1'
    WeirdShape: '&3Residence is out of regular shape. &6%1 &3side is &6%2 &3times
      bigger than &6%3 &3side'
  Select:
    Points: '&eSelect two points first before using this command!'
    Overlap: '&cSelected points overlap with &6%1 &cregion!'
    WorldGuardOverlap: '&cSelected points overlap with &6%1 &cWorldGuard region!'
    KingdomsOverlap: '&cSelected points overlap with &6%1 &cKingdoms land!'
    Success: '&eSelection Successful!'
    Fail: '&cInvalid select command...'
    Bedrock: '&eSelection expanded to your lowest allowed limit.'
    Sky: '&eSelection expanded to your highest allowed limit.'
    Area: '&eSelected area &6%1 &eof residence &6%2'
    Tool: '&e- Selection Tool: &6%1'
    PrimaryPoint: '&ePlaced &6Primary &eSelection Point %1'
    SecondaryPoint: '&ePlaced &6Secondary &eSelection Point %1'
    Primary: '&ePrimary selection: &6%1'
    Secondary: '&eSecondary selection: &6%1'
    TooHigh: '&cWarning, selection went above top of map, limiting.'
    TooLow: '&cWarning, selection went below bottom of map, limiting.'
    TotalSize: '&eSelection total size: &6%1'
    AutoEnabled: '&eAuto selection mode turned &6ON&e. To disable it write &6/res
      select auto'
    AutoDisabled: '&eAuto selection mode turned &6OFF&e. To enable it again write
      &6/res select auto'
    Disabled: '&cYou don''t have access to selections commands'
  Sign:
    Updated: '&6%1 &esigns updated!'
    TopLine: '[market]'
    DateFormat: YY/MM/dd HH:mm
    ForRentTopLine: '&0For Rent'
    ForRentPriceLine: '&0%1&f/&0%2&f/&0%3'
    ForRentResName: '&0%1'
    ForRentBottomLine: '&9Available'
    RentedAutorenewTrue: '&2%1'
    RentedAutorenewFalse: '&c%1'
    RentedTopLine: '%1'
    RentedPriceLine: '&0%1&f/&0%2&f/&0%3'
    RentedResName: '&0%1'
    RentedBottomLine: '&1%1'
    ForSaleTopLine: '&0For Sale'
    ForSalePriceLine: '&0%1'
    ForSaleResName: '&0%1'
    ForSaleBottom: '&0%1m³'
    LookAt: '&cYou are not looking at sign'
    TooMany: '&cToo many signs for this residence'
    ResName: '&0%1'
    Owner: '&0%1'
  Raid:
    NotEnabled: '&cRaid feature is not enabled!'
    NotIn: '&cYou are not in the raid!'
    CantLeave: '&cYou cant leave (%1) your own residence raid!'
    CantKick: '&cCant kick (%1) residence owner!'
    Kicked: '&eKicked &7%1 &efrom &7%2 &eresidence raid!'
    StartsIn: '&7Raid starts in: [autoTimeLeft] &2%1D &4%2A'
    EndsIn: '&cRaid ends in: [autoTimeLeft] &2%1D &4%2A'
    Ended: '&7Raid on &4%1 &7ended!'
    cantDo: '&cCan''t do this during raid!'
    left: '&7You have left &4%1 &7raid'
    noFlagChange: '&cCan''t change flags during raid'
    noRemoval: '&cCan''t remove residence during raid'
    immune: '&eImmune for next %1'
    notImmune: '&eNo longer immune'
    notInRaid: '&ePlayer isn''t in raid'
    attack:
      Joined: '&7Joined &2%1 &7raid!'
      Started: '&7Raid started!'
      cooldown: '&cToo soon for another raid on this residence! Wait %1'
      immune: '&cThis residence is immune to raids! Wait %1'
      playerImmune: '&cResidence owner is immune to raids! Wait %1'
      isOffline: '&cCan''t raid while owner is offline!'
      noSubzones: '&cCan''t raid subzones!'
      noSelf: '&cCan''t raid your own residence!'
      alreadyInAnother: '&cCan''t join this rais, you are in another one already (%1)'
    defend:
      Joined: '&7Joined &2%1 &7defence forces!'
      Sent: '&7Request to join raid defence is sent, wait for confirmation'
      Join: '&7Join &6%1 &7raid defence'
      Invitation: '&7Accept raid defend from &6%1'
      JoinedDef: '&2%1&7 joined defence forces!'
      IsOffline: '&cCan''t join defend team while owner is offline!'
      noSelf: '&cYou already defending this residence'
      notRaided: '&cResidence is not under the raid'
      alreadyInAnother: '&cCan''t join this residence defence, you are in another
        one already (%1)'
    status:
      title: '&7----------- &f%1(%2) &7-----------'
      immune: '&eImmune to raids for next: %1'
      starts: '&7Raid starts in: %1'
      attackers: '&7Attackers: &4%1'
      defenders: '&7Defenders: &4%1'
      ends: '&7Raid ends in: %1'
      canraid: '&2Can be raided'
      raidin: '&ePosible raid in: %1'
    stopped: '&eRaid on &6%1 &ewas stopped'
  info:
    years: '&e%1 &6years '
    oneYear: '&e%1 &6year '
    day: '&e%1 &6days '
    oneDay: '&e%1 &6day '
    hour: '&e%1 &6hours '
    oneHour: '&e%1 &6hour '
    min: '&e%1 &6min '
    sec: '&e%1 &6sec '
    listSplitter: ', '
    click: '&7Click'
    clickToConfirm: '&7Click to confirm'
  server:
    land: Server_Land
  Flag:
    p1Color: '&2'
    p2Color: '&a'
    haveColor: '&2'
    havePrefix: ''
    lackColor: '&7'
    lackPrefix: ''
    others: '&eand &2%1 &eothers'
    Set: '&eFlag (&6%1&e) set for &6%2 &eto &6%3 &estate'
    SetFailed: '&cYou dont have access to &6%1 &cflag'
    CheckTrue: '&eFlag &6%1 &eapplies to player &6%2 &efor residence &6%3&e, value
      = &6%4'
    CheckFalse: '&eFlag &6%1 &edoes not apply to player &6%2 &efor residence.'
    Cleared: '&eFlags Cleared.'
    RemovedAll: '&eAll flags removed for &6%1 &ein &6%2 &eresidence.'
    RemovedGroup: '&eAll flags removed for &6%1 &egroup in &6%2 &eresidence.'
    Default: '&eFlags set to default.'
    Deny: '&cYou dont have &6%1 &cpermission<s> here.'
    SetDeny: '&cOwner does not have access to flag &6%1'
    ChangeDeny: '&cYou cant change &6%1 &cflag state while there is &6%2 &cplayer(s)
      inside.'
    ChangedForOne: '&eChanged &6%1 &eflags for &6%2 &eresidences'
    ChangedFor: '&eChanged &6%1 &eflags from &6%2 &eresidences checked'
    reset: '&eReset flags for &6%1 &eresidence'
    resetAll: '&eReset flags for &6%1 &eresidences'
  Bank:
    NoAccess: '&cYou dont have bank access.'
    Name: ' &eBank: &6%1'
    NoMoney: '&cNot enough money in the bank.'
    Deposit: '&eYou deposit &6%1 &einto the residence bank.'
    Withdraw: '&eYou withdraw &6%1 &efrom the residence bank.'
    rentedWithdraw: '&cCan''t withdraw from rented residence bank.'
    full: '&eResidence bank is full!'
  Subzone:
    Rename: '&eRenamed subzone &6%1 &eto &6%2'
    Remove: '&eSubzone &6%1 &eremoved.'
    Create: '&eCreated Subzone &6%1'
    CreateFail: '&cUnable to create subzone &6%1'
    Exists: '&cSubzone &6%1 &calready exists.'
    Collide: '&cSubzone collides with subzone &6%1'
    MaxAmount: '&cYou have reached the maximum allowed subzone amount for this residence.'
    MaxDepth: '&cYou have reached the maximum allowed subzone depth.'
    SelectInside: '&eBoth selection points must be inside the residence.'
    CantCreate: '&cYou dont have permission to create residence subzone.'
    CantDelete: '&cYou dont have permission to delete residence subzone.'
    CantDeleteNotOwnerOfParent: '&cYou are not owner of parent residence to delete
      this subzone.'
    CantContract: '&cYou dont have permission to contract residence subzone.'
    CantExpand: '&cYou dont have permission to expand residence subzone.'
    DeleteConfirm: '&eAre you sure you want to delete subzone &6%1&e, use &6/res confirm
      &eto confirm.'
    OwnerChange: '&eSubzone &6%1 &eowner changed to &6%2'
  Residence:
    DontOwn: '&eNothing to show'
    Hidden: ' &e(&6Hidden&e)'
    Bought: '&eYou bought residence &6%1'
    Buy: '&6%1 &ehas bought residence &6%2 &efrom you.'
    BuyTooBig: '&cThis residence has areas bigger then your allowed max.'
    NotForSale: '&cResidence is not for sale.'
    ForSale: '&eResidence &6%1 &eis now for sale for &6%2'
    StopSelling: '&cResidence is no longer for sale.'
    TooMany: '&cYou already own the max number of residences your allowed to.'
    MaxRent: '&cYou already are renting the maximum number of residences your allowed
      to.'
    AlreadyRent: '&cResidence is already for rent...'
    NotForRent: '&cResidence not for rent...'
    NotForRentOrSell: '&cResidence not for rent or sell...'
    NotRented: '&cResidence not rented.'
    Unrent: '&eResidence &6%1 &ehas been unrented.'
    RemoveRentable: '&eResidence &6%1 &eis no longer rentable.'
    ForRentSuccess: '&eResidence &6%1 &eis now for rent for &6%2 &eevery &6%3 &edays.'
    RentSuccess: '&eYou have rented Residence &6%1 &efor &6%2 &edays.'
    EndingRent: '&eRent time is ending for &6%1 &eon &6%2'
    AlreadyRented: '&eResidence &6%1 &ehas currently been rented to &6%2'
    CantAutoPay: '&eResidence is not allowing auto pay, it will be set to &6false'
    AlreadyExists: '&cA residence named &6%1 &calready exists.'
    Create: '&eYou have created residence &6%1&e!'
    Rename: '&eRenamed Residence &6%1 &eto &6%2'
    Remove: '&eResidence &6%1 &ehas been removed...'
    CantRemove: '&cResidence &6%1 &ccant be removed as &6%2 &csubzone is still rented
      by &6%3'
    MoveDeny: '&cYou dont have movement permission for Residence &6%1'
    TeleportNoFlag: '&cYou dont have teleport access for that residence.'
    FlagDeny: '&cYou dont have &6%1 &cpermission for &6%2 &cresidence'
    BaseFlagDeny: '&cYou dont have &6%1 &cpermission'
    GiveLimits: '&cCannot give residence to target player, because it is outside the
      target players limits.'
    GiveConfirm: '&7Click to confirm &6%1 &7residence transfer from &6%2 &7to &6%3'
    Give: '&eYou give residence &6%1 &eto player &6%2'
    Recieve: '&eYou have recieved residence &6%1 &efrom player &6%2'
    ResList: ' &a%1. &e%2 &e- &6%3 %4&6%5'
    TrustedResList: ' &a%1. &f%2 &e- &6%3 %4&6%5'
    List: ' &e%2 &e- &6%3'
    Near: '&eNearby residences: &7%1'
    TeleportNear: '&eTeleported to near residence.'
    SetTeleportLocation: '&eTeleport Location Set...'
    PermissionsApply: '&ePermissions applied to residence.'
    NotOwner: '&cYou are not owner of this residence'
    RemovePlayersResidences: '&eRemoved all residences belonging to player &6%1'
    NotIn: '&cYou are not in a Residence.'
    PlayerNotIn: '&cPlayer standing not in your Residence area.'
    Kicked: '&eYou were kicked from residence'
    CantKick: '&eCan''t kick this player'
    In: '&eYou are standing in Residence &6%1'
    OwnerChange: '&eResidence &6%1 &eowner changed to &6%2'
    NonAdmin: '&cYou are not a Residence admin.'
    Line: '&eResidence: &6%1 '
    RentedBy: '&eRented by: &6%1'
    MessageChange: '&eMessage Set...'
    CantDeleteResidence: '&cYou dont have permission to delete residence.'
    CantExpandResidence: '&cYou dont have permission to expand residence.'
    CantContractResidence: '&cYou dont have permission to contract residence.'
    NoResHere: '&cThere is no residence in there.'
    OwnerNoPermission: '&cThe owner does not have permission for this.'
    ParentNoPermission: '&cYou don''t have permission to make changes to the parent
      zone.'
    ChatDisabled: '&eResidence Chat Disabled...'
    DeleteConfirm: '&eAre you sure you want to delete residence &6%1&e, use &6/res
      confirm &eto confirm.'
    ChangedMain: '&eChanged main residence to &6%1'
    LwcRemoved: '&eRemoved &6%1 &eLwc protections in &6%2ms'
    Balance: '&eBalance: &6%1'
    CanBeRented: '&6%1&e can be rented for &6%2 &eper &6%3 &edays. &6/res market rent'
    CanBeBought: '&6%1&e can be bought for &6%2&e. &6/res market buy'
    IsForRent: '&6(For rent)'
    IsForSale: '&6(For sale)'
    IsRented: '&6(Rented)'
  Rent:
    Disabled: '&cRent is disabled...'
    DisableRenew: '&eResidence &6%1 &ewill now no longer re-rent upon expire.'
    EnableRenew: '&eResidence &6%1 &ewill now automatically re-rent upon expire.'
    NotByYou: '&cResidence is rented not by you.'
    isForRent: '&2Residence available for renting.'
    MaxRentDays: '&cYou cant rent for more than &6%1 &cdays at once.'
    OneTime: '&cCan''t extend rent time for this residence.'
    Extended: '&eRent extended for aditional &6%1 &edays for &6%2 &eresidence'
    Expire: '&eRent Expire Time: &6%1'
    AutoPayTurnedOn: '&eAutoPay is turned &2ON'
    AutoPayTurnedOff: '&eAutoPay is turned &cOFF'
    ModifyDeny: '&cCannot modify a rented residence.'
    Days: '&eRent days: &6%1'
    Rented: ' &6(Rented)'
    RentList: ' &6%1&e. &6%2 &e(&6%3&e/&6%4&e/&6%5&e) - &6%6 &6%7'
    EvictConfirm: '&eWrite &6/res market confirm &eto evict renter from &6%1 &eresidence'
    UnrentConfirm: '&eWrite &6/res market confirm &eto unrent &6%1 &eresidence'
    ReleaseConfirm: '&eWrite &6/res market confirm &eto remove &6%1 &eresidence from
      market'
  command:
    addedAllow: '&eAdded new allowed command for &6%1 &eresidence'
    removedAllow: '&eRemoved allowed command for &6%1 &eresidence'
    addedBlock: '&eAdded new blocked command for &6%1 &eresidence'
    removedBlock: '&eRemoved blocked command for &6%1 &eresidence'
    Blocked: '&eBlocked commands: &6%1'
    Allowed: '&eAllowed commands: &6%1'
    Parsed: '%1'
    PlacehlderList: '&e%1. &6%2'
    PlacehlderResult: ' &eresult: &6%1'
  Rentable:
    Land: '&eRentable Land: &6'
    AllowRenewing: '&eCan Renew: &6%1'
    StayInMarket: '&eRentable stay in market: &6%1'
    AllowAutoPay: '&eRentable allows auto pay: &6%1'
    DisableRenew: '&6%1 &ewill no longer renew rentable status upon expire.'
    EnableRenew: '&6%1 &ewill now automatically renew rentable status upon expire.'
  Economy:
    LandForSale: '&eLand For Sale:'
    NotEnoughMoney: '&cYou dont have enough money.'
    MoneyCharged: '&eCharged &6%1 &eto your &6%2 &eaccount.'
    MoneyAdded: '&eGot &6%1 &eto your &6%2 &eaccount.'
    MoneyCredit: '&eCredited &6%1 &eto your &6%2 &eaccount.'
    RentReleaseInvalid: '&eResidence &6%1 &eis not rented or for rent.'
    RentSellFail: '&cCannot sell a Residence if it is for rent.'
    SubzoneRentSellFail: '&cCannot sell a Residence if its subzone set for rent.'
    ParentRentSellFail: '&cCannot sell a Residence if its parent zone is for rent.'
    SubzoneSellFail: '&cCannot sell a subzone.'
    SellRentFail: '&cCannot rent a Residence if it is for sale.'
    ParentSellRentFail: '&cCannot rent a Residence if its parent zone is for sale.'
    OwnerBuyFail: '&cCannot buy your own land!'
    OwnerRentFail: '&cCannot rent your own land!'
    AlreadySellFail: '&eResidence already for sale!'
    LeaseRenew: '&eLease valid until &6%1'
    LeaseRenewMax: '&eLease renewed to maximum allowed'
    LeaseNotExpire: '&eNo such lease, or lease does not expire.'
    LeaseRenewalCost: '&eRenewal cost for area &6%1 &eis &6%2'
    LeaseInfinite: '&eLease time set to infinite...'
    MarketDisabled: '&cEconomy Disabled!'
    SellAmount: '&eSell Amount: &2%1'
    SellList: ' &6%1&e. &6%2 &e(&6%3&e) - &6%4'
    LeaseExpire: '&eLease Expire Time: &2%1'
    LeaseList: '&6%1. &e%2 &2%3 &e%4'
  Expanding:
    North: '&eExpanding North &6%1 &eblocks'
    West: '&eExpanding West &6%1 &eblocks'
    South: '&eExpanding South &6%1 &eblocks'
    East: '&eExpanding East &6%1 &eblocks'
    Up: '&eExpanding Up &6%1 &eblocks'
    Down: '&eExpanding Down &6%1 &eblocks'
  Contracting:
    North: '&eContracting North &6%1 &eblocks'
    West: '&eContracting West &6%1 &eblocks'
    South: '&eContracting South &6%1 &eblocks'
    East: '&eContracting East &6%1 &eblocks'
    Up: '&eContracting Up &6%1 &eblocks'
    Down: '&eContracting Down &6%1 &eblocks'
  Shifting:
    North: '&eShifting North &6%1 &eblocks'
    West: '&eShifting West &6%1 &eblocks'
    South: '&eShifting South &6%1 &eblocks'
    East: '&eShifting East &6%1 &eblocks'
    Up: '&eShifting Up &6%1 &eblocks'
    Down: '&eShifting Down &6%1 &eblocks'
  Limits:
    PGroup: '&7- &ePermissions Group:&3 %1'
    RGroup: '&7- &eResidence Group:&3 %1'
    Admin: '&7- &eResidence Admin:&3 %1'
    CanCreate: '&7- &eCan Create Residences:&3 %1'
    MaxRes: '&7- &eMax Residences:&3 %1'
    MaxEW: '&7- &eMax East/West Size:&3 %1'
    MaxNS: '&7- &eMax North/South Size:&3 %1'
    MaxUD: '&7- &eMax Up/Down Size:&3 %1'
    MinMax: '&7- &eMin/Max Protection Height:&3 %1 to %2'
    MaxSubzones: '&7- &eMax Subzones:&3 %1'
    MaxSubDepth: '&7- &eMax Subzone Depth:&3 %1'
    MaxRents: '&7- &eMax Rents:&3 %1'
    MaxRentDays: ' &eMax Rent days:&3 %1'
    EnterLeave: '&7- &eCan Set Enter/Leave Messages:&3 %1'
    NumberOwn: '&7- &eNumber of Residences you own:&3 %1'
    Cost: '&7- &eResidence Cost Per Block:&3 %1'
    Sell: '&7- &eResidence Sell Cost Per Block:&3 %1'
    Flag: '&7- &eFlag Permissions:&3 %1'
    MaxDays: '&7- &eMax Lease Days:&3 %1'
    LeaseTime: '&7- &eLease Time Given on Renew:&3 %1'
    RenewCost: '&7- &eRenew Cost Per Block:&3 %1'
  Gui:
    Set:
      Title: '&8%1 flags'
    Pset:
      Title: '&8%1 &7%2 &6flags'
    Actions:
    - '&2Left click to enable'
    - '&cRight click to disable'
    - '&eShift + left click to remove'
  InformationPage:
    Top: '&e___/ &a %1 - %2 &e \___'
    TopSingle: '&e___/ &a %1 &e \___'
    Page: '&e-----< &6%1 &e>-----'
    NextPage2: '&e-----< &6%1 &e>-----'
    NoNextPage: '&e-----------------------'
    GeneralList: '&2 %1 &6- &e%2'
    FlagsList: '&2 %1 &6- &e%2'
    SmallSeparator: '&6------'
  Chat:
    ChatChannelChange: '&eChanged residence chat channel to &6%1!'
    ChatChannelLeave: '&eLeft residence chat'
    ChatMessage: '%1 %2%3: %4%5'
    ChatListeningMessage: '&2[Listening %6]%1 %2%3: %4%5'
    JoinFirst: '&4Join residence chat channel first...'
    InvalidChannel: '&4Invalid Channel...'
    InvalidColor: '&4Incorrect color code'
    NotInChannel: '&4Player is not in channel'
    Kicked: '&6%1 &ewas kicked from &6%2 &echannel'
    InvalidPrefixLength: '&4Prefix is to long. Allowed length: %1'
    ChangedColor: '&eResidence chat channel color changed to %1'
    ChangedPrefix: '&eResidence chat channel prefix changed to %1'
  Shop:
    ListTopLine: '&6%1 &eShop list - Page &6%2 &eof &6%3 %4'
    List: ' &e%1. &6%2 &e(&6%3&e) %4'
    ListVoted: '&e%1 (&6%2&e)'
    ListLiked: '&7Likes: &7%1'
    VotesTopLine: '&6%1 &e%2 residence vote list &6- &ePage &6%3 &eof &6%4 %5'
    VotesList: ' &e%1. &6%2 &e%3 &7%4'
    NoDesc: '&6No description'
    Desc: |-
      &6Description:
      %1
    DescChange: '&6Description changed to: %1'
    ChantChange: '&4Can''t change while shop flag is set to true'
    NewBoard: '&6Successfully added new shop sign board'
    BoardExist: '&cShop board already exists in this location'
    DeleteBoard: '&6Right click sign of board you want to delete'
    DeletedBoard: '&6Sign board removed'
    IncorrectBoard: '&cThis is not sign board, try performing command again and clicking
      correct sign'
    InvalidSelection: '&cLeft click with selection tool top left sign and then right
      click bottom right'
    ToBigSelection: '&cYour selection is too big, max allowed is 16 blocks'
    ToDeapSelection: '&cYour selection is too deap, max allowed is 16x16x1 blocks'
    VoteChanged: '&6Vote changed from &e%1 &6to &e%2 &6for &e%3 &6residence'
    Voted: '&6You voted, and gave &e%1 &6votes to &e%2 &6residence'
    Liked: '&6You liked &e%1 &6residence'
    AlreadyLiked: '&6You already liked &e%1 &6residence'
    NoVotes: '&cThere is no registered votes for this residence'
    CantVote: '&cResidence don''t have shop flag set to true'
    VotedRange: '&6Vote range is from &e%1 &6to &e%2'
    SignLines:
      '1': '&e--== &8%1 &e==--'
      '2': '&9%1'
      '3': '&4%1'
      '4': '&8%1&e (&8%2&e)'
      Likes4: '&9Likes: &8%2'
  RandomTeleport:
    TpLimit: '&eYou can''t teleport so fast, please wait &6%1 &esec and try again'
    TeleportSuccess: '&eTeleported to X:&6%1&e, Y:&6%2&e, Z:&6%3 &elocation'
    IncorrectLocation: '&6Could not find correct teleport location, please wait &e%1
      &6sec and try again.'
    Disabled: '&cRandom teleportation is disabled in this world'
    TeleportStarted: '&eTeleportation started, don''t move for next &6%4 &esec.'
    WorldList: '&ePossible worlds: &6%1'
  Permissions:
    variableColor: '&f'
    permissionColor: '&6'
    cmdPermissionColor: '&2'
  General:
    DisabledWorld: '&cResidence plugin is disabled in this world'
    CantCreate: '&cCan''t create residences in this world'
    UseNumbers: '&cPlease use numbers...'
    # Replace all text with '' to disable this message
    CantPlaceLava: '&cYou can''t place lava outside residence and higher than &6%1
      &cblock level'
    # Replace all text with '' to disable this message
    CantPlaceWater: '&cYou can''t place Water outside residence and higher than &6%1
      &cblock level'
    CantPlaceChest: '&cYou can''t place chest at this place'
    NoPermission: '&cYou dont have permission for this.'
    info:
      NoPlayerPermission: '&c[playerName] doesn''t have [permission] permission'
    NoCmdPermission: '&cYou dont have permission for this command.'
    DefaultUsage: '&eType &6/%1 ? &efor more info'
    MaterialGet: '&eThe material name for ID &6%1 &eis &6%2'
    MarketList: '&e---- &6Market List &e----'
    Separator: '&e----------------------------------------------------'
    AdminOnly: '&cOnly admins have access to this command.'
    InfoTool: '&e- Info Tool: &6%1'
    ListMaterialAdd: '&6%1 &eadded to the residence &6%2'
    ListMaterialRemove: '&6%1 &eremoved from the residence &6%2'
    ItemBlacklisted: '&cYou are blacklisted from using this item here.'
    WorldPVPDisabled: '&cWorld PVP is disabled.'
    NoPVPZone: '&cNo PVP zone.'
    NoFriendlyFire: '&cNo friendly fire'
    InvalidHelp: '&cInvalid help page.'
    TeleportDeny: '&cYou dont have teleport access.'
    TeleportSuccess: '&eTeleported!'
    TeleportConfirmLava: '&cThis teleport is not safe, you will fall into &6lava&c.
      Use &6/res tpconfirm &cto perform teleportation anyways.'
    TeleportConfirmVoid: '&cThis teleport is not safe, you will fall into &6void&c.
      Use &6/res tpconfirm &cto perform teleportation anyways.'
    TeleportConfirm: '&cThis teleport is not safe, you will fall for &6%1 &cblocks.
      Use &6/res tpconfirm &cto perform teleportation anyways.'
    TeleportStarted: '&eTeleportation to &6%1 &estarted, don''t move for next &6%2
      &esec.'
    TeleportTitle: '&eTeleporting!'
    TeleportTitleTime: '&6%1'
    TeleportCanceled: '&eTeleportation canceled!'
    NoTeleportConfirm: '&eThere is no teleports waiting for confirmation!'
    HelpPageHeader2: '&eHelp Pages - &6%1 &e- Page <&6%2 &eof &6%3&e>'
    ListExists: '&cList already exists...'
    ListRemoved: '&eList removed...'
    ListCreate: '&eCreated list &6%1'
    PhysicalAreas: '&ePhysical Areas'
    CurrentArea: '&eCurrent Area: &6%1'
    TotalResSize: '&eTotal size: &6%1m³ (%2m²)'
    ResSize:
      eastWest: '&eEast/West: &6%1'
      northSouth: '&eNorth/South: &6%1'
      upDown: '&eUp/Down: &6%1'
    TotalWorth: '&eTotal worth of residence: &6%1 &e(&6%2&e)'
    TotalSubzones: '&eSubzones in residence: &6%1 &e(&6%2&e)'
    NotOnline: '&eTarget player must be online.'
    GenericPages: '&ePage &6%1 &eof &6%2 &e(&6%3&e)'
    WorldEditNotFound: '&cWorldEdit was not detected.'
    CoordsTop: '&eX:&6%1 &eY:&6%2 &eZ:&6%3'
    CoordsBottom: '&eX:&6%1 &eY:&6%2 &eZ:&6%3'
    CoordsLiner: '&7 (&3%1&7;%2&7)'
    AllowedTeleportIcon: '&2T'
    BlockedTeleportIcon: '&7T'
    AllowedMovementIcon: '&2M'
    BlockedMovementIcon: '&7M'
    AdminToggleTurnOn: '&eAutomatic resadmin toggle turned &6On'
    AdminToggleTurnOff: '&eAutomatic resadmin toggle turned &6Off'
    NoSpawn: '&eYou do not have &6move &epermissions at your spawn point. Relocating'
    CompassTargetReset: '&eYour compass has been reset'
    CompassTargetSet: '&eYour compass now points to &6%1'
    Ignorelist: '&2Ignorelist:&6'
    Blacklist: '&cBlacklist:&6'
    LandCost: '&eLand cost: &6%1'
    'True': '&2True'
    'False': '&cFalse'
    Removed: '&6Removed'
    FlagState: '&eFlag state: %1'
    Land: '&eLand: &6%1'
    Cost: '&eCost: &6%1 &eper &6%2 &edays'
    Status: '&eStatus: %1'
    Available: '&2Available'
    Size: ' &eSize: &6%1'
    ResidenceFlags: '&eResidence flags: &6%1'
    PlayersFlags: '&ePlayers flags: &6%1'
    GroupFlags: '&eGroup flags: &6%1'
    OthersFlags: '&eOthers flags: &6%1'
    Moved: '&eMoved...'
    Name: '&eName: &6%1'
    Lists: '&eLists: &6'
    Residences: '&eResidences&6'
    CreatedOn: '&eCreated on: &6%1'
    Owner: '&eOwner: &6%1'
    World: '&eWorld: &6%1'
    Subzones: '&eSubzones'
    # The below lines represent various messages residence sends to the players.
    # Note that some messages have variables such as %1 that are inserted at runtime.
    NewPlayerInfo: '&eIf you want to create protected area for your house, please
      use wooden axe to select opposite sides of your home and execute command &2/res
      create YourResidenceName'
CommandHelp:
  Description: Contains Help for Residence
  SubCommands:
    res:
      Description: Main Residence Command
      Info:
      - '&2Use &6/res [command] ? <page> &2to view more help Information.'
      SubCommands:
        auto:
          Info:
          - '&eUsage: &6/res auto (residence name) (radius)'
          Description: Create maximum allowed residence around you
        select:
          Info:
          - This command selects areas for usage with residence.
          - /res select [x] [y] [z] - selects a radius of blocks, with you in the
            middle.
          Description: Selection Commands
          SubCommands:
            coords:
              Description: Display selected coordinates
              Info:
              - '&eUsage: &6/res select coords'
            size:
              Description: Display selected size
              Info:
              - '&eUsage: &6/res select size'
            auto:
              Description: Turns on auto selection tool
              Info:
              - '&eUsage: &6/res select auto [playername]'
            cost:
              Description: Display selection cost
              Info:
              - '&eUsage: &6/res select cost'
            vert:
              Description: Expand Selection Vertically
              Info:
              - '&eUsage: &6/res select vert'
              - Will expand selection as high and as low as allowed.
            sky:
              Description: Expand Selection to Sky
              Info:
              - '&eUsage: &6/res select sky'
              - Expands as high as your allowed to go.
            bedrock:
              Description: Expand Selection to Bedrock
              Info:
              - '&eUsage: &6/res select bedrock'
              - Expands as low as your allowed to go.
            expand:
              Description: Expand selection in a direction.
              Info:
              - '&eUsage: &6/res select expand <amount>'
              - Expands <amount> in the direction your looking.
            shift:
              Description: Shift selection in a direction
              Info:
              - '&eUsage: &6/res select shift <amount>'
              - Pushes your selection by <amount> in the direction your looking.
            chunk:
              Description: Select the chunk your currently in.
              Info:
              - '&eUsage: &6/res select chunk'
              - Selects the chunk your currently standing in.
            residence:
              Description: Select a existing area in a residence.
              Info:
              - '&eUsage: &6/res select residence <residence>'
              - Selects a existing area in a residence.
            worldedit:
              Description: Set selection using the current WorldEdit selection.
              Info:
              - '&eUsage: &6/res select worldedit'
              - Sets selection area using the current WorldEdit selection.
        padd:
          Info:
          - '&eUsage: &6/res padd <residence> [player]'
          - Adds essential flags for player
          Description: Add player to residence.
        placeholders:
          Info:
          - '&eUsage: &6/res placeholders (parse) (placeholder) (playerName)'
          Description: List of placeholders
          parse: '[result]'
        signconvert:
          Info:
          - '&eUsage: &6/res signconvert'
          - Will try to convert saved sign data from 3rd party plugin
          Description: Converts signs from ResidenceSign plugin
        listallhidden:
          Info:
          - '&eUsage: &6/res listhidden <page>'
          - Lists all hidden residences on the server.
          Description: List All Hidden Residences
        bank:
          Info:
          - '&eUsage: &6/res bank [deposit/withdraw/balance] <residence> <amount>'
          - You must be standing in a Residence or provide residence name
          - You must have the +bank flag.
          Description: Manage money in a Residence
        create:
          Info:
          - '&eUsage: &6/res create [residence_name]'
          Description: Create Residences
        listall:
          Info:
          - '&eUsage: &6/res listall <page> <worldName> <-a/-f>'
          - Lists all residences
          Description: List All Residences
        info:
          Info:
          - '&eUsage: &6/res info <residence>'
          - Leave off <residence> to display info for the residence your currently
            in.
          Description: Show info on a residence.
        area:
          Description: Manage physical areas for a residence.
          Info:
          - ''
          SubCommands:
            list:
              Description: List physical areas in a residence
              Info:
              - '&eUsage: &6/res area list [residence] <page>'
            listall:
              Description: List coordinates and other Info for areas
              Info:
              - '&eUsage: &6/res area listall [residence] <page>'
            add:
              Description: Add physical areas to a residence
              Info:
              - '&eUsage: &6/res area add [residence] [areaID]'
              - You must first select two points first.
            remove:
              Description: Remove physical areas from a residence
              Info:
              - '&eUsage: &6/res area remove [residence] [areaID]'
            replace:
              Description: Replace physical areas in a residence
              Info:
              - '&eUsage: &6/res area replace [residence] [areaID]'
              - You must first select two points first.
              - Replacing a area will charge the difference in size if the new area
                is bigger.
        give:
          Info:
          - '&eUsage: &6/res give <residence name> [player] <-s>'
          - Gives your owned residence to target player
          Description: Give residence to player.
        renamearea:
          Info:
          - '&eUsage: &6/res removeworld [residence] [oldAreaName] [newAreaName]'
          Description: Rename area name for residence
        contract:
          Info:
          - '&eUsage: &6/res contract (residence) [amount]'
          - Contracts residence in direction you looking.
          - Residence name is optional
          Description: Contracts residence in direction you looking
        check:
          Info:
          - '&eUsage: &6/res check [residence] [flag] (playername)'
          Description: Check flag state for you
        gset:
          Info:
          - '&eUsage: &6/res gset <residence> [group] [flag] [true/false/remove]'
          - To see a list of flags, use /res flags ?
          Description: Set flags on a specific group for a Residence.
        list:
          Info:
          - '&eUsage: &6/res list <player> <page> <worldName>'
          - Lists all the residences a player owns (except hidden ones).
          - If listing your own residences, shows hidden ones as well.
          - To list everyones residences, use /res listall.
          Description: List Residences
        version:
          Info:
          - '&eUsage: &6/res version'
          Description: how residence version
        tool:
          Info:
          - '&eUsage: &6/res tool'
          Description: Shows residence selection and info tool names
        pdel:
          Info:
          - '&eUsage: &6/res pdel <residence> [player]'
          - Removes essential flags from player
          Description: Remove player from residence.
        market:
          Info:
          - '&eUsage: &6/res market ? for more Info'
          Description: Buy, Sell, or Rent Residences
          SubCommands:
            Info:
              Description: Get economy Info on residence
              Info:
              - '&eUsage: &6/res market Info [residence]'
              - Shows if the Residence is for sale or for rent, and the cost.
            list:
              Description: Lists rentable and for sale residences.
              Info:
              - '&eUsage: &6/res market list [rent/sell]'
              SubCommands:
                rent:
                  Description: Lists rentable residences.
                  Info:
                  - '&eUsage: &6/res market list rent'
                sell:
                  Description: Lists for sale residences.
                  Info:
                  - '&eUsage: &6/res market list sell'
            sell:
              Description: Sell a residence
              Info:
              - '&eUsage: &6/res market sell [residence] [amount]'
              - Puts a residence for sale for [amount] of money.
              - Another player can buy the residence with /res market buy
            sign:
              Description: Set market sign
              Info:
              - '&eUsage: &6/res market sign [residence]'
              - Sets market sign you are looking at.
            buy:
              Description: Buy a residence
              Info:
              - '&eUsage: &6/res market buy [residence]'
              - Buys a Residence if its for sale.
            unsell:
              Description: Stops selling a residence
              Info:
              - '&eUsage: &6/res market unsell [residence]'
            rent:
              Description: ent a residence
              Info:
              - '&eUsage: &6/res market rent [residence] <AutoPay>'
              - Rents a residence.  Autorenew can be either true or false.  If true,
                the residence will be automatically re-rented upon expire if the residence
                owner has allowed it.
            rentable:
              Description: Make a residence rentable.
              Info:
              - '&eUsage: &6/res market rentable [residence] [cost] [days] <AllowRenewing>
                <StayInMarket> <AllowAutoPay>'
              - Makes a residence rentable for [cost] money for every [days] number
                of days.
              - If <AllowRenewing> is true, the residence will be able to be rented
                again before rent expires.
              - If <StayInMarket> is true, the residence will stay in market after
                last renter will be removed.
              - If <AllowAutoPay> is true, money for rent will be automaticaly taken
                from players balance if he chosen that option when renting
            autopay:
              Description: Sets residence AutoPay to given value
              Info:
              - '&eUsage: &6/res market autopay [residence] [true/false]'
            payrent:
              Description: Pays rent for defined residence
              Info:
              - '&eUsage: &6/res market payrent [residence]'
            confirm:
              Description: Confirms residence unrent/release action
              Info:
              - '&eUsage: &6/res market confirm'
            unrent:
              Description: Remove a residence from rent or rentable.
              Info:
              - '&eUsage: &6/res market unrent [residence]'
              - If you are the renter, this command releases the rent on the house
                for you.
              - If you are the owner, this command makes the residence not for rent
                anymore.
        rc:
          Info:
          - '&eUsage: &6/res rc (residence)'
          - Join residence chat channel.
          Description: Joins current or defined residence chat channel
          SubCommands:
            leave:
              Description: Leaves current residence chat channel
              Info:
              - '&eUsage: &6/res rc leave'
              - If you are in residence chat channel then you will leave it
            setcolor:
              Description: Sets residence chat channel text color
              Info:
              - '&eUsage: &6/res rc setcolor [colorCode]'
              - Sets residence chat channel text color
            setprefix:
              Description: Sets residence chat channel prefix
              Info:
              - '&eUsage: &6/res rc setprefix [newName]'
              - Sets residence chat channel prefix
            kick:
              Description: Kicks player from channel
              Info:
              - '&eUsage: &6/res rc kick [player]'
              - Kicks player from channel
        expand:
          Info:
          - '&eUsage: &6/res expand (residence) [amount]'
          - Expands residence in direction you looking.
          - Residence name is optional
          Description: Expands residence in direction you looking
        compass:
          Info:
          - '&eUsage: &6/res compass <residence>'
          Description: Set compass pointer to residence location
        lists:
          Info:
          - Predefined permissions that can be applied to a residence.
          Description: Predefined permission lists
          SubCommands:
            add:
              Description: Add a list
              Info:
              - '&eUsage: &6/res lists add <listname>'
            remove:
              Description: Remove a list
              Info:
              - '&eUsage: &6/res lists remove <listname>'
            apply:
              Description: Apply a list to a residence
              Info:
              - '&eUsage: &6/res lists apply <listname> <residence>'
            set:
              Description: Set a flag
              Info:
              - '&eUsage: &6/res lists set <listname> <flag> <value>'
            pset:
              Description: Set a player flag
              Info:
              - '&eUsage: &6/res lists pset <listname> <player> <flag> <value>'
            gset:
              Description: Set a group flag
              Info:
              - '&eUsage: &6/res lists gset <listname> <group> <flag> <value>'
            view:
              Description: View a list.
              Info:
              - '&eUsage: &6/res lists view <listname>'
        reset:
          Info:
          - '&eUsage: &6/res reset <residence/all>'
          - Resets the flags on a residence to their default.  You must be the owner
            or an admin to do this.
          Description: Reset residence to default flags.
        listhidden:
          Info:
          - '&eUsage: &6/res listhidden <player> <page>'
          - Lists hidden residences for a player.
          Description: List Hidden Residences
        raid:
          Info:
          - '&eUsage: &6/res raid start [resname] (playerName)'
          - '&6/res raid stop [resname]'
          - '&6/res raid kick [playerName]'
          - '&6/res raid immunity [add/take/set/clear] [resname/currentres] [time]'
          Description: Manage raid in residence
        setmain:
          Info:
          - '&eUsage: &6/res setmain (residence)'
          - Set defined residence as main.
          Description: Sets defined residence as main to show up in chat as prefix
        server:
          Info:
          - '&eUsage: &6/resadmin server [residence]'
          - Make a residence server owned.
          Description: Make land server owned.
        mirror:
          Info:
          - '&eUsage: &6/res mirror [Source Residence] [Target Residence]'
          - Mirrors flags from one residence to another.  You must be owner of both
            or a admin to do this.
          Description: Mirrors Flags
        rt:
          Info:
          - '&eUsage: &6/res rt (worldname) (playerName)'
          - Teleports you to random location in defined world.
          Description: Teleports to random location in world
        shop:
          Info:
          - Manages residence shop feature
          Description: Manage residence shop
          SubCommands:
            list:
              Description: Shows list of res shops
              Info:
              - '&eUsage: &6/res shop list'
              - Shows full list of all residences with shop flag
            vote:
              Description: Vote for residence shop
              Info:
              - '&eUsage: &6/res shop vote <residence> [amount]'
              - Votes for current or defined residence
            like:
              Description: Give like for residence shop
              Info:
              - '&eUsage: &6/res shop like <residence>'
              - Gives like for residence shop
            votes:
              Description: Shows res shop votes
              Info:
              - '&eUsage: &6/res shop votes <residence> <page>'
              - Shows full vote list of current or defined residence shop
            likes:
              Description: Shows res shop likes
              Info:
              - '&eUsage: &6/res shop likes <residence> <page>'
              - Shows full like list of current or defined residence shop
            setdesc:
              Description: Sets residence shop description
              Info:
              - '&eUsage: &6/res shop setdesc [text]'
              - Sets residence shop description. Color code supported. For new line
                use /n
            createboard:
              Description: Create res shop board
              Info:
              - '&eUsage: &6/res shop createboard [place]'
              - Creates res shop board from selected area. Place - position from which
                to start filling board
            deleteboard:
              Description: Deletes res shop board
              Info:
              - '&eUsage: &6/res shop deleteboard'
              - Deletes res shop board bi right clicking on one of signs
        lset:
          Info:
          - '&eUsage: &6/res lset <residence> [blacklist/ignorelist] [material]'
          - '&eUsage: &6/res lset <residence> Info'
          - Blacklisting a material prevents it from being placed in the residence.
          - Ignorelist causes a specific material to not be protected by Residence.
          Description: Change blacklist and ignorelist options
        pset:
          Info:
          - '&eUsage: &6/res pset <residence> [player] [flag] [true/false/remove]'
          - '&eUsage: &6/res pset <residence> [player] removeall'
          - To see a list of flags, use /res flags ?
          Description: Set flags on a specific player for a Residence.
        raidstatus:
          Info:
          - '&eUsage: &6/res raidstatus (resName/playerName)'
          Description: Check raid status for a residence
        flags:
          Info:
          - For flag values, usually true allows the action, and false denys the action.
          Description: List of flags
          SubCommands:
            anvil:
              Translated: anvil
              Description: Allows or denys interaction with anvil
              Info:
              - '&eUsage: &6/res set/pset <residence> anvil true/false/remove'
            admin:
              Translated: admin
              Description: Gives a player permission to change flags on a residence
              Info:
              - '&eUsage: &6/res pset <residence> admin true/false/remove'
            animalkilling:
              Translated: animalkilling
              Description: Allows or denys animal killing
              Info:
              - '&eUsage: &6/res set/pset <residence> animalkilling true/false/remove'
            animals:
              Translated: animals
              Description: Allows or denys animal spawns
              Info:
              - '&eUsage: &6/res set <residence> animals true/false/remove'
            anchor:
              Translated: anchor
              Description: Allows or denys respawn anchor usage
              Info:
              - '&eUsage: &6/res set/pset <residence> anchor true/false/remove'
            anvilbreak:
              Translated: anvilbreak
              Description: Allows or denys anvil break in residence
              Info:
              - '&eUsage: &6/res set <residence> anvilbreak true/false/remove'
            safezone:
              Translated: safezone
              Description: Setting to true makes the residence clean bad effects from
                its occupants
              Info:
              - '&eUsage: &6/res set <residence> safezone true/false/remove'
            backup:
              Translated: backup
              Description: If set to true, restores previous look of area (WordEdit
                required)
              Info:
              - '&eUsage: &6/res set <residence> backup true/false/remove'
            bank:
              Translated: bank
              Description: Allows or denys deposit/withdraw money from res bank
              Info:
              - '&eUsage: &6/res set/pset <residence> bank true/false/remove'
            bed:
              Translated: bed
              Description: Allows or denys players to use beds
              Info:
              - '&eUsage: &6/res set/pset <residence> bed true/false/remove'
            honey:
              Translated: honey
              Description: Allows or denys players to get honey
              Info:
              - '&eUsage: &6/res set/pset <residence> honey true/false/remove'
            honeycomb:
              Translated: honeycomb
              Description: Allows or denys players to get honeycomb
              Info:
              - '&eUsage: &6/res set/pset <residence> honeycomb true/false/remove'
            beacon:
              Translated: beacon
              Description: Allows or denys interaction with beacon
              Info:
              - '&eUsage: &6/res set/pset <residence> beacon true/false/remove'
            brew:
              Translated: brew
              Description: Allows or denys players to use brewing stands
              Info:
              - '&eUsage: &6/res set/pset <residence> brew true/false/remove'
            build:
              Translated: build
              Description: Allows or denys building
              Info:
              - '&eUsage: &6/res set/pset <residence> build true/false/remove'
            burn:
              Translated: burn
              Description: Allows or denys Mob combustion in residences
              Info:
              - '&eUsage: &6/res set <residence> burn true/false/remove'
            button:
              Translated: button
              Description: Allows or denys players to use buttons
              Info:
              - '&eUsage: &6/res set/pset <residence> button true/false/remove'
            brush:
              Translated: brush
              Description: Allows or denys block brushing
              Info:
              - '&eUsage: &6/res set/pset <residence> brush true/false/remove'
            cake:
              Translated: cake
              Description: Allows or denys players to eat cake
              Info:
              - '&eUsage: &6/res set/pset <residence> cake true/false/remove'
            canimals:
              Translated: canimals
              Description: Allows or denys custom animal spawns
              Info:
              - '&eUsage: &6/res set <residence> canimals true/false/remove'
            chorustp:
              Translated: chorustp
              Description: Allow or disallow teleporting to the residence with chorus
                fruit
              Info:
              - '&eUsage: &6/res set/pset <residence> chorustp true/false/remove'
            chat:
              Translated: chat
              Description: Allows to join residence chat room
              Info:
              - '&eUsage: &6/res set/pset <residence> chat true/false/remove'
            cmonsters:
              Translated: cmonsters
              Description: Allows or denys custom monster spawns
              Info:
              - '&eUsage: &6/res set <residence> cmonsters true/false/remove'
            commandblock:
              Translated: commandblock
              Description: Allows or denys command block interaction
              Info:
              - '&eUsage: &6/res set/pset <residence> commandblock true/false/remove'
            command:
              Translated: command
              Description: Allows or denys comamnd use in residences
              Info:
              - '&eUsage: &6/res set/pset <residence> command true/false/remove'
            container:
              Translated: container
              Description: Allows or denys use of furnaces, chests, dispensers, etc...
              Info:
              - '&eUsage: &6/res set/pset <residence> container true/false/remove'
            coords:
              Translated: coords
              Description: Hides residence coordinates
              Info:
              - '&eUsage: &6/res set <residence> coords true/false/remove'
            copper:
              Translated: copper
              Description: Allows to modify copper blocks
              Info:
              - '&eUsage: &6/res set/pset <residence> copper true/false/remove'
            craft:
              Translated: craft
              Description: Gives table, enchant, brew flags
              Info:
              - '&eUsage: &6/res set <residence> craft true/false/remove'
            creeper:
              Translated: creeper
              Description: Allow or deny creeper explosions
              Info:
              - '&eUsage: &6/res set <residence> creeper true/false/remove'
            dragongrief:
              Translated: dragongrief
              Description: Prevents ender dragon block griefing
              Info:
              - '&eUsage: &6/res set <residence> dragongrief true/false/remove'
            day:
              Translated: day
              Description: Sets day time in residence
              Info:
              - '&eUsage: &6/res set <residence> day true/false/remove'
            dye:
              Translated: dye
              Description: Allows or denys sheep dyeing
              Info:
              - '&eUsage: &6/res set/pset <residence> dye true/false/remove'
            damage:
              Translated: damage
              Description: Allows or denys all entity damage within the residence
              Info:
              - '&eUsage: &6/res set <residence> damage true/false/remove'
            decay:
              Translated: decay
              Description: Allows or denys leave decay in the residence
              Info:
              - '&eUsage: &6/res set <residence> decay true/false/remove'
            destroy:
              Translated: destroy
              Description: Allows or denys only destruction of blocks, overrides the
                build flag
              Info:
              - '&eUsage: &6/res set/pset <residence> destroy true/false/remove'
            dryup:
              Translated: dryup
              Description: Prevents land from drying up
              Info:
              - '&eUsage: &6/res set <residence> dryup true/false/remove'
            diode:
              Translated: diode
              Description: Allows or denys players to use redstone repeaters
              Info:
              - '&eUsage: &6/res set/pset <residence> diode true/false/remove'
            door:
              Translated: door
              Description: Allows or denys players to use doors and trapdoors
              Info:
              - '&eUsage: &6/res set/pset <residence> door true/false/remove'
            egg:
              Translated: egg
              Description: Allows or denys interaction with dragon egg
              Info:
              - '&eUsage: &6/res set/pset <residence> egg true/false/remove'
            enchant:
              Translated: enchant
              Description: Allows or denys players to use enchanting tables
              Info:
              - '&eUsage: &6/res set/pset <residence> enchant true/false/remove'
            explode:
              Translated: explode
              Description: Allows or denys explosions in residences
              Info:
              - '&eUsage: &6/res set <residence> explode true/false/remove'
            elytra:
              Translated: elytra
              Description: Allows or denys elytra usage in residences
              Info:
              - '&eUsage: &6/res set/pset <residence> elytra true/false/remove'
            enderpearl:
              Translated: enderpearl
              Description: Allow or disallow teleporting to the residence with enderpearl
              Info:
              - '&eUsage: &6/res set/pset <residence> enderpearl true/false/remove'
            fallinprotection:
              Translated: fallinprotection
              Description: Protects from blocks falling into residence
              Info:
              - '&eUsage: &6/res set <residence> fallinprotection true/false/remove'
            falldamage:
              Translated: falldamage
              Description: Protects players from fall damage
              Info:
              - '&eUsage: &6/res set <residence> falldamage true/false/remove'
            feed:
              Translated: feed
              Description: Setting to true makes the residence feed its occupants
              Info:
              - '&eUsage: &6/res set <residence> feed true/false/remove'
            friendlyfire:
              Translated: friendlyfire
              Description: Allow or disallow friendly fire
              Info:
              - '&eUsage: &6/res pset <residence> friendlyfire true/false/remove'
            fireball:
              Translated: fireball
              Description: Allows or denys fire balls in residences
              Info:
              - '&eUsage: &6/res set <residence> fireball true/false/remove'
            firespread:
              Translated: firespread
              Description: Allows or denys fire spread
              Info:
              - '&eUsage: &6/res set <residence> firespread true/false/remove'
            flowinprotection:
              Translated: flowinprotection
              Description: Allows or denys liquid flow into residence
              Info:
              - '&eUsage: &6/res set <residence> flowinprotection true/false/remove'
            flow:
              Translated: flow
              Description: Allows or denys liquid flow
              Info:
              - '&eUsage: &6/res set <residence> flow true/false/remove'
            flowerpot:
              Translated: flowerpot
              Description: Allows or denys interaction with flower pot
              Info:
              - '&eUsage: &6/res set/pset <residence> flowerpot true/false/remove'
            grow:
              Translated: grow
              Description: Allows or denys plant growing
              Info:
              - '&eUsage: &6/res set <residence> grow true/false/remove'
            glow:
              Translated: glow
              Description: Players will start glowing when entering residence
              Info:
              - '&eUsage: &6/res set <residence> glow true/false/remove'
            goathorn:
              Translated: goathorn
              Description: Allows or denys goat horn usage
              Info:
              - '&eUsage: &6/res set/pset <residence> goathorn true/false/remove'
            harvest:
              Translated: harvest
              Description: Allows harvesting
              Info:
              - '&eUsage: &6/res set/pset <residence> harvest true/false/remove'
            hotfloor:
              Translated: hotfloor
              Description: Prevent damage from magma blocks
              Info:
              - '&eUsage: &6/res set <residence> hotfloor true/false/remove'
            hidden:
              Translated: hidden
              Description: Hides residence from list or listall commands
              Info:
              - '&eUsage: &6/res set <residence> hidden true/false/remove'
            hook:
              Translated: hook
              Description: Allows or denys fishing rod hooking entities
              Info:
              - '&eUsage: &6/res set/pset <residence> hook true/false/remove'
            healing:
              Translated: healing
              Description: Setting to true makes the residence heal its occupants
              Info:
              - '&eUsage: &6/res set <residence> healing true/false/remove'
            iceform:
              Translated: iceform
              Description: Prevents from ice forming
              Info:
              - '&eUsage: &6/res set <residence> iceform true/false/remove'
            icemelt:
              Translated: icemelt
              Description: Prevents ice from melting
              Info:
              - '&eUsage: &6/res set <residence> icemelt true/false/remove'
            ignite:
              Translated: ignite
              Description: Allows or denys fire ignition
              Info:
              - '&eUsage: &6/res set/pset <residence> ignite true/false/remove'
            itemdrop:
              Translated: itemdrop
              Description: Allows or denys item drop
              Info:
              - '&eUsage: &6/res set/pset <residence> itemdrop true/false/remove'
            itempickup:
              Translated: itempickup
              Description: Allows or denys item pickup
              Info:
              - '&eUsage: &6/res set/pset <residence> itempickup true/false/remove'
            jump2:
              Translated: jump2
              Description: Allows to jump 2 blocks high
              Info:
              - '&eUsage: &6/res set <residence> jump2 true/false/remove'
            jump3:
              Translated: jump3
              Description: Allows to jump 3 blocks high
              Info:
              - '&eUsage: &6/res set <residence> jump3 true/false/remove'
            keepinv:
              Translated: keepinv
              Description: Players keeps inventory after death
              Info:
              - '&eUsage: &6/res set <residence> keepinv true/false/remove'
            keepexp:
              Translated: keepexp
              Description: Players keeps exp after death
              Info:
              - '&eUsage: &6/res set <residence> keepexp true/false/remove'
            lavaflow:
              Translated: lavaflow
              Description: Allows or denys lava flow, overrides flow
              Info:
              - '&eUsage: &6/res set <residence> lavaflow true/false/remove'
            leash:
              Translated: leash
              Description: Allows or denys aninal leash
              Info:
              - '&eUsage: &6/res set/pset <residence> leash true/false/remove'
            lever:
              Translated: lever
              Description: Allows or denys players to use levers
              Info:
              - '&eUsage: &6/res set/pset <residence> lever true/false/remove'
            mobexpdrop:
              Translated: mobexpdrop
              Description: Prevents mob droping exp on death
              Info:
              - '&eUsage: &6/res set <residence> mobexpdrop true/false/remove'
            mobitemdrop:
              Translated: mobitemdrop
              Description: Prevents mob droping items on death
              Info:
              - '&eUsage: &6/res set <residence> mobitemdrop true/false/remove'
            mobkilling:
              Translated: mobkilling
              Description: Allows or denys mob killing
              Info:
              - '&eUsage: &6/res set/pset <residence> mobkilling true/false/remove'
            monsters:
              Translated: monsters
              Description: Allows or denys monster spawns
              Info:
              - '&eUsage: &6/res set <residence> monsters true/false/remove'
            move:
              Translated: move
              Description: Allows or denys movement in the residence
              Info:
              - '&eUsage: &6/res set/pset <residence> move true/false/remove'
            nametag:
              Translated: nametag
              Description: Allows or denys name tag usage
              Info:
              - '&eUsage: &6/res set/pset <residence> nametag true/false/remove'
            nanimals:
              Translated: nanimals
              Description: Allows or denys natural animal spawns
              Info:
              - '&eUsage: &6/res set <residence> nanimals true/false/remove'
            nmonsters:
              Translated: nmonsters
              Description: Allows or denys natural monster spawns
              Info:
              - '&eUsage: &6/res set <residence> nmonsters true/false/remove'
            night:
              Translated: night
              Description: Sets night time in residence
              Info:
              - '&eUsage: &6/res set <residence> night true/false/remove'
            nofly:
              Translated: nofly
              Description: Allows or denys fly in residence
              Info:
              - '&eUsage: &6/res set/pset <residence> nofly true/false/remove'
            fly:
              Translated: fly
              Description: Toggles fly for players in residence
              Info:
              - '&eUsage: &6/res set/pset <residence> fly true/false/remove'
            nomobs:
              Translated: nomobs
              Description: Prevents monsters from entering residence. Requires AutoMobRemoval
                to be enabled
              Info:
              - '&eUsage: &6/res set <residence> nomobs true/false/remove'
            note:
              Translated: note
              Description: Allows or denys players to use note blocks
              Info:
              - '&eUsage: &6/res set/pset <residence> note true/false/remove'
            nodurability:
              Translated: nodurability
              Description: Prevents item durability loss
              Info:
              - '&eUsage: &6/res set <residence> nodurability true/false/remove'
            overridepvp:
              Translated: overridepvp
              Description: Overrides any plugin pvp protection
              Info:
              - '&eUsage: &6/res set <residence> overridepvp true/false/remove'
            pressure:
              Translated: pressure
              Description: Allows or denys players to use pressure plates
              Info:
              - '&eUsage: &6/res set/pset <residence> pressure true/false/remove'
            piston:
              Translated: piston
              Description: Allow or deny pistons from pushing or pulling blocks in
                the residence
              Info:
              - '&eUsage: &6/res set <residence> piston true/false/remove'
            pistonprotection:
              Translated: pistonprotection
              Description: Enables or disabled piston block move in or out of residence
              Info:
              - '&eUsage: &6/res set <residence> pistonprotection true/false/remove'
            place:
              Translated: place
              Description: Allows or denys only placement of blocks, overrides the
                build flag
              Info:
              - '&eUsage: &6/res set/pset <residence> place true/false/remove'
            pvp:
              Translated: pvp
              Description: Allow or deny pvp in the residence
              Info:
              - '&eUsage: &6/res set <residence> pvp true/false/remove'
            rain:
              Translated: rain
              Description: Sets weather to rainny in residence
              Info:
              - '&eUsage: &6/res set <residence> rain true/false/remove'
            respawn:
              Translated: respawn
              Description: Automaticaly respawns player
              Info:
              - '&eUsage: &6/res set <residence> respawn true/false/remove'
            riding:
              Translated: riding
              Description: Prevent riding a horse
              Info:
              - '&eUsage: &6/res set/pset <residence> riding true/false/remove'
            shoot:
              Translated: shoot
              Description: Allows or denys shooting projectile in area
              Info:
              - '&eUsage: &6/res set <residence> shoot true/false/remove'
            sun:
              Translated: sun
              Description: Sets weather to sunny in residence
              Info:
              - '&eUsage: &6/res set <residence> sun true/false/remove'
            shop:
              Translated: shop
              Description: Adds residence to special residence shop list
              Info:
              - '&eUsage: &6/res set <residence> shop true/false/remove'
            snowtrail:
              Translated: snowtrail
              Description: Prevents snowman snow trails
              Info:
              - '&eUsage: &6/res set <residence> snowtrail true/false/remove'
            spread:
              Translated: spread
              Description: Prevents block spreading
              Info:
              - '&eUsage: &6/res set <residence> spread true/false/remove'
            snowball:
              Translated: snowball
              Description: Prevents snowball knockback
              Info:
              - '&eUsage: &6/res set <residence> snowball true/false/remove'
            sanimals:
              Translated: sanimals
              Description: Allows or denys spawner or spawn egg animal spawns
              Info:
              - '&eUsage: &6/res set <residence> sanimals true/false/remove'
            shear:
              Translated: shear
              Description: Allows or denys sheep shear
              Info:
              - '&eUsage: &6/res set/pset <residence> shear true/false/remove'
            smonsters:
              Translated: smonsters
              Description: Allows or denys spawner or spawn egg monster spawns
              Info:
              - '&eUsage: &6/res set <residence> smonsters true/false/remove'
            subzone:
              Translated: subzone
              Description: Allow a player to make subzones in the residence
              Info:
              - '&eUsage: &6/res set/pset <residence> subzone true/false/remove'
            title:
              Translated: title
              Description: Shows or hides enter/leave message in residence
              Info:
              - '&eUsage: &6/res set <residence> title true/false/remove'
            table:
              Translated: table
              Description: Allows or denys players to use workbenches
              Info:
              - '&eUsage: &6/res set/pset <residence> table true/false/remove'
            tnt:
              Translated: tnt
              Description: Allow or deny tnt explosions
              Info:
              - '&eUsage: &6/res set <residence> tnt true/false/remove'
            tp:
              Translated: tp
              Description: Allow or disallow teleporting to the residence
              Info:
              - '&eUsage: &6/res set/pset <residence> tp true/false/remove'
            trade:
              Translated: trade
              Description: Allows or denys villager trading in residence
              Info:
              - '&eUsage: &6/res set/pset <residence> trade true/false/remove'
            trample:
              Translated: trample
              Description: Allows or denys crop trampling in residence
              Info:
              - '&eUsage: &6/res set <residence> trample true/false/remove'
            use:
              Translated: use
              Description: Allows or denys use of doors, lever, buttons, etc...
              Info:
              - '&eUsage: &6/res set/pset <residence> use true/false/remove'
            vehicledestroy:
              Translated: vehicledestroy
              Description: Allows or denys vehicle destroy
              Info:
              - '&eUsage: &6/res set/pset <residence> vehicledestroy true/false/remove'
            witherspawn:
              Translated: witherspawn
              Description: Allows or denys wither spawning
              Info:
              - '&eUsage: &6/res set <residence> witherspawn true/false/remove'
            phantomspawn:
              Translated: phantomspawn
              Description: Allows or denys phantom spawning
              Info:
              - '&eUsage: &6/res set <residence> phantomspawn true/false/remove'
            witherdamage:
              Translated: witherdamage
              Description: Allows or denys wither damage
              Info:
              - '&eUsage: &6/res set <residence> witherdamage true/false/remove'
            witherdestruction:
              Translated: witherdestruction
              Description: Allows or denys wither block damage
              Info:
              - '&eUsage: &6/res set <residence> witherdestruction true/false/remove'
            waterflow:
              Translated: waterflow
              Description: Allows or denys water flow, overrides flow
              Info:
              - '&eUsage: &6/res set <residence> waterflow true/false/remove'
            wspeed1:
              Translated: wspeed1
              Description: Change players walk speed in residence to %1
              Info:
              - '&eUsage: &6/res set <residence> wspeed1 true/false/remove'
            wspeed2:
              Translated: wspeed2
              Description: Change players walk speed in residence to %1
              Info:
              - '&eUsage: &6/res set <residence> wspeed2 true/false/remove'
        show:
          Info:
          - '&eUsage: &6/res show <residence>'
          Description: Show residence boundaries
        remove:
          Info:
          - '&eUsage: &6/res remove [residence_name]'
          Description: Remove residences.
        signupdate:
          Info:
          - '&eUsage: &6/res signupdate'
          Description: Updated residence signs
        current:
          Info:
          - '&eUsage: &6/res current'
          Description: Show residence your currently in.
        leaveraid:
          Info:
          - '&eUsage: &6/res leaveraid'
          Description: Leave raid
        reload:
          Info:
          - '&eUsage: &6/res reload [config/lang/groups/flags]'
          Description: reload lanf or config files
        setowner:
          Info:
          - '&eUsage: &6/resadmin setowner [residence] [player] (-keepflags)'
          Description: Change owner of a residence.
        defend:
          Info:
          - '&eUsage: &6/res defend [resName] (playerName)'
          Description: Join raid defence on residence
        attack:
          Description: Start raid on residence
          Info:
          - '&eUsage: &6/res attack [resName]'
        unstuck:
          Info:
          - '&eUsage: &6/res unstuck'
          Description: Teleports outside of residence
        subzone:
          Info:
          - '&eUsage: &6/res subzone <residence> [subzone name]'
          - If residence name is left off, will attempt to use residence your standing
            in.
          Description: Create subzones in residences.
        limits:
          Info:
          - '&eUsage: &6/res limits (playerName)'
          - Shows the limitations you have on creating and managing residences.
          Description: Show your limits.
        removeworld:
          Info:
          - '&eUsage: &6/res removeworld [worldname]'
          - Can only be used from console
          Description: Remove all residences from world
        set:
          Info:
          - '&eUsage: &6/res set <residence> [flag] [true/false/remove]'
          - To see a list of flags, use /res flags ?
          - These flags apply to any players who do not have the flag applied specifically
            to them. (see /res pset ?)
          Description: Set general flags on a Residence
        clearflags:
          Info:
          - '&eUsage: &6/res clearflags <residence>'
          Description: Remove all flags from residence
        message:
          Info:
          - '&eUsage: &6/res message <residence> [enter/leave] [message]'
          - Set the enter or leave message of a residence.
          - '&eUsage: &6/res message <residence> remove [enter/leave]'
          - Removes a enter or leave message.
          Description: Manage residence enter / leave messages
        command:
          Info:
          - '&eUsage: &6/res command <residence> <allow/block/list> <command>'
          - Shows list, adds or removes allowed or disabled commands in residence
          - Use _ to include command with multiple variables
          Description: Manages allowed or blocked commands in residence
        confirm:
          Description: Confirms removal of a residence.
          Info:
          - '&eUsage: &6/res confirm'
          - Confirms removal of a residence.
        resadmin:
          Info:
          - '&eUsage: &6/res resadmin [on/off]'
          Description: Enabled or disable residence admin
        tpset:
          Info:
          - '&eUsage: &6/res tpset'
          - This will set the teleport location for a residence to where your standing.
          - You must be standing in the residence to use this command.
          - You must also be the owner or have the +admin flag for the residence.
          Description: Set the teleport location of a Residence
        removeall:
          Info:
          - '&eUsage: &6/res removeall [owner]'
          - Removes all residences owned by a specific player.'
          - Requires /resadmin if you use it on anyone besides yourself.
          Description: Remove all residences owned by a player.
        tpconfirm:
          Info:
          - '&eUsage: &6/res tpconfirm'
          - Teleports you to a residence, when teleportation is unsafe.
          Description: Ignore unsafe teleportation warning
        kick:
          Info:
          - '&eUsage: &6/res kick <player>'
          - You must be the owner or an admin to do this.
          - Player should be online.
          Description: Kicks player from residence.
        material:
          Info:
          - '&eUsage: &6/res material [material]'
          Description: Check if material exists by its id
        rename:
          Info:
          - '&eUsage: &6/res rename [OldName] [NewName]'
          - You must be the owner or an admin to do this.
          - The name must not already be taken by another residence.
          Description: Renames a residence.
        sublist:
          Info:
          - '&eUsage: &6/res sublist <residence> <page>'
          - List subzones within a residence.
          Description: List Residence Subzones
        setallfor:
          Info:
          - '&eUsage: &6/res setallfor [playerName] [flag] [true/false/remove]'
          Description: Set general flags on all residences owned by particular player
        lease:
          Info:
          - '&eUsage: &6/res lease [renew/cost] [residence]'
          - /res lease cost will show the cost of renewing a residence lease.
          - /res lease renew will renew the residence provided you have enough money.
          Description: Manage residence leases
          SubCommands:
            set:
              Description: Set the lease time
              Info:
              - '&eUsage: &6/resadmin lease set [residence] [#days/infinite]'
              - Sets the lease time to a specified number of days, or infinite.
            renew:
              Description: Renew the lease time
              Info:
              - '&eUsage: &6/resadmin lease renew <residence>'
              - Renews the lease time for current or specified residence.
            list:
              Description: Show lease list of current residence
              Info:
              - '&eUsage: &6/resadmin lease list <residence> <page>'
              - Prints out all subzones lease times
            expires:
              Description: Lease end date
              Info:
              - '&eUsage: &6/resadmin lease expires <residence>'
              - Shows when expires residence lease time.
            cost:
              Description: Shows renew cost
              Info:
              - '&eUsage: &6/resadmin lease cost <residence>'
              - Shows how much money you need to renew residence lease.
        tp:
          Info:
          - '&eUsage: &6/res tp [residence]'
          - Teleports you to a residence, you must have +tp flag access or be the
            owner.
          - Your permission group must also be allowed to teleport by the server admin.
          Description: Teleport to a residence
        setall:
          Info:
          - '&eUsage: &6/res setall [flag] [true/false/remove]'
          Description: Set general flags on all residences
        resreload:
          Description: Reload residence.
          Info:
          - '&eUsage: &6/resreload'
        resload:
          Description: Load residence save file.
          Info:
          - '&eUsage: &6/resload'
          - UNSAFE command, does not save residences first.
          - Loads the residence save file after you have made changes.
