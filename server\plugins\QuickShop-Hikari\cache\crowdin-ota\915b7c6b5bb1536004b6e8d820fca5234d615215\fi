break-shop-use-supertool: <yellow>Rikot kauppoja käyttäen tyokalua.
fee-charged-for-price-change: <green>Ma<PERSON><PERSON> {0} vai<PERSON><PERSON><PERSON> kaupan hinnan.
not-allowed-to-create: <red>Et luultavasti voi luoda kauppaa tänne.
disabled-in-this-world: <red>QuickShop on poistettu käytöstä tässä maailmassa
how-much-to-trade-for: <green><PERSON><PERSON><PERSON><PERSON> chattiin, mihin hintaan haluat vaihtaa <yellow>{1}x {0}<green>.
client-language-changed: <green>QuickShop detected your client language setting has been changed, we're now using {0} locale for you.
shops-backingup: <PERSON><PERSON><PERSON>-varmuuskopio tietokannasta...
_comment: Hei kääntäjä! Jos muokkaat tätä Githubista tai koodilähteistä, sinun pitäisi mennä https://crowdin.com/project/quickshop-hikari
unlimited-shop-owner-changed: <yellow>Tämä rajoittamaton kaupan omistaja on muutettu {0}.
bad-command-usage-detailed: '<red>Bad command arguments! Accepts the following parameters: <gray>{0}'
thats-not-a-number: <red>Epäkelpo numero
shop-name-disallowed: <red>The shop name <yellow>{0} <red>is disallowed. Pick another one!
console-only-danger: <red>Tämä on vaarallinen komento, joten sen voi suorittaa vain Konsolista.
not-a-number: <red>Voit ainoastaan antaa numeroita, kirjoitit {0}.
not-looking-at-valid-shop-block: <red>Could not find a block for creating shop. You need to look at one.
shop-removed-cause-ongoing-fee: <red>Kauppasi {0} on poistettu, koska sinulla ei ollut rahaa sen pitämiseen!
tabcomplete:
  amount: '[määrä]'
  item: '[tuote]'
  price: '[hinta]'
  name: '[name]'
  range: '[säde]'
  currency: '[valuutan nimi]'
  percentage: '[percentage%]'
taxaccount-unset: <green>This shop's tax account now following server global setting.
blacklisted-item: <red>Et voi myydä tätä tavaraa, koska se on mustalla listalla
command-type-mismatch: <red>Tämän komennon voi suorittaa vain <aqua>{0}.
server-crash-warning: '<red>Palvelin voi kaatua /qs reload komennon jälkeen jos vaihdat tai poistat QuickShopin Jar tiedoston kun palvelin on päällä.'
you-cant-afford-to-change-price: <red>Hinnan vaihtaminen maksaa {0}.
safe-mode: <red>QuickShop now in safe-mode, you cannot open this shop container, please contact with server administrator to fix the errors.
forbidden-vanilla-behavior: <red>Toimenpide on kielletty, koska se ei ole yhdenmukainen vanilla käyttäytymisen kanssa
shop-out-of-space-name: <dark_purple>Kauppasi {0} on täynnä!
paste-disabled: |-
  <red>Paste functionality has been disabled! You cannot request technical support. <newline>Reason: {0}
quick-fill:
  entry-own: ' <yellow>- <aqua>{0} <light_purple>{1}'
  hover:
    - '<yellow>Nimi: <aqua>{0}'
    - '<yellow>Omistaja: <aqua>{0}'
    - '<yellow>Tyyppi: <aqua>{0}'
    - '<yellow>Hinta: <aqua>{0}'
    - '<yellow>Tuote: <aqua>{0}'
    - '<yellow>Sijainti: <aqua>{0}'
  hover-arg-filled:
    - '<yellow>Nimi: <aqua>{name}'
    - '<yellow>Omistaja: <aqua>{owner}'
    - '<yellow>Tyyppi: <aqua>{type}'
    - '<yellow>Hinta: <aqua>{price}'
    - '<yellow>Tuote: <aqua>{item}'
    - '<yellow>Sijainti: <aqua>{location}'
  header: '<yellow>You have multiple shops with name "<green>{0}<yellow>", choose one to continue:'
  entry-normal: ' <yellow>- <aqua>{0} <light_purple>{1}'
  entry-cooperation: ' <yellow>- <red>[co] <aqua>{0} <light_purple>{1}'
3rd-plugin-build-check-failed-admin: '<gray>(AdminOnly) <light_purple>{0}<dark_gray> denied the permission checks. If this not expected, try adding <light_purple>{1} <gray>to the listener blacklist. Configuration Guide:https://ghost-chu.github.io/QuickShop-Hikari-Documents/docs/modules/shops/protection-checker'
average-price-nearby: '<green>Lähistöllä olevien keskimääräinen hinta: <yellow>{0}'
inventory-check-global-alert: "<red>[Inventory Check] <gray>Warning! Found a QuickShop display item <gold>{2}</gold> in inventory at <aqua>{0}</aqua> <dark_gray>[{1}]</dark_gray>, which it shouldn't happened, This usually means that someone is maliciously exploiting the exploit to duplicating the display item."
digits-reach-the-limit: <red>Olet saavuttanut desimaalien rajan hinnassa.
currency-unset: <green>Kaupan valuutta poistettiin onnistuneesti. Käytetään oletusasetuksia.
you-cant-create-shop-in-there: <red>Sinulla ei ole oikeutta luoda kauppaa tälle alueelle.
no-pending-action: <red>Sinulla ei ole mitään odottavia toimintapyyntöjä
refill-success: <green>Uudelleentäyttö onnistui
failed-to-paste: <red>Tiedon lataaminen Pastebiniin epäonnistui. Tarkista internet yhteytesi ja yritä uudelleen. (Katso konsolista lisätietoja)
shop-out-of-stock-name: <dark_purple>Kaupastasi {0} on loppunut {1}!
shop-name-invalid: <red>The shop name <yellow>{0} <red>is invalid. Pick another one!
how-many-buy-stack: <yellow>Enter in chat, how many bulks you wish to <aqua>BUY<green>. There are <yellow>{0}<green> items in each bulk and you can buy <yellow>{1}<green> bulks. Enter <aqua>{2}<green> in chat to buy all.
exceeded-maximum: <red>Arvo ylitti Javan enimmäisarvon.
unlimited-shop-owner-keeped: '<yellow>Huomio: Kaupan omistajalla on edelleen rajaton kauppa, sinun täytyy asettaa kaupan uusi omistaja itse.'
no-enough-money-to-keep-shops: <red>Sinulla ei ollut tarpeeksi rahaa kauppojen ylläpitoon! Kaikki kaupat on poistettu...
3rd-plugin-build-check-failed: <red>Kolmannen osapuolen lisäosa <bold>{0}<reset><red> kielsi oikeuden tarkistukset, onko sinulla oikeudet asennettu oikein täällä?
not-a-integer: <red>Voit ainoastaan antaa kokonaislukuja, kirjoitit {0}.
translation-country: 'Kielialue: Suomi (fi_FI)'
buying-more-than-selling: '<red>VAROITUS: Olet ostamassa tavaraa kalliimmalla kuin myymässä!'
purchase-failed: '<red>Osto epäonnistui: Sisäinen virhe. Ota yhteyttä palvelimen ylläpitoon.'
denied-put-in-item: <red>Et voi laittaa tätä tavaraa kauppaasi!
shop-has-changed: <red>Kauppa, jota yritit käyttää, on muuttanut hintaansa tai muita tietoja sen jälkeen kun klikkasit sitä!
flush-finished: <green>Viestit päivitettiin onnistuneesti.
no-price-given: <red>Annathan validin hinnan.
shop-already-owned: <red>Tämä on jo kauppa.
backup-success: <green>Varmuuskopiointi onnistui.
not-looking-at-shop: <red>QuickShoppia ei löytynyt. Sinun täytyy katsoa yhtä.
you-cant-afford-a-new-shop: <red>Kaupan luominen maksaa {0}.
success-created-shop: <red>Kauppa luotu.
shop-creation-cancelled: <red>Kaupan luonti peruttu.
shop-owner-self-trade: <yellow>Käyt kauppaa omassa kaupassa, joten et saa lisää rahaa.
purchase-out-of-space: <red>This shop run out of the space, Contact shop owner or staffs to empty the shop.
reloading-status:
  success: <green>Reload completed without any errors.
  scheduled: <green>Reload completed. <gray>(Some changes required a while to affect)
  require-restart: <green>Reload completed. <yellow>(Some changes require server restart to affect)
  failed: <red>Reload failed, check the server console
player-bought-from-your-store-tax: <green>{0} osti {1} {2} kaupastasi ja sait siitä {3} ({4} verot).
not-enough-space: <red>Sinulla on tilaa vain {0} lisää!
shop-name-success: <green>Successfully set the shop name to <yellow>{0}<green>.
shop-staff-added: <green>Onnistuneesti lisätty {0} kauppasi henkilökuntaan.
shop-staff-empty: <yellow>This shop have no staff members.
shops-recovering: Palautetaan kauppoja varmuuskopiosta...
virtual-player-component-hover: "<gray>This is a virtual player.\n<gray>Refers to a system account of this name with the same name.</gray>\n<green>UUID: <yellow>{0}</yellow></green>\n<green>Username: <yellow>{1}</yellow></green>\n<green>Display as: <yellow>{2}</yellow></green>"
real-player-component-hover: "<gray>This is a real exists player.\n<green>UUID: <yellow>{0}</yellow></green>\n<green>Username: <yellow>{1}</yellow></green>\n<green>Display as: <yellow>{2}</yellow></green>\n<gray>If you wish to use a virtual system account with the same name, add <dark_gray>\"[]\"</dark_gray> to the both side of the username: <dark_gray>[{1}]</dark_gray>."
menu:
  sell-tax: <green>Maksoit <yellow>{0} <green>veroa.
  owner: '<green>Omistaja: {0}'
  preview: <aqua>[Tavaran esikatselu]
  enchants: <dark_purple>Lumoukset
  sell-tax-self: <green>Sinun ei tarvi maksaa veroja, koska omistat tämän kaupan.
  shop-information: '<green>Kaupan tiedot:'
  item: '<green>Tuote: <yellow>{0}'
  price-per: <green>Hinta per <yellow>{0} <green>- <yellow>{1}
  item-name-and-price: <yellow>{0} {1} <green>hintaan <yellow>{2}
  item-name-and-price-tax: <yellow>{0} {1} <green></green> {2} <gray>(<green>{3}</green> veroissa)</gray>
  successful-purchase: '<green>Onnistuneesti ostettu:'
  price-per-stack: <green>Hinta per <yellow>{2}x {0} - {1}
  stored-enchants: <dark_purple>Varastoidut Lumoukset
  item-holochat-error: <red>[Error]
  this-shop-is-selling: <green>Tämä kauppa <red><bold>MYY <green>tavaroita.
  shop-stack: '<green>Irtotavaran määrä: <yellow>{0}'
  space: '<green>Vapaa tila: <yellow>{0}'
  effects: <green>Efektit
  damage-percent-remaining: <yellow>{0}% <green>jäljelle.
  item-holochat-data-too-large: <red>[Error] Item NBT is too large for showing
  stock: '<green>Varastossa: <yellow>{0}'
  this-shop-is-buying: <green>Tämä kauppa <green><bold>OSTAA <green>tuotteita.
  successfully-sold: '<green>Onnistuneesti myyty:'
  total-value-of-chest: '<green>Arkun kokonaisarvo: <yellow>{0}'
currency-not-exists: <red>Valuutan jonka kirjoitit ei löytynyt. Ehkä kirjoitit sen väärin tai valuutta ei ole käytössä tässä maailmassa.
no-nearby-shop: <red>Mikään lähellä oleva kauppa ei vastaa {0}.
translation-author: 'Ghost_chu, Andre_601'
integrations-check-failed-trade: <red>Intergraatio {0} kielsi kaupankäynnin
shop-transaction-failed: <red>Sorry, but an internal error occurred while processing your purchase. The purchase has been cancelled and any operation has been rollback. Please contact the server administrators if it keeping occurred.
success-change-owner-to-server: <green>Onnistuneesti vaihdettu kaupan omistaja palvelimeen.
shop-name-not-found: <red>The shop named <yellow>{0} <red>not exists.
shop-name-too-long: <red>Kaupan nimi on liian pitkä (maksimipituus {0}), valitse toinen!
metric:
  header-player: '<yellow>{0}''s {1} {2} Tapahtumat:'
  action-hover: <yellow>{0}
  price-hover: <yellow>Yhteensä {0}, sisältäen {1} veroa.
  unknown: <gray>(tuntematon)
  undefined: <gray>(ei nimeä)
  no-results: Tapahtumia ei löytynyt
  action-description:
    DELETE: <yellow>Player deleted a shop. And refunded the shop creation fee to owner if possible.
    ONGOING_FEE: <yellow>Player paid the ongoing fee because the payment period.
    PURCHASE_BUYING_SHOP: <yellow>Pelaaja myi joitakin tuotteita ostavaan kauppaan.
    CREATE: <yellow>Pelaaja loi kaupan.
    PURCHASE_SELLING_SHOP: <yellow>Pelaaja osti joitakin esineitä myyvästä kaupasta
    PURCHASE: <yellow>Ostettu esine kaupasta
  query-argument: 'Kyselyn Argumentti: {0}'
  amount-hover: <yellow>{0}x
  header-shop: '<yellow>Kaupan {0} {1} {2} tapahtumat:'
  player-hover: |-
    <yellow>{0}<newline><gold>UUID: <gray>{1}
  looking-up: <yellow>Suoritat metrijärjestelmän haun, odota...
  tax-hover: <yellow>{0} verot
  header-global: '<yellow>{0}''s {1} Tapahtumat:'
  na: <gray>N/A
  transaction-count: <yellow>{0} yhteensä
  shop-hover: |-
    <yellow>{0}<newline><gold>Pos: <gray>{1} {2} {3}, Maailma: {4}<newline><gold>Omistaja: <gray>{5}<newline><gold>Kaupan tyyppi: <gray>{6}<newline><gold>Esine: <gray>{7}<newline><gold>Hinta: <gray>{8}
  time-hover: '<yellow>Aika: {0}'
  amount-stack-hover: <yellow>{0} yhteensä
permission-denied-3rd-party: '<red>Oikeus kielletty: kolmannen osapuolen lisäosa [{0}].'
you-dont-have-that-many-items: <red>Sinulla on vain {0} {1}.
complete: <green>Valmis!
translate-not-completed-yet-url: 'Kielen {0} käännös ei ole vielä valmis {1} toimesta. Haluatko auttaa meitä parantamaan käännöstä? Selaile: {2}'
success-removed-shop: <green>Kauppa poistettu.
currency-set: <green>Kaupan valuutta asetettu onnistuneesti {0}.
shop-purged-start: <green>Kaupan tyhjennys aloitettu, tarkista konsolista lisätietoja.
economy-transaction-failed: <red>Sorry, but an internal error occurred while processing your transaction. The transaction has been cancelled and any economy operation has been rollback. Please contact the server administrators if it keeping occurred.
nothing-to-flush: <green>Sinulle ei ole uusia kauppaviestejä.
no-price-change: <red>Tuo ei vaihtaisi kaupan hintaa!
edition-confilct: QuicShop-Hikari with QuickShop-Reremake installed may conflict each other, uninstall one of them.
inventory-unavailable: |-
  <red>This shop InventoryWrapper not exists or invalid. Did you using addon to re-binding the shop Inventory? Information: InventoryWrapper={0}, WrapperProvider={1}, SymbolLink={2}. Please contact the server administrators.
file-test: Tämä on testi tekstitiedosto. Käytämme sitä testataksemme onko messages.json rikki. Voit täyttää sen millä tahansa pääsiäismunalla, josta pidät :)
unknown-player: <red>Kohde pelaajaa ei ole olemassa, tarkista käyttäjätunnus jonka kirjoitit.
player-offline: <red>The target player currently is offline.
player-profile-format: <aqua>{0}</aqua><gold>(<yellow>{1}</yellow>)</gold>
shop-type:
  selling:
  buying: BUYING
language:
  qa-issues: '<yellow>Laadunvarmistuksen ongelmat: <aqua>{0}%'
  code: '<yellow>Koodi: <gold>{0}'
  approval-progress: '<yellow>Hyväksynnän edistyminen: <aqua>{0}%'
  translate-progress: '<yellow>Käännöksen edistyminen: <gold>{0}'
  name: '<yellow>Nimi: <gold>{0}'
  help-us: <green>[Auta meitä parantamaan käännösten laatua]
warn-to-paste: |-
  <yellow>Kerätään tietoja ja ladataan ne Pastebiniin, tässä saattaa mennä hetki. <red><bold>Varoitus:<red> Tietoja pidetään julkisena vain yhden viikon! Se voi vuotaa palvelimesi asetuksia tai muuta arkoluontoista tietoa. Varmista, että lähetät sen vain <bold>luotettaville ylläpidon jäsenille ja kehittäjille.
how-many-sell-stack: <green>Enter in chat, how many bulk you wish to <light_purple>SELL<green>. There are <yellow>{0}<green> items per bulk and you can sell <yellow>{1}<green> bulks. Enter <aqua>{2}<green> in chat to sell all.
updatenotify:
  buttontitle: '[Päivitä nyt]'
  onekeybuttontitle: '[Klikkaa Päivittääksesi]'
  label:
    github: '[GitHub]'
    ore: '[Ore]'
    lts: '[LTS]'
    nbtapi: '[+NBTAPI]'
    qualityverifyed: '[Laatu]'
    master: '[Master]'
    unstable: '[Epävakaa]'
    paper: '[+Paper Optimoitu]'
    stable: '[Vakaa]'
    spigotmc: '[SpigotMC]'
    papermc: '[PaperMC]'
    bukkitdev: '[BukkitDev]'
    polymart: '[Polymart]'
    modrinth: '[Modrinth]'
    basic: '[Perus]'
  list:
    - '{0} julkaistiin. Käytät yhä {1}!'
    - Pam! Uusi päivtys {0} tulossa. Päivitä!
    - Yllätys! {0} julkaistiin. Olet yhä versiossa {1}
    - Näyttää siltä, että tarvitset päivitystä. {0} julkaistiin!
    - Hupss! {0} on nyt nyt julkaistu. Olet yhä versiossa {1}!
    - Kiroan, QS on päivittynyt versioon {0}. Miksi et ole vielä päivittänyt?
    - Korjataan ja uu... Sori, mutta {0} on julkaistu!
    - Virhe! Eipäs. Tämä ei ole virhe. {0} on julkaistu!
    - OMG! {0} on tullut ulos! Miksi käytät yhä {1}?
    - 'Päivän uutiset: QuickShop päivittyi versioon {0}!'
    - Lisäsosa k.i.a. Sinun pitäisi päivittää versioon {0}!
    - Päivitys {0} sytytetty. Pelasta päivitys!
    - Päivitys saatavilla Komentaja. {0} on juuri tullut ulos!
    - Katso tyyliäni---{0} päivitetty. Olet yhä versiossa {1}
    - Jeeeeeeee! Uusi päivitys {0} on tullut! Päivitä!
    - Mitä mieltä olet? {0} on julkaistu! Päivitä!
    - Tohtori, QuickShop sai uuden päivityksen {0}! Sinun pitäisi päivittää~
    - Ko~ko~da~yo~ QuickShop on saanut uuden päivityksen {0}~
    - Paimon haluaa kertoa sinulle, että QuickShopilla on uusi päivitys {0}!
  remote-disable-warning: '<red>Tämä QuickShopin version on merkitty etäpalvelimella poistettavaksi versioksi. Se tarkoittaa, että tällä versiolla saattaa olla vakavia ongelmia. Lisätietoja saat meidän SpigotMC sivuilta: {0}. Tämä varoitus ilmestyy joka kerta kunnes vaihdat pluginin vakaaseen versioon. Tämä varoitus ei vaikuta palvelimesi toimintaan.'
purchase-out-of-stock: <red>This shop run out of the stock, Contact shop owner or staffs to refill the stock.
nearby-shop-entry: '<green>- Info:{0} <green>Hinta:<aqua>{1} <green>x:<aqua>{2} <green>y:<aqua>{3} <green>z:<aqua>{4} <green>matka: <aqua>{5} <green>kuutio(ta)'
chest-title: QuickShop Kauppa
console-only: <red>Tämä komento voidaan suorittaa vain Konsolista.
failed-to-put-sign: <red>Kaupan ympärillä ei ole tarpeeksi tilaa tietokylttiä varten.
shop-name-unset: <red>This shop's name now removed
shop-nolonger-freezed: <green>Poistit kaupan jäädytyksen. Se on nyt normaalissa tilassa!
no-permission-build: <red>Et voi rakentaa kauppaa tähän.
tableformat:
  left_half_line: <dark_purple>+----------------
  right_half_line: <dark_purple>+----------------
  full_line: <dark_purple>+---------------------------------------------------------------+
  left_begin: '<dark_purple>| '
quickshop-gui-preview: QuickShop GUI Tavaran Esikatselu
translate-not-completed-yet-click: Kielen {0} käännös ei ole vielä valmis {1} toimesta. Haluatko auttaa meitä parantamaan käännöstä? Klikkaa tästä!
taxaccount-invalid: <red>Target account not invalid, please enter a valid player name or uuid(with dashes).
player-bought-from-your-store: <red>{0} osti {1} {2} kaupastasi ja sait siitä {3}.
reached-maximum-can-create: <red>Olet jo luonut jo {0}/{1}, eli maksimi määrän kauppoja!
reached-maximum-create-limit: <red>Olet saavuttanut maksimimäärän kauppoja, joita voit luoda
translation-version: 'Tukiversio: Hikari'
no-double-chests: <red>Et voi luoda tupla-arkkukauppaa.
price-too-cheap: <red>Hinnan pitää olla korkeampi kuin <yellow>${0}
shop-not-exist: <red>Siellä ei ole kauppaa.
bad-command-usage: <red>Huonot komentoargumentit!
cleanghost-warning: <yellow>This command will purge <red>all</red> shops if the shop is corrupted, was created in not allowed worlds, is selling/buying not allowed items or <red><bold>EXISTS IN A UNLOADED WORLD</bold></red>. Make sure to create a full backup of your shop data first and use <aqua>/qs cleanghost confirm</aqua> to continue.
cleanghost-starting: <green>Starting to check for ghost shops (missing container blocks). All non-existing shops will be removed...
cleanghost-deleting: <yellow>Found a corrupted shop <aqua>{0}</aqua> because {1}, mark it to delete...
cleanghost-deleted: <green>Total <yellow>{0}</yellow> shops have been deleted.
shop-purchase-cancelled: <red>Kaupasta ostaminen peruttu.
bypassing-lock: <red>Ohitetaan QuickShop lukitus!
bungee-cross-server-msg: '<yellow>QuickShop CSM: <green>{0}'
saved-to-path: Varmuuskopio tallennettin {0}.
shop-now-freezed: <green>Olet jäädyttänyt kaupan. Kukaan ei voi vaihtaa kaupan kanssa nyt!
price-is-now: <green>Kaupan uusi hinta on nyt <yellow>{0}
shops-arent-locked: <red>Muista, kaupat EIVÄT ole suojattu varkailta! Jos haluat suojata arkkusi, käytä esim. LWC- tai Lockette-lisäosaa!
that-is-locked: <red>Tämä kauppa on lukossa.
shop-has-no-space: <red>Kaupassa on {1} paikkaa tuotteelle {0}.
safe-mode-admin: '<red><bold>WARNING: <red>The QuickShop on this server now running under safe-mode, no features will working, please type <yellow>/qs <red> command to check any errors.'
shop-stock-too-low: <red>Kaupassa on jäljellä {0} {1}!
world-not-exists: <red>Maailmaa <yellow>{0}<red> ei ole olemassa
how-many-sell: <green>Enter in chat, how much you wish to <light_purple>SELL<green>. You can sell <yellow>{0}<green>. Enter <aqua>{1}<green> in chat, to sell all.
shop-freezed-at-location: <yellow>Kauppasi {0} {1} jäätyi!
translation-contributors: 'Avustajat: Timtower, Netherfoam, KaiNoMood, Mgazul, JackTheChicken and Andre_601'
empty-success: <green>Kaupan tyhjennys onnistui
taxaccount-set: <green>Tämän kaupan verotiliksi on määritetty <yellow>{0}
support-disable-reason:
  hot-reload: <yellow>Unsupported hot reloaded, restart the server and check again.
  outdated: <yellow>This version of QuickShop already outdated, up-to-date before requesting support!
  bad-hosts: |-
    <yellow>This server HOSTS has been modified and QuickShop some functions require connect to Mojang API correctly. Fix the HOSTS before asking support, Windows at C:\windows\system32\drivers\etc\hosts, Linux at /etc/hosts.
  privacy: <yellow>This server running under cracked (offline) mode. If you are running bukkit server under proxy and online-mode=true on proxy, please configure the proxy related settings correctly.
  modified: <yellow>File integrity check failed, this QuickShop build has been modified by others.
  consolespamfix-installed: <yellow>ConsoleSpamFix installed, it will hidden exception details, temporarily disable it during asking support.
  authlib-injector-detected: <yellow>This server running under 3rd-party authlib provider like authlib-injector.
  unsupported-server-software: <yellow>Unsupported server software, Any modded hybrid server software is unsupported including MCPC, Cauldron, CatServer, Mohist, Magma, Fukkit, Cardboard etc.
supertool-is-disabled: <red>Supertyökalu on poistettu käytöstä, et voi rikkoa mitään kauppoja.
unknown-owner: Tuntematon
restricted-prices: '<red>Rajattu hinta tuotteelle {0}: vähintään {1}, enintään {2}'
nearby-shop-this-way: <green>Kauppa on {0} kuution päässä sinusta.
owner-bypass-check: <yellow>Ohitit kaikki tarkastukset. Vaihto onnistui! (Olet nyt kaupan omistaja!)
april-rick-and-roll-easter-egg: "<hover:show_text:'Click to open in browser for time limited rewards!'><click:open_url:'https://www.youtube.com/watch?v=dQw4w9WgXcQ'><green>Click here to acquire your time limited rewards that provided by QuickShop-Hikari developer!</green></click></hover>"
signs:
  item-right: ''
  out-of-space: Arkku Täynnä
  unlimited: Rajaton
  stack-selling: Myydään {0}
  stack-price: '{0} per {1}x {2}'
  status-unavailable: <red>
  out-of-stock: Loppuunmyyty
  stack-buying: Ostetaan {0}
  freeze: Vaihto jäädytetty
  price: '{0} kpl'
  buying: Ostetaan {0}
  header: '{1}{0}'
  header-available: <green>{0}
  header-unavailable: <red>{0}
  selling: '{0} kpl'
  status-available: <green>
  item-left: ''
negative-amount: <red>Et voi vaihtaa negatiivisia määriä
display-turn-on: <green>Successfully turn on the shop display.
shop-staff-deleted: <green>Onnistuneesti poistettu {0} kauppasi henkilökunnasta.
nearby-shop-header: '<green>Lähellä oleva kauppa vastaa <aqua>{0}<green>:'
backup-failed: Tietokantaa ei pystytty varmuuskopioimaan, katso konsoli yksityiskohtia varten.
shop-staff-cleared: <green>Kauppasi henkilökunta poistettiin onnistuneesti.
price-too-high: <red>Kaupan hinta on liian korkea! Et voi luoda kauppaan, jonka hinta on korkeampi kuin {0}.
plugin-cancelled: '<red>Operation cancelled, Reason: {0}'
player-sold-to-your-store: <green>{0} myi {1} {2} kauppaasi.
shop-out-of-stock: <dark_purple>Kaupastasi koordinaateissa {0}, {1}, {2} on loppunut {3}!
how-many-buy: <green>Enter in chat, how many you wish to <aqua>BUY<green>. You can buy <yellow>{0}<green>. Enter <aqua>{1}<green> to buy them all.
language-info-panel:
  help: 'Auta meitä: '
  code: 'Koodi: '
  name: 'Kieli: '
  progress: 'Edistyminen: '
  translate-on-crowdin: '[Käännä Crowdinissa]'
item-not-exist: <red>Esinettä <yellow>{0} <red>ei ole olemassa, tarkista oikeinkirjoitus.
shop-creation-failed: <red>Kaupan luonti epäonnistui, ota yhteyttä palvelimen järjestelmänvalvojaan.
inventory-space-full: <red>Your inventory space remains can only put <green>{1}x <red>items into it, try empty the your inventory!
no-creative-break: <red>Et voi rikkoa muiden pelaajien kauppoja luova tilassa, vaihda selviytymistilaan tai yritä käyttää supertyökalua {0}.
booleanformat:
  success: <green>✔
  failed: <red>✘
controlpanel:
  stack: '<green>Per bulk amount: <aqua>{0} <yellow>[<light_purple><bold>Change<yellow>]'
  price-hover: <yellow>Paina asettaaksesi uusi hinta tuotteelle.
  remove: <red><bold>[Poista kauppa]
  mode-buying-hover: <yellow>Paina asettaaksesi kauppa myynti-tilaan.
  empty: '<green>Empty: Remove all items <yellow>[<light_purple><bold>OK<yellow>]'
  stack-hover: <yellow>Klikkaa asettaaksesi tavaran määrä bulkkia kohden. Aseta 1 normaalia käytöstä varten.
  alwayscounting-hover: <yellow>Click to toggle if the shop is always counting container.
  alwayscounting: '<green>Always counting: {0} <yellow>[<light_purple><bold>Change<yellow>]'
  setowner: '<green>Owner: <aqua>{0} <yellow>[<light_purple><bold>Change<yellow>]'
  freeze: '<yellow>Freeze mode: <aqua>{0} <yellow>[<light_purple><bold>Toggle<yellow>]'
  price: '<green>Price: <aqua>{0} <yellow>[<light_purple><bold>Change<yellow>]'
  currency-hover: <yellow>Klikkaa muuttaaksesi valuuttaa jota tämä kauppa käyttää
  lock: '<yellow>Shop lock: <aqua>{0} <yellow>[<light_purple><bold>Toggle<yellow>]'
  mode-selling: '<green>Kaupan tila: <aqua>Myydään <yellow>[<light_purple><bold>Muuta<yellow>]'
  currency: '<green>Currency: <aqua>{0} <yellow>[<light_purple><bold>Set<yellow>]'
  setowner-hover: <yellow>Klikkaa vaihtaaksesi omistaja.
  mode-buying: '<green>Shop mode: <aqua>Buying <yellow>[<light_purple><bold>Change<yellow>]'
  item: '<green>Shop Item: {0} <yellow>[<light_purple><bold>Change<yellow>]'
  unlimited: '<green>Unlimited: {0} <yellow>[<light_purple><bold>Change<yellow>]'
  unlimited-hover: <yellow>Klikkaa vaihtaaksesi kaupan rajattomuutta.
  refill-hover: <yellow>Klikkaa täyttääksesi kaupan varasto.
  remove-hover: <yellow>Poista tämä kauppa klikkaamalla.
  toggledisplay-hover: <yellow>Toggle the shop's displayitem status
  refill: '<green>Refill: Refill the items <yellow>[<light_purple><bold>OK<yellow>]'
  freeze-hover: <yellow>Vaihda kaupan jäädytystila.
  lock-hover: <yellow>Ota käyttöön tai poista käytöstä kaupan lukitussuojaus.
  item-hover: <yellow>Paina vaihtaaksesi kaupan esinettä
  infomation: '<green>Kaupan asetukset:'
  mode-selling-hover: <yellow>Paina asettaaksesi kauppa osto-tilaan.
  empty-hover: <yellow>Klikkaa tyhjentääksesi kaupan varasto.
  toggledisplay: '<green>Display-Item: <aqua>{0} <yellow>[<light_purple><bold>Toggle</bold></light_purple>]'
  history: '<green>History: <yellow>[<bold><light_purple>View</light_purple></bold>]</yellow>'
  history-hover: <yellow>Click to view shop history logs
timeunit:
  behind: behind
  week: "{0} week"
  weeks: "{0} weeks"
  year: "{0} year"
  before: before
  scheduled: scheduled
  years: "{0} years"
  scheduled-in: scheduled in {0}
  second: "{0} second"
  std-past-format: '{5}{4}{3}{2}{1}{0}ago'
  std-time-format: HH:mm:ss
  seconds: "{0} seconds"
  hour: "{0} hour"
  scheduled-at: scheduled at {0}
  after: after
  day: "{0} day"
  recent: recent
  between: between
  hours: "{0} hours"
  months: "{0} months"
  longtimeago: long time ago
  between-format: between {0} and {1}
  minutes: "{0} minutes"
  justnow: just now
  minute: "{0} minute"
  std-format: MM/dd/yyyy HH:mm:ss
  future-plain-text: future
  month: "{0} month"
  future: in {0}
  days: "{0} days"
command:
  reloading: '<green>Configuration reloaded. <yellow>Some changes may require reboot to affect. <newline><gray>(Notice: Reloading behavior has been changed after ********, we now only reload configuration but not whole plugin to ensure the server won''t crashed.)'
  description:
    buy: <yellow>Vaihtaa kaupan <light_purple>ostamaan<yellow> tuotteita
    about: <yellow>Näytä QuickShop tietoja
    language: <yellow>Vaihda tällä hetkellä käytettyä kieltä
    purge: <yellow>Start the shop purge task in background
    paste: <yellow>Lataa palvelin tietoja pastebiniin
    title: <green>QuickShop apu
    remove: <yellow>Poistaa kaupan, jota katsot
    ban: <yellow>Antaa pelaajalle porttikiellon kauppaasi
    empty: <yellow>Tyhjentää kaupan varaston
    alwayscounting: <yellow>Set if shop always counting container even is unlimited
    setowner: <yellow>Vaihtaa kaupan omistajaa.
    reload: <yellow>Uudelleenlataa QuickShopin config.yml tiedoston
    freeze: <yellow>Ota kaupankäynti käyttöön tai poista se käytöstä
    price: <yellow>Vaihtaa kaupan osto- tai myyntihinnan kaupassasi
    find: <yellow>Etsii lähimmän kaupan haluumallesi tavaralle.
    create: <yellow>Luo uuden kaupan kohde arkusta
    lock: <yellow>Vaihda kaupan lukituksen tila
    currency: <yellow>Muuta kaupan valuutta-asetuksia
    removeworld: <yellow>Poista KAIKKI kaupat kyseisestä maailmasta
    info: <yellow>Näytä QuickShop tilastoja
    owner: <yellow>Vaihtaa kaupan omistajaa.
    amount: <yellow>Aseta tuotteen määrä (Hyödyllinen chat ongelmien aikana)
    item: <yellow>Vaihda kauppatavara
    debug: <yellow>Käyttöön ottaa kehittäjä tilan
    unlimited: <yellow>Antaa kaupalle loputtoman varaston.
    sell: <yellow>Vaihtaa kaupan <green>myymään<yellow> tuotteita
    fetchmessage: <yellow>Näyttää lukemattomat kauppa viestit
    staff: <yellow>Hallitse kauppasi henkilökuntaa
    clean: <yellow>Poistaa kaikki (ladatut) kaupat, jotka ovat tyhjiä
    refill: <yellow>Lisää tietyn määrän tavaroita kauppaan
    help: <yellow>Näytä QuickShop ohjeet
    removeall: <yellow>Poista KAIKKI kaupat kyseiseltä pelaajalta
    unban: <yellow>Poistaa pelaajan porttikiellon kaupastasi
    transfer: <yellow>Siirrä jonkun KAIKKI kaupat toiselle
    transferall: <yellow>Siirrä jonkun KAIKKI kaupat toiselle
    transferownership: <yellow>Transfer the shop you're looking at to someone else
    size: <yellow>Vaihda kaupan per bulkkimäärä
    supercreate: <yellow>Luo kauppa ohittamalla kaikki suojaustarkistukset
    taxaccount: <yellow>Set the tax account that shop using
    name: <yellow>Naming a shop to specific name
    toggledisplay: <yellow>Toggle the shop display item status
    permission: <yellow>Kaupan oikeuksien hallinta
    lookup: <yellow>Hallitse hakukelpoisten kohteiden taulua
    database: <yellow>View and maintain QuickShop database
    benefit: <yellow>Asetukset jaettujen oikeuksien kaupan omistan ja pelaajien välillä
    tag: <yellow>Lisää, poista tai kysy tunnisteita kaupasta
    suggestprice: <yellow>Suggest a recommend price for looking shop, based on other shops
    history: <yellow>View the history transactions of a shop
    sign: <yellow>Change the sign material of a shop
  bulk-size-not-set: '<red>Käyttö: /qs size <määrä>'
  no-type-given: '<red>Käytä: /qs find <tavara>'
  feature-not-enabled: Tämä ominaisuus ei ole käytössä asetustiedostossa.
  now-debuging: <green>Onnistuneesti siirrytty kehittäjätilaan. QuickShop ladataan uudelleen...
  no-amount-given: <red>Määrää ei annettu. Käytä <green>/qs refill <määrä><red>
  no-owner-given: <red>Ei omistajaa annettu
  disabled: '<red>Tämä komento on poistettu käytöstä: <yellow>{0}'
  bulk-size-now: <green>Nyt vaihdossa <yellow>{0}x {1}
  toggle-always-counting:
    counting: <green>Shop is now always counting container even is unlimited
    not-counting: <green>Shop is now respect if shop is unlimited
  cleaning: <green>Poistetaan kaupat ilman jäljellä olevaan irtotavaraa...
  now-nolonger-debuging: <green>Onnistuneesti siirrytty käyttötilaan. QuickShop ladataan uudelleen...
  toggle-unlimited:
    limited: <green>Kauppa ei ole enää rajaton
    unlimited: <green>Kaupassa on nyt rajattomasti tavaroita
  transfer-success-other: <green>Siirretty <yellow>{0} <green>pelaajan <yellow>{1}<green> kauppa(a) pelaajalle <yellow>{2}
  no-trade-item: <green>Pidä tavaraa kädessäsi vaihtaaksesi se
  wrong-args: <red>Virheellinen argumentti. Kirjoita <bold>/qs help <red>nähdäksesi lista komennoista.
  some-shops-removed: <yellow>{0} <green>kauppa(a) poistettu
  new-owner: '<green>Uusi omistaja: <yellow>{0}'
  format: <green>{0}{1}<yellow>{2}
  transfer-success: <green>Siirretty <yellow>{0} <green>kauppa(a) pelaajalle <yellow>{1}
  now-buying: <green>Nyt <light_purple>OSTETAAN <yellow>{0}
  now-selling: <green>Kauppasi <aqua>MYY<green> nyt tuotetta <yellow>{0}
  cleaned: <green>Poistettu <yellow>{0}<green> kauppaa.
  trade-item-now: <green>Nyt vaihdossa <yellow>{0}x {1}
  no-world-given: <red>Anna maailman nimi
  format-disabled: <red>{0}{1}<gray>{2}
  invalid-bulk-amount: <red>Annettu arvo {0} on suurempi kuin maksimi pino koko tai pienempi kuin yksi
currency-not-support: <red>Tämä ekonomia plugini ei tue multi-economy ominaisuutta.
trading-in-creative-mode-is-disabled: <red>Et voi ostaa/myydä luovassa tilassa.
the-owner-cant-afford-to-buy-from-you: <red>Tuotteen hinta on {0}, mutta omistajalla on vain {1}
you-cant-afford-shop-naming: <red>You can't afford shop naming, it costs {0} to naming.
inventory-error: |-
  <red>Failed to processing the InventoryWrapper. Did you using addon to re-binding the shop Inventory? Information: Exception={0}, InventoryWrapper={1}, WrapperType={2}, WrapperProvider={3}, SymbolLink={4}. Please contact the server administrators.
integrations-check-failed-create: <red>Intergraatio {0} kielsi kaupan luomisen
shop-out-of-space: <dark_purple>Kauppasi koordinaateissa {0}, {1}, {2} on nyt täynnä.
admin-shop: AdminKauppa
no-anythings-in-your-hand: <red>Kädessäsi ei ole mitään.
no-permission: <red>Sinulla ei ole oikeutta tehdä noin.
chest-was-removed: <red>Kauppa poistettiin onnisteuneesti.
you-cant-afford-to-buy: <red>Tuote maksaa {0}, mutta sinulla on vain {1}
shops-removed-in-world: <yellow>Yhteensä <aqua>{0}<yellow> kauppa(a) on poistettu maailmasta <aqua>{1}<yellow>.
display-turn-off: <green>Successfully turn off the shop display.
client-language-unsupported: <yellow>QuickShop doesn't support your client language, we're fallback to {0} locale now.
language-version: '63'
not-managed-shop: <red>Et ole tämän kaupan omistaja taikka valvoja
shop-cannot-trade-when-freezing: <red>Et voi vaihtaa tämän kaupan kanssa, koska se on jäässä.
invalid-container: <red>Invalid container, You can only create the shop on block who have inventory.
permission:
  header: <green>Kaupan käyttöoikeuksien yksityiskohdat
  header-player: '<green>Kaupan käyttöoikeuksien yksityiskohdat: {0}'
  header-group: '<green>Kaupan käyttöoikeuksien yksityiskohdat ryhmälle: {0}'
  table: <gold><b>{0}</b>:</gold> <gray>{1}</gray>
  item:
    purchase: <yellow>Permission to allow users who have this to purchase the shop. (including buying and selling)
    show-information: <yellow>Lupa sallia käyttäjiä näkemään kaupan tiedot. (avaa kaupan infopaneeli)
    preview-shop: <yellow>Permission to allow users who have this to preview the shop. (preview item)
    search: <yellow>Permission to allow users who have this to search the target shop. (remove permission will hide shop from search result)
    delete: <yellow>Permission to allow users who have this to delete the shop.
    receive-alert: <yellow>Permission to allow users who have this to receive alert message (e.g out-of-stock or new trade).
    access-inventory: <yellow>Permission to allow users who have this to access the shop inventory.
    ownership-transfer: <yellow>Permission to allow users who have this to transfer the shop ownership.
    management-permission: <yellow>Permission to allow users who have this to manage the groups permissions and edit user's group.
    toggle-display: <yellow>Permission to allow users who have this to toggle the shop display item.
    set-shoptype: <yellow>Permission to allow users who have this to set the shop type (switch buying or selling).
    set-price: <yellow>Permission to allow users who have this to set the shop price.
    set-item: <yellow>Permission to allow users who have this to set the shop item.
    set-stack-amount: <yellow>Permission to allow users who have this to set the shop stack amount.
    set-currency: <yellow>Permission to allow users who have this to set the shop currency.
    set-name: <yellow>Permission to allow users who have this to set the shop name.
    set-sign-type: <yellow>Change the sign material that attached on the shop.
    view-purchase-logs: <yellow>Permission to view shop purchase logs.
  group:
    everyone: <yellow>Default group for all users excepted shop owner.
    staff: <yellow>System group for shop staffs.
    administrator: <red>System group for administrators, the users in this group will have permissions almost same with shop owner.
invalid-group: <red>Invalid group name.
invalid-permission: <red>Invalid permission.
invalid-operation: <red>Invalid operation, only {0} are allowed.
player-no-group: <yellow>Player {0} not in any group in this shop.
player-in-group: <green>Player {0} is in group <aqua>{1}</aqua> in this shop.
permission-required: <red>You don't have permission {0} in this shop to do this.
no-permission-detailed: <red>You don't have permission <yellow>{0}</yellow> to do this.
paste-notice: "<yellow>Note: If you are creating a Paste for troubleshooting purposes, be sure to reproduce the error before creating the Paste as quickly as possible; we need the logs retained briefly in the buffer to troubleshoot. If you create a Paste too slowly or without first reproducing the error or restarted server, the Paste will record nothing and useless."
paste-uploading: <aqua>Please wait... Uploading the paste to pastebin......
paste-created: '<green>Paste created, click to open in browser: <yellow>{0}</yellow><newline><red>Warning: <gray>Never send paste to who you don''t trust.'
paste-created-local: |-
  <green>Paste created and saved to your local disk at: {0}<newline><red>Warning: <gray>Never send paste to who you don't trust.
paste-created-local-failed: <red>Failed to save paste to your local disk, please check your disk.
paste-451: |-
  <gray><b>TIPS: </b> Your current country or region seems to have blocked the CloudFlare Workers service and QuickShop Paste may not be loaded properly.<newline><gray>If the subsequent operation fails, try adding the --file additional parameter to generate a local Paste: <dark_gray>/qs paste --file</dark_gray>
paste-upload-failed: <red>Failed to upload paste to {0}, trying other pastebin provider...
paste-upload-failed-local: <red>Failed to upload paste, trying generate local paste...
command-incorrect: '<red>Command usage incorrect, type /qs help to check help. Usage: {0}.'
successfully-set-player-group: <green>Successfully set player {0} group to <aqua>{1}</aqua>.
successfully-unset-player-group: <green>Successfully remove player group in this shop.
successfully-set-player-permission: <green>Successfully set player {0} permission <aqua>{1}</aqua> in shop <aqua>{2}</aqua>.
lookup-item-created: <green>An item named <aqua>{0}</aqua> has been created in the lookup table. You can refer this item in configurations now.
lookup-item-exists: <red>An item named <yellow>{0}</yellow> already exists in the lookup table, delete it or pick another name.
lookup-item-not-found: <red>An item named <yellow>{0}</yellow> not exists.
lookup-item-name-illegal: <red>Item name illegal. Only alphanumeric characters and underscores are allowed.
lookup-item-name-regex: '<red>The name must match this regex: <aqua>{0}</aqua>.'
lookup-item-test-not-found: '<gold>Test: <yellow>The item in your hand not registered in lookup table.'
lookup-item-test-found: '<gold>Test: <green>The item in your hand was registered as name <aqua>{0}</aqua> in lookup table.'
lookup-item-removed: <green>The specified item <aqua>{0}</aqua> has been successfully removed from lookup table.
internal-error: <red>An internal error occurred, please contact the server administrator.
argument-cannot-be: <red>The argument <aqua>{0}</aqua> cannot be <yellow>{1}</yellow>.
argument-must-between: <red>The argument <aqua>{0}</aqua> value must between <yellow>{1}</yellow> and <yellow>{2}</yellow>
invalid-percentage: <red>Percentage invalid, you must add '%' after the number of percentage.
display-fallback: |-
  <red>Due to an internal failure, a fallback message is being displayed.
  The value of this item may not be localized or processed correctly.
  Please contact the server administrator.
not-a-valid-time: |-
  <red>The string <yellow>{0}</yellow> not a valid timestamp, please enter a <aqua>Zulu Time (ISO 8601)</aqua> or <aqua>Unix Epoch Time in seconds</aqua>.
  <gold>Valid timestamp example: (for Sat, 17 Dec 2022 10:31:37 GMT)</gold>
  <aqua>-<aqua> <yellow>2022-12-17T10:31:37Z</yellow> <grey>(Zulu Time)</grey>
  <aqua>-<aqua> <yellow>1671273097</yellow> <grey>(Unix Epoch Time in seconds)</grey>
invalid-past-time: <red>You can't specify a time in the past.
debug:
  arguments-invalid: <red>Provided arguments <yellow>{0}</yellow> invalid.
  sign-located: '<green>Sign valid: <yellow>{0}</yellow>.'
  operation-missing: <red>You must specific an operation.
  operation-invalid: <red>You must specific an valid operation.
  invalid-base64-encoded-sql: <red>The SQL provided must be base64 encoded.
  warning-sql: |-
    <red><bold>Warning:</bold></red><yellow>You're executing a SQL statement, this may corrupt your database or destroy any data in database even it belongs to other plugins.
    <red>Don't confirm if you don't trust who send you this command.
  warning-sql-confirm: <yellow>To confirm this dangerous operation, please type <aqua>/qs debug database sql confirm {0}</aqua> in 60 seconds.
  warning-sql-confirm-hover: <yellow>Click to confirm this dangerous operation.
  sql-confirm-not-found: <yellow>Didn't found the operation that you given, it may invalid or expired.
  sql-executing: '<yellow>Executing SQL statement: <aqua>{0}</aqua>'
  sql-completed: <green>Completed, {0} rows affected.
  sql-exception: <red>An error occurred while executing the SQL query, check the Console for details!
  sql-disabled: '<red>For security reason, SQL queries are disabled in this server, If you really need this feature, you can add the following flag to startup arguments: <aqua>{0}</aqua> with `true` value to enable it.'
  force-shop-reload: <yellow>Forcing reload all loaded shops...
  force-shop-reload-complete: <green>Forced <aqua>{0}</aqua> shops to be reloaded.
  force-shop-loader-reload: <yellow>Forcing shop-loader reloading...
  force-shop-loader-reload-unloading-shops: <yellow>Unloading <aqua>{0}</aqua> loaded shops...
  force-shop-loader-reload-unloading-shops-from-memory: <yellow>Removing <aqua>{0}</aqua> shops from memory...
  force-shop-loader-reload-reloading-shop-loader: <yellow>Re-calling shop-loader for reloading all shops from database...
  force-shop-loader-reload-complete: <green>Shop-loader has been reloaded all shops!
  toggle-shop-loaded-status: <aqua>Toggle this shop loaded status to <gold>{0}</gold>
  shop-internal-data: '<yellow>The internal data of this shop: <reset>{0}</reset>'
  handler-list-not-valid-bukkit-event-class: <red>The provided class <yellow>{0}</yellow> is not a valid Bukkit event class.
  update-player-shops-signs-no-username-given: <red>You must provide a valid player username.
  update-player-shops-signs-create-async-task: <yellow>Creating async tasks for force updating signs...
  update-player-shops-player-selected: '<yellow>Player selected: <gold>{0}</gold>'
  update-player-shops-player-shops: <yellow>Total <gold>{0}</gold> shops waiting for updates.
  update-player-shops-per-tick-threshold: '<yellow>Max shops can be update per tick: <gold>{0}</gold>'
  update-player-shops-complete: <green>Task completed, Used <yellow>{0}ms</yellow> for updating.
  update-player-shops-task-started: <gold>The tasks has been started, please wait for it to complete.
  item-info-store-as-string: "<green>The store you looking at: <gold>{0}</gold> Hash: <white>{1}</white>"
  item-info-hand-as-string: "<green>The item in your hand: <gold>{0}</gold> Hash: <white>{1}</white>"
  item-matching-result: "<green>Hand2Store: <aqua>{0}</aqua>, Store2Hand: <aqua>{1}</aqua>"
  hikari-cp-size-tweak: "<green>HikariCP MaximumPoolSize and MinimumIdle was set to <white>{0}</white>"
  hikari-cp-testing: "<green>Please wait, testing HikariCP connection..."
  hikari-cp-working: "<green>Pass! HikariCP working well!"
  hikari-cp-not-working: "<red>Failed! The connection that returned by HikariCP is dead! (Not passed test in 1 second)"
  hikari-cp-timeout: "<red>HikariCP is timed out while getting a valid connection, please clean up all active queries to release connection resources."
  queries-stopped: "<green>Stopped <white>{0}</white> active queries."
  queries-dumping: "<yellow>Dumping active queries..."
  restart-database-manager: "<yellow>Restarting SQLManager..."
  restart-database-manager-clear-executors: "<yellow>Clearing executors..."
  restart-database-manager-unfinished-task: "<yellow>Unfinished task: <white>{0}</white>"
  restart-database-manager-unfinished-task-history-query: "<yellow>Unfinished task (History Query): <white>{0}</white>"
  restart-database-manager-reconnect: "<yellow>Re-launching SQLManager via initial sequence (via async executor)"
  restart-database-manager-done: "<green>Done!"
  property-incorrect: "<yellow>You must enter a (and only one) property key=value set. E.g   aaa=bbb"
  property-security-block: "<red>Request was rejected, for security reason, you can only change the property that startsWith <aqua>com.ghostchu.quickshop</aqua> or <aqua>quickshop</aqua>."
  property-removed: "<green>Removed property key <white>{0}</white>"
  property-changed: "<green>Property key <white>{0}</white> was changed from <white>{1}</white> to <white>{2}</white>"
  marked-as-dirty: "<green>Marked all stores as dirty status, they will be force save in next auto-save task. (Restart the server to force execute store saving task)"
  display-removed: "<green>Successfully removed <yellow>{0}</yellow> QuickShop display items/entities from the worlds."
database:
  scanning: <green>Scanning the isolated data in QuickShop databases, the database load may increase in scan progress, this might need a while...
  scanning-async: <yellow>Scanning the isolated data in QuickShop databases on async task thread, the database load may increase in scan progress, this might need a while, try again later.
  already-scanning: <red>An scan task has been started, please wait until it finished.
  trim-warning: '<yellow><red><bold>Warning: </red>Backup your database before continue database trim to avoid the data loss. Once you get ready, execute <aqua>/qs database trim confirm</aqua> to continue.'
  status: '<yellow>Status: {0}'
  status-good: <green>Good
  status-bad: <yellow>Maintenance Required
  isolated: '<yellow>Isolated Data:'
  isolated-data-ids: '<yellow><aqua>└</aqua> Data Records: <gold>{0}</gold>'
  isolated-shop-ids: '<yellow><aqua>└</aqua> Shop Indexes: <gold>{0}</gold>'
  isolated-logs: '<yellow><aqua>└</aqua> Logs: <gold>{0}</gold>'
  isolated-external-caches: '<yellow><aqua>└</aqua> External Caches: <gold>{0}</gold>'
  last-purge-time: <yellow>Last time of trim at {0}
  report-time: <yellow>Last time of scan at {0}
  auto-scan-alert: <yellow>The QuickShop database on this server require a maintenance. Found <gold>{0}</gold> isolated data wait for trimming.
  auto-trim: <green>Auto trim has been enabled on this server, no manually trim required.
  trim-complete: <green>Database trim completed, <yellow>{0}</yellow> isolated trimmed.
  auto-trim-started: <green>Auto trim has been started, please wait...
  trim-start: <green>Database trim started, please wait...
  trim-exception: <red>Database trim failed, an exception raised during database trimming, check the server console.
  generated-at: '<yellow>Generated at: <gold>{0}</gold>'
  purge-date: <red>You must given a date.
  purge-warning: <yellow>This operation will purge the history that stored in your database including shop creates/changes/deletes, purchases, transactions and system logs. Delete those data can free the disk space but all history metric will lost, and other plugins that depend on metrics will stop working. To continue execute this operation, execute `/qs database purgelogs <before-days> confirm`
  purge-task-created: <green>Task created! Database are silent purging history records in background.
  purge-done-with-line: <green>Purge task completed, total <gold>{0}</gold> lines purged from database.
  purge-done-with-error: <red>Purge task failed, check the server console for details.
  purge-players-cache: <yellow>Please wait, purging players caches...
  purge-players-completed: |-
    <green>Successfully purged {0} players caches from both memory and database.<newline><aqua>Note: You server performance may impact by this operation.
  purge-players-error: <red>Failed to purge players caches, please check the server console.
  suggestion:
    trim: <yellow>This database require a isolated data trim, execute command <aqua>/qs database trim</aqua> to trim the database.
always-counting-removal-early-warning: <red>Always Counting feature is scheduled for removal, you shouldn't use it anymore and will stop work in future.
exporting-database: <green>Exporting database, please wait...
exporting-failed: <red>Failed to export database, please check the server console.
exported-database: <green>Database exported to <yellow>{0}</yellow>.
importing-not-found: <red>The <yellow>{0}</yellow> file not found, please check the file path.
importing-early-warning: |-
  <yellow><red><bold>Warning: </bold></red>The backup will be imported to the current database, any exists data will be purged and permanently lost if you don't have a backup.<newline><red>Are you sure to continue the import progress?</red><yellow>Type <aqua>/qs recovery confirm</aqua> to continue.
importing-database: <green>Importing database from backup, please wait...
importing-failed: <red>Failed to import database, please check the server console.
imported-database: <green>Database imported from <yellow>{0}</yellow>.
transfer-sent: <green>Shops transfer request sent to <yellow>{0}</yellow>.
transfer-request: <yellow>Player <aqua>{0}</aqua> requested to transfer their shops to you. Did you want accept the transfer?
transfer-single-request: <yellow>Player <aqua>{0}</aqua> would like to transfer a shop to you. Do you want to accept the request?
transfer-ask: |-
  <gold>Type <red>/qs transfer accept</red> to accept.</gold><newline><gold>Type <red>/qs transfer deny</red> to deny.</gold><newline><gold>Request will expire after <red>{0}</red> seconds.
transferall-ask: |-
  <gold>Type <red>/quickshop transferall accept</red> to accept or <red>/quickshop transferall deny</red> to deny.
  The request will expire after <red>{0}</red> seconds.
transfer-single-ask: |-
  <gold>Type <red>/quickshop transferownership accept</red> to accept or <red>/quickshop transferownership deny</red> to deny.
  The request will expire after <red>{0}</red> seconds.
transfer-accepted-fromside: <green>Player <aqua>{0}</aqua> accepted your shop transfer request.
transfer-accepted-toside: <green>You accepted <aqua>{0}</aqua>'s transfer request.
transfer-rejected-fromside: <red>Player <aqua>{0}</aqua> rejected your shop transfer request.
transfer-rejected-toside: <red>You rejected <aqua>{0}</aqua>'s shop transfer request.
transfer-no-pending-operation: <red>You don't have pending transfer request yet.
transfer-no-self: <red>You can't transfer your shops to yourself.
benefit-overflow: <red>The sum of all benefits cannot be greater than or equal to 100%.
benefit-exists: <red>Target player already in benefits list on this shop.
benefit-removed: <red>Target player has been removed from shop benefits.
benefit-added: <green>Player <aqua>{0}</aqua> has been added into shop benefits!
benefit-updated: <green>Player <aqua>{0}</aqua>'s benefits has been updated!
benefit-query: <green>This shop have <yellow>{0}</yellow> players in benefits list!
benefit-query-list: <yellow> - </yellow><white>Player <gold>{0}</gold>, Benefit <gold>{1}%</gold></white>
tag-added: '<green>Successfully added <aqua>#{0}</aqua> to this shop!'
tag-add-duplicate: '<red>The tag <aqua>#{0}</aqua> already exists at this shop!'
tag-removed: '<green>Successfully removed <aqua>#{0}</aqua> from this shop!'
tag-remove-not-exists: 'The tag <aqua>#{0}</aqua> not exists at this shop!'
tag-cleared: <green>Successfully cleared all tags from this shop!
tag-shops-cleared: '<green>Successfully cleared <aqua>#{0}</aqua> from all your tagged shops!'
tag-query: '<green>This shop have <yellow>{0}</yellow> tags:'
tag-query-listing: '<yellow> - <aqua>#{0}</aqua>'
tag-query-no-tag: <red>This shop have no tags.
tag-query-shops: '<green>This tag contains <yellow>{0}</yellow> shops:'
tag-query-shops-listing: <yellow> - <aqua>{0}</aqua>
batch-operations-based-on-tags-no-failure: <green>Successfully batch processed <yellow>{0}</yellow> shops.
batch-operations-based-on-tags-have-failure: <yellow>Total <yellow>{0}</yellow> shops in batch processing successfully completed, but <red>{1}</red> requests not completed.
batch-operations-based-on-tags-have-failure-with-reason: '<yellow>Total <yellow>{0}</yellow> shops in batch processing successfully completed, but <red>{1}</red> requests not completed. Reason: <gold>{2}</gold>'
batch-operations-based-on-tags-have-failure-error-messages-may-appears-above: <gray>The error messages will send to you above this message in the chat.
addon:
  towny:
    commands:
      town: <yellow>Set or unset a shop to a Town shop
      nation: <yellow>Set or unset a shop to a Nation shop
    make-shop-owned-by-town: <green>You have made the shop owned by town <yellow>{0}</yellow>.
    make-shop-no-longer-owned-by-town: <green>You have reset shop ownership, it now transfer back to original shop owner.
    make-shop-owned-by-nation: <green>You have made the shop owned by nation <yellow>{0}</yellow>.
    make-shop-no-longer-owned-by-nation: <green>You have reset shop ownership, it now transfer back to original shop owner.
    shop-owning-changing-notice: <grey>This shop now owned by a town/nation, original shop owner has been automatically added into administrator list, modify or add any new owner / staff by using command /qs permission
    target-shop-already-is-town-shop: <red>The target shop is already owned by a town.
    target-shop-already-is-nation-shop: <red>The target shop is already owned by a nation.
    target-shop-not-in-town-region: <red>The target shop is not in the town.
    target-shop-not-in-nation-region: <red>The target shop is not in the nation.
    item-not-allowed: <red>This shop's item are not allowed used by town/nation shop, pick another one!
    operation-disabled-due-shop-status: <red>This shop operation has been disabled due this already is a town/nation shop.
    plot-type-disallowed: <red>You can't create town/nation shop on this type of plot.
    flags:
      own: <red>You can only create shops at your owned shop type towny plot.
      modify: <red>You didn't have build permission on this towny plot.
      shop-type: <red>You must create shop on a shop type towny plot.
  residence:
    creation-flag-denied: <red>You don't have permission to create shops in this residence.
    trade-flag-denied: <red>You don't have permission to purchase in this residence.
    you-cannot-create-shop-in-wildness: <red>You can't create shop in wildness.
  griefprevention:
    creation-denied: <red>You don't have permission to create shops in this claim.
    trade-denied: <red>You don't have permission to purchase shops in this claim.
  lands:
    world-not-enabled: <red>You are not allowed to create or purchase in this world.
    creation-denied: <red>You don't have permission to create shops in this Land area.
  plotsquared:
    no-plot-whitelist-creation: <red>You cannot create shop outside of Plot.
    no-plot-whitelist-trade: <red>You cannot purchase shop outside of Plot.
    creation-denied: <red>You don't have permission to create shops in this Plot.
    trade-denied: <red>You don't have permission to purchase shops in this Plot.
    flag:
      create: Create QuickShop-Hikari shops
      trade: Purchase QuickShop-Hikari shops
  superiorskyblock:
    owner-create-only: <red>Only island owner can create shop there.
    owner-member-create-only: <red>Only island owner or member can create shop there.
  worldguard:
    creation-flag-test-failed: <red>You don't have permission create shop in this WorldGuard area.
    trade-flag-test-failed: <red>You don't have permission trade shop in this WorldGuard area.
    reached-per-region-amount-limit: "<red>You have reached the maximum amount of shops in this region."
  discord:
    __to_translators: 'To translators: `addon.discord.discord-messages` use a special variable in strings, %%LANG_KEY%% instead of QuickShop''s style {LANG_INDEX}, do not translate anything inside double ''%'' section. BUT for any other section texts, it respect the normal QuickShop Text System syntax ({0},{1} etc.) This may be difficult to understand because two different variable forms are used in the same program at the same time, but trust me, it is reasonable. (you don''t need translate this string, it''s a comment)'
    __to_users: Like the QuickShop Text System, Discord addon will also auto-detect user language and use user's language to send Discord message, it following the QuickShop-Hikari's language system settings.
    __to_message_designer: 'Design your Discord message with GUI: https://glitchii.github.io/embedbuilder/, then copy the JSON code and paste it into the translation and here we go!'
    discord-enabled: <aqua>Successfully <green>enabled</green> your QuickShop discord messages, now you can receive the shop messages from Discord.
    discord-disabled: <aqua>Successfully <red>disabled</red> your QuickShop discord messages, now you won't receive the shop messages from Discord anymore.
    discord-not-integrated: <red>You hadn't link your Discord yet! Please link your Discord account first!
    feature-enabled-for-user: <aqua>You have <green>enabled</green> <gold>{0}</gold> notification.
    feature-disabled-for-user: <aqua>You have <red>disabled</red> <gold>{0}</gold> notification.
    link-help: <yellow>This server using <gold>{0}</gold> for Discord driver, please use <green>{0}</green> to link your Discord account.
    save-notifaction-exception: <red>An error occurred while saving your Discord notifaction settings, please contact the server administrator.
    feature-status-changed: <green>Successfully set the notifaction <aqua>{0}</aqua> status to <gold>{1}</gold>
    commands:
      discord:
        description: <yellow>Managing your QuickShop Discord settings
    discord-messages:
      sold-to-your-shop: |
        {
         "embed":
           {
             "title": ":inbox_tray: Somebody sold items to your shop",
             "description": "The player %%purchase.name%% sold x%%purchase.amount%% %%shop.item.name%% to your shop.",
             "color": 52084,
             "author": {
               "name": "QuickShop-Hikari",
               "icon_url": ""
             },
             "thumbnail": {
               "url": ""
             },
             "image": {
               "url": ""
             },
             "footer": {
               "text": "QuickShop-Hikari Discord Notification",
               "icon_url": ""
             },
             "fields": [
               {
                 "name": "Shop",
                 "value": "%%shop.display-name%%",
                 "inline": false
               },
               {
                 "name": "Purchaser",
                 "value": "%%purchase.name%%",
                 "inline": true
               },
               {
                 "name": "Item",
                 "value": "%%shop.item.name%%",
                 "inline": true
               },
               {
                 "name": "Amount",
                 "value": "%%purchase.amount%% (x%%shop.stacking-amount%%)",
                 "inline": true
               },
               {
                 "name": "Your paid",
                 "value": "%%purchase.balance-formatted%%",
                 "inline": true
               },
               {
                 "name": "Taxes",
                 "value": "%%purchase.taxes-formatted%%",
                 "inline": true
               }
             ]
           }
        }
      bought-from-your-shop: |
        {
           "embed":
             {
               "title": ":outbox_tray: Somebody bought items from shop",
               "description": "The player %%purchase.name%% bought x%%purchase.amount%% %%shop.item.name%% from your shop.",
               "color": 52084,
               "author": {
                   "name": "QuickShop-Hikari",
                   "icon_url": ""
               },
               "thumbnail": {
                   "url": ""
               },
               "image": {
                   "url": ""
               },
               "footer": {
                   "text": "QuickShop-Hikari Discord Notification",
                   "icon_url": ""
               },
               "fields": [
                   {
                       "name": "Shop",
                       "value": "%%shop.display-name%%",
                       "inline": false
                   },
                   {
                       "name": "Purchaser",
                       "value": "%%purchase.name%%",
                       "inline": true
                   },
                   {
                       "name": "Item",
                       "value": "%%shop.item.name%%",
                       "inline": true
                   },
                   {
                       "name": "Amount",
                       "value": "%%purchase.amount%%(x%%shop.stacking-amount%%)",
                       "inline": true
                   },
                   {
                       "name": "Your earn",
                       "value": "%%purchase.balance-formatted%%",
                       "inline": true
                   },
                   {
                       "name": "Taxes",
                       "value": "%%purchase.taxes-formatted%%",
                       "inline": true
                   }
               ]
           }
         }
      mod-shop-purchase: |
        {
        "embed":
          {
              "title": ":dollar: Somebody purchased in a shop",
              "description": "The player %%purchase.name%% purchase x%%purchase.amount%% %%shop.item.name%% from the shop.",
              "color": 6061450,
              "author": {
                  "name": "QuickShop-Hikari",
                  "icon_url": ""
              },
              "thumbnail": {
                  "url": ""
              },
              "image": {
                  "url": ""
              },
              "footer": {
                  "text": "QuickShop-Hikari Discord Notification",
                  "icon_url": ""
              },
              "fields": [
                  {
                      "name": "Shop",
                      "value": "%%shop.display-name%%",
                      "inline": false
                  },
                  {
                      "name": "Purchaser",
                      "value": "%%purchase.name%%",
                      "inline": true
                  },
                  {
                      "name": "Item",
                      "value": "%%shop.item.name%%",
                      "inline": true
                  },
                  {
                      "name": "Amount",
                      "value": "%%purchase.amount%% (x%%shop.stacking-amount%%)",
                      "inline": true
                  },
                  {
                      "name": "Balance",
                      "value": "%%purchase.balance-formatted%%",
                      "inline": true
                  },
                  {
                      "name": "Taxes",
                      "value": "%%purchase.taxes-formatted%%",
                      "inline": true
                  }
              ]
          }
        }
      out-of-space: |
        {
        "embed":
          {
              "title": ":mailbox_with_mail: Your shop has run out of space",
              "description": "Your shop inventory is full of items!\nYou need empty your shop container to free the space then you can continue to accept new items.",
              "color": 16065893,
              "author": {
                  "name": "QuickShop-Hikari",
                  "icon_url": ""
              },
              "thumbnail": {
                  "url": ""
              },
              "image": {
                  "url": ""
              },
              "footer": {
                  "text": "QuickShop-Hikari Discord Notification",
                  "icon_url": ""
              },
              "fields": [
                  {
                      "name": "Shop",
                      "value": "%%shop.display-name%%",
                      "inline": false
                  }
              ]
          }
        }
      out-of-stock: |
        {
            "embed":
                {
                    "title": ":mailbox_with_no_mail: Your shop has run out of stock",
                    "description": "Your shop inventory is empty now!\nYou need to refill the shop container with items to continue selling items!",
                    "color": 16065893,
                    "author": {
                        "name": "QuickShop-Hikari",
                        "icon_url": ""
                    },
                    "thumbnail": {
                        "url": ""
                    },
                    "image": {
                        "url": ""
                    },
                    "footer": {
                        "text": "QuickShop-Hikari Discord Notification",
                        "icon_url": ""
                    },
                    "fields": [
                        {
                            "name": "Shop",
                            "value": "%%shop.display-name%%",
                            "inline": false
                        }
                    ]
                }
        }
      mod-shop-created: |
        {
          "embed": {
            "title": ":classical_building: A new shop has been created",
            "description": "Player created a new shop on your server!",
            "color": 6061450,
            "author": {
              "name": "QuickShop-Hikari",
              "icon_url": ""
            },
            "thumbnail": {
              "url": ""
            },
            "image": {
              "url": ""
            },
            "footer": {
              "text": "QuickShop-Hikari Discord Notification",
              "icon_url": ""
            },
            "fields": [
              {
                "name": "Shop",
                "value": "%%shop.display-name%%",
                "inline": false
              },
              {
                "name": "Owner",
                "value": "%%shop.owner.name%%",
                "inline": true
              },
              {
                "name": "Item",
                "value": "%%shop.item.name%%",
                "inline": true
              },
              {
                "name": "Amount",
                "value": "%%shop.stacking-amount%%",
                "inline": true
              },
              {
                "name": "Type",
                "value": "%%shop.type%%",
                "inline": true
              }
            ]
          }
        }
      mod-remove-shop: |
        {
            "embed": {
                "title": ":recycle: A shop was removed from this server",
                "description": "A shop was removed from this server.",
                "color": 6061450,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "Reason",
                        "value": "%%delete.reason%%",
                        "inline": false
                    }
                ]
            }
        }
      shop-transfer-to-you: |
        {
            "embed": {
                "title": ":placard: A shop was transfer to you",
                "description": "A shop transfer to you from another player.",
                "color": 16172079,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "From",
                        "value": "%%transfer.from%%",
                        "inline": false
                    }
                ]
            }
        }
      mod-shop-transfer: |
        {
            "embed": {
                "title": ":placard: A shop was transferred",
                "description": "A shop transfer to a player from another player.",
                "color": 6061450,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "From",
                        "value": "%%transfer.from%%",
                        "inline": true
                    },
                    {
                        "name": "To",
                        "value": "%%transfer.to%%",
                        "inline": true
                    }
                ]
            }
        }
      shop-price-changed: |
        {
            "embed": {
                "title": ":money_with_wings: Your shop price was changed",
                "description": "You or your shop staff changed your shop price.",
                "color": 16172079,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "From",
                        "value": "%%change-price.from%%",
                        "inline": true
                    },
                    {
                        "name": "To",
                        "value": "%%change-price.to%%",
                        "inline": true
                    }
                ]
            }
        }
      mod-shop-price-changed: |
        {
            "embed": {
                "title": ":money_with_wings: A shop price has been changed",
                "description": "A shop changed its price settings.",
                "color": 6061450,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": true
                    },
                    {
                        "name": "Owner",
                        "value": "%%shop.owner.name%%",
                        "inline": true
                    },
                    {
                        "name": "",
                        "value": "",
                        "inline": true
                    },
                    {
                        "name": "From",
                        "value": "%%change-price.from%%",
                        "inline": true
                    },
                    {
                        "name": "To",
                        "value": "%%change-price.to%%",
                        "inline": true
                    }
                ]
            }
        }
      shop-permission-changed: |
        {
            "embed": {
                "title": ":closed_lock_with_key: Your shop permission settings has been changed",
                "description": "One of your shop's permission setting has been changed.",
                "color": 15879747,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "Player",
                        "value": "%%change-permission.player%%",
                        "inline": true
                    },
                    {
                        "name": "Assign to group",
                        "value": "%%change-permission.to-group%%",
                        "inline": true
                    },
                    {
                        "name": "",
                        "value": "",
                        "inline": true
                    },
                    {
                        "name": "Permission granted (Inherit from group)",
                        "value": "```\n%%change-permission.perms-list%%\n```",
                        "inline": false
                    }
                ]
            }
        }
    field:
      player: Player
      item: Item
      amount: Amount
      balance: Balance
      balance-after-tax: Balance (after tax)
      account: Your account balance
      taxes: Taxes
      cost: Cost
  discount:
    commands:
      discount:
        description: <yellow>Apply a discount code or manage your discount codes.
    tab-complete:
      discount:
        general:
          code: <code>
        create:
          rate: |
            Command Hint:
            Argument: <rate>
            Description: The actual percentage or money you will earn
            Input `30%` = price * 0.3
            Input `50` = price - 50
          max-usage: |
            Command Hint:
            Argument: [max-usage]
            Description: The times that code can be used
            `-1` for unlimited.
          threshold: |
            Command Hint:
            Argument: [threshold]
            Description: The minimum price that code can be applied
            `-1` for unlimited
          expired: |
            Command Hint
            Argument: [expired]
            Description: The code expired time.
            `-1` for unlimited duration.
            Accept both Zulu time and UNIX timestamp in seconds.
            Zulu Example: 2022-12-17T10:31:37Z
            UNIX Example: 1671273097
    discount-code-already-exists: <red>Sorry, the name of your discount code already in use.
    invalid-discount-code-regex: '<red>The discount code must matches the regex: <yellow>{0}</yellow>'
    invalid-discount-code: <red>This discount code are invalid.
    discount-code-added: <green>Your discount code <yellow>{0}</yellow> has been added into shop <aqua>{1}</aqua>
    discount-code-removed: <green>Your discount code <yellow>{0}</yellow> has been removed from shop <aqua>{1}</aqua>
    invalid-code-type: <red>The code type <yellow>{0}</yellow> are invalid.
    invalid-usage-restriction: <red>The usage restriction <yellow>{0}</yellow> are invalid.
    invalid-threshold-restriction: <red>The threshold restriction <yellow>{0}</yellow> are invalid.
    invalid-effect-scope: <red>The scope <yellow>{0}</yellow> are invalid.
    invalid-expire-time: <red>You can't specify a time in the past.
    invalid-discount-rate: <red>The discount rate <yellow>{0}</yellow> are invalid, it can be a fixed number or percentage.
    discount-code-expired: <red>Ouch! Your discount code <yellow>{0}</yellow> has been expired!
    discount-code-installed: <green>You have installed discount code <gold>{0}</gold>, the discount code will be automatically applied to the available purchase in all the following purchases during this session. To uninstall discount code, execute <aqua>/qs discount uninstall {0}</aqua>.
    discount-code-uninstalled: <green>You have uninstalled your discount code.
    discount-code-query-nothing: <red>You hadn't install a discount code yet!
    discount-code-query: '<green>You are using discount code: <yellow>{0}</yellow>.'
    discount-code-applicable: '<color:#bcef26>Your discount code <yellow><bold>{0}</bold></yellow> are <bold>applicable</bold> in this shop!'
    discount-code-applicable-with-threshold: '<color:#bcef26>Your discount code <yellow><bold>{0}</bold></yellow> are <bold>applicable</bold> in this shop, but only your purchase cost over <yellow>{1}</yellow> in single transaction.'
    discount-code-not-applicable: <red>Your discount code <yellow><bold>{0}</bold></yellow> are <bold>not applicable</bold> in this shop!
    discount-code-reach-the-limit: <red>You have reach the usage limit of discount code <yellow><bold>{0}</bold></yellow>, discount are not applied.
    discount-code-no-permission: <red>You didn't have permission to use any discount code in this shop!
    discount-code-has-been-expired: <red>Your discount has been expired!
    discount-code-config-shop-added: <green>Successfully to add this shop into your discount code allow list.
    discount-code-config-shop-add-failure: <red>This shop already in your discount code allow list.
    discount-code-config-shop-removed: <green>Successfully to remove this shop from your discount code allow list.
    discount-code-config-shop-remove-failure: <red>This shop not exists in your discount code allow list.
    discount-code-config-expire: <green>Successfully changed your code expire time.
    discount-code-config-applied: <green>Config the discount code successfully!
    discount-code-created-successfully: |
      <green>Your discount code <yellow>{0}</yellow> has been successfully created!
      <gold>Scope: <aqua>{1}</aqua></gold>.
      <gold>Share with others: <color:#bcef26>{2}</color:#bcef26></gold>.
      <yellow>For <gray>Specific Shops</gray> scope only: To add shops to your discount code allow list, please looking an quickshop and execute <aqua>{3}</aqua> command.
      <yellow>You can use <aqua>/qs discount config {0}</aqua> to edit your discount code anytime.
    discount-code-under-threshold: <red>The discount not applied to your purchase because the total value under the discount code threshold <yellow>{0}</yellow>.
    percentage-off: '<bold><color:#bcef26>-{0}%</color:#bcef26></bold>'
    fixed-off: '<bold><color:#bcef26>-{0}</color:#bcef26></bold>'
    discount-code-list: '<gold>Listing your discount codes:</gold>'
    discount-code-applied-in-purchase: '<color:#bcef26>Your discount code <yellow>{0}</yellow> has been applied to this purchase and you saved <gold>{1}</gold>!'
    scope:
      this-shop: '{0}'
      your-shops-owned: all your shops (owned)
      your-shops-managed: all your shops (managed)
      server: whole server
    code-type:
      SERVER_ALL_SHOPS: All shops on this server
      PLAYER_ALL_SHOPS: All shops that created by code owner
      SPECIFIC_SHOPS: Specific shops
    discount-code-details: |-
      <gold>Code: <yellow>{0}</yellow>
      <gold>Creator: <yellow>{1}</yellow>
      <gold>Applied to: <yellow>{2}</yellow>
      <gold>Remaining usage: <yellow>{3}</yellow>
      <gold>Expired on: <yellow>{4}</yellow>
      <gold>Threshold: <yellow>{5}</yellow>
      <gold>Discount: <yellow>{6}</yellow>
  list:
    commands:
      list: <yellow>Listing all shops owned by your or specific player
    table-prefix: '<yellow><green>{0}</green>''s shops <gray>(Total {1})</gray>: '
    table-prefix-pageable: '<yellow><green>{0}</green>''s shops <gray>(Page {1}/{2})</gray>: '
    entry: "<hover:show_text:'<yellow>{1}</yellow>\n<gray>{2} {3},{4},{5}</gray>\n<aqua>Price <yellow>{6}</yellow> - <light_purple>x{7}</light_purple> <yellow>{8}</yellow>\n<green>{9}'><yellow>{0}. <aqua>{1}</aqua></hover>"
  shopitemonly:
    message: <red>You can't put non-shop items into shop container, all non-shop items will dropped at your location.
  limited:
    commands:
      limit: <yellow>Set a limit that restrict the purchase that player can make in period
    titles:
      title: <green>Purchase Success
      subtitle: <aqua>You can also purchase <gold>{0}</gold> more in this shop
    reach-the-limit: <red>You have reached the limit in this shop. You can also purchase <green>{0}</green> items, but you trying purchase <yellow>{1}</yellow> items.
    success-reset: <green>Reset the restriction of this shop successfully
    success-remove: <green>Successfully remove the all restriction of this shop
    success-setup: <green>Restriction settings for this shop were saved successfully
    trade-limit-reached-cancel-reason: <red>Reached the restriction of this shop
    remains-limits: '<gold>Your remaining purchase quota in this shop: <yellow>{0}</yellow>'
    remains-limits-before-reset-period-note: '<gold>This shop will reset their purchase quota every: <yellow>{0}</yellow>'
  dynmap:
    markerset-title: QuickShop-Hikari Shops
    marker-name: '{0} @ {2} @ {3} for x{4} @ {5}'
    marker-description: |
      Name: {0}
      Owner: {1}
      Item: {2}
      Price: {3} for x{4} item(s)
      Type: {5}
      Unlimited: {6}
      Location: {7}
  bluemap:
    markerset-title: QuickShop-Hikari Shops
    marker-name: '{0} @ {2} @ {3} for x{4} @ {5}'
    marker-description: |
      Name: {0}
      Owner: {1}
      Item: {2}
      Price: {3} for x{4} item(s)
      Type: {5}
      Unlimited: {6}
      Location: {7}
  chestprotect:
    protection-exists: <red>This region already protected by ChestProtect and you don't have permission to create shops at here.
    shops-exsts: <red>The region that you want to protect contains other player's shops and you don't have permission to access them.
  displaycontrol:
    toggle: |-
      <green>Successfully toggle your QuickShop display showcasing to <aqua>{0}</aqua>.
      <yellow>You may need re-join to take effect.
    toggle-exception: <red>Failed to toggle your display showcase settings due an internal error, please contact server administrator.
    command:
      displaycontrol: <yellow>Toggle your QuickShop display showcasing
  reremake-migrator:
    commands:
      migratefromreremake: Migrate QuickShop-Reremake's data to QuickShop-Hikari
    server-not-empty: "<red>No players are allowed to be online on the server during the conversion progress, please enable server whitelist or maintenance mode."
    starting-convert-progress: "<gold>Starting the convert progress, <red>DO NOT SHUTDOWN YOUR SERVER!</red>"
    executing: "<gold>Executing migrate component <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
    completed: "<green>Done! Migrate completed, please restart your server and remove QuickShop-Reremake from plugins directory."
    join_blocking_converting: "<red>[QuickShop-Hikari Migrator] This server is performing a data conversion and you cannot join the server at this time, try again later!"
    join_blocking_finished: "<red>[QuickShop-Hikari Migrator] This server has just completed a data conversion and is waiting for a reboot to apply the changes, try again later!"
    failed: "<red>Migration progress exited with error, please check the server console."
    modules:
      config:
        copy-values: "<yellow>Copying values (total {0} entries)..."
        copying-value: "<gray> - Copying <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        migrate-price-restriction: "<yellow>Migrating price-restriction related settings..."
        migrate-price-restriction-entry: "<gray> - Migrating <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
      shop:
        start-migrate: "<yellow>Migrating shops (total {0} entries)..."
        migrate-entry: "<gray> - Migrating <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        unloading-reremake: "<gold>Unloading Reremake to avoid data overwrite..."
        register-entry: "<gray> - Registering <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        save-entry: "<gray> - Saving <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        saving-shops: "<yellow>Saving <gold>{0}</gold> shops, this may need a while™ (the time based on shops amounts)..."
        conflict: "<gray> - CONFLICT > Detects a conflict between a Reremake store and an existing Hikari store location, taking a predefined behavior: <dark_gray>{0}</dark_gray>"
      translation:
        start-migrate: "<yellow>Migrating translation files...</yellow>"
        copy-values: "<yellow>Copying values in locale <gold>{0}</gold> (total {1} entries)..."
        migrate-entry: "<gray> - Migrating <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        copying-value: "<gray> - Copying <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
      shop-logs:
        start-migrate: "<yellow>Migrating shop logs (details in console), please wait...</yellow>"
        extract-history-files: "<gray>Please wait for us to unzip and append the history logs..."
        filter-history-files: "<gray>Please wait for us to filter the history log..."
        filtered-history-files: "<gray>Filtered {0} lines out of queue."
        import-entry: "<gray> - Migrating <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
compat:
  advancedchests:
    created: <green>You're successfully created a shop on AdvancedChests container!
    permission-denied: <red>Sorry! You don't have the permission to create a shop on a AdvancedChests container!
suite:
  dynmap:
    title: '{0} - {1} - {2}'
    description: |-
      Item: {0}
      Owner: {1}
      Type: {2} {3}
      Price: {4}
      Location: {5} {6}, {7}, {8}
      Space: {9}
      Stock: {10}
  limited:
    command-description: <yellow>Set a limit that restrict the purchase that player can make in period.
    reach-the-quota-limit: <red>You have reach the purchase limit in this shop ({0}/{1}).
    quota-reset-countdown: <yellow>The quota in this shop will reset in {0}.
    quota-reset-player-successfully: <green>The quota for player {0} in this shop has been reset successfully.
    quota-reset-everybody-successfully: <green>The quota for everyone in this shop has been reset successfully.
    quota-setup: <green>The purchase restriction now apply to this shop!
    quota-remove: <green>The purchase restriction now removed from this shop!
    subtitles:
      title: <green>Purchase Success
      subtitle: <aqua>You can purchase <yellow>{0}</yellow> more in this shop
  list:
    command-description: <yellow>List all shops owned by yourself or another player.
    table-prefix: <yellow>You owned total <aqua>{0}</aqua> shops on this server.
    entry-syntax: <aqua>- <yellow>{0}
    entry-syntax-fallback-name: '<yellow>Item:{0} X:{1}, Y:{2}, Z:{3}, World: {4}'
    hover-text: |-
      <yellow>{0}
      <gray>{1} {2}, {3}, {4}
      <aqua>{5} - <yellow>{6}
      <yellow>{7} items each stack.
  shopitemonly:
    message: <red>You can't put non-shop items into shop container, all non-shop items will dropped at your location.
compatibility:
  elitemobs:
    soulbound-disallowed: You can't trade item which have EliteMobs Soulbound enchantment.
internet-paste-forbidden-privacy-reason: "<red>Failed! According your privacy setting, QuickShop-Hikari are not allowed upload your paste to Internet, turn on DIAGNOSTIC permission in privacy setting in config.yml or use <aqua>/quickshop paste --file</aqua> instead."
no-sign-type-given: "<red>You need give a sign material, the sign materials that available on this server: {0}"
sign-type-invalid: "<red>The type <yellow>{0}</yellow> not a valid sign material."
delete-controlpanel-button-confirm: "<red>Do you really want remove this shop? Click the <bold>[Remove Shop]</bold> button again in {0} seconds to confirm."
cannot-suggest-price: "<red>Sorry, For the time being, no more people are trading the same item as you and there is not enough data to generate a suggested price."
price-suggest: "<green>Based on data from <aqua>{0}</aqua> stores, the highest priced store is priced at <light_purple>{1}</light_purple>, the lowest priced store is priced at <light_purple>{2}</light_purple>, the average price is <light_purple>{3}</light_purple>, and the median price is <light_purple>{4}</light_purple>. <newline><yellow>It's recommended that you set your price around <gold>{5}</gold>.</yellow>"
suggest-wait: "<green>Please wait... Calculating recommend price."
history:
  shop:
    gui-title: "View purchase history"
    header-icon-multiple-shop: "<white>The query result across {0} shops</white>"
    header-icon-description:
      - "<white>Type: <yellow>{0}</yellow></white>"
      - "<white>Owner: <yellow>{1}</yellow></white>"
      - "<white>Item: <yellow>{2}</yellow></white>"
      - "<white>Price: <yellow>{3} <aqua>x{4}</aqua></yellow></white>"
      - "<white>Location: <yellow>{5}</yellow></white>"
    log-icon-title: "<reset><green>Time: {0}</green>"
    log-icon-description:
      - "<white>Purchaser: <yellow>{0}</yellow></white>"
      - "<white>Item: <yellow>{1} <aqua>x{2}</aqua></yellow></white>"
      - "<white>Balance: <yellow>{3}</yellow></white>"
      - "<white>Tax: <yellow>{4}</yellow></white>"
    log-icon-description-with-store-name:
      - "<white>Store: <yellow>{0}</yellow></white>"
      - "<white>Purchaser: <yellow>{1}</yellow></white>"
      - "<white>Item: <yellow>{2} <aqua>x{3}</aqua></yellow></white>"
      - "<white>Balance: <yellow>{4}</yellow></white>"
      - "<white>Tax: <yellow>{5}</yellow></white>"
    query-icon: "<gray>Please wait, querying...</gray>"
    previous-page: "<white><< Previous Page</white>"
    next-page: "<white>Next Page >></white>"
    current-page: "<white>Page {0}</white>"
    summary-icon-title: "<green>Shop Summary"
    recent-purchases: "<white>Recent <aqua>{0}</aqua> purchases: <yellow>{1}</yellow></white>"
    recent-purchase-balance: "<white>Recent <aqua>{0}</aqua> turnover: <yellow>{1}</yellow></white>"
    total-purchases: "<white>Total purchases: <yellow>{0}</yellow></white>"
    total-balances: "<white>Total turnover: <yellow>{0}</yellow></white>"
    total-unique-purchasers: "<white>Total unique purchasers: <yellow>{0}</yellow></white>"
    top-n-valuable-customers-title: "<gold>Top {0} valuable customers</gold>"
    top-n-valuable-customers-entry: "<white>- <yellow>{0}</yellow> <aqua>{1}</aqua></white>"
    no-result: "<white>No result</white>"
about:
  text:
    - "<b><aqua>QuickShop <yellow>{0}</yellow></aqua></b>"
    - "<aqua>Version <yellow>>></yellow> <green>{1}</green></aqua>"
    - "<aqua>Release <yellow>>></yellow> <green>{2}</green></aqua>"
    - "<aqua>Developers <yellow>>></yellow> <color:#218bff><hover:show_text:'<yellow>Click to check contributors'><click:open_url:'https://github.com/Ghost-chu/QuickShop-Hikari/graphs/contributors'>[View contributors on GitHub]</click></hover></color></aqua>"
    - "<aqua>Localized members <gold>({4})</gold>: <yellow>>></yellow> <green>{5}</green></aqua>"
    - "  <yellow>+ <color:#43A047><hover:show_text:'<yellow>Click to open Crowdin translate page'><click:open_url:'https://crowdin.com/project/quickshop-hikari'>[Help translate on Crowdin]</click></hover></color></yellow>"
    - "<aqua>Donation Key <yellow>>></yellow> <green>{6}</green></aqua>"
    - "<gold>Powered by community</gold> <red>Made with ❤</red>"
  valid-donation-key: "<color:#00AFF1>Bound to <color:#F9C23C>{0}</color></color>"
  invalid-donation-key: "<gray>Invalid donation key</gra>"
  kofi-thanks: "<gold>Special thanks to those who support QuickShop on Ko-fi :)</gold>"
history-command-leave-blank: "<Leave blank for seeing store>"
shop-information-not-shown-due-an-internal-error: "<red>An internal error occurred. The store information panel may be displayed incompletely, please contact the server administrator."
