break-shop-use-supertool: <yellow>Obchod můžete rozbít pomocí super nástroje.
fee-charged-for-price-change: <green>Zaplatili jste <red>{0}</red> za změnu ceny.
not-allowed-to-create: <red>Zde nemůžete vytvořit obchod.
disabled-in-this-world: <red>Obchod je v tomto světě zakázán
how-much-to-trade-for: <green>Zadejte v chatu, kolik chcete obchodovat <yellow>{1}krát {0}</yellow>.
client-language-changed: <green><PERSON><PERSON><PERSON> z<PERSON>, že nastavení jazyka vašeho klienta bylo <PERSON>, nyní pro Vás používáme {0} jazyků.
shops-backingup: Vytváření zálohy obchodu z databáze...
_comment: Ahoj překladatel! Pokud to upravujete na GitHubu nebo pomocí zdrojo<PERSON><PERSON><PERSON> kódu, měli byste místo toho jít na https://crowdin.com/project/quickshop-hikari.
unlimited-shop-owner-changed: <yellow>Majitel tohoto neomezeného obchodu byl změněn na {0}.
bad-command-usage-detailed: '<red>Špatné parametry příkazů! Povoleny jsou následující parametry: <gray>{0}'
thats-not-a-number: <red>Neplatné číslo
shop-name-disallowed: <red>Název obchodu <yellow>{0}</yellow> je zakázán. Vyberte jiný!
console-only-danger: <red>Tento příkaz není bezpečný, můžete ho použít pouze v konzoly.
not-a-number: <red>Můžete zadat pouze číslo, váš vstup byl {0}.
not-looking-at-valid-shop-block: <red>Nelze najít blok pro vytvoření obchodu. Musíte se na něj dívat.
shop-removed-cause-ongoing-fee: <red>Váš obchod v {0} byl odstraněn, protože nemáte dostatek peněz na jeho udržení!
tabcomplete:
  amount: '[amount]'
  item: '[item]'
  price: '[price]'
  name: '[name]'
  range: '[range]'
  currency: '[currency name]'
  percentage: '[percentage%]'
taxaccount-unset: <green>Tento účet obchodu nyní následuje globální nastavení serveru.
blacklisted-item: <red>Nemůžete prodávat tuto položku, protože je mezi zakázanými
command-type-mismatch: <red>Tento příkaz může být proveden pouze uživatelem <aqua>{0}</aqua>.
server-crash-warning: '<red>VAROVÁNÍ: Použití příkazu /qs pro nahrazení nebo smazání souboru pluginu může způsobit pád serveru.'
you-cant-afford-to-change-price: <red>Cena za změnu ceny ve tvém obchodě je {0}.
safe-mode: <red>Plugin je nyní v bezpečném režimu, nemůžete otevřít tento obchod, prosím kontaktujte správce serveru.
forbidden-vanilla-behavior: <red>Tato operace není povolena, protože není v souladu se základním chováním hry
shop-out-of-space-name: <dark_purple>Váš obchod {0} je plný!
paste-disabled: |-
  <red>Vložení funkce bylo zakázáno! Nemůžete požádat o technickou podporu.<newline>Důvod: {0}
quick-fill:
  entry-own: ' <yellow>- <red>[own] <aqua>{0} <light_purple>{1}'
  hover:
    - '<yellow>Název: <aqua>{0}'
    - '<yellow>vlastník: <aqua>{0}'
    - '<yellow>Type: <aqua>{0}'
    - '<yellow>Price: <aqua>{0}'
    - '<yellow>Položka: <aqua>{0}'
    - '<yellow>Location: <aqua>{0}'
  hover-arg-filled:
    - '<yellow>Název: <aqua>{name}'
    - '<yellow>Vlastník: <aqua>{owner}'
    - '<yellow>Typ: <aqua>{type}'
    - '<yellow>Cena: <aqua>{price}'
    - '<yellow>Položka: <aqua>{item}'
    - '<yellow>Lokace:<aqua>{location}'
  header: '<yellow>Máte více obchodů s názvem "<green>{0}</green>", vyberte jeden pro pokračování:'
  entry-normal: ' <yellow>- <aqua>{0} <light_purple>{1}'
  entry-cooperation: ' <yellow>- <red>[co] <aqua>{0} <light_purple>{1}'
3rd-plugin-build-check-failed-admin: '<gray>(Pouze admini) <light_purple>{0} <dark_gray>zamítl kontrolu oprávnění, pokud to není výjimka, zkuste přidat <light_purple>{1} <gray>na blacklist. <gray>Konfigurace průvodce: https://github.com/Ghost-chu/QuickShop-Hikari/wiki/Use-protection-listener-filter'
average-price-nearby: '<green>Okolní průměrná cena: <yellow>{0}'
inventory-check-global-alert: "<red>[Inventory Check] <gray>Warning! Found a QuickShop display item <gold>{2}</gold> in inventory at <aqua>{0}</aqua> <dark_gray>[{1}]</dark_gray>, which it shouldn't happened, This usually means that someone is maliciously exploiting the exploit to duplicating the display item."
digits-reach-the-limit: <red>Dosáhli jste limitu desetinných míst v ceně.
currency-unset: <green>Měna Shopu byla úspěšně odstraněna. Nyní se používá výchozí nastavení.
you-cant-create-shop-in-there: <red>Nemáte oprávnění k vytvoření obchodu v tomto umístění.
no-pending-action: <red>Nemáte žádné čekající akce
refill-success: <green>Doplnění úspěšně proběhlo
failed-to-paste: <red>Nepodařilo se nahrát data do schránky. Zkontrolujte připojení k internetu a zkuste to znovu. (Podrobnosti naleznete v konzoli)
shop-out-of-stock-name: <dark_purple>v obchodě {0} došel {1}!
shop-name-invalid: <red>Název obchodu <yellow>{0}</yellow> je neplatný. Vyberte jiný!
how-many-buy-stack: <yellow>Zadejte v chatu, kolik kusů si přejete <aqua>KOUPIT<green>. V každé hromadné zásilce jsou <yellow>{0}<green> položky a můžete si koupit <yellow>{1}<green> kusů. Chcete-li koupit všechny, zadejte <aqua>{2}<green> do chatu.
exceeded-maximum: <red>Hodnota překročila maximální hodnotu v Javě.
unlimited-shop-owner-keeped: '<yellow>Upozornění: Vlastník obchodu je stále neomezený vlastník obchodu, musíte si nastavit nového vlastníka obchodu sami.'
no-enough-money-to-keep-shops: <red>Nemáš dostatek peněz na zaplacení svých obchodů! Všechny vaše obchody byly odstraněny...
3rd-plugin-build-check-failed: <red>Plugin 3. strany <bold>{0}</bold> odmítl kontrolu oprávnění, máte správně nastavená oprávnění?
not-a-integer: <red>Musíte zadat celé číslo, zadal jsi {0}.
translation-country: 'Jazyková Zóna: Czech (cs_CZ)'
buying-more-than-selling: '<red>VAROVÁNÍ: Kupujete předměty za více, než je prodáváte!'
purchase-failed: '<red>Nákup selhal: Vnitřní chyba. Obraťte se na správce serveru.'
denied-put-in-item: <red>Tuto položku nelze vložit do vašeho obchodu!
shop-has-changed: <red>Obchod, který jste se pokusili použít, se změnil od doby, kdy jste na něj klikli!
flush-finished: <green>Nedávné zprávy byly úspěšně vymazány.
no-price-given: <red>Zadejte platnou cenu.
shop-already-owned: <red>Toto je již obchod.
backup-success: <green>Záloha byla úspěšně provedena.
not-looking-at-shop: <red>Obchod se nepodařilo najít. Ujistěte se, že se na něj díváte.
you-cant-afford-a-new-shop: <red>Cena založení nového obchodu je {0}.
success-created-shop: <green>Obchod vytvořen.
shop-creation-cancelled: <red>Vytvoření obchodu bylo zrušeno.
shop-owner-self-trade: <yellow>Obchodujete s vlastním obchodem. Nemusíte získat žádné peníze.
purchase-out-of-space: <red>Tomuto obchodu došlo místo, Kontaktuj jeho majitele aby ho vyprázdnil.
reloading-status:
  success: <green>Obnovení konfigurace proběhlo bez problému.
  scheduled: <green>Obnovení dokončeno. <gray>(Může chvíli trvat, než se změny projeví)
  require-restart: <green>Obnovení dokončeno. <yellow>(Změny vyžadují restartování serveru)
  failed: <red>Obnovení selhalo, zkontrolujte konzoli serveru
player-bought-from-your-store-tax: <green>{0} koupil {1} {2} z vašeho obchodu a vy jste vydělali {3} ({4} daně).
not-enough-space: <red>Zbývá ti jen {0} volných míst!
shop-name-success: <green>Název obchodu byl úspěšně nastaven na <yellow>{0}<green>.
shop-staff-added: <green>Uživatel {0} byl úspěšně přidán do vašeho obchodu.
shop-staff-empty: <yellow>Tento obchod nemá žádné zaměstnance.
shops-recovering: Obnovování obchodů ze zálohy...
virtual-player-component-hover: "<gray>This is a virtual player.\n<gray>Refers to a system account of this name with the same name.</gray>\n<green>UUID: <yellow>{0}</yellow></green>\n<green>Username: <yellow>{1}</yellow></green>\n<green>Display as: <yellow>{2}</yellow></green>"
real-player-component-hover: "<gray>Jedná se o skutečného hráče.\n<green>UUID: <yellow>{0}</yellow></green>\n<green>Nick: <yellow>{1}</yellow></green>\n<green>Zobrazovat jako: <yellow>{2}</yellow></green>\n<gray>Pokud si přejete použít virtuální systémový účet se stejným názvem, přidejte <dark_gray>\"[]\"</dark_gray> na obě strany uživatelského jména: <dark_gray>[{1}]</dark_gray>."
menu:
  sell-tax: <green>Zaplatili jste <yellow>{0}</yellow> na daních.
  owner: '<green>Vlastník: {0}'
  preview: <aqua>[Náhled položky]
  enchants: <dark_purple>Očarování
  sell-tax-self: <green>Nemuseli jste platit daně, protože vlastníte tento obchod.
  shop-information: '<green>Informace o obchodu:'
  item: '<green>Položka: <yellow>{0}'
  price-per: <green>Cena za <yellow>{0} <green>- <yellow>{1}
  item-name-and-price: <yellow>{0} {1} <green>za </green>{2}
  item-name-and-price-tax: <yellow>{0} {1} <green>za</green> {2} <gray>(<green>{3}</green> daň)
  successful-purchase: '<green>Úspěšně zakoupeno:'
  price-per-stack: <green>Cena za <yellow>{2} kusů {0} - {1}
  stored-enchants: <dark_purple>Uložené očarování
  item-holochat-error: <red>[Error]
  this-shop-is-selling: <green>Tento obchod <aqua>PRODÁVÁ</aqua> položky.
  shop-stack: '<green>Počet stacků: <yellow>{0}'
  space: '<green>Mezera: <yellow>{0}'
  effects: <green>Efekty
  damage-percent-remaining: <yellow>{0}% <green>zbývá.
  item-holochat-data-too-large: <red>[Error] Položka NBT je příliš velká k zobrazení
  stock: '<green>Zásoby: <yellow>{0}'
  this-shop-is-buying: <green>Tento obchod je <light_purple>KUPUJTE<green> položek.
  successfully-sold: '<green>Úspěšně prodáno:'
  total-value-of-chest: '<green>Celková hodnota truhly: <yellow>{0}'
currency-not-exists: <red>Nelze najít měnu, kterou chcete nastavit. Možná je pravopis špatný nebo není měna v tomto světě k dispozici.
no-nearby-shop: <red>Žádné blízké obchody odpovídající {0}.
translation-author: 'Ghost_chu, Andre_601'
integrations-check-failed-trade: <red>Integrace {0} zamítla obchod
shop-transaction-failed: <red>Omlouváme se, ale při zpracování nákupu došlo k vnitřní chybě. Nákup byl zrušen a všechny ostatní operace byly vráceny zpět. Pokud tato chyba přetrvává, kontaktujte správce serveru.
success-change-owner-to-server: <green>Úspěšně nastaven vlastník obchodu na Server.
shop-name-not-found: <red>Obchod s názvem <yellow>{0}</yellow> neexistuje.
shop-name-too-long: <red>Tento název obchodu je příliš dlouhý (maximální délka {0}), vyberte prosím jiný!
metric:
  header-player: '<yellow>{0} od {1} {2} transakce:'
  action-hover: <yellow>{0}
  price-hover: <yellow>Celkem {0}, včetně {1} poplatku.
  unknown: <gray>(neznámý)
  undefined: <gray>(bezejmenný)
  no-results: <red>Nenalezeny žádné transakce.
  action-description:
    DELETE: <yellow>Hráč odstranil obchod. Poplatek za shop se vrátil vlastníkovi, pokud to bylo možné.
    ONGOING_FEE: <yellow>Hráč zaplatil poplatek na další období.
    PURCHASE_BUYING_SHOP: <yellow>Hráč prodal nějaké předměty do obchodu.
    CREATE: <yellow>Hráč vytvořil obchod.
    PURCHASE_SELLING_SHOP: <yellow>Hráč koupil některé předměty z obchodu
    PURCHASE: <yellow>Zakoupené položky z obchodu
  query-argument: 'Argument dotazu: {0}'
  amount-hover: <yellow>{0}x
  header-shop: '<yellow>Transakce {1} {2} obchodu {0}:'
  player-hover: |-
    <yellow>{0}<newline><gold>UUID: <gray>{1}
  looking-up: <yellow>Provádím metrické vyhledávání, čekejte prosím...
  tax-hover: <yellow>{0} daň
  header-global: '<yellow>Transakce na serveru {0} {1}:'
  na: <gray>N/A
  transaction-count: <yellow>{0} celkem
  shop-hover: |-
    <yellow>{0}<newline><gold>Pozice: <gray>{1} {2} {3} Svět: {4}<newline><gold>Majitel: <gray>{5}<newline><gold>Typ obchodu: <gray>{6}<newline><gold>Položka: <gray>{7}<newline><gold>Cena: <gray>{8}
  time-hover: '<yellow>Čas: {0}'
  amount-stack-hover: <yellow>{0}x stack
permission-denied-3rd-party: <red>Oprávnění odepřeno pluginem třetí strany {0}.
you-dont-have-that-many-items: <red>Máte pouze {0} {1}.
complete: <green>Hotovo!
translate-not-completed-yet-url: 'Překlad jazyka {0} ještě nebyl dokončen uživatelem {1}. Chcete nám pomoci zlepšit překlad? Klikněte zde!'
success-removed-shop: <green>Obchod odstraněn.
currency-set: <green>Měna obchodu byla úspěšně nastavena na {0}.
shop-purged-start: <green>Shop čištění začalo, zkontrolujte konzoli pro podrobnosti.
economy-transaction-failed: <red>Omlouváme se, ale při zpracování vaší transakce došlo k vnitřní chybě. Transakce byla zrušena a všechny ekonomické změny byly vráceny zpět. Pokud tato chyba přetrvává, kontaktujte prosím správce serveru.
nothing-to-flush: <green>Nemáte žádné nové zprávy o obchodu.
no-price-change: <red>Toto nezmění cenu!
edition-confilct: Plugin Hikari s nainstalovaným QuickShop může způsobit vzájemné konflikty. Odinstalujte jeden z nich.
inventory-unavailable: |-
  <red>This shop InventoryWrapper not exists or invalid. Did you using addon to re-binding the shop Inventory? Information: InventoryWrapper={0}, WrapperProvider={1}, SymbolLink={2}. Please contact the server administrators.
file-test: Toto je zkušební textový soubor. Používáme jej k testování, pokud jde o zprávy json je rozbitý. Můžete jej vyplnit jakýmkoliv východním vejci, které se vám zde líbí :)
unknown-player: <aqua>▪<dark_gray>▏ <aqua>System <dark_gray>› <red>Cílový hráč neexistuje, zkontrolujte prosím uživatelské jméno, které jste zadali.
player-offline: <red>The target player currently is offline.
player-profile-format: <aqua>{0}</aqua><gold>(<yellow>{1}</yellow>)</gold>
shop-type:
  selling: SELLING
  buying: BUYING
language:
  qa-issues: '<white>Otázky zabezpečení kvality: <aqua>{0}%'
  code: '<yellow>Kód: <gold>{0}'
  approval-progress: '<yellow>Průběh schválení: <aqua>{0}%'
  translate-progress: '<yellow>Průběh překladu: <aqua>{0}%'
  name: '<yellow>Název: <gold>{0}'
  help-us: <green>[Pomozte nám zlepšit kvalitu překladu]
warn-to-paste: |-
  <aqua>▪<dark_gray>▏ <aqua>System <dark_gray>› <gray>Shromažďování dat a jejich nahrávání do Pastebinu, to může chvíli trvat. <dark_red>Varování:<red><red> Data jsou po dobu jednoho týdne veřejně uchovávána! Může dojít k úniku konfigurace serveru a dalších citlivých informací. Ujistěte se, že jste jej poslali pouze <bold>důvěryhodnému personálu/vývojářům.
how-many-sell-stack: <gray>Zadej do chatu, kolik stacků si přejete <aqua>Prodat<gray>. Existuje <aqua>{0}<gray> položek v každém balíku a můžete koupit <aqua>{1}<gray> stacků. Vložte <aqua>{2}<gray> do chatu pro nákup všech.
updatenotify:
  buttontitle: '[Update Now]'
  onekeybuttontitle: '[OneKey Update]'
  label:
    github: '[GitHub]'
    ore: '[Ore]'
    lts: '[LTS]'
    nbtapi: '[+NBTAPI]'
    qualityverifyed: '[Quality]'
    master: 'Master'
    unstable: '[Unstable]'
    paper: '[+Dokument optimalizován]'
    stable: '[Stable]'
    spigotmc: '[SpigotMC]'
    papermc: '[PaperMC]'
    bukkitdev: '[BukkitDev]'
    polymart: '[Polymart]'
    modrinth: '[Modrinth]'
    basic: '[Basic]'
  list:
    - '{0} byla vydána. Stále používáte {1}!'
    - Boom! Nová aktualizace {0} přichází. Aktualizujte si plugin!
    - Překvapení! {0} přišel. Jste na {1}
    - Vypadá to, že je třeba aktualizovat plugin. {0} byla vydána!
    - Jejda! {0} byl nyní vydán. Jste na {1}!
    - Přísahám, QS bylo aktualizováno na {0}. Proč jste ještě neupdatovali?
    - Oprava a re... Omlouváme se, ale {0} update byl vydán!
    - Chyba! Ne. Není to chyba. {0} byl vydán!
    - OMG! {0} přišel na to! Proč stále používáte {1}?
    - 'Dnešní novinky: QuickShop byl updatován na {0}!'
    - Plugin k.i.a. Měl byste aktualizovat na {0}!
    - Update {0} zapálen. Uložit update!
    - K dispozici je aktualizační velitel. {0} update právě přišel!
    - Podívejte se na můj styl ---{0} updatován. Stále používáte {1}
    - Ahhhhhh! Nový update {0}! Updatujte plugin!
    - Co si myslíte? {0} byla vydána! Updatujte plugin!
    - Doktor, QuickShop má novou aktualizaci {0}! Měl byste aktualizovat~
    - Ko~ko~da~yo~ QuickShop má nový update {0}~
    - Paimon vám nenápadně oznamuje, že QuickShop má novou aktualizaci {0}!
  remote-disable-warning: '<red>Tato verze QuickShopu je označena jako zakázaná vzdáleným serverem, což znamená, že tato verze může mít vážný problém, získejte podrobnosti na naší SpigotMC stránce: {0}. Toto varování se bude objevovat dokud nepřepnete na stabilní verzi, ale neovlivní to výkon vašeho serveru.'
purchase-out-of-stock: <red>Tumuto obchodu došli zásoby, kontaktujte majitele obchodu pro doplňování zásob.
nearby-shop-entry: '<aqua>▪<dark_gray>▏ <aqua>Info <dark_gray>› {0}<white>Cena:<aqua>{1} <white>x:<aqua>{2} <white>y:<aqua>{3} <white>z:<aqua>{4} <white>vzdálenost: <aqua>{5} <white>blok(y)'
chest-title: Obchod QuickShop
console-only: <red>Tento příkaz může být použit pouze z konzole.
failed-to-put-sign: <red>Není dostatek místa v okolí obchodu a umístit informační značku.
shop-name-unset: <red>Název tohoto obchodu byl odstraněn
shop-nolonger-freezed: <aqua>Odzmrazil jsi obchod. Nyní je to zpět do normální!
no-permission-build: <aqua>▪<dark_gray>▏ <aqua>System <dark_gray>› <red>Zde nelze postavit obchod.
tableformat:
  left_half_line: <dark_purple>+--------------------
  right_half_line: <dark_purple>--------------------+
  full_line: <dark_purple>+-----------------------------------------------------------+
  left_begin: '<dark_purple>| '
quickshop-gui-preview: Náhled GUI položky v QuickShopu
translate-not-completed-yet-click: Překlad jazyka {0} ještě nebyl dokončen uživatelem {1}. Chcete nám pomoci zlepšit překlad? Klikněte zde!
taxaccount-invalid: <red>Cílový účet není platný, zadejte prosím platné jméno hráče nebo uuid(s pomlčkami).
player-bought-from-your-store: <green>{0} si koupil {1} {2} z vašeho obchodu a získali jste {3}.
reached-maximum-can-create: <aqua>▪<dark_gray>▏ <aqua>System <dark_gray>› <red>Již jste vytvořili maximum {0}/{1} obchodů!
reached-maximum-create-limit: <red>Dosáhli jste maximálního počtu obchodů, které můžete vytvořit
translation-version: 'Verze podpory: Hikari'
no-double-chests: <red>Nemůžeš vytvářet DoubleChest obchod.
price-too-cheap: <red>Cena musí být větší než <yellow>${0}
shop-not-exist: <aqua>▪<dark_gray>▏ <aqua>System <dark_gray>› <red>Nemáš žádný obchod.
bad-command-usage: <red>Špatné parametry příkazu!
cleanghost-warning: <yellow>This command will purge <red>all</red> shops if the shop is corrupted, was created in not allowed worlds, is selling/buying not allowed items or <red><bold>EXISTS IN A UNLOADED WORLD</bold></red>. Make sure to create a full backup of your shop data first and use <aqua>/qs cleanghost confirm</aqua> to continue.
cleanghost-starting: <green>Starting to check for ghost shops (missing container blocks). All non-existing shops will be removed...
cleanghost-deleting: <yellow>Found a corrupted shop <aqua>{0}</aqua> because {1}, mark it to delete...
cleanghost-deleted: <green>Total <yellow>{0}</yellow> shops have been deleted.
shop-purchase-cancelled: <red>Nákup v obchodě byl zrušen.
bypassing-lock: <red>Obcházíte zámek QuickShopu!
bungee-cross-server-msg: '<yellow>QuickShop CSM: <green>{0}'
saved-to-path: Záloha byla uložena do {0}.
shop-now-freezed: <aqua>Zmrazili jste obchod. Nikdo nemůže obchodovat s tímto obchodem!
price-is-now: <aqua>▪<dark_gray>▏ <aqua>System <dark_gray>› <gray>Nová cena obchodu je <aqua>{0}
shops-arent-locked: <red>Pamatujte, obchody nejsou chráněny před krádeží! Pokud chcete zastavit zloději uzamknout pomocí LWC, Lockette, atd!
that-is-locked: <red>Tento obchod je uzamčen.
shop-has-no-space: <aqua>▪<dark_gray>▏ <aqua>System <dark_gray>› <red>Obchod má pouze místo pro {0} více {1}.
safe-mode-admin: '<red><bold>WARNING: <red>The QuickShop on this server now running under safe-mode, no features will working, please type <yellow>/qs <red> command to check any errors.'
shop-stock-too-low: <red>V obchodě zbývá pouze {0} {1}!
world-not-exists: <aqua>▪<dark_gray>▏ <aqua>System <dark_gray>› <red>Svět <aqua>{0}<red> neexistuje
how-many-sell: <gray>Zadejte do chatu, kolik si přejete <aqua>Prodat<gray>. Máte k dispozici <aqua>{0}<gray>. Zadejte <aqua>{1}<gray> do chatu, k prodeji všeho.
shop-freezed-at-location: <aqua>Váš obchod {0} v {1} byl zmražen!
translation-contributors: 'Přispěli: Timtower, Netherfoam, KaiNoMood, Mgazul, JackTheChicken and Andre_601'
empty-success: <aqua>▪<dark_gray>▏ <aqua>System <dark_gray>› <gray>Emptying obchod byl úspěšný
taxaccount-set: Daňový účet tohoto obchodu byl nastaven na <yellow>{0}
support-disable-reason:
  hot-reload: <yellow>Unsupported hot reloaded, restart the server and check again.
  outdated: <yellow>This version of QuickShop already outdated, up-to-date before requesting support!
  bad-hosts: |-
    <yellow>This server HOSTS has been modified and QuickShop some functions require connect to Mojang API correctly. Fix the HOSTS before asking support, Windows at C:\windows\system32\drivers\etc\hosts, Linux at /etc/hosts.
  privacy: <yellow>This server running under cracked (offline) mode. If you are running bukkit server under proxy and online-mode=true on proxy, please configure the proxy related settings correctly.
  modified: <yellow>File integrity check failed, this QuickShop build has been modified by others.
  consolespamfix-installed: <yellow>ConsoleSpamFix installed, it will hidden exception details, temporarily disable it during asking support.
  authlib-injector-detected: <yellow>This server running under 3rd-party authlib provider like authlib-injector.
  unsupported-server-software: <yellow>Unsupported server software, Any modded hybrid server software is unsupported including MCPC, Cauldron, CatServer, Mohist, Magma, Fukkit, Cardboard etc.
supertool-is-disabled: <red>Supernástroj je zakázán. Nelze rozbít žádné obchody.
unknown-owner: Nezname
restricted-prices: '<aqua>▪<dark_gray>▏ <aqua>System <dark_gray>› <red>Omezená cena za {0}: Min {1}, max {2}'
nearby-shop-this-way: <aqua>▪<dark_gray>▏ <aqua>System <dark_gray>› <gray>Shop je od vás {0} bloků.
owner-bypass-check: <aqua>▪<dark_gray>▏ <aqua>System <dark_gray>› <gray>Prošel všechny kontroly. Obchod byl úspěšný! (Nyní jste vlastníkem obchodu!)
april-rick-and-roll-easter-egg: "<green>---------------------------------------------------</green>\n<rainbow><bold>LIMITED TIME EVENT -- QuickShop-Hikari</bold></rainbow>\n<yellow>Get your <gold>PREMIUM</gold> particle effect on all QuickShop server!</yellow>\n<aqua>Claim it by watch the video tutorial below!</aqua>\n<hover:show_text:'<gold>Click to open rewards claim page in your browser.</gold><click:open_url:'https://youtu.be/dQw4w9WgXcQ'>https://youtu.be/dQw4w9WgXcQ</click></hover>\n<green>---------------------------------------------------</green>"
signs:
  item-right: ''
  out-of-space: Nedostatek místa
  unlimited: Neomezené
  stack-selling: Prodej {0}
  stack-price: '{0} za {1}x {2}'
  status-unavailable: <red>
  out-of-stock: Vyprodáno
  stack-buying: Koupě {0}
  freeze: Obchod je zakázán
  price: '{0} každý'
  buying: Koupě {0}
  header: '{1}{0}'
  header-available: <green>{0}
  header-unavailable: <red>{0}
  selling: Prodej {0}
  status-available: <green>
  item-left: ''
negative-amount: <red>Derp, nemůžeš obchodovat se zápornými částkami
display-turn-on: <green>Úspěšně zapnuto zobrazení obchodu.
shop-staff-deleted: <aqua>▪<dark_gray>▏ <aqua>System <dark_gray>› <gray>Úspěšně byl odebrán {0} jako člen z tvého obchodu.
nearby-shop-header: '<aqua>▪<dark_gray>▏ <aqua>System <dark_gray>› <gray>Nejbližší obchod odpovídající <aqua>{0}<gray>:'
backup-failed: Nelze zálohovat databázi. Podrobnosti naleznete v konzole.
shop-staff-cleared: <aqua>▪<dark_gray>▏ <aqua>System <dark_gray>› <gray>Úspěšně byli odebráni všichni členi z tvého obchodu.
price-too-high: <aqua>▪<dark_gray>▏ <aqua>System <dark_gray>› <red>Cena obchodu je příliš vysoká! Nelze vytvořit s cenou vyšší než {0}.
plugin-cancelled: '<red>Operation cancelled, Reason: {0}'
player-sold-to-your-store: <aqua>▪<dark_gray>▏ <aqua>System <dark_gray>› <gray>Hráč {0} prodal {1} {2} do tvého obchodu.
shop-out-of-stock: <aqua>▪<dark_gray>▏ <aqua>System <dark_gray>› <gray>Váš obchod v {0}, {1}, {2}, vyčerpal {3}!
how-many-buy: <gray>Napište do chatu, kolik si přejete <aqua>Koupit<gray>. Můžete si koupit <aqua>{0}<gray>. Zadejte <aqua>{1}<gray> pro zakoupení všech.
language-info-panel:
  help: 'Pomozte nám'
  code: 'kód:'
  name: 'Jazyky:'
  progress: 'Průběh: '
  translate-on-crowdin: 'Přeložit na Crowdin'
item-not-exist: <red>Položka <aqua>{0} <red>neexistuje, prosím zkontrolujte pravopis.
shop-creation-failed: <red>Vytvoření ochodu se nezdařilo, kontaktujte prosím správce serveru.
inventory-space-full: <red>Your inventory space remains can only put <green>{1}x <red>items into it, try empty the your inventory!
no-creative-break: <red>Nemůžete rozbít obchody ostatních hráčů v kreativním režimu, přepněte do režimu přežití nebo zkuste použít super tool {0}.
booleanformat:
  success: <green>✔
  failed: <red>
controlpanel:
  stack: '<green>Za celý stack: <aqua>{0} <yellow>[<light_purple><bold>Změnit<yellow>]'
  price-hover: <aqua>Kliknutím nastavíte novou cenu itemu.
  remove: <dark_red>[<red>Odstranit obchod<dark_red>]
  mode-buying-hover: <aqua>Kliknutím změníte obchod na SELL režim.
  empty: '<green>Prázdné: Odstranit všechny položky <yellow>[<light_purple><bold>OK<yellow>]'
  stack-hover: <aqua>Kliknutím nastavíte množství položky za každý bulk. Nastavte na 1 pro normální chování.
  alwayscounting-hover: <yellow>Click to toggle if the shop is always counting container.
  alwayscounting: '<green>Vždy počítat {0} <yellow>[<light_purple><bold>Změnit<yellow>]'
  setowner: '<green>Vlastník: <aqua>{0} <yellow>[<light_purple><bold>Změna<yellow>]'
  freeze: '<yellow>Režim zmražení: <aqua>{0} <yellow>[<light_purple><bold>Přepnout<yellow>]'
  price: '<green>Cena: <aqua>{0} <yellow>[<light_purple><bold>Změna<yellow>]'
  currency-hover: <aqua>Klikněte pro nastavení nebo odebrání měny, kterou tento obchod používá
  lock: '<yellow>Zámek obchodu: <aqua>{0} <yellow>[<light_purple><bold>Přepnout<yellow>]'
  mode-selling: '<gray>Režim obchodu: <aqua>Selling <gray>[<light_purple><dark_aqua>Change<gray>]'
  currency: '<green>Měna: <aqua>{0} <yellow>[<light_purple><bold>Nastavit<yellow>]'
  setowner-hover: <aqua>Klikni pro přepnutí vlastníka.
  mode-buying: '<green>Režim obchodu: <aqua>Koupit <yellow>[<light_purple><bold>Změnit<yellow>]'
  item: '<green>Položka obchodu: {0} <yellow>[<light_purple><bold>Změnit<yellow>]'
  unlimited: '<green>Neomezeno: {0} <yellow>[<light_purple><bold>Změnit<yellow>]'
  unlimited-hover: <aqua>Kliknutím přepnete pokud obchod není omezený.
  refill-hover: <aqua>Klikněte pro doplnění obchodu.
  remove-hover: <aqua>Klikněte pro odstranění tohoto obchodu.
  toggledisplay-hover: <white>Přepne obchodu status ukazovaného předmětu
  refill: '<green>Plnění: Doplnit položky <yellow>[<light_purple><bold>OK<yellow>]'
  freeze-hover: <aqua>Přepnout stav zmrazení obchodu.
  lock-hover: <aqua>Povolit/Zakázat ochranu zámku obchodu.
  item-hover: <aqua>Klikněte pro změnu itemu obchodu
  infomation: 'Ovládací panel <aqua>Shop:'
  mode-selling-hover: <aqua>Kliknutím změníte obchod na režim BUY.
  empty-hover: <aqua>Klikněte pro vymazání inventáře obchodu.
  toggledisplay: '<green>Display-Item: <aqua>{0} <yellow>[<light_purple><bold>Toggle</bold></light_purple>]'
  history: '<green>History: <yellow>[<bold><light_purple>View</light_purple></bold>]</yellow>'
  history-hover: <yellow>Click to view shop history logs
timeunit:
  behind: za
  week: "{0} week"
  weeks: "{0} weeks"
  year: "{0} year"
  before: před
  scheduled: naplánováno
  years: "{0} years"
  scheduled-in: naplánováno na {0}
  second: "{0} second"
  std-past-format: 'před {5}{4}{3}{2}{1}{0}'
  std-time-format: HH:mm:ss
  seconds: "{0} seconds"
  hour: "{0} hour"
  scheduled-at: naplánováno na {0}
  after: po
  day: "{0} day"
  recent: nejnovější
  between: mezi
  hours: "{0} hours"
  months: "{0} months"
  longtimeago: před dlouhou dobou
  between-format: mezi {0} a {1}
  minutes: "{0} minutes"
  justnow: právě teď
  minute: "{0} minute"
  std-format: MM/dd/yyyy HH:mm:ss
  future-plain-text: v budoucnosti
  month: "{0} month"
  future: v {0}
  days: "{0} days"
command:
  reloading: '<green>Konfigurace obnovena. <yellow>Některé změny mohou vyžadovat restartování. <newline><gray>(Poznámka: Chování obnovení bylo změněno po ******** nyní pouze znovu načítáme konfiguraci, ale ne celý plugin, abychom zajistili, že server bude stabilní.)'
  description:
    buy: <white>Změní obchod na režim <light_purple>BUY<yellow>
    about: <white>Zobrazí informace o QuickShopu
    language: <white>Změní aktuální jazyk bota na serveru
    purge: <white>Začne promazávání obchodů na pozadí
    paste: <white>Nahrává data serveru do Pastebinu
    title: <aqua>Nápověda pro QuickShop
    remove: <white>Odstraní obchod, na kterém se díváte
    ban: <white>Zabanování hráče z obchodu
    empty: <white>Odstraní všechny položky z obchodu
    alwayscounting: <yellow>Nastavte, pokud obchod vždy počítá obsah chestky i když je neomezený
    setowner: <white>Změní vlastnictví obchodu.
    reload: <white>Znovu načte config.yml QuickShopu
    freeze: <white>Zakázat nebo povolit obchodování obchodu
    price: <white>Změní kupní cenu obchodu
    find: <white>Najde nejbližší obchod konkrétního typu.
    create: <white>Vytvoří nový obchod z cílené truhly
    lock: <white>Přepnout stav zámku obchodu
    currency: <white>Nastavte nebo odeberte nastavení měny obchodu
    removeworld: <white>Odebrat VŠECHNY obchody zadaného hráče
    info: <white>Zobrazit statistiky v QuickShopu
    owner: <white>Změní vlastnictví obchodu.
    amount: <white>Možnost nastavit částku položky (Useful když máte problémy s chatem)
    item: <white>Změnit položku v obchodě
    debug: <white>Zapne Vývojářský mód
    unlimited: <white>Dá obchodu neomezený sklad.
    sell: <white>Změní obchod na režim <light_purple>BUY<yellow>
    fetchmessage: <white>Zobrazit zprávy o nepřečtených obchodech
    staff: <white>Spravovat zaměstnance obchodu
    clean: <white>Odstraní všechny (načtené) obchody bez skladu
    refill: <white>Přidá zadaný počet položek do obchodu
    help: <white>Zobrazí informace o QuickShopu
    removeall: <white>Odebrat VŠECHNY obchody zadaného hráče
    unban: <white>Odbanování hráče z obchodu
    transfer: <white>Přeneste všechny obchody do jiného
    transferall: <white>Přeneste všechny obchody do jiného
    transferownership: <yellow>Transfer the shop you're looking at to someone else
    size: <white>Změna na hromadné množství obchodu
    supercreate: <white>Vytvořit obchod při obcházení všech ochranných kontrol
    taxaccount: <white>Nastaví daně, které obchod zaplatí
    name: <yellow>Pojmenování obchodu na konkrétní název
    toggledisplay: <white>Přepne obchodu status ukazovaného předmětu
    permission: <yellow>Správa oprávnění obchodu
    lookup: <yellow>Spravovat tabulku položek pro vyhledávání
    database: <yellow>View and maintain QuickShop database
    benefit: <yellow>Settings of divide benefits between shop owner and other players
    tag: <yellow>Add, remove or query tags of a shop
    suggestprice: <yellow>Suggest a recommend price for looking shop, based on other shops
    history: <yellow>View the history transactions of a shop
    sign: <yellow>Change the sign material of a shop
  bulk-size-not-set: '<red>Použití: <gray>/qs size <amount>'
  no-type-given: '<red>Použití: <gray>/qs find <item>'
  feature-not-enabled: Funkce není v konfiguraci povolena.
  now-debuging: <gray>Úspěšně povolen vývojářský režim. Reloaduji QuickShop...
  no-amount-given: <red>Nebyla poskytnuta žádná hodnota. Použijte <aqua>/qs refill <amount><red>
  no-owner-given: <red>Žádný vlastník
  disabled: '<red>Tento příkaz je zakázán: <aqua>{0}'
  bulk-size-now: <gray>Nyní tradujete <aqua>{0}x {1}
  toggle-always-counting:
    counting: <green>Shop is now always counting container even is unlimited
    not-counting: <green>Obchod respektuje, pokud je nastavený na neomezený
  cleaning: <gray>Odstraňuji obchody bez skladu...
  now-nolonger-debuging: <gray>Úspěšně zakázán vývojářský režim. Reloaduji QuickShop...
  toggle-unlimited:
    limited: <gray>Obchod je nyní omezený
    unlimited: <gray>Obchod je nyní neomezený
  transfer-success-other: <gray>Přeneseno <aqua>{0} <gray>obchodů <aqua>{1}
  no-trade-item: <gray>Podržte prosím item obchodu, který chcete změnit v hlavní ruce
  wrong-args: <red>Neplatné argumenty. Použij <bold>/qs help <red>pro zobrazení seznamu příkazů.
  some-shops-removed: <aqua>{0} <gray>obchod(ů) odstraněno
  new-owner: '<white>Nový majitel: <aqua>{0}'
  format: <green>/{0} {1} <yellow>- {2}
  transfer-success: <gray>Přeneseno <aqua>{0} <gray>obchodů <aqua>{1}
  now-buying: <gray>Nyní <aqua>Kupujete <white>{0}
  now-selling: <gray>Nyní <aqua>Prodáváte <white>{0}
  cleaned: <gray>Odstraněno <aqua>{0}<gray> obchodů.
  trade-item-now: <gray>Nyní tradujete <aqua>{0}x {1}
  no-world-given: <red>Zadejte název světa
  format-disabled: <dark_gray>| <red>/{0} {1} <gray>- <white>{2}
  invalid-bulk-amount: <red>Zadaná hodnota {0} je větší než maximální velikost stacku nebo menší než jedna
currency-not-support: <red>Doplněk pro ekonomiku nepodporuje funkci více ekonomik.
trading-in-creative-mode-is-disabled: <red>V kreativním módu nemůžeš obchodovat.
the-owner-cant-afford-to-buy-from-you: <aqua>▪<dark_gray>▏ <aqua>System <dark_gray>› <red>Tohle stojí {0}, ale vlastník má pouze {1}
you-cant-afford-shop-naming: <red>Pojmenování obchodu si nemůžeš dovolit, pojmenovávání stojí {0}.
inventory-error: |-
  <red>Failed to processing the InventoryWrapper. Did you using addon to re-binding the shop Inventory? Information: Exception={0}, InventoryWrapper={1}, WrapperType={2}, WrapperProvider={3}, SymbolLink={4}. Please contact the server administrators.
integrations-check-failed-create: <red>Integrace {0} zamítla vytvoření obchodu
shop-out-of-space: <aqua>▪<dark_gray>▏ <aqua>System <dark_gray>› <gray>Tvůj obchod v {0}, {1}, {2}, je nyní plný.
admin-shop: Server
no-anythings-in-your-hand: <red>Nemáš v ruce žádný předmět.
no-permission: <red>Na tuto akci nemáš práva.
chest-was-removed: <red>Truhla byla odstraněna.
you-cant-afford-to-buy: <red>Toto stojí {0}, ale ty máš jen {1}
shops-removed-in-world: <aqua>▪<dark_gray>▏ <aqua>System <dark_gray>› <gray>Celkem <aqua>{0}<gray> obchodů bylo smazáno ve světě <aqua>{1}<gray>.
display-turn-off: <green>Úspěšně vypnuto zobrazení obchodu.
client-language-unsupported: <yellow>QuickShop nepodporuje váš klientský jazyk, nastavili jsme vám tedy záložní na {0} locales.
language-version: '63'
not-managed-shop: <red>Nejsi vlastník nebo moderátor obchodu
shop-cannot-trade-when-freezing: <red>Nemůžete obchodovat s tímto obchodem, protože je zmražený.
invalid-container: <red>Neplatný kontejner, můžete vytvořit pouze obchod na bloku, který má inventář.
permission:
  header: <green>Podrobnosti o oprávnění obchodu
  header-player: <green>Podrobnosti o oprávnění obchodu pro {0}
  header-group: <green>Detaily oprávnění obchodu pro skupinu {0}
  table: <gold><b>{0}</b>:</gold> <gray>{1}</gray>
  item:
    purchase: <yellow>Oprávnění umožní uživatelům, s tímto oprávněním, zakoupit obchod (včetně nákupu a prodeje)
    show-information: <yellow>Oprávnění umožní uživatelům, s tímto oprávněním, zobrazit informace o obchodě (otevřený obchodní informační panel)
    preview-shop: <yellow>Oprávnění povolí uživatelům, s tímto oprávněním, zobrazit náhled obchodu. (náhled položky)
    search: <yellow>Oprávnění umožní uživatelům, s tímto oprávněním, vyhledávat cílový obchod. (Odstraněním oprávnění skryjete obchod ve výsledcích vyhledávání)
    delete: <yellow>Oprávnění povolí uživatelům, s tímto oprávněním, odstranit obchod.
    receive-alert: <yellow>Oprávnění povolí uživatelům, s tímto oprávněním, přijímat upozornění (např. mimo burzu nebo nový obchod).
    access-inventory: <yellow>Oprávnění umožní hráčům přístup k inventáři obchodu.
    ownership-transfer: <yellow>Oprávnění umožní hráčům, s tímto oprávněním, převést vlastnictví obchodu.
    management-permission: <yellow>Oprávnění povolií uživatelům, s tímto oprávněním, spravovat oprávnění skupin a upravovat skupinu uživatelů.
    toggle-display: <yellow>Oprávnění umožní uživatelům, s tímto oprávněním, přepnout položku zobrazení obchodu.
    set-shoptype: <yellow>Oprávnění umožní uživatelům, s tímto oprávněním,, nastavit typ obchodu (přepnout nákup nebo prodej).
    set-price: <yellow>Oprávnění umožní uživatelům, s tímto oprávněním, nastavit cenu obchodu.
    set-item: <yellow>Oprávnění umožní uživatelům, s tímto oprávněním, nastavit položku obchodu.
    set-stack-amount: <yellow>Oprávnění umožní uživatelům, s tímto oprávněním, nastavit množství zásobníku obchodu.
    set-currency: <yellow>Oprávnění umožní uživatelům, s tímto oprávněním, nastavit měnu obchodu.
    set-name: <yellow>Oprávnění umožní uživatelům, s tímto oprávněním, nastavit název obchodu.
    set-sign-type: <yellow>Change the sign material that attached on the shop.
    view-purchase-logs: <yellow>Permission to view shop purchase logs.
  group:
    everyone: <yellow>Výchozí skupina pro všechny uživatele kromě vlastníka obchodu.
    staff: <yellow>Systém skupin pro zaměstnance v obchodech.
    administrator: <red>Systémová skupina pro správce, uživatelé v této skupině budou mít téměř stejná oprávnění jako majitel obchodu.
invalid-group: <red>Neplatný název skupiny.
invalid-permission: <red>Neplatné oprávnění.
invalid-operation: <red>Neplatná operace, pouze {0} je povoleno.
player-no-group: <yellow>Hráč {0} není v žádné skupině v tomto obchodě.
player-in-group: <green>Hráč {0} je ve skupině <aqua>{1}</aqua> v tomto obchodě.
permission-required: <red>K tomu nemáte oprávnění {0} v tomto obchodě.
no-permission-detailed: <red>K tomu nemáte oprávnění <yellow>{0}</yellow>.
paste-notice: "<yellow>Note: If you are creating a Paste for troubleshooting purposes, be sure to reproduce the error before creating the Paste as quickly as possible; we need the logs retained briefly in the buffer to troubleshoot. If you create a Paste too slowly or without first reproducing the error or restarted server, the Paste will record nothing and useless."
paste-uploading: <aqua>Počkejte prosím... Nahrávání vložení do úložiště kopírování......
paste-created: '<green>Vložte vytvořené, klikněte pro otevření v prohlížeči: <yellow>{0}</yellow><newline><red>Varování: <gray>Nikdy neposílejte vložení nikomu, komu nedůvěřujete.'
paste-created-local: |-
  <green>Vložte vytvořené a uložené na váš místní disk na: {0}<newline><red>Varování: <gray>Nikdy neposílejte vložení nikomu, komu nedůvěřujete.
paste-created-local-failed: <red>Nepodařilo se uložit vložení na místní disk, zkontrolujte svůj disk.
paste-451: |-
  <gray><b>TIPS: </b> Zdá se, že vaše současná země nebo oblast blokovala službu CloudFlare Workers a QuickShop vložení nemusí být načteno správně.<newline><gray>Pokud následná operace selže, zkuste přidat --file parametr pro vygenerování místní cesty: <dark_gray>/qs paste --file</dark_gray>
paste-upload-failed: <red>Nepodařilo se nahrát vložení do {0}, vyzkoušet jiného poskytovatele vkládání...
paste-upload-failed-local: <red>Nepodařilo se nahrát vložení, pokus o vygenerování lokálního vložení...
command-incorrect: '<red>Chybné použití příkazu, napište /qs help pro kontrolu nápovědy. Použití: {0}.'
successfully-set-player-group: <green>Skupina hráče {0} byla úspěšně nastavena na <aqua>{1}</aqua>.
successfully-unset-player-group: <green>Skupina hráčů byla úspěšně odstraněna z tohoto obchodu.
successfully-set-player-permission: <green>Úspěšně nastaveno oprávnění hráče {0} <aqua>{1}</aqua> v obchodě <aqua>{2}</aqua>.
lookup-item-created: <green>Položka s názvem <aqua>{0}</aqua> byla vytvořena v tabulce vyhledávání. Nyní můžete odkazovat na tuto položku v konfiguracích.
lookup-item-exists: <red>Položka s názvem <yellow>{0}</yellow> již existuje v tabulce vyhledávání, smaž ji nebo vyber jiné jméno.
lookup-item-not-found: <red>Položka s názvem <yellow>{0}</yellow> neexistuje.
lookup-item-name-illegal: <red>Název položky je nezákonný. Jsou povoleny pouze alfanumerické znaky a podtržítka.
lookup-item-name-regex: '<red>Název musí odpovídat tomuto regexu: <aqua>{0}</aqua>.'
lookup-item-test-not-found: '<gold>Test: <yellow>Položka v ruce není zaregistrována ve vyhledávací tabulce.'
lookup-item-test-found: '<gold>Test: <green>Položka v ruce byla zaregistrována v tabulce vyhledávání pod názvem <aqua>{0}</aqua>.'
lookup-item-removed: <green>Zadaná položka <aqua>{0}</aqua> byla úspěšně odstraněna z tabulky vyhledávání.
internal-error: <red>Došlo k vnitřní chybě, kontaktujte prosím správce serveru.
argument-cannot-be: <red>The argument <aqua>{0}</aqua> cannot be <yellow>{1}</yellow>.
argument-must-between: <red>The argument <aqua>{0}</aqua> value must between <yellow>{1}</yellow> and <yellow>{2}</yellow>
invalid-percentage: <red>Percentage invalid, you must add '%' after the number of percentage.
display-fallback: |-
  <red>Due to an internal failure, a fallback message is being displayed.
  The value of this item may not be localized or processed correctly.
  Please contact the server administrator.
not-a-valid-time: |-
  <red>The string <yellow>{0}</yellow> not a valid timestamp, please enter a <aqua>Zulu Time (ISO 8601)</aqua> or <aqua>Unix Epoch Time in seconds</aqua>.
  <gold>Valid timestamp example: (for Sat, 17 Dec 2022 10:31:37 GMT)</gold>
  <aqua>-<aqua> <yellow>2022-12-17T10:31:37Z</yellow> <grey>(Zulu Time)</grey>
  <aqua>-<aqua> <yellow>1671273097</yellow> <grey>(Unix Epoch Time in seconds)</grey>
invalid-past-time: <red>You can't specify a time in the past.
debug:
  arguments-invalid: <red>Provided arguments <yellow>{0}</yellow> invalid.
  sign-located: '<green>Sign valid: <yellow>{0}</yellow>.'
  operation-missing: <red>You must specific an operation.
  operation-invalid: <red>You must specific an valid operation.
  invalid-base64-encoded-sql: <red>The SQL provided must be base64 encoded.
  warning-sql: |-
    <red><bold>Warning:</bold></red><yellow>You're executing a SQL statement, this may corrupt your database or destroy any data in database even it belongs to other plugins.
    <red>Don't confirm if you don't trust who send you this command.
  warning-sql-confirm: <yellow>To confirm this dangerous operation, please type <aqua>/qs debug database sql confirm {0}</aqua> in 60 seconds.
  warning-sql-confirm-hover: <yellow>Click to confirm this dangerous operation.
  sql-confirm-not-found: <yellow>Didn't found the operation that you given, it may invalid or expired.
  sql-executing: '<yellow>Spuštění příkazu SQL: <aqua>{0}</aqua>'
  sql-completed: <green>Completed, {0} rows affected.
  sql-exception: <red>An error occurred while executing the SQL query, check the Console for details!
  sql-disabled: '<red>For security reason, SQL queries are disabled in this server, If you really need this feature, you can add the following flag to startup arguments: <aqua>{0}</aqua> with `true` value to enable it.'
  force-shop-reload: <yellow>Forcing reload all loaded shops...
  force-shop-reload-complete: <green>Forced <aqua>{0}</aqua> shops to be reloaded.
  force-shop-loader-reload: <yellow>Forcing shop-loader reloading...
  force-shop-loader-reload-unloading-shops: <yellow>Unloading <aqua>{0}</aqua> loaded shops...
  force-shop-loader-reload-unloading-shops-from-memory: <yellow>Removing <aqua>{0}</aqua> shops from memory...
  force-shop-loader-reload-reloading-shop-loader: <yellow>Re-calling shop-loader for reloading all shops from database...
  force-shop-loader-reload-complete: <green>Shop-loader has been reloaded all shops!
  toggle-shop-loaded-status: <aqua>Toggle this shop loaded status to <gold>{0}</gold>
  shop-internal-data: '<yellow>The internal data of this shop: <reset>{0}</reset>'
  handler-list-not-valid-bukkit-event-class: <red>The provided class <yellow>{0}</yellow> is not a valid Bukkit event class.
  update-player-shops-signs-no-username-given: <red>You must provide a valid player username.
  update-player-shops-signs-create-async-task: <yellow>Creating async tasks for force updating signs...
  update-player-shops-player-selected: '<yellow>Player selected: <gold>{0}</gold>'
  update-player-shops-player-shops: <yellow>Total <gold>{0}</gold> shops waiting for updates.
  update-player-shops-per-tick-threshold: '<yellow>Max shops can be update per tick: <gold>{0}</gold>'
  update-player-shops-complete: <green>Task completed, Used <yellow>{0}ms</yellow> for updating.
  update-player-shops-task-started: <gold>The tasks has been started, please wait for it to complete.
  item-info-store-as-string: "<green>The store you looking at: <gold>{0}</gold> Hash: <white>{1}</white>"
  item-info-hand-as-string: "<green>The item in your hand: <gold>{0}</gold> Hash: <white>{1}</white>"
  item-matching-result: "<green>Hand2Store: <aqua>{0}</aqua>, Store2Hand: <aqua>{1}</aqua>"
  hikari-cp-size-tweak: "<green>HikariCP MaximumPoolSize and MinimumIdle was set to <white>{0}</white>"
  hikari-cp-testing: "<green>Please wait, testing HikariCP connection..."
  hikari-cp-working: "<green>Pass! HikariCP working well!"
  hikari-cp-not-working: "<red>Failed! The connection that returned by HikariCP is dead! (Not passed test in 1 second)"
  hikari-cp-timeout: "<red>HikariCP is timed out while getting a valid connection, please clean up all active queries to release connection resources."
  queries-stopped: "<green>Stopped <white>{0}</white> active queries."
  queries-dumping: "<yellow>Dumping active queries..."
  restart-database-manager: "<yellow>Restarting SQLManager..."
  restart-database-manager-clear-executors: "<yellow>Clearing executors..."
  restart-database-manager-unfinished-task: "<yellow>Unfinished task: <white>{0}</white>"
  restart-database-manager-unfinished-task-history-query: "<yellow>Unfinished task (History Query): <white>{0}</white>"
  restart-database-manager-reconnect: "<yellow>Re-launching SQLManager via initial sequence (via async executor)"
  restart-database-manager-done: "<green>Done!"
  property-incorrect: "<yellow>You must enter a (and only one) property key=value set. E.g   aaa=bbb"
  property-security-block: "<red>Request was rejected, for security reason, you can only change the property that startsWith <aqua>com.ghostchu.quickshop</aqua> or <aqua>quickshop</aqua>."
  property-removed: "<green>Removed property key <white>{0}</white>"
  property-changed: "<green>Property key <white>{0}</white> was changed from <white>{1}</white> to <white>{2}</white>"
  marked-as-dirty: "<green>Marked all stores as dirty status, they will be force save in next auto-save task. (Restart the server to force execute store saving task)"
  display-removed: "<green>Successfully removed <yellow>{0}</yellow> QuickShop display items/entities from the worlds."
database:
  scanning: <green>Scanning the isolated data in QuickShop databases, the database load may increase in scan progress, this might need a while...
  scanning-async: <yellow>Scanning the isolated data in QuickShop databases on async task thread, the database load may increase in scan progress, this might need a while, try again later.
  already-scanning: <red>An scan task has been started, please wait until it finished.
  trim-warning: '<yellow><red><bold>Warning: </red>Backup your database before continue database trim to avoid the data loss. Once you get ready, execute <aqua>/qs database trim confirm</aqua> to continue.'
  status: '<yellow>Stav: {0}'
  status-good: <green>Dobré
  status-bad: <yellow>Maintenance Required
  isolated: '<yellow>Izolovaná data:'
  isolated-data-ids: '<yellow><aqua>└</aqua> Data Records: <gold>{0}</gold>'
  isolated-shop-ids: '<yellow><aqua>└</aqua> Shop Indexes: <gold>{0}</gold>'
  isolated-logs: '<yellow><aqua>└</aqua> Logs: <gold>{0}</gold>'
  isolated-external-caches: '<yellow><aqua>└</aqua> External Caches: <gold>{0}</gold>'
  last-purge-time: <yellow>Poslední čas oříznutí v {0}
  report-time: <yellow>Poslední čas skenování při {0}
  auto-scan-alert: <yellow>The QuickShop database on this server require a maintenance. Found <gold>{0}</gold> isolated data wait for trimming.
  auto-trim: <green>Auto trim has been enabled on this server, no manually trim required.
  trim-complete: <green>Database trim completed, <yellow>{0}</yellow> isolated trimmed.
  auto-trim-started: <green>Auto trim has been started, please wait...
  trim-start: <green>Database trim started, please wait...
  trim-exception: <red>Database trim failed, an exception raised during database trimming, check the server console.
  generated-at: '<yellow>Generated at: <gold>{0}</gold>'
  purge-date: <red>You must given a date.
  purge-warning: <yellow>This operation will purge the history that stored in your database including shop creates/changes/deletes, purchases, transactions and system logs. Delete those data can free the disk space but all history metric will lost, and other plugins that depend on metrics will stop working. To continue execute this operation, execute `/qs database purgelogs <before-days> confirm`
  purge-task-created: <green>Task created! Database are silent purging history records in background.
  purge-done-with-line: <green>Purge task completed, total <gold>{0}</gold> lines purged from database.
  purge-done-with-error: <red>Purge task failed, check the server console for details.
  purge-players-cache: <yellow>Please wait, purging players caches...
  purge-players-completed: |-
    <green>Successfully purged {0} players caches from both memory and database.<newline><aqua>Note: You server performance may impact by this operation.
  purge-players-error: <red>Failed to purge players caches, please check the server console.
  suggestion:
    trim: <yellow>This database require a isolated data trim, execute command <aqua>/qs database trim</aqua> to trim the database.
always-counting-removal-early-warning: <red>Always Counting feature is scheduled for removal, you shouldn't use it anymore and will stop work in future.
exporting-database: <green>Export databáze, počkejte prosím...
exporting-failed: <red>Nepodařilo se exportovat databázi, zkontrolujte konzolu serveru.
exported-database: <green>Databáze exportovaná do <yellow>{0}</yellow>.
importing-not-found: <red>The <yellow>{0}</yellow> file not found, please check the file path.
importing-early-warning: |-
  <yellow><red><bold>Warning: </bold></red>The backup will be imported to the current database, any exists data will be purged and permanently lost if you don't have a backup.<newline><red>Are you sure to continue the import progress?</red><yellow>Type <aqua>/qs recovery confirm</aqua> to continue.
importing-database: <green>Import databáze ze zálohy, počkejte prosím...
importing-failed: <red>Nepodařilo se importovat databázi, zkontrolujte konzolu serveru.
imported-database: <green>Databáze importovaná z <yellow>{0}</yellow>.
transfer-sent: <green>Shops transfer request sent to <yellow>{0}</yellow>.
transfer-request: <yellow>Player <aqua>{0}</aqua> requested to transfer their shops to you. Did you want accept the transfer?
transfer-single-request: <yellow>Player <aqua>{0}</aqua> would like to transfer a shop to you. Do you want to accept the request?
transfer-ask: |-
  <gold>Type <red>/qs transfer accept</red> to accept.</gold><newline><gold>Type <red>/qs transfer deny</red> to deny.</gold><newline><gold>Request will expire after <red>{0}</red> seconds.
transferall-ask: |-
  <gold>Type <red>/quickshop transferall accept</red> to accept or <red>/quickshop transferall deny</red> to deny.
  The request will expire after <red>{0}</red> seconds.
transfer-single-ask: |-
  <gold>Type <red>/qs transfer accept</red> to accept.</gold><newline><gold>Type <red>/qs transfer deny</red> to deny.</gold><newline><gold>Request will expire after <red>{0}</red> seconds.
transfer-accepted-fromside: <green>Player <aqua>{0}</aqua> accepted your shop transfer request.
transfer-accepted-toside: <green>You accepted <aqua>{0}</aqua>'s transfer request.
transfer-rejected-fromside: <red>Player <aqua>{0}</aqua> rejected your shop transfer request.
transfer-rejected-toside: <red>You rejected <aqua>{0}</aqua>'s shop transfer request.
transfer-no-pending-operation: <red>You don't have pending transfer request yet.
transfer-no-self: <red>You can't transfer your shops to yourself.
benefit-overflow: <red>The sum of all benefits cannot be greater than or equal to 100%.
benefit-exists: <red>Target player already in benefits list on this shop.
benefit-removed: <red>Target player has been removed from shop benefits.
benefit-added: <green>Player <aqua>{0}</aqua> has been added into shop benefits!
benefit-updated: <green>Player <aqua>{0}</aqua>'s benefits has been updated!
benefit-query: <green>This shop have <yellow>{0}</yellow> players in benefits list!
benefit-query-list: <yellow> - </yellow><white>Player <gold>{0}</gold>, Benefit <gold>{1}%</gold></white>
tag-added: '<green>Successfully added <aqua>#{0}</aqua> to this shop!'
tag-add-duplicate: '<red>The tag <aqua>#{0}</aqua> already exists at this shop!'
tag-removed: '<green>Successfully removed <aqua>#{0}</aqua> from this shop!'
tag-remove-not-exists: 'The tag <aqua>#{0}</aqua> not exists at this shop!'
tag-cleared: <green>Successfully cleared all tags from this shop!
tag-shops-cleared: '<green>Successfully cleared <aqua>#{0}</aqua> from all your tagged shops!'
tag-query: '<green>This shop have <yellow>{0}</yellow> tags:'
tag-query-listing: '<yellow> - <aqua>#{0}</aqua>'
tag-query-no-tag: <red>This shop have no tags.
tag-query-shops: '<green>This tag contains <yellow>{0}</yellow> shops:'
tag-query-shops-listing: <yellow> - <aqua>{0}</aqua>
batch-operations-based-on-tags-no-failure: <green>Successfully batch processed <yellow>{0}</yellow> shops.
batch-operations-based-on-tags-have-failure: <yellow>Total <yellow>{0}</yellow> shops in batch processing successfully completed, but <red>{1}</red> requests not completed.
batch-operations-based-on-tags-have-failure-with-reason: '<yellow>Total <yellow>{0}</yellow> shops in batch processing successfully completed, but <red>{1}</red> requests not completed. Reason: <gold>{2}</gold>'
batch-operations-based-on-tags-have-failure-error-messages-may-appears-above: <gray>The error messages will send to you above this message in the chat.
addon:
  towny:
    commands:
      town: <yellow>Set or unset a shop to a Town shop
      nation: <yellow>Set or unset a shop to a Nation shop
    make-shop-owned-by-town: <green>You have made the shop owned by town <yellow>{0}</yellow>.
    make-shop-no-longer-owned-by-town: <green>You have reset shop ownership, it now transfer back to original shop owner.
    make-shop-owned-by-nation: <green>You have made the shop owned by nation <yellow>{0}</yellow>.
    make-shop-no-longer-owned-by-nation: <green>You have reset shop ownership, it now transfer back to original shop owner.
    shop-owning-changing-notice: <grey>This shop now owned by a town/nation, original shop owner has been automatically added into administrator list, modify or add any new owner / staff by using command /qs permission
    target-shop-already-is-town-shop: <red>The target shop is already owned by a town.
    target-shop-already-is-nation-shop: <red>The target shop is already owned by a nation.
    target-shop-not-in-town-region: <red>The target shop is not in the town.
    target-shop-not-in-nation-region: <red>The target shop is not in the nation.
    item-not-allowed: <red>This shop's item are not allowed used by town/nation shop, pick another one!
    operation-disabled-due-shop-status: <red>This shop operation has been disabled due this already is a town/nation shop.
    plot-type-disallowed: <red>You can't create town/nation shop on this type of plot.
    flags:
      own: <red>You can only create shops at your owned shop type towny plot.
      modify: <red>You didn't have build permission on this towny plot.
      shop-type: <red>You must create shop on a shop type towny plot.
  residence:
    creation-flag-denied: <red>You don't have permission to create shops in this residence.
    trade-flag-denied: <red>You don't have permission to purchase in this residence.
    you-cannot-create-shop-in-wildness: <red>You can't create shop in wildness.
  griefprevention:
    creation-denied: <red>You don't have permission to create shops in this claim.
    trade-denied: <red>You don't have permission to purchase shops in this claim.
  lands:
    world-not-enabled: <red>You are not allowed to create or purchase in this world.
    creation-denied: <red>You don't have permission to create shops in this Land area.
  plotsquared:
    no-plot-whitelist-creation: <red>You cannot create shop outside of Plot.
    no-plot-whitelist-trade: <red>You cannot purchase shop outside of Plot.
    creation-denied: <red>You don't have permission to create shops in this Plot.
    trade-denied: <red>You don't have permission to purchase shops in this Plot.
    flag:
      create: Create QuickShop-Hikari shops
      trade: Purchase QuickShop-Hikari shops
  superiorskyblock:
    owner-create-only: <red>Only island owner can create shop there.
    owner-member-create-only: <red>Only island owner or member can create shop there.
  worldguard:
    creation-flag-test-failed: <red>You don't have permission create shop in this WorldGuard area.
    trade-flag-test-failed: <red>You don't have permission trade shop in this WorldGuard area.
    reached-per-region-amount-limit: "<red>You have reached the maximum amount of shops in this region."
  discord:
    __to_translators: 'To translators: `addon.discord.discord-messages` use a special variable in strings, %%LANG_KEY%% instead of QuickShop''s style {LANG_INDEX}, do not translate anything inside double ''%'' section. BUT for any other section texts, it respect the normal QuickShop Text System syntax ({0},{1} etc.) This may be difficult to understand because two different variable forms are used in the same program at the same time, but trust me, it is reasonable. (you don''t need translate this string, it''s a comment)'
    __to_users: Like the QuickShop Text System, Discord addon will also auto-detect user language and use user's language to send Discord message, it following the QuickShop-Hikari's language system settings.
    __to_message_designer: 'Design your Discord message with GUI: https://glitchii.github.io/embedbuilder/, then copy the JSON code and paste it into the translation and here we go!'
    discord-enabled: <aqua>Successfully <green>enabled</green> your QuickShop discord messages, now you can receive the shop messages from Discord.
    discord-disabled: <aqua>Successfully <red>disabled</red> your QuickShop discord messages, now you won't receive the shop messages from Discord anymore.
    discord-not-integrated: <red>You hadn't link your Discord yet! Please link your Discord account first!
    feature-enabled-for-user: <aqua>You have <green>enabled</green> <gold>{0}</gold> notification.
    feature-disabled-for-user: <aqua>You have <red>disabled</red> <gold>{0}</gold> notification.
    link-help: <yellow>This server using <gold>{0}</gold> for Discord driver, please use <green>{0}</green> to link your Discord account.
    save-notifaction-exception: <red>An error occurred while saving your Discord notifaction settings, please contact the server administrator.
    feature-status-changed: <green>Successfully set the notifaction <aqua>{0}</aqua> status to <gold>{1}</gold>
    commands:
      discord:
        description: <yellow>Managing your QuickShop Discord settings
    discord-messages:
      sold-to-your-shop: |
        {
         "embed":
           {
             "title": ":inbox_tray: Somebody sold items to your shop",
             "description": "The player %%purchase.name%% sold x%%purchase.amount%% %%shop.item.name%% to your shop.",
             "color": 52084,
             "author": {
               "name": "QuickShop-Hikari",
               "icon_url": ""
             },
             "thumbnail": {
               "url": ""
             },
             "image": {
               "url": ""
             },
             "footer": {
               "text": "QuickShop-Hikari Discord Notification",
               "icon_url": ""
             },
             "fields": [
               {
                 "name": "Shop",
                 "value": "%%shop.display-name%%",
                 "inline": false
               },
               {
                 "name": "Purchaser",
                 "value": "%%purchase.name%%",
                 "inline": true
               },
               {
                 "name": "Item",
                 "value": "%%shop.item.name%%",
                 "inline": true
               },
               {
                 "name": "Amount",
                 "value": "%%purchase.amount%% (x%%shop.stacking-amount%%)",
                 "inline": true
               },
               {
                 "name": "Your paid",
                 "value": "%%purchase.balance-formatted%%",
                 "inline": true
               },
               {
                 "name": "Taxes",
                 "value": "%%purchase.taxes-formatted%%",
                 "inline": true
               }
             ]
           }
        }
      bought-from-your-shop: |
        {
           "embed":
             {
               "title": ":outbox_tray: Somebody bought items from shop",
               "description": "The player %%purchase.name%% bought x%%purchase.amount%% %%shop.item.name%% from your shop.",
               "color": 52084,
               "author": {
                   "name": "QuickShop-Hikari",
                   "icon_url": ""
               },
               "thumbnail": {
                   "url": ""
               },
               "image": {
                   "url": ""
               },
               "footer": {
                   "text": "QuickShop-Hikari Discord Notification",
                   "icon_url": ""
               },
               "fields": [
                   {
                       "name": "Shop",
                       "value": "%%shop.display-name%%",
                       "inline": false
                   },
                   {
                       "name": "Purchaser",
                       "value": "%%purchase.name%%",
                       "inline": true
                   },
                   {
                       "name": "Item",
                       "value": "%%shop.item.name%%",
                       "inline": true
                   },
                   {
                       "name": "Amount",
                       "value": "%%purchase.amount%%(x%%shop.stacking-amount%%)",
                       "inline": true
                   },
                   {
                       "name": "Your earn",
                       "value": "%%purchase.balance-formatted%%",
                       "inline": true
                   },
                   {
                       "name": "Taxes",
                       "value": "%%purchase.taxes-formatted%%",
                       "inline": true
                   }
               ]
           }
         }
      mod-shop-purchase: |
        {
        "embed":
          {
              "title": ":dollar: Somebody purchased in a shop",
              "description": "The player %%purchase.name%% purchase x%%purchase.amount%% %%shop.item.name%% from the shop.",
              "color": 6061450,
              "author": {
                  "name": "QuickShop-Hikari",
                  "icon_url": ""
              },
              "thumbnail": {
                  "url": ""
              },
              "image": {
                  "url": ""
              },
              "footer": {
                  "text": "QuickShop-Hikari Discord Notification",
                  "icon_url": ""
              },
              "fields": [
                  {
                      "name": "Shop",
                      "value": "%%shop.display-name%%",
                      "inline": false
                  },
                  {
                      "name": "Purchaser",
                      "value": "%%purchase.name%%",
                      "inline": true
                  },
                  {
                      "name": "Item",
                      "value": "%%shop.item.name%%",
                      "inline": true
                  },
                  {
                      "name": "Amount",
                      "value": "%%purchase.amount%% (x%%shop.stacking-amount%%)",
                      "inline": true
                  },
                  {
                      "name": "Balance",
                      "value": "%%purchase.balance-formatted%%",
                      "inline": true
                  },
                  {
                      "name": "Taxes",
                      "value": "%%purchase.taxes-formatted%%",
                      "inline": true
                  }
              ]
          }
        }
      out-of-space: |
        {
        "embed":
          {
              "title": ":mailbox_with_mail: Your shop has run out of space",
              "description": "Your shop inventory is full of items!\nYou need empty your shop container to free the space then you can continue to accept new items.",
              "color": 16065893,
              "author": {
                  "name": "QuickShop-Hikari",
                  "icon_url": ""
              },
              "thumbnail": {
                  "url": ""
              },
              "image": {
                  "url": ""
              },
              "footer": {
                  "text": "QuickShop-Hikari Discord Notification",
                  "icon_url": ""
              },
              "fields": [
                  {
                      "name": "Shop",
                      "value": "%%shop.display-name%%",
                      "inline": false
                  }
              ]
          }
        }
      out-of-stock: |
        {
            "embed":
                {
                    "title": ":mailbox_with_no_mail: Your shop has run out of stock",
                    "description": "Your shop inventory is empty now!\nYou need to refill the shop container with items to continue selling items!",
                    "color": 16065893,
                    "author": {
                        "name": "QuickShop-Hikari",
                        "icon_url": ""
                    },
                    "thumbnail": {
                        "url": ""
                    },
                    "image": {
                        "url": ""
                    },
                    "footer": {
                        "text": "QuickShop-Hikari Discord Notification",
                        "icon_url": ""
                    },
                    "fields": [
                        {
                            "name": "Shop",
                            "value": "%%shop.display-name%%",
                            "inline": false
                        }
                    ]
                }
        }
      mod-shop-created: |
        {
          "embed": {
            "title": ":classical_building: A new shop has been created",
            "description": "Player created a new shop on your server!",
            "color": 6061450,
            "author": {
              "name": "QuickShop-Hikari",
              "icon_url": ""
            },
            "thumbnail": {
              "url": ""
            },
            "image": {
              "url": ""
            },
            "footer": {
              "text": "QuickShop-Hikari Discord Notification",
              "icon_url": ""
            },
            "fields": [
              {
                "name": "Shop",
                "value": "%%shop.display-name%%",
                "inline": false
              },
              {
                "name": "Owner",
                "value": "%%shop.owner.name%%",
                "inline": true
              },
              {
                "name": "Item",
                "value": "%%shop.item.name%%",
                "inline": true
              },
              {
                "name": "Amount",
                "value": "%%shop.stacking-amount%%",
                "inline": true
              },
              {
                "name": "Type",
                "value": "%%shop.type%%",
                "inline": true
              }
            ]
          }
        }
      mod-remove-shop: |
        {
            "embed": {
                "title": ":recycle: A shop was removed from this server",
                "description": "A shop was removed from this server.",
                "color": 6061450,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "Reason",
                        "value": "%%delete.reason%%",
                        "inline": false
                    }
                ]
            }
        }
      shop-transfer-to-you: |
        {
            "embed": {
                "title": ":placard: A shop was transfer to you",
                "description": "A shop transfer to you from another player.",
                "color": 16172079,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "From",
                        "value": "%%transfer.from%%",
                        "inline": false
                    }
                ]
            }
        }
      mod-shop-transfer: |
        {
            "embed": {
                "title": ":placard: A shop was transferred",
                "description": "A shop transfer to a player from another player.",
                "color": 6061450,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "From",
                        "value": "%%transfer.from%%",
                        "inline": true
                    },
                    {
                        "name": "To",
                        "value": "%%transfer.to%%",
                        "inline": true
                    }
                ]
            }
        }
      shop-price-changed: |
        {
            "embed": {
                "title": ":money_with_wings: Your shop price was changed",
                "description": "You or your shop staff changed your shop price.",
                "color": 16172079,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "From",
                        "value": "%%change-price.from%%",
                        "inline": true
                    },
                    {
                        "name": "To",
                        "value": "%%change-price.to%%",
                        "inline": true
                    }
                ]
            }
        }
      mod-shop-price-changed: |
        {
            "embed": {
                "title": ":money_with_wings: A shop price has been changed",
                "description": "A shop changed its price settings.",
                "color": 6061450,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": true
                    },
                    {
                        "name": "Owner",
                        "value": "%%shop.owner.name%%",
                        "inline": true
                    },
                    {
                        "name": "",
                        "value": "",
                        "inline": true
                    },
                    {
                        "name": "From",
                        "value": "%%change-price.from%%",
                        "inline": true
                    },
                    {
                        "name": "To",
                        "value": "%%change-price.to%%",
                        "inline": true
                    }
                ]
            }
        }
      shop-permission-changed: |
        {
            "embed": {
                "title": ":closed_lock_with_key: Your shop permission settings has been changed",
                "description": "One of your shop's permission setting has been changed.",
                "color": 15879747,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "Player",
                        "value": "%%change-permission.player%%",
                        "inline": true
                    },
                    {
                        "name": "Assign to group",
                        "value": "%%change-permission.to-group%%",
                        "inline": true
                    },
                    {
                        "name": "",
                        "value": "",
                        "inline": true
                    },
                    {
                        "name": "Permission granted (Inherit from group)",
                        "value": "```\n%%change-permission.perms-list%%\n```",
                        "inline": false
                    }
                ]
            }
        }
    field:
      player: Player
      item: Item
      amount: Amount
      balance: Balance
      balance-after-tax: Balance (after tax)
      account: Your account balance
      taxes: Taxes
      cost: Cost
  discount:
    commands:
      discount:
        description: <yellow>Apply a discount code or manage your discount codes.
    tab-complete:
      discount:
        general:
          code: <code>
        create:
          rate: |
            Command Hint:
            Argument: <rate>
            Description: The actual percentage or money you will earn
            Input `30%` = price * 0.3
            Input `50` = price - 50
          max-usage: |
            Command Hint:
            Argument: [max-usage]
            Description: The times that code can be used
            `-1` for unlimited.
          threshold: |
            Command Hint:
            Argument: [threshold]
            Description: The minimum price that code can be applied
            `-1` for unlimited
          expired: |
            Command Hint
            Argument: [expired]
            Description: The code expired time.
            `-1` for unlimited duration.
            Accept both Zulu time and UNIX timestamp in seconds.
            Zulu Example: 2022-12-17T10:31:37Z
            UNIX Example: 1671273097
    discount-code-already-exists: <red>Sorry, the name of your discount code already in use.
    invalid-discount-code-regex: '<red>The discount code must matches the regex: <yellow>{0}</yellow>'
    invalid-discount-code: <red>This discount code are invalid.
    discount-code-added: <green>Your discount code <yellow>{0}</yellow> has been added into shop <aqua>{1}</aqua>
    discount-code-removed: <green>Your discount code <yellow>{0}</yellow> has been removed from shop <aqua>{1}</aqua>
    invalid-code-type: <red>The code type <yellow>{0}</yellow> are invalid.
    invalid-usage-restriction: <red>The usage restriction <yellow>{0}</yellow> are invalid.
    invalid-threshold-restriction: <red>The threshold restriction <yellow>{0}</yellow> are invalid.
    invalid-effect-scope: <red>The scope <yellow>{0}</yellow> are invalid.
    invalid-expire-time: <red>You can't specify a time in the past.
    invalid-discount-rate: <red>The discount rate <yellow>{0}</yellow> are invalid, it can be a fixed number or percentage.
    discount-code-expired: <red>Ouch! Your discount code <yellow>{0}</yellow> has been expired!
    discount-code-installed: <green>You have installed discount code <gold>{0}</gold>, the discount code will be automatically applied to the available purchase in all the following purchases during this session. To uninstall discount code, execute <aqua>/qs discount uninstall {0}</aqua>.
    discount-code-uninstalled: <green>You have uninstalled your discount code.
    discount-code-query-nothing: <red>You hadn't install a discount code yet!
    discount-code-query: '<green>You are using discount code: <yellow>{0}</yellow>.'
    discount-code-applicable: '<color:#bcef26>Your discount code <yellow><bold>{0}</bold></yellow> are <bold>applicable</bold> in this shop!'
    discount-code-applicable-with-threshold: '<color:#bcef26>Your discount code <yellow><bold>{0}</bold></yellow> are <bold>applicable</bold> in this shop, but only your purchase cost over <yellow>{1}</yellow> in single transaction.'
    discount-code-not-applicable: <red>Your discount code <yellow><bold>{0}</bold></yellow> are <bold>not applicable</bold> in this shop!
    discount-code-reach-the-limit: <red>You have reach the usage limit of discount code <yellow><bold>{0}</bold></yellow>, discount are not applied.
    discount-code-no-permission: <red>You didn't have permission to use any discount code in this shop!
    discount-code-has-been-expired: <red>Your discount has been expired!
    discount-code-config-shop-added: <green>Successfully to add this shop into your discount code allow list.
    discount-code-config-shop-add-failure: <red>This shop already in your discount code allow list.
    discount-code-config-shop-removed: <green>Successfully to remove this shop from your discount code allow list.
    discount-code-config-shop-remove-failure: <red>This shop not exists in your discount code allow list.
    discount-code-config-expire: <green>Successfully changed your code expire time.
    discount-code-config-applied: <green>Config the discount code successfully!
    discount-code-created-successfully: |
      <green>Your discount code <yellow>{0}</yellow> has been successfully created!
      <gold>Scope: <aqua>{1}</aqua></gold>.
      <gold>Share with others: <color:#bcef26>{2}</color:#bcef26></gold>.
      <yellow>For <gray>Specific Shops</gray> scope only: To add shops to your discount code allow list, please looking an quickshop and execute <aqua>{3}</aqua> command.
      <yellow>You can use <aqua>/qs discount config {0}</aqua> to edit your discount code anytime.
    discount-code-under-threshold: <red>The discount not applied to your purchase because the total value under the discount code threshold <yellow>{0}</yellow>.
    percentage-off: '<bold><color:#bcef26>-{0}%</color:#bcef26></bold>'
    fixed-off: '<bold><color:#bcef26>-{0}</color:#bcef26></bold>'
    discount-code-list: '<gold>Listing your discount codes:</gold>'
    discount-code-applied-in-purchase: '<color:#bcef26>Your discount code <yellow>{0}</yellow> has been applied to this purchase and you saved <gold>{1}</gold>!'
    scope:
      this-shop: '{0}'
      your-shops-owned: all your shops (owned)
      your-shops-managed: all your shops (managed)
      server: whole server
    code-type:
      SERVER_ALL_SHOPS: All shops on this server
      PLAYER_ALL_SHOPS: All shops that created by code owner
      SPECIFIC_SHOPS: Specific shops
    discount-code-details: |-
      <gold>Code: <yellow>{0}</yellow>
      <gold>Creator: <yellow>{1}</yellow>
      <gold>Applied to: <yellow>{2}</yellow>
      <gold>Remaining usage: <yellow>{3}</yellow>
      <gold>Expired on: <yellow>{4}</yellow>
      <gold>Threshold: <yellow>{5}</yellow>
      <gold>Discount: <yellow>{6}</yellow>
  list:
    commands:
      list: <yellow>Listing all shops owned by your or specific player
    table-prefix: '<yellow><green>{0}</green>''s shops <gray>(Total {1})</gray>: '
    table-prefix-pageable: '<yellow><green>{0}</green>''s shops <gray>(Page {1}/{2})</gray>: '
    entry: "<hover:show_text:'<yellow>{1}</yellow>\n<gray>{2} {3},{4},{5}</gray>\n<aqua>Price <yellow>{6}</yellow> - <light_purple>x{7}</light_purple> <yellow>{8}</yellow>\n<green>{9}'><yellow>{0}. <aqua>{1}</aqua></hover>"
  shopitemonly:
    message: <red>You can't put non-shop items into shop container, all non-shop items will dropped at your location.
  limited:
    commands:
      limit: <yellow>Set a limit that restrict the purchase that player can make in period
    titles:
      title: <green>Purchase Success
      subtitle: <aqua>You can also purchase <gold>{0}</gold> more in this shop
    reach-the-limit: <red>You have reached the limit in this shop. You can also purchase <green>{0}</green> items, but you trying purchase <yellow>{1}</yellow> items.
    success-reset: <green>Reset the restriction of this shop successfully
    success-remove: <green>Successfully remove the all restriction of this shop
    success-setup: <green>Restriction settings for this shop were saved successfully
    trade-limit-reached-cancel-reason: <red>Reached the restriction of this shop
    remains-limits: '<gold>Your remaining purchase quota in this shop: <yellow>{0}</yellow>'
    remains-limits-before-reset-period-note: '<gold>This shop will reset their purchase quota every: <yellow>{0}</yellow>'
  dynmap:
    markerset-title: QuickShop-Hikari Shops
    marker-name: '{0} @ {2} @ {3} for x{4} @ {5}'
    marker-description: |
      Name: {0}
      Owner: {1}
      Item: {2}
      Price: {3} for x{4} item(s)
      Type: {5}
      Unlimited: {6}
      Location: {7}
  bluemap:
    markerset-title: QuickShop-Hikari Shops
    marker-name: '{0} @ {2} @ {3} for x{4} @ {5}'
    marker-description: |
      Name: {0}
      Owner: {1}
      Item: {2}
      Price: {3} for x{4} item(s)
      Type: {5}
      Unlimited: {6}
      Location: {7}
  chestprotect:
    protection-exists: <red>This region already protected by ChestProtect and you don't have permission to create shops at here.
    shops-exsts: <red>The region that you want to protect contains other player's shops and you don't have permission to access them.
  displaycontrol:
    toggle: |-
      <green>Successfully toggle your QuickShop display showcasing to <aqua>{0}</aqua>.
      <yellow>You may need re-join to take effect.
    toggle-exception: <red>Failed to toggle your display showcase settings due an internal error, please contact server administrator.
    command:
      displaycontrol: <yellow>Toggle your QuickShop display showcasing
  reremake-migrator:
    commands:
      migratefromreremake: Migrate QuickShop-Reremake's data to QuickShop-Hikari
    server-not-empty: "<red>No players are allowed to be online on the server during the conversion progress, please enable server whitelist or maintenance mode."
    starting-convert-progress: "<gold>Starting the convert progress, <red>DO NOT SHUTDOWN YOUR SERVER!</red>"
    executing: "<gold>Executing migrate component <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
    completed: "<green>Done! Migrate completed, please restart your server and remove QuickShop-Reremake from plugins directory."
    join_blocking_converting: "<red>[QuickShop-Hikari Migrator] This server is performing a data conversion and you cannot join the server at this time, try again later!"
    join_blocking_finished: "<red>[QuickShop-Hikari Migrator] This server has just completed a data conversion and is waiting for a reboot to apply the changes, try again later!"
    failed: "<red>Migration progress exited with error, please check the server console."
    modules:
      config:
        copy-values: "<yellow>Copying values (total {0} entries)..."
        copying-value: "<gray> - Copying <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        migrate-price-restriction: "<yellow>Migrating price-restriction related settings..."
        migrate-price-restriction-entry: "<gray> - Migrating <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
      shop:
        start-migrate: "<yellow>Migrating shops (total {0} entries)..."
        migrate-entry: "<gray> - Migrating <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        unloading-reremake: "<gold>Unloading Reremake to avoid data overwrite..."
        register-entry: "<gray> - Registering <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        save-entry: "<gray> - Saving <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        saving-shops: "<yellow>Saving <gold>{0}</gold> shops, this may need a while™ (the time based on shops amounts)..."
        conflict: "<gray> - CONFLICT > Detects a conflict between a Reremake store and an existing Hikari store location, taking a predefined behavior: <dark_gray>{0}</dark_gray>"
      translation:
        start-migrate: "<yellow>Migrating translation files...</yellow>"
        copy-values: "<yellow>Copying values in locale <gold>{0}</gold> (total {1} entries)..."
        migrate-entry: "<gray> - Migrating <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        copying-value: "<gray> - Copying <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
      shop-logs:
        start-migrate: "<yellow>Migrating shop logs (details in console), please wait...</yellow>"
        extract-history-files: "<gray>Please wait for us to unzip and append the history logs..."
        filter-history-files: "<gray>Please wait for us to filter the history log..."
        filtered-history-files: "<gray>Filtered {0} lines out of queue."
        import-entry: "<gray> - Migrating <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
compat:
  advancedchests:
    created: <green>You're successfully created a shop on AdvancedChests container!
    permission-denied: <red>Sorry! You don't have the permission to create a shop on a AdvancedChests container!
suite:
  dynmap:
    title: '{0} - {1} - {2}'
    description: |-
      Item: {0}
      Owner: {1}
      Type: {2} {3}
      Price: {4}
      Location: {5} {6}, {7}, {8}
      Space: {9}
      Stock: {10}
  limited:
    command-description: <yellow>Set a limit that restrict the purchase that player can make in period.
    reach-the-quota-limit: <red>You have reach the purchase limit in this shop ({0}/{1}).
    quota-reset-countdown: <yellow>The quota in this shop will reset in {0}.
    quota-reset-player-successfully: <green>The quota for player {0} in this shop has been reset successfully.
    quota-reset-everybody-successfully: <green>The quota for everyone in this shop has been reset successfully.
    quota-setup: <green>The purchase restriction now apply to this shop!
    quota-remove: <green>The purchase restriction now removed from this shop!
    subtitles:
      title: <green>Purchase Success
      subtitle: <aqua>You can purchase <yellow>{0}</yellow> more in this shop
  list:
    command-description: <yellow>List all shops owned by yourself or another player.
    table-prefix: <yellow>You owned total <aqua>{0}</aqua> shops on this server.
    entry-syntax: <aqua>- <yellow>{0}
    entry-syntax-fallback-name: '<yellow>Item:{0} X:{1}, Y:{2}, Z:{3}, World: {4}'
    hover-text: |-
      <yellow>{0}
      <gray>{1} {2}, {3}, {4}
      <aqua>{5} - <yellow>{6}
      <yellow>{7} items each stack.
  shopitemonly:
    message: <red>You can't put non-shop items into shop container, all non-shop items will dropped at your location.
compatibility:
  elitemobs:
    soulbound-disallowed: You can't trade item which have EliteMobs Soulbound enchantment.
internet-paste-forbidden-privacy-reason: "<red>Failed! According your privacy setting, QuickShop-Hikari are not allowed upload your paste to Internet, turn on DIAGNOSTIC permission in privacy setting in config.yml or use <aqua>/quickshop paste --file</aqua> instead."
no-sign-type-given: "<red>You need give a sign material, the sign materials that available on this server: {0}"
sign-type-invalid: "<red>The type <yellow>{0}</yellow> not a valid sign material."
delete-controlpanel-button-confirm: "<red>Do you really want remove this shop? Click the <bold>[Remove Shop]</bold> button again in {0} seconds to confirm."
cannot-suggest-price: "<red>Sorry, For the time being, no more people are trading the same item as you and there is not enough data to generate a suggested price."
price-suggest: "<green>Based on data from <aqua>{0}</aqua> stores, the highest priced store is priced at <light_purple>{1}</light_purple>, the lowest priced store is priced at <light_purple>{2}</light_purple>, the average price is <light_purple>{3}</light_purple>, and the median price is <light_purple>{4}</light_purple>. <newline><yellow>It's recommended that you set your price around <gold>{5}</gold>.</yellow>"
suggest-wait: "<green>Please wait... Calculating recommend price."
history:
  shop:
    gui-title: "View purchase history"
    header-icon-multiple-shop: "<white>The query result across {0} shops</white>"
    header-icon-description:
      - "<white>Type: <yellow>{0}</yellow></white>"
      - "<white>Owner: <yellow>{1}</yellow></white>"
      - "<white>Item: <yellow>{2}</yellow></white>"
      - "<white>Price: <yellow>{3} <aqua>x{4}</aqua></yellow></white>"
      - "<white>Location: <yellow>{5}</yellow></white>"
    log-icon-title: "<reset><green>Time: {0}</green>"
    log-icon-description:
      - "<white>Purchaser: <yellow>{0}</yellow></white>"
      - "<white>Item: <yellow>{1} <aqua>x{2}</aqua></yellow></white>"
      - "<white>Balance: <yellow>{3}</yellow></white>"
      - "<white>Tax: <yellow>{4}</yellow></white>"
    log-icon-description-with-store-name:
      - "<white>Store: <yellow>{0}</yellow></white>"
      - "<white>Purchaser: <yellow>{1}</yellow></white>"
      - "<white>Item: <yellow>{2} <aqua>x{3}</aqua></yellow></white>"
      - "<white>Balance: <yellow>{4}</yellow></white>"
      - "<white>Tax: <yellow>{5}</yellow></white>"
    query-icon: "<gray>Please wait, querying...</gray>"
    previous-page: "<white><< Previous Page</white>"
    next-page: "<white>Next Page >></white>"
    current-page: "<white>Page {0}</white>"
    summary-icon-title: "<green>Shop Summary"
    recent-purchases: "<white>Recent <aqua>{0}</aqua> purchases: <yellow>{1}</yellow></white>"
    recent-purchase-balance: "<white>Recent <aqua>{0}</aqua> turnover: <yellow>{1}</yellow></white>"
    total-purchases: "<white>Total purchases: <yellow>{0}</yellow></white>"
    total-balances: "<white>Total turnover: <yellow>{0}</yellow></white>"
    total-unique-purchasers: "<white>Total unique purchasers: <yellow>{0}</yellow></white>"
    top-n-valuable-customers-title: "<gold>Top {0} valuable customers</gold>"
    top-n-valuable-customers-entry: "<white>- <yellow>{0}</yellow> <aqua>{1}</aqua></white>"
    no-result: "<white>No result</white>"
about:
  text:
    - "<b><aqua>QuickShop <yellow>{0}</yellow></aqua></b>"
    - "<aqua>Version <yellow>>></yellow> <green>{1}</green></aqua>"
    - "<aqua>Release <yellow>>></yellow> <green>{2}</green></aqua>"
    - "<aqua>Developers <yellow>>></yellow> <color:#218bff><hover:show_text:'<yellow>Click to check contributors'><click:open_url:'https://github.com/Ghost-chu/QuickShop-Hikari/graphs/contributors'>[View contributors on GitHub]</click></hover></color></aqua>"
    - "<aqua>Localized members <gold>({4})</gold>: <yellow>>></yellow> <green>{5}</green></aqua>"
    - "  <yellow>+ <color:#43A047><hover:show_text:'<yellow>Click to open Crowdin translate page'><click:open_url:'https://crowdin.com/project/quickshop-hikari'>[Help translate on Crowdin]</click></hover></color></yellow>"
    - "<aqua>Donation Key <yellow>>></yellow> <green>{6}</green></aqua>"
    - "<gold>Powered by community</gold> <red>Made with ❤</red>"
  valid-donation-key: "<color:#00AFF1>Bound to <color:#F9C23C>{0}</color></color>"
  invalid-donation-key: "<gray>Invalid donation key</gra>"
  kofi-thanks: "<gold>Special thanks to those who support QuickShop on Ko-fi :)</gold>"
history-command-leave-blank: "<Leave blank for seeing store>"
shop-information-not-shown-due-an-internal-error: "<red>An internal error occurred. The store information panel may be displayed incompletely, please contact the server administrator."
