# of this file and modify that instead. This file will be updated automatically by Residence
# when a newer version is detected, and your changes will be overwritten.  Once you 
# have a copy of this file, change the Language: option under the Residence config.yml
# to whatever you named your copy.

Language:
  Invalid:
    Player: '&cНедійсне ім''я гравця...'
    PlayerOffline: '&cГравець офлайн'
    World: '&cНедійсний світ...'
    Residence: '&cНедійсне місце проживання...'
    Subzone: '&cНедійсна підзона...'
    Direction: '&cНедійсний напрямок...'
    Amount: '&cНедійсна сума...'
    Cost: '&cНедійсна вартість...'
    Days: '&cНедійсна кількість днів...'
    Material: '&cНедійсний матеріал...'
    Boolean: '&cНедійсне значення, має бути &6true(t) &cабо &6false(f)'
    Area: '&cНедійсна область...'
    Group: '&cНедійсна група...'
    MessageType: '&cТип повідомлення має бути enter або remove.'
    Flag: '&cНедійсний прапор...'
    FlagType:
      Fail: '&cНедійсний прапор... Цей прапор можна використовувати лише на %1'
      Player: Гравець
      Residence: Резиденція
    FlagState: '&cНедійсний стан прапора, має бути &6true(t)&c, &6false(f)&c, або &6remove(r)'
    List: '&eНевідомий тип списку, має бути &6blacklist &eабо &6ignorelist.'
    Page: '&eНедійсна сторінка...'
    Help: '&cНедійсна сторінка довідки...'
    NameCharacters: '&cНазва містить недозволені символи...'
    PortalDestination: '&cПункт призначення порталу знаходиться в зоні обмеженого доступу. Створення порталу
      скасовано. &7Знайти нове місце'
    FromConsole: '&cВи можете використовувати це лише в консолі!'
    Ingame: '&cВи можете використовувати це лише в грі!'
    Location: '&cНедійсне розташування...'
  Area:
    Exists: '&cНазва області вже існує.'
    Create: '&eРайон проживання створено, ID &6%1'
    DiffWorld: '&cРайон знаходиться в іншому світі, ніж місце проживання.'
    Collision: '&cТериторія стикається з місцем проживання &6%1'
    TooClose: '&cНадто близько до іншого помешкання. Вам потрібен принаймні &e%1 &cпроміжок блоків.'
    SubzoneCollision: '&cОбласть стикається з підзоною &6%1'
    NonExist: '&cТакої території не існує.'
    InvalidName: '&cНедійсна назва області...'
    ToSmallX: '&cВаша &6X &cдовжина вибору (&6%1&c) замала. &eДозволено &6%2
      &eі більше.'
    ToSmallY: '&cВаша висота виділення (&6%1&c) замала. &eДозволено &6%2 &eі
      більше.'
    ToSmallZ: '&cВаша &6Z &cдовжина виділення (&6%1&c) замала. &eДозволено &6%2
      &eі більше.'
    ToBigX: '&cВаша &6X &cдовжина виділення (&6%1&c) завелика. &eДозволено &6%2 &eі
      менше.'
    ToBigY: '&cВаша висота виділення (&6%1&c) завелика. &eДозволено &6%2 &eі менше.'
    ToBigZ: '&cВаша &6Z &cдовжина виділення (&6%1&c) завелика. &eДозволено &6%2 &eі
      менше.'
    Rename: '&eПерейменовано область &6%1 &eна &6%2'
    Remove: '&eВидалено область &6%1...'
    Name: '&eІм''я: &2%1'
    ListAll: '&a{&eID:&c%1 &eP1:&c(%2,%3,%4) &eP2:&c(%5,%6,%7) &e(Розмір:&c%8&e)&a}'
    RemoveLast: '&cНеможливо видалити останню область у резиденції.'
    NotWithinParent: '&cОбласть не входить до батьківської області.'
    Update: '&eПлоща оновлена...'
    MaxPhysical: '&eВи досягли максимальної фізичної площі, дозволеної для вашого проживання.'
    SizeLimit: '&eРозмір області не входить у ваші дозволені обмеження.'
    HighLimit: '&cВи не можете захистити цю висоту, ваш ліміт становить &6%1'
    LowLimit: '&cВи не можете захистити цю глибину, ваш ліміт становить &6%1'
    WeirdShape: '&3Резиденція не в правильному вигляді. &6%1 &3сторона дорівнює &6%2 &3разів
      більше ніж &6%3 &3сторона'
  Select:
    Points: '&eПерш ніж використовувати цю команду, виберіть дві точки!'
    Overlap: '&cВибрані точки збігаються з &6%1 &cрегіоном!'
    WorldGuardOverlap: '&cВибрані точки збігаються з регіоном &6%1 &cWorldGuard!'
    KingdomsOverlap: '&cВибрані точки збігаються з &6%1 &cKingdoms land!'
    Success: '&eВибір успішний!'
    Fail: '&cНедійсна команда вибору...'
    Bedrock: '&eВибір розширено до найнижчої дозволеної межі.'
    Sky: '&eВибір розширено до максимально допустимої межі.'
    Area: '&eВибрана ділянка &6%1 &eпроживання &6%2'
    Tool: '&e- Інструмент виділення: &6%1'
    PrimaryPoint: '&eРозміщено &6Основну &eТочку вибору %1'
    SecondaryPoint: '&eРозміщено &6Додаткову &eТочку вибору %1'
    Primary: '&eПервинний вибір: &6%1'
    Secondary: '&eВторинний вибір: &6%1'
    TooHigh: '&cПопередження, виділено вище верхньої частини карти, обмеження.'
    TooLow: '&cПопередження, вибір опустився нижче нижньої частини карти, обмеження.'
    TotalSize: '&eЗагальний розмір вибору: &6%1'
    AutoEnabled: '&eРежим автоматичного вибору &6УВІМК&e. Щоб вимкнути його, напишіть &6/res
      select auto'
    AutoDisabled: '&eРежим автоматичного вибору &6ВИМК&e. Щоб увімкнути його, напишіть
      &6/res select auto'
    Disabled: '&cВи не маєте доступу до команд вибору'
  Sign:
    Updated: '&6%1 &eзнаки оновлені!'
    TopLine: '[market]'
    DateFormat: YY/MM/dd HH:mm
    ForRentTopLine: '&0В оренду'
    ForRentPriceLine: '&0%1&f/&0%2&f/&0%3'
    ForRentResName: '&0%1'
    ForRentBottomLine: '&9Доступний'
    RentedAutorenewTrue: '&2%1'
    RentedAutorenewFalse: '&c%1'
    RentedTopLine: '%1'
    RentedPriceLine: '&0%1&f/&0%2&f/&0%3'
    RentedResName: '&0%1'
    RentedBottomLine: '&1%1'
    ForSaleTopLine: '&0На продаж'
    ForSalePriceLine: '&0%1'
    ForSaleResName: '&0%1'
    ForSaleBottom: '&0%1m³'
    LookAt: '&cВи не дивитесь на знак'
    TooMany: '&cЗабагато знаків для цієї резиденції'
    ResName: '&0%1'
    Owner: '&0%1'
  Raid:
    NotEnabled: '&cФункція рейду не ввімкнена!'
    NotIn: '&cВи не в рейді!'
    CantLeave: '&cВи не можете залишити (%1) власну рейдову резиденцію!'
    CantKick: '&cНе можу вигнати (%1) власника резиденції!'
    Kicked: '&eВигнано &7%1 &eз &7%2 &eрейду резиденції!'
    StartsIn: '&7Рейд починається через: [autoTimeLeft] &2%1D &4%2A'
    EndsIn: '&cРейд закінчується через: [autoTimeLeft] &2%1D &4%2A'
    Ended: '&7Рейд на &4%1 &7завершено!'
    cantDo: '&cНе можна це робити під час рейду!'
    left: '&7Ви залишили &4%1 &7рейж'
    noFlagChange: '&cНеможливо змінити прапори під час рейду'
    noRemoval: '&cНеможливо видалити резиденцію під час рейду'
    immune: '&eІмунітет для наступного %1'
    notImmune: '&eБільше немає імунітету'
    notInRaid: '&eГравець не в рейді'
    attack:
      Joined: '&7Приєднався до &2%1 &7рейду!'
      Started: '&7Рейд розпочато!'
      cooldown: '&cЗанадто швидко для нового рейду на цю резиденцію! Зачекайте %1'
      immune: '&cЦя резиденція захищена від рейдів! Зачекайте %1'
      playerImmune: '&cВласник резиденції захищений від рейдерства! Зачекайте %1'
      isOffline: '&cНеможливо провести рейд, поки власник офлайн!'
      noSubzones: '&cНе можна здійснювати набіги на підзони!'
      noSelf: '&cНе можна здійснити набіг на власну резиденцію!'
      alreadyInAnother: '&cНеможливо приєднатися до цього рейду, ви вже в іншому (%1)'
    defend:
      Joined: '&7Приєднався до &2%1 &7оборонних сил!'
      Sent: '&7Заявка на участь у рейдовій обороні відправлена, чекайте підтвердження'
      Join: '&7Приєднуйтеся до &6%1 &7оборони рейду'
      Invitation: '&7Прийняти рейдовий захист від &6%1'
      JoinedDef: '&2%1&7 приєднався до сил оборони!'
      IsOffline: '&cНеможливо приєднатися до команди захисту, поки власник офлайн!'
      noSelf: '&cВи вже захищаєте цю резиденцію'
      notRaided: '&cМісце проживання не під рейдом'
      alreadyInAnother: '&cНе можна приєднатися до цієї резиденційної оборони, ви в іншій
        одній (%1)'
    status:
      title: '&7----------- &f%1(%2) &7-----------'
      immune: '&eІмунітет до рейдів для наступних: %1'
      starts: '&7Рейд починається через: %1'
      attackers: '&7Рейдери: &4%1'
      defenders: '&7Захисники: &4%1'
      ends: '&7Рейд закінчується через: %1'
      canraid: '&2Можна провести рейд'
      raidin: '&eМожливий рейд у: %1'
    stopped: '&eРейд на &6%1 &eзупинено'
  info:
    years: '&e%1 &6років '
    oneYear: '&e%1 &6рік '
    day: '&e%1 &6днів '
    oneDay: '&e%1 &6день '
    hour: '&e%1 &6години '
    oneHour: '&e%1 &6година '
    min: '&e%1 &6хв '
    sec: '&e%1 &6сек '
    listSplitter: ', '
    click: '&7Натисніть'
    clickToConfirm: '&7Натисніть для підтвердження'
  server:
    land: Server_Land
  Flag:
    p1Color: '&2'
    p2Color: '&a'
    haveColor: '&2'
    havePrefix: ''
    lackColor: '&7'
    lackPrefix: ''
    others: '&eта &2%1 &eінші'
    Set: '&eПрапор (&6%1&e) встановлено для &6%2 &eна &6%3 &eстан'
    SetFailed: '&cВи не маєте доступу до &cфлагу &6%1'
    CheckTrue: '&eПрапор &6%1 &eзастосовується для гравця &6%2 &eдля проживання &6%3&e, значення
      = &6%4'
    CheckFalse: '&eПрапор &6%1 &eне стосується гравця &6%2 &eдля проживання.'
    Cleared: '&eПрапори видалено.'
    RemovedAll: '&eУсі флаги видалено для &6%1 &eу &6%2 &eрезиденції.'
    RemovedGroup: '&eУсі позначки видалено для &6%1 &eгрупи у &6%2 &eрезиденції.'
    Default: '&eПрапори встановлені за замовчуванням.'
    Deny: '&cУ вас немає &6%1 &cдозвола<ів> тут.'
    SetDeny: '&cВласник не має доступу до прапора &6%1'
    ChangeDeny: '&cВи не можете змінити стан прапора &6%1 &cпоки є &6%2 &cгравець(і)
      всередині.'
    ChangedForOne: '&eЗмінено прапор &6%1 &eдля резиденції &6%2'
    ChangedFor: '&eЗмінено &6%1 &eфлаг з &6%2 &eрезиденцій'
    reset: '&eСкинути прапори для &6%1 &eрезиденції'
    resetAll: '&eСкинути всі прапори для &6%1 &eрезиденції'
  Bank:
    NoAccess: '&cУ вас немає доступу до банку.'
    Name: ' &eБанк: &6%1'
    NoMoney: '&cНедостатньо грошей у банку.'
    Deposit: '&eВи вносли &6%1 &eв резидентний банк.'
    Withdraw: '&eВи зняли &6%1 &eіз резидентного банку.'
    rentedWithdraw: '&cНе можна вийти з банку орендованого житла.'
    full: '&eБанк резиденції заповнений!'
  Subzone:
    Rename: '&eПерейменовано підзону &6%1 &eна &6%2'
    Remove: '&eПідзону &6%1 &eвидалено.'
    Create: '&eСтворено підзону &6%1'
    CreateFail: '&cНе вдалося створити підзону &6%1'
    Exists: '&cПідзона &6%1 &cвже існує.'
    Collide: '&cПідзона стикається з підзоною &6%1'
    MaxAmount: '&cВи досягли максимально дозволеної кількості підзон для цієї резиденції.'
    MaxDepth: '&cВи досягли максимально дозволеної глибини підзони.'
    SelectInside: '&eОбидві точки відбору повинні бути всередині резиденції.'
    CantCreate: '&cВи не маєте дозволу на створення підзони резиденції.'
    CantDelete: '&cУ вас немає дозволу на видалення підзони резиденції.'
    CantDeleteNotOwnerOfParent: '&cВи не є власником батьківської резиденції, щоб видалити
      підзону.'
    CantContract: '&cВи не маєте дозволу на укладення договору підзони проживання.'
    CantExpand: '&cВи не маєте дозволу на розширення підзони проживання.'
    DeleteConfirm: '&eВи впевнені, що хочете видалити підзону &6%1&e, використовуйте &6/res confirm
      &eщоб підтвердити.'
    OwnerChange: '&eВласника підзони &6%1 &eзмінено на &6%2'
  Residence:
    DontOwn: '&eНічого показати'
    Hidden: ' &e(&6Прихований&e)'
    Bought: '&eВи купили резиденцію &6%1'
    Buy: '&6%1 &eкупив у вас &6%2 &eрезиденцію.'
    BuyTooBig: '&cЦя резиденція має площу, яка перевищує ваш дозволений максимум.'
    NotForSale: '&cРезиденція не продається.'
    ForSale: '&eРезиденція &6%1 &eзараз продається за &6%2'
    StopSelling: '&cРезиденція більше не продається.'
    TooMany: '&cВи вже володієте максимальною дозволеною кількістю резиденцій.'
    MaxRent: '&cВи вже орендуєте максимальну дозволену кількість резиденцій
      .'
    AlreadyRent: '&cРезиденція вже здана...'
    NotForRent: '&cЖитло не здається...'
    NotForRentOrSell: '&cРезиденція не здається чи не продається...'
    NotRented: '&cЖитло не здається.'
    Unrent: '&eРезиденція &6%1 &eне здана в оренду.'
    RemoveRentable: '&eРезиденцію &6%1 &eбільше не можна орендувати.'
    ForRentSuccess: '&eРезиденція &6%1 &eзараз здається в оренду за &6%2 &eкожні &6%3 &eдні.'
    RentSuccess: '&eВи орендували резиденцію &6%1 &eна &6%2 &eдні.'
    EndingRent: '&eТермін оренди закінчується для &6%1 &eна &6%2'
    AlreadyRented: '&eРезиденція &6%1 &eнаразі здана в оренду &6%2'
    CantAutoPay: '&eРезиденція не дозволяє автоматичну оплату, для неї буде встановлено значення &6false'
    AlreadyExists: '&cРезиденція під назвою &6%1 &cвже існує.'
    Create: '&eВи створили резиденцію &6%1&e!'
    Rename: '&eПерейменовано резиденцію &6%1 &eна &6%2'
    Remove: '&eРезиденцію &6%1 &eбуло видалено...'
    CantRemove: '&cРезиденцію &6%1 &cнеможливо видалити, оскільки підзона &6%2 &cвсе ще орендована
      гравцем &6%3'
    MoveDeny: '&cВи не маєте дозволу на пересування по Резиденції &6%1'
    TeleportNoFlag: '&cУ вас немає доступу до телепорту до цієї резиденції.'
    FlagDeny: '&cВи не маєте &6%1 &cна &6%2 &cрезиденцію'
    GiveLimits: '&cНеможливо надати місце проживання цільовому гравцеві, оскільки воно виходить за межі
      цільових гравців.'
    GiveConfirm: '&7Натисніть, щоб підтвердити &6%1 &7перенесення резеденція &6%2 &7на &6%3'
    Give: '&eВи надаєте резиденцію &6%1 &eгравцеві &6%2'
    Recieve: '&eВи отримали резиденцію &6%1 &eвід гравця &6%2'
    ResList: ' &a%1. &e%2 &e- &6%3 %4&6%5'
    TrustedResList: ' &a%1. &f%2 &e- &6%3 %4&6%5'
    List: ' &e%2 &e- &6%3'
    Near: '&eНайближчі резиденції: &7%1'
    TeleportNear: '&eТелепортовано до найближчої резиденції.'
    SetTeleportLocation: '&eВстановлено місцезнаходження телепорту...'
    PermissionsApply: '&eДозволи на проживання.'
    NotOwner: '&cВи не власник цієї резиденції'
    RemovePlayersResidences: '&eВидалено всі помешкання гравця &6%1'
    NotIn: '&cВи не в резиденції.'
    PlayerNotIn: '&cГравець, який стоїть не в зоні вашої резиденції.'
    Kicked: '&eВас вигнали з місця проживання'
    CantKick: '&eНе можна вигнати цього гравця'
    In: '&eВи стоїте в Резиденції &6%1'
    OwnerChange: '&eРезиденцію &6%1 &eвласника змінено на &6%2'
    NonAdmin: '&cВи не є адміністратором резиденції.'
    Line: '&eРезиденція: &6%1 '
    RentedBy: '&eЗдано в оренду: &6%1'
    MessageChange: '&eНабір повідомлень...'
    CantDeleteResidence: '&cВи не маєте дозволу на видалення місця проживання.'
    CantExpandResidence: '&cВи не маєте дозволу на розширення місця проживання.'
    CantContractResidence: '&cВи не маєте дозволу на проживання за договором.'
    NoResHere: '&cЖодної резиденції там немає.'
    OwnerNoPermission: '&cВласник не має на це дозволу.'
    ParentNoPermission: '&cВи не маєте дозволу вносити зміни в батьківський елемент
      зони.'
    ChatDisabled: '&eЧат проживання вимкнено...'
    DeleteConfirm: '&eВи впевнені, що хочете видалити місце проживання &6%1&e, використайте &6/res
      confirm &eщоб підтвердити.'
    ChangedMain: '&eЗмінено основне місце проживання на &6%1'
    LwcRemoved: '&eВидалено захист &6%1 &eLwc через &6%2ms'
    CanBeRented: '&6%1&e можна орендувати за &6%2 &eза &6%3 &eдні. &6/res market rent'
    CanBeBought: '&6%1&e можна купити за &6%2&e. &6/res market buy'
    IsForRent: '&6(В оренду)'
    IsForSale: '&6(На продаж)'
    IsRented: '&6(Зданий в оренду)'
  Rent:
    Disabled: '&cОренда відключена...'
    DisableRenew: '&eРезиденцію &6%1 &eбільше не можна повторно орендувати після закінчення терміну.'
    EnableRenew: '&eРезиденція &6%1 &eтепер автоматично повторно орендуватиметься після закінчення терміну.'
    NotByYou: '&cРезиденцію знімаєте не ви.'
    isForRent: '&2Можлива оренда помешкання.'
    MaxRentDays: '&cВи можете орендувати більше ніж на &6%1 &cднів одночасно.'
    OneTime: '&cНеможливо продовжити термін оренди цього житла.'
    Extended: '&eОренду продовжено ще на &6%1 &eднів для &6%2 &eрезиденції'
    Expire: '&eТермін дії оренди: &6%1'
    AutoPayTurnedOn: '&eАвтоматичну оплату &2УВІМК'
    AutoPayTurnedOff: '&eАвтоматичну оплату &cВИМК'
    ModifyDeny: '&cНе можна змінити орендоване житло.'
    Days: '&eДні оренди: &6%1'
    Rented: ' &6(Зданий в оренду)'
    RentList: ' &6%1&e. &6%2 &e(&6%3&e/&6%4&e/&6%5&e) - &6%6 &6%7'
    EvictConfirm: '&eНапишіть &6/res market confirm &eвиселити орендаря з &6%1 &eпомешкання'
    UnrentConfirm: '&eНапишіть &6/res market confirm &eзняти в оренду &6%1 &eпомешкання'
    ReleaseConfirm: '&eНапишіть &6/res market confirm &eвидалити &6%1 &eмісце проживання
      з ринку'
  command:
    addedAllow: '&eДодано нову дозволену команду для &6%1 &eмісця проживання'
    removedAllow: '&eВидалено дозволену команду для &6%1 &eмісця проживання'
    addedBlock: '&eДодано нову заблоковану команду для &6%1 &eмісця проживання'
    removedBlock: '&eВидалено заблоковану команду для &6%1 &eмісця проживання'
    Blocked: '&eЗаблоковані команди: &6%1'
    Allowed: '&eДозволені команди: &6%1'
    Parsed: '%1'
    PlacehlderList: '&e%1. &6%2'
    PlacehlderResult: ' &eрезультат: &6%1'
  Rentable:
    Land: '&eОрендна земля: &6'
    AllowRenewing: '&eМожна поновити: &6%1'
    StayInMarket: '&eОрендуване перебування на ринку: &6%1'
    AllowAutoPay: '&eRentable дозволяє автоматично оплачувати: &6%1'
    DisableRenew: '&6%1 &eбільше не поновлюватиме статус оренди після закінчення терміну дії.'
    EnableRenew: '&6%1 &eтепер автоматично поновлюватиме статус оренди після закінчення терміну дії.'
  Economy:
    LandForSale: '&eЗемля для продажу:'
    NotEnoughMoney: '&cВам не вистачає грошей.'
    MoneyCharged: '&eСтягнено &6%1 &eна ваш &eрахунок &6%2.'
    MoneyAdded: '&eОтримано &6%1 &eдо вашого &eоблікового запису &6%2.'
    MoneyCredit: '&eЗараховано &6%1 &eна ваш &6%2 &eрахунок.'
    RentReleaseInvalid: '&eРезиденція &6%1 &eне здається і не здається.'
    RentSellFail: '&cНе можна продати житло, якщо воно здається в оренду.'
    SubzoneRentSellFail: '&cНеможливо продати резиденцію, якщо її підзона здається в оренду.'
    ParentRentSellFail: '&cНе можна продати резиденцію, якщо її головна зона здається в оренду.'
    SubzoneSellFail: '&cНеможливо продати підзону.'
    SellRentFail: '&cНе можна орендувати резиденцію, якщо вона продається.'
    ParentSellRentFail: '&cНе можна орендувати резиденцію, якщо її головна зона продається.'
    OwnerBuyFail: '&cНе можна купити власну землю!'
    OwnerRentFail: '&cНе можна орендувати власну землю!'
    AlreadySellFail: '&eРезиденція вже в продажу!'
    LeaseRenew: '&eДоговір оренди до &6%1'
    LeaseRenewMax: '&eОренду продовжено до максимально дозволеного'
    LeaseNotExpire: '&eНемає такої оренди, або оренда не закінчується.'
    LeaseRenewalCost: '&eВартість поновлення для області &6%1 &eстановить &6%2'
    LeaseInfinite: '&eТермін оренди встановлено на безстроковий...'
    MarketDisabled: '&cЕкономіка відключена!'
    SellAmount: '&eСума продажу: &2%1'
    SellList: ' &6%1&e. &6%2 &e(&6%3&e) - &6%4'
    LeaseExpire: '&eТермін дії договору оренди: &2%1'
    LeaseList: '&6%1. &e%2 &2%3 &e%4'
  Expanding:
    North: '&eРозширення на північ &6%1 &eблоків'
    West: '&eРозширення Західних &6%1 &eблоків'
    South: '&eРозширення Південних &6%1 &eблоків'
    East: '&eРозширення східних &6%1 &eблоків'
    Up: '&eРозгортання &6%1 &eблоків'
    Down: '&eРозгортання вниз &6%1 &eблоків'
  Contracting:
    North: '&eСкорочення північних &6%1 &eблоків'
    West: '&eПідрядний Захід &6%1 &eблоків'
    South: '&eПідрядний південь &6%1 &eблоків'
    East: '&eКонтрактуючи Східний &6%1 &eблоки'
    Up: '&eЗростання &6%1 &eблоків'
    Down: '&eСкорочення вниз &6%1 &eблоків'
  Shifting:
    North: '&eЗміщення на північ &6%1 &eблоків'
    West: '&eЗсув на захід &6%1 &eблоків'
    South: '&eЗсув на південь &6%1 &eблоків'
    East: '&eЗміщення на схід &6%1 &eблоків'
    Up: '&eЗміщення вгору &6%1 &eблоків'
    Down: '&eЗсув вниз &6%1 &eблоків'
  Limits:
    PGroup: '&7- &eГрупа дозволів:&3 %1'
    RGroup: '&7- &eГрупа проживання:&3 %1'
    Admin: '&7- &eАдміністратор резиденції:&3 %1'
    CanCreate: '&7- &eМожна створювати резиденції:&3 %1'
    MaxRes: '&7- &eМакс резиденцій:&3 %1'
    MaxEW: '&7- &eМакс. схід/захід розмір:&3 %1'
    MaxNS: '&7- &eМаксимальний розмір північ/південь:&3 %1'
    MaxUD: '&7- &eМаксимальний розмір вгору/вниз:&3 %1'
    MinMax: '&7- &eМінімальна/максимальна висота захисту:&3 %1 to %2'
    MaxSubzones: '&7- &eМакс. підзони:&3 %1'
    MaxSubDepth: '&7- &eМаксимальна глибина підзони:&3 %1'
    MaxRents: '&7- &eМаксимальна орендна плата:&3 %1'
    MaxRentDays: ' &eМаксимальна кількість днів оренди:&3 %1'
    EnterLeave: '&7- &eМожна встановити повідомлення для входу/виходу:&3 %1'
    NumberOwn: '&7- &eКількість резиденцій, якими ви володієте:&3 %1'
    Cost: '&7- &eВартість проживання за блок:&3 %1'
    Sell: '&7- &eВартість продажу за блок:&3 %1'
    Flag: '&7- &eПозначити дозволи:&3 %1'
    MaxDays: '&7- &eМаксимальна кількість днів оренди:&3 %1'
    LeaseTime: '&7- &eТермін оренди, наданий при поновленні:&3 %1'
    RenewCost: '&7- &eВартість поновлення за блок:&3 %1'
  Gui:
    Set:
      Title: '&8%1 прапори'
    Pset:
      Title: '&8%1 &7%2 &6прапори'
    Actions:
    - '&2ЛКМ, щоб увімкнути'
    - '&cПКМ, щоб вимкнути'
    - '&eShift + ЛКМ, щоб видалити'
  InformationPage:
    Top: '&e___/ &a %1 - %2 &e \___'
    TopSingle: '&e___/ &a %1 &e \___'
    Page: '&e-----< &6%1 &e>-----'
    NextPage2: '&e-----< &6%1 &e>-----'
    NoNextPage: '&e-----------------------'
    GeneralList: '&2 %1 &6- &e%2'
    FlagsList: '&2 %1 &6- &e%2'
    SmallSeparator: '&6------'
  Chat:
    ChatChannelChange: '&eКанал чату місця проживання змінено на &6%1!'
    ChatChannelLeave: '&eВийшов з чату місця проживання'
    ChatMessage: '%1 %2%3: %4%5'
    ChatListeningMessage: '&2[Слухання %6]%1 %2%3: %4%5'
    JoinFirst: '&4Спочатку приєднайтеся до каналу чату проживання...'
    InvalidChannel: '&4Недійсний канал...'
    InvalidColor: '&4Неправильний код кольору'
    NotInChannel: '&4Гравець не на каналі'
    Kicked: '&6%1 &eбув виключений з каналу &6%2'
    InvalidPrefixLength: '&4Префікс занадто довгий. Дозволена довжина: %1'
    ChangedColor: '&eКолір каналу чату проживання змінено на %1'
    ChangedPrefix: '&eПрефікс каналу чату проживання змінено на %1'
  Shop:
    ListTopLine: '&6%1 &eСписок магазинів - Сторінка &6%2 &eз &6%3 %4'
    List: ' &e%1. &6%2 &e(&6%3&e) %4'
    ListVoted: '&e%1 (&6%2&e)'
    ListLiked: '&7Лайків: &7%1'
    VotesTopLine: '&6%1 &e%2 список голосування за резиденцією &6- &eСторінка &6%3 &eз &6%4 %5'
    VotesList: ' &e%1. &6%2 &e%3 &7%4'
    NoDesc: '&6Без опису'
    Desc: |-
      &6Опис:
      %1
    DescChange: '&6Опис змінено на: %1'
    ChantChange: '&4Неможливо змінити, якщо для прапора магазину встановлено значення true'
    NewBoard: '&6Успішно додано нову вивіску магазину'
    BoardExist: '&cДошка магазину вже існує в цьому місці'
    DeleteBoard: '&6ПКМ по знаку дошки, яку потрібно видалити'
    DeletedBoard: '&6Вивіска знята'
    IncorrectBoard: '&cЦе не табличка, спробуйте виконати команду ще раз і натиснути
      правильний знак'
    InvalidSelection: '&cЛКМ за допомогою верхнього лівого знака інструмента виділення, а потім клацніть правою кнопкою
      миші внизу праворуч'
    ToBigSelection: '&cВаш вибір завеликий, максимально дозволено 16 блоків'
    ToDeapSelection: '&cВаш вибір занадто глибокий, максимальний дозволений блок – 16x16x1'
    VoteChanged: '&6Голосування змінено з &e%1 &6на &e%2 &6за &e%3 &6місце проживання'
    Voted: '&6Ви проголосували та віддали &e%1 &6голоси &e%2 &6резиденції'
    Liked: '&6Вам сподобалася &e%1 &6резиденція'
    AlreadyLiked: '&6Ви вже вподобали &e%1 &6резиденцію'
    NoVotes: '&cНемає зареєстрованих голосів для цієї резиденції'
    CantVote: '&cРезиденція не має прапора магазину, встановленого на true'
    VotedRange: '&6Діапазон голосів від &e%1 &6до &e%2'
    SignLines:
      '1': '&e--== &8%1 &e==--'
      '2': '&9%1'
      '3': '&4%1'
      '4': '&8%1&e (&8%2&e)'
      Likes4: '&9Лайки: &8%2'
  RandomTeleport:
    TpLimit: '&eВи не можете телепортуватися так швидко, зачекайте &6%1 &eсекунд і повторіть спробу'
    TeleportSuccess: '&eТелепортовано до X:&6%1&e, Y:&6%2&e, Z:&6%3 &eлокації'
    IncorrectLocation: '&6Не вдалося знайти правильне місце телепорту, зачекайте &e%1
      &6секунд та повторіть спробу.'
    Disabled: '&cВипадкова телепортація вимкнена в цьому світі'
    TeleportStarted: '&eТелепортацію розпочато, не рухайтеся протягом наступних &6%4 &eсекунд.'
    WorldList: '&eМожливі світи: &6%1'
  Permissions:
    variableColor: '&f'
    permissionColor: '&6'
    cmdPermissionColor: '&2'
  General:
    DisabledWorld: '&cПлагін Residence вимкнено в цьому світі'
    UseNumbers: '&cБудь ласка, використовуйте цифри...'
    # Replace all text with '' to disable this message
    CantPlaceLava: '&cВи не можете розміщувати лаву за межами помешкання та вище рівня &6%1
      &cблоку'
    # Replace all text with '' to disable this message
    CantPlaceWater: '&cВи не можете розміщувати Воду за межами помешкання та вище рівня &6%1
      &cблоку'
    CantPlaceChest: '&cВи не можете поставити скриню на цьому місці'
    NoPermission: '&cВи не маєте на це дозволу.'
    info:
      NoPlayerPermission: '&c[playerName] не має дозволу [permission]'
    NoCmdPermission: '&cВи не маєте дозволу на цю команду.'
    DefaultUsage: '&eВведіть &6/%1 ? &eдля отримання додаткової інформації'
    MaterialGet: '&eНазва матеріалу для ID &6%1 &eє &6%2'
    MarketList: '&e---- &6Список ринку &e----'
    Separator: '&e----------------------------------------------------'
    AdminOnly: '&cТільки адміністратори мають доступ до цієї команди.'
    InfoTool: '&e- Інструмент інформації: &6%1'
    ListMaterialAdd: '&6%1 &eдодано до резиденції &6%2'
    ListMaterialRemove: '&6%1 &eвилучено з місця проживання &6%2'
    ItemBlacklisted: '&cВи внесені до чорного списку використання цього предмета тут.'
    WorldPVPDisabled: '&cСвіт PVP вимкнено.'
    NoPVPZone: '&cБез зони PVP.'
    NoFriendlyFire: '&cБез дружнього вогню'
    InvalidHelp: '&cНедійсна сторінка довідки.'
    TeleportDeny: '&cУ вас немає доступу до телепорту.'
    TeleportSuccess: '&eТелепортовано!'
    TeleportConfirmLava: '&cЦей телепорт небезпечний, ви потрапите в &6лаву&c.
      Використайте &6/res tpconfirm &cщоб все одно виконати телепортацію.'
    TeleportConfirmVoid: '&cЦей телепорт небезпечний, ви потрапите в &6пустоту&c.
      Використайте &6/res tpconfirm &cщоб все одно виконати телепортацію.'
    TeleportConfirm: '&cЦей телепорт небезпечний, ви впадете в &6%1 &cблоки.
      Використайте &6/res tpconfirm &cщоб все одно виконати телепортацію.'
    TeleportStarted: '&eТелепортація до &6%1 &eрозпочато, не рухайтеся наступні &6%2
      &eсек.'
    TeleportTitle: '&eТелепортація!'
    TeleportTitleTime: '&6%1'
    TeleportCanceled: '&eТелепортацію скасовано!'
    NoTeleportConfirm: '&eНемає жодних телепортів, які очікують підтвердження!'
    HelpPageHeader2: '&eСторінки довідки - &6%1 &e- Сторінка <&6%2 &eз &6%3&e>'
    ListExists: '&cСписок вже є...'
    ListRemoved: '&eСписок видалено...'
    ListCreate: '&eСтворено список &6%1'
    PhysicalAreas: '&eФізичні зони'
    CurrentArea: '&eПоточна площа: &6%1'
    TotalResSize: '&eПоточна площа: &6%1m³ (%2m²)'
    ResSize:
      eastWest: '&eЗ заходу/на схід: &6%1'
      northSouth: '&eЗ заходу/на схід: &6%1'
      upDown: '&eВверх/вниз: &6%1'
    TotalWorth: '&eЗагальна вартість проживання: &6%1 &e(&6%2&e)'
    TotalSubzones: '&eПідзони в резиденції: &6%1 &e(&6%2&e)'
    NotOnline: '&eЦільовий гравець має бути онлайн.'
    GenericPages: '&eСторінка &6%1 &eз &6%2 &e(&6%3&e)'
    WorldEditNotFound: '&cWorldEdit не виявлено.'
    CoordsTop: '&eX:&6%1 &eY:&6%2 &eZ:&6%3'
    CoordsBottom: '&eX:&6%1 &eY:&6%2 &eZ:&6%3'
    CoordsLiner: '&7 (&3%1&7;%2&7)'
    AllowedTeleportIcon: '&2T'
    BlockedTeleportIcon: '&7T'
    AllowedMovementIcon: '&2M'
    BlockedMovementIcon: '&7M'
    AdminToggleTurnOn: '&eПеремикач автоматичного повторного адміністрування &6УВІМК'
    AdminToggleTurnOff: '&eПеремикач автоматичного повторного адміністрування &6ВИМК'
    NoSpawn: '&eУ вас немає прав на &6переміщення &eв точці появи. Переїзд'
    CompassTargetReset: '&eВаш компас скинуто'
    CompassTargetSet: '&eВаш компас тепер вказує на &6%1'
    Ignorelist: '&2Список ігнорування:&6'
    Blacklist: '&cЧорний список:&6'
    LandCost: '&eВартість землі: &6%1'
    'True': '&2True'
    'False': '&cFalse'
    Removed: '&6Видалено'
    FlagState: '&eДержава прапора: %1'
    Land: '&eЗемля: &6%1'
    Cost: '&eВартість: &6%1 &eза &6%2 &eдні'
    Status: '&eСтатус: %1'
    Available: '&2Доступний'
    Size: ' &eРозмір: &6%1'
    ResidenceFlags: '&eПрапори резиденції: &6%1'
    PlayersFlags: '&eПрапори гравців: &6%1'
    GroupFlags: '&eПрапори груп: &6%1'
    OthersFlags: '&eІнші прапори: &6%1'
    Moved: '&eПереїхав...'
    Name: '&eІм''я: &6%1'
    Lists: '&eСписки: &6'
    Residences: '&eРезиденції&6'
    CreatedOn: '&eДата створення: &6%1'
    Owner: '&eВласник: &6%1'
    World: '&eСвіт: &6%1'
    Subzones: '&eПідзони'
    # The below lines represent various messages residence sends to the players.
    # Note that some messages have variables such as %1 that are inserted at runtime.
    NewPlayerInfo: '&eЯкщо ви хочете створити охоронену територію для свого будинку, будь ласка
      використовуйте дерев''яну сокиру, щоб вибрати протилежні сторони вашого будинку та виконайте команду &2/res
      create Ім''яВашоїРезиденції'
CommandHelp:
  Description: Довідка Residence
  SubCommands:
    res:
      Description: Головні команди резиденції
      Info:
      - '&2Використайте &6/res [command] ? <сторінка> &2щоб переглянути додаткову довідкову інформацію.'
      SubCommands:
        auto:
          Info:
          - '&eВикористання: &6/res auto (ім''я резиденції) (радіус)'
          Description: Створіть максимально дозволене місце проживання навколо себе
        select:
          Info:
          - Ця команда вибирає зони для використання з проживанням.
          - /res select [x] [y] [z] - вибирає радіус блоків із вами
            посередині.
          Description: Команди вибору
          SubCommands:
            coords:
              Description: Показати вибрані координати
              Info:
              - '&eВикористання: &6/res select coords'
            size:
              Description: Показати вибраний розмір
              Info:
              - '&eВикористання: &6/res select size'
            auto:
              Description: Вмикає інструмент автоматичного вибору
              Info:
              - '&eВикористання: &6/res select auto [ім''ягравця]'
            cost:
              Description: Вартість вибору дисплея
              Info:
              - '&eВикористання: &6/res select cost'
            vert:
              Description: Розгорнути виділення по вертикалі
              Info:
              - '&eВикористання: &6/res select vert'
              - Розширить вибір настільки високо, наскільки дозволено.
            sky:
              Description: Розгорнути виділення до неба
              Info:
              - '&eВикористання: &6/res select sky'
              - Розширює настільки, наскільки вам дозволено.
            bedrock:
              Description: Розширити вибір до бедрока
              Info:
              - '&eВикористання: &6/res select bedrock'
              - Розгортає настільки низько, наскільки вам дозволено.
            expand:
              Description: Розширити виділення в напрямку.
              Info:
              - '&eВикористання: &6/res select expand <кількість>'
              - Розгортає <кількість> у напрямку, у якому ви дивитесь.
            shift:
              Description: Зміщення виділення в напрямку
              Info:
              - '&eВикористання: &6/res select shift <кількість>'
              - Зміщує ваш вибір на <кількість> у напрямку, у якому ви дивитесь.
            chunk:
              Description: Виберіть чанк, на якому ви зараз.
              Info:
              - '&eВикористання: &6/res select chunk'
              - Вибирає чанк, на якому ви зараз перебуваєте.
            residence:
              Description: Виберіть існуючу територію в резиденції.
              Info:
              - '&eВикористання: &6/res select residence <резиденція>'
              - Вибирає наявну територію в резиденції.
            worldedit:
              Description: Встановіть виділення за допомогою поточного вибору WorldEdit.
              Info:
              - '&eВикористання: &6/res select worldedit'
              - Встановлює область виділення за допомогою поточного виділення WorldEdit.
        padd:
          Info:
          - '&eВикористання: &6/res padd <резиденція> [гравець]'
          - Додає важливі прапори для гравця
          Description: Додайте гравця в резиденцію.
        placeholders:
          Info:
          - '&eВикористання: &6/res placeholders (розбір) (заповнювач) (ім''ягравця)'
          Description: Список заповнювачів
          parse: '[result]'
        signconvert:
          Info:
          - '&eВикористання: &6/res signconvert'
          - Спробуємо конвертувати збережені дані знаків із стороннього плагіна
          Description: Перетворює вивіски з плагіна ResidenceSign
        listallhidden:
          Info:
          - '&eВикористання: &6/res listhidden <сторінка>'
          - Перелічує всі приховані резиденції на сервері.
          Description: Список усіх прихованих резиденцій
        bank:
          Info:
          - '&eВикористання: &6/res bank [deposit/withdraw] <резиденція> [кількість]'
          - Ви повинні проживати в резиденції або вказати ім''я проживання
          - Ви повинні мати позначку +bank.
          Description: Розпоряджайтеся грошима в резиденції
        create:
          Info:
          - '&eВикористання: &6/res create [назва_резиденції]'
          Description: Створення резиденцій
        listall:
          Info:
          - '&eВикористання: &6/res listall <сторінка> <Ім''яСвіту> <-a/-f>'
          - Перелічує всі помешкання
          Description: Список усіх резиденцій
        info:
          Info:
          - '&eВикористання: &6/res info <резиденція>'
          - Залиште <резиденція>, щоб відобразити інформацію про резиденцію, де ви зараз
            проживаєте.
          Description: Показати інформацію про резиденцію.
        area:
          Description: Управління фізичними площами для проживання.
          Info:
          - ''
          SubCommands:
            list:
              Description: Перелічіть фізичні площі в резиденції
              Info:
              - '&eВикористання: &6/res area list [резиденція] <сторінка>'
            listall:
              Description: Список координат та іншої інформації для областей
              Info:
              - '&eВикористання: &6/res area listall [резиденція] <сторінка>'
            add:
              Description: Додайте фізичні території до помешкання
              Info:
              - '&eВикористання: &6/res area add [резиденція] [areaID]'
              - Спочатку потрібно вибрати дві точки.
            remove:
              Description: Видалити фізичні території з помешкання
              Info:
              - '&eВикористання: &6/res area remove [резиденція] [areaID]'
            replace:
              Description: Замінити фізичні площі в резиденції
              Info:
              - '&eВикористання: &6/res area replace [резиденція] [areaID]'
              - Спочатку потрібно вибрати дві точки.
              - Заміна області стягує різницю в розмірі, якщо нова область
                більша.
        give:
          Info:
          - '&eВикористання: &6/res give <ім''я резиденції> [гравець] <-s>'
          - Надає ваше житло цільовому гравцеві
          Description: Надайте місце проживання гравцеві.
        renamearea:
          Info:
          - '&eВикористання: &6/res removeworld [резиденція] [oldAreaName] [newAreaName]'
          Description: Перейменувати резиденцію
        contract:
          Info:
          - '&eВикористання: &6/res contract (резиденція) [кількість]'
          - Контракт на проживання в тому напрямку, в якому ви дивитесь.
          - Ім''я резиденції необов''язкове
          Description: Контракт на проживання в тому напрямку, в якому ви дивитесь
        check:
          Info:
          - '&eВикористання: &6/res check [резиденція] [прапор] (ім''ягрався)'
          Description: Перевірте ваш стан прапора
        gset:
          Info:
          - '&eВикористання: &6/res gset <резиденція> [група] [прапор] [true/false/remove]'
          - Щоб переглянути список прапорів, використовуйте /res flags ?
          Description: Встановіть прапор для певної групи резиденції.
        list:
          Info:
          - '&eВикористання: &6/res list <гравець> <сторінка> <Ім''ясвіту>'
          - Перелічує всі помешкання, якими володіє гравець (крім прихованих).
          - Якщо вказано ваші власні резиденції, також буде показано приховані.
          - Щоб отримати список усіх резиденцій, використовуйте /res listall.
          Description: Список резиденцій
        version:
          Info:
          - '&eВикористання: &6/res version'
          Description: версія Residence
        tool:
          Info:
          - '&eВикористання: &6/res tool'
          Description: Показує вибір місця проживання та назви інформаційних інструментів
        pdel:
          Info:
          - '&eВикористання: &6/res pdel <резиденція> [гравець]'
          - Видаляє важливі прапори з гравця
          Description: Видалити гравця з місця проживання.
        market:
          Info:
          - '&eВикористання: &6/res market ? для отримання додаткової інформації'
          Description: Купити, продати або орендувати резиденцію
          SubCommands:
            Info:
              Description: Отримати економічну інформацію про резиденцію
              Info:
              - '&eВикористання: &6/res market Info [резиденція]'
              - Показує, чи продається резиденція чи здається в оренду, а також вартість.
            list:
              Description: Списки житла, що здається в оренду та на продаж.
              Info:
              - '&eВикористання: &6/res market list [rent/sell]'
              SubCommands:
                rent:
                  Description: Списки орендованих резиденцій.
                  Info:
                  - '&eВикористання: &6/res market list rent'
                sell:
                  Description: Списки на продаж резиденцій.
                  Info:
                  - '&eВикористання: &6/res market list sell'
            sell:
              Description: Продати житло
              Info:
              - '&eВикористання: &6/res market sell [резиденція] [сума]'
              - Виставляє резиденцію на продаж за [суму] грошей.
              - Інший гравець може купити резиденцію за допомогою /res market buy
            sign:
              Description: Встановити знак ринку
              Info:
              - '&eВикористання: &6/res market sign [резиденція]'
              - Встановлює знак ринку, на який ви дивитесь.
            buy:
              Description: Купити резиденцію
              Info:
              - '&eВикористання: &6/res market buy [резиденція]'
              - Купує резиденцію, якщо вона продається.
            unsell:
              Description: Припиняє продаж житла
              Info:
              - '&eВикористання: &6/res market unsell [резиденція]'
            rent:
              Description: Зняти житло
              Info:
              - '&eВикористання: &6/res market rent [резиденція] <AutoPay>'
              - Здає резиденцію. Автоматичне поновлення може мати значення true або false.  Якщо true,
                житло буде автоматично повторно орендовано після закінчення терміну,
                якщо власник помешкання дозволив це.
            rentable:
              Description: Здати житло в оренду
              Info:
              - '&eВикористання: &6/res market rentable [резиденція] [вартість] [дні] <AllowRenewing>
                <StayInMarket> <AllowAutoPay>'
              - Орендує житло за [вартість] грошей на кожні [дні] кількість
                днів.
              - Якщо <AllowRenewing> має значення true, резиденцію можна буде знову
                орендувати до закінчення терміну оренди.
              - Якщо <StayInMarket> має значення true, резиденція залишиться на ринку
                після видалення останнього орендаря.
              - If <AllowAutoPay> має значення true, гроші за оренду будуть автоматично зніматися
                з балансу гравця, якщо він вибрав цю опцію під час оренди
            autopay:
              Description: Встановлює задане значення AutoPay для місця проживання
              Info:
              - '&eВикористання: &6/res market autopay [резиденція] [true/false]'
            payrent:
              Description: Сплачує орендну плату за визначене місце проживання
              Info:
              - '&eВикористання: &6/res market payrent [резиденція]'
            confirm:
              Description: Підтверджує дії щодо скасування/звільнення місця проживання
              Info:
              - '&eВикористання: &6/res market confirm'
            unrent:
              Description: Зняти житло в оренду чи оренду.
              Info:
              - '&eВикористання: &6/res market unrent [резиденція]'
              - Якщо ви є орендарем, ця команда зніме для вас орендну плату
                за будинок.
              - Якщо ви є власником, ця команда робить резиденцію не здається
                в оренду.
        rc:
          Info:
          - '&eВикористання: &6/res rc (резиденція)'
          - Приєднуйтеся до каналу чату проживання.
          Description: Приєднується до поточного або визначеного каналу чату за місцем проживання
          SubCommands:
            leave:
              Description: Залишає поточний канал чату за місцем проживання
              Info:
              - '&eВикористання: &6/res rc leave'
              - Якщо ви перебуваєте в каналі чату для проживання, ви залишите його
            setcolor:
              Description: Встановлює колір тексту каналу чату місця проживання
              Info:
              - '&eВикористання: &6/res rc setcolor [colorCode]'
              - Встановлює колір тексту каналу чату місця проживання
            setprefix:
              Description: Встановлює префікс каналу чату місця проживання
              Info:
              - '&eВикористання: &6/res rc setprefix [newName]'
              - Встановлює префікс каналу чату місця проживання
            kick:
              Description: Викидає гравця з каналу
              Info:
              - '&eВикористання: &6/res rc kick [гравець]'
              - Викидає гравця з каналу
        expand:
          Info:
          - '&eВикористання: &6/res expand (резиденція) [кількість]'
          - Розширює місце проживання в тому напрямку, у якому ви дивитеся.
          - Ім'я проживання необов'язкове
          Description: Розширює місце проживання в тому напрямку, у якому ви дивитеся
        compass:
          Info:
          - '&eВикористання: &6/res compass <резиденція>'
          Description: Встановіть покажчик компаса на місце проживання
        lists:
          Info:
          - Попередньо визначені дозволи, які можна застосувати до резиденції.
          Description: Попередньо визначені списки дозволів
          SubCommands:
            add:
              Description: Додайте список
              Info:
              - '&eВикористання: &6/res lists add <listname>'
            remove:
              Description: Видалити список
              Info:
              - '&eВикористання: &6/res lists remove <listname>'
            apply:
              Description: Застосувати список до місця проживання
              Info:
              - '&eВикористання: &6/res lists apply <listname> <резиденція>'
            set:
              Description: Встановити прапор
              Info:
              - '&eВикористання: &6/res lists set <listname> <флаг> <value>'
            pset:
              Description: Встановити прапор гравця
              Info:
              - '&eВикористання: &6/res lists pset <listname> <гравець> <флаг> <value>'
            gset:
              Description: Встановіть прапор групи
              Info:
              - '&eВикористання: &6/res lists gset <listname> <group> <флаг> <value>'
            view:
              Description: Переглянути список.
              Info:
              - '&eВикористання: &6/res lists view <listname>'
        reset:
          Info:
          - '&eВикористання: &6/res reset <резиденція/all>'
          - Скидає прапори на резиденції до стандартних. Щоб це зробити, ви повинні
            бути власником або адміністратором.
          Description: Скинути прапори місця проживання за замовчуванням.
        listhidden:
          Info:
          - '&eВикористання: &6/res listhidden <гравець> <сторінка>'
          - Перераховує приховані резиденції для гравця.
          Description: Список прихованих резиденцій
        raid:
          Info:
          - '&eВикористання: &6/res raid start [resname] (playerName)'
          - '&6/res raid stop [resname]'
          - '&6/res raid kick [playerName]'
          - '&6/res raid immunity [add/take/set/clear] [resname/currentres] [time]'
          Description: Керуйте рейдом у резиденції
        setmain:
          Info:
          - '&eВикористання: &6/res setmain (резиденція)'
          - Встановити визначене місце проживання як основне.
          Description: Встановлює визначене місце проживання як основне для відображення в чаті як префікс
        server:
          Info:
          - '&eВикористання: &6/resadmin server [резиденція]'
          - Зробіть постійний сервер власником.
          Description: Зробіть наземний сервер власністю.
        rt:
          Info:
          - '&eВикористання: &6/res rt (worldname) (playerName)'
          - Телепортує вас у випадкове місце у визначеному світі.
          Description: Телепортується у випадкове місце у світі
        mirror:
          Info:
          - '&eВикористання: &6/res mirror [Source Residence] [Target Residence]'
          - Дзеркала прапорів від однієї резиденції до іншої. Щоб це зробити, ви повинні
            бути власником обох або адміністратором.
          Description: Дзеркала Прапори
        shop:
          Info:
          - Керує функцією резидентного магазину
          Description: Керувати домашнім магазином
          SubCommands:
            list:
              Description: Відображає список магазинів ресурсів
              Info:
              - '&eВикористання: &6/res shop list'
              - Показує повний список усіх резиденцій із прапором магазину
            vote:
              Description: Голосуйте за магазин за місцем проживання
              Info:
              - '&eВикористання: &6/res shop vote <резиденція> [кількість]'
              - Голосує за поточне або визначене місце проживання
            like:
              Description: Поставте лайк для постійного магазину
              Info:
              - '&eВикористання: &6/res shop like <резиденція>'
              - Здається під проживання магазин
            votes:
              Description: Показує голоси res shop
              Info:
              - '&eВикористання: &6/res shop votes <резиденція> <сторінка>'
              - Показує повний список голосів поточного або визначеного магазину проживання
            likes:
              Description: Показує лайки res shop
              Info:
              - '&eВикористання: &6/res shop likes <резиденція> <сторінка>'
              - Показує повний список лайків поточного або визначеного магазину проживання
            setdesc:
              Description: Встановлює опис магазину для проживання
              Info:
              - '&eВикористання: &6/res shop setdesc [текст]'
              - Встановлює опис магазину для проживання. Підтримується код кольору. Для нового рядка
                використовуйте /n
            createboard:
              Description: Створіть дошку магазину res
              Info:
              - '&eВикористання: &6/res shop createboard [place]'
              - Створює дошку магазину res з вибраної області. Місце - позиція,
                з якої починається заповнення дошки
            deleteboard:
              Description: Видаляє дошку res shop
              Info:
              - '&eВикористання: &6/res shop deleteboard'
              - Видаляє дошку res shop, клацнувши правою кнопкою миші на одному зі знаків
        lset:
          Info:
          - '&eВикористання: &6/res lset <резиденція> [blacklist/ignorelist] [material]'
          - '&eВикористання: &6/res lset <резиденція> Info'
          - Занесення матеріалу в чорний список запобігає його розміщенню в резиденції.
          - Ignorelist призводить до того, що певний матеріал не захищений Residence.
          Description: Змінити параметри чорного списку та списку ігнорування
        raidstatus:
          Info:
          - '&eВикористання: &6/res raidstatus (resName/playerName)'
          Description: Перевірте статус рейду на місце проживання
        pset:
          Info:
          - '&eВикористання: &6/res pset <резиденція> [гравець] [флаг] [true/false/remove]'
          - '&eВикористання: &6/res pset <резиденція> [гравець] removeall'
          - Щоб переглянути список прапорів, використовуйте /res flags ?
          Description: Встановіть прапори на певного гравця для резиденції.
        show:
          Info:
          - '&eВикористання: &6/res show <резиденція>'
          Description: Показати межі проживання
        flags:
          Info:
          - Для значень прапорів зазвичай true дозволяє дію, а false забороняє дію.
          Description: Список прапорів
          SubCommands:
            anvil:
              Translated: ковадло
              Description: Дозволяє або забороняє взаємодіяти з ковадлом
              Info:
              - '&eВикористання: &6/res set/pset <резиденція> anvil true/false/remove'
            admin:
              Translated: адмін
              Description: Дає гравцеві дозвіл змінювати прапори на резиденції
              Info:
              - '&eВикористання: &6/res pset <резиденція> admin true/false/remove'
            animalkilling:
              Translated: вбивствотварин
              Description: Дозволяє або забороняє вбивати тварин
              Info:
              - '&eВикористання: &6/res set/pset <резиденція> animalkilling true/false/remove'
            animals:
              Translated: тварини
              Description: Дозволяє або забороняє спавн тварин
              Info:
              - '&eВикористання: &6/res set <резиденція> animals true/false/remove'
            anchor:
              Translated: anchor
              Description: Дозволяє або забороняє використання прив'язки відродження
              Info:
              - '&eВикористання: &6/res set/pset <резиденція> anchor true/false/remove'
            anvilbreak:
              Translated: зламатиковадло
              Description: Дозволяє або забороняє зламати ковадло в резиденції
              Info:
              - '&eВикористання: &6/res set <резиденція> anvilbreak true/false/remove'
            safezone:
              Translated: безпечназона
              Description: Якщо встановити значення true, резиденція очищає погані
                наслідки її мешканців
              Info:
              - '&eВикористання: &6/res set <резиденція> safezone true/false/remove'
            backup:
              Translated: резервнекопіювання
              Description: Якщо встановлено значення true, відновлюється попередній вигляд області (потрібен
                WordEdit)
              Info:
              - '&eВикористання: &6/res set <резиденція> backup true/false/remove'
            bank:
              Translated: банк
              Description: Дозволяє або забороняє депозит/зняття грошей із банку резиденції
              Info:
              - '&eВикористання: &6/res set/pset <резиденція> bank true/false/remove'
            bed:
              Translated: ліжко
              Description: Дозволяє або забороняє гравцям використовувати ліжка
              Info:
              - '&eВикористання: &6/res set/pset <резиденція> bed true/false/remove'
            honey:
              Translated: мед
              Description: Дозволяє або забороняє гравцям отримувати мед
              Info:
              - '&eВикористання: &6/res set/pset <резиденція> honey true/false/remove'
            honeycomb:
              Translated: стільниковий
              Description: Дозволяє або забороняє гравцям отримувати стільники
              Info:
              - '&eВикористання: &6/res set/pset <резиденція> honeycomb true/false/remove'
            beacon:
              Translated: маяк
              Description: Дозволяє або забороняє взаємодіяти з маяком
              Info:
              - '&eВикористання: &6/res set/pset <резиденція> beacon true/false/remove'
            brew:
              Translated: варити
              Description: Дозволяє або забороняє гравцям використовувати підставки для варіння
              Info:
              - '&eВикористання: &6/res set/pset <резиденція> brew true/false/remove'
            build:
              Translated: будувати
              Description: Дозволяє або забороняє будівництво
              Info:
              - '&eВикористання: &6/res set/pset <резиденція> build true/false/remove'
            burn:
              Translated: спалювати
              Description: Дозволяє або забороняє спалювання натовпу в помешканнях
              Info:
              - '&eВикористання: &6/res set <резиденція> burn true/false/remove'
            button:
              Translated: кнопка
              Description: Дозволяє або забороняє гравцям використовувати кнопки
              Info:
              - '&eВикористання: &6/res set/pset <резиденція> button true/false/remove'
            cake:
              Translated: торт
              Description: Дозволяє або забороняє гравцям їсти торт
              Info:
              - '&eВикористання: &6/res set/pset <резиденція> cake true/false/remove'
            canimals:
              Translated: тварини
              Description: Дозволяє або забороняє нестандартну появу тварин
              Info:
              - '&eВикористання: &6/res set <резиденція> canimals true/false/remove'
            chorustp:
              Translated: хоруси
              Description: Дозволити або заборонити телепортацію до резиденції за допомогою
                хорусів
              Info:
              - '&eВикористання: &6/res set/pset <резиденція> chorustp true/false/remove'
            chat:
              Translated: чат
              Description: Дозволяє приєднатися до кімнати чату резиденції
              Info:
              - '&eВикористання: &6/res set/pset <резиденція> chat true/false/remove'
            cmonsters:
              Translated: монстри
              Description: Дозволяє або забороняє появу спеціальних монстрів
              Info:
              - '&eВикористання: &6/res set <резиденція> cmonsters true/false/remove'
            commandblock:
              Translated: команднийблок
              Description: Дозволяє або забороняє взаємодію з командним блоком
              Info:
              - '&eВикористання: &6/res set/pset <резиденція> commandblock true/false/remove'
            command:
              Translated: команда
              Description: Дозволяє або забороняє команду, що використовується в резиденціях
              Info:
              - '&eВикористання: &6/res set/pset <резиденція> command true/false/remove'
            container:
              Translated: відкрити
              Description: Дозволяє або забороняє використання печей, скринь, дозаторів тощо...
              Info:
              - '&eВикористання: &6/res set/pset <резиденція> container true/false/remove'
            coords:
              Translated: коорди
              Description: Приховує координати проживання
              Info:
              - '&eВикористання: &6/res set <резиденція> coords true/false/remove'
            copper:
              Translated: мідь
              Description: Дозволяє модифікувати мідні блоки
              Info:
              - '&eВикористання: &6/res set/pset <резиденція> copper true/false/remove'
            craft:
              Translated: крафт
              Description: Дає стіл, зачаровує, варить прапори
              Info:
              - '&eВикористання: &6/res set <резиденція> craft true/false/remove'
            creeper:
              Translated: кріпер
              Description: Дозволити або заборонити вибухи криперів
              Info:
              - '&eВикористання: &6/res set <резиденція> creeper true/false/remove'
            dragongrief:
              Translated: драконовапечаль
              Description: Запобігає горю блоку ендерського дракона
              Info:
              - '&eВикористання: &6/res set <резиденція> dragongrief true/false/remove'
            day:
              Translated: день
              Description: Встановлює час дня в резиденції
              Info:
              - '&eВикористання: &6/res set <резиденція> day true/false/remove'
            dye:
              Translated: барвник
              Description: Дозволяє або забороняє фарбування овець
              Info:
              - '&eВикористання: &6/res set/pset <резиденція> dye true/false/remove'
            damage:
              Translated: пошкодження
              Description: Дозволяє або забороняє нанесення будь-якої шкоди об’єктам у резиденції
              Info:
              - '&eВикористання: &6/res set <резиденція> damage true/false/remove'
            decay:
              Translated: розпад
              Description: Дозволяє або забороняє гниття листя в резиденції
              Info:
              - '&eВикористання: &6/res set <резиденція> decay true/false/remove'
            destroy:
              Translated: знищити
              Description: Дозволяє або забороняє лише знищення блоків, скасовує
                прапор збірки
              Info:
              - '&eВикористання: &6/res set/pset <резиденція> destroy true/false/remove'
            dryup:
              Translated: висихати
              Description: Запобігає висиханню землі
              Info:
              - '&eВикористання: &6/res set <резиденція> dryup true/false/remove'
            diode:
              Translated: діод
              Description: Дозволяє або забороняє гравцям використовувати повторювачі червоного каменю
              Info:
              - '&eВикористання: &6/res set/pset <резиденція> diode true/false/remove'
            door:
              Translated: двері
              Description: Дозволяє або забороняє гравцям використовувати двері та люки
              Info:
              - '&eВикористання: &6/res set/pset <резиденція> door true/false/remove'
            egg:
              Translated: яйце
              Description: Дозволяє або забороняє взаємодію з яйцем дракона
              Info:
              - '&eВикористання: &6/res set/pset <резиденція> egg true/false/remove'
            enchant:
              Translated: зачарувати
              Description: Дозволяє або забороняє гравцям використовувати чарівні столи
              Info:
              - '&eВикористання: &6/res set/pset <резиденція> enchant true/false/remove'
            explode:
              Translated: вибухи
              Description: Дозволяє або забороняє вибухи в помешканнях
              Info:
              - '&eВикористання: &6/res set <резиденція> explode true/false/remove'
            elytra:
              Translated: елітри
              Description: Дозволяє або забороняє використання елітрів у помешканнях
              Info:
              - '&eВикористання: &6/res set/pset <резиденція> elytra true/false/remove'
            enderpearl:
              Translated: ендерськаперлина
              Description: Дозволити або заборонити телепортацію до резиденції за допомогою enderpearl
              Info:
              - '&eВикористання: &6/res set/pset <резиденція> enderpearl true/false/remove'
            fallinprotection:
              Translated: падіння
              Description: Захищає від падіння блоків у помешкання
              Info:
              - '&eВикористання: &6/res set <резиденція> fallinprotection true/false/remove'
            falldamage:
              Translated: пошкодженняприпадінні
              Description: Захищає гравців від пошкоджень при падінні
              Info:
              - '&eВикористання: &6/res set <резиденція> falldamage true/false/remove'
            feed:
              Translated: їжа
              Description: Якщо встановити значення true, резиденція годує своїх мешканців
              Info:
              - '&eВикористання: &6/res set <резиденція> feed true/false/remove'
            friendlyfire:
              Translated: дружнійвогонь
              Description: Дозволити або заборонити дружній вогонь
              Info:
              - '&eВикористання: &6/res pset <резиденція> friendlyfire true/false/remove'
            fireball:
              Translated: вогнянакуля
              Description: Дозволяє або забороняє вогняні кулі в помешканнях
              Info:
              - '&eВикористання: &6/res set <резиденція> fireball true/false/remove'
            firespread:
              Translated: поширенняпожежі
              Description: Дозволяє або забороняє поширення пожежі
              Info:
              - '&eВикористання: &6/res set <резиденція> firespread true/false/remove'
            flowinprotection:
              Translated: захиствідтечії
              Description: Дозволяє або забороняє надходження рідини до помешкання
              Info:
              - '&eВикористання: &6/res set <резиденція> flowinprotection true/false/remove'
            flow:
              Translated: потік
              Description: Дозволяє або забороняє потік рідини
              Info:
              - '&eВикористання: &6/res set <резиденція> flow true/false/remove'
            flowerpot:
              Translated: квітковийгорщик
              Description: Дозволяє або забороняє взаємодію з квітковим горщиком
              Info:
              - '&eВикористання: &6/res set/pset <резиденція> flowerpot true/false/remove'
            grow:
              Translated: рости
              Description: Дозволяє або забороняє вирощувати рослини
              Info:
              - '&eВикористання: &6/res set <резиденція> grow true/false/remove'
            glow:
              Translated: світіння
              Description: Гравці почнуть світитися при вході в резиденцію
              Info:
              - '&eВикористання: &6/res set <резиденція> glow true/false/remove'
            harvest:
              Translated: врожай
              Description: Дозволяє збирати врожай
              Info:
              - '&eВикористання: &6/res set/pset <резиденція> harvest true/false/remove'
            hotfloor:
              Translated: магма
              Description: Запобігайте пошкодженню від блоків магми
              Info:
              - '&eВикористання: &6/res set <резиденція> hotfloor true/false/remove'
            hidden:
              Translated: прихований
              Description: Приховує місце проживання в списку всіх команд
              Info:
              - '&eВикористання: &6/res set <резиденція> hidden true/false/remove'
            hook:
              Translated: вудка
              Description: Дозволяє або забороняє об'єкти, що підхоплюють вудку
              Info:
              - '&eВикористання: &6/res set/pset <резиденція> hook true/false/remove'
            healing:
              Translated: зцілення
              Description: Якщо встановити значення true, резиденція зцілює своїх мешканців
              Info:
              - '&eВикористання: &6/res set <резиденція> healing true/false/remove'
            iceform:
              Translated: крижанаформа
              Description: Запобігає утворенню льоду
              Info:
              - '&eВикористання: &6/res set <резиденція> iceform true/false/remove'
            icemelt:
              Translated: таненняльоду
              Description: Запобігає таненню льоду
              Info:
              - '&eВикористання: &6/res set <резиденція> icemelt true/false/remove'
            ignite:
              Translated: запалити
              Description: Дозволяє або забороняє займання
              Info:
              - '&eВикористання: &6/res set/pset <резиденція> ignite true/false/remove'
            itemdrop:
              Translated: падінняпредмета
              Description: Дозволяє або забороняє скидання предметів
              Info:
              - '&eВикористання: &6/res set/pset <резиденція> itemdrop true/false/remove'
            itempickup:
              Translated: самовивізтовару
              Description: Дозволяє або забороняє отримання товару
              Info:
              - '&eВикористання: &6/res set/pset <резиденція> itempickup true/false/remove'
            jump2:
              Translated: стрибок2
              Description: Дозволяє стрибнути на 2 блоки у висоту
              Info:
              - '&eВикористання: &6/res set <резиденція> jump2 true/false/remove'
            jump3:
              Translated: стрибок3
              Description: Дозволяє стрибнути на 3 блоки у висоту
              Info:
              - '&eВикористання: &6/res set <резиденція> jump3 true/false/remove'
            keepinv:
              Translated: зберігатиінвентар
              Description: Гравці зберігають інвентар після смерті
              Info:
              - '&eВикористання: &6/res set <резиденція> keepinv true/false/remove'
            keepexp:
              Translated: зберігатидосвід
              Description: Гравці зберігають досвід після смерті
              Info:
              - '&eВикористання: &6/res set <резиденція> keepexp true/false/remove'
            lavaflow:
              Translated: лавовийпотік
              Description: Дозволяє або забороняє потік лави, перекриває потік
              Info:
              - '&eВикористання: &6/res set <резиденція> lavaflow true/false/remove'
            leash:
              Translated: повідець
              Description: Дозволяє або забороняє прив'язувати тварин
              Info:
              - '&eВикористання: &6/res set/pset <резиденція> leash true/false/remove'
            lever:
              Translated: важіль
              Description: Дозволяє або забороняє гравцям використовувати важелі
              Info:
              - '&eВикористання: &6/res set/pset <резиденція> lever true/false/remove'
            mobexpdrop:
              Translated: падіннядосвідумобу
              Description: Запобігає випаданню досвіду натовпу після смерті
              Info:
              - '&eВикористання: &6/res set <резиденція> mobexpdrop true/false/remove'
            mobitemdrop:
              Translated: падінняпредметівмобу
              Description: Запобігає скиданню предметів натовпу після смерті
              Info:
              - '&eВикористання: &6/res set <резиденція> mobitemdrop true/false/remove'
            mobkilling:
              Translated: вбивствомобів
              Description: Дозволяє або забороняє вбивати натовп
              Info:
              - '&eВикористання: &6/res set/pset <резиденція> mobkilling true/false/remove'
            monsters:
              Translated: монстри
              Description: Дозволяє або забороняє появу монстрів
              Info:
              - '&eВикористання: &6/res set <резиденція> monsters true/false/remove'
            move:
              Translated: рухатися
              Description: Дозволяє або забороняє пересування в резиденції
              Info:
              - '&eВикористання: &6/res set/pset <резиденція> move true/false/remove'
            nametag:
              Translated: іменнатабличка
              Description: Дозволяє або забороняє використання тегу імені
              Info:
              - '&eВикористання: &6/res set/pset <резиденція> nametag true/false/remove'
            nanimals:
              Translated: тварини3
              Description: Дозволяє або забороняє природне відродження тварин
              Info:
              - '&eВикористання: &6/res set <резиденція> nanimals true/false/remove'
            nmonsters:
              Translated: монстри3
              Description: Дозволяє або забороняє появу природних монстрів
              Info:
              - '&eВикористання: &6/res set <резиденція> nmonsters true/false/remove'
            night:
              Translated: ніч
              Description: Встановлює нічний час у резиденції
              Info:
              - '&eВикористання: &6/res set <резиденція> night true/false/remove'
            nofly:
              Translated: нелітати
              Description: Дозволяє або забороняє літати в резиденції
              Info:
              - '&eВикористання: &6/res set/pset <резиденція> nofly true/false/remove'
            fly:
              Translated: літати
              Description: Перемикає політ для гравців у резиденції
              Info:
              - '&eВикористання: &6/res set/pset <резиденція> fly true/false/remove'
            nomobs:
              Translated: немаємобів
              Description: Запобігає проникненню монстрів у помешкання. Потрібно
                ввімкнути AutoMobRemoval
              Info:
              - '&eВикористання: &6/res set <резиденція> nomobs true/false/remove'
            note:
              Translated: примітка
              Description: Дозволяє або забороняє гравцям використовувати блоки нотаток
              Info:
              - '&eВикористання: &6/res set/pset <резиденція> note true/false/remove'
            nodurability:
              Translated: нестійкість
              Description: Запобігає втраті міцності виробу
              Info:
              - '&eВикористання: &6/res set <резиденція> nodurability true/false/remove'
            overridepvp:
              Translated: перевизначитиpvp
              Description: Замінює будь-який захист плагіна pvp
              Info:
              - '&eВикористання: &6/res set <резиденція> overridepvp true/false/remove'
            pressure:
              Translated: тиск
              Description: Дозволяє або забороняє гравцям використовувати натискні пластини
              Info:
              - '&eВикористання: &6/res set/pset <резиденція> pressure true/false/remove'
            piston:
              Translated: поршень
              Description: Дозволити або заборонити поршням штовхати або тягнути
                блоки в резиденції
              Info:
              - '&eВикористання: &6/res set <резиденція> piston true/false/remove'
            pistonprotection:
              Translated: поршневийзахист
              Description: Вмикає або вимикає рух поршневого блоку всередину або з місця проживання
              Info:
              - '&eВикористання: &6/res set <резиденція> pistonprotection true/false/remove'
            place:
              Translated: place
              Description: Дозволяє або забороняє лише розміщення блоків, скасовує
                прапор збірки
              Info:
              - '&eВикористання: &6/res set/pset <резиденція> place true/false/remove'
            pvp:
              Translated: pvp
              Description: Дозволити або заборонити пвп у резиденції
              Info:
              - '&eВикористання: &6/res set <резиденція> pvp true/false/remove'
            rain:
              Translated: дощ
              Description: Встановлює дощову погоду в резиденції
              Info:
              - '&eВикористання: &6/res set <резиденція> rain true/false/remove'
            respawn:
              Translated: респаун
              Description: Автоматично респаунить гравця
              Info:
              - '&eВикористання: &6/res set <резиденція> respawn true/false/remove'
            riding:
              Translated: їздаверхи
              Description: Запобігайте їзді на коні
              Info:
              - '&eВикористання: &6/res set/pset <резиденція> riding true/false/remove'
            shoot:
              Translated: стрілянина
              Description: Дозволяє або забороняє стрільбу снарядом у зоні
              Info:
              - '&eВикористання: &6/res set <резиденція> shoot true/false/remove'
            sun:
              Translated: сонячно
              Description: Встановлює сонячну погоду в резиденції
              Info:
              - '&eВикористання: &6/res set <резиденція> sun true/false/remove'
            shop:
              Translated: крамниця
              Description: Додає резиденцію до списку спеціальних магазинів резиденцій
              Info:
              - '&eВикористання: &6/res set <резиденція> shop true/false/remove'
            snowtrail:
              Translated: сніговастежка
              Description: Запобігає утворенню снігових слідів від сніговика
              Info:
              - '&eВикористання: &6/res set <резиденція> snowtrail true/false/remove'
            spread:
              Translated: поширення
              Description: Запобігає розповсюдженню блоків
              Info:
              - '&eВикористання: &6/res set <резиденція> spread true/false/remove'
            snowball:
              Translated: сніговакуля
              Description: Запобігає зворотному удару снігової кулі
              Info:
              - '&eВикористання: &6/res set <резиденція> snowball true/false/remove'
            sanimals:
              Translated: тварини4
              Description: Дозволяє або забороняє нерест нерестовика або ікринок тварин
              Info:
              - '&eВикористання: &6/res set <резиденція> sanimals true/false/remove'
            shear:
              Translated: стрижка
              Description: Дозволяє або забороняє стрижку овець
              Info:
              - '&eВикористання: &6/res set/pset <резиденція> shear true/false/remove'
            smonsters:
              Translated: монстрис
              Description: Дозволяє або забороняє нерест нерестовика або ікри монстра
              Info:
              - '&eВикористання: &6/res set <резиденція> smonsters true/false/remove'
            subzone:
              Translated: підзони
              Description: Дозволити гравцеві створювати підзони в резиденції
              Info:
              - '&eВикористання: &6/res set/pset <резиденція> subzone true/false/remove'
            title:
              Translated: показ
              Description: Показує або приховує повідомлення про вхід/вихід з резиденції
              Info:
              - '&eВикористання: &6/res set <резиденція> title true/false/remove'
            table:
              Translated: стіл
              Description: Дозволяє або забороняє гравцям використовувати робочі столи
              Info:
              - '&eВикористання: &6/res set/pset <резиденція> table true/false/remove'
            tnt:
              Translated: tnt
              Description: Дозволити або заборонити вибухи ТНТ
              Info:
              - '&eВикористання: &6/res set <резиденція> tnt true/false/remove'
            tp:
              Translated: tp
              Description: Дозволити або заборонити телепортацію до резиденції
              Info:
              - '&eВикористання: &6/res set/pset <резиденція> tp true/false/remove'
            trade:
              Translated: торгівля
              Description: Дозволяє або забороняє селянам здійснювати торгівлю за місцем проживання
              Info:
              - '&eВикористання: &6/res set/pset <резиденція> trade true/false/remove'
            trample:
              Translated: розтоптати
              Description: Дозволяє або забороняє витоптування посівів на території проживання
              Info:
              - '&eВикористання: &6/res set <резиденція> trample true/false/remove'
            use:
              Translated: використання
              Description: Дозволяє або забороняє використання дверей, важелів, кнопок тощо...
              Info:
              - '&eВикористання: &6/res set/pset <резиденція> use true/false/remove'
            vehicledestroy:
              Translated: знищенняавтотранспорту
              Description: Дозволяє або забороняє знищення транспортного засобу
              Info:
              - '&eВикористання: &6/res set/pset <резиденція> vehicledestroy true/false/remove'
            witherspawn:
              Translated: візерспаун
              Description: Дозволяє або забороняє візера
              Info:
              - '&eВикористання: &6/res set <резиденція> witherspawn true/false/remove'
            phantomspawn:
              Translated: фантомспаун
              Description: Дозволяє або забороняє фантомний спавн
              Info:
              - '&eВикористання: &6/res set <резиденція> phantomspawn true/false/remove'
            witherdamage:
              Translated: пошкодженнявізера
              Description: Дозволяє або забороняє пошкодження при в'яненні
              Info:
              - '&eВикористання: &6/res set <резиденція> witherdamage true/false/remove'
            witherdestruction:
              Translated: witherdestruction
              Description: Дозволяє або забороняє пошкодження блоків в'янення
              Info:
              - '&eВикористання: &6/res set <резиденція> witherdestruction true/false/remove'
            waterflow:
              Translated: потікводи
              Description: Дозволяє або забороняє потік води, перекриває потік
              Info:
              - '&eВикористання: &6/res set <резиденція> waterflow true/false/remove'
            wspeed1:
              Translated: wspeed1
              Description: Змінити швидкість ходьби гравців у резиденції на %1
              Info:
              - '&eВикористання: &6/res set <резиденція> wspeed1 true/false/remove'
            wspeed2:
              Translated: wspeed2
              Description: Змінити швидкість ходьби гравців у резиденції на %1
              Info:
              - '&eВикористання: &6/res set <резиденція> wspeed2 true/false/remove'
            brush:
              Translated: пензлик
              Description: Дозволяє або забороняє чищення блоків
              Info:
              - '&eВикористання: &6/res set/pset <резиденція> brush true/false/remove'
        remove:
          Info:
          - '&eВикористання: &6/res remove [residence_name]'
          Description: Видалити резиденції.
        signupdate:
          Info:
          - '&eВикористання: &6/res signupdate'
          Description: Оновлені вивіски про місце проживання
        current:
          Info:
          - '&eВикористання: &6/res current'
          Description: Покажіть місце проживання, в якому ви зараз перебуваєте.
        reload:
          Info:
          - '&eВикористання: &6/res reload [config/lang/groups/flags]'
          Description: перезавантажити файли перекладу, або конфігурації
        leaveraid:
          Info:
          - '&eВикористання: &6/res leaveraid'
          Description: Залишаште рейд.
        setowner:
          Info:
          - '&eВикористання: &6/resadmin setowner [резиденція] [player] (-keepflags)'
          Description: Змінити власника житла.
        defend:
          Info:
          - '&eВикористання: &6/res defend [resName] (playerName)'
          Description: Приєднуйтесь до захисту від рейдерства за місцем проживання
        attack:
          Description: Початок рейду за місцем проживання
          Info:
          - '&eВикористання: &6/res attack [resName]'
        unstuck:
          Info:
          - '&eВикористання: &6/res unstuck'
          Description: Телепортується за межі місця проживання
        subzone:
          Info:
          - '&eВикористання: &6/res subzone <резиденція> [subzone name]'
          - Якщо ви не введете назву місця проживання, ми спробуємо використати місце проживання,
            в якому ви перебуваєте.
          Description: Створюйте підзони в резиденціях.
        removeworld:
          Info:
          - '&eВикористання: &6/res removeworld [worldname]'
          - Можна використовувати тільки в консолі
          Description: Видалити всі резиденції зі світу
        limits:
          Info:
          - '&eВикористання: &6/res limits (playerName)'
          - Показує обмеження, які ви маєте при створенні та управлінні резиденціями.
          Description: Покажіть свої межі.
        set:
          Info:
          - '&eВикористання: &6/res set <резиденція> [flag] [true/false/remove]'
          - Щоб переглянути список прапорів, скористайтеся командою /res flags ?
          - Ці прапори застосовуються до всіх гравців, які не мають прапора, застосованого
            спеціально для них. (див /res pset ?)
          Description: Встановіть загальні прапори на резиденції
        clearflags:
          Info:
          - '&eВикористання: &6/res clearflags <резиденція>'
          Description: Приберіть усі прапори з резиденцій
        message:
          Info:
          - '&eВикористання: &6/res message <резиденція> [enter/leave] [message]'
          - Встановіть повідомлення про вхід або вихід з резиденції.
          - '&eВикористання: &6/res message <резиденція> remove [enter/leave]'
          - Видаляє повідомлення про вхід або вихід.
          Description: Керування резиденцією вхід / залишення повідомлень
        command:
          Info:
          - '&eВикористання: &6/res command <резиденція> <allow/block/list> <команда>'
          - Показує список, додає або видаляє дозволені або заборонені команди в резиденції
          - Використовуйте _ для включення команди з декількома змінними
          Description: Керує дозволеними або заблокованими командами в резиденції
        confirm:
          Description: Підтверджує видалення місця проживання.
          Info:
          - '&eВикористання: &6/res confirm'
          - Підтверджує видалення місця проживання.
        resadmin:
          Info:
          - '&eВикористання: &6/res resadmin [on/off]'
          Description: Увімкнути або вимкнути адміністратора резиденції
        tpset:
          Info:
          - '&eВикористання: &6/res tpset'
          - Це встановить місце телепортації для резиденції на те місце, де ви перебуваєте.
          - Щоб скористатися цією командою, ви повинні знаходитися в резиденції.
          - Ви також повинні бути власником або мати прапор +admin для резиденції.
          Description: Встановити місце телепортації резиденції
        tpconfirm:
          Info:
          - '&eВикористання: &6/res tpconfirm'
          - Телепортує вас до місця проживання, коли телепортація небезпечна.
          Description: Ігнорувати попередження про небезпечну телепортацію
        removeall:
          Info:
          - '&eВикористання: &6/res removeall [owner]'
          - Видаляє всі резиденції, що належать певному гравцеві.'
          - Потрібен /resadmin, якщо ви використовуєте його не для себе, а для когось іншого.
          Description: Видалити всі резиденції, якими володіє гравець.
        material:
          Info:
          - '&eВикористання: &6/res material [material]'
          Description: Перевірка наявності матеріалу за його ідентифікатором
        kick:
          Info:
          - '&eВикористання: &6/res kick <гравець>'
          - Для цього ви повинні бути власником або адміністратором.
          - Гравець повинен бути онлайн.
          Description: Виганяє гравця з резиденції.
        sublist:
          Info:
          - '&eВикористання: &6/res sublist <резиденція> <сторінка>'
          - Перелічіть підзони в межах резиденції.
          Description: Перелік підзон резиденції
        rename:
          Info:
          - '&eВикористання: &6/res rename [OldName] [NewName]'
          - Для цього ви повинні бути власником або адміністратором.
          - Назва не повинна бути зайнята іншою резиденцією.
          Description: Перейменовує місце проживання.
        setallfor:
          Info:
          - '&eВикористання: &6/res setallfor [playerName] [flag] [true/false/remove]'
          Description: Встановити загальні прапори на всіх резиденціях, що належать певному гравцеві
        lease:
          Info:
          - '&eВикористання: &6/res lease [renew/cost] [residence]'
          - /res lease cost покаже вартість продовження оренди житла.
          - /res lease renew продовжить проживання за умови, що у вас є достатньо коштів.
          Description: Керуйте орендою житла
          SubCommands:
            set:
              Description: Встановіть термін оренди
              Info:
              - '&eВикористання: &6/resadmin lease set [residence] [#days/infinite]'
              - Встановлює час оренди на певну кількість днів або нескінченно.
            renew:
              Description: Продовження терміну оренди
              Info:
              - '&eВикористання: &6/resadmin lease renew <резиденція>'
              - Продовження терміну оренди для поточного або вказаного місця проживання.
            list:
              Description: Показати список договорів оренди поточного місця проживання
              Info:
              - '&eВикористання: &6/resadmin lease list <резиденція> <page>'
              - Виводить всі терміни оренди підзон
            expires:
              Description: Дата закінчення оренди
              Info:
              - '&eВикористання: &6/resadmin lease expires <резиденція>'
              - Показує, коли закінчується термін оренди житла.
            cost:
              Description: Показує вартість поновлення
              Info:
              - '&eВикористання: &6/resadmin lease cost <резиденція>'
              - Показує, скільки грошей вам потрібно для продовження оренди житла.
        tp:
          Info:
          - '&eВикористання: &6/res tp [резиденція]'
          - Переміщує вас до резиденції, ви повинні мати доступ до прапора +tp
            або бути власником.
          - Адміністратор сервера також повинен дозволити вашій групі доступу телепортуватися.
          Description: Телепортуйтеся до місця проживання
        setall:
          Info:
          - '&eВикористання: &6/res setall [flag] [true/false/remove]'
          Description: Встановіть загальні прапори на всіх резиденціях
        resreload:
          Description: Перезавантажити резиденцію.
          Info:
          - '&eВикористання: &6/resreload'
        resload:
          Description: Завантажте файл збереження резиденції.
          Info:
          - '&eВикористання: &6/resload'
          - Команда UNSAFE не зберігає житлові будинки в першу чергу.
          - Завантажує файл збереження резиденції після внесення змін.
