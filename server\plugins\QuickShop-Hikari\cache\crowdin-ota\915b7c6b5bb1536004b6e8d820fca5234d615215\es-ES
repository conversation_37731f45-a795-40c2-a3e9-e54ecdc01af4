break-shop-use-supertool: <yellow><PERSON><PERSON><PERSON> romper la tienda usando la SuperTool.
fee-charged-for-price-change: <green>Has pagado <red>{0}<green> para cambiar de precio.
not-allowed-to-create: <red>No puedes crear una tienda aquí.
disabled-in-this-world: <red>QuickShop está deshabilitado en este mundo.
how-much-to-trade-for: <green>Ingresa en el chat, por cuánto deseas intercambiar <yellow>{1}x {0}<green>.
client-language-changed: <green>QuickShop ha detectado que la configuración de idioma del cliente ha sido cambiada, ahora estamos usando la configuración regional de {0} para tí.
shops-backingup: Creando copia de seguridad de la tienda desde la base de datos...
_comment: '¡Hola traductor! Si está editando esto en GitHub o a través del código fuente, debe ir a https://crowdin.com/project/quickshop-hikari.'
unlimited-shop-owner-changed: <yellow>El propietario de esta tienda ilimitada ha cambiado a {0}.
bad-command-usage-detailed: '<red>¡Argumentos incorrectos del comando! Acepta los siguientes parámetros: <gray>{0}'
thats-not-a-number: <red>Número inválido.
shop-name-disallowed: <red>The shop name <yellow>{0} <red>is disallowed. Pick another one!
console-only-danger: <red>Este es un comando peligroso, por lo que solo la consola puede ejecutarlo.
not-a-number: <red>Solo puedes introducir un número, tu entrada era {0}.
not-looking-at-valid-shop-block: <red>No se pudo encontrar un bloque para crear una tienda. Tienes que mirar uno.
shop-removed-cause-ongoing-fee: <red>¡Tu tienda en {0} ha sido eliminada porque no tenías fondos para mantenerla!
tabcomplete:
  amount: '[cantidad]'
  item: '[item]'
  price: '[precio]'
  name: '[name]'
  range: '[rango]'
  currency: '[nombre de divisa]'
  percentage: '[percentage%]'
taxaccount-unset: <green>La cuenta de impuestos de esta tienda ahora está siguiendo la configuración global del servidor.
blacklisted-item: <red>No puedes vender esto porque está en la lista negra
command-type-mismatch: <red>Este comando solo puede ser ejecutado por <aqua>{0}.
server-crash-warning: '<red>El servidor puede fallar después de ejecutar el comando " /qs reload " si reemplaza/borra el archivo .Jar (plugin) de QuickShop mientras se ejecuta el servidor.'
you-cant-afford-to-change-price: <red>Cuesta {0} para cambiar el precio en tu tienda.
safe-mode: <red>QuickShop ahora en modo seguro, no puede abrir este contenedor de la tienda, comuníquese con el administrador del servidor para corregir los errores.
forbidden-vanilla-behavior: <red>La operación está prohibida por no ser compatible con el comportamiento vainilla
shop-out-of-space-name: <dark_purple>¡Tu tienda {0} está llena!
paste-disabled: |-
  <red>Paste functionality has been disabled! You cannot request technical support. <newline>Reason: {0}
quick-fill:
  entry-own: ' <yellow>- <red>[own] <aqua>{0} <light_purple>{1}'
  hover:
    - '<yellow>Nombre: <aqua>{0}'
    - '<yellow>Propietario: <aqua>{0}'
    - '<yellow>Tipo: <aqua>{0}'
    - '<yellow>Precio: <aqua>{0}'
    - '<yellow>Objeto: <aqua>{0}'
    - '<yellow>Ubicación: <aqua>{0}'
  hover-arg-filled:
    - '<yellow>Nombre: <aqua>{name}'
    - '<yellow>Propietario: <aqua>{owner}'
    - '<yellow>Tipo: <aqua>{type}'
    - '<yellow>Precio: <aqua>{price}'
    - '<yellow>Objeto: <aqua>{item}'
    - '<yellow>Ubicación: <aqua>{location}'
  header: '<yellow>You have multiple shops with name "<green>{0}<yellow>", choose one to continue:'
  entry-normal: ' <yellow>- <aqua>{0} <light_purple>{1}'
  entry-cooperation: ' <yellow>- <red>[co] <aqua>{0} <light_purple>{1}'
3rd-plugin-build-check-failed-admin: '<gray>(AdminOnly) <light_purple>{0} <dark_gray>denegó las comprobaciones de permisos, si esto no está excepcionado, intente añadir <light_purple>{1} <gray>a la lista negra de escucha. <gray>Configurar Guía: https://github.com/Ghost-chu/QuickShop-Hikari/wiki/Use-protection-listener-filter'
average-price-nearby: '<green>Precio medio en esta zona: <yellow>{0}'
inventory-check-global-alert: "<red>[[Inventory Check] ¡Aviso! Encontró un elemento de QuickShop <gold>{2}</gold> en inventario en <aqua>{0}</aqua> <dark_gray>[{1}]</dark_gray>, que no debería ocurrir, Esto generalmente significa que alguien está explotando malintencionadamente el exploit para duplicar el elemento de visualización."
digits-reach-the-limit: <red>Has alcanzado el límite de decimales en el precio.
currency-unset: <green>Divisa de la tienda ha sido removida con éxito. Usando la configuración predeterminada ahora.
you-cant-create-shop-in-there: <red>No tienes permiso para crear una tienda en este lugar.
no-pending-action: <red>No tienes ninguna acción pendiente
refill-success: <green>Reabastecido con éxito
failed-to-paste: <red>Fallo al subir datos a Pastebin. Comprueba tu conexión a internet e intenta de nuevo. (Revisa la consola para más detalles)
shop-out-of-stock-name: <dark_purple>¡Tu tienda {0} se ha quedado sin {1}!
shop-name-invalid: <red>The shop name <yellow>{0} <red>is invalid. Pick another one!
how-many-buy-stack: <yellow>Ingrese en el chat, cuantos bultos desea <aqua>COMPRAR<green>. Hay elementos <yellow>{0}<green> en cada lote y puede comprar <yellow>{1}<green> lote. Ingresa <aqua>{2}<green> en el chat para comprar todo.
exceeded-maximum: <red>El valor excedió el valor máximo en Java.
unlimited-shop-owner-keeped: '<yellow>Atención: El dueño de la tienda todavía es un dueño ilimitado de la tienda, necesitas reconfigurar tu nuevo dueño de la tienda.'
no-enough-money-to-keep-shops: <red>¡No dispones del suficiente dinero para mantener tus tiendas! Todas tus tiendas han sido eliminadas...
3rd-plugin-build-check-failed: '<red>El complemento de terceros: <bold>{0}<reset><red> ha denegado la comprobación de permisos, ¿has ajustado los permisos?'
not-a-integer: <red>Solo puedes introducir un número, tu entrada era {0}.
translation-country: '&c&lIdioma: &b&lEspañol (es_ES)'
buying-more-than-selling: '<red>ATENCIÓN: ¡Estás comprando artículos por más de lo que los estás vendiendo!'
purchase-failed: '<red>Compra sin éxito: fallo interno. Por favor, contacta con el administrador del servidor.'
denied-put-in-item: <red>¡No puedes poner este ítem en tu tienda!
shop-has-changed: <red>¡La tienda que trataste de usar ha cambiado desde que hiciste clic!
flush-finished: <green>Mensajes purgados con éxito.
no-price-given: <red>Por favor ingresa un precio válido.
shop-already-owned: <red>Esto ya es una tienda.
backup-success: <green>Copia de seguridad exitosa.
not-looking-at-shop: <red>No se pudo encontrar ninguna tienda. Tienes que mirar hacia una.
you-cant-afford-a-new-shop: <red>Cuesta {0} crear una tienda.
success-created-shop: <red>Tienda creada.
shop-creation-cancelled: <red>Creación de tienda cancelada.
shop-owner-self-trade: <yellow>Estás operando con tu propia tienda, por lo que no ganarás nada de saldo.
purchase-out-of-space: <red>Esta tienda se quedó sin espacio, Contacte con el propietario o personal de la tienda para vaciar la tienda.
reloading-status:
  success: <green>Recarga completada sin errores.
  scheduled: <green>Recarga completada. <gray>(Algunos cambios requieren un tiempo para hacer efecto)
  require-restart: <green>Recarga completada. <gray>(Algunos cambios requieren un reinicio del servidor para hacer efecto)
  failed: <red>Recarga fallida, comprueba la consola del servidor
player-bought-from-your-store-tax: <green>{0} Comprado {1} {2} de tu tienda y ganaste {3} ({4} en impuestos).
not-enough-space: <red>¡Sólo tienes sitio para {0} más!
shop-name-success: <green>Nombre de la tienda establecido a <yellow>{0}<green>.
shop-staff-added: <green>Agregado a {0} como personal de tu tienda con éxito.
shop-staff-empty: <yellow>Esta tienda no tiene personal.
shops-recovering: Recuperando tiendas desde la copia de seguridad...
virtual-player-component-hover: "<gray>Este es un jugador virtual.\n<gray>Hace referencia a una cuenta del sistema con este nombre con el mismo nombre.</gray>\n<green>UUID: <yellow>{0}</yellow></green>\n<green>Nombre de usuario: <yellow>{1}</yellow></green>\n<green>Mostrar como: <yellow>{2}</yellow></green>"
real-player-component-hover: "<gray>Este es un jugador que existe realmente.\n<green>UUID: <yellow>{0}</yellow></green>\n<green>Nombre de usuario: <yellow>{1}</yellow></green>\n<green>Mostrar como: <yellow>{2}</yellow></green>\n<gray>Si desea utilizar una cuenta de sistema virtual con el mismo nombre, agregue <dark_gray>\"[]\"</dark_gray> a ambos lados del nombre de usuario: <dark_gray>[{1}]</dark_gray>."
menu:
  sell-tax: <green>Pagaste <yellow>{0} <green>en impuestos.
  owner: '<green>Propietario: {0}'
  preview: <aqua>[Vista previa del artículo]
  enchants: <dark_purple>Encantamientos
  sell-tax-self: <green>No has pagado impuestos porque esta tienda te pertenece.
  shop-information: '<green>Descripción de tienda:'
  item: '<green>Articulo: <yellow>{0}'
  price-per: <green>Precio por <yellow>{0} <green>- <yellow>{1}
  item-name-and-price: <yellow>{0} {1} <green>por <yellow>{2}
  item-name-and-price-tax: <yellow>{0} {1} <green>for</green> {2} <gray>(<green>{3}</green> in taxes)</gray>
  successful-purchase: '<green>Comprado con éxito:'
  price-per-stack: <green>Precio por <yellow>{2}x {0} - {1}
  stored-enchants: <dark_purple>Encantamientos Almacenados
  item-holochat-error: <red>[Error]
  this-shop-is-selling: <green>Esta tienda está <light_purple>VENDIENDO<green> artículos.
  shop-stack: '<green>Cantidad de stacks: <yellow>{0}'
  space: '<green>Espacio: <yellow>{0}'
  effects: <green>Efectos
  damage-percent-remaining: <yellow>{0}% <green>restante.
  item-holochat-data-too-large: <red>[Error] El elemento NBT es demasiado grande para mostrarlo
  stock: '<green>Existencias <yellow>{0}'
  this-shop-is-buying: <green>Esta tienda está <light_purple>COMPRANDO<green> artículos.
  successfully-sold: '<green>Vendido con éxito:'
  total-value-of-chest: '<green>Valor total del cofre: <yellow>{0}'
currency-not-exists: <red>No se puede encontrar la divisa que quieres establecer, tal vez sea mala ortografía o esa moneda no está disponible en este mundo.
no-nearby-shop: <red>No hay ninguna tienda en la zona que coincida con {0}.
translation-author: 'Ghost_chu, Andre_601, alexbm0'
integrations-check-failed-trade: La integración {0} impide la creación de la tienda
shop-transaction-failed: <red>Sorry, but an internal error occurred while processing your purchase. The purchase has been cancelled and any operation has been rollback. Please contact the server administrators if it keeping occurred.
success-change-owner-to-server: <green>Establecido exitosamente el dueño de la tienda al Servidor.
shop-name-not-found: <red>The shop named <yellow>{0} <red>not exists.
shop-name-too-long: <red>El nombre de la tienda es demasiado largo (logitud máxima {0}), por favor elige otro!
metric:
  header-player: '<yellow>{0}''s {1} {2} transacciones:'
  action-hover: <yellow>{0}
  price-hover: <yellow>Total {0}, incluyendo {1} impuestos.
  unknown: <gray>(desconocido)
  undefined: <gray>(sin nombre)
  no-results: <red>No se encontraron transacciones.
  action-description:
    DELETE: <yellow>Player deleted a shop. And refunded the shop creation fee to owner if possible.
    ONGOING_FEE: <yellow>Player paid the ongoing fee because the payment period.
    PURCHASE_BUYING_SHOP: <yellow>El jugador vendió algunos objetos a una tienda.
    CREATE: <yellow>El jugador creó una tienda.
    PURCHASE_SELLING_SHOP: <yellow>El jugador compró algunos objetos en una tienda
    PURCHASE: <yellow>Objeto comprado en una tienda
  query-argument: 'Argumento de consulta: {0}'
  amount-hover: <yellow>{0}x
  header-shop: '<yellow>La tienda {0}, transacciones:{1} {2} '
  player-hover: |-
    <yellow>{0}<newline><gold>UUID: <gray>{1}
  looking-up: <yellow>Realizando búsquedas métricas, Por favor espere...
  tax-hover: <yellow>{0} impuestos
  header-global: '<yellow>El servidor {0} {1} transacciones:'
  na: <gray>N/A
  transaction-count: <yellow>{0} total
  shop-hover: |-
    <yellow>{0}<newline><gold>Pos: <gray>{1} {2} {3}, Mundo: {4}<newline><gold>Dueño: <gray>{5}<newline><gold>Tipo de tienda: <gray>{6}<newline><gold>Objeto: <gray>{7}<newline><gold>Precio: <gray>{8}
  time-hover: '<yellow>Tiempo: {0}'
  amount-stack-hover: <yellow>pila de{0}
permission-denied-3rd-party: '<red>Permiso denegado: Plugin de terceros [{0}].'
you-dont-have-that-many-items: <red>Solo tienes {0} {1}.
complete: <green>¡Terminado!
translate-not-completed-yet-url: 'La traducción del lenguaje {0} aún no fue completada por {1}. ¿Quieres ayudarnos a mejorar la traducción? Navegar: {2}'
success-removed-shop: <green>La tienda ha sido eliminada.
currency-set: <green>Divisa de la tienda ha sido establecida correctamente a {0}.
shop-purged-start: <green>La purga de la tienda ha comenzado, revisa la consola para más detalles.
economy-transaction-failed: <red>Sorry, but an internal error occurred while processing your transaction. The transaction has been cancelled and any economy operation has been rollback. Please contact the server administrators if it keeping occurred.
nothing-to-flush: <green>No tienes mensajes de tienda nuevos.
no-price-change: <red>¡Esto no cambiaría el precio!
edition-confilct: QuicShop-Hikari with QuickShop-Reremake installed may conflict each other, uninstall one of them.
inventory-unavailable: |-
  <red>This shop InventoryWrapper not exists or invalid. Did you using addon to re-binding the shop Inventory? Information: InventoryWrapper={0}, WrapperProvider={1}, SymbolLink={2}. Please contact the server administrators.
file-test: Este es un archivo de texto de prueba. Lo usamos para probar si el messages.json está roto. Puedes llenarlo con los huevos de pascua que quieras aquí :)
unknown-player: <red>Dicho jugador no existe, por favor revisa el nombre de usuario que escribiste.
player-offline: <red>The target player currently is offline.
player-profile-format: <aqua>{0}</aqua><gold>(<yellow>{1}</yellow>)</gold>
shop-type:
  selling: VENTA
  buying: COMPRA
language:
  qa-issues: '<yellow>Problemas de garantía de calidad: <aqua>{0}%'
  code: '<yellow>Código: <gold>{0}'
  approval-progress: '<yellow>Progreso de aprobación: <aqua>{0}%'
  translate-progress: '<yellow>Progreso de traducción: <aqua>{0}%'
  name: '<yellow>Nombre: <gold>{0}'
  help-us: <green>[Ayúdanos a mejorar la calidad de la traducción]
warn-to-paste: |-
  <yellow>Recopilando datos y subiendo a Pastebin, esto puede llevar un tiempo. <red><bold>Aviso:<red> ¡Los datos serán públicos durante una semana! Esto puede revelar la configuración de tu servidor y otra información sensible. Asegúrate de enviarlo solamente a <bold>personal/desarrolladores de confianza.
how-many-sell-stack: <green>Ingrese en el chat, cuántos lotes desea <light_purple>VENDER<green>. Hay artículos <yellow>{0}<green> por volumen y puede vender <yellow>{1}<green> por volumen. Introduce <aqua>{2}<green> en el chat para vender todo.
updatenotify:
  buttontitle: '[Actualizar Ahora]'
  onekeybuttontitle: '[Actualización Automática]'
  label:
    github: '[GitHub]'
    ore: '[Ore]'
    lts: '[LTS]'
    nbtapi: '[+NBTAPI]'
    qualityverifyed: '[Calidad]'
    master: '[Master]'
    unstable: '[Inestable]'
    paper: '[+Optimizado para Paper]'
    stable: '[Estable]'
    spigotmc: '[SpigotMC]'
    papermc: '[PaperMC]'
    bukkitdev: '[BukkitDev]'
    polymart: '[Polymart]'
    modrinth: '[Modrinth]'
    basic: '[Basic]'
  list:
    - '{0} lanzada. ¡Aún estás usando {1}!'
    - '¡Boom! Nueva actualización {0} a la vista. ¡Actualiza!'
    - '¡Sorpresa! {0} ha salido. Estás en {1}'
    - Parece que necesitas actualizar. ¡{0} ha sido lanzada!
    - Oops! {0} ha sido lanzada. ¡Tú estás en {1}!
    - Te lo juro, QS ha sido actualizada a {0}. ¿Por qué sigues sin actualizar?
    - Corrigiendo y repa... Lo siento, ¡pero {0} ha sido lanzada!
    - '¡Grr! Nop. No es un error. ¡{0} ha sido lanzada!'
    - '¡OMG! {0} ya salió! ¿Por qué aún usas {1}?'
    - 'Noticias del día: ¡QuickShop ha sido actualizado a {0}!'
    - Plugin desparecido en combate. ¡Deberías actualizar a {0}!
    - Actualización {0} prendida. ¡Guárdala!
    - Hay una actualización disponible, comandante. ¡{0} acaba de salir!
    - Mira qué flow que llevo---{0} actualizada. Tú aún usas {1}
    - '¡Ahhhhhhh! ¡Nueva versión {0}! ¡Actualiza!'
    - '¿Qué piensas? ¡{0} ha sido lanzada! ¡Actualiza!'
    - '¡Doctor, QuickShop tiene una nueva actualización {0}! Debería actualizar~'
    - Ko~ko~da~yo~ QuickShop tiene una nueva actualización {0} ~
    - '¡Paimon quiere decirte que QuickShop tiene una nueva actualización {0}!'
  remote-disable-warning: '<red>Esta versión de QuickShop ha sido marcada como deshabilitada por el servidor remoto. Esto significa que esta versión puede tener serios problemas. Más detalles en nuestra página de SpigotMC: {0}. Este aviso aparecerá y te advertirá en tu consola hasta que cambies a una versión aceptada. Sin embargo, esto no tiene por qué afectar al rendimiento de tu servidor.'
purchase-out-of-stock: <red>Esta tienda se quedó sin stock, Contacte con el propietario o personal de la tienda para rellenar el stock.
nearby-shop-entry: '<green>- Info:{0} <green>Precio:<aqua>{1} <green>x:<aqua>{2} <green>y:<aqua>{3} <green>z:<aqua>{4} <green>Distancia: <aqua>{5} <green>Bloque(s)'
chest-title: Tienda de QuickShop
console-only: <red>Este comando solo puede ser ejecutado por Consola.
failed-to-put-sign: <red>No hay espacio suficiente alrededor de la tienda para colocar el cartel informativo.
shop-name-unset: <red>This shop's name now removed
shop-nolonger-freezed: <green>Has descongelado la tienda. ¡Ha vuelto a la normalidad!
no-permission-build: <red>No puedes construir una tienda aquí.
tableformat:
  left_half_line: <dark_purple>+--------------------
  right_half_line: <dark_purple>--------------------+
  full_line: <dark_purple>+---------------------------------------------------+
  left_begin: '<dark_purple>| '
quickshop-gui-preview: Vista previa QuickShop GUI
translate-not-completed-yet-click: La traducción del lenguaje {0} aún no fue completada por {1}. ¿Quieres ayudarnos a mejorar la traducción? ¡Clic aquí!
taxaccount-invalid: <red>La cuenta de destino no es válida, por favor ingrese un nombre de jugador válido o uuid(con guiones).
player-bought-from-your-store: <red>{0} ha comprado {1} {2} por un valor de {3} en tu tienda.
reached-maximum-can-create: <red>¡Ya has creado un máximo de {0}/{1} tiendas!
reached-maximum-create-limit: <red>Has alcanzado el número de tiendas máximas
translation-version: 'Versión de soporte: Hikari'
no-double-chests: <red>No puedes crear tiendas de cofres dobles.
price-too-cheap: <red>El precio debe ser mayor a <yellow>${0}
shop-not-exist: <red>No hay ninguna tienda.
bad-command-usage: <red>¡Argumentos incorrectos!
cleanghost-warning: <yellow>This command will purge <red>all</red> shops if the shop is corrupted, was created in not allowed worlds, is selling/buying not allowed items or <red><bold>EXISTS IN A UNLOADED WORLD</bold></red>. Make sure to create a full backup of your shop data first and use <aqua>/qs cleanghost confirm</aqua> to continue.
cleanghost-starting: <green>Empezando a comprobar si hay tiendas fantasma (faltan bloques de contenedores). Se eliminarán todas las tiendas que no existan...
cleanghost-deleting: <yellow>Has encontrado una tienda corrupta <aqua>{0}</aqua> porque {1}, márcalo para eliminarlo...
cleanghost-deleted: <green>Total <yellow>{0}</yellow> Las tiendas han sido eliminadas.
shop-purchase-cancelled: <red>Compra cancelada.
bypassing-lock: <red>¡Omitiendo el bloqueo de una QuickShop!
bungee-cross-server-msg: '<yellow>Tienda Rápida CSM: <green>{0}'
saved-to-path: Copia de seguridad guardad en {0}.
shop-now-freezed: <green>Has congelado la tienda. ¡Nadie puede comerciar con ella ahora!
price-is-now: <green>El nuevo precio de la tienda es <yellow>{0}
shops-arent-locked: <red>¡Recuerda, las tiendas NO están protegidas contra el robo! ¡Si quieres detener a los ladrones, protege tu cofre con LWC, Lockette, BlockLocker, etc!
that-is-locked: <red>Esta tienda está bloqueada.
shop-has-no-space: <red>La tienda sólo tiene espacio para {0} más {1}.
safe-mode-admin: '<red><bold>ADVERTENCIA: <red>El QuickShop en este servidor ahora se ejecuta en modo seguro, no funcionará ninguna característica, escriba el comando <yellow>/qs <red> para verificar cualquier error.'
shop-stock-too-low: <red>Sólo queda {0} {1} en la tienda!
world-not-exists: <red>El mundo <yellow>{0}<red> no existe
how-many-sell: <green>Ingrese en el chat, cuánto desea <light_purple>VENDER<green>. Puede vender <yellow>{0}<green>. Introduce <aqua>{1}<green> en el chat para vender todo.
shop-freezed-at-location: <yellow>Tú tienda {0} en {1} se ha congelado!
translation-contributors: 'Contribuidores: Randall_Rut, Timtower, Netherfoam, KaiNoMood, Mgazul, JackTheChicken y Andre_601'
empty-success: <green>Vaciado con éxito
taxaccount-set: <green>La cuenta de impuestos de esta tienda ha sido establecida en <yellow>{0}
support-disable-reason:
  hot-reload: <yellow>Unsupported hot reloaded, restart the server and check again.
  outdated: <yellow>This version of QuickShop already outdated, up-to-date before requesting support!
  bad-hosts: |-
    <yellow>This server HOSTS has been modified and QuickShop some functions require connect to Mojang API correctly. Fix the HOSTS before asking support, Windows at C:\windows\system32\drivers\etc\hosts, Linux at /etc/hosts.
  privacy: <yellow>This server running under cracked (offline) mode. If you are running bukkit server under proxy and online-mode=true on proxy, please configure the proxy related settings correctly.
  modified: <yellow>File integrity check failed, this QuickShop build has been modified by others.
  consolespamfix-installed: <yellow>ConsoleSpamFix installed, it will hidden exception details, temporarily disable it during asking support.
  authlib-injector-detected: <yellow>This server running under 3rd-party authlib provider like authlib-injector.
  unsupported-server-software: <yellow>Unsupported server software, Any modded hybrid server software is unsupported including MCPC, Cauldron, CatServer, Mohist, Magma, Fukkit, Cardboard etc.
supertool-is-disabled: <red>Superherramienta deshabilitada. No se puede romper ninguna tienda.
unknown-owner: Desconocido
restricted-prices: '<red>Precio restringido de {0}: Mín. {1}, Máx. {2}'
nearby-shop-this-way: <green>La tienda está a {0} bloques de ti.
owner-bypass-check: <yellow>Pasado todos los controles. ¡Comercio exitoso! (¡Ahora eres el propietario de la tienda!)
april-rick-and-roll-easter-egg: "<green>---------------------------------------------------</green>\n<rainbow><bold>LIMITED TIME EVENT -- QuickShop-Hikari</bold></rainbow>\n<yellow>Get your <gold>PREMIUM</gold> particle effect on all QuickShop server!</yellow>\n<aqua>Claim it by watch the video tutorial below!</aqua>\n<hover:show_text:'<gold>Click to open rewards claim page in your browser.</gold><click:open_url:'https://youtu.be/dQw4w9WgXcQ'>https://youtu.be/dQw4w9WgXcQ</click></hover>\n<green>---------------------------------------------------</green>"
signs:
  item-right: ''
  out-of-space: Sin espacio
  unlimited: Ilimitado
  stack-selling: Vendo {0}
  stack-price: '{0} por {1}x {2}'
  status-unavailable: <red>
  out-of-stock: Agotado
  stack-buying: Compro {0}
  freeze: Comercio desactivado
  price: '{0} c/u'
  buying: Compro {0}
  header: '{1}{0}'
  header-available: <green>{0}
  header-unavailable: <red>{0}
  selling: Vendo {0}
  status-available: <green>
  item-left: ''
negative-amount: <red>No puedes comerciar con valores negativos
display-turn-on: <green>Se encendió con éxito la visualización de la tienda.
shop-staff-deleted: <green>Eliminado a {0} como personal de tu tienda con éxito.
nearby-shop-header: '<green>Tienda cercana que coincida con <aqua>{0}<green>:'
backup-failed: No se puede hacer una copia de seguridad de la base de datos. Revisa la consola para más detalles.
shop-staff-cleared: <green>Todo el personal de tu tienda ha sido eliminado con éxito.
price-too-high: <red>¡El precio establecido es demasiado alto! No puedes crear una tienda con un precio superior a {0}.
plugin-cancelled: '<red>Operation cancelled, Reason: {0}'
player-sold-to-your-store: <green>{0} ha vendido {1} {2} en tu tienda.
shop-out-of-stock: <dark_purple>Tu tienda en {0}, {1}, {2} se ha quedado sin {3}!
how-many-buy: <green>Ingrese en el chat, cuantos desea <aqua>COMPRAR<green>. Puede comprar <yellow>{0}<green>. Introduce <aqua>{1}<green> para comprarlos todos.
language-info-panel:
  help: 'Ayúdanos: '
  code: 'Código: '
  name: 'Idioma: '
  progress: 'Progreso: '
  translate-on-crowdin: '[Traduce en Crowdin]'
item-not-exist: <red>El objeto <yellow>{0} <red>no existe, por favor comprueba tu ortografía.
shop-creation-failed: <red>Falló la creación de la tienda, por favor contacte con el administrador del servidor.
inventory-space-full: <red>Your inventory space remains can only put <green>{1}x <red>items into it, try empty the your inventory!
no-creative-break: <red>No puedes romper las tiendas de otros jugadores en modo Creativo. Cambia a modo Supervivencia o intenta usar superherramienta {0} en su lugar.
booleanformat:
  success: <green>✔
  failed: <red>✘
controlpanel:
  stack: '<green>Per bulk amount: <aqua>{0} <yellow>[<light_purple><bold>Change<yellow>]'
  price-hover: <yellow>Click para establecer un precio nuevo.
  remove: <red><bold>[Eliminar tienda]
  mode-buying-hover: <yellow>Click para cambiar a modo de VENTA.
  empty: '<green>Empty: Remove all items <yellow>[<light_purple><bold>OK<yellow>]'
  stack-hover: <yellow>Click para establecer la cantidad de artículos por stack. Introduce 1 para comportamiento por defecto.
  alwayscounting-hover: <yellow>Haga clic para alternar si la tienda siempre está contando contenedores.
  alwayscounting: '<green>Always counting: {0} <yellow>[<light_purple><bold>Change<yellow>]'
  setowner: '<green>Owner: <aqua>{0} <yellow>[<light_purple><bold>Change<yellow>]'
  freeze: '<yellow>Freeze mode: <aqua>{0} <yellow>[<light_purple><bold>Toggle<yellow>]'
  price: '<green>Price: <aqua>{0} <yellow>[<light_purple><bold>Change<yellow>]'
  currency-hover: <yellow>Click para establecer o remover la divisa que utiliza la tienda
  lock: '<yellow>Shop lock: <aqua>{0} <yellow>[<light_purple><bold>Toggle<yellow>]'
  mode-selling: '<green>Modo: <aqua>Venta <yellow>[<light_purple><bold>Cambiar<yellow>]'
  currency: '<green>Currency: <aqua>{0} <yellow>[<light_purple><bold>Set<yellow>]'
  setowner-hover: <yellow>Click para cambiar el dueño.
  mode-buying: '<green>Shop mode: <aqua>Buying <yellow>[<light_purple><bold>Change<yellow>]'
  item: '<green>Shop Item: {0} <yellow>[<light_purple><bold>Change<yellow>]'
  unlimited: '<green>Unlimited: {0} <yellow>[<light_purple><bold>Change<yellow>]'
  unlimited-hover: <yellow>Click para alternar si la tienda es ilimitada.
  refill-hover: <yellow>Click para reaprovisionar la tienda.
  remove-hover: <yellow>Click para eliminar esta tienda.
  toggledisplay-hover: <yellow>Alternar el estado del mostrar item de la tienda
  refill: '<green>Refill: Refill the items <yellow>[<light_purple><bold>OK<yellow>]'
  freeze-hover: <yellow>Cambiar estado de congelación de la tienda.,
  lock-hover: <yellow>Activar/Desactivar protección contra bloqueo de la tienda.
  item-hover: <yellow>Clic para cambiar el artículo de la tienda
  infomation: '<green>Panel de Control de la tienda:'
  mode-selling-hover: <yellow>Click para cambiar a modo de COMPRA.
  empty-hover: <yellow>Click para limpiar el inventario de la tienda.
  toggledisplay: '<green>Display-Item: <aqua>{0} <yellow>[<light_purple><bold>Toggle</bold></light_purple>]'
  history: '<green>Historial: <yellow>[<bold><light_purple>Ver</light_purple></bold>]</yellow>'
  history-hover: <yellow>Haga clic para ver los registros del historial de la tienda
timeunit:
  behind: atrás
  week: "{0} semana"
  weeks: "{0} Semanas"
  year: "{0} año"
  before: antes
  scheduled: programado
  years: "{0} años"
  scheduled-in: programado en {0}
  second: "{0} segundo"
  std-past-format: '{5}{4}{3}{2}{1}{0}hace'
  std-time-format: HH:mm:ss
  seconds: "{0} segundos"
  hour: "{0} hora"
  scheduled-at: programado en {0}
  after: después
  day: "{0} día"
  recent: reciente
  between: entre
  hours: "{0} horas"
  months: "{0} meses"
  longtimeago: hace mucho tiempo
  between-format: entre {0} y {1}
  minutes: "{0} minutos"
  justnow: justo ahora
  minute: "{0} minuto"
  std-format: MM/dd/yyyy HH:mm:ss
  future-plain-text: futuro
  month: "{0} mes"
  future: en {0}
  days: "{0} días"
command:
  reloading: '<green>Configuration reloaded. <yellow>Some changes may require reboot to affect. <newline><gray>(Notice: Reloading behavior has been changed after ********, we now only reload configuration but not whole plugin to ensure the server won''t crashed.)'
  description:
    buy: <yellow>Cambia la tienda a modo <light_purple>COMPRA<yellow>
    about: <yellow>Muestra información de QuickShop
    language: <yellow>Cambiar el idioma actual
    purge: <green>Iniciar la tarea de purga de la tienda en segundo plano
    paste: <yellow>Carga datos del servidor a Pastebin
    title: <green>Ayuda de QuickShop
    remove: <yellow>Elimina la tienda seleccionada
    ban: <yellow>Banear a un jugador de la tienda
    empty: <yellow>Elimina todos los ítems de una tienda
    alwayscounting: <yellow>Establecer si la tienda siempre cuenta el contenedor incluso es ilimitado
    setowner: <yellow>Cambia al propietario de la tienda.
    reload: <yellow>Recarga el archivo config.yml de QuickShop
    freeze: <yellow>Desactivar o activar el trading en la tienda
    price: <yellow>Cambia el precio de compraventa de la tienda
    find: <yellow>Localiza la tienda más cercana de un tipo específico
    create: <yellow>Crea una tienda nueva en el cofre seleccionado
    lock: <yellow>Cambiar el estado de bloqueo de la tienda
    currency: <yellow>Establece o remueve la configuración de divisa en la tienda
    removeworld: <yellow>se removieron ALL tiendas en el mundo
    info: <yellow>Mostrar estadísticas de QuickShop
    owner: <yellow>Cambia al propietario de la tienda.
    amount: <yellow>Para establecer la cantidad de ítems (útil cuando se tienen problemas con el chat)
    item: <yellow>Cambia el artículo de una tienda
    debug: <yellow>Habilita el modo desarrollador
    unlimited: <yellow>Da un stock ilimitado a una tienda.
    sell: <yellow>Cambia la tienda a modo <aqua>VENTA<yellow>
    fetchmessage: <yellow>Mostrar mensajes de tienda sin leer
    staff: <yellow>Gestionar personal de tu tienda
    clean: <yellow>Suprime todas las tiendas (cargadas) fuera de existencias
    refill: <yellow>Agrega un número dado de artículos a la tienda
    help: <yellow>Muestra la ayuda de QuickShop
    removeall: <yellow>Elimina todas las tiendas de un jugador especificado
    unban: <yellow>Banear a un jugador de la tienda
    transfer: <yellow>Transferir TODAS las tiendas de alguien a otros
    transferall: <yellow>Transferir TODAS las tiendas de alguien a otros
    transferownership: <yellow>Transfiere la tienda que estás viendo a otra persona
    size: <yellow>Cambiar cantidad de artículos por stack de una tienda
    supercreate: '&Crear una tienda circunvalando todas las comprobaciones de seguridad'
    taxaccount: <yellow>Configure la cuenta de impuestos que compra usando
    name: <yellow>Naming a shop to specific name
    toggledisplay: <yellow>Alterna el estado de visualización del artículo de la tienda
    permission: <yellow>Gestión de permisos de la tienda
    lookup: <yellow>Administrar tabla de elementos consultables
    database: <yellow>View and maintain QuickShop database
    benefit: <yellow>Configuración de dividir los beneficios entre el dueño de la tienda y otros jugadores
    tag: <yellow>Añadir, eliminar o consultar etiquetas de una tienda
    suggestprice: <yellow>Sugerir un precio recomendado para mostrar en la tienda, basado en otras tiendas
    history: <yellow>Ver el historial de transacciones de una tienda
    sign: <yellow>Cambiar el material de señal de una tienda
  bulk-size-not-set: '<red>Usa: /qs size <cantidad>'
  no-type-given: '<red>Uso: /qs find <artículo>'
  feature-not-enabled: La característica no está habilitada en la configuración.
  now-debuging: <green>Modo desarrollador habilitado. Recargando QuickShop...
  no-amount-given: <red>No se especificó cantidad. Usa <green>/qs refill <amount><red>
  no-owner-given: <red>No hay dueño proporcionado
  disabled: '<red>Este comando esta deshabilitado: <yellow>{0}'
  bulk-size-now: <green>Ahora intercambiando <yellow>{0}x {1}
  toggle-always-counting:
    counting: <green>Shop ahora siempre cuenta contenedores, incluso es ilimitado
    not-counting: <green>Shop ahora es respeto si la tienda es ilimitada
  cleaning: <green>Elimintando tiendas fuera de stock...
  now-nolonger-debuging: <green>Modo desarrollador deshabilitado. Recargando QuickShop...
  toggle-unlimited:
    limited: <green>La tienda ahora es limitada.
    unlimited: <green>La tienda ahora es ilimitada.
  transfer-success-other: <green>Transferidas <yellow>{0} {1}<green> tienda(s) a <yellow>{2}
  no-trade-item: <green>Por favor mantenga un objeto comercial para cambiar en la mano principal
  wrong-args: <red>Argumento no válido. Usa la ayuda <bold>/qs <red>para ver una lista de comandos.
  some-shops-removed: <yellow>{0} <green>tienda(s) eliminada
  new-owner: '<green>Nuevo dueño: <yellow>{0}'
  format: <green>/{0} {1} <yellow>- {2}
  transfer-success: <green>Transferido <yellow>{0} <green>shop(s) a <yellow>{1}
  now-buying: <green>Ahora <light_purple>COMPRANDO <yellow>{0}
  now-selling: <green>Ahora <light_purple>VENDIENDO <yellow>{0}
  cleaned: <green>Eliminadas <yellow>{0}<green> tiendas.
  trade-item-now: <green>Ahora intercambiando <yellow>{0}x {1}
  no-world-given: <red>Por favor especifica un nombre de mundo
  format-disabled: <red>/{0} {1} <gray>- {2}
  invalid-bulk-amount: <red>El valor dado {0} es mayor que el tamaño máximo de la pila o inferior a uno
currency-not-support: <red>El plugin de economía no soporta funciones multidivisas.
trading-in-creative-mode-is-disabled: <red>No puedes comerciar con esta tienda mientras estás en modo creativo.
the-owner-cant-afford-to-buy-from-you: <red>Esto cuesta {0} pero el propietario de la tienda sólo dispone de {1}
you-cant-afford-shop-naming: <red>No puedes pagar el cambio de nombre de la tienda, cuesta {0}.
inventory-error: |-
  <red>Failed to processing the InventoryWrapper. Did you using addon to re-binding the shop Inventory? Information: Exception={0}, InventoryWrapper={1}, WrapperType={2}, WrapperProvider={3}, SymbolLink={4}. Please contact the server administrators.
integrations-check-failed-create: La integración {0} impide la creación de la tienda
shop-out-of-space: <dark_purple>Tu tienda en {0}, {1}, {2} ahora está llena.
admin-shop: Tienda Admin
no-anythings-in-your-hand: <red>No hay nada en tu mano.
no-permission: <red>No tienes permiso para hacer eso.
chest-was-removed: <red>El cofre ha sido eliminado.
you-cant-afford-to-buy: <red>Cuesta {0} pero sólo tienes {1}
shops-removed-in-world: <yellow>Total <aqua>{0}<yellow> tiendas han sido eliminadas en el mundo <aqua>{1}<yellow>.
display-turn-off: <green>Se apagó con éxito la visualización de la tienda.
client-language-unsupported: <yellow>QuickShop no soporta el idioma de tu cliente, ahora volvemos a {0}.
language-version: '63'
not-managed-shop: <red>No eres el dueño o el moderador de esta tienda
shop-cannot-trade-when-freezing: <red>No puedes comerciar con esta tienda porque se encuentra congelada.
invalid-container: <red>Invalid container, You can only create the shop on block who have inventory.
permission:
  header: <green>Permisos de la tienda
  header-player: <green>Detalles de permisos de la tienda {0}
  header-group: <green>Detalles de permisos de la tienda para el grupo {0}
  table: <gold><b>{0}</b>:</gold> <gray>{1}</gray>
  item:
    purchase: <yellow>Permission to allow users who have this to purchase the shop. (including buying and selling)
    show-information: <yellow>Permiso para permitir a los usuarios que tienen esto ver la información de la tienda. (Abrir panel de información de la tienda)
    preview-shop: <yellow>Permiso para permitir a los usuarios que tienen esto previsualizar la tienda. (vista previa del artículo)
    search: <yellow>Permission to allow users who have this to search the target shop. (remove permission will hide shop from search result)
    delete: <yellow>Permiso para permitir a los usuarios que tienen esto borrar la tienda.
    receive-alert: <yellow>Permission to allow users who have this to receive alert message (e.g out-of-stock or new trade).
    access-inventory: <yellow>Permiso para permitir a los usuarios que tienen esto acceder al inventario de la tienda.
    ownership-transfer: <yellow>Permiso para permitir a los usuarios que tienen esto transferir la propiedad de la tienda.
    management-permission: <yellow>Permission to allow users who have this to manage the groups permissions and edit user's group.
    toggle-display: <yellow>Permiso para permitir a los usuarios que tienen esto alternar el objeto de exhibición de la tienda.
    set-shoptype: <yellow>Permission to allow users who have this to set the shop type (switch buying or selling).
    set-price: <yellow>Permiso para permitir a los usuarios que tienen esto establecer el precio de la tienda.
    set-item: <yellow>Permiso para permitir a los usuarios que tienen esto establecer el artículo de la tienda.
    set-stack-amount: <yellow>Permiso para permitir a los usuarios que tienen esto establecer la cantidad de la pila de la tienda.
    set-currency: <yellow>Permiso para permitir a los usuarios que tienen esto establecer la moneda de la tienda.
    set-name: <yellow>Permiso para permitir a los usuarios que tienen esto establecer el nombre de la tienda.
    set-sign-type: <yellow>Cambia la etiqueta del material que se asignó a la tienda.
    view-purchase-logs: <yellow>Permiso para ver los registros de compras de la tienda.
  group:
    everyone: <yellow>Default group for all users excepted shop owner.
    staff: <yellow>System group for shop staffs.
    administrator: <red>System group for administrators, the users in this group will have permissions almost same with shop owner.
invalid-group: <red>Nombre de grupo inválido.
invalid-permission: <red>Permisos no válidos.
invalid-operation: <red>Operación no válida, solo se permiten {0}.
player-no-group: <yellow>El jugador {0} no está en ningún grupo en esta tienda.
player-in-group: <green>El jugador {0} está en grupo <aqua>{1}</aqua> en esta tienda.
permission-required: <red>No tienes el permiso {0} en esta tienda para hacer eso.
no-permission-detailed: <red>No tienes permiso <yellow>{0}</yellow> para hacer esto.
paste-notice: "<yellow>Nota: Si está creando un Pegado para solucionar problemas, asegúrese de reproducir el error antes de crear el Pegado lo más rápido posible; Necesitamos que los registros se conserven brevemente en el búfer para solucionar problemas. Si crea un Pegado demasiado lento o sin reproducir primero el error o reiniciar el servidor, el Pegado no registrará nada e inútil."
paste-uploading: <aqua>Por favor, espere... Subiendo la copia a Pastebin......
paste-created: '<green>Paste created, click to open in browser: <yellow>{0}</yellow><newline><red>Warning: <gray>Never send paste to who you don''t trust.'
paste-created-local: |-
  <green>Paste created and saved to your local disk at: {0}<newline><red>Warning: <gray>Never send paste to who you don't trust.
paste-created-local-failed: <red>Error al guardar la copia en su disco local, por favor compruebe su disco.
paste-451: |-
  <gray><b>TIPS: </b> Your current country or region seems to have blocked the CloudFlare Workers service and QuickShop Paste may not be loaded properly.<newline><gray>If the subsequent operation fails, try adding the --file additional parameter to generate a local Paste: <dark_gray>/qs paste --file</dark_gray>
paste-upload-failed: <red>Failed to upload paste to {0}, trying other pastebin provider...
paste-upload-failed-local: <red>Failed to upload paste, trying generate local paste...
command-incorrect: '<red>Uso incorrecto del comando, escribe /qs help para consultar ayuda. Uso: {0}.'
successfully-set-player-group: <green>Se ha establecido correctamente el grupo de jugadores {0} a <aqua>{1}</aqua>.
successfully-unset-player-group: <green>Successfully remove player group in this shop.
successfully-set-player-permission: <green>Se ha establecido correctamente el permiso <aqua>{1}</aqua> al jugador{0} en la tienda <aqua>{2}</aqua>.
lookup-item-created: <green>An item named <aqua>{0}</aqua> has been created in the lookup table. You can refer this item in configurations now.
lookup-item-exists: <red>Un elemento llamado <yellow>{0}</yellow> ya existe en la tabla de búsqueda, elimínela o elija otro nombre.
lookup-item-not-found: <red>An item named <yellow>{0}</yellow> not exists.
lookup-item-name-illegal: <red>Item name illegal. Only alphanumeric characters and underscores are allowed.
lookup-item-name-regex: '<red>El nombre debe coincidir con esta expresión regular: <aqua>{0}</aqua>.'
lookup-item-test-not-found: '<gold>Test: <yellow>The item in your hand not registered in lookup table.'
lookup-item-test-found: '<gold>Test: <green>The item in your hand was registered as name <aqua>{0}</aqua> in lookup table.'
lookup-item-removed: <green>The specified item <aqua>{0}</aqua> has been successfully removed from lookup table.
internal-error: <red>Se ha producido un error interno, póngase en contacto con el administrador del servidor.
argument-cannot-be: <red>El argumento <aqua>{0}</aqua> no puede ser <yellow>{1}</yellow>.
argument-must-between: <red>The argument <aqua>{0}</aqua> value must between <yellow>{1}</yellow> and <yellow>{2}</yellow>
invalid-percentage: <red>Porcentaje inválido, debe añadir '%' después del número de porcentaje.
display-fallback: |-
  <red>Debido a un error interno, se muestra un mensaje de reserva.
  Es posible que el valor de este elemento no se haya localizado o procesado correctamente.
  Póngase en contacto con el administrador del servidor.
not-a-valid-time: |-
  <red>The string <yellow>{0}</yellow> not a valid timestamp, please enter a <aqua>Zulu Time (ISO 8601)</aqua> or <aqua>Unix Epoch Time in seconds</aqua>.
  <gold>Valid timestamp example: (for Sat, 17 Dec 2022 10:31:37 GMT)</gold>
  <aqua>-<aqua> <yellow>2022-12-17T10:31:37Z</yellow> <grey>(Zulu Time)</grey>
  <aqua>-<aqua> <yellow>1671273097</yellow> <grey>(Unix Epoch Time in seconds)</grey>
invalid-past-time: <red>No se puede especificar una hora en el pasado.
debug:
  arguments-invalid: <red>Provided arguments <yellow>{0}</yellow> invalid.
  sign-located: '<green>Sign valid: <yellow>{0}</yellow>.'
  operation-missing: <red>You must specific an operation.
  operation-invalid: <red>You must specific an valid operation.
  invalid-base64-encoded-sql: <red>The SQL provided must be base64 encoded.
  warning-sql: |-
    <red><bold>Warning:</bold></red><yellow>You're executing a SQL statement, this may corrupt your database or destroy any data in database even it belongs to other plugins.
    <red>Don't confirm if you don't trust who send you this command.
  warning-sql-confirm: <yellow>To confirm this dangerous operation, please type <aqua>/qs debug database sql confirm {0}</aqua> in 60 seconds.
  warning-sql-confirm-hover: <yellow>Click to confirm this dangerous operation.
  sql-confirm-not-found: <yellow>Didn't found the operation that you given, it may invalid or expired.
  sql-executing: '<yellow>Executing SQL statement: <aqua>{0}</aqua>'
  sql-completed: <green>Completed, {0} rows affected.
  sql-exception: <red>An error occurred while executing the SQL query, check the Console for details!
  sql-disabled: '<red>For security reason, SQL queries are disabled in this server, If you really need this feature, you can add the following flag to startup arguments: <aqua>{0}</aqua> with `true` value to enable it.'
  force-shop-reload: <yellow>Forzando la recarga de todas las tiendas cargadas...
  force-shop-reload-complete: <green>Forzado <aqua>{0}</aqua> Tiendas para ser recargadas.
  force-shop-loader-reload: <yellow>Forzando la recarga del cargador...
  force-shop-loader-reload-unloading-shops: <yellow>Descargando <aqua>{0}</aqua> tiendas cargadas...
  force-shop-loader-reload-unloading-shops-from-memory: <yellow>Eliminando <aqua>{0}</aqua> tiendas de memoria...
  force-shop-loader-reload-reloading-shop-loader: <yellow>Volver a llamar al cargador de tiendas para recargar todas las tiendas desde la base de datos...
  force-shop-loader-reload-complete: <green>¡El cargador de tiendas se ha recargado en todas las tiendas!
  toggle-shop-loaded-status: <aqua>Toggle this shop loaded status to <gold>{0}</gold>
  shop-internal-data: '<yellow>The internal data of this shop: <reset>{0}</reset>'
  handler-list-not-valid-bukkit-event-class: <red>La clase proporcionada <yellow>{0}</yellow> no es una clase de evento Bukkit válida.
  update-player-shops-signs-no-username-given: <red>Debe proporcionar un nombre de usuario de jugador válido.
  update-player-shops-signs-create-async-task: ...
  update-player-shops-player-selected: '<yellow>Player selected: <gold>{0}</gold>'
  update-player-shops-player-shops: <yellow>Total <gold>{0}</gold> Tiendas a la espera de actualizaciones.
  update-player-shops-per-tick-threshold: '<yellow>Max shops can be update per tick: <gold>{0}</gold>'
  update-player-shops-complete: <green>Tarea completada, Utilizada <yellow>{0}Sra.</yellow> para actualizar.
  update-player-shops-task-started: <gold>Las tareas se han iniciado, espere a que se completen.
  item-info-store-as-string: "<green>The store you looking at: <gold>{0}</gold> Hash: <white>{1}</white>"
  item-info-hand-as-string: "<green>The item in your hand: <gold>{0}</gold> Hash: <white>{1}</white>"
  item-matching-result: "<green>Tienda a la mano: <aqua>{0}</aqua>, Tienda a la mano: <aqua>{1}</aqua>"
  hikari-cp-size-tweak: "<green>HikariCP MaximumPoolSize and MinimumIdle was set to <white>{0}</white>"
  hikari-cp-testing: "<green>Por favor, espere, compruebe la conexión HikariCP..."
  hikari-cp-working: "<green>¡Pasar! ¡HikariCP funcionando bien!"
  hikari-cp-not-working: "<red>¡Fracasado! ¡La conexión que devolvió HikariCP está muerta! (No pasó la prueba en 1 segundo)"
  hikari-cp-timeout: "<red>HikariCP is timed out while getting a valid connection, please clean up all active queries to release connection resources."
  queries-stopped: "<green>Stopped <white>{0}</white> active queries."
  queries-dumping: "<yellow>Volcando consultas activas..."
  restart-database-manager: "<yellow>Reiniciando SQLManager..."
  restart-database-manager-clear-executors: "<yellow>Limpiando ejecutores..."
  restart-database-manager-unfinished-task: "<yellow>Tarea inconclusa: <white>{0}</white>"
  restart-database-manager-unfinished-task-history-query: "<yellow>Unfinished task (History Query): <white>{0}</white>"
  restart-database-manager-reconnect: "<yellow>Re-launching SQLManager via initial sequence (via async executor)"
  restart-database-manager-done: "<green>¡Hecho!"
  property-incorrect: "<yellow>You must enter a (and only one) property key=value set. E.g   aaa=bbb"
  property-security-block: "<red>Request was rejected, for security reason, you can only change the property that startsWith <aqua>com.ghostchu.quickshop</aqua> or <aqua>quickshop</aqua>."
  property-removed: "<green>Removed property key <white>{0}</white>"
  property-changed: "<green>Property key <white>{0}</white> was changed from <white>{1}</white> to <white>{2}</white>"
  marked-as-dirty: "<green>Marked all stores as dirty status, they will be force save in next auto-save task. (Restart the server to force execute store saving task)"
  display-removed: "<green>Eliminado con éxito <yellow>{0}</yellow> QuickShop muestra objetos/entidades de los mundos."
database:
  scanning: <green>Scanning the isolated data in QuickShop databases, the database load may increase in scan progress, this might need a while...
  scanning-async: <yellow>Scanning the isolated data in QuickShop databases on async task thread, the database load may increase in scan progress, this might need a while, try again later.
  already-scanning: <red>An scan task has been started, please wait until it finished.
  trim-warning: '<yellow><red><bold>Warning: </red>Backup your database before continue database trim to avoid the data loss. Once you get ready, execute <aqua>/qs database trim confirm</aqua> to continue.'
  status: '<yellow>Estado: {0}'
  status-good: <green>Bien
  status-bad: <yellow>Maintenance Required
  isolated: '<yellow>Datos aislados:'
  isolated-data-ids: '<yellow><aqua>└</aqua> Data Records: <gold>{0}</gold>'
  isolated-shop-ids: '<yellow><aqua>└</aqua> Shop Indexes: <gold>{0}</gold>'
  isolated-logs: '<yellow><aqua>└</aqua> Logs: <gold>{0}</gold>'
  isolated-external-caches: '<yellow><aqua>└</aqua> External Caches: <gold>{0}</gold>'
  last-purge-time: <yellow>Última vez de escaneo en {0}
  report-time: <yellow>Última vez de escaneo en {0}
  auto-scan-alert: <yellow>The QuickShop database on this server require a maintenance. Found <gold>{0}</gold> isolated data wait for trimming.
  auto-trim: <green>Auto trim has been enabled on this server, no manually trim required.
  trim-complete: <green>Database trim completed, <yellow>{0}</yellow> isolated trimmed.
  auto-trim-started: <green>Auto trim has been started, please wait...
  trim-start: <green>Database trim started, please wait...
  trim-exception: <red>Database trim failed, an exception raised during database trimming, check the server console.
  generated-at: '<yellow>Generated at: <gold>{0}</gold>'
  purge-date: <red>You must given a date.
  purge-warning: <yellow>This operation will purge the history that stored in your database including shop creates/changes/deletes, purchases, transactions and system logs. Delete those data can free the disk space but all history metric will lost, and other plugins that depend on metrics will stop working. To continue execute this operation, execute `/qs database purgelogs <before-days> confirm`
  purge-task-created: <green>Tarea creada! La base de datos son registros de historial de purga silenciosa en segundo plano.
  purge-done-with-line: <green>Purgación de tarea completada, total <gold>{0}</gold> líneas purgadas de la base de datos.
  purge-done-with-error: <red>Purge task failed, check the server console for details.
  purge-players-cache: <yellow>Please wait, purging players caches...
  purge-players-completed: |-
    <green>Successfully purged {0} players caches from both memory and database.<newline><aqua>Note: You server performance may impact by this operation.
  purge-players-error: <red>Failed to purge players caches, please check the server console.
  suggestion:
    trim: <yellow>This database require a isolated data trim, execute command <aqua>/qs database trim</aqua> to trim the database.
always-counting-removal-early-warning: <red>Always Counting feature is scheduled for removal, you shouldn't use it anymore and will stop work in future.
exporting-database: <green>Exportando base de datos, por favor espere...
exporting-failed: <red>Error al exportar la base de datos, por favor comprueba la consola del servidor.
exported-database: <green>Base de datos exportada a <yellow>{0}</yellow>.
importing-not-found: <red>The <yellow>{0}</yellow> file not found, please check the file path.
importing-early-warning: |-
  <yellow><red><bold>Warning: </bold></red>The backup will be imported to the current database, any exists data will be purged and permanently lost if you don't have a backup.<newline><red>Are you sure to continue the import progress?</red><yellow>Type <aqua>/qs recovery confirm</aqua> to continue.
importing-database: <green>Importando base de datos desde la copia de seguridad, por favor espere...
importing-failed: <red>Error al importar la base de datos, por favor comprueba la consola del servidor.
imported-database: <green>Base de datos importada de <yellow>{0}</yellow>.
transfer-sent: <green>Shops transfer request sent to <yellow>{0}</yellow>.
transfer-request: <yellow>Player <aqua>{0}</aqua> requested to transfer their shops to you. Did you want accept the transfer?
transfer-single-request: <yellow>Player <aqua>{0}</aqua> would like to transfer a shop to you. Do you want to accept the request?
transfer-ask: |-
  <gold>Type <red>/qs transfer accept</red> to accept.</gold><newline><gold>Type <red>/qs transfer deny</red> to deny.</gold><newline><gold>Request will expire after <red>{0}</red> seconds.
transferall-ask: |-
  <gold>Type <red>/quickshop transferall accept</red> to accept or <red>/quickshop transferall deny</red> to deny.
  The request will expire after <red>{0}</red> seconds.
transfer-single-ask: |-
  <gold>Type <red>/qs transfer accept</red> to accept.</gold><newline><gold>Type <red>/qs transfer deny</red> to deny.</gold><newline><gold>Request will expire after <red>{0}</red> seconds.
transfer-accepted-fromside: <green>Player <aqua>{0}</aqua> accepted your shop transfer request.
transfer-accepted-toside: <green>You accepted <aqua>{0}</aqua>'s transfer request.
transfer-rejected-fromside: <red>Player <aqua>{0}</aqua> rejected your shop transfer request.
transfer-rejected-toside: <red>You rejected <aqua>{0}</aqua>'s shop transfer request.
transfer-no-pending-operation: <red>You don't have pending transfer request yet.
transfer-no-self: <red>No puedes transferir tus tiendas a ti mismo.
benefit-overflow: <red>La suma de todos los beneficios no puede ser mayor o igual al 100%.
benefit-exists: <red>Jugador objetivo que ya está en la lista de beneficios de esta tienda.
benefit-removed: <red>El jugador objetivo se ha eliminado de los beneficios de la tienda.
benefit-added: '¡<green>Jugador <aqua>{0}</aqua> se ha añadido a los beneficios de la tienda!'
benefit-updated: <green>Jugador <aqua>{0}</aqua>¡Los beneficios han sido actualizados!
benefit-query: <green>Esta tienda tiene <yellow>{0}</yellow> ¡Jugadores en la lista de beneficios!
benefit-query-list: <yellow> - </yellow><white>Player <gold>{0}</gold>, Benefit <gold>{1}%</gold></white>
tag-added: '<green>Añadido con éxito <aqua>#{0}</aqua> a esta tienda!'
tag-add-duplicate: '<red>¡La etiqueta <aqua>#{0}</aqua> ya existe en esta tienda!'
tag-removed: '<green>Eliminado con éxito <aqua>#{0}</aqua> de esta tienda!'
tag-remove-not-exists: '¡La etiqueta <aqua>#{0}</aqua> ya existe en esta tienda!'
tag-cleared: <green>¡Se han eliminado todas las etiquetas de esta tienda!
tag-shops-cleared: '<green>¡Se ha limpiado con éxito <aqua>#{0}</aqua> de todas tus tiendas etiquetadas!'
tag-query: '<green>Esta tienda tiene <yellow>{0}</yellow> Etiquetas:'
tag-query-listing: '<yellow> - <aqua>#{0}</aqua>'
tag-query-no-tag: <red>Esta tienda no tiene etiquetas.
tag-query-shops: '<green>Esta etiqueta contiene <yellow>{0}</yellow> tiendas:'
tag-query-shops-listing: <yellow> - <aqua>{0}</aqua>
batch-operations-based-on-tags-no-failure: <green>Lotes procesados con éxito <yellow>{0}</yellow> tiendas.
batch-operations-based-on-tags-have-failure: <yellow>Total <yellow>{0}</yellow> shops in batch processing successfully completed, but <red>{1}</red> requests not completed.
batch-operations-based-on-tags-have-failure-with-reason: '<yellow>Total <yellow>{0}</yellow> shops in batch processing successfully completed, but <red>{1}</red> requests not completed. Reason: <gold>{2}</gold>'
batch-operations-based-on-tags-have-failure-error-messages-may-appears-above: <gray>Los mensajes de error se le enviarán encima de este mensaje en el chat.
addon:
  towny:
    commands:
      town: <yellow>Establecer o desactivar una tienda en una tienda de Town
      nation: <yellow>Establecer o desactivar una tienda en una tienda de Nación
    make-shop-owned-by-town: <green>Has hecho la tienda propiedad de la ciudad <yellow>{0}</yellow>.
    make-shop-no-longer-owned-by-town: <green>Ha restablecido la propiedad de la tienda, ahora se transfiere de nuevo al propietario original de la tienda.
    make-shop-owned-by-nation: <green>Has hecho que la tienda sea propiedad de la nación <yellow>{0}</yellow>.
    make-shop-no-longer-owned-by-nation: <green>Ha restablecido la propiedad de la tienda, ahora se transfiere de nuevo al propietario original de la tienda.
    shop-owning-changing-notice: <grey>This shop now owned by a town/nation, original shop owner has been automatically added into administrator list, modify or add any new owner / staff by using command /qs permission
    target-shop-already-is-town-shop: <red>The target shop is already owned by a town.
    target-shop-already-is-nation-shop: <red>The target shop is already owned by a nation.
    target-shop-not-in-town-region: <red>The target shop is not in the town.
    target-shop-not-in-nation-region: <red>The target shop is not in the nation.
    item-not-allowed: <red>This shop's item are not allowed used by town/nation shop, pick another one!
    operation-disabled-due-shop-status: <red>This shop operation has been disabled due this already is a town/nation shop.
    plot-type-disallowed: <red>You can't create town/nation shop on this type of plot.
    flags:
      own: .
      modify: <red>You didn't have build permission on this towny plot.
      shop-type: <red>You must create shop on a shop type towny plot.
  residence:
    creation-flag-denied: <red>You don't have permission to create shops in this residence.
    trade-flag-denied: <red>You don't have permission to purchase in this residence.
    you-cannot-create-shop-in-wildness: <red>You can't create shop in wildness.
  griefprevention:
    creation-denied: <red>You don't have permission to create shops in this claim.
    trade-denied: <red>You don't have permission to purchase shops in this claim.
  lands:
    world-not-enabled: <red>You are not allowed to create or purchase in this world.
    creation-denied: <red>You don't have permission to create shops in this Land area.
  plotsquared:
    no-plot-whitelist-creation: <red>You cannot create shop outside of Plot.
    no-plot-whitelist-trade: <red>You cannot purchase shop outside of Plot.
    creation-denied: <red>You don't have permission to create shops in this Plot.
    trade-denied: <red>You don't have permission to purchase shops in this Plot.
    flag:
      create: Create QuickShop-Hikari shops
      trade: Purchase QuickShop-Hikari shops
  superiorskyblock:
    owner-create-only: <red>Only island owner can create shop there.
    owner-member-create-only: <red>Only island owner or member can create shop there.
  worldguard:
    creation-flag-test-failed: <red>You don't have permission create shop in this WorldGuard area.
    trade-flag-test-failed: <red>You don't have permission trade shop in this WorldGuard area.
    reached-per-region-amount-limit: "<red>You have reached the maximum amount of shops in this region."
  discord:
    __to_translators: 'To translators: `addon.discord.discord-messages` use a special variable in strings, %%LANG_KEY%% instead of QuickShop''s style {LANG_INDEX}, do not translate anything inside double ''%'' section. BUT for any other section texts, it respect the normal QuickShop Text System syntax ({0},{1} etc.) This may be difficult to understand because two different variable forms are used in the same program at the same time, but trust me, it is reasonable. (you don''t need translate this string, it''s a comment)'
    __to_users: Like the QuickShop Text System, Discord addon will also auto-detect user language and use user's language to send Discord message, it following the QuickShop-Hikari's language system settings.
    __to_message_designer: 'Design your Discord message with GUI: https://glitchii.github.io/embedbuilder/, then copy the JSON code and paste it into the translation and here we go!'
    discord-enabled: <aqua>Successfully <green>enabled</green> your QuickShop discord messages, now you can receive the shop messages from Discord.
    discord-disabled: <aqua>Successfully <red>disabled</red> your QuickShop discord messages, now you won't receive the shop messages from Discord anymore.
    discord-not-integrated: <red>You hadn't link your Discord yet! Please link your Discord account first!
    feature-enabled-for-user: <aqua>You have <green>enabled</green> <gold>{0}</gold> notification.
    feature-disabled-for-user: <aqua>You have <red>disabled</red> <gold>{0}</gold> notification.
    link-help: <yellow>This server using <gold>{0}</gold> for Discord driver, please use <green>{0}</green> to link your Discord account.
    save-notifaction-exception: <red>An error occurred while saving your Discord notifaction settings, please contact the server administrator.
    feature-status-changed: <green>Successfully set the notifaction <aqua>{0}</aqua> status to <gold>{1}</gold>
    commands:
      discord:
        description: <yellow>Managing your QuickShop Discord settings
    discord-messages:
      sold-to-your-shop: |
        {
         "embed":
           {
             "title": ":inbox_tray: Somebody sold items to your shop",
             "description": "The player %%purchase.name%% sold x%%purchase.amount%% %%shop.item.name%% to your shop.",
             "color": 52084,
             "author": {
               "name": "QuickShop-Hikari",
               "icon_url": ""
             },
             "thumbnail": {
               "url": ""
             },
             "image": {
               "url": ""
             },
             "footer": {
               "text": "QuickShop-Hikari Discord Notification",
               "icon_url": ""
             },
             "fields": [
               {
                 "name": "Shop",
                 "value": "%%shop.display-name%%",
                 "inline": false
               },
               {
                 "name": "Purchaser",
                 "value": "%%purchase.name%%",
                 "inline": true
               },
               {
                 "name": "Item",
                 "value": "%%shop.item.name%%",
                 "inline": true
               },
               {
                 "name": "Amount",
                 "value": "%%purchase.amount%% (x%%shop.stacking-amount%%)",
                 "inline": true
               },
               {
                 "name": "Your paid",
                 "value": "%%purchase.balance-formatted%%",
                 "inline": true
               },
               {
                 "name": "Taxes",
                 "value": "%%purchase.taxes-formatted%%",
                 "inline": true
               }
             ]
           }
        }
      bought-from-your-shop: |
        {
           "embed":
             {
               "title": ":outbox_tray: Somebody bought items from shop",
               "description": "The player %%purchase.name%% bought x%%purchase.amount%% %%shop.item.name%% from your shop.",
               "color": 52084,
               "author": {
                   "name": "QuickShop-Hikari",
                   "icon_url": ""
               },
               "thumbnail": {
                   "url": ""
               },
               "image": {
                   "url": ""
               },
               "footer": {
                   "text": "QuickShop-Hikari Discord Notification",
                   "icon_url": ""
               },
               "fields": [
                   {
                       "name": "Shop",
                       "value": "%%shop.display-name%%",
                       "inline": false
                   },
                   {
                       "name": "Purchaser",
                       "value": "%%purchase.name%%",
                       "inline": true
                   },
                   {
                       "name": "Item",
                       "value": "%%shop.item.name%%",
                       "inline": true
                   },
                   {
                       "name": "Amount",
                       "value": "%%purchase.amount%%(x%%shop.stacking-amount%%)",
                       "inline": true
                   },
                   {
                       "name": "Your earn",
                       "value": "%%purchase.balance-formatted%%",
                       "inline": true
                   },
                   {
                       "name": "Taxes",
                       "value": "%%purchase.taxes-formatted%%",
                       "inline": true
                   }
               ]
           }
         }
      mod-shop-purchase: |
        {
        "embed":
          {
              "title": ":dollar: Somebody purchased in a shop",
              "description": "The player %%purchase.name%% purchase x%%purchase.amount%% %%shop.item.name%% from the shop.",
              "color": 6061450,
              "author": {
                  "name": "QuickShop-Hikari",
                  "icon_url": ""
              },
              "thumbnail": {
                  "url": ""
              },
              "image": {
                  "url": ""
              },
              "footer": {
                  "text": "QuickShop-Hikari Discord Notification",
                  "icon_url": ""
              },
              "fields": [
                  {
                      "name": "Shop",
                      "value": "%%shop.display-name%%",
                      "inline": false
                  },
                  {
                      "name": "Purchaser",
                      "value": "%%purchase.name%%",
                      "inline": true
                  },
                  {
                      "name": "Item",
                      "value": "%%shop.item.name%%",
                      "inline": true
                  },
                  {
                      "name": "Amount",
                      "value": "%%purchase.amount%% (x%%shop.stacking-amount%%)",
                      "inline": true
                  },
                  {
                      "name": "Balance",
                      "value": "%%purchase.balance-formatted%%",
                      "inline": true
                  },
                  {
                      "name": "Taxes",
                      "value": "%%purchase.taxes-formatted%%",
                      "inline": true
                  }
              ]
          }
        }
      out-of-space: |
        {
        "embed":
          {
              "title": ":mailbox_with_mail: Your shop has run out of space",
              "description": "Your shop inventory is full of items!\nYou need empty your shop container to free the space then you can continue to accept new items.",
              "color": 16065893,
              "author": {
                  "name": "QuickShop-Hikari",
                  "icon_url": ""
              },
              "thumbnail": {
                  "url": ""
              },
              "image": {
                  "url": ""
              },
              "footer": {
                  "text": "QuickShop-Hikari Discord Notification",
                  "icon_url": ""
              },
              "fields": [
                  {
                      "name": "Shop",
                      "value": "%%shop.display-name%%",
                      "inline": false
                  }
              ]
          }
        }
      out-of-stock: |
        {
            "embed":
                {
                    "title": ":mailbox_with_no_mail: Your shop has run out of stock",
                    "description": "Your shop inventory is empty now!\nYou need to refill the shop container with items to continue selling items!",
                    "color": 16065893,
                    "author": {
                        "name": "QuickShop-Hikari",
                        "icon_url": ""
                    },
                    "thumbnail": {
                        "url": ""
                    },
                    "image": {
                        "url": ""
                    },
                    "footer": {
                        "text": "QuickShop-Hikari Discord Notification",
                        "icon_url": ""
                    },
                    "fields": [
                        {
                            "name": "Shop",
                            "value": "%%shop.display-name%%",
                            "inline": false
                        }
                    ]
                }
        }
      mod-shop-created: |
        {
          "embed": {
            "title": ":classical_building: A new shop has been created",
            "description": "Player created a new shop on your server!",
            "color": 6061450,
            "author": {
              "name": "QuickShop-Hikari",
              "icon_url": ""
            },
            "thumbnail": {
              "url": ""
            },
            "image": {
              "url": ""
            },
            "footer": {
              "text": "QuickShop-Hikari Discord Notification",
              "icon_url": ""
            },
            "fields": [
              {
                "name": "Shop",
                "value": "%%shop.display-name%%",
                "inline": false
              },
              {
                "name": "Owner",
                "value": "%%shop.owner.name%%",
                "inline": true
              },
              {
                "name": "Item",
                "value": "%%shop.item.name%%",
                "inline": true
              },
              {
                "name": "Amount",
                "value": "%%shop.stacking-amount%%",
                "inline": true
              },
              {
                "name": "Type",
                "value": "%%shop.type%%",
                "inline": true
              }
            ]
          }
        }
      mod-remove-shop: |
        {
            "embed": {
                "title": ":recycle: A shop was removed from this server",
                "description": "A shop was removed from this server.",
                "color": 6061450,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "Reason",
                        "value": "%%delete.reason%%",
                        "inline": false
                    }
                ]
            }
        }
      shop-transfer-to-you: |
        {
            "embed": {
                "title": ":placard: A shop was transfer to you",
                "description": "A shop transfer to you from another player.",
                "color": 16172079,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "From",
                        "value": "%%transfer.from%%",
                        "inline": false
                    }
                ]
            }
        }
      mod-shop-transfer: |
        {
            "embed": {
                "title": ":placard: A shop was transferred",
                "description": "A shop transfer to a player from another player.",
                "color": 6061450,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "From",
                        "value": "%%transfer.from%%",
                        "inline": true
                    },
                    {
                        "name": "To",
                        "value": "%%transfer.to%%",
                        "inline": true
                    }
                ]
            }
        }
      shop-price-changed: |
        {
            "embed": {
                "title": ":money_with_wings: Your shop price was changed",
                "description": "You or your shop staff changed your shop price.",
                "color": 16172079,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "From",
                        "value": "%%change-price.from%%",
                        "inline": true
                    },
                    {
                        "name": "To",
                        "value": "%%change-price.to%%",
                        "inline": true
                    }
                ]
            }
        }
      mod-shop-price-changed: |
        {
            "embed": {
                "title": ":money_with_wings: A shop price has been changed",
                "description": "A shop changed its price settings.",
                "color": 6061450,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": true
                    },
                    {
                        "name": "Owner",
                        "value": "%%shop.owner.name%%",
                        "inline": true
                    },
                    {
                        "name": "",
                        "value": "",
                        "inline": true
                    },
                    {
                        "name": "From",
                        "value": "%%change-price.from%%",
                        "inline": true
                    },
                    {
                        "name": "To",
                        "value": "%%change-price.to%%",
                        "inline": true
                    }
                ]
            }
        }
      shop-permission-changed: |
        {
            "embed": {
                "title": ":closed_lock_with_key: Your shop permission settings has been changed",
                "description": "One of your shop's permission setting has been changed.",
                "color": 15879747,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "Player",
                        "value": "%%change-permission.player%%",
                        "inline": true
                    },
                    {
                        "name": "Assign to group",
                        "value": "%%change-permission.to-group%%",
                        "inline": true
                    },
                    {
                        "name": "",
                        "value": "",
                        "inline": true
                    },
                    {
                        "name": "Permission granted (Inherit from group)",
                        "value": "```\n%%change-permission.perms-list%%\n```",
                        "inline": false
                    }
                ]
            }
        }
    field:
      player: Player
      item: Item
      amount: Amount
      balance: Saldo
      balance-after-tax: Saldo (después de impuestos)
      account: Saldo de cuenta
      taxes: Impuestos
      cost: Coste
  discount:
    commands:
      discount:
        description: <yellow>Apply a discount code or manage your discount codes.
    tab-complete:
      discount:
        general:
          code: <code>
        create:
          rate: |
            Command Hint:
            Argument: <rate>
            Description: The actual percentage or money you will earn
            Input `30%` = price * 0.3
            Input `50` = price - 50
          max-usage: |
            Command Hint:
            Argument: [max-usage]
            Description: The times that code can be used
            `-1` for unlimited.
          threshold: |
            Command Hint:
            Argument: [threshold]
            Description: The minimum price that code can be applied
            `-1` for unlimited
          expired: |
            Command Hint
            Argument: [expired]
            Description: The code expired time.
            `-1` for unlimited duration.
            Accept both Zulu time and UNIX timestamp in seconds.
            Zulu Example: 2022-12-17T10:31:37Z
            UNIX Example: 1671273097
    discount-code-already-exists: <red>Lo sentimos, el nombre de su código de descuento ya está en uso.
    invalid-discount-code-regex: '<red>The discount code must matches the regex: <yellow>{0}</yellow>'
    invalid-discount-code: <red>Este código de descuento no es válido.
    discount-code-added: <green>Your discount code <yellow>{0}</yellow> has been added into shop <aqua>{1}</aqua>
    discount-code-removed: <green>Your discount code <yellow>{0}</yellow> has been removed from shop <aqua>{1}</aqua>
    invalid-code-type: <red>The code type <yellow>{0}</yellow> are invalid.
    invalid-usage-restriction: <red>The usage restriction <yellow>{0}</yellow> are invalid.
    invalid-threshold-restriction: <red>The threshold restriction <yellow>{0}</yellow> are invalid.
    invalid-effect-scope: <red>The scope <yellow>{0}</yellow> are invalid.
    invalid-expire-time: <red>No se puede especificar una hora en el pasado.
    invalid-discount-rate: <red>The discount rate <yellow>{0}</yellow> are invalid, it can be a fixed number or percentage.
    discount-code-expired: <red>Ouch! Your discount code <yellow>{0}</yellow> has been expired!
    discount-code-installed: <green>You have installed discount code <gold>{0}</gold>, the discount code will be automatically applied to the available purchase in all the following purchases during this session. To uninstall discount code, execute <aqua>/qs discount uninstall {0}</aqua>.
    discount-code-uninstalled: <green>You have uninstalled your discount code.
    discount-code-query-nothing: <red>You hadn't install a discount code yet!
    discount-code-query: '<green>You are using discount code: <yellow>{0}</yellow>.'
    discount-code-applicable: '<color:#bcef26>Your discount code <yellow><bold>{0}</bold></yellow> are <bold>applicable</bold> in this shop!'
    discount-code-applicable-with-threshold: '<color:#bcef26>Your discount code <yellow><bold>{0}</bold></yellow> are <bold>applicable</bold> in this shop, but only your purchase cost over <yellow>{1}</yellow> in single transaction.'
    discount-code-not-applicable: <red>Your discount code <yellow><bold>{0}</bold></yellow> are <bold>not applicable</bold> in this shop!
    discount-code-reach-the-limit: <red>You have reach the usage limit of discount code <yellow><bold>{0}</bold></yellow>, discount are not applied.
    discount-code-no-permission: <red>You didn't have permission to use any discount code in this shop!
    discount-code-has-been-expired: <red>Your discount has been expired!
    discount-code-config-shop-added: <green>Successfully to add this shop into your discount code allow list.
    discount-code-config-shop-add-failure: <red>This shop already in your discount code allow list.
    discount-code-config-shop-removed: <green>Successfully to remove this shop from your discount code allow list.
    discount-code-config-shop-remove-failure: <red>This shop not exists in your discount code allow list.
    discount-code-config-expire: <green>Successfully changed your code expire time.
    discount-code-config-applied: <green>Config the discount code successfully!
    discount-code-created-successfully: |
      <green>Your discount code <yellow>{0}</yellow> has been successfully created!
      <gold>Scope: <aqua>{1}</aqua></gold>.
      <gold>Share with others: <color:#bcef26>{2}</color:#bcef26></gold>.
      <yellow>For <gray>Specific Shops</gray> scope only: To add shops to your discount code allow list, please looking an quickshop and execute <aqua>{3}</aqua> command.
      <yellow>You can use <aqua>/qs discount config {0}</aqua> to edit your discount code anytime.
    discount-code-under-threshold: <red>The discount not applied to your purchase because the total value under the discount code threshold <yellow>{0}</yellow>.
    percentage-off: '<bold><color:#bcef26>-{0}%</color:#bcef26></bold>'
    fixed-off: '<bold><color:#bcef26>-{0}</color:#bcef26></bold>'
    discount-code-list: '<gold>Listing your discount codes:</gold>'
    discount-code-applied-in-purchase: '<color:#bcef26>Your discount code <yellow>{0}</yellow> has been applied to this purchase and you saved <gold>{1}</gold>!'
    scope:
      this-shop: '{0}'
      your-shops-owned: all your shops (owned)
      your-shops-managed: all your shops (managed)
      server: Servidor completo
    code-type:
      SERVER_ALL_SHOPS: Todas las tiendas en este servidor
      PLAYER_ALL_SHOPS: Todas las tiendas creadas por el propietario del código
      SPECIFIC_SHOPS: Tiendas específicas
    discount-code-details: |-
      <gold>Code: <yellow>{0}</yellow>
      <gold>Creator: <yellow>{1}</yellow>
      <gold>Applied to: <yellow>{2}</yellow>
      <gold>Remaining usage: <yellow>{3}</yellow>
      <gold>Expired on: <yellow>{4}</yellow>
      <gold>Threshold: <yellow>{5}</yellow>
      <gold>Discount: <yellow>{6}</yellow>
  list:
    commands:
      list: <yellow>Listing all shops owned by your or specific player
    table-prefix: '<yellow><green>{0}</green>''s shops <gray>(Total {1})</gray>: '
    table-prefix-pageable: '<yellow><green>{0}</green>''s shops <gray>(Page {1}/{2})</gray>: '
    entry: "<hover:show_text:'<yellow>{1}</yellow>\n<gray>{2} {3},{4},{5}</gray>\n<aqua>Price <yellow>{6}</yellow> - <light_purple>x{7}</light_purple> <yellow>{8}</yellow>\n<green>{9}'><yellow>{0}. <aqua>{1}</aqua></hover>"
  shopitemonly:
    message: <red>You can't put non-shop items into shop container, all non-shop items will dropped at your location.
  limited:
    commands:
      limit: <yellow>Set a limit that restrict the purchase that player can make in period
    titles:
      title: <green>Compra con Éxito
      subtitle: <aqua>You can also purchase <gold>{0}</gold> more in this shop
    reach-the-limit: <red>You have reached the limit in this shop. You can also purchase <green>{0}</green> items, but you trying purchase <yellow>{1}</yellow> items.
    success-reset: <green>Reset the restriction of this shop successfully
    success-remove: <green>Successfully remove the all restriction of this shop
    success-setup: <green>Restriction settings for this shop were saved successfully
    trade-limit-reached-cancel-reason: <red>Reached the restriction of this shop
    remains-limits: '<gold>Your remaining purchase quota in this shop: <yellow>{0}</yellow>'
    remains-limits-before-reset-period-note: '<gold>This shop will reset their purchase quota every: <yellow>{0}</yellow>'
  dynmap:
    markerset-title: Tiendas de QuickShop-Hikari
    marker-name: '{0} @ {2} @ {3} for x{4} @ {5}'
    marker-description: |
      Name: {0}
      Owner: {1}
      Item: {2}
      Price: {3} for x{4} item(s)
      Type: {5}
      Unlimited: {6}
      Location: {7}
  bluemap:
    markerset-title: Tiendas de QuickShop-Hikari
    marker-name: '{0} @ {2} @ {3} for x{4} @ {5}'
    marker-description: |
      Name: {0}
      Owner: {1}
      Item: {2}
      Price: {3} for x{4} item(s)
      Type: {5}
      Unlimited: {6}
      Location: {7}
  chestprotect:
    protection-exists: <red>This region already protected by ChestProtect and you don't have permission to create shops at here.
    shops-exsts: <red>The region that you want to protect contains other player's shops and you don't have permission to access them.
  displaycontrol:
    toggle: |-
      <green>Successfully toggle your QuickShop display showcasing to <aqua>{0}</aqua>.
      <yellow>You may need re-join to take effect.
    toggle-exception: <red>Failed to toggle your display showcase settings due an internal error, please contact server administrator.
    command:
      displaycontrol: <yellow>Toggle your QuickShop display showcasing
  reremake-migrator:
    commands:
      migratefromreremake: Migrate QuickShop-Reremake's data to QuickShop-Hikari
    server-not-empty: "<red>No players are allowed to be online on the server during the conversion progress, please enable server whitelist or maintenance mode."
    starting-convert-progress: "<gold>Starting the convert progress, <red>DO NOT SHUTDOWN YOUR SERVER!</red>"
    executing: "<gold>Executing migrate component <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
    completed: "<green>Done! Migrate completed, please restart your server and remove QuickShop-Reremake from plugins directory."
    join_blocking_converting: "<red>[QuickShop-Hikari Migrator] This server is performing a data conversion and you cannot join the server at this time, try again later!"
    join_blocking_finished: "<red>[QuickShop-Hikari Migrator] This server has just completed a data conversion and is waiting for a reboot to apply the changes, try again later!"
    failed: "<red>Migration progress exited with error, please check the server console."
    modules:
      config:
        copy-values: "<yellow>Copying values (total {0} entries)..."
        copying-value: "<gray> - Copying <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        migrate-price-restriction: "<yellow>Migrating price-restriction related settings..."
        migrate-price-restriction-entry: "<gray> - Migrating <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
      shop:
        start-migrate: "<yellow>Migrating shops (total {0} entries)..."
        migrate-entry: "<gray> - Migrating <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        unloading-reremake: "<gold>Unloading Reremake to avoid data overwrite..."
        register-entry: "<gray> - Registering <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        save-entry: "<gray> - Saving <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        saving-shops: "<yellow>Saving <gold>{0}</gold> shops, this may need a while™ (the time based on shops amounts)..."
        conflict: "<gray> - CONFLICT > Detects a conflict between a Reremake store and an existing Hikari store location, taking a predefined behavior: <dark_gray>{0}</dark_gray>"
      translation:
        start-migrate: "<yellow>Migrating translation files...</yellow>"
        copy-values: "<yellow>Copying values in locale <gold>{0}</gold> (total {1} entries)..."
        migrate-entry: "<gray> - Migrating <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        copying-value: "<gray> - Copying <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
      shop-logs:
        start-migrate: "<yellow>Migrating shop logs (details in console), please wait...</yellow>"
        extract-history-files: "<gray>Please wait for us to unzip and append the history logs..."
        filter-history-files: "<gray>Please wait for us to filter the history log..."
        filtered-history-files: "<gray>Filtered {0} lines out of queue."
        import-entry: "<gray> - Migrating <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
compat:
  advancedchests:
    created: <green>You're successfully created a shop on AdvancedChests container!
    permission-denied: <red>Sorry! You don't have the permission to create a shop on a AdvancedChests container!
suite:
  dynmap:
    title: '{0} - {1} - {2}'
    description: |-
      Item: {0}
      Owner: {1}
      Type: {2} {3}
      Price: {4}
      Location: {5} {6}, {7}, {8}
      Space: {9}
      Stock: {10}
  limited:
    command-description: <yellow>Set a limit that restrict the purchase that player can make in period.
    reach-the-quota-limit: <red>You have reach the purchase limit in this shop ({0}/{1}).
    quota-reset-countdown: <yellow>La cuota de esta tienda se restablecerá en {0}.
    quota-reset-player-successfully: <green>The quota for player {0} in this shop has been reset successfully.
    quota-reset-everybody-successfully: <green>The quota for everyone in this shop has been reset successfully.
    quota-setup: '!'
    quota-remove: <green>The purchase restriction now removed from this shop!
    subtitles:
      title: <green>Compra con Éxito
      subtitle: <aqua>You can purchase <yellow>{0}</yellow> more in this shop
  list:
    command-description: <yellow>List all shops owned by yourself or another player.
    table-prefix: <yellow>You owned total <aqua>{0}</aqua> shops on this server.
    entry-syntax: <aqua>- <yellow>{0}
    entry-syntax-fallback-name: '<yellow>Item:{0} X:{1}, Y:{2}, Z:{3}, World: {4}'
    hover-text: |-
      <yellow>{0}
      <gray>{1} {2}, {3}, {4}
      <aqua>{5} - <yellow>{6}
      <yellow>{7} items each stack.
  shopitemonly:
    message: <red>You can't put non-shop items into shop container, all non-shop items will dropped at your location.
compatibility:
  elitemobs:
    soulbound-disallowed: You can't trade item which have EliteMobs Soulbound enchantment.
internet-paste-forbidden-privacy-reason: "<red>Failed! According your privacy setting, QuickShop-Hikari are not allowed upload your paste to Internet, turn on DIAGNOSTIC permission in privacy setting in config.yml or use <aqua>/quickshop paste --file</aqua> instead."
no-sign-type-given: "<red>You need give a sign material, the sign materials that available on this server: {0}"
sign-type-invalid: "<red>The type <yellow>{0}</yellow> not a valid sign material."
delete-controlpanel-button-confirm: "<red>¿De verdad quieres eliminar esta tienda? Haga clic en el botón <bold>[Eliminar tienda]</bold> botón de nuevo en {0} segundos para confirmar."
cannot-suggest-price: "<red>Sorry, For the time being, no more people are trading the same item as you and there is not enough data to generate a suggested price."
price-suggest: "<green>Based on data from <aqua>{0}</aqua> stores, the highest priced store is priced at <light_purple>{1}</light_purple>, the lowest priced store is priced at <light_purple>{2}</light_purple>, the average price is <light_purple>{3}</light_purple>, and the median price is <light_purple>{4}</light_purple>. <newline><yellow>It's recommended that you set your price around <gold>{5}</gold>.</yellow>"
suggest-wait: "<green>Please wait... Calculating recommend price."
history:
  shop:
    gui-title: "Ver historial de compras"
    header-icon-multiple-shop: "<white>The query result across {0} shops</white>"
    header-icon-description:
      - "<white>Tipo: <yellow>{0}</yellow></white>"
      - "<white>Dueño: <yellow>{1}</yellow></white>"
      - "<white>Item: <yellow>{2}</yellow></white>"
      - "<white>Price: <yellow>{3} <aqua>x{4}</aqua></yellow></white>"
      - "<white>Location: <yellow>{5}</yellow></white>"
    log-icon-title: "<reset><green>Time: {0}</green>"
    log-icon-description:
      - "<white>Purchaser: <yellow>{0}</yellow></white>"
      - "<white>Artículo: <yellow>{1} <aqua>x{2}</aqua></yellow></white>"
      - "<white>Saldo: <yellow>{3}</yellow></white>"
      - "<white>Impuesto: <yellow>{4}</yellow></white>"
    log-icon-description-with-store-name:
      - "<white>Tienda: <yellow>{0}</yellow></white>"
      - "<white>Comprador: <yellow>{1}</yellow></white>"
      - "<white>Artículo: <yellow>{2} <aqua>x{3}</aqua></yellow></white>"
      - "<white>Saldo: <yellow>{4}</yellow></white>"
      - "<white>Impuesto: <yellow>{5}</yellow></white>"
    query-icon: "<gray>Por favor espere, consultando...</gray>"
    previous-page: "<white><< Página Anterior</white>"
    next-page: "<white>Página siguiente >></white>"
    current-page: "<white>Página {0}</white>"
    summary-icon-title: "<green>Resumen de tienda"
    recent-purchases: "<white>Recientes <aqua>{0}</aqua> compras: <yellow>{1}</yellow></white>"
    recent-purchase-balance: "<white>Recent <aqua>{0}</aqua> turnover: <yellow>{1}</yellow></white>"
    total-purchases: "<white>Compras totales: <yellow>{0}</yellow></white>"
    total-balances: "<white>Total de ingresos: <yellow>{0}</yellow></white>"
    total-unique-purchasers: "<white>Total de compradores únicos: <yellow>{0}</yellow></white>"
    top-n-valuable-customers-title: "<gold>Top {0} clientes valiosos</gold>"
    top-n-valuable-customers-entry: "<white>- <yellow>{0}</yellow> <aqua>{1}</aqua></white>"
    no-result: "<white>Sin resultado</white>"
about:
  text:
    - "<b><aqua>QuickShop <yellow>{0}</yellow></aqua></b>"
    - "<aqua>Versión <yellow>>></yellow> <green>{1}</green></aqua>"
    - "<aqua>Lanzamiento <yellow>>></yellow> <green>{2}</green></aqua>"
    - "<aqua>Desarrolladores <yellow>>></yellow> <color:#218bff><hover:show_text:'<yellow>Haga clic para ver los > de los colaboradores<click:open_url:'https://github.com/Ghost-chu/QuickShop-Hikari/graphs/contributors'>[Ver colaboradores en GitHub]</click></hover></color></aqua>"
    - "<aqua>Miembros localizados <gold>({4})</gold>: <yellow>>></yellow> <green>{5}</green></aqua>"
    - "  <yellow>+ <color:#43A047><hover:show_text:'<yellow>Haz clic para abrir la página de traducción de Crowdin'><click:open_url:'https://crowdin.com/project/quickshop-hikari'>[Ayuda a traducir en Crowdin]</click></hover></color></yellow>"
    - "<aqua>Clave de donación <yellow>>></yellow> <green>{6}</green></aqua>"
    - "<gold>Impulsado por la comunidad</gold> <red>Hecho con ❤</red>"
  valid-donation-key: "<color:#00AFF1>Enlazado a <color:#F9C23C>{0}</color></color>"
  invalid-donation-key: "<gray>Clave de donación inválida</gra>"
  kofi-thanks: "<gold>Un agradecimiento especial a aquellos que apoyan a QuickShop en Ko-fi :)</gold>"
history-command-leave-blank: "<Dejar en blanco para ver la tienda>"
shop-information-not-shown-due-an-internal-error: "<red>Se ha producido un error interno. Es posible que el panel de información de la tienda se muestre de forma incompleta, póngase en contacto con el administrador del servidor."
