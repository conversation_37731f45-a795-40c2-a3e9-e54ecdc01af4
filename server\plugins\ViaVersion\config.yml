# Thanks for downloading ViaVersion
# Ensure you look through all these options
# If you need help:
# Discord - https://viaversion.com/discord
# Docs - https://docs.viaversion.com
#
#----------------------------------------------------------#
#                     GLOBAL OPTIONS                       #
#----------------------------------------------------------#
#
# Should ViaVersion check for updates?
check-for-updates: true
# Send the supported versions with the Status (Ping) response packet
send-supported-versions: false
# Easier to configure alternative to 'block-protocols'. Uses readable version strings with possible '<' and '>' prefixes.
# An example to block 1.16.4, everything below 1.16, as well as everything above 1.17.1 would be: ["<1.16", "1.16.4", ">1.17.1"]
# You can use both this and the block-protocols option at the same time as well.
block-versions: []
# Block specific Minecraft protocol version numbers.
# List of all Minecraft protocol versions: https://minecraft.wiki/w/Protocol_version, or use a generator: https://via.krusic22.com
block-protocols: []
# Change the blocked disconnect message
block-disconnect-msg: You are using an unsupported Minecraft version!
# If you use ProtocolLib, we can't reload without kicking the players.
# (We don't suggest using reload either, use a plugin manager)
# You can customize the message we kick people with if you use ProtocolLib here.
reload-disconnect-msg: Server reload, please rejoin!
# We warn when there's an error converting item/block or component/nbt data over versions, should we suppress these? (Only suggested if spamming)
suppress-conversion-warnings: false
# This can be disabled for debugging purposes if text in chat/entities/items shows an error tag.
suppress-text-component-conversion-warnings: true
#
#----------------------------------------------------------#
#                  GLOBAL PACKET LIMITER                   #
#----------------------------------------------------------#
# THIS FEATURE IS DISABLED ON 1.17.1+ PAPER SERVERS, SINCE IT HAS A BETTER PACKET-LIMITER INBUILT
#
# Packets Per Second (PPS) limiter (Use -1 on max-pps and tracking-period to disable)
# Clients by default send around 20-90 packets per second.
#
# What is the maximum per second a client can send (Use %pps to display their pps)
# Use -1 to disable.
max-pps: 800
max-pps-kick-msg: You are sending too many packets!
#
# We can also kick them if over a period they send over a threshold a certain amount of times.
#
# Period to track (in seconds)
# Use -1 to disable.
tracking-period: 6
# How many packets per second count as a warning?
tracking-warning-pps: 120
# How many warnings over the interval can we have
# This can never be higher than "tracking-period"?
tracking-max-warnings: 4
# The kick message sent if the user hits the max packets per second.
tracking-max-kick-msg: You are sending too many packets, :(
#
#----------------------------------------------------------#
#                 MULTIPLE VERSIONS OPTIONS                #
#----------------------------------------------------------#
#
# Whether to make sure ViaVersion's UserConnection object is already available in the PlayerJoinEvent.
# You may disable this for faster startup/join time if you are 100% sure no plugin requires this.
register-userconnections-on-join: true
# Should we enable our hologram patch?
# If they're in the wrong place, enable this
hologram-patch: false
# This is the offset, should work as default when enabled.
hologram-y: -0.96
# Should we disable piston animation for 1.11/1.11.1 clients?
# In some cases, when firing lots of pistons, it crashes them.
piston-animation-patch: false
# Experimental - Should we fix shift quick move action for 1.12 clients (causes shift + double click not to work when moving items) (only works on 1.8-1.11.2 bukkit based servers)
quick-move-action-fix: false
# Should we use prefix for team color on 1.13 and above clients?
team-colour-fix: true
# 1.13 introduced new auto complete which can trigger "Kicked for spamming" for servers older than 1.13, the following option will disable it completely.
disable-1_13-auto-complete: false
# The following option will delay the tab complete request in x ticks if greater than 0, if other tab-complete is received, the previous is cancelled
1_13-tab-complete-delay: 0
# For 1.13 clients the smallest (1 layer) snow doesn't have collisions, this will send these as 2 snowlayers for 1.13+ clients to prevent them bugging through them
fix-low-snow-collision: false
# Infested blocks are instantly breakable for 1.13+ clients, resulting in them being unable to break them on sub 1.13 servers. This remaps them to their normal stone variants
fix-infested-block-breaking: true
# In 1.14 the client page limit has been upped to 100 (from 50). Some anti-exploit plugins ban when clients go higher than 50. This option cuts edited books to 50 pages.
truncate-1_14-books: false
# This prevents clients using 1.9-1.13 on 1.8 servers from receiving no knockback/having velocity bugs whilst sneaking under a block.
change-1_9-hitbox: false
# Similar to the above, but for 1.14+ players on 1.8-1.13 servers.
# WARNING: This gives 1.14+ players the ability to sneak under blocks, that players under that version cannot (sneaking in places that are only 1.5 blocks high)!
# Another thing to remember is that those players might be missed by projectiles and other hits directed at the very top of their head whilst sneaking.
change-1_14-hitbox: false
# Fixes 1.14+ clients on sub 1.14 servers having a light value of 0 for non-full blocks.
fix-non-full-blocklight: true
# Fixes walk animation not shown when health is set to Float.NaN
fix-1_14-health-nan: true
# Should 1.15+ clients respawn instantly / without showing a death screen?
use-1_15-instant-respawn: false
#
# Enable serverside block-connections for 1.13+ clients - all the options in this section are built around this option
serverside-blockconnections: true
# Sets the method for the block connections (world for highly experimental (USE AT OWN RISK) world-level or packet for packet-level)
blockconnection-method: packet
# When activated, only the most important blocks are stored in the blockstorage. (fences, glass panes etc. won't connect to solid blocks)
reduce-blockstorage-memory: false
# When activated with serverside-blockconnections, flower parts with blocks above will be sent as stems
# Useful for lobbyservers where users can't build and those stems are used decoratively
flowerstem-when-block-above: false
# Vines that are not connected to blocks will be mapped to air, else 1.13+ would still be able to climb up on them.
vine-climb-fix: false
#
# Ignores incoming plugin channel messages of 1.16+ clients with channel names longer than 32 characters.
# CraftBukkit had this limit hardcoded until 1.16, so we have to assume any server/proxy might have this arbitrary check present.
ignore-long-1_16-channel-names: true
#
# Force 1.17+ client to accept the server resource pack; they will automatically disconnect if they decline.
forced-use-1_17-resource-pack: false
# The message to be displayed at the prompt when the 1.17+ client receives the server resource pack.
resource-pack-1_17-prompt: ''
#
# Caches light until chunks are unloaded to allow later chunk update packets as opposed to instantly uncaching when the first chunk data is sent.
# Only disable this if you know what you are doing.
cache-1_17-light: true
#
# Force-update 1.19.4+ player's inventory when they try to swap armor in a pre-occupied slot.
armor-toggle-fix: true
#
# Get the world names which should be returned for each vanilla dimension
map-1_16-world-names:
  overworld: minecraft:overworld
  nether: minecraft:the_nether
  end: minecraft:the_end
#
# If disabled, tamed cats will be displayed as ocelots to 1.14+ clients on 1.13 servers. Otherwise, ocelots (tamed and untamed) will be displayed as cats.
translate-ocelot-to-cat: false
#
# Determines the value sent to 1.19+ clients on join if currently not accessible by ViaVersion.
# It is not recommended to fake this value if your server is running 1.19 or later, as 1.20.5 have stricter chat handling and may get kicked otherwise.
enforce-secure-chat: false
#
# Handles items with invalid count values (higher than max stack size) on 1.20.3 servers.
handle-invalid-item-count: false
#
# Hides scoreboard numbers for 1.20.3+ clients on older server versions.
hide-scoreboard-numbers: false
#
# Fixes 1.21+ clients on 1.20.5 servers placing water/lava buckets at the wrong location when moving fast, NOTE: This may cause issues with anti-cheat plugins.
fix-1_21-placement-rotation: true
#
# If enabled, cancel swing packets sent while having an inventory opened on 1.15.2 and below servers. This can cause false positives with anti-cheat plugins.
cancel-swing-in-inventory: true
#
#----------------------------------------------------------#
#             1.9+ CLIENTS ON 1.8 SERVERS OPTIONS          #
#----------------------------------------------------------#
#
# No collide options, these allow you to configure how collision works.
# Do you want us to prevent collision?
prevent-collision: true
# If the above is true, should we automatically team players until you do?
auto-team: true
# When enabled if certain entity data can't be read, we won't tell you about it
suppress-metadata-errors: false
# When enabled, 1.9+ will be able to block by using shields
shield-blocking: true
# If this setting is active, the main hand is used instead of the off-hand to trigger the blocking of the player.
# With the main hand, the blocking starts way faster.
# (Requires "show-shield-when-sword-in-hand" to be disabled)
no-delay-shield-blocking: false
# If this setting is active, the shield will appear immediately for 1.9+ when you hold a sword in your main hand.
# The shield disappears when you switch to another item.
# (Requires "shield-blocking" to be enabled)
show-shield-when-sword-in-hand: false
# Enable player tick simulation, this fixes eating, drinking, nether portals.
simulate-pt: true
# Should we use nms player to simulate packets, (may fix anti-cheat issues)
nms-player-ticking: true
# Should we patch boss bars so they work? (Default: true, disable if you're having issues)
bossbar-patch: true
# If your boss bar flickers on 1.9+, set this to 'true'. It will keep all boss bars on 100% (not recommended)
bossbar-anti-flicker: false
# This will show the new effect indicator in the top-right corner for 1.9+ players.
use-new-effect-indicator: true
# Show the new death messages for 1.9+ on the death screen
use-new-deathmessages: true
# Should we cache our items, this will prevent the server from being lagged out, however, the cost is a constant task caching items
item-cache: true
# Should we replace extended pistons to fix 1.10.1 (Only on chunk loading)?
replace-pistons: false
# What id should we replace with, default is air. (careful of players getting stuck standing on them)
replacement-piston-id: 0
# Fix 1.9+ clients not rendering the far away chunks and improve chunk rendering when moving fast (Increases network usage and decreases client fps slightly)
chunk-border-fix: false
# Allows 1.9+ left-handedness (main hand) on 1.8 servers
left-handed-handling: true
# Tries to cancel block break/place sounds sent by 1.8 servers to 1.9+ clients to prevent them from playing twice
cancel-block-sounds: true
