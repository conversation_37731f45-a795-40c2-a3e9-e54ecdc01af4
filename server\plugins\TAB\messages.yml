announce-command-usage: "Usage: /tab announce <type> <name> <length>\nCurrently supported types: &lbar, scoreboard"
bossbar-feature-not-enabled: "&cThis command requires the bossbar feature to be enabled."
bossbar-announce-command-usage: "Usage: /tab announce bar <bar name> <length>"
bossbar-not-found: "&cNo bossbar found with the name \"%name%\""
bossbar-already-announced: "&cThis bossbar is already being announced"
group-data-removed: "&3[TAB] All data has been successfully removed from group &e%group%"
group-value-assigned: "&3[TAB] %property% '&r%value%&r&3' has been successfully assigned to group &e%group%"
group-value-removed: "&3[TAB] %property% has been successfully removed from group &e%group%"
user-data-removed: "&3[TAB] All data has been successfully removed from player &e%player%"
user-value-assigned: "&3[TAB] %property% '&r%value%&r&3' has been successfully assigned to player &e%player%"
user-value-removed: "&3[TAB] %property% has been successfully removed from player &e%player%"
parse-command-usage: "Usage: /tab parse <player> <placeholder>"
send-command-usage: "Usage: /tab send <type> <player> <bar name> <length>\nCurrently supported types: &lbar"
send-bar-command-usage: "Usage: /tab send bar <player> <bar name> <length>"
team-feature-required: "This command requires scoreboard teams feature enabled"
collision-command-usage: "Usage: /tab setcollision <player> <true/false>"
no-permission: "&cI'm sorry, but you do not have permission to perform this command. Please contact the server administrators if you believe that this is in error."
command-only-from-game: "&cThis command must be ran from the game"
player-not-online: "&cNo online player found with the name \"%player%\""
invalid-number: "\"%input%\" is not a number!"
scoreboard-feature-not-enabled: "&4This command requires the scoreboard feature to be enabled."
scoreboard-announce-command-usage: "Usage: /tab scoreboard announce <scoreboard name> <length>"
scoreboard-not-found: "&cNo scoreboard found with the name \"%name%\""
reload-success: "&3[TAB] Successfully reloaded"
reload-fail-file: "&3[TAB] &4Failed to reload, file %file% has broken syntax. Check console for more info."
scoreboard-toggle-on: "&2Scoreboard enabled"
scoreboard-toggle-off: "&7Scoreboard disabled"
bossbar-toggle-on: "&2Bossbar is now visible"
bossbar-toggle-off: "&7Bossbar is no longer visible. Magic!"
scoreboard-show-usage: "Usage: /tab scoreboard show <scoreboard> [player]"
bossbar-not-marked-as-announcement: "&cThis bossbar is not marked as an announcement bar and is therefore already displayed permanently (if display condition is met)"
bossbar-announcement-success: "&aAnnouncing bossbar &6%bossbar% &afor %length% seconds."
bossbar-send-success: "&aSending bossbar &6%bossbar% &ato player &6%player% &afor %length% seconds."
help-menu:
  - "&m                                                                                "
  - " &8>> &3&l/tab reload"
  - "      - &7Reloads plugin and config"
  - " &8>> &3&l/tab &9group&3/&9player &3<name> &9<property> &3<value...>"
  - "      - &7Do &8/tab group/player &7to show properties"
  - " &8>> &3&l/tab parse <player> <placeholder> "
  - "      - &7Test if a placeholder works"
  - " &8>> &3&l/tab debug [player]"
  - "      - &7displays debug information about player"
  - " &8>> &3&l/tab cpu"
  - "      - &7shows CPU usage of the plugin"
  - " &8>> &3&l/tab group/player <name> remove"
  - "      - &7Clears all data about player/group"
  - "&m                                                                                "
mysql-help-menu:
  - "&6/tab mysql upload - uploads data from files to mysql"
  - "&6/tab mysql download - downloads data from mysql to files"
mysql-fail-not-enabled: "&cCannot download/upload data from/to MySQL, because it's disabled."
mysql-fail-error: "MySQL download failed due to an error. Check console for more info."
mysql-download-success: "&aMySQL data downloaded successfully."
mysql-upload-success: "&aMySQL data uploaded successfully."
nametag-help-menu:
  - "/tab nametag toggle [player] - toggles nametags on all players for command sender"
nametag-feature-not-enabled: "&cThis command requires nametag feature to be enabled."
nametags-hidden: "&aNametags of all players were hidden to you"
nametags-shown: "&aNametags of all players were shown to you"
scoreboard-help-menu:
  - "/tab scoreboard [on/off/toggle] [player] [options]"
  - "/tab scoreboard show <name> [player]"
  - "/tab scoreboard announce <name> <length>"
bossbar-help-menu:
  - "/tab bossbar [on/off/toggle] [player] [options]"
  - "/tab bossbar send <name> [player]"
  - "/tab bossbar announce <name> <length>"