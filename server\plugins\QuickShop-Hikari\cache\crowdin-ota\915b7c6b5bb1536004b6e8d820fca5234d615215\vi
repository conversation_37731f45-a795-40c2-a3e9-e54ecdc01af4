break-shop-use-supertool: <yellow>Bạn có thể phá vỡ cửa hàng bằng cách sử dụng SuperTool.
fee-charged-for-price-change: <green>You đã trả <red>{0}<green> để được thay đổi giá.
not-allowed-to-create: <red>Bạn không thể tạo cửa hàng ở đây.
disabled-in-this-world: <red>QuickShop bị vô hiệu hóa trong thế giới này
how-much-to-trade-for: <green>Nhập vào khung chat số tiền bạn muốn giao dịch <yellow>{1}x {0}<green>.
client-language-changed: <green>QuickShop detected your client language setting has been changed, we're now using {0} locale for you.
shops-backingup: Tạo bản sao lưu shop từ cơ sở dữ liệu...
_comment: Chào người biên dịch! Nếu bạn đang chỉnh sửa code trên GitHub hoặc thông qua mã nguồn, bạn nên truy cập https://crowdin.com/project/quickshop-hikari.
unlimited-shop-owner-changed: <yellow>This unlimited shop owner has been changed to {0}.
bad-command-usage-detailed: '<red>Lệnh không hợp lệ! Chấp nhận các lệnh sau:<gray>{0}'
thats-not-a-number: <red>Số không hợp lệ
shop-name-disallowed: <red>The shop name <yellow>{0} <red>is disallowed. Pick another one!
console-only-danger: <red>Đây là một lệnh nguy hiểm nên chỉ có Console mới có thể thực thi nó.
not-a-number: <red>Bạn chỉ có thể nhập vào số, bạn vừa nhập vào {0}.
not-looking-at-valid-shop-block: <red>Không thể tìm ra block để tạo Shop. Bạn cần phải nhìn vào block trước.
shop-removed-cause-ongoing-fee: <red>Cửa hàng của bạn tại {0} đã bị xóa vì bạn không có đủ tiền để giữ cửa hàng đó!
tabcomplete:
  amount: '[amount]'
  item: '[item]'
  price: '[price]'
  name: '[name]'
  range: '[range]'
  currency: '[currency name]'
  percentage: '[percentage%]'
taxaccount-unset: <green>Tài khoản thuế của cửa hàng này hiện đang thiết lập theo máy chủ.
blacklisted-item: <red>Bạn không thể bán vật phẩm này vì nó nằm trong danh sách đen
command-type-mismatch: <red>This command only can executed by <aqua>{0}.
server-crash-warning: '<red>Server may crash after execute /qs reload command if you replace/delete QuickShop plugin Jar file while server running.'
you-cant-afford-to-change-price: <red>Tiêu tốn {0} để thay đổi giá của shop.
safe-mode: <red>QuickShop hiện đang ở chế độ an toàn, bạn không thể mở cửa hàng này, vui lòng liên hệ với quản trị viên máy chủ để sửa lỗi.
forbidden-vanilla-behavior: <red>Hoạt động bị cấm do nó không phù hợp với hành vi vanilla
shop-out-of-space-name: <dark_purple>Cửa hàng {0} của bạn đã đầy!
paste-disabled: |-
  <red>Chức năng dán đã bị vô hiệu hóa! Bạn không thể yêu cầu hỗ trợ kỹ thuật.<newline>Lý do: {0}
quick-fill:
  entry-own: ' <yellow>- <red>[own] <aqua>{0} <light_purple>{1}'
  hover:
    - '<yellow>Tên: <aqua>{0}'
    - '<yellow>Chủ: <aqua>{0}'
    - '<yellow>Loại: <aqua>{0}'
    - '<yellow>Giá: <aqua>{0}'
    - '<yellow>Vật phẩm: <aqua>{0}'
    - '<yellow>Vị trí: <aqua>{0}'
  hover-arg-filled:
    - '<yellow>Tên: <aqua>{name}'
    - '<yellow>Chủ: <aqua>{owner}'
    - '<yellow>Loại: <aqua>{type}'
    - '<yellow>Giá: <aqua>{price}'
    - '<yellow>Vật phẩm: <aqua>{item}'
    - '<yellow>Vị trí: <aqua>{location}'
  header: '<yellow>You have multiple shops with name "<green>{0}<yellow>", choose one to continue:'
  entry-normal: ' <yellow>- <aqua>{0}<light_purple>{1}'
  entry-cooperation: ' <yellow>- <red>[co] <aqua>{0} <light_purple>{1}'
3rd-plugin-build-check-failed-admin: '<gray>(Chỉ admin) <light_purple>{0} <dark_gray>đã từ chối kiểm tra quyền, Nếu điều này không hoạt động, hãy thử thêm <light_purple>{1} <gray>vào danh sách đen. Hướng dẫn điều chỉnh: https://ghost-chu.github.io/QuickShop-Hikari-Documents/docs/modules/shops/protection-checker'
average-price-nearby: '<green>Giá trung bình lân cận: <yellow>{0}'
inventory-check-global-alert: "<red>[Kiểm tra túi đồ] <gray>Cảnh báo! Đã tìm thấy bảng hiển thị thông tin QuickShop <gold>{2}</gold> trong túi đồ <aqua>{0}</aqua> <dark_gray>[{1}]</dark_gray>, điều này không nên xảy ra vì có ai đó đang cố gắng sửa dụng lỗ hổng để sao chép bảng hiển thị thông tin vật phẩm."
digits-reach-the-limit: <red>Bạn đã đạt đến giới hạn của số thập phân trong giá.
currency-unset: <green>Đã xóa thành công đơn vị tiền tệ của cửa hàng. Sử dụng mặt định.
you-cant-create-shop-in-there: <red>Bạn không có quyền tạo cửa hàng tại ví trị này.
no-pending-action: <red>You do not have any pending actions
refill-success: <green>Nạp tiền thành công
failed-to-paste: <red>Không thể tải dữ liệu lên Pastebin. Kiểm tra kết nối internet của bạn và thử lại. (Xem bảng điều khiển để biết chi tiết)
shop-out-of-stock-name: <dark_purple>Cửa hàng của bạn {0} đã hết {1}!
shop-name-invalid: <red>The shop name <yellow>{0} <red>is invalid. Pick another one!
how-many-buy-stack: <yellow>Nhập vào khung chat số  lượng bạn muốn<light_purple> MUA<yellow>. Có<green> {0} <yellow>vật phẩm trong mỗi số lượng lớn và bạn có thể mua <green>{1}<yellow> hàng loạt. Nhập <green>{2} <yellow>vào trò chuyện để mua tất cả.
exceeded-maximum: <red>Giá trị vượt quá giá trị tối đa trong Java.
unlimited-shop-owner-keeped: '<yellow>Attention: The shop owner still is unlimited shop owner, you need re-set new shop owner by yourself.'
no-enough-money-to-keep-shops: <red>You didn't have enough money to keep your shops! All shops have been removed...
3rd-plugin-build-check-failed: <red>Plugin bên thứ 3 <bold>{0}<reset><red> đã từ chối quyền kiểm tra, bạn đã có quyền thiết lập ở đó chưa?
not-a-integer: <red>You must input a integer, your input was {0}.
translation-country: 'Vùng ngôn ngữ: Tiếng Việt (vi_VN)'
buying-more-than-selling: '<red>WARNING: You are buying items for more than you are selling them!'
purchase-failed: '<red>Mua không thành công: Lỗi nội bộ. Vui lòng liên hệ với Quản trị viên máy chủ.'
denied-put-in-item: <red>Bạn không thể đặt vật phẩm này vào cửa hàng của bạn!
shop-has-changed: <red>Shop mà bạn cố gắng sử dụng đã thay đổi kể từ khi bạn nhấp vào nó!
flush-finished: <green>Successfully flushed the messages.
no-price-given: <red>Vui lòng nhập một giá hợp lệ.
shop-already-owned: <red>Đây đã được coi là một shop rồi.
backup-success: <green>Sao lưu thành công.
not-looking-at-shop: <red>Không thể tìm ra QuickShop. Bạn cần phải nhìn vào nó trước.
you-cant-afford-a-new-shop: <red>Tiêu tốn {0} để tạo ra 1 shop mới.
success-created-shop: <red>Shop đã được tạo.
shop-creation-cancelled: <red>Khởi tạo shop đã bị huỷ.
shop-owner-self-trade: <yellow>You trading with yourself shop, so you might won't gain any balance.
purchase-out-of-space: <red>This shop run out of the space, Contact shop owner or staffs to empty the shop.
reloading-status:
  success: <green>Tải lại hoàn tất và không có bất kỳ lỗi nào.
  scheduled: <green>Reload completed. <gray>(Some changes required a while to affect)
  require-restart: <green>Reload completed. <yellow>(Some changes require server restart to affect)
  failed: <red>Tải lại không thành công, hãy kiểm tra bảng điều khiển máy chủ
player-bought-from-your-store-tax: <green>{0} đã mua {1} {2} từ cửa hàng của bạn và bạn nhận được {3} ({4} tiền thuế).
not-enough-space: <red>You only have room for {0} more!
shop-name-success: <green>Đặt tên cửa hàng thành công thành <yellow>{0}<green>.
shop-staff-added: <green>Successfully added {0} as a staff member for your shop.
shop-staff-empty: <yellow>Cửa hàng này không có chủ.
shops-recovering: Đang khôi phục cửa hàng từ bản sao lưu...
virtual-player-component-hover: "<gray>Đây là một người chơi ảo.\n<gray>Vì tên tài khoản này trùng tên với tài khoản khác.</gray>\n<green>UUID: <yellow>{0}</yellow></green>\n<green>Tên đăng nhập: <yellow>{1}</yellow></green>\n<green>Hiển thị dưới dạng: <yellow>{2}</yellow></green>"
real-player-component-hover: "<gray>Đây là người chơi thật.\n<green>UUID: <yellow>{0}</yellow></green>\n<green>Tên đăng nhập: <yellow>{1}</yellow></green>\n<green>Hiển thị dưới dạng: <yellow>{2}</yellow></green>\n<gray>Nếu bạn muốn sử dụng tài khoản ảo có cùng tên, hãy thêm <dark_gray>\"[]\"</dark_gray> Ở cả hai phía của tên người dùng: <dark_gray>[{1}]</dark_gray>."
menu:
  sell-tax: <green>Bạn đã trả <yellow>{0} <green>tiền thuế.
  owner: '<green>Chủ: {0}'
  preview: <aqua>[Xem trước item]
  enchants: <dark_purple>Phù phép
  sell-tax-self: <green>You didn't pay taxes because you own this shop.
  shop-information: '<green>Thông tin shop:'
  item: '<green>Vật phẩm: <yellow>{0}'
  price-per: <green>Giá mỗi <yellow>{0} <green>- <yellow>{1}
  item-name-and-price: <yellow>{0} {1} <green>cho <yellow>{2}
  item-name-and-price-tax: <yellow>{0} {1} <green>cho</green> {2} <gray>(<green>{3}</green> tiền thuế)</gray>
  successful-purchase: '<green>Mua thành công:'
  price-per-stack: <green>Giá mỗi <yellow>{2}x {0} - {1}
  stored-enchants: <dark_purple>Phù phép được lưu trữ
  item-holochat-error: <red>[Lỗi]
  this-shop-is-selling: <green>Cửa hàng này đang <aqua>BÁN <green>các vật phẩm.
  shop-stack: '<green>Số lượng: <yellow>{0}'
  space: '<green>Trống: <yellow>{0}'
  effects: <green>Hiệu ứng
  damage-percent-remaining: <yellow>{0}% <green>còn lại.
  item-holochat-data-too-large: <red>[Error] Item NBT is too large for showing
  stock: '<green>Stock <yellow>{0}'
  this-shop-is-buying: <green>Cửa hàng này đang <light_purple>MUA <green>các vật phẩm.
  successfully-sold: '<green>Bán thành công:'
  total-value-of-chest: '<green>Tổng giá trị của rương: <yellow>{0}'
currency-not-exists: <red>Không thể tìm thấy đơn vị tiền tệ mà bạn muốn đặt. Có thể lỗi chính tả hoặc đơn vị tiền tệ đó không có sẵn trên thế giới này.
no-nearby-shop: <red>Không có shop {0} nào gần đây.
translation-author: 'Ghost_chu, Andre_601 (dịch bởi maiminhdung)'
integrations-check-failed-trade: <red>Tích hợp {0} đã từ chối Shop trao đổi
shop-transaction-failed: <red>Sorry, but an internal error occurred while processing your purchase. The purchase has been cancelled and any operation has been rollback. Please contact the server administrators if it keeping occurred.
success-change-owner-to-server: <green>Đã đặt thành công chủ cửa hàng thành Máy chủ.
shop-name-not-found: <red>The shop named <yellow>{0} <red>not exists.
shop-name-too-long: <red>Tên cửa hàng quá dài (chiều dài tối đa {0}), vui lòng chọn tên khác!
metric:
  header-player: '<yellow>{0} giao dịch {1} {2}:'
  action-hover: <yellow>{0}
  price-hover: <yellow>Tổng {0}, bao gồm {1} thuế.
  unknown: <gray>(không xác định)
  undefined: <gray>(không có tên)
  no-results: <red>Không tìm thấy giao dịch.
  action-description:
    DELETE: <yellow>Người chơi đã xóa cửa hàng. Và hoàn lại phí tạo cửa hàng cho chủ nếu có thể.
    ONGOING_FEE: <yellow>Người chơi đã trả tiền liên tục vì thời gian thanh toán.
    PURCHASE_BUYING_SHOP: <yellow>Người chơi đã bán một số vật phẩm cho một cửa hàng mua lại.
    CREATE: <yellow>Người chơi đã tạo một cửa hàng.
    PURCHASE_SELLING_SHOP: <yellow>Người chơi đã mua một số vật phẩm từ một cửa hàng
    PURCHASE: <yellow>Mua vật phẩm từ cửa hàng
  query-argument: 'Đối số Truy vấn: {0}'
  amount-hover: <yellow>{0}
  header-shop: '<yellow>{0} giao dịch {1} {2}:'
  player-hover: |-
    <yellow>{0}<newline><gold>UUID: <gray>{1}
  looking-up: <yellow>Thực hiện tra cứu số liệu, Vui lòng đợi...
  tax-hover: thuế <yellow>{0}
  header-global: '<yellow>Máy chủ {0} {1} giao dịch:'
  na: <gray>N/A
  transaction-count: <yellow>{0} tất cả
  shop-hover: |-
    <yellow>{0}<newline><gold>Ví trí: <gray>{1}{2}{3}, Thế giới: {4}<newline><gold>Chủ: <gray>{5}<newline><gold>Loại cửa hàng: <gray>{6}<newline><gold>Vật phẩm: <gray>{7}<newline><gold>Giá: <gray>{8}
  time-hover: '<yellow>Thời gian: {0}'
  amount-stack-hover: <yellow>{0}x stack
permission-denied-3rd-party: '<red>Permission denied: 3rd Party Plugin [{0}].'
you-dont-have-that-many-items: <red>Bạn chỉ còn {0} {1}.
complete: <green>Hoàn thành!
translate-not-completed-yet-url: 'Bản dịch của ngôn ngữ {0} chưa hoàn thành bởi {1}. Nếu bạn muốn giúp chúng tôi cải thiện bạn dịch này không? Bấm vào: {2}'
success-removed-shop: <green>Shop đã bị xoá.
currency-set: <green>Đơn vị tiền tệ được đặt thành công thành {0}
shop-purged-start: <green>Thanh lọc cửa hàng bắt đầu, kiểm tra bảng điều khiển để biết chi tiết.
economy-transaction-failed: <red>Sorry, but an internal error occurred while processing your transaction. The transaction has been cancelled and any economy operation has been rollback. Please contact the server administrators if it keeping occurred.
nothing-to-flush: <green>Bạn không có tin nhắn từ của hàng.
no-price-change: <red>Điều này sẽ không được thay đổi giá!
edition-confilct: QuicShop-Hikari với QuickShop-Reremake được cài đặt có thể xung đột lẫn nhau, hãy gỡ cài đặt một trong số chúng.
inventory-unavailable: |-
  <red>This shop InventoryWrapper not exists or invalid. Did you using addon to re-binding the shop Inventory? Information: InventoryWrapper={0}, WrapperProvider={1}, SymbolLink={2}. Please contact the server administrators.
file-test: Đây là một file test thử. Chúng tôi dùng nó để kiểm tra xem liệu file messages.json có bị lỗi hay không. Bạn có thể tuỳ ý điền bất cứ gì bạn thích vào đây :)
unknown-player: <red>Target player doesn't exist, please check the username you typed.
player-offline: <red>The target player currently is offline.
player-profile-format: <aqua>{0}</aqua><gold>(<yellow>{1}</yellow>)</gold>
shop-type:
  selling: ĐANG BÁN
  buying: ĐANG MUA
language:
  qa-issues: '<yellow>Vấn đề đảm bảo chất lượng: <aqua>{0}%'
  code: '<yellow>Mã: <gold>{0}'
  approval-progress: '<yellow>Tiến độ phê duyệt: <aqua>{0}%'
  translate-progress: '<yellow>Tiến độ dịch: <aqua>{0}%'
  name: '<yellow>Tên: <gold>{0}'
  help-us: <green>[Giúp chúng tôi cải thiện chất lượng bản dịch]
warn-to-paste: |-
  <yellow>Collecting data and uploading it to Pastebin, this may take a while. <red><bold>Warning:<red> The data is kept public for one week! It may leak your server configuration and other sensitive information. Make sure you only send it to <bold>trusted staff/developers.
how-many-sell-stack: <yellow>Nhập vào khung chat số lượng bạn muốn<light_purple> BÁN<yellow>. Có<green> {0} <yellow>vật phẩm trong mỗi số lượng lớn và bạn có thể bán <green>{1}<yellow> hàng loạt. Nhập <green>{2} <yellow>vào trò chuyện để bán tất cả.
updatenotify:
  buttontitle: '[Cập nhật bây giờ]'
  onekeybuttontitle: '[Cập nhật OneKey]'
  label:
    github: '[GitHub]'
    ore: '[Ore]'
    lts: '[LTS]'
    nbtapi: '[+NBTAPI]'
    qualityverifyed: '[Chất lượng]'
    master: '[Master]'
    unstable: '[Không ổn định]'
    paper: '[+Paper Optimized]'
    stable: '[Ổn định]'
    spigotmc: '[SpigotMC]'
    papermc: '[PaperMC]'
    bukkitdev: '[BukkitDev]'
    polymart: '[Polymart]'
    modrinth: '[Modrinth]'
    basic: '[Basic]'
  list:
    - '{0} đã được phát hành. Bạn vẫn đang sử dụng {1}!'
    - Đùng! Cập nhật mới {0} đã đến. Cập nhật ngay!
    - Bất ngờ chưa! {0} vừa mới ra mắt. Bạn vẫn đang sử dụng {1}
    - Có vẻ như bạn cần cập nhật. {0} đã được phát hành!
    - Ooops! {0} vửa mới ra mắt. Bạn vẫn đang sử dụng {1}!
    - Tôi thề, QS đã được cập nhật lên bản {0}. Tại sao bạn chưa cập nhật?
    - Sửa lỗi và lại... Xin lỗi, nhưng {0} đã được phát hành!
    - Err! Nope. Đây không phải là lỗi. {0} đã được phát hành!
    - CHÚA ƠI! {0} đã ra mắt! Tại sao bạn vẫn đang sử dụng {1}?
    - 'Tin tức hôm nay: QuickShop đã được cập nhật lên {0}!'
    - Plugin k.i.a. Bạn nên cập nhật lên {0}!
    - Cập nhật {0} được kích hoạt. Lưu cập nhật!
    - Có một bản cập nhật mới có sẵn. {0} vừa mới ra mắt!
    - Nhìn phong cách của tôi---{0} cập nhật. Bạn vẫn đang sử dụng {1}
    - Ahhhhhhhh! Cập nhật mới {0}! Cập nhật!
    - Bạn nghĩ gì? {0} đã được phát hành! Cập nhật ngay!
    - Bác sĩ, QuickShop có bản cập nhật mới {0}! Bạn nên cập nhật ~
    - Ko~ko~da~yo~ QuickShop có bản cập nhật mới {0} ~
    - Paimon muốn cho bạn biết QuickShop có bản cập nhật mới {0}!
  remote-disable-warning: '<red>Phiên bản QuickShop này bị máy chủ vô hiệu hóa, có nghĩa là phiên bản này có thể có vấn đề nghiêm trọng, hãy lấy thông tin chi tiết từ trang SpigotMC của chúng tôi: {0}. Cảnh báo này sẽ tiếp tục xuất hiện cho đến khi bạn chuyển sang phiên bản ổn định, nhưng nó sẽ không ảnh hưởng đến hiệu suất máy chủ của bạn.'
purchase-out-of-stock: <red>This shop run out of the stock, Contact shop owner or staffs to refill the stock.
nearby-shop-entry: '<green>- Info:{0} <green>Price:<aqua>{1} <green>x:<aqua>{2} <green>y:<aqua>{3} <green>z:<aqua>{4} <green>distance: <aqua>{5} <green>block(s)'
chest-title: Cửa hàng QuickShop
console-only: <dark_red>Lệnh chỉ có thể dùng bởi người chơi
failed-to-put-sign: <red>Không đủ không gian xung quanh cửa hàng để đặt biển.
shop-name-unset: <red>Tên cửa hàng đã bị xóa
shop-nolonger-freezed: <green>Bạn mở cửa hàng. Nó trở lại bình thường bây giờ!
no-permission-build: <red>Bạn không thể đặt cửa hàng ở đây.
tableformat:
  left_half_line: <dark_purple>+--------------------
  right_half_line: <dark_purple>--------------------+
  full_line: <dark_purple>+---------------------------------------------------+
  left_begin: '<dark_purple>| '
quickshop-gui-preview: Xem trước vật phẩm QuickShop
translate-not-completed-yet-click: Bản dịch ngôn ngữ {0} chưa được hoàn thành bởi {1}. Bạn có muốn giúp chúng tôi cải thiện bản dịch không? Bấm vào đây!
taxaccount-invalid: <red>Tài khoản không hợp lệ, vui lòng nhập tên người chơi hợp lệ hoặc uuid (có dấu gạch ngang).
player-bought-from-your-store: <red>{0} đã mua {1} {2} từ shop của bạn, bạn nhận được {3}.
reached-maximum-can-create: <red>Bạn đã tạo tối đa {0}/{1} cửa hàng!
reached-maximum-create-limit: <red>Bạn đã đạt đến số lượng tối đa tạo cửa hàng
translation-version: 'Phiên bản hỗ trợ: Hikari'
no-double-chests: <red>Bạn không thể tạo ra Shop gương đôi.
price-too-cheap: <red>Giá tiền phải lớn hơn <yellow>${0}
shop-not-exist: <red>Không có shop nào.
bad-command-usage: <red>Lệnh không hợp lệ!
cleanghost-warning: <yellow>This command will purge <red>all</red> shops if the shop is corrupted, was created in not allowed worlds, is selling/buying not allowed items or <red><bold>EXISTS IN A UNLOADED WORLD</bold></red>. Make sure to create a full backup of your shop data first and use <aqua>/qs cleanghost confirm</aqua> to continue.
cleanghost-starting: <green>Bắt đầu kiểm tra các cửa hàng ảo (không có đặt khối rương). Tất cả các cửa hàng không tồn tại sẽ bị xóa...
cleanghost-deleting: <yellow>Tìm thấy một cửa hàng bị hỏng <aqua>{0}</aqua> vì {1}, đánh dấu nó để xóa...
cleanghost-deleted: <green>Tất cả có <yellow>{0}</yellow> cửa hàng đã bị xóa.
shop-purchase-cancelled: <red>Tính năng mua bán của shop đã bị huỷ.
bypassing-lock: <red>Bỏ qua khóa QuickShop!
bungee-cross-server-msg: '<green>QuickShop CSM: <green>{0}'
saved-to-path: Bản sao lưu đã được lưu vào {0}.
shop-now-freezed: <green>Bạn đã đóng băng cửa hàng. Không ai có thể giao dịch với cửa hàng này bây giờ!
price-is-now: <green>Giá mới của shop hiện giờ là <yellow>{0}
shops-arent-locked: <red>Xin hãy lưu ý, Các shop không có cơ chế bảo vệ khỏi trộm! Nếu không muốn bị trộm, khoá nó lại với các plugin LWC, Lockette, v. v!
that-is-locked: <red>Cửa hàng này đã bị khóa.
shop-has-no-space: <red>Shop chỉ còn chỗ cho {0} {1}.
safe-mode-admin: '<red><bold>WARNING: <red>The QuickShop on this server now running under safe-mode, no features will working, please type <yellow>/qs <red> command to check any errors.'
shop-stock-too-low: <red>Shop chỉ còn {0} {1} !
world-not-exists: <red>Thế giới <yellow>{0} <red>không tồn tại
how-many-sell: <green>Nhập vào khung chat số lượng bạn muốn <aqua>BÁN<green>. Bạn có thể bán <aqua>{0}<green>. Nhập <aqua>{1}<green> để bán tất cả.
shop-freezed-at-location: <yellow>Cửa hàng của bạn {0} tại {1} đã bị đóng băng!
translation-contributors: 'Người đóng góp: huyhhuy'
empty-success: <green>Cửa hàng trống
taxaccount-set: <green>Tài khoản thuế của cửa hàng này đã được đặt thành <yellow>{0}
support-disable-reason:
  hot-reload: <yellow>Tải lại không được hỗ trợ, hãy khởi động lại máy chủ và kiểm tra lại.
  outdated: <yellow>Phiên bản QuickShop này đã lỗi thời, hãy cập nhật trước khi yêu cầu hỗ trợ!
  bad-hosts: |-
    <yellow>Máy chủ HOSTS này đã được sửa đổi và QuickShop một số chức năng yêu cầu kết nối chính xác với API Mojang. Sửa lỗi HOSTS trước khi yêu cầu hỗ trợ, Windows tại C:\windows\system32\drivers\etc\hosts, Linux tại /etc/hosts.
  privacy: <red>Máy chủ này đang chạy ở chế độ crack (ngoại tuyến). Nếu bạn đang chạy máy chủ bukkit dưới proxy và online-mode=true trên proxy, vui lòng định cấu hình các cài đặt liên quan đến proxy một cách chính xác.
  modified: <yellow>File integrity check failed, this QuickShop build has been modified by others.
  consolespamfix-installed: <yellow>ConsoleSpamFix installed, it will hidden exception details, temporarily disable it during asking support.
  authlib-injector-detected: <yellow>Máy chủ này đang chạy dưới nhà cung cấp authlib bên thứ 3 như authlib-injection.
  unsupported-server-software: <yellow>Unsupported server software, Any modded hybrid server software is unsupported including MCPC, Cauldron, CatServer, Mohist, Magma, Fukkit, Cardboard etc.
supertool-is-disabled: Supertool bị tắt. Không thể phá bất kỳ cửa hàng nào.
unknown-owner: Không xác định
restricted-prices: '<red>Giá hạn chế cho {0}: Tối thiểu {1}, tối đa {2}'
nearby-shop-this-way: <green>Shop cách {0} blocks từ vị trí của bạn.
owner-bypass-check: <yellow>Bỏ qua tất cả các kiểm tra. Giao dịch thành công! (Bây giờ bạn là chủ cửa hàng!)
april-rick-and-roll-easter-egg: "<hover:show_text:'Click to open in browser for time limited rewards!'><click:open_url:'https://www.youtube.com/watch?v=dQw4w9WgXcQ'><green>Nhấp vào đây để nhận phần thưởng phiên bản giới hạn do nhà phát triển QuickShop-Hikari cung cấp!</green></click></hover>"
signs:
  item-right: ''
  out-of-space: Hết chỗ trống
  unlimited: Vô hạn
  stack-selling: Bán {0}
  stack-price: '{0} cho {1}x {2}'
  status-unavailable: <red>
  out-of-stock: Hết hàng
  stack-buying: Mua {0}
  freeze: Giao dịch bị vô hiệu hóa
  price: '{0} cho 1'
  buying: Mua {0}
  header: '{1}{0}'
  header-available: <green>{0}
  header-unavailable: <red>{0}
  selling: Bán {0}
  status-available: <green>
  item-left: ''
negative-amount: <red>Bạn không thể giao dịch lượng số âm
display-turn-on: <green>Successfully turn on the shop display.
shop-staff-deleted: <green>Successfully removed {0} as a staff member for your shop.
nearby-shop-header: '<green>Shop <aqua>{0}<green>đã được tìm thấy gần đây:'
backup-failed: Không thể sao lưu cơ sở dữ liệu. Kiểm tra bảng điều khiển để biết chi tiết.
shop-staff-cleared: <green>Đã xóa thành công tất cả nhân viên khỏi cửa hàng của bạn.
price-too-high: <red>The shop price is too high! You cannot create one with a price higher than {0}.
plugin-cancelled: '<red>Thao tác bị hủy, Lý do: {0}'
player-sold-to-your-store: <green>{0} đã bán {1} {2} cho shop của bạn.
shop-out-of-stock: <dark_purple>Shop của bạn ở toạ độ {0}, {1}, {2} đã hết {3}!
how-many-buy: <green>Nhập vào khung chat số lượng bạn muốn <aqua>MUA<green>. Bạn có thể mua <aqua>{0}<green>. Nhập <aqua>{1}<green> để mua tất cả.
language-info-panel:
  help: 'Liên lạc'
  code: 'Mã: '
  name: 'Ngôn ngữ: '
  progress: 'Tiến trình:'
  translate-on-crowdin: '[Dịch trên Crowdin]'
item-not-exist: <red>Vật phẩm <yellow>{0} <red>không tồn tại, vui lòng kiểm tra chính tả của bạn.
shop-creation-failed: <red>Không tạo được cửa hàng, vui lòng liên hệ với quản trị viên máy chủ.
inventory-space-full: <red>Your inventory space remains can only put <green>{1}x <red>items into it, try empty the your inventory!
no-creative-break: <red>You cannot break other players shops in creative mode, switch to survival mode or try to use the supertool {0} instead.
booleanformat:
  success: <green>✔
  failed: <red>✘
controlpanel:
  stack: '<green>Per bulk amount: <aqua>{0} <yellow>[<light_purple><bold>Change<yellow>]'
  price-hover: <yellow>Nhấp để set giá mới cho item.
  remove: <red><bold>[Xoá Shop]
  mode-buying-hover: <yellow>Nhấp để đổi shop thành chế độ BÁN.
  empty: '<green>Empty: Remove all items <yellow>[<light_purple><bold>OK<yellow>]'
  stack-hover: Nhấp để đặt số lượng vật phẩm trên mỗi số lượng lớn. Đặt thành 1 cho hành vi bình thường.
  alwayscounting-hover: <yellow>Click to toggle if the shop is always counting container.
  alwayscounting: '<green>Always counting: {0} <yellow>[<light_purple><bold>Change<yellow>]'
  setowner: '<green>Owner: <aqua>{0} <yellow>[<light_purple><bold>Change<yellow>]'
  freeze: '<yellow>Freeze mode: <aqua>{0} <yellow>[<light_purple><bold>Toggle<yellow>]'
  price: '<green>Price: <aqua>{0} <yellow>[<light_purple><bold>Change<yellow>]'
  currency-hover: <yellow>Nhấp để đặt hoặc xóa đơn vị tiền tệ mà cửa hàng này đang sử dụng
  lock: '<yellow>Shop lock: <aqua>{0} <yellow>[<light_purple><bold>Toggle<yellow>]'
  mode-selling: '<green>Chế độ Shop: <aqua>Bán <yellow>[<light_purple><bold>Đổi<yellow>]'
  currency: '<green>Currency: <aqua>{0} <yellow>[<light_purple><bold>Set<yellow>]'
  setowner-hover: <yellow>Nhấp để đổi chủ.
  mode-buying: '<green>Shop mode: <aqua>Buying <yellow>[<light_purple><bold>Change<yellow>]'
  item: '<green>Shop Item: {0} <yellow>[<light_purple><bold>Change<yellow>]'
  unlimited: '<green>Unlimited: {0} <yellow>[<light_purple><bold>Change<yellow>]'
  unlimited-hover: <yellow>Nhấp để đổi trạng thái số lượng Shop.
  refill-hover: <yellow>Nhấp để refill shop.
  remove-hover: <yellow>Nhấp để xoá shop.
  toggledisplay-hover: <yellow>Toggle the shop's displayitem status
  refill: '<green>Refill: Refill the items <yellow>[<light_purple><bold>OK<yellow>]'
  freeze-hover: <yellow>Chuyển đổi trạng thái đóng băng của cửa hàng.
  lock-hover: <yellow>Bật/Tắt tính năng bảo vệ khóa của cửa hàng.
  item-hover: <yellow>Nhấp để đổi item của shop
  infomation: '<green>Cửa sổ điều chỉnh Shop:'
  mode-selling-hover: <yellow>Nhấp để đổi shop thành chế độ MUA.
  empty-hover: <yellow>Nhấp để xóa kho đồ của cửa hàng.
  toggledisplay: '<green>Vật phẩm hiện thỉ: <aqua>{0} <yellow>[<light_purple><bold>Cài đặt</bold></light_purple>]'
  history: '<green>Lịch sử: <yellow>[<bold><light_purple>Xem</light_purple></bold>]</yellow>'
  history-hover: <yellow>Nhấp để xem nhật ký lịch sử cửa hàng
timeunit:
  behind: sau
  week: "{0} tuần"
  weeks: "{0} tuần"
  year: "{0} năm"
  before: trước
  scheduled: đã lên kế hoạch
  years: "{0} năm"
  scheduled-in: Đặt trước {0}
  second: "{0} giây"
  std-past-format: '{5}{4}{3}{2}{1}{0}trước'
  std-time-format: HH:mm:ss
  seconds: "{0} giây"
  hour: "{0} giờ"
  scheduled-at: lên hẹn lúc {0}
  after: sau
  day: "{0} ngày"
  recent: Gần đây
  between: giữa
  hours: "{0} giờ"
  months: "{0} tháng"
  longtimeago: trước đây khá lâu
  between-format: từ {0} đến {1}
  minutes: "{0} phút"
  justnow: mới đây
  minute: "{0} phút"
  std-format: dd/MM/yyyy HH:mm:ss
  future-plain-text: tương lai
  month: "{0} tháng"
  future: trong {0}
  days: "{0} ngày"
command:
  reloading: '<green>Configuration reloaded. <yellow>Some changes may require reboot to affect. <newline><gray>(Notice: Reloading behavior has been changed after ********, we now only reload configuration but not whole plugin to ensure the server won''t crashed.)'
  description:
    buy: Thay đổi cửa hàng sang chế độ <light_purple>MUA
    about: <yellow>Hiển thị thông tin QuickShop
    language: <yellow>Thay đổi ngôn ngữ hiện tại
    purge: <yellow>Start the shop purge task in background
    paste: <yellow>Tải dữ liệu máy chủ lên Pastebin
    title: <green>QuickShop trợ giúp
    remove: <yellow>Xóa cửa hàng bạn đang nhìn
    ban: <yellow>Bans a player from the shop
    empty: <yellow>Xóa tất cả các vật phẩm khỏi cửa hàng
    alwayscounting: <yellow>Set if shop always counting container even is unlimited
    setowner: <yellow>Thay đổi quyền sở hữu của một cửa hàng.
    reload: <yellow>Tải lại config.yml của QuickShop
    freeze: <yellow>Tắt hoặc Bật giao dịch tại cửa hàng
    price: <yellow>Thay đổi giá mua/bán của một cửa hàng
    find: <yellow>Xác định loại cửa hàng cụ thể gần nhất.
    create: <yellow>Tạo cửa hàng mới từ rương được nhắm mục tiêu
    lock: <yellow>Chuyển trạng thái KHÓA của cửa hàng
    currency: <yellow>Đặt hoặc xóa cài đặt tiền tệ của cửa hàng
    removeworld: <yellow>Xóa TẤT CẢ các cửa hàng trong thế giới được chỉ định
    info: <yellow>Hiển thị thống kê QuickShop
    owner: <yellow>Thay đổi quyền sở hữu của một cửa hàng.
    amount: <yellow>Để đặt số lượng vật phẩm.
    item: <yellow>Thay đổi vật phẩm của cửa hàng
    debug: <yellow>Bật chế độ nhà phát triển
    unlimited: <yellow>Cung cấp cho một cửa hàng kho không giới hạn.
    sell: Thay đổi cửa hàng sang chế độ <light_purple>BÁN
    fetchmessage: <yellow>Hiển thị tin nhắn cửa hàng chưa đọc
    staff: <yellow>Quản lý nhân viên cửa hàng của bạn
    clean: <yellow>Xóa tất cả các cửa hàng (đã tải) mà không hàng nào
    refill: <yellow>Thêm số vật phẩm nhất định vào một cửa hàng
    help: <yellow>Hiển thị trợ giúp QuickShop
    removeall: <yellow>Xóa TẤT CẢ các cửa hàng của người chơi được chỉ định
    unban: <yellow>Unbans a player from the shop
    transfer: <yellow>Chuyển TẤT CẢ các cửa hàng từ người này sang người khác.
    transferall: <yellow>Chuyển TẤT CẢ các cửa hàng từ người này sang người khác.
    transferownership: <yellow>Chuyển cửa hàng bạn đang nhìn trước mặt cho người khác
    size: <yellow>Thay đổi theo số lượng lớn của cửa hàng
    supercreate: <yellow>Tạo cửa hàng trong khi bỏ qua tất cả các kiểm tra bảo vệ
    taxaccount: <yellow>Đặt tài khoản thuế mà cửa hàng sử dụng
    name: <yellow>Đặt tên cửa hàng thành tên cụ thể
    toggledisplay: <yellow>Chuyển đổi trạng thái vật phẩm hiển thị trong cửa hàng
    permission: <yellow>quản lý quyền của Shop
    lookup: <yellow>Quản lý bảng mục có thể tra cứu
    database: <yellow>View and maintain QuickShop database
    benefit: <yellow>Cài đặt phân chia lợi nhuận giữa chủ cửa hàng và những người chơi khác
    tag: <yellow>Thêm, xóa hoặc truy vấn thẻ của cửa hàng
    suggestprice: <yellow>Đề xuất giá hợp lí cho cửa hàng tìm kiếm, dựa trên các cửa hàng khác
    history: <yellow>Xem lịch sử giao dịch của cửa hàng
    sign: <yellow>Thay đổi chất liệu gỗ của bảng tên của cửa hàng
  bulk-size-not-set: '<red>Sử dụng lệnh: /qs find <số lượng>'
  no-type-given: '<red>Sử dụng lệnh: /qs find <item>'
  feature-not-enabled: Tính năng này không được kích hoạt trong tệp cấu hình.
  now-debuging: <green>Đã bật chế độ nhà phát triển thành công. Đang tải lại QuickShop ...
  no-amount-given: <red>Không có số tiền được cung cấp. Sử dụng <green>/qs refill <số lượng><red>
  no-owner-given: <red>Không có chủ sở hữu.
  disabled: '<red>Lệnh này bị tắt: <yellow>{0}'
  bulk-size-now: <green>Đang giao dịch <yellow>{0}x {1}
  toggle-always-counting:
    counting: <green>Shop is now always counting container even is unlimited
    not-counting: <green>Shop is now respect if shop is unlimited
  cleaning: <green>Đang loại bỏ các cửa hàng đã hết hàng ...
  now-nolonger-debuging: <green>Đã tắt chế độ nhà phát triển thành công. Đang tải lại QuickShop ...
  toggle-unlimited:
    limited: <green>Cửa hàng bây giờ có giới hạn
    unlimited: <green>Cửa hàng bây giờ là không giới hạn
  transfer-success-other: <green>Đã chuyển cửa hàng <yellow>{0} {1} <green>sang <yellow>{2}
  no-trade-item: <green>Vui lòng giữ item muốn đổi trên tay
  wrong-args: <red>Lệnh không hợp lệ. Sử dụng <green>/qs help
  some-shops-removed: <yellow>{0} <green>cửa hàng đã bị xóa
  new-owner: '<green>Chủ mới: <yellow>{0}'
  format: <green>/{0} {1} <yellow>- {2}
  transfer-success: <green>Đã chuyển cửa hàng <yellow>{0} <green>sang <yellow>{1}
  now-buying: '<green>Trạng thái: <light_purple>ĐANG BÁN <yellow>{0}'
  now-selling: '<green>Trạng thái: <light_purple>ĐANG MUA <yellow>{0}'
  cleaned: <green>Đã xoá <yellow>{0}<green> shops.
  trade-item-now: <green>Đang giao dịch <yellow>{0}x {1}
  no-world-given: <red>Xin vui lòng chỉ rõ một tên hợp lệ.
  format-disabled: <red>/{0} {1} <gray>- {2}
  invalid-bulk-amount: <red>Giá trị đưa vào {0} đang lớn hơn số lượng max của 1 stack hoặc có thể đang nhỏ hơn
currency-not-support: <red>Plugin kinh tế không hỗ trợ tính năng multi-economy.
trading-in-creative-mode-is-disabled: <red>You cannot trade with this shop while being in creative mode.
the-owner-cant-afford-to-buy-from-you: <red>Tiêu tốn {0}, trong khi chủ sở hữu chỉ còn {1}
you-cant-afford-shop-naming: <red>You can't afford shop naming, it costs {0} to naming.
inventory-error: |-
  <red>Failed to processing the InventoryWrapper. Did you using addon to re-binding the shop Inventory? Information: Exception={0}, InventoryWrapper={1}, WrapperType={2}, WrapperProvider={3}, SymbolLink={4}. Please contact the server administrators.
integrations-check-failed-create: <red>Tích hợp {0} đã từ chối sự khởi tạo shop
shop-out-of-space: <dark_purple>Shop của bạn ở toạ độ {0}, {1}, {2} đã bị đầy.
admin-shop: AdminShop
no-anythings-in-your-hand: <red>Chẳng có gì trong tay bạn cả.
no-permission: <red>Bạn không có quyền làm thế.
chest-was-removed: <red>Gương đã được xoá.
you-cant-afford-to-buy: <red>Tiêu tốn {0} để mua, nhưng bạn chỉ còn {1}
shops-removed-in-world: <yellow>Total <aqua>{0}<yellow> shops has been deleted in world <aqua>{1}<yellow>.
display-turn-off: <green>Successfully turn off the shop display.
client-language-unsupported: <yellow>QuickShop doesn't support your client language, we're fallback to {0} locale now.
language-version: '63'
not-managed-shop: <red>Bạn không phải người sở hữu/điều hành của shop này
shop-cannot-trade-when-freezing: <red>Bạn không thể giao dịch với cửa hàng này vì nó bị đóng băng.
invalid-container: <red>Invalid container, You can only create the shop on block who have inventory.
permission:
  header: Chi tiết về quyền <green>cửa hàng
  header-player: <green>Chi tiết quyền cửa hàng cho {0}
  header-group: <green>Chi tiết quyền cửa hàng cho nhóm {0}
  table: <gold><b>{0}</b>:</gold> <gray>{1}</gray>
  item:
    purchase: <yellow>Permission to allow users who have this to purchase the shop. (including buying and selling)
    show-information: <yellow>Quyền cho phép người dùng xem thông tin cửa hàng. (Mở bảng thông tin cửa hàng)
    preview-shop: <yellow>Quyền cho phép người dùng xem trước cửa hàng. (Xem trước vật phẩm)
    search: <yellow>Permission to allow users who have this to search the target shop. (remove permission will hide shop from search result)
    delete: <yellow>Quyền cho phép người chơi có quyền xóa cửa hàng này.
    receive-alert: <yellow>Permission to allow users who have this to receive alert message (e.g out-of-stock or new trade).
    access-inventory: <yellow>Quyền cho phép người chơi có quyền này truy cập vào kho của cửa hàng.
    ownership-transfer: <yellow>Quyền cho phép người chơi có quyền này chuyển quyền sở hữu cửa hàng.
    management-permission: <yellow>Permission to allow users who have this to manage the groups permissions and edit user's group.
    toggle-display: <yellow>Quyền cho phép người chơi có thể thay đổi vật phẩm hiển thị của cửa hàng.
    set-shoptype: <yellow>Permission to allow users who have this to set the shop type (switch buying or selling).
    set-price: <yellow>Quyền cho phép người chơi có đặt giá cửa hàng.
    set-item: <yellow>Quyền cho phép người chơi có thể đặt vật phẩm trong cửa hàng.
    set-stack-amount: <yellow>Quyền cho phép người dùng có thể đặt số lượng cửa hàng.
    set-currency: <yellow>Quyền cho phép người chơi có thể đặt đơn vị tiền tệ của cửa hàng.
    set-name: <yellow>Quyền cho phép người chơi được đặt tên trong hàng.
    set-sign-type: <yellow>Thay đổi chất liệu gỗ bảng tên gắn trên cửa hàng.
    view-purchase-logs: <yellow>Quyền xem nhật ký mua hàng của cửa hàng.
  group:
    everyone: <yellow>Default group for all users excepted shop owner.
    staff: <yellow>System group for shop staffs.
    administrator: <red>System group for administrators, the users in this group will have permissions almost same with shop owner.
invalid-group: <red>Tên không hợp lệ.
invalid-permission: <red>Quyền không hợp lệ.
invalid-operation: <red>Hoạt động không hợp lệ, chỉ {0} được cho phép.
player-no-group: <yellow>Người chơi {0} không thuộc bất kỳ nhóm nào trong cửa hàng này.
player-in-group: <green>Người chơi {0} nằm trong nhóm <aqua>{1}</aqua> trong cửa hàng này.
permission-required: <red>Bạn không có quyền {0} trong cửa hàng này để làm điều này.
no-permission-detailed: <red>Bạn không có quyền <yellow>{0}</yellow> để làm điều này.
paste-notice: "<yellow>Lưu ý: Nếu bạn đang tạo \"Paste\" cho mục đích khắc phục sự cố, hãy đảm bảo làm lại quá trình gây ra lỗi trước khi tạo \"Paste\" càng nhanh càng tốt; Chúng tôi cần các bản ghi giữ lại một thời gian ngắn trong bộ đệm để khắc phục sự cố. Nếu bạn tạo \"Paste\" quá chậm hoặc không làm lại quá trình gây ra lỗi trước hoặc khởi động lại máy chủ thì \"Paste\" sẽ không có giá trị và vô dụng."
paste-uploading: <aqua>Vui lòng chờ... Đảng tải paste lên pastebin......
paste-created: '<green>Paste created, click to open in browser: <yellow>{0}</yellow><newline><red>Warning: <gray>Never send paste to who you don''t trust.'
paste-created-local: |-
  <green>Paste created and saved to your local disk at: {0}<newline><red>Warning: <gray>Never send paste to who you don't trust.
paste-created-local-failed: <red>Không thể lưu "paste" vào ổ cứng của bạn, vui lòng kiểm tra ổ cứng của bạn.
paste-451: |-
  <gray><b>TIPS: </b> Your current country or region seems to have blocked the CloudFlare Workers service and QuickShop Paste may not be loaded properly.<newline><gray>If the subsequent operation fails, try adding the --file additional parameter to generate a local Paste: <dark_gray>/qs paste --file</dark_gray>
paste-upload-failed: <red>Failed to upload paste to {0}, trying other pastebin provider...
paste-upload-failed-local: <red>Failed to upload paste, trying generate local paste...
command-incorrect: '<red>Command usage incorrect, type /qs help to check help. Usage: {0}.'
successfully-set-player-group: <green>Đã thành công đặt người chơi từ nhóm {0} đến <aqua>{1}</aqua>.
successfully-unset-player-group: <green>Successfully remove player group in this shop.
successfully-set-player-permission: <green>Đã thành công cho {0} quyền <aqua>{1}</aqua> trong cửa hàng <aqua>{2}</aqua>.
lookup-item-created: <green>An item named <aqua>{0}</aqua> has been created in the lookup table. You can refer this item in configurations now.
lookup-item-exists: <red>Đã có vật phẩm <yellow>{0}</yellow> đã tồn tại trong cửa hàng dưới dạng vật phẩm trưng bày, hãy xóa hoặc chọn tên khác.
lookup-item-not-found: <red>An item named <yellow>{0}</yellow> not exists.
lookup-item-name-illegal: <red>Item name illegal. Only alphanumeric characters and underscores are allowed.
lookup-item-name-regex: '<red>Tên phải khớp với biểu thức này: <aqua>{0}</aqua>.'
lookup-item-test-not-found: '<gold>Test: <yellow>The item in your hand not registered in lookup table.'
lookup-item-test-found: '<gold>Test: <green>The item in your hand was registered as name <aqua>{0}</aqua> in lookup table.'
lookup-item-removed: <green>The specified item <aqua>{0}</aqua> has been successfully removed from lookup table.
internal-error: <red>Đã xảy ra lỗi nội bộ, vui lòng liên hệ với quản trị viên máy chủ.
argument-cannot-be: <red>Tham số <aqua>{0}</aqua> không thể trở thành <yellow>{1}</yellow>.
argument-must-between: <red>The argument <aqua>{0}</aqua> value must between <yellow>{1}</yellow> and <yellow>{2}</yellow>
invalid-percentage: <red>Tỷ lệ phần trăm không hợp lệ, bạn phải thêm '%' sau số phần trăm.
display-fallback: |-
  <red>Due to an internal failure, a fallback message is being displayed.
  The value of this item may not be localized or processed correctly.
  Please contact the server administrator.
not-a-valid-time: |-
  <red>The string <yellow>{0}</yellow> not a valid timestamp, please enter a <aqua>Zulu Time (ISO 8601)</aqua> or <aqua>Unix Epoch Time in seconds</aqua>.
  <gold>Valid timestamp example: (for Sat, 17 Dec 2022 10:31:37 GMT)</gold>
  <aqua>-<aqua> <yellow>2022-12-17T10:31:37Z</yellow> <grey>(Zulu Time)</grey>
  <aqua>-<aqua> <yellow>1671273097</yellow> <grey>(Unix Epoch Time in seconds)</grey>
invalid-past-time: <red>You can't specify a time in the past.
debug:
  arguments-invalid: <red>Provided arguments <yellow>{0}</yellow> invalid.
  sign-located: '<green>Sign valid: <yellow>{0}</yellow>.'
  operation-missing: <red>You must specific an operation.
  operation-invalid: <red>You must specific an valid operation.
  invalid-base64-encoded-sql: <red>The SQL provided must be base64 encoded.
  warning-sql: |-
    <red><bold>Warning:</bold></red><yellow>You're executing a SQL statement, this may corrupt your database or destroy any data in database even it belongs to other plugins.
    <red>Don't confirm if you don't trust who send you this command.
  warning-sql-confirm: <yellow>To confirm this dangerous operation, please type <aqua>/qs debug database sql confirm {0}</aqua> in 60 seconds.
  warning-sql-confirm-hover: <yellow>Click to confirm this dangerous operation.
  sql-confirm-not-found: <yellow>Didn't found the operation that you given, it may invalid or expired.
  sql-executing: '<yellow>Thực thi câu lệnh SQL: <aqua>{0}</aqua>'
  sql-completed: <green>Completed, {0} rows affected.
  sql-exception: <red>An error occurred while executing the SQL query, check the Console for details!
  sql-disabled: '<red>For security reason, SQL queries are disabled in this server, If you really need this feature, you can add the following flag to startup arguments: <aqua>{0}</aqua> with `true` value to enable it.'
  force-shop-reload: <yellow>Forcing reload all loaded shops...
  force-shop-reload-complete: <green>Forced <aqua>{0}</aqua> shops to be reloaded.
  force-shop-loader-reload: <yellow>Forcing shop-loader reloading...
  force-shop-loader-reload-unloading-shops: <yellow>Unloading <aqua>{0}</aqua> loaded shops...
  force-shop-loader-reload-unloading-shops-from-memory: <yellow>Removing <aqua>{0}</aqua> shops from memory...
  force-shop-loader-reload-reloading-shop-loader: <yellow>Re-calling shop-loader for reloading all shops from database...
  force-shop-loader-reload-complete: <green>Shop-loader has been reloaded all shops!
  toggle-shop-loaded-status: <aqua>Toggle this shop loaded status to <gold>{0}</gold>
  shop-internal-data: '<yellow>The internal data of this shop: <reset>{0}</reset>'
  handler-list-not-valid-bukkit-event-class: <red>The provided class <yellow>{0}</yellow> is not a valid Bukkit event class.
  update-player-shops-signs-no-username-given: <red>You must provide a valid player username.
  update-player-shops-signs-create-async-task: <yellow>Creating async tasks for force updating signs...
  update-player-shops-player-selected: '<yellow>Player selected: <gold>{0}</gold>'
  update-player-shops-player-shops: <yellow>Total <gold>{0}</gold> shops waiting for updates.
  update-player-shops-per-tick-threshold: '<yellow>Max shops can be update per tick: <gold>{0}</gold>'
  update-player-shops-complete: <green>Task completed, Used <yellow>{0}ms</yellow> for updating.
  update-player-shops-task-started: <gold>The tasks has been started, please wait for it to complete.
  item-info-store-as-string: "<green>The store you looking at: <gold>{0}</gold> Hash: <white>{1}</white>"
  item-info-hand-as-string: "<green>The item in your hand: <gold>{0}</gold> Hash: <white>{1}</white>"
  item-matching-result: "<green>Hand2Store: <aqua>{0}</aqua>, Store2Hand: <aqua>{1}</aqua>"
  hikari-cp-size-tweak: "<green>HikariCP MaximumPoolSize and MinimumIdle was set to <white>{0}</white>"
  hikari-cp-testing: "<green>Please wait, testing HikariCP connection..."
  hikari-cp-working: "<green>Pass! HikariCP working well!"
  hikari-cp-not-working: "<red>Failed! The connection that returned by HikariCP is dead! (Not passed test in 1 second)"
  hikari-cp-timeout: "<red>HikariCP is timed out while getting a valid connection, please clean up all active queries to release connection resources."
  queries-stopped: "<green>Stopped <white>{0}</white> active queries."
  queries-dumping: "<yellow>Dumping active queries..."
  restart-database-manager: "<yellow>Restarting SQLManager..."
  restart-database-manager-clear-executors: "<yellow>Clearing executors..."
  restart-database-manager-unfinished-task: "<yellow>Unfinished task: <white>{0}</white>"
  restart-database-manager-unfinished-task-history-query: "<yellow>Unfinished task (History Query): <white>{0}</white>"
  restart-database-manager-reconnect: "<yellow>Re-launching SQLManager via initial sequence (via async executor)"
  restart-database-manager-done: "<green>Done!"
  property-incorrect: "<yellow>You must enter a (and only one) property key=value set. E.g   aaa=bbb"
  property-security-block: "<red>Request was rejected, for security reason, you can only change the property that startsWith <aqua>com.ghostchu.quickshop</aqua> or <aqua>quickshop</aqua>."
  property-removed: "<green>Removed property key <white>{0}</white>"
  property-changed: "<green>Property key <white>{0}</white> was changed from <white>{1}</white> to <white>{2}</white>"
  marked-as-dirty: "<green>Marked all stores as dirty status, they will be force save in next auto-save task. (Restart the server to force execute store saving task)"
  display-removed: "<green>Successfully removed <yellow>{0}</yellow> QuickShop display items/entities from the worlds."
database:
  scanning: <green>Scanning the isolated data in QuickShop databases, the database load may increase in scan progress, this might need a while...
  scanning-async: <yellow>Scanning the isolated data in QuickShop databases on async task thread, the database load may increase in scan progress, this might need a while, try again later.
  already-scanning: <red>An scan task has been started, please wait until it finished.
  trim-warning: '<yellow><red><bold>Warning: </red>Backup your database before continue database trim to avoid the data loss. Once you get ready, execute <aqua>/qs database trim confirm</aqua> to continue.'
  status: '<yellow>Trạng thái: {0}'
  status-good: <green>Tốt
  status-bad: <yellow>Maintenance Required
  isolated: '<yellow>Dữ liệu bị cô lập:'
  isolated-data-ids: '<yellow><aqua>└</aqua> Data Records: <gold>{0}</gold>'
  isolated-shop-ids: '<yellow><aqua>└</aqua> Shop Indexes: <gold>{0}</gold>'
  isolated-logs: '<yellow><aqua>└</aqua> Logs: <gold>{0}</gold>'
  isolated-external-caches: '<yellow><aqua>└</aqua> External Caches: <gold>{0}</gold>'
  last-purge-time: <yellow>Lần cắt cuối cùng tại {0}
  report-time: <yellow>Lần quét cuối cùng tại {0}
  auto-scan-alert: <yellow>The QuickShop database on this server require a maintenance. Found <gold>{0}</gold> isolated data wait for trimming.
  auto-trim: <green>Auto trim has been enabled on this server, no manually trim required.
  trim-complete: <green>Database trim completed, <yellow>{0}</yellow> isolated trimmed.
  auto-trim-started: <green>Auto trim has been started, please wait...
  trim-start: <green>Database trim started, please wait...
  trim-exception: <red>Database trim failed, an exception raised during database trimming, check the server console.
  generated-at: '<yellow>Generated at: <gold>{0}</gold>'
  purge-date: <red>You must given a date.
  purge-warning: <yellow>This operation will purge the history that stored in your database including shop creates/changes/deletes, purchases, transactions and system logs. Delete those data can free the disk space but all history metric will lost, and other plugins that depend on metrics will stop working. To continue execute this operation, execute `/qs database purgelogs <before-days> confirm`
  purge-task-created: <green>Task created! Database are silent purging history records in background.
  purge-done-with-line: <green>Purge task completed, total <gold>{0}</gold> lines purged from database.
  purge-done-with-error: <red>Purge task failed, check the server console for details.
  purge-players-cache: <yellow>Please wait, purging players caches...
  purge-players-completed: |-
    <green>Successfully purged {0} players caches from both memory and database.<newline><aqua>Note: You server performance may impact by this operation.
  purge-players-error: <red>Failed to purge players caches, please check the server console.
  suggestion:
    trim: <yellow>This database require a isolated data trim, execute command <aqua>/qs database trim</aqua> to trim the database.
always-counting-removal-early-warning: <red>Always Counting feature is scheduled for removal, you shouldn't use it anymore and will stop work in future.
exporting-database: <green>Đang xuất cơ sở dữ liệu, vui lòng đợi...
exporting-failed: <red>Không thể xuất cơ sở dữ liệu, vui lòng kiểm tra bảng điều khiển máy chủ.
exported-database: <green>Cơ sở dữ liệu đã được xuất tới <yellow>{0}</yellow>.
importing-not-found: <red>The <yellow>{0}</yellow> file not found, please check the file path.
importing-early-warning: |-
  <yellow><red><bold>Warning: </bold></red>The backup will be imported to the current database, any exists data will be purged and permanently lost if you don't have a backup.<newline><red>Are you sure to continue the import progress?</red><yellow>Type <aqua>/qs recovery confirm</aqua> to continue.
importing-database: <green>Đang nhập cơ sở dữ liệu từ bản sao lưu, vui lòng đợi...
importing-failed: <red>Không thể nhập cơ sở dữ liệu, vui lòng kiểm tra bảng điều khiển máy chủ.
imported-database: <green>Cơ sở dữ liệu đã được nhập từ <yellow>{0}</yellow>.
transfer-sent: <green>Shops transfer request sent to <yellow>{0}</yellow>.
transfer-request: <yellow>Player <aqua>{0}</aqua> requested to transfer their shops to you. Did you want accept the transfer?
transfer-single-request: <yellow>Player <aqua>{0}</aqua> would like to transfer a shop to you. Do you want to accept the request?
transfer-ask: |-
  <gold>Type <red>/qs transfer accept</red> to accept.</gold><newline><gold>Type <red>/qs transfer deny</red> to deny.</gold><newline><gold>Request will expire after <red>{0}</red> seconds.
transferall-ask: |-
  <gold>Type <red>/quickshop transferall accept</red> to accept or <red>/quickshop transferall deny</red> to deny.
  The request will expire after <red>{0}</red> seconds.
transfer-single-ask: |-
  <gold>Type <red>/quickshop transferownership accept</red> to accept or <red>/quickshop transferownership deny</red> to deny.
  The request will expire after <red>{0}</red> seconds.
transfer-accepted-fromside: <green>Player <aqua>{0}</aqua> accepted your shop transfer request.
transfer-accepted-toside: <green>You accepted <aqua>{0}</aqua>'s transfer request.
transfer-rejected-fromside: <red>Player <aqua>{0}</aqua> rejected your shop transfer request.
transfer-rejected-toside: <red>You rejected <aqua>{0}</aqua>'s shop transfer request.
transfer-no-pending-operation: <red>You don't have pending transfer request yet.
transfer-no-self: <red>You can't transfer your shops to yourself.
benefit-overflow: <red>The sum of all benefits cannot be greater than or equal to 100%.
benefit-exists: <red>Target player already in benefits list on this shop.
benefit-removed: <red>Target player has been removed from shop benefits.
benefit-added: <green>Player <aqua>{0}</aqua> has been added into shop benefits!
benefit-updated: <green>Player <aqua>{0}</aqua>'s benefits has been updated!
benefit-query: <green>This shop have <yellow>{0}</yellow> players in benefits list!
benefit-query-list: <yellow> - </yellow><white>Player <gold>{0}</gold>, Benefit <gold>{1}%</gold></white>
tag-added: '<green>Successfully added <aqua>#{0}</aqua> to this shop!'
tag-add-duplicate: '<red>The tag <aqua>#{0}</aqua> already exists at this shop!'
tag-removed: '<green>Successfully removed <aqua>#{0}</aqua> from this shop!'
tag-remove-not-exists: 'The tag <aqua>#{0}</aqua> not exists at this shop!'
tag-cleared: <green>Successfully cleared all tags from this shop!
tag-shops-cleared: '<green>Successfully cleared <aqua>#{0}</aqua> from all your tagged shops!'
tag-query: '<green>This shop have <yellow>{0}</yellow> tags:'
tag-query-listing: '<yellow> - <aqua>#{0}</aqua>'
tag-query-no-tag: <red>This shop have no tags.
tag-query-shops: '<green>This tag contains <yellow>{0}</yellow> shops:'
tag-query-shops-listing: <yellow> - <aqua>{0}</aqua>
batch-operations-based-on-tags-no-failure: <green>Successfully batch processed <yellow>{0}</yellow> shops.
batch-operations-based-on-tags-have-failure: <yellow>Total <yellow>{0}</yellow> shops in batch processing successfully completed, but <red>{1}</red> requests not completed.
batch-operations-based-on-tags-have-failure-with-reason: '<yellow>Total <yellow>{0}</yellow> shops in batch processing successfully completed, but <red>{1}</red> requests not completed. Reason: <gold>{2}</gold>'
batch-operations-based-on-tags-have-failure-error-messages-may-appears-above: <gray>The error messages will send to you above this message in the chat.
addon:
  towny:
    commands:
      town: <yellow>Set or unset a shop to a Town shop
      nation: <yellow>Set or unset a shop to a Nation shop
    make-shop-owned-by-town: <green>You have made the shop owned by town <yellow>{0}</yellow>.
    make-shop-no-longer-owned-by-town: <green>You have reset shop ownership, it now transfer back to original shop owner.
    make-shop-owned-by-nation: <green>You have made the shop owned by nation <yellow>{0}</yellow>.
    make-shop-no-longer-owned-by-nation: <green>You have reset shop ownership, it now transfer back to original shop owner.
    shop-owning-changing-notice: <grey>This shop now owned by a town/nation, original shop owner has been automatically added into administrator list, modify or add any new owner / staff by using command /qs permission
    target-shop-already-is-town-shop: <red>The target shop is already owned by a town.
    target-shop-already-is-nation-shop: <red>The target shop is already owned by a nation.
    target-shop-not-in-town-region: <red>The target shop is not in the town.
    target-shop-not-in-nation-region: <red>The target shop is not in the nation.
    item-not-allowed: <red>This shop's item are not allowed used by town/nation shop, pick another one!
    operation-disabled-due-shop-status: <red>This shop operation has been disabled due this already is a town/nation shop.
    plot-type-disallowed: <red>You can't create town/nation shop on this type of plot.
    flags:
      own: <red>You can only create shops at your owned shop type towny plot.
      modify: <red>You didn't have build permission on this towny plot.
      shop-type: <red>You must create shop on a shop type towny plot.
  residence:
    creation-flag-denied: <red>You don't have permission to create shops in this residence.
    trade-flag-denied: <red>You don't have permission to purchase in this residence.
    you-cannot-create-shop-in-wildness: <red>You can't create shop in wildness.
  griefprevention:
    creation-denied: <red>You don't have permission to create shops in this claim.
    trade-denied: <red>You don't have permission to purchase shops in this claim.
  lands:
    world-not-enabled: <red>You are not allowed to create or purchase in this world.
    creation-denied: <red>You don't have permission to create shops in this Land area.
  plotsquared:
    no-plot-whitelist-creation: <red>You cannot create shop outside of Plot.
    no-plot-whitelist-trade: <red>You cannot purchase shop outside of Plot.
    creation-denied: <red>You don't have permission to create shops in this Plot.
    trade-denied: <red>You don't have permission to purchase shops in this Plot.
    flag:
      create: Create QuickShop-Hikari shops
      trade: Purchase QuickShop-Hikari shops
  superiorskyblock:
    owner-create-only: <red>Only island owner can create shop there.
    owner-member-create-only: <red>Only island owner or member can create shop there.
  worldguard:
    creation-flag-test-failed: <red>You don't have permission create shop in this WorldGuard area.
    trade-flag-test-failed: <red>You don't have permission trade shop in this WorldGuard area.
    reached-per-region-amount-limit: "<red>You have reached the maximum amount of shops in this region."
  discord:
    __to_translators: 'To translators: `addon.discord.discord-messages` use a special variable in strings, %%LANG_KEY%% instead of QuickShop''s style {LANG_INDEX}, do not translate anything inside double ''%'' section. BUT for any other section texts, it respect the normal QuickShop Text System syntax ({0},{1} etc.) This may be difficult to understand because two different variable forms are used in the same program at the same time, but trust me, it is reasonable. (you don''t need translate this string, it''s a comment)'
    __to_users: Like the QuickShop Text System, Discord addon will also auto-detect user language and use user's language to send Discord message, it following the QuickShop-Hikari's language system settings.
    __to_message_designer: 'Design your Discord message with GUI: https://glitchii.github.io/embedbuilder/, then copy the JSON code and paste it into the translation and here we go!'
    discord-enabled: <aqua>Successfully <green>enabled</green> your QuickShop discord messages, now you can receive the shop messages from Discord.
    discord-disabled: <aqua>Successfully <red>disabled</red> your QuickShop discord messages, now you won't receive the shop messages from Discord anymore.
    discord-not-integrated: <red>You hadn't link your Discord yet! Please link your Discord account first!
    feature-enabled-for-user: <aqua>You have <green>enabled</green> <gold>{0}</gold> notification.
    feature-disabled-for-user: <aqua>You have <red>disabled</red> <gold>{0}</gold> notification.
    link-help: <yellow>This server using <gold>{0}</gold> for Discord driver, please use <green>{0}</green> to link your Discord account.
    save-notifaction-exception: <red>An error occurred while saving your Discord notifaction settings, please contact the server administrator.
    feature-status-changed: <green>Successfully set the notifaction <aqua>{0}</aqua> status to <gold>{1}</gold>
    commands:
      discord:
        description: <yellow>Managing your QuickShop Discord settings
    discord-messages:
      sold-to-your-shop: |
        {
         "embed":
           {
             "title": ":inbox_tray: Somebody sold items to your shop",
             "description": "The player %%purchase.name%% sold x%%purchase.amount%% %%shop.item.name%% to your shop.",
             "color": 52084,
             "author": {
               "name": "QuickShop-Hikari",
               "icon_url": ""
             },
             "thumbnail": {
               "url": ""
             },
             "image": {
               "url": ""
             },
             "footer": {
               "text": "QuickShop-Hikari Discord Notification",
               "icon_url": ""
             },
             "fields": [
               {
                 "name": "Shop",
                 "value": "%%shop.display-name%%",
                 "inline": false
               },
               {
                 "name": "Purchaser",
                 "value": "%%purchase.name%%",
                 "inline": true
               },
               {
                 "name": "Item",
                 "value": "%%shop.item.name%%",
                 "inline": true
               },
               {
                 "name": "Amount",
                 "value": "%%purchase.amount%% (x%%shop.stacking-amount%%)",
                 "inline": true
               },
               {
                 "name": "Your paid",
                 "value": "%%purchase.balance-formatted%%",
                 "inline": true
               },
               {
                 "name": "Taxes",
                 "value": "%%purchase.taxes-formatted%%",
                 "inline": true
               }
             ]
           }
        }
      bought-from-your-shop: |
        {
           "embed":
             {
               "title": ":outbox_tray: Somebody bought items from shop",
               "description": "The player %%purchase.name%% bought x%%purchase.amount%% %%shop.item.name%% from your shop.",
               "color": 52084,
               "author": {
                   "name": "QuickShop-Hikari",
                   "icon_url": ""
               },
               "thumbnail": {
                   "url": ""
               },
               "image": {
                   "url": ""
               },
               "footer": {
                   "text": "QuickShop-Hikari Discord Notification",
                   "icon_url": ""
               },
               "fields": [
                   {
                       "name": "Shop",
                       "value": "%%shop.display-name%%",
                       "inline": false
                   },
                   {
                       "name": "Purchaser",
                       "value": "%%purchase.name%%",
                       "inline": true
                   },
                   {
                       "name": "Item",
                       "value": "%%shop.item.name%%",
                       "inline": true
                   },
                   {
                       "name": "Amount",
                       "value": "%%purchase.amount%%(x%%shop.stacking-amount%%)",
                       "inline": true
                   },
                   {
                       "name": "Your earn",
                       "value": "%%purchase.balance-formatted%%",
                       "inline": true
                   },
                   {
                       "name": "Taxes",
                       "value": "%%purchase.taxes-formatted%%",
                       "inline": true
                   }
               ]
           }
         }
      mod-shop-purchase: |
        {
        "embed":
          {
              "title": ":dollar: Somebody purchased in a shop",
              "description": "The player %%purchase.name%% purchase x%%purchase.amount%% %%shop.item.name%% from the shop.",
              "color": 6061450,
              "author": {
                  "name": "QuickShop-Hikari",
                  "icon_url": ""
              },
              "thumbnail": {
                  "url": ""
              },
              "image": {
                  "url": ""
              },
              "footer": {
                  "text": "QuickShop-Hikari Discord Notification",
                  "icon_url": ""
              },
              "fields": [
                  {
                      "name": "Shop",
                      "value": "%%shop.display-name%%",
                      "inline": false
                  },
                  {
                      "name": "Purchaser",
                      "value": "%%purchase.name%%",
                      "inline": true
                  },
                  {
                      "name": "Item",
                      "value": "%%shop.item.name%%",
                      "inline": true
                  },
                  {
                      "name": "Amount",
                      "value": "%%purchase.amount%% (x%%shop.stacking-amount%%)",
                      "inline": true
                  },
                  {
                      "name": "Balance",
                      "value": "%%purchase.balance-formatted%%",
                      "inline": true
                  },
                  {
                      "name": "Taxes",
                      "value": "%%purchase.taxes-formatted%%",
                      "inline": true
                  }
              ]
          }
        }
      out-of-space: |
        {
        "embed":
          {
              "title": ":mailbox_with_mail: Your shop has run out of space",
              "description": "Your shop inventory is full of items!\nYou need empty your shop container to free the space then you can continue to accept new items.",
              "color": 16065893,
              "author": {
                  "name": "QuickShop-Hikari",
                  "icon_url": ""
              },
              "thumbnail": {
                  "url": ""
              },
              "image": {
                  "url": ""
              },
              "footer": {
                  "text": "QuickShop-Hikari Discord Notification",
                  "icon_url": ""
              },
              "fields": [
                  {
                      "name": "Shop",
                      "value": "%%shop.display-name%%",
                      "inline": false
                  }
              ]
          }
        }
      out-of-stock: |
        {
            "embed":
                {
                    "title": ":mailbox_with_no_mail: Your shop has run out of stock",
                    "description": "Your shop inventory is empty now!\nYou need to refill the shop container with items to continue selling items!",
                    "color": 16065893,
                    "author": {
                        "name": "QuickShop-Hikari",
                        "icon_url": ""
                    },
                    "thumbnail": {
                        "url": ""
                    },
                    "image": {
                        "url": ""
                    },
                    "footer": {
                        "text": "QuickShop-Hikari Discord Notification",
                        "icon_url": ""
                    },
                    "fields": [
                        {
                            "name": "Shop",
                            "value": "%%shop.display-name%%",
                            "inline": false
                        }
                    ]
                }
        }
      mod-shop-created: |
        {
          "embed": {
            "title": ":classical_building: A new shop has been created",
            "description": "Player created a new shop on your server!",
            "color": 6061450,
            "author": {
              "name": "QuickShop-Hikari",
              "icon_url": ""
            },
            "thumbnail": {
              "url": ""
            },
            "image": {
              "url": ""
            },
            "footer": {
              "text": "QuickShop-Hikari Discord Notification",
              "icon_url": ""
            },
            "fields": [
              {
                "name": "Shop",
                "value": "%%shop.display-name%%",
                "inline": false
              },
              {
                "name": "Owner",
                "value": "%%shop.owner.name%%",
                "inline": true
              },
              {
                "name": "Item",
                "value": "%%shop.item.name%%",
                "inline": true
              },
              {
                "name": "Amount",
                "value": "%%shop.stacking-amount%%",
                "inline": true
              },
              {
                "name": "Type",
                "value": "%%shop.type%%",
                "inline": true
              }
            ]
          }
        }
      mod-remove-shop: |
        {
            "embed": {
                "title": ":recycle: A shop was removed from this server",
                "description": "A shop was removed from this server.",
                "color": 6061450,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "Reason",
                        "value": "%%delete.reason%%",
                        "inline": false
                    }
                ]
            }
        }
      shop-transfer-to-you: |
        {
            "embed": {
                "title": ":placard: A shop was transfer to you",
                "description": "A shop transfer to you from another player.",
                "color": 16172079,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "From",
                        "value": "%%transfer.from%%",
                        "inline": false
                    }
                ]
            }
        }
      mod-shop-transfer: |
        {
            "embed": {
                "title": ":placard: A shop was transferred",
                "description": "A shop transfer to a player from another player.",
                "color": 6061450,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "From",
                        "value": "%%transfer.from%%",
                        "inline": true
                    },
                    {
                        "name": "To",
                        "value": "%%transfer.to%%",
                        "inline": true
                    }
                ]
            }
        }
      shop-price-changed: |
        {
            "embed": {
                "title": ":money_with_wings: Your shop price was changed",
                "description": "You or your shop staff changed your shop price.",
                "color": 16172079,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "From",
                        "value": "%%change-price.from%%",
                        "inline": true
                    },
                    {
                        "name": "To",
                        "value": "%%change-price.to%%",
                        "inline": true
                    }
                ]
            }
        }
      mod-shop-price-changed: |
        {
            "embed": {
                "title": ":money_with_wings: A shop price has been changed",
                "description": "A shop changed its price settings.",
                "color": 6061450,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": true
                    },
                    {
                        "name": "Owner",
                        "value": "%%shop.owner.name%%",
                        "inline": true
                    },
                    {
                        "name": "",
                        "value": "",
                        "inline": true
                    },
                    {
                        "name": "From",
                        "value": "%%change-price.from%%",
                        "inline": true
                    },
                    {
                        "name": "To",
                        "value": "%%change-price.to%%",
                        "inline": true
                    }
                ]
            }
        }
      shop-permission-changed: |
        {
            "embed": {
                "title": ":closed_lock_with_key: Your shop permission settings has been changed",
                "description": "One of your shop's permission setting has been changed.",
                "color": 15879747,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "Player",
                        "value": "%%change-permission.player%%",
                        "inline": true
                    },
                    {
                        "name": "Assign to group",
                        "value": "%%change-permission.to-group%%",
                        "inline": true
                    },
                    {
                        "name": "",
                        "value": "",
                        "inline": true
                    },
                    {
                        "name": "Permission granted (Inherit from group)",
                        "value": "```\n%%change-permission.perms-list%%\n```",
                        "inline": false
                    }
                ]
            }
        }
    field:
      player: Player
      item: Item
      amount: Amount
      balance: Balance
      balance-after-tax: Balance (after tax)
      account: Your account balance
      taxes: Taxes
      cost: Cost
  discount:
    commands:
      discount:
        description: <yellow>Apply a discount code or manage your discount codes.
    tab-complete:
      discount:
        general:
          code: <code>
        create:
          rate: |
            Command Hint:
            Argument: <rate>
            Description: The actual percentage or money you will earn
            Input `30%` = price * 0.3
            Input `50` = price - 50
          max-usage: |
            Command Hint:
            Argument: [max-usage]
            Description: The times that code can be used
            `-1` for unlimited.
          threshold: |
            Command Hint:
            Argument: [threshold]
            Description: The minimum price that code can be applied
            `-1` for unlimited
          expired: |
            Command Hint
            Argument: [expired]
            Description: The code expired time.
            `-1` for unlimited duration.
            Accept both Zulu time and UNIX timestamp in seconds.
            Zulu Example: 2022-12-17T10:31:37Z
            UNIX Example: 1671273097
    discount-code-already-exists: <red>Sorry, the name of your discount code already in use.
    invalid-discount-code-regex: '<red>The discount code must matches the regex: <yellow>{0}</yellow>'
    invalid-discount-code: <red>This discount code are invalid.
    discount-code-added: <green>Your discount code <yellow>{0}</yellow> has been added into shop <aqua>{1}</aqua>
    discount-code-removed: <green>Your discount code <yellow>{0}</yellow> has been removed from shop <aqua>{1}</aqua>
    invalid-code-type: <red>The code type <yellow>{0}</yellow> are invalid.
    invalid-usage-restriction: <red>The usage restriction <yellow>{0}</yellow> are invalid.
    invalid-threshold-restriction: <red>The threshold restriction <yellow>{0}</yellow> are invalid.
    invalid-effect-scope: <red>The scope <yellow>{0}</yellow> are invalid.
    invalid-expire-time: <red>You can't specify a time in the past.
    invalid-discount-rate: <red>The discount rate <yellow>{0}</yellow> are invalid, it can be a fixed number or percentage.
    discount-code-expired: <red>Ouch! Your discount code <yellow>{0}</yellow> has been expired!
    discount-code-installed: <green>You have installed discount code <gold>{0}</gold>, the discount code will be automatically applied to the available purchase in all the following purchases during this session. To uninstall discount code, execute <aqua>/qs discount uninstall {0}</aqua>.
    discount-code-uninstalled: <green>You have uninstalled your discount code.
    discount-code-query-nothing: <red>You hadn't install a discount code yet!
    discount-code-query: '<green>You are using discount code: <yellow>{0}</yellow>.'
    discount-code-applicable: '<color:#bcef26>Your discount code <yellow><bold>{0}</bold></yellow> are <bold>applicable</bold> in this shop!'
    discount-code-applicable-with-threshold: '<color:#bcef26>Your discount code <yellow><bold>{0}</bold></yellow> are <bold>applicable</bold> in this shop, but only your purchase cost over <yellow>{1}</yellow> in single transaction.'
    discount-code-not-applicable: <red>Your discount code <yellow><bold>{0}</bold></yellow> are <bold>not applicable</bold> in this shop!
    discount-code-reach-the-limit: <red>You have reach the usage limit of discount code <yellow><bold>{0}</bold></yellow>, discount are not applied.
    discount-code-no-permission: <red>You didn't have permission to use any discount code in this shop!
    discount-code-has-been-expired: <red>Your discount has been expired!
    discount-code-config-shop-added: <green>Successfully to add this shop into your discount code allow list.
    discount-code-config-shop-add-failure: <red>This shop already in your discount code allow list.
    discount-code-config-shop-removed: <green>Successfully to remove this shop from your discount code allow list.
    discount-code-config-shop-remove-failure: <red>This shop not exists in your discount code allow list.
    discount-code-config-expire: <green>Successfully changed your code expire time.
    discount-code-config-applied: <green>Config the discount code successfully!
    discount-code-created-successfully: |
      <green>Your discount code <yellow>{0}</yellow> has been successfully created!
      <gold>Scope: <aqua>{1}</aqua></gold>.
      <gold>Share with others: <color:#bcef26>{2}</color:#bcef26></gold>.
      <yellow>For <gray>Specific Shops</gray> scope only: To add shops to your discount code allow list, please looking an quickshop and execute <aqua>{3}</aqua> command.
      <yellow>You can use <aqua>/qs discount config {0}</aqua> to edit your discount code anytime.
    discount-code-under-threshold: <red>The discount not applied to your purchase because the total value under the discount code threshold <yellow>{0}</yellow>.
    percentage-off: '<bold><color:#bcef26>-{0}%</color:#bcef26></bold>'
    fixed-off: '<bold><color:#bcef26>-{0}</color:#bcef26></bold>'
    discount-code-list: '<gold>Listing your discount codes:</gold>'
    discount-code-applied-in-purchase: '<color:#bcef26>Your discount code <yellow>{0}</yellow> has been applied to this purchase and you saved <gold>{1}</gold>!'
    scope:
      this-shop: '{0}'
      your-shops-owned: all your shops (owned)
      your-shops-managed: all your shops (managed)
      server: whole server
    code-type:
      SERVER_ALL_SHOPS: All shops on this server
      PLAYER_ALL_SHOPS: All shops that created by code owner
      SPECIFIC_SHOPS: Specific shops
    discount-code-details: |-
      <gold>Code: <yellow>{0}</yellow>
      <gold>Creator: <yellow>{1}</yellow>
      <gold>Applied to: <yellow>{2}</yellow>
      <gold>Remaining usage: <yellow>{3}</yellow>
      <gold>Expired on: <yellow>{4}</yellow>
      <gold>Threshold: <yellow>{5}</yellow>
      <gold>Discount: <yellow>{6}</yellow>
  list:
    commands:
      list: <yellow>Listing all shops owned by your or specific player
    table-prefix: '<yellow><green>{0}</green>''s shops <gray>(Total {1})</gray>: '
    table-prefix-pageable: '<yellow><green>{0}</green>''s shops <gray>(Page {1}/{2})</gray>: '
    entry: "<hover:show_text:'<yellow>{1}</yellow>\n<gray>{2} {3},{4},{5}</gray>\n<aqua>Price <yellow>{6}</yellow> - <light_purple>x{7}</light_purple> <yellow>{8}</yellow>\n<green>{9}'><yellow>{0}. <aqua>{1}</aqua></hover>"
  shopitemonly:
    message: <red>You can't put non-shop items into shop container, all non-shop items will dropped at your location.
  limited:
    commands:
      limit: <yellow>Set a limit that restrict the purchase that player can make in period
    titles:
      title: <green>Purchase Success
      subtitle: <aqua>You can also purchase <gold>{0}</gold> more in this shop
    reach-the-limit: <red>You have reached the limit in this shop. You can also purchase <green>{0}</green> items, but you trying purchase <yellow>{1}</yellow> items.
    success-reset: <green>Reset the restriction of this shop successfully
    success-remove: <green>Successfully remove the all restriction of this shop
    success-setup: <green>Restriction settings for this shop were saved successfully
    trade-limit-reached-cancel-reason: <red>Reached the restriction of this shop
    remains-limits: '<gold>Your remaining purchase quota in this shop: <yellow>{0}</yellow>'
    remains-limits-before-reset-period-note: '<gold>This shop will reset their purchase quota every: <yellow>{0}</yellow>'
  dynmap:
    markerset-title: QuickShop-Hikari Shops
    marker-name: '{0} @ {2} @ {3} for x{4} @ {5}'
    marker-description: |
      Name: {0}
      Owner: {1}
      Item: {2}
      Price: {3} for x{4} item(s)
      Type: {5}
      Unlimited: {6}
      Location: {7}
  bluemap:
    markerset-title: QuickShop-Hikari Shops
    marker-name: '{0} @ {2} @ {3} for x{4} @ {5}'
    marker-description: |
      Name: {0}
      Owner: {1}
      Item: {2}
      Price: {3} for x{4} item(s)
      Type: {5}
      Unlimited: {6}
      Location: {7}
  chestprotect:
    protection-exists: <red>This region already protected by ChestProtect and you don't have permission to create shops at here.
    shops-exsts: <red>The region that you want to protect contains other player's shops and you don't have permission to access them.
  displaycontrol:
    toggle: |-
      <green>Successfully toggle your QuickShop display showcasing to <aqua>{0}</aqua>.
      <yellow>You may need re-join to take effect.
    toggle-exception: <red>Failed to toggle your display showcase settings due an internal error, please contact server administrator.
    command:
      displaycontrol: <yellow>Toggle your QuickShop display showcasing
  reremake-migrator:
    commands:
      migratefromreremake: Migrate QuickShop-Reremake's data to QuickShop-Hikari
    server-not-empty: "<red>No players are allowed to be online on the server during the conversion progress, please enable server whitelist or maintenance mode."
    starting-convert-progress: "<gold>Starting the convert progress, <red>DO NOT SHUTDOWN YOUR SERVER!</red>"
    executing: "<gold>Executing migrate component <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
    completed: "<green>Done! Migrate completed, please restart your server and remove QuickShop-Reremake from plugins directory."
    join_blocking_converting: "<red>[QuickShop-Hikari Migrator] This server is performing a data conversion and you cannot join the server at this time, try again later!"
    join_blocking_finished: "<red>[QuickShop-Hikari Migrator] This server has just completed a data conversion and is waiting for a reboot to apply the changes, try again later!"
    failed: "<red>Migration progress exited with error, please check the server console."
    modules:
      config:
        copy-values: "<yellow>Copying values (total {0} entries)..."
        copying-value: "<gray> - Copying <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        migrate-price-restriction: "<yellow>Migrating price-restriction related settings..."
        migrate-price-restriction-entry: "<gray> - Migrating <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
      shop:
        start-migrate: "<yellow>Migrating shops (total {0} entries)..."
        migrate-entry: "<gray> - Migrating <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        unloading-reremake: "<gold>Unloading Reremake to avoid data overwrite..."
        register-entry: "<gray> - Registering <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        save-entry: "<gray> - Saving <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        saving-shops: "<yellow>Saving <gold>{0}</gold> shops, this may need a while™ (the time based on shops amounts)..."
        conflict: "<gray> - CONFLICT > Detects a conflict between a Reremake store and an existing Hikari store location, taking a predefined behavior: <dark_gray>{0}</dark_gray>"
      translation:
        start-migrate: "<yellow>Migrating translation files...</yellow>"
        copy-values: "<yellow>Copying values in locale <gold>{0}</gold> (total {1} entries)..."
        migrate-entry: "<gray> - Migrating <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        copying-value: "<gray> - Copying <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
      shop-logs:
        start-migrate: "<yellow>Migrating shop logs (details in console), please wait...</yellow>"
        extract-history-files: "<gray>Please wait for us to unzip and append the history logs..."
        filter-history-files: "<gray>Please wait for us to filter the history log..."
        filtered-history-files: "<gray>Filtered {0} lines out of queue."
        import-entry: "<gray> - Migrating <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
compat:
  advancedchests:
    created: <green>You're successfully created a shop on AdvancedChests container!
    permission-denied: <red>Sorry! You don't have the permission to create a shop on a AdvancedChests container!
suite:
  dynmap:
    title: '{0} - {1} - {2}'
    description: |-
      Item: {0}
      Owner: {1}
      Type: {2} {3}
      Price: {4}
      Location: {5} {6}, {7}, {8}
      Space: {9}
      Stock: {10}
  limited:
    command-description: <yellow>Set a limit that restrict the purchase that player can make in period.
    reach-the-quota-limit: <red>You have reach the purchase limit in this shop ({0}/{1}).
    quota-reset-countdown: <yellow>The quota in this shop will reset in {0}.
    quota-reset-player-successfully: <green>The quota for player {0} in this shop has been reset successfully.
    quota-reset-everybody-successfully: <green>The quota for everyone in this shop has been reset successfully.
    quota-setup: <green>The purchase restriction now apply to this shop!
    quota-remove: <green>The purchase restriction now removed from this shop!
    subtitles:
      title: <green>Purchase Success
      subtitle: <aqua>You can purchase <yellow>{0}</yellow> more in this shop
  list:
    command-description: <yellow>List all shops owned by yourself or another player.
    table-prefix: <yellow>You owned total <aqua>{0}</aqua> shops on this server.
    entry-syntax: <aqua>- <yellow>{0}
    entry-syntax-fallback-name: '<yellow>Item:{0} X:{1}, Y:{2}, Z:{3}, World: {4}'
    hover-text: |-
      <yellow>{0}
      <gray>{1} {2}, {3}, {4}
      <aqua>{5} - <yellow>{6}
      <yellow>{7} items each stack.
  shopitemonly:
    message: <red>You can't put non-shop items into shop container, all non-shop items will dropped at your location.
compatibility:
  elitemobs:
    soulbound-disallowed: You can't trade item which have EliteMobs Soulbound enchantment.
internet-paste-forbidden-privacy-reason: "<red>Failed! According your privacy setting, QuickShop-Hikari are not allowed upload your paste to Internet, turn on DIAGNOSTIC permission in privacy setting in config.yml or use <aqua>/quickshop paste --file</aqua> instead."
no-sign-type-given: "<red>You need give a sign material, the sign materials that available on this server: {0}"
sign-type-invalid: "<red>The type <yellow>{0}</yellow> not a valid sign material."
delete-controlpanel-button-confirm: "<red>Do you really want remove this shop? Click the <bold>[Remove Shop]</bold> button again in {0} seconds to confirm."
cannot-suggest-price: "<red>Sorry, For the time being, no more people are trading the same item as you and there is not enough data to generate a suggested price."
price-suggest: "<green>Based on data from <aqua>{0}</aqua> stores, the highest priced store is priced at <light_purple>{1}</light_purple>, the lowest priced store is priced at <light_purple>{2}</light_purple>, the average price is <light_purple>{3}</light_purple>, and the median price is <light_purple>{4}</light_purple>. <newline><yellow>It's recommended that you set your price around <gold>{5}</gold>.</yellow>"
suggest-wait: "<green>Please wait... Calculating recommend price."
history:
  shop:
    gui-title: "View purchase history"
    header-icon-multiple-shop: "<white>The query result across {0} shops</white>"
    header-icon-description:
      - "<white>Type: <yellow>{0}</yellow></white>"
      - "<white>Owner: <yellow>{1}</yellow></white>"
      - "<white>Item: <yellow>{2}</yellow></white>"
      - "<white>Price: <yellow>{3} <aqua>x{4}</aqua></yellow></white>"
      - "<white>Location: <yellow>{5}</yellow></white>"
    log-icon-title: "<reset><green>Time: {0}</green>"
    log-icon-description:
      - "<white>Purchaser: <yellow>{0}</yellow></white>"
      - "<white>Item: <yellow>{1} <aqua>x{2}</aqua></yellow></white>"
      - "<white>Balance: <yellow>{3}</yellow></white>"
      - "<white>Tax: <yellow>{4}</yellow></white>"
    log-icon-description-with-store-name:
      - "<white>Store: <yellow>{0}</yellow></white>"
      - "<white>Purchaser: <yellow>{1}</yellow></white>"
      - "<white>Item: <yellow>{2} <aqua>x{3}</aqua></yellow></white>"
      - "<white>Balance: <yellow>{4}</yellow></white>"
      - "<white>Tax: <yellow>{5}</yellow></white>"
    query-icon: "<gray>Please wait, querying...</gray>"
    previous-page: "<white><< Previous Page</white>"
    next-page: "<white>Next Page >></white>"
    current-page: "<white>Page {0}</white>"
    summary-icon-title: "<green>Shop Summary"
    recent-purchases: "<white>Recent <aqua>{0}</aqua> purchases: <yellow>{1}</yellow></white>"
    recent-purchase-balance: "<white>Recent <aqua>{0}</aqua> turnover: <yellow>{1}</yellow></white>"
    total-purchases: "<white>Total purchases: <yellow>{0}</yellow></white>"
    total-balances: "<white>Total turnover: <yellow>{0}</yellow></white>"
    total-unique-purchasers: "<white>Total unique purchasers: <yellow>{0}</yellow></white>"
    top-n-valuable-customers-title: "<gold>Top {0} valuable customers</gold>"
    top-n-valuable-customers-entry: "<white>- <yellow>{0}</yellow> <aqua>{1}</aqua></white>"
    no-result: "<white>No result</white>"
about:
  text:
    - "<b><aqua>QuickShop <yellow>{0}</yellow></aqua></b>"
    - "<aqua>Version <yellow>>></yellow> <green>{1}</green></aqua>"
    - "<aqua>Release <yellow>>></yellow> <green>{2}</green></aqua>"
    - "<aqua>Developers <yellow>>></yellow> <color:#218bff><hover:show_text:'<yellow>Click to check contributors'><click:open_url:'https://github.com/Ghost-chu/QuickShop-Hikari/graphs/contributors'>[View contributors on GitHub]</click></hover></color></aqua>"
    - "<aqua>Localized members <gold>({4})</gold>: <yellow>>></yellow> <green>{5}</green></aqua>"
    - "  <yellow>+ <color:#43A047><hover:show_text:'<yellow>Click to open Crowdin translate page'><click:open_url:'https://crowdin.com/project/quickshop-hikari'>[Help translate on Crowdin]</click></hover></color></yellow>"
    - "<aqua>Donation Key <yellow>>></yellow> <green>{6}</green></aqua>"
    - "<gold>Powered by community</gold> <red>Made with ❤</red>"
  valid-donation-key: "<color:#00AFF1>Bound to <color:#F9C23C>{0}</color></color>"
  invalid-donation-key: "<gray>Invalid donation key</gra>"
  kofi-thanks: "<gold>Special thanks to those who support QuickShop on Ko-fi :)</gold>"
history-command-leave-blank: "<Leave blank for seeing store>"
shop-information-not-shown-due-an-internal-error: "<red>An internal error occurred. The store information panel may be displayed incompletely, please contact the server administrator."
