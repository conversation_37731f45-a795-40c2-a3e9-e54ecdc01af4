[21:11:40] [ServerMain/INFO]: [bootstrap] Running Java 21 (OpenJDK 64-Bit Server VM 21+35-jvmci-23.1-b15; GraalVM Community GraalVM CE 21+35.1) on Windows 11 10.0 (amd64)
[21:11:40] [ServerMain/INFO]: [bootstrap] Loading Paper 1.21.8-11-main@a5f2f61 (2025-07-22T08:53:33Z) for Minecraft 1.21.8
[21:11:41] [ServerMain/INFO]: [PluginInitializerManager] Initializing plugins...
[21:11:41] [Paper Plugin Remapper Thread - 0/INFO]: [PluginRemapper] Remapping plugin 'plugins\CMILib*******.jar'...
[21:11:42] [Paper Plugin Remapper Thread - 0/INFO]: [PluginRemapper] Done remapping plugin 'plugins\CMILib*******.jar' in 314ms.
[21:11:42] [ServerMain/INFO]: [PluginInitializerManager] Initialized 20 plugins
[21:11:42] [ServerMain/INFO]: [PluginInitializerManager] Bukkit plugins (20):
 - AuthMe (5.6.0-SNAPSHOT-b2623), BedrockPlayerSupport (2.1.0), CMILib (*******), Essentials (2.20.1), EssentialsChat (2.20.1), EssentialsSpawn (2.20.1), Geyser-Spigot (2.8.2-SNAPSHOT), LuckPerms (5.4.131), MiniMOTD (2.1.2), PlaceholderAPI (2.11.6), ProtocolLib (5.3.0), QuickShop-Hikari (*******), Residence (*******), SkinsRestorer (15.7.7), TAB (5.0.3), Themis (0.17.6), Vault (1.7.3-CMI), ViaBackwards (5.4.2), ViaVersion (5.4.2), floodgate (2.2.4-SNAPSHOT (b118-40d320a))
[21:11:45] [ServerMain/INFO]: Environment: Environment[sessionHost=https://sessionserver.mojang.com, servicesHost=https://api.minecraftservices.com, name=PROD]
[21:11:45] [ServerMain/INFO]: [ca.spottedleaf.dataconverter.minecraft.datatypes.MCTypeRegistry] Initialising converters for DataConverter...
[21:11:45] [ServerMain/INFO]: [ca.spottedleaf.dataconverter.minecraft.datatypes.MCTypeRegistry] Finished initialising converters for DataConverter in 258.2ms
[21:11:46] [ServerMain/INFO]: Loaded 1407 recipes
[21:11:46] [ServerMain/INFO]: Loaded 1520 advancements
[21:11:46] [Server thread/INFO]: Starting minecraft server version 1.21.8
[21:11:46] [Server thread/INFO]: Loading properties
[21:11:46] [Server thread/INFO]: This server is running Paper version 1.21.8-11-main@a5f2f61 (2025-07-22T08:53:33Z) (Implementing API version 1.21.8-R0.1-SNAPSHOT)
[21:11:46] [Server thread/INFO]: [spark] This server bundles the spark profiler. For more information please visit https://docs.papermc.io/paper/profiling
[21:11:46] [Server thread/INFO]: Server Ping Player Sample Count: 12
[21:11:46] [Server thread/INFO]: Using 4 threads for Netty based IO
[21:11:47] [Server thread/INFO]: [MoonriseCommon] Paper is using 4 worker threads, 1 I/O threads
[21:11:47] [Server thread/INFO]: [ChunkTaskScheduler] Chunk system is using population gen parallelism: true
[21:11:47] [Server thread/INFO]: Default game type: SURVIVAL
[21:11:47] [Server thread/INFO]: Generating keypair
[21:11:47] [Server thread/INFO]: Starting Minecraft server on *:25565
[21:11:47] [Server thread/INFO]: Using default channel type
[21:11:47] [Server thread/INFO]: Paper: Using Java compression from Velocity.
[21:11:47] [Server thread/INFO]: Paper: Using Java cipher from Velocity.
[21:11:48] [Server thread/INFO]: [ViaVersion] Loading server plugin ViaVersion v5.4.2
[21:11:48] [Server thread/INFO]: [ViaVersion] ViaVersion 5.4.2 is now loaded. Registering protocol transformers and injecting...
[21:11:48] [Via-Mappingloader-0/INFO]: [ViaVersion] Loading block connection mappings ...
[21:11:48] [Via-Mappingloader-0/INFO]: [ViaVersion] Using FastUtil Long2ObjectOpenHashMap for block connections
[21:11:48] [Server thread/INFO]: [ViaBackwards] Loading translations...
[21:11:48] [Server thread/INFO]: [ViaBackwards] Registering protocols...
[21:11:48] [Server thread/INFO]: [LuckPerms] Loading server plugin LuckPerms v5.4.131
[21:11:49] [Server thread/INFO]: [Vault] Loading server plugin Vault v1.7.3-CMI
[21:11:49] [Server thread/INFO]: [Essentials] Loading server plugin Essentials v2.20.1
[21:11:49] [Server thread/INFO]: [PlaceholderAPI] Loading server plugin PlaceholderAPI v2.11.6
[21:11:49] [Server thread/INFO]: [floodgate] Loading server plugin floodgate v2.2.4-SNAPSHOT (b118-40d320a)
[21:11:49] [Server thread/INFO]: [floodgate] 启动 Floodgate，耗时390ms
[21:11:49] [Server thread/INFO]: [ProtocolLib] Loading server plugin ProtocolLib v5.3.0
[21:11:49] [Server thread/WARN]: [ProtocolLib] Version (MC: 1.21.8) has not yet been tested! Proceed with caution.
[21:11:49] [Server thread/ERROR]:   [ProtocolLib] INTERNAL ERROR: Cannot load ProtocolLib.
  If this problem hasn't already been reported, please open a ticket
  at https://github.com/dmulloy2/ProtocolLib/issues with the following data:
  Stack Trace:
  java.lang.RuntimeException: Unable to find network.ProtocolInfo$a (network.ProtocolInfo$Unbound)
  	at ProtocolLib (2).jar//com.comphenix.protocol.utility.MinecraftReflection.lambda$getMinecraftClass$5(MinecraftReflection.java:1581)
  	at java.base/java.util.Optional.orElseThrow(Optional.java:403)
  	at ProtocolLib (2).jar//com.comphenix.protocol.utility.MinecraftReflection.getMinecraftClass(MinecraftReflection.java:1581)
  	at ProtocolLib (2).jar//com.comphenix.protocol.utility.MinecraftReflection.getProtocolInfoUnboundClass(MinecraftReflection.java:1878)
  	at ProtocolLib (2).jar//com.comphenix.protocol.injector.packet.PacketRegistry.createRegisterV1_20_5(PacketRegistry.java:367)
  	at ProtocolLib (2).jar//com.comphenix.protocol.injector.packet.PacketRegistry.initialize(PacketRegistry.java:552)
  	at ProtocolLib (2).jar//com.comphenix.protocol.injector.packet.PacketRegistry.getClientPacketTypes(PacketRegistry.java:604)
  	at ProtocolLib (2).jar//com.comphenix.protocol.injector.PacketFilterManager.<init>(PacketFilterManager.java:112)
  	at ProtocolLib (2).jar//com.comphenix.protocol.injector.PacketFilterBuilder.build(PacketFilterBuilder.java:121)
  	at ProtocolLib (2).jar//com.comphenix.protocol.ProtocolLib.onLoad(ProtocolLib.java:179)
  	at io.papermc.paper.plugin.storage.ServerPluginProviderStorage.processProvided(ServerPluginProviderStorage.java:59)
  	at io.papermc.paper.plugin.storage.ServerPluginProviderStorage.processProvided(ServerPluginProviderStorage.java:18)
  	at io.papermc.paper.plugin.storage.SimpleProviderStorage.enter(SimpleProviderStorage.java:39)
  	at io.papermc.paper.plugin.entrypoint.LaunchEntryPointHandler.enter(LaunchEntryPointHandler.java:39)
  	at org.bukkit.craftbukkit.CraftServer.loadPlugins(CraftServer.java:566)
  	at net.minecraft.server.dedicated.DedicatedServer.initServer(DedicatedServer.java:235)
  	at net.minecraft.server.MinecraftServer.runServer(MinecraftServer.java:1164)
  	at net.minecraft.server.MinecraftServer.lambda$spin$2(MinecraftServer.java:310)
  	at java.base/java.lang.Thread.run(Thread.java:1583)
  Dump:
  Parameters: 
    [NULL]
  Sender:
    com.comphenix.protocol.ProtocolLib@59a28a6e[
      statistics=<null>
      packetTask=<null>
      tickCounter=0
      configExpectedMod=-1
      updater=com.comphenix.protocol.updater.SpigotUpdater@e9339ac
      redirectHandler=<null>
      scheduler=com.comphenix.protocol.scheduler.DefaultScheduler@643f508b
      commandProtocol=<null>
      commandPacket=<null>
      commandFilter=<null>
      packetLogging=<null>
      skipDisable=false
      isEnabled=false
      loader=io.papermc.paper.plugin.manager.DummyBukkitPluginLoader@40dad08
      server=CraftServer{serverName=Paper,serverVersion=1.21.8-11-a5f2f61,minecraftVersion=1.21.8}
      file=plugins\ProtocolLib (2).jar
      description=org.bukkit.plugin.PluginDescriptionFile@6c2dd9d1
      pluginMeta=org.bukkit.plugin.PluginDescriptionFile@6c2dd9d1
      dataFolder=plugins\ProtocolLib
      classLoader=PluginClassLoader{plugin=ProtocolLib v5.3.0, pluginEnabled=false, url=plugins\ProtocolLib (2).jar}
      naggable=true
      newConfig=YamlConfiguration[path='', root='YamlConfiguration']
      configFile=plugins\ProtocolLib\config.yml
      logger=com.destroystokyo.paper.utils.PaperPluginLogger@7f122466
      lifecycleEventManager=io.papermc.paper.plugin.lifecycle.event.PaperLifecycleEventManager@59ab11d8
      allowsLifecycleRegistration=true
      isBeingEnabled=false
    ]
  Version:
    ProtocolLib v5.3.0
  Java Version:
    21
  Server:
    1.21.8-11-a5f2f61 (MC: 1.21.8)

[21:11:49] [Server thread/INFO]: [EssentialsSpawn] Loading server plugin EssentialsSpawn v2.20.1
[21:11:49] [Server thread/INFO]: [CMILib] Loading server plugin CMILib v*******
[21:11:49] [Server thread/INFO]: [ViaBackwards] Loading server plugin ViaBackwards v5.4.2
[21:11:49] [Server thread/INFO]: [Geyser-Spigot] Loading server plugin Geyser-Spigot v2.8.2-SNAPSHOT
[21:11:49] [Server thread/INFO]: [Geyser-Spigot] 正在加载扩展...
[21:11:49] [Server thread/INFO]: [Geyser-Spigot] 已加载 0 个扩展
[21:11:49] [Server thread/INFO]: [AuthMe] Loading server plugin AuthMe v5.6.0-SNAPSHOT-b2623
[21:11:49] [Server thread/INFO]: [Residence] Loading server plugin Residence v*******
[21:11:49] [Server thread/INFO]: [SkinsRestorer] Loading server plugin SkinsRestorer v15.7.7
[21:11:49] [Server thread/INFO]: [EssentialsChat] Loading server plugin EssentialsChat v2.20.1
[21:11:49] [Server thread/INFO]: [Themis] Loading server plugin Themis v0.17.6
[21:11:50] [Server thread/INFO]: [BedrockPlayerSupport] Loading server plugin BedrockPlayerSupport v2.1.0
[21:11:50] [Server thread/INFO]: [QuickShop-Hikari] Loading server plugin QuickShop-Hikari v*******
[21:11:50] [Server thread/INFO]: [QuickShop-Hikari/Bootstrap] QuickShop-Hikari - Bootstrap -> Execute the initialization sequence
[21:11:50] [Server thread/INFO]: [QuickShop-Hikari/Bootstrap] Bootloader preparing for startup, please wait...
[21:11:50] [Server thread/INFO]: [QuickShop-Hikari/Bootstrap] Initializing libraries...
[21:11:51] [Server thread/INFO]: [QuickShop-Hikari] Maven repository mirror test result:
[21:11:51] [Server thread/INFO]: [QuickShop-Hikari] [CN] TENCENT: 22ms
[21:11:51] [Server thread/INFO]: [QuickShop-Hikari] [CN] ALIYUN: 47ms
[21:11:51] [Server thread/INFO]: [QuickShop-Hikari] [CN] HUAWEI: 85ms
[21:11:51] [Server thread/INFO]: [QuickShop-Hikari] [US] APACHE: 526ms
[21:11:51] [Server thread/INFO]: [QuickShop-Hikari] [US] CENTRAL: 818ms
[21:11:51] [Server thread/INFO]: [QuickShop-Hikari/Bootstrap] Loading 12 libraries (0 skipped libraries)...
[21:11:51] [Server thread/INFO]: [QuickShop-Hikari/Bootstrap] Loading library org.apache.commons:commons-lang3:3.14.0 [1/12]
[21:11:51] [Server thread/ERROR]: [STDERR] [org.slf4j.helpers.Util] SLF4J: Failed to load class "org.slf4j.impl.StaticLoggerBinder".
[21:11:51] [Server thread/ERROR]: [STDERR] [org.slf4j.helpers.Util] SLF4J: Defaulting to no-operation (NOP) logger implementation
[21:11:51] [Server thread/ERROR]: [STDERR] [org.slf4j.helpers.Util] SLF4J: See http://www.slf4j.org/codes.html#StaticLoggerBinder for further details.
[21:11:51] [Server thread/INFO]: [QuickShop-Hikari/Bootstrap] Loading library org.apache.commons:commons-compress:1.25.0 [2/12]
[21:11:51] [Server thread/INFO]: [QuickShop-Hikari/Bootstrap] Loading library com.google.code.gson:gson:2.10.1 [3/12]
[21:11:51] [Server thread/INFO]: [QuickShop-Hikari/Bootstrap] Loading library com.google.guava:guava:33.1.0-jre [4/12]
[21:11:51] [Server thread/INFO]: [QuickShop-Hikari/Bootstrap] Loading library com.rollbar:rollbar-java:1.9.0 [5/12]
[21:11:51] [Server thread/INFO]: [QuickShop-Hikari/Bootstrap] Loading library cc.carm.lib:easysql-hikaricp:0.4.7 [6/12]
[21:11:51] [Server thread/INFO]: [QuickShop-Hikari/Bootstrap] Loading library com.h2database:h2:2.1.214 [7/12]
[21:11:51] [Server thread/INFO]: [QuickShop-Hikari/Bootstrap] Loading library com.konghq:unirest-java:3.14.5 [8/12]
[21:11:51] [Server thread/INFO]: [QuickShop-Hikari/Bootstrap] Loading library net.sourceforge.csvjdbc:csvjdbc:1.0.42 [9/12]
[21:11:51] [Server thread/INFO]: [QuickShop-Hikari/Bootstrap] Loading library org.dom4j:dom4j:2.1.4 [10/12]
[21:11:51] [Server thread/INFO]: [QuickShop-Hikari/Bootstrap] Loading library com.vdurmont:semver4j:3.1.0 [11/12]
[21:11:51] [Server thread/INFO]: [QuickShop-Hikari/Bootstrap] Loading library com.ghostchu.crowdin:crowdinota:1.0.3 [12/12]
[21:11:51] [Server thread/INFO]: [QuickShop-Hikari/Bootstrap] Initialing Unirest...
[21:11:51] [Server thread/INFO]: [QuickShop-Hikari/Bootstrap] Initializing platform...
[21:11:51] [Server thread/INFO]: [QuickShop-Hikari/Bootstrap] Platform detected: Paper
[21:11:51] [Server thread/INFO]: [QuickShop-Hikari] Slf4jLogger initialized
[21:11:51] [Server thread/INFO]: [QuickShop-Hikari/Bootstrap] Platform initialized: com.ghostchu.quickshop.platform.paper.PaperPlatform
[21:11:51] [Server thread/INFO]: [QuickShop-Hikari/Bootstrap] Boot QuickShop instance...
[21:11:51] [Server thread/INFO]: [QuickShop-Hikari/Bootstrap] Creating QuickShop instance...
[21:11:51] [Server thread/INFO]: [QuickShop-Hikari] Registering Bukkit Service: com.ghostchu.quickshop.api.QuickShopProvider
[21:11:51] [Server thread/INFO]: [QuickShop-Hikari] QuickShop Hikari - Early boot step - Booting up
[21:11:51] [Server thread/INFO]: [QuickShop-Hikari] Self testing...
[21:11:51] [Server thread/INFO]: [QuickShop-Hikari] Reading the configuration...
[21:11:51] [Server thread/INFO]: [QuickShop-Hikari] [ConfigUpdater] Saving configuration changes...
[21:11:51] [Server thread/INFO]: [QuickShop-Hikari] Setting up privacy controller...
[21:11:51] [Server thread/INFO]: [QuickShop-Hikari] Setting up QuickShop registry....
[21:11:51] [Server thread/INFO]: [QuickShop-Hikari] Setting up metrics manager...
[21:11:51] [Server thread/INFO]: [QuickShop-Hikari] Loading player name and unique id mapping...
[21:11:51] [Server thread/INFO]: [QuickShop-Hikari] Loading translations (This may take a while)...
[21:11:51] [Server thread/INFO]: [QuickShop-Hikari] Please wait us fetch the translation updates from Crowdin OTA service...
[21:11:51] [Server thread/INFO]: [CrowdinOTA] Downloading Crowdin distribution manifest from remote server...
[21:11:56] [Server thread/INFO]: [OTAFileInstance-0] Downloading translations for 0 locales...
[21:11:56] [Server thread/INFO]: [OTAFileInstance-1] Downloading translations for 0 locales...
[21:11:56] [Server thread/INFO]: [QuickShop-Hikari] Loading up translations from Crowdin OTA, this may need a while...
[21:11:57] [Server thread/INFO]: [QuickShop-Hikari] Loading up translations from Crowdin OTA, this may need a while...
[21:11:57] [Server thread/INFO]: [QuickShop-Hikari] Register InventoryWrapper...
[21:11:57] [Server thread/INFO]: [QuickShop-Hikari] Initializing NexusManager...
[21:11:57] [Server thread/INFO]: [QuickShop-Hikari] QuickShop Hikari - Early boot step - Complete
[21:11:57] [Server thread/INFO]: [QuickShop-Hikari/Bootstrap] QuickShop-Hikari - Bootstrap -> Complete (7161ms). Waiting for enable...
[21:11:57] [Server thread/INFO]: [MiniMOTD] Loading server plugin MiniMOTD v2.1.2
[21:11:57] [Server thread/INFO]: [TAB] Loading server plugin TAB v5.0.3
[21:11:57] [Server thread/INFO]: Server permissions file permissions.yml is empty, ignoring it
[21:11:57] [Server thread/INFO]: [LuckPerms] Enabling LuckPerms v5.4.131
[21:11:57] [Server thread/INFO]:         __    
[21:11:57] [Server thread/INFO]:   |    |__)   LuckPerms v5.4.131
[21:11:57] [Server thread/INFO]:   |___ |      Running on Bukkit - Paper
[21:11:57] [Server thread/INFO]: 
[21:11:57] [Server thread/INFO]: [LuckPerms] Loading configuration...
[21:11:57] [Server thread/INFO]: [LuckPerms] Loading storage provider... [H2]
[21:11:58] [Server thread/INFO]: [LuckPerms] Loading internal permission managers...
[21:11:58] [Server thread/INFO]: [LuckPerms] Performing initial data load...
[21:11:58] [Server thread/INFO]: [LuckPerms] Successfully enabled. (took 729ms)
[21:11:58] [Server thread/INFO]: [Vault] Enabling Vault v1.7.3-CMI
[21:11:58] [Server thread/INFO]: [Vault] [Economy] Essentials Economy found: Waiting
[21:11:58] [Server thread/INFO]: [Vault] [Permission] SuperPermissions loaded as backup permission system.
[21:11:58] [Server thread/INFO]: [Vault] Enabled Version 1.7.3-CMI
[21:11:58] [Server thread/INFO]: [LuckPerms] Registered Vault permission & chat hook.
[21:11:58] [Server thread/INFO]: [ProtocolLib] Enabling ProtocolLib v5.3.0
[21:11:58] [Server thread/ERROR]: ******************************************************
[21:11:58] [Server thread/ERROR]: *** ProtocolLib does not support plugin reloaders! ***
[21:11:58] [Server thread/ERROR]: *** Please use the built-in reload command!        ***
[21:11:58] [Server thread/ERROR]: ******************************************************
[21:11:58] [Server thread/INFO]: [ProtocolLib] Disabling ProtocolLib v5.3.0
[21:11:58] [Server thread/INFO]: [SkinsRestorer] Enabling SkinsRestorer v15.7.7
[21:11:58] [Server thread/ERROR]: [SkinsRestorer] Your Minecraft version (1.21.8) is not supported by this version of SkinsRestorer! Is there a newer version available? https://modrinth.com/plugin/skinsrestorer If not, join our Discord server!
[21:11:58] [Server thread/INFO]: [SkinsRestorer] Running on Minecraft 1.21.8.
[21:11:58] [Server thread/INFO]: [SkinsRestorer] Floodgate skin listener registered
[21:11:58] [Server thread/INFO]: [SkinsRestorer] Using paper join listener!
[21:11:58] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: skinsrestorer [15.7.7]
[21:11:58] [Server thread/INFO]: [SkinsRestorer] PlaceholderAPI expansion registered!
[21:11:58] [Server thread/WARN]: **** SERVER IS RUNNING IN OFFLINE/INSECURE MODE!
[21:11:58] [Server thread/WARN]: The server will make no attempt to authenticate usernames. Beware.
[21:11:58] [Server thread/WARN]: While this makes the game possible to play without internet access, it also opens up the ability for hackers to connect with any username they choose.
[21:11:58] [Server thread/WARN]: To change this, set "online-mode" to "true" in the server.properties file.
[21:11:58] [Server thread/INFO]: Preparing level "world"
[21:11:59] [Server thread/INFO]: Preparing start region for dimension minecraft:overworld
[21:11:59] [Server thread/INFO]: Preparing spawn area: 0%
[21:11:59] [Folia Async Scheduler Thread #1/INFO]: [SkinsRestorer] ----------------------------------------------
[21:11:59] [Folia Async Scheduler Thread #1/INFO]: [SkinsRestorer]     +==================+
[21:11:59] [Folia Async Scheduler Thread #1/INFO]: [SkinsRestorer]     |   SkinsRestorer  |
[21:11:59] [Folia Async Scheduler Thread #1/INFO]: [SkinsRestorer]     |------------------|
[21:11:59] [Folia Async Scheduler Thread #1/INFO]: [SkinsRestorer]     |  Standalone Mode |
[21:11:59] [Folia Async Scheduler Thread #1/INFO]: [SkinsRestorer]     +==================+
[21:11:59] [Folia Async Scheduler Thread #1/INFO]: [SkinsRestorer] ----------------------------------------------
[21:11:59] [Folia Async Scheduler Thread #1/INFO]: [SkinsRestorer]     Version: 15.7.7
[21:11:59] [Folia Async Scheduler Thread #1/INFO]: [SkinsRestorer]     Commit: 7eff058
[21:11:59] [Folia Async Scheduler Thread #1/INFO]: [SkinsRestorer]     This is the latest version!
[21:11:59] [Folia Async Scheduler Thread #1/INFO]: [SkinsRestorer] ----------------------------------------------
[21:11:59] [Folia Async Scheduler Thread #1/INFO]: [SkinsRestorer] Do you have issues? Read our troubleshooting guide: https://skinsrestorer.net/docs/troubleshooting
[21:11:59] [Folia Async Scheduler Thread #1/INFO]: [SkinsRestorer] Want to support SkinsRestorer? Consider donating: https://skinsrestorer.net/donate
[21:11:59] [Server thread/INFO]: Preparing spawn area: 0%
[21:11:59] [Server thread/INFO]: Time elapsed: 533 ms
[21:11:59] [Server thread/INFO]: Preparing start region for dimension minecraft:the_nether
[21:11:59] [Server thread/INFO]: Preparing spawn area: 0%
[21:12:00] [Server thread/INFO]: Time elapsed: 43 ms
[21:12:00] [Server thread/INFO]: Preparing start region for dimension minecraft:the_end
[21:12:00] [Server thread/INFO]: Preparing spawn area: 0%
[21:12:00] [Server thread/INFO]: Time elapsed: 42 ms
[21:12:00] [Server thread/INFO]: [ViaVersion] Enabling ViaVersion v5.4.2
[21:12:00] [Server thread/INFO]: [ViaVersion] ViaVersion detected server version: 1.21.7-1.21.8 (772)
[21:12:00] [Server thread/INFO]: [Essentials] Enabling Essentials v2.20.1
[21:12:00] [Server thread/ERROR]: [Essentials] You are running an unsupported server version!
[21:12:00] [Server thread/INFO]: [Essentials] Attempting to convert old kits in config.yml to new kits.yml
[21:12:00] [Server thread/INFO]: [Essentials] No kits found to migrate.
[21:12:00] [Server thread/INFO]: [Essentials] Loaded 39094 items from items.json.
[21:12:00] [Server thread/INFO]: [Essentials] Using locale zh_CN
[21:12:00] [Server thread/INFO]: [Essentials] ServerListPingEvent: Spigot iterator API
[21:12:00] [Server thread/INFO]: [Essentials] Starting Metrics. Opt-out using the global bStats config.
[21:12:00] [Server thread/INFO]: [Vault][Economy] Essentials Economy hooked.
[21:12:00] [Server thread/INFO]: [Essentials] Using Vault based permissions (LuckPerms)
[21:12:00] [Server thread/INFO]: [PlaceholderAPI] Enabling PlaceholderAPI v2.11.6
[21:12:00] [Server thread/INFO]: [PlaceholderAPI] Fetching available expansion information...
[21:12:00] [Server thread/INFO]: [floodgate] Enabling floodgate v2.2.4-SNAPSHOT (b118-40d320a)
[21:12:00] [Server thread/INFO]: [EssentialsSpawn] Enabling EssentialsSpawn v2.20.1
[21:12:00] [Server thread/INFO]: [EssentialsSpawn] Starting Metrics. Opt-out using the global bStats config.
[21:12:00] [Server thread/INFO]: [CMILib] Enabling CMILib v*******
[21:12:01] [Server thread/INFO]: Server version: v1_21_R5 - 1.21.8 - paper  1.21.8-11-a5f2f61 (MC: 1.21.8)
[21:12:01] [Server thread/INFO]: Environment: Environment[sessionHost=https://sessionserver.mojang.com, servicesHost=https://api.minecraftservices.com, name=PROD]
[21:12:01] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: cmil [*******]
[21:12:01] [Server thread/INFO]: PlaceholderAPI hooked.
[21:12:01] [Server thread/INFO]: Updated (EN) language file. Took 22ms
[21:12:01] [Server thread/INFO]: [ViaBackwards] Enabling ViaBackwards v5.4.2
[21:12:01] [Server thread/INFO]: [Geyser-Spigot] Enabling Geyser-Spigot v2.8.2-SNAPSHOT
[21:12:01] [Server thread/INFO]: [AuthMe] Enabling AuthMe v5.6.0-SNAPSHOT-b2623
[21:12:01] [Server thread/INFO]: [AuthMe] SQLite Setup finished
[21:12:01] [Server thread/INFO]: [AuthMe] Hooked into LuckPerms!
[21:12:01] [Server thread/INFO]: [AuthMe] Hooked successfully into Essentials
[21:12:01] [Server thread/INFO]: [AuthMe] Essentials spawn file not found: 'C:\Users\<USER>\Downloads\【1.21.4】[mienbbs]Tian服务端\Tian1.21.4服务端\plugins\Essentials\spawn.yml'
[21:12:01] [Server thread/WARN]: [AuthMe] WARNING! The protectInventory feature requires ProtocolLib! Disabling it...
[21:12:01] [Server thread/INFO]: [AuthMe] AuthMe 5.6.0-SNAPSHOT build n.2623 successfully enabled!
[21:12:01] [Server thread/INFO]: [Residence] Enabling Residence v*******
[21:12:02] [Server thread/INFO]: [Residence] Loaded (2) groups
[21:12:02] [Server thread/INFO]: [Residence] Found LuckPerms5 Plugin!
[21:12:02] [Server thread/INFO]: [Residence] Scanning for economy systems...
[21:12:02] [Server thread/INFO]: [Residence] Found Vault using economy: EssentialsX Economy
[21:12:02] [Server thread/INFO]: [Residence] Loaded world data into memory. (0 ms) -> 0 residences
[21:12:02] [Server thread/INFO]: [Residence] Loaded world_the_end data into memory. (0 ms) -> 0 residences
[21:12:02] [Server thread/INFO]: [Residence] Loaded world_nether data into memory. (0 ms) -> 0 residences
[21:12:02] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: residence [*******]
[21:12:02] [Server thread/INFO]: [Residence] PlaceholderAPI was found - Enabling capabilities.
[21:12:02] [Server thread/INFO]: [Residence] Enabled! Version ******* by Zrips
[21:12:02] [Server thread/INFO]: [EssentialsChat] Enabling EssentialsChat v2.20.1
[21:12:02] [Server thread/INFO]: [EssentialsChat] Starting Metrics. Opt-out using the global bStats config.
[21:12:02] [Server thread/INFO]: [Themis] Enabling Themis v0.17.6
[21:12:02] [Server thread/INFO]: [BedrockPlayerSupport] Enabling BedrockPlayerSupport v2.1.0
[21:12:02] [Server thread/INFO]: [QuickShop-Hikari] Enabling QuickShop-Hikari v*******
[21:12:02] [Server thread/INFO]: [QuickShop-Hikari/Bootstrap] QuickShop-Hikari - Bootstrap -> Execute the enable sequence
[21:12:02] [Server thread/INFO]: [QuickShop-Hikari] QuickShop Hikari
[21:12:02] [Server thread/INFO]: [QuickShop-Hikari] Registering Bukkit Service: com.ghostchu.quickshop.api.QuickShopProvider
[21:12:02] [Server thread/INFO]: [QuickShop-Hikari] Starting plugin self-test, please wait...
[21:12:02] [Server thread/INFO]: [QuickShop-Hikari] [OK] Spigot Based Server Test
[21:12:02] [Server thread/INFO]: [QuickShop-Hikari] [OK] Old QuickShop Test
[21:12:02] [Server thread/INFO]: [QuickShop-Hikari] [OK] ModdedServer Based Test
[21:12:02] [Server thread/INFO]: [QuickShop-Hikari] [OK] ModdedServer Database Driver Test
[21:12:02] [Server thread/WARN]: [QuickShop-Hikari] [WARN] CoreSupport Test: QuickShop may not fully support version craftbukkit/1.21.8, Some features may not work.
[21:12:02] [Server thread/WARN]: [QuickShop-Hikari] [WARN] Virtual DisplayItem Support Test: Your server version are not supports Virtual DisplayItem, resetting to RealDisplayItem...
[21:12:02] [Server thread/INFO]: [QuickShop-Hikari] [OK] ProtocolLib Incorrect Locate Test
[21:12:02] [Server thread/WARN]: [QuickShop-Hikari] [WARN] GameVersion supporting Test: Your Minecraft server version not tested by developers, QuickShop may ran into issues on this version.
[21:12:02] [Server thread/INFO]: [QuickShop-Hikari] [OK] Permission Manager Test
[21:12:02] [Server thread/INFO]: [QuickShop-Hikari] [OK] PacketListenerAPI Conflict Test
[21:12:02] [Server thread/INFO]: [QuickShop-Hikari] [OK] Reremake Test
[21:12:02] [Server thread/INFO]: [QuickShop-Hikari] [OK] EcoEnchants V11 Check
[21:12:02] [Server thread/INFO]: [QuickShop-Hikari] [OK] End of life Test
[21:12:02] [Server thread/INFO]: [QuickShop-Hikari] Reading the configuration...
[21:12:02] [Server thread/INFO]: [QuickShop-Hikari] [ConfigUpdater] Saving configuration changes...
[21:12:02] [Server thread/INFO]: [QuickShop-Hikari] Contributors: Ghost_chu, PotatoCraft Studio, Netherfoam, Timtower, KaiNoMood (KaiKikuchi), sandtechnology, jho5245, cakoyo, Andre601, Ectabro, Chris6ix, portlek, log4b0at, deadman96385, tiararinne, DoctaEnkoda, CarmJos, YuanYuanOwO, Mgazul, mart-r, Tim269, raphtaliapt, creatorfromhell, LoneDev6, Steven-OS, confuxeon, ibmibmibm, judgetread, mfnalex, Warriorrrr, PyvesB, yannicklamprecht, ORelio, RMSCA, Starmism, yiwenwang2090, PaulBGD, Nlkomaru, harry0198, Draesia, Localized community members on Crowdin
[21:12:02] [Server thread/INFO]: [QuickShop-Hikari] Original author: Netherfoam, Timtower, KaiNoMood, sandtechnology
[21:12:02] [Server thread/INFO]: [QuickShop-Hikari] Let's start loading the plugin
[21:12:02] [Server thread/INFO]: [QuickShop-Hikari] Setting up ItemExpressionRegistry...
[21:12:02] [Server thread/INFO]: [QuickShop-Hikari] Setting up database...
[21:12:02] [Server thread/INFO]: [QuickShop-Hikari] Create database backup...
[21:12:02] [Server thread/INFO]: [cc.carm.lib.easysql.hikari.HikariDataSource] HikariPool-1 - Starting...
[21:12:02] [Server thread/INFO]: [cc.carm.lib.easysql.hikari.HikariDataSource] HikariPool-1 - Start completed.
[21:12:02] [Server thread/INFO]: [QuickShop-Hikari] Checking and updating database columns, it may take a while...
[21:12:02] [Server thread/INFO]: [QuickShop-Hikari] Finished!
[21:12:02] [Server thread/INFO]: [QuickShop-Hikari] Selected permission provider: Bukkit
[21:12:02] [Server thread/INFO]: [QuickShop-Hikari] Registering commands...
[21:12:02] [Server thread/INFO]: [QuickShop-Hikari] Loaded 1 rules for listener blacklist.
[21:12:02] [Server thread/INFO]: [QuickShop-Hikari] EventManager selected: QSEventManager
[21:12:02] [Server thread/INFO]: [QuickShop-Hikari] Loading shops from database...
[21:12:02] [Server thread/INFO]: [QuickShop-Hikari] Used 2ms to fetch 0 shops from database.
[21:12:02] [Server thread/INFO]: [QuickShop-Hikari] Loading shops into memory...
[21:12:02] [Server thread/INFO]: [QuickShop-Hikari] Used 0ms to load 0 shops into memory (0 shops will be loaded after chunks/world loaded).
[21:12:02] [Server thread/INFO]: [QuickShop-Hikari] Registering listeners...
[21:12:02] [Server thread/INFO]: [QuickShop-Hikari] Registering DisplayCheck task....
[21:12:02] [Server thread/INFO]: [QuickShop-Hikari] Cleaning MsgUtils...
[21:12:02] [Server thread/INFO]: [QuickShop-Hikari] Cleaning purchase messages from the database that are over a week old...
[21:12:02] [Server thread/INFO]: [QuickShop-Hikari] Log actions is enabled. Actions will be logged in the qs.log file!
[21:12:02] [Server thread/INFO]: [QuickShop-Hikari] [Shop Purger] Purge not enabled!
[21:12:02] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: qs [*******]
[21:12:02] [Server thread/INFO]: [QuickShop-Hikari] Successfully loaded PlaceHolderAPI support!
[21:12:02] [Server thread/WARN]: [QuickShop-Hikari] [WARN] Virtual DisplayItem Support Test: VirtualDisplayItemManager is null, this shouldn't happen, contact with QuickShop-Hikari developer.
[21:12:02] [Server thread/INFO]: [QuickShop-Hikari/Bootstrap] QuickShop-Hikari - Bootstrap -> All Complete. (330ms)
[21:12:02] [Server thread/INFO]: [MiniMOTD] Enabling MiniMOTD v2.1.2
[21:12:03] [Server thread/INFO]: [TAB] Enabling TAB v5.0.3
[21:12:03] [Folia Async Scheduler Thread #3/INFO]: [BedrockPlayerSupport] 插件是最新版本, 继续保持 ~
[21:12:03] [Server thread/INFO]: [TAB] Loaded NMS hook in 23ms
[21:12:03] [Server thread/INFO]: [TAB] Enabled in 120ms
[21:12:03] [Server thread/INFO]: [spark] Starting background profiler...
[21:12:03] [Server thread/INFO]: [spark] The async-profiler engine is not supported for your os/arch (windows11/amd64), so the built-in Java engine will be used instead.
[21:12:03] [Server thread/INFO]: [PlaceholderAPI] Placeholder expansion registration initializing...
[21:12:03] [Server thread/INFO]: 0 placeholder hook(s) registered!
[21:12:03] [Server thread/INFO]: [Geyser-Spigot] ******************************************
[21:12:03] [Server thread/INFO]: [Geyser-Spigot] 
[21:12:03] [Server thread/INFO]: [Geyser-Spigot] 正在加载 Geyser 版本 2.8.2-b891 (git-master-ca99ab7)
[21:12:03] [Server thread/INFO]: [Geyser-Spigot] 
[21:12:03] [Server thread/INFO]: [Geyser-Spigot] ******************************************
[21:12:05] [pool-18-thread-1/WARN]: [floodgate] 
**********************************
* You specified an empty prefix in your Floodgate config for Bedrock players!
* Should a Java player join and a Bedrock player join with the same username, unwanted results and conflicts will happen!
* We strongly recommend using . as the prefix, but other alternatives that will not conflict include: +, - and *
**********************************
[21:12:05] [Server thread/INFO]: [Geyser-Spigot] 已在 UDP 端口 19132 上启动 Geyser
[21:12:05] [Server thread/INFO]: [Geyser-Spigot] 完成 (2.025秒)！ 运行 /geyser help 以获取帮助！
[21:12:06] [Server thread/INFO]: Done preparing level "world" (7.032s)
[21:12:06] [Server thread/INFO]: Running delayed init tasks
[21:12:06] [Server thread/INFO]: [Essentials] Essentials found a compatible payment resolution method: Vault Compatibility Layer (v1.7.3-CMI)!
[21:12:06] [Craft Scheduler Thread - 2 - ViaVersion/INFO]: [ViaVersion] Finished mapping loading, shutting down loader executor.
[21:12:06] [Craft Scheduler Thread - 0 - Essentials/INFO]: [Essentials] 正在获取版本信息...
[21:12:06] [Craft Scheduler Thread - 7 - AuthMe/INFO]: [AuthMe] Downloading GEO IP database, because the old database is older than 30 days or doesn't exist
[21:12:06] [Craft Scheduler Thread - 12 - QuickShop-Hikari/INFO]: [QuickShop-Hikari] Start to caching usernames (async)...
[21:12:06] [Craft Scheduler Thread - 7 - AuthMe/WARN]: [AuthMe] No MaxMind credentials found in the configuration file! GeoIp protections will be disabled.
[21:12:06] [Craft Scheduler Thread - 7 - AuthMe/INFO]: [AuthMe] There is no newer GEO IP database uploaded to MaxMind. Using the old one for now.
[21:12:06] [Craft Scheduler Thread - 7 - AuthMe/WARN]: [AuthMe] Could not download GeoLiteAPI database [FileNotFoundException]: plugins\AuthMe\GeoLite2-Country.mmdb (系统找不到指定的文件。)
[21:12:06] [Server thread/INFO]: [QuickShop-Hikari] Using economy system: EssentialsX Economy
[21:12:06] [Server thread/INFO]: [QuickShop-Hikari] Selected economy bridge: BuiltIn-Vault
[21:12:06] [Server thread/INFO]: Done (26.282s)! For help, type "help"
[21:12:06] [Craft Scheduler Thread - 9 - CMILib/INFO]: _________________/ Residence \_________________
| ******* is now available! Your version: *******
----------------------------------------
[21:12:06] [Server thread/INFO]: Downloaded Locale_BR.yml file
[21:12:06] [Craft Scheduler Thread - 0 - Essentials/WARN]: [Essentials] 已有新的EssentialsX版本可供下载：2.21.1。
[21:12:06] [Craft Scheduler Thread - 0 - Essentials/WARN]: [Essentials] 在此处下载：https://essentialsx.net/downloads.html?branch=stable
[21:12:06] [Server thread/INFO]: Updated (EN) language file. Took 8ms
