<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.slf4j</groupId>
    <artifactId>slf4j-parent</artifactId>
    <version>2.0.10</version>
    <relativePath>../parent/pom.xml</relativePath>
  </parent>

  <artifactId>slf4j-jdk14</artifactId>
  <packaging>jar</packaging>
  <name>SLF4J JDK14 Provider</name>
  <description>SLF4J JDK14 Provider</description>
  <url>http://www.slf4j.org</url>

  <properties>
    <module-name>org.slf4j.jul</module-name>
    <slf4j.provider.implementation>org.slf4j.jul.JULServiceProvider</slf4j.provider.implementation>
    <slf4j.provider.type>jul</slf4j.provider.type>
  </properties>

  <dependencies>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
    </dependency>

    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
      <type>test-jar</type>
      <version>${project.version}</version>
      <scope>test</scope>
    </dependency>
  </dependencies>

</project>
