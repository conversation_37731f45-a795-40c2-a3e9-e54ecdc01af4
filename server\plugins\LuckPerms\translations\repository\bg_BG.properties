luckperms.logs.actionlog-prefix=ЛОГ
luckperms.logs.verbose-prefix=VB
luckperms.logs.export-prefix=ИЗНОС
luckperms.commandsystem.available-commands=Използвайте {0}, за да видите наличните команди
luckperms.commandsystem.command-not-recognised=Непозната команда
luckperms.commandsystem.no-permission=Нямате разрешение да използвате тази команда\!
luckperms.commandsystem.no-permission-subcommands=Нямате разрешение да използвате която и да е под команда
luckperms.commandsystem.already-executing-command=Изпълнява се друга команда, изчакване докато приключи...
luckperms.commandsystem.usage.sub-commands-header=Под Команди
luckperms.commandsystem.usage.usage-header=Правилно използване на командата
luckperms.commandsystem.usage.arguments-header=Аргументи
luckperms.first-time.no-permissions-setup=Изглежда, че никакви права не са настроени\!
luckperms.first-time.use-console-to-give-access=Преди да използвате, която и да е команда на LuckPerms в играта, трябва да изпозвате конзолата, за да си дадете правомощия
luckperms.first-time.console-command-prompt=Отворете конзолата и изпълнете
luckperms.first-time.next-step=След като направите това, можете да започнете да дефинирате права и групи
luckperms.first-time.wiki-prompt=Не знаете от къде да започнете? Вижте тук\: {0}
luckperms.login.try-again=Моля, опитайте отново по-късно
luckperms.login.loading-database-error=Настъпи грешка в базата данни при зареждане на информация за права
luckperms.login.server-admin-check-console-errors=Ако сте сървърен администратор, моля проверете конзолата за грешки
luckperms.login.server-admin-check-console-info=Моля, проверете конзолата за повече информация
luckperms.login.data-not-loaded-at-pre=Данните за права за Вас не бяха заредени по време на етапа преди вход
luckperms.login.unable-to-continue=невъзможно продължаване
luckperms.login.craftbukkit-offline-mode-error=това е вероятно поради конфликт между CraftBukkit и настройката online-mode
luckperms.login.unexpected-error=Възникна неочаквана грешка при настройването на данните за правата Ви
luckperms.opsystem.disabled=Вградената OP система е изключена на този сървър
luckperms.opsystem.sponge-warning=Обърнете внимание, че статуса на Сървърен Оператор няма ефект на проверките на Sponge за права, когато плъгин за права е инсталиран, Вие трябва да редактирате потребителските данни ръчно
luckperms.duration.unit.years.plural={0} години
luckperms.duration.unit.years.singular={0} година
luckperms.duration.unit.years.short={0}г
luckperms.duration.unit.months.plural={0} Месеца
luckperms.duration.unit.months.singular={0} месец
luckperms.duration.unit.months.short={0}м
luckperms.duration.unit.weeks.plural={0} седмици
luckperms.duration.unit.weeks.singular={0} седмица
luckperms.duration.unit.weeks.short={0}с
luckperms.duration.unit.days.plural={0} дни
luckperms.duration.unit.days.singular={0} ден
luckperms.duration.unit.days.short={0}д
luckperms.duration.unit.hours.plural={0} часа
luckperms.duration.unit.hours.singular={0} час
luckperms.duration.unit.hours.short={0}ч
luckperms.duration.unit.minutes.plural={0} минути
luckperms.duration.unit.minutes.singular={0} минута
luckperms.duration.unit.minutes.short={0}мин
luckperms.duration.unit.seconds.plural={0} секунди
luckperms.duration.unit.seconds.singular={0} секунда
luckperms.duration.unit.seconds.short={0}с
luckperms.duration.since=преди {0}
luckperms.command.misc.invalid-code=Невалиден код
luckperms.command.misc.response-code-key=код на отговор
luckperms.command.misc.error-message-key=съобщение
luckperms.command.misc.bytebin-unable-to-communicate=Не можахме да комуникираме с bytebin
luckperms.command.misc.webapp-unable-to-communicate=Не можахме да комуникираме с уеб апликацията
luckperms.command.misc.check-console-for-errors=Проверете конзолата за грешки
luckperms.command.misc.file-must-be-in-data=Файлът {0} трябва да се намира в главната папка с файлове
luckperms.command.misc.wait-to-finish=Моля изчакайте да свърши и опитайте отново
luckperms.command.misc.invalid-priority=Невалиден приоритет {0}
luckperms.command.misc.expected-number=Очаквахме число
luckperms.command.misc.date-parse-error=Не може да се анализира датата {0}
luckperms.command.misc.date-in-past-error=Не може да сложите дата в миналото\!
luckperms.command.misc.page=страница {0} от {1}
luckperms.command.misc.page-entries={0} записа
luckperms.command.misc.none=Няма
luckperms.command.misc.loading.error.unexpected=Възникна неочаквана грешка
luckperms.command.misc.loading.error.user=Информацията на потребителя не е заредена
luckperms.command.misc.loading.error.user-specific=Не можахме да заредим информацията за потребителя {0}
luckperms.command.misc.loading.error.user-not-found=Потребителят {0} не може да бъде намерен
luckperms.command.misc.loading.error.user-save-error=Имаше проблем при запазването на информацията за потребителя {0}
luckperms.command.misc.loading.error.user-not-online=Потребителят {0} не е на линия
luckperms.command.misc.loading.error.user-invalid={0} не е валидно потребителско име или uuid
luckperms.command.misc.loading.error.user-not-uuid=Целевото uuid {0} е невалидно
luckperms.command.misc.loading.error.group=Групата не е заредена
luckperms.command.misc.loading.error.all-groups=Не можахме да заредим всички групи
luckperms.command.misc.loading.error.group-not-found=Група с името {0} не може да бъде намерена
luckperms.command.misc.loading.error.group-save-error=Имаше проблем при запазването на информацията за групата {0}
luckperms.command.misc.loading.error.group-invalid={0} не е валидно име на група
luckperms.command.misc.loading.error.track=Трака не е зареден
luckperms.command.misc.loading.error.all-tracks=Не може да се заредят всички тракове
luckperms.command.misc.loading.error.track-not-found=Трак с името {0} не може да бъде намерен
luckperms.command.misc.loading.error.track-save-error=Имаше проблем при запазването на информацията за трака {0}
luckperms.command.misc.loading.error.track-invalid={0} не е валидно име на трак
luckperms.command.editor.no-match=Не можахме да отворим редактора, никакви обекти не съвпаднаха с желаният тип
luckperms.command.editor.start=Приготвяме нова сесия на редактора, моля изчакайте...
luckperms.command.editor.url=Натиснете на връзката долу, за да отворите редактора
luckperms.command.editor.unable-to-communicate=Не можахме да комуникираме с редактора
luckperms.command.editor.apply-edits.success=Информацията от уеб редакторът за {0} {1} беше успешно приложена
luckperms.command.editor.apply-edits.success-summary={0} {1} и {2} {3}
luckperms.command.editor.apply-edits.success.additions=допълнения
luckperms.command.editor.apply-edits.success.additions-singular=допълнение
luckperms.command.editor.apply-edits.success.deletions=изтривания
luckperms.command.editor.apply-edits.success.deletions-singular=изтриване
luckperms.command.editor.apply-edits.no-changes=Не бяха приложени никакви промени от уеб редактора, дадената информация нямаше никакви редакции
luckperms.command.editor.apply-edits.unknown-type=Не можахме да приложим редакциите върху посочения обект
luckperms.command.editor.apply-edits.unable-to-read=Не можахме да прочетем информацията, използвайки даденият код
luckperms.command.search.searching.permission=Търсене на потребители и групи с {0}
luckperms.command.search.searching.inherit=Търсене на потребители и групи, които наследяват {0}
luckperms.command.search.result=Намерихме {0} записа от {1} потребители и {2} групи
luckperms.command.search.result.default-notice=Бележка\: когато търсите за членове на групата по подразбиране, потребителите, които не са на линия и нямат други права няма да бъдат показани\!
luckperms.command.search.showing-users=Показваме потребителските записи
luckperms.command.search.showing-groups=Показваме груповите записи
luckperms.command.tree.start=Генерираме дърво на правата, моля изчакайте...
luckperms.command.tree.empty=Не можахме да генерираме дърво, резултати не бяха намерени
luckperms.command.tree.url=URL адресът на дървото за права
luckperms.command.verbose.invalid-filter={0} не е валиден подробен филтър
luckperms.command.verbose.enabled=Подробно изписване на {0} за проверки, които изпълват критерии {1}
luckperms.command.verbose.command-exec=Караме {0} да изпълни командата {1} и показваме всички направени проверки...
luckperms.command.verbose.off=Подробно изписваме {0}
luckperms.command.verbose.command-exec-complete=Успешно изпълнявне на командата
luckperms.command.verbose.command.no-checks=Изпълнението на командата беше завършено, но не бяха извършвани проверки за права
luckperms.command.verbose.command.possibly-async=Това може да е, защото плъгина изпълнява команди на заден план (асинхронизирано)
luckperms.command.verbose.command.try-again-manually=Все още можете да използвате verbose ръчно, за да засечете проверки, правени по този начин
luckperms.command.verbose.enabled-recording=Подробно записване на {0} за проверки, които изпълват критерии {1}
luckperms.command.verbose.uploading=Допълнително регистриране на информация {0}, качване на резултати...
luckperms.command.verbose.url=Допълнителен резултат URL
luckperms.command.verbose.enabled-term=включено
luckperms.command.verbose.disabled-term=изключено
luckperms.command.verbose.query-any=ВСЯКАКВО
luckperms.command.info.running-plugin=Изпълнява
luckperms.command.info.platform-key=Платформа
luckperms.command.info.server-brand-key=Марка на сървъра
luckperms.command.info.server-version-key=Версия на сървъра
luckperms.command.info.storage-key=Хранилище
luckperms.command.info.storage-type-key=Вид
luckperms.command.info.storage.meta.split-types-key=Видове
luckperms.command.info.storage.meta.ping-key=Латентност
luckperms.command.info.storage.meta.connected-key=Свързан
luckperms.command.info.storage.meta.file-size-key=Големина на файла
luckperms.command.info.extensions-key=Добавки
luckperms.command.info.messaging-key=Услуга за съобщения
luckperms.command.info.instance-key=Инстанция
luckperms.command.info.static-contexts-key=Статични контексти
luckperms.command.info.online-players-key=Онлайн Играчи
luckperms.command.info.online-players-unique={0} уникални
luckperms.command.info.uptime-key=Време на работа
luckperms.command.info.local-data-key=Локална Информация
luckperms.command.info.local-data={0} потребителя, {1} групи, {2} пътеки
luckperms.command.generic.create.success={0} беше създаден/а успешно
luckperms.command.generic.create.error=Възникна грешка докато създавахме {0}
luckperms.command.generic.create.error-already-exists={0} вече съществува\!
luckperms.command.generic.delete.success={0} беше изтрит/а успешно
luckperms.command.generic.delete.error=Възникна грешка при изтриването на {0}
luckperms.command.generic.delete.error-doesnt-exist={0} не съществува\!
luckperms.command.generic.rename.success={0} беше успешно преименувано до {1}
luckperms.command.generic.clone.success={0} беше успешно клонирано на {1}
luckperms.command.generic.info.parent.title=Групи родители
luckperms.command.generic.info.parent.temporary-title=Временни групи родители
luckperms.command.generic.info.expires-in=изтича след
luckperms.command.generic.info.inherited-from=наследено от
luckperms.command.generic.info.inherited-from-self=себе си
luckperms.command.generic.show-tracks.title=Траковете на {0}
luckperms.command.generic.show-tracks.empty={0} не е на никакви тракове
luckperms.command.generic.clear.node-removed={0} ноуда бяха премахнати
luckperms.command.generic.clear.node-removed-singular={0} ноуд беше премахнат
luckperms.command.generic.clear=Ноудовете на {0} бяха премахнати в контекст {1}
luckperms.command.generic.permission.info.title=Правата на {0}
luckperms.command.generic.permission.info.empty={0} няма никакви права
luckperms.command.generic.permission.info.click-to-remove=Натиснете за да премахнете този ноуд от {0}
luckperms.command.generic.permission.check.info.title=Информация за правото {0}
luckperms.command.generic.permission.check.info.directly={0} има {1} настроено на {2} в контекст {3}
luckperms.command.generic.permission.check.info.inherited={0} наследява правото {1} настроено на {2} от група {3} в контекст {4}
luckperms.command.generic.permission.check.info.not-directly={0} няма правото {1}
luckperms.command.generic.permission.check.info.not-inherited={0} не наследява {1}
luckperms.command.generic.permission.check.result.title=Проверка на право {0}
luckperms.command.generic.permission.check.result.result-key=Резултат
luckperms.command.generic.permission.check.result.processor-key=Процесор
luckperms.command.generic.permission.check.result.cause-key=Причина
luckperms.command.generic.permission.check.result.context-key=Контекст
luckperms.command.generic.permission.set=Зададен {0} на {1} за {2} в контекст {3}
luckperms.command.generic.permission.already-has={0} вече има зададено {1} в контекст {2}
luckperms.command.generic.permission.set-temp=Задай {0} на {1} за {2} с времетраене от {3} в контекст {4}
luckperms.command.generic.permission.already-has-temp={0} вече има временно зададен {1} в контекст {2}
luckperms.command.generic.permission.unset=Пермахнат {0} за {1} в контекст {2}
luckperms.command.generic.permission.doesnt-have={0} няма задеден {1} в контекст {2}
luckperms.command.generic.permission.unset-temp=Премахване на временен метаключ за права {0} за {1} в контекст {2}
luckperms.command.generic.permission.subtract=Задай {0} към {1} за {2} за продължение {3} в контекст {4}, {5} по-малко от преди
luckperms.command.generic.permission.doesnt-have-temp={0} няма временно задеден {1} в контекст {2}
luckperms.command.generic.permission.clear=Правата на {0} бяха премахнати в контекст {1}
luckperms.command.generic.parent.info.title=Родителите на {0}
luckperms.command.generic.parent.info.empty=За {0} не са дефинирани родители
luckperms.command.generic.parent.info.click-to-remove=Натиснете, за да премахнете този родител от {0}
luckperms.command.generic.parent.add={0} вече наследява права от {1} в контекст {2}
luckperms.command.generic.parent.add-temp={0} вече наследява права от {1} за време {2} в контекст {3}
luckperms.command.generic.parent.set=На {0} бяха премахнати групите родители, и вече наследява само {1} в контекст {2}
luckperms.command.generic.parent.remove={0} вече не наследява права от {1} в контекст {2}
luckperms.command.generic.parent.remove-temp={0} вече не наследява временно права от {1} в контекст {2}
luckperms.command.generic.parent.subtract={0} ще наследява права от {1} за време {2} в контекст {3}, по-малко от преди {4}
luckperms.command.generic.parent.clear=Родителите на {0} бяха премахнати в контекст {1}
luckperms.command.generic.parent.already-inherits={0} вече има зададено наследяване от {1} в контекст {2}
luckperms.command.generic.parent.doesnt-inherit={0} няма задедено наследяване от {1} в контекст {2}
luckperms.command.generic.parent.already-temp-inherits={0} вече наследява временно права от {1} в контекст {2}
luckperms.command.generic.parent.doesnt-temp-inherit={0} вече не наследява временно от {1} в контекст {2}
luckperms.command.generic.chat-meta.info.title-prefix=Префиксите на {0}
luckperms.command.generic.chat-meta.info.title-suffix=Съфиксите на {0}
luckperms.command.generic.chat-meta.info.none-prefix={0} няма никакви префикси
luckperms.command.generic.chat-meta.info.none-suffix={0} няма никакви съфикси
luckperms.command.generic.chat-meta.info.click-to-remove=Натиснете, за да премахнете {0} от {1}
luckperms.command.generic.meta.info.title=Метаданни на {0}
luckperms.command.generic.meta.info.none={0} няма метаданни
luckperms.command.generic.meta.info.click-to-remove=Натиснете, за да премахнете метавъзела от {0}
luckperms.command.generic.meta.already-has={0} вече има метаключа {1}, задеден на {2} в контекст {3}
luckperms.command.generic.meta.already-has-temp={0} вече има метаключа {1}, задеден временно на {2} в контекст {3}
luckperms.command.generic.meta.doesnt-have={0} няма зададен метаключ {1} в контекст {2}
luckperms.command.generic.meta.doesnt-have-temp={0} няма временно зададен метаключ {1} в контекст {2}
luckperms.command.generic.meta.set=Задаване на метаключ {0} на {1} за {2} в контекст {3}
luckperms.command.generic.meta.set-temp=Задаване на метаключ {0} на {1} за {2} за продължителност от {3} в контекст {4}
luckperms.command.generic.meta.unset=Премахване на метаключ {0} за {1} в контекст {2}
luckperms.command.generic.meta.unset-temp=Премахване на временен метаключ {0} за {1} в контекст {2}
luckperms.command.generic.meta.clear=Съвшадащият метатип {1} на {0} бе премахнат в контекст {2}
luckperms.command.generic.contextual-data.title=Контекстуална информация
luckperms.command.generic.contextual-data.mode.key=режим
luckperms.command.generic.contextual-data.mode.server=сървър
luckperms.command.generic.contextual-data.mode.active-player=активен играч
luckperms.command.generic.contextual-data.contexts-key=Контексти
luckperms.command.generic.contextual-data.prefix-key=Префикс
luckperms.command.generic.contextual-data.suffix-key=Съфикс
luckperms.command.generic.contextual-data.primary-group-key=Основна група
luckperms.command.generic.contextual-data.meta-key=Метаданни
luckperms.command.generic.contextual-data.null-result=Няма
luckperms.command.user.info.title=Информация за потребител
luckperms.command.user.info.uuid-key=UUID
luckperms.command.user.info.uuid-type-key=вид
luckperms.command.user.info.uuid-type.mojang=mojang
luckperms.command.user.info.uuid-type.not-mojang=офлайн
luckperms.command.user.info.status-key=Статус
luckperms.command.user.info.status.online=Онлайн
luckperms.command.user.info.status.offline=Офлайн
luckperms.command.user.removegroup.error-primary=Вие не можете да премахнете потребител от тяхната основна група
luckperms.command.user.primarygroup.not-member={0} не беше член на {1}, добавяме го сега
luckperms.command.user.primarygroup.already-has={1} вече е основната група на {0}
luckperms.command.user.primarygroup.warn-option=Внимание\: Изчислителният метод на основната група, използван от сървъра ({0}), може да не отрази тази промяна
luckperms.command.user.primarygroup.set=Основната група на {0} беше настроена на {1}
luckperms.command.user.track.error-not-contain-group={0} все още не е в нито една група на трасе {1}
luckperms.command.user.track.unsure-which-track=Не е ясно кое трасе трябва да бъде използвано, моля отбележето го чрез аргумент
luckperms.command.user.track.missing-group-advice=Или създайте групата, или я премахнете от трасето и опитайте отново
luckperms.command.user.promote.added-to-first={0} все още не е в нито една група на трасе {1}, така че бе добавен към първата такава - {2}, в контекст {3}
luckperms.command.user.promote.not-on-track={0} не е в нито една група на трасе {1} и не беше повишен
luckperms.command.user.promote.success=Повишаване на {0} по трасе {1} от {2} на {3} в контекст {4}
luckperms.command.user.promote.end-of-track={1} достигна края на трасе {0} и не бе повишен
luckperms.command.user.promote.next-group-deleted=Следващата група по трасето - {0} вече не съществува
luckperms.command.user.promote.unable-to-promote=Не можахме да повишим потребителя
luckperms.command.user.demote.success=Понижаване на {0} по трасе {1} от {2} на {3} в контекст {4}
luckperms.command.user.demote.end-of-track=Краят на трасе {0} бе достигнат, в резултат на което {1} бе премахнат от {2}
luckperms.command.user.demote.end-of-track-not-removed=Краят на трасе {0} бе достигнат, но {1} не бе премахнат от първата група
luckperms.command.user.demote.previous-group-deleted=Предната група по трасето - {0} вече не съществува
luckperms.command.user.demote.unable-to-demote=Не можахме да понижим потребителя
luckperms.command.group.list.title=Групи
luckperms.command.group.delete.not-default=Не можете да изтриете групата по подразбиране
luckperms.command.group.info.title=Информация за групата
luckperms.command.group.info.display-name-key=Дисплей име
luckperms.command.group.info.weight-key=Тежест
luckperms.command.group.setweight.set=Тежестта беше променена на {0} за група {1}
luckperms.command.group.setdisplayname.doesnt-have={0} няма настроено показвано име
luckperms.command.group.setdisplayname.already-has={0} вече има показвано име\: {1}
luckperms.command.group.setdisplayname.already-in-use=Дисплей името {0} вече се използва от {1}
luckperms.command.group.setdisplayname.set=Показваното име за група {1} беше настроено на {0} в контекст {2}
luckperms.command.group.setdisplayname.removed=Беше премахнато показвано име за група {0} в контекст {1}
luckperms.command.track.list.title=Трасета
luckperms.command.track.path.empty=Няма
luckperms.command.track.info.showing-track=Показва се трасе
luckperms.command.track.info.path-property=Път
luckperms.command.track.clear=Трасето с групи на {0} бе изчистено
luckperms.command.track.insert.error-number=Очаквахме число, но получихме\: {0}
luckperms.command.track.insert.error-invalid-pos=Не можахме да извършим вмъкване на позиция {0}
luckperms.command.track.insert.error-invalid-pos-reason=невалидна позиция
luckperms.command.track.error-empty={0} не може да се използва понеже е празно или съдържа само една група
luckperms.command.track.error-ambiguous=Не можахме да определим тяхното местоположение
luckperms.command.track.already-contains={0} вече съдържа {1}
luckperms.command.track.doesnt-contain={0} не съдържа {1}
luckperms.command.log.load-error=Логовете не могат да бъдат заредени
luckperms.command.log.invalid-page=Невалиден номер на страница
luckperms.command.log.empty=Няма логове за показване
luckperms.command.log.notify.error-console=Не могат да се превключват известията за конзолата
luckperms.command.log.notify.enabled-term=Включено
luckperms.command.log.notify.disabled-term=Изключено
luckperms.command.log.notify.changed-state=Логове на {0}
luckperms.command.log.notify.already-on=Вие вече получавате известия
luckperms.command.log.notify.already-off=Към момента Вие не получавате известия
luckperms.command.log.notify.invalid-state=Невалидно състояние. Очаквахме {0} или {1}
luckperms.command.log.show.search=Показваме скорошните действия за заявката {0}
luckperms.command.log.show.recent=Показваме скорошните действия
luckperms.command.log.show.by=Показваме скорошните действия на {0}
luckperms.command.export.error-term=Грешка
luckperms.command.export.already-running=Друг експортиращ процес вече се изпълнява
luckperms.command.export.file.already-exists=Файлът с име {0} вече съществува
luckperms.command.export.file.not-writable=Не можем да записваме на файла {0}
luckperms.command.export.file.success=Успешно експортиране до {0}
luckperms.command.export.file-unexpected-error-writing=Възникна неочаквана грешка при записване на файла
luckperms.command.export.web.export-code=Износен код
luckperms.command.export.web.import-command-description=Използвай следната команда, за да вмъкнеш промените
luckperms.command.import.term=Вмъкване
luckperms.command.import.error-term=Грешка
luckperms.command.import.already-running=Друг вмъкващ процес вече се изпълнява
luckperms.command.import.file.doesnt-exist=Файлът {0} не съществува
luckperms.command.import.file.not-readable=Файлът {0} не може да бъде разчетен
luckperms.command.import.file.unexpected-error-reading=Възникна неочаквана грешка докато разчитахме вмъкнатия файл
luckperms.command.import.file.correct-format=в правилния формат ли е?
luckperms.command.import.web.unable-to-read=Не можахме да прочетем информацията, използвайки даденият код
luckperms.command.import.progress.percent={0}% завършено
luckperms.command.import.progress.operations={0}/{1} операции завършени
luckperms.command.import.starting=Стартиране на вмъкващ процес
luckperms.command.import.completed=ЗАВЪРШЕН
luckperms.command.import.duration=отне {0} секунди
luckperms.command.bulkupdate.must-use-console=Командата за масивно актуализиране може да бъде използвана само от конзолата
luckperms.command.bulkupdate.invalid-data-type=Невалиден вид, очаквахме {0}
luckperms.command.bulkupdate.invalid-constraint=Невалидно ограничение {0}
luckperms.command.bulkupdate.invalid-constraint-format=Ограниченията трябва да са във формат {0}
luckperms.command.bulkupdate.invalid-comparison=Невалиден оператор за сравнение {0}
luckperms.command.bulkupdate.invalid-comparison-format=Очаквахме едно от следните\: {0}
luckperms.command.bulkupdate.queued=Операцията за масивно актуализиране е на опашка
luckperms.command.bulkupdate.confirm=Изпълнете {0}, за да извършите актуализацията
luckperms.command.bulkupdate.unknown-id=Операцията с идентификация {0} не съществува или изтече
luckperms.command.bulkupdate.starting=Стартиране на масивна актуализация
luckperms.command.bulkupdate.success=Масивната актуализация беше успешно завършена
luckperms.command.bulkupdate.success.statistics.users=Брой засегнати потребители
luckperms.command.bulkupdate.success.statistics.groups=Брой засегнати групи
luckperms.command.bulkupdate.failure=Не можахме да изпълним груповата актуализация, моля проверете конзолата за грешки
luckperms.command.update-task.request=Заявката за актуализация е изпратена, моля изчакайте
luckperms.command.update-task.complete=Актуализацията приключи
luckperms.command.update-task.push.attempting=Вмомента се опитваме да актуализираме другите сървъри
luckperms.command.update-task.push.error=Грешка докато се опитвахме да актуализираме промените в другите сървъри
luckperms.command.update-task.push.error-not-setup=Не можахме да актуализираме промените в другите сървъри понеже услугата за съобщения не е конфигурирана
luckperms.command.reload-config.success=Конфигурационният файл беше презареден
luckperms.command.reload-config.restart-note=някои опции ще бъдат приложение само след рестарт на сървъра
luckperms.command.translations.searching=Търсим за налични преводи, моля изчакайте...
luckperms.command.translations.searching-error=Не можахме да получим лист с налични преводи
luckperms.command.translations.installed-translations=Инсталирани преводи
luckperms.command.translations.available-translations=Налични преводи
luckperms.command.translations.percent-translated={0}% преведено
luckperms.command.translations.translations-by=от
luckperms.command.translations.installing=Инсталиране на превод, моля изчакайте...
luckperms.command.translations.download-error=Не можахме да изтеглим превод за {0}
luckperms.command.translations.installing-specific=Инсталираме език {0}...
luckperms.command.translations.install-complete=Инсталирането завърши
luckperms.command.translations.download-prompt=Използвай {0} за да изтеглиш и инсталираш най-новите версии на тези преводи, предоставени от общността
luckperms.command.translations.download-override-warning=Имайте предвид, че това ще отмени направените от вас промени за тези езици
luckperms.usage.user.description=Набор от команди за управляване на потребители в LuckPerms. (''Потребител'' се използва в смисъл на играч, и може да се отнася до UUID или потребителско име)
luckperms.usage.group.description=Набор от команди за управляване на групи в LuckPerms. Групите са колекция от зададени правомощия, които могат да бъдат давани на потребителите. Нова група може да се създаде с командата ''creategroup''.
luckperms.usage.track.description=Набор от команди за управляване на трасета в LuckPerms. Трасетата са прогресивен списък от групи, които могат да бъдат ползвани за създаване на система от повишения и понижения (напр. рангове).
luckperms.usage.log.description=Набор от команди за управление на логването на LuckPerms.
luckperms.usage.sync.description=Презарежда всичката информация от хранилището на plugin-ите в паметта, като същевременно прилага всякакви открити промени.
luckperms.usage.info.description=Показва информация относно активния екземплярт на plugin-а.
luckperms.usage.editor.argument.type=видовете информация, които да заредим в редактора. (''all'', ''users'' или ''groups'')
luckperms.usage.editor.argument.filter=право да филтрира потребителски записи чрез
luckperms.usage.verbose.description=Контролира системата за мониторинг на подробна проверка на правата на добавките.
luckperms.usage.verbose.argument.filter=филтъра, чрез който се гледа за съвпадение на записи срещу
luckperms.usage.tree.description=Генерира се дървовиден изглед (йерархия подреден списък) на всички права, за който LuckPerms знае.
luckperms.usage.tree.argument.player=ника на наличния в сървъра играч, цел на проверката
luckperms.usage.search.description=Търси във всички потребители/групи със специфичното право
luckperms.usage.search.argument.permission=търсеното право
luckperms.usage.search.argument.page=страницата за преглед
luckperms.usage.network-sync.description=Синхронизирай промените със съхранените и поискай всички други сървъри на мрежата да направят същото
luckperms.usage.import.description=Вкарва информацията от (предишно създаден) изкаран файл
luckperms.usage.import.argument.file=файла от който да вкарва информацията
luckperms.usage.import.argument.replace=замени съществуващата информация вместо тя да бъде слята
luckperms.usage.export.description=Изкарва всичката информация за правата във файл, която може да бъде вкарана в по-късен момент.
luckperms.usage.export.argument.file=файла, към който да изкарваме информация
luckperms.usage.export.argument.without-users=изключи потребители от изкарването на информация
luckperms.usage.export.argument.without-groups=изключи групи от изкарването на информация
luckperms.usage.export.argument.upload=Качва всичката информация в уеб редактора. Може да бъде вкарана наново в по-късен момент.
luckperms.usage.reload-config.description=Презареди някои опции от конфигурацията
luckperms.usage.bulk-update.argument.data-type=типа на информация, която ще се променя. (''all'', ''users'' или ''groups'')
luckperms.usage.bulk-update.argument.action=действието, което да бъде извършено върху информацията. (''update'' или ''delete'')
luckperms.usage.bulk-update.argument.action-field=областта, върху която ще се извършва действието. задължително само за действие ''update''. (''permission'', ''server'' или ''world'')
luckperms.usage.bulk-update.argument.action-value=стойността, с която да се замени. задължителна само за ''update''.
luckperms.usage.bulk-update.argument.constraint=необходимите ограничения за актуализацията
luckperms.usage.translations.description=Управление на преводите
luckperms.usage.translations.argument.install=подкоманда за инсталиране на преводи
luckperms.usage.apply-edits.description=Прилага промени в правата, направени от уеб редактор
luckperms.usage.apply-edits.argument.code=уникалния код за информацията
luckperms.usage.apply-edits.argument.target=върху кого да се приложи информацията
luckperms.usage.create-group.description=Създай нова група
luckperms.usage.create-group.argument.name=име на групата
luckperms.usage.create-group.argument.weight=тежест на групата
luckperms.usage.create-group.argument.display-name=показваното име на групата
luckperms.usage.delete-group.description=Изтрий група
luckperms.usage.delete-group.argument.name=името на групата
luckperms.usage.list-groups.description=Списък на всички групи на платформата
luckperms.usage.user-info.description=Показва информация относно потребителя
luckperms.usage.user-demote.argument.context=контекстите, в които да понижите потребителя
luckperms.usage.user-demote.argument.dont-remove-from-first=предотвратете потребителя от това да бъде премахнат от първата група
luckperms.usage.user-clone.description=Клонирай потребителя
luckperms.usage.user-clone.argument.user=ника/uuid (идентификационния код) на клонирания потребител
luckperms.usage.group-info.description=Дава информация относно групата
luckperms.usage.group-listmembers.description=Показва потребителите/групите които наследяват права от тази група
luckperms.usage.group-listmembers.argument.page=страницата която да бъде видяна
luckperms.usage.group-setweight.description=Променя тежестта на група
luckperms.usage.group-setweight.argument.weight=нова тежест
luckperms.usage.group-set-display-name.description=Промени показваното име на групата
luckperms.usage.group-set-display-name.argument.name=ново име
luckperms.usage.group-set-display-name.argument.context=контекстите в които да се промени името
luckperms.usage.group-rename.description=Преименувай групата
luckperms.usage.group-rename.argument.name=новото име
luckperms.usage.group-clone.description=Клониране на групата
luckperms.usage.group-clone.argument.name=името на групата, върху която ще бъде клонирана
luckperms.usage.holder-editor.description=Отваря уеб редактора на права
luckperms.usage.holder-clear.description=Премахва всики права, родители и метаданни
luckperms.usage.holder-clear.argument.context=контекстите, по които да филтрираме
luckperms.usage.permission.description=Редактиране на правата
luckperms.usage.parent.description=Редактиране на наследници
luckperms.usage.meta.description=Редактирай метастойностите
luckperms.usage.permission-info.description=Списък на всички права, които обектът има
luckperms.usage.permission-info.argument.page=страницата за преглед
luckperms.usage.permission-info.argument.sort-mode=как да сортираме записите
luckperms.usage.permission-set.description=Задава право на обекта
luckperms.usage.permission-set.argument.node=правото, което да се зададе
luckperms.usage.permission-set.argument.value=правова стойност
luckperms.usage.permission-set.argument.context=контекстите в които да се добави правото
luckperms.usage.permission-unset.description=Премахва право на обекта
luckperms.usage.permission-unset.argument.node=правото, което да се премахне
luckperms.usage.permission-unset.argument.context=контекстите в които да се премахне правото
luckperms.usage.permission-settemp.description=Задава временно право на обекта
luckperms.usage.permission-settemp.argument.node=правото което да се зададе
luckperms.usage.permission-settemp.argument.value=правова стойност
luckperms.usage.permission-settemp.argument.duration=продължителността на правото, след която то изтича
luckperms.usage.permission-settemp.argument.temporary-modifier=как да се приложи временното право
luckperms.usage.permission-settemp.argument.context=контекстите в които да се добави правото
luckperms.usage.permission-unsettemp.description=Премахва временно право от обекта
luckperms.usage.permission-unsettemp.argument.node=правото, което да се премахне
luckperms.usage.permission-unsettemp.argument.duration=продължителността, която да се извади от текущата
luckperms.usage.permission-unsettemp.argument.context=контекстите в които да се премахне правото
luckperms.usage.permission-check.description=Проверява дали обектът има специфично право
luckperms.usage.permission-check.argument.node=правото което да се провери
luckperms.usage.permission-clear.description=Изчиства всички права
luckperms.usage.permission-clear.argument.context=контекстите чрез които да филтрираме
luckperms.usage.parent-info.description=Изброява групите, от които този обект наследява
luckperms.usage.parent-info.argument.page=страницата за преглеждане
luckperms.usage.parent-info.argument.sort-mode=как да сортираме записите
luckperms.usage.parent-set.description=Премахва всички други групи, от които обектът вече наследява, и ги добавя към дадената една
luckperms.usage.parent-set.argument.group=групата, към която да се зададе
luckperms.usage.parent-set.argument.context=контексите в които да се зададе групата
luckperms.usage.parent-add.description=Задава друга група за обектът, от която да наследява права
luckperms.usage.parent-add.argument.group=групата, от която да наследява
luckperms.usage.parent-add.argument.context=контексите в които групата да може да наследява
luckperms.usage.parent-remove.description=Премахва предишно зададено правило за наследяване
luckperms.usage.parent-remove.argument.group=групата, която да се премахне
luckperms.usage.parent-remove.argument.context=контекстите в които да премахнем групата
luckperms.usage.parent-set-track.argument.context=контекстите в които да се зададе групата
luckperms.usage.parent-add-temp.description=Задава друга груа за обектът, от която да наследява права временно
luckperms.usage.parent-add-temp.argument.group=групата, от която да наследява
luckperms.usage.parent-add-temp.argument.duration=продължителността на членството в групата
luckperms.usage.parent-add-temp.argument.temporary-modifier=как да се приложи временното право
luckperms.usage.parent-add-temp.argument.context=контекстите в които групата да може да наследява
luckperms.usage.parent-remove-temp.description=Премахва предишно зададено временно правило за наследяване
luckperms.usage.parent-remove-temp.argument.group=групата, която да се премахне
luckperms.usage.parent-remove-temp.argument.duration=продължителността, която да се извади от текущата
luckperms.usage.parent-remove-temp.argument.context=контекстите в които да премахнем групата
luckperms.usage.parent-clear.description=Изчиства всички родители
luckperms.usage.parent-clear.argument.context=контекстите чрез които да филтрираме
luckperms.usage.parent-clear-track.argument.context=контекстите чрез които да филтрираме
luckperms.usage.meta-info.description=Показва всички метаданни за чата
luckperms.usage.meta-set.description=Задава метастойност
luckperms.usage.meta-set.argument.key=ключът който да бъде зададен
luckperms.usage.meta-set.argument.value=стойността която да бъде зададена
luckperms.usage.meta-unset.description=Премахва метастойност
luckperms.usage.meta-unset.argument.key=ключът който да бъде премахнат
luckperms.usage.meta-settemp.description=Задава временна метастойност
luckperms.usage.meta-settemp.argument.key=ключът който да бъде зададен
luckperms.usage.meta-settemp.argument.value=стойността която да бъде зададена
luckperms.usage.meta-settemp.argument.duration=продължителността на метастойността, след която тя изтича
luckperms.usage.meta-unsettemp.description=Премахва временна метастойност
luckperms.usage.meta-unsettemp.argument.key=ключът който да бъде премахнат
luckperms.usage.meta-addprefix.description=Добавя префикс
luckperms.usage.meta-addprefix.argument.priority=приоритетът в който да се добави префикса
luckperms.usage.meta-addprefix.argument.prefix=префикса
luckperms.usage.meta-addprefix.argument.context=контекстите в които да добавим префикса
luckperms.usage.meta-addsuffix.description=Добавя суфикс
luckperms.usage.meta-addsuffix.argument.priority=приоритетът в който да се добави суфикса
luckperms.usage.meta-addsuffix.argument.suffix=суфикса
luckperms.usage.meta-addsuffix.argument.context=контекстите в които да добавим суфикса
luckperms.usage.meta-setprefix.description=Задава префикс
luckperms.usage.meta-setprefix.argument.priority=приоритетът в който да се зададе префикса
luckperms.usage.meta-setprefix.argument.prefix=префикса
luckperms.usage.meta-setprefix.argument.context=контекстите в които да зададем префикса
luckperms.usage.meta-setsuffix.description=Задава суфикс
luckperms.usage.meta-setsuffix.argument.priority=приоритетът в който да се зададе суфикса
luckperms.usage.meta-setsuffix.argument.suffix=суфикса
luckperms.usage.meta-setsuffix.argument.context=контекстите в които да се зададе суфикса
luckperms.usage.meta-removeprefix.description=Премахва префикс
luckperms.usage.meta-removeprefix.argument.priority=приоритетът от който да се премахне префиксът
luckperms.usage.meta-removeprefix.argument.prefix=префикса
luckperms.usage.meta-removeprefix.argument.context=контекситте в които да премахнем префикса
luckperms.usage.meta-removesuffix.description=Премахва суфикс
luckperms.usage.meta-removesuffix.argument.priority=приоритетът от който да се премахне суфиксът
luckperms.usage.meta-removesuffix.argument.suffix=суфикса
luckperms.usage.meta-removesuffix.argument.context=контекстите в които да премахнем суфикса
luckperms.usage.meta-addtemp-prefix.description=Добавя временен префикс
luckperms.usage.meta-addtemp-prefix.argument.priority=приоритетът в който да се добави префикса
luckperms.usage.meta-addtemp-prefix.argument.prefix=префикса
luckperms.usage.meta-addtemp-prefix.argument.duration=продължителността на префикса, след която той изтича
luckperms.usage.meta-addtemp-prefix.argument.context=контекстите в които да добавим префикса
luckperms.usage.meta-addtemp-suffix.description=Добавя временен суфикс
luckperms.usage.meta-addtemp-suffix.argument.priority=приоритетът в който да се добави суфикса
luckperms.usage.meta-addtemp-suffix.argument.suffix=суфикса
luckperms.usage.meta-addtemp-suffix.argument.duration=продължителността на суфикса, след която той изтича
luckperms.usage.meta-addtemp-suffix.argument.context=контекстите в които да добавим суфикса
luckperms.usage.meta-settemp-prefix.description=Задава временен префикс
luckperms.usage.meta-settemp-prefix.argument.priority=приоритетът в който да се зададе префикса
luckperms.usage.meta-settemp-prefix.argument.prefix=префикса
luckperms.usage.meta-settemp-prefix.argument.duration=продължителността на префикса, след която той изтича
luckperms.usage.meta-settemp-prefix.argument.context=контекстите в които да зададем префикса
luckperms.usage.meta-settemp-suffix.description=Задава временен суфикс
luckperms.usage.meta-settemp-suffix.argument.priority=приоритетът в който да се зададе суфикса
luckperms.usage.meta-settemp-suffix.argument.suffix=суфикса
luckperms.usage.meta-settemp-suffix.argument.duration=продължителността на суфикса, след която той изтича
luckperms.usage.meta-settemp-suffix.argument.context=контекстите в които да се зададе суфикса
luckperms.usage.meta-removetemp-prefix.description=Премахва временен префикс
luckperms.usage.meta-removetemp-prefix.argument.priority=приоритетът от който да премахнем префикса
luckperms.usage.meta-removetemp-prefix.argument.prefix=префикса
luckperms.usage.meta-removetemp-prefix.argument.context=контекстите от които да премахнем префикса
luckperms.usage.meta-removetemp-suffix.description=Премахва временен суфикс
luckperms.usage.meta-removetemp-suffix.argument.priority=приоритетът от който да се премахне суфикса
luckperms.usage.meta-removetemp-suffix.argument.suffix=суфикса
luckperms.usage.meta-removetemp-suffix.argument.context=контекстите в които да премахнем суфикса
luckperms.usage.meta-clear.description=Изчиства всички метаданни
luckperms.usage.meta-clear.argument.type=типа метаданни, които да бъдат премахнати
luckperms.usage.meta-clear.argument.context=контекстите чрез които да филтрираме
luckperms.usage.track-editor.description=Отваря уеб редактора на права
luckperms.usage.track-append.argument.group=групата, която да бъде прибавена
luckperms.usage.track-insert.argument.group=групата, която да бъде вмъкната
luckperms.usage.track-remove.argument.group=групата, която да се премахне
luckperms.usage.track-rename.argument.name=новото име
luckperms.usage.log-recent.description=Покажи скорошните действия
luckperms.usage.log-recent.argument.user=ника/uuid (идентификационния код) на потребителя, чрез който да филтрираме
luckperms.usage.log-recent.argument.page=номер на страницата, която да се прегледа
luckperms.usage.log-search.description=Търси из логовете за запис
luckperms.usage.log-search.argument.query=заявка за търсене
luckperms.usage.log-search.argument.page=номер на страницата, която да се прегледа
luckperms.usage.log-notify.description=Превключи нотификациите за логове
luckperms.usage.log-notify.argument.toggle=дали да се превлключи в режим "включено" или режим "изключено"
luckperms.usage.log-user-history.description=Покажи историята на потребител
luckperms.usage.log-user-history.argument.user=ника/uuid (идентификационен код) на потребителя
luckperms.usage.log-user-history.argument.page=номер на страницата, която да се прегледа
luckperms.usage.log-group-history.description=Покажи историята на група
luckperms.usage.log-group-history.argument.group=име на групата
luckperms.usage.log-group-history.argument.page=номер на страницата, която да се прегледа
luckperms.usage.log-track-history.argument.page=номер на страницата, която да се прегледа
luckperms.usage.sponge.description=Редактирай допълнителната информация за Sponge
luckperms.usage.sponge.argument.subject=субекта, който да бъде редактиран
luckperms.usage.sponge-permission-info.description=Показва информация относно правата на субекта
luckperms.usage.sponge-permission-info.argument.contexts=контекстите, по които да филтрираме
luckperms.usage.sponge-permission-set.description=Задава право за субектът
luckperms.usage.sponge-permission-set.argument.node=правото което да се зададе
luckperms.usage.sponge-permission-set.argument.tristate=зададената стойност на правото
luckperms.usage.sponge-permission-set.argument.contexts=контекстите в които да се зададе правото
luckperms.usage.sponge-permission-clear.description=Премахва правата на групата/играча
luckperms.usage.sponge-permission-clear.argument.contexts=контекстите в които се изчистват права
luckperms.usage.sponge-parent-info.description=Показва информация относно родителите на група/права
luckperms.usage.sponge-parent-info.argument.contexts=контекстите, по които да филтрираме
luckperms.usage.sponge-parent-add.description=Добавя родител на групата/правото
luckperms.usage.sponge-parent-add.argument.subject=името на родителя на групата/правото
luckperms.usage.sponge-parent-add.argument.contexts=контекстите в които да добавим родител
luckperms.usage.sponge-parent-remove.description=Премахва родител на групата/правото
luckperms.usage.sponge-parent-remove.argument.subject=името на родителя на групата/правото
luckperms.usage.sponge-parent-remove.argument.contexts=контекстите в които да премахнем родител
luckperms.usage.sponge-parent-clear.description=Премахва родителите на групата/правото
luckperms.usage.sponge-parent-clear.argument.contexts=контексите от които да премахваме родители
luckperms.usage.sponge-option-info.description=Показва информация относно опциите на група/права
luckperms.usage.sponge-option-info.argument.contexts=контекстите, по които да филтрираме
luckperms.usage.sponge-option-set.description=Задава се опция за субекта
luckperms.usage.sponge-option-set.argument.key=ключът който да бъде зададен
luckperms.usage.sponge-option-set.argument.value=задаваната стойност на този ключ
luckperms.usage.sponge-option-set.argument.contexts=контекстите в които да зададем опцията
luckperms.usage.sponge-option-unset.description=Премахва опция на субекта
luckperms.usage.sponge-option-unset.argument.key=ключът кото да бъде премахнат
luckperms.usage.sponge-option-unset.argument.contexts=контекстите в които да се премахне ключът
luckperms.usage.sponge-option-clear.description=Премахва опциите на групата/правото/играча
luckperms.usage.sponge-option-clear.argument.contexts=контекстите от които да премахваме опции
