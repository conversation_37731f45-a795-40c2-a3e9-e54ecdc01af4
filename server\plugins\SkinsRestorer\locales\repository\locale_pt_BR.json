{"skinsrestorer.prefix_format": "<yellow>[<dark_green>SkinsRestorer</dark_green>] <message>", "skinsrestorer.help_skin": "Altera a sua skin.", "skinsrestorer.help_skins": "Abre o menu de skins.", "skinsrestorer.help_sr": "Comandos de administradores do SkinsRestorer.", "skinsrestorer.help_skin_help": "Mostra este comando de ajuda.", "skinsrestorer.help_skin_set": "Altera a sua skin.", "skinsrestorer.help_skin_set_other": "Define a skin para um jogador alvo.", "skinsrestorer.help_skin_set_url": "Altera sua skin a partir de um URL.", "skinsrestorer.help_skin_clear": "Apaga a sua skin.", "skinsrestorer.help_skin_clear_other": "Remove a skin de um jogador alvo.", "skinsrestorer.help_skin_random": "<PERSON>á uma skin aleatória.", "skinsrestorer.help_skin_random_other": "Define uma skin aleatória para um jogador específico.", "skinsrestorer.help_skin_search": "Procura a skin que você quer.", "skinsrestorer.help_skin_edit": "Edite sua skin atual online.", "skinsrestorer.help_skin_update": "<PERSON>ualiza a sua skin.", "skinsrestorer.help_skin_update_other": "Atualiza a skin de um determinado jogador.", "skinsrestorer.help_skin_undo": "Restaura sua skin anterior.", "skinsrestorer.help_skin_undo_other": "Restaura a skin anterior de um jogador específico.", "skinsrestorer.help_skin_favourite": "Salva sua skin como uma favorita.", "skinsrestorer.help_skin_favourite_other": "Salva a skin de um jogador em específico como favorita.", "skinsrestorer.help_skull": "Te dá uma cabeça.", "skinsrestorer.help_skull_help": "Comandos de cabeças para SkinsRestorer.", "skinsrestorer.help_skull_get": "Te dá uma cabeça.", "skinsrestorer.help_skull_get_other": "Dá uma cabeça para outro jogador.", "skinsrestorer.help_skull_get_url": "Te dá a cabeça de uma skin por URL.", "skinsrestorer.help_skull_random": "Te dá uma cabeça aleatória.", "skinsrestorer.help_skull_random_other": "Dá uma cabeça aleatória para outro jogador.", "skinsrestorer.help_sr_reload": "Recarrega o arquivo de configurações.", "skinsrestorer.help_sr_status": "Verifica os serviços de API de plugin necessários.", "skinsrestorer.help_sr_drop": "Remove skin ou jogador do banco de dados.", "skinsrestorer.help_sr_info": "<PERSON><PERSON> as informações sobre uma skin ou um jogador.", "skinsrestorer.help_sr_apply_skin": "Reaplica a skin num certo jogador.", "skinsrestorer.help_sr_create_custom": "Cria uma skin personalizada para o servidor.", "skinsrestorer.help_sr_purge_old_data": "<PERSON>pa dados da skin antigos de mais de x dias atrás.", "skinsrestorer.help_sr_dump": "Envia dados de suporte para bytebin.lucko.me.", "skinsrestorer.success_generic": "<dark_green><message>", "skinsrestorer.info_use_quotes": "URLs precisam estar entre aspas. Exemplo: <yellow>/skin set \"https://example.com/skin.png\"</yellow> (Você pode pressionar TAB para completar automaticamente as aspas)", "skinsrestorer.success_skin_change": "Sua skin foi alterada.", "skinsrestorer.success_skin_change_other": "<PERSON><PERSON><PERSON> mudou a skin de <yellow><name></yellow>.", "skinsrestorer.success_skin_undo": "Sua skin<yellow><skin></yellow> foi revertida de volta à skin de <yellow><timestamp></yellow>.", "skinsrestorer.success_skin_undo_other": "A skin <yellow><skin></yellow> de <yellow><name></yellow> foi revertida para <yellow><timestamp></yellow>.", "skinsrestorer.success_skin_favourite": "<PERSON>a skin <yellow><skin></yellow> foi adicionada as favoritas.", "skinsrestorer.success_skin_favourite_other": "A skin <yellow><skin></yellow> de <yellow><name></yellow> foi definida como favorita.", "skinsrestorer.success_skin_unfavourite": "Sua skin favorita <yellow><skin></yellow> de <yellow><timestamp></yellow> foi desfavoritada.", "skinsrestorer.success_skin_unfavourite_other": "A skin favorita <yellow><skin></yellow> de <yellow><name></yellow> de <yellow><timestamp></yellow> foi desfavoritada.", "skinsrestorer.success_skin_clear": "Sua skin foi removida.", "skinsrestorer.success_skin_clear_other": "A skin de <yellow><name></yellow> foi removida.", "skinsrestorer.success_updating_skin": "Sua skin foi atualizada.", "skinsrestorer.success_updating_skin_other": "A skin de <yellow><name></yellow> foi alterada.", "skinsrestorer.success_skull_get": "Você recebeu uma cabeça.", "skinsrestorer.success_skull_get_other": "Você deu uma cabeça para <yellow><name></yellow>.", "skinsrestorer.success_admin_applyskin": "A skin do jogador foi recarregada!", "skinsrestorer.success_admin_createcustom": "A skin <yellow><skin></yellow> foi criada!", "skinsrestorer.success_admin_setcustomname": "O nome da skin <yellow><skin></yellow> foi alterada para <yellow><display_name></yellow>.", "skinsrestorer.success_admin_drop": "Os dados de <target> do tipo <type> foram eliminados.", "skinsrestorer.success_admin_reload": "Configuração e idioma recarregados!", "skinsrestorer.success_history_line": "<dark_green>- <hover:show_text:'<dark_green>Clique para usar <yellow><skin></yellow>'><click:run_command:/skin set \"<skin_id>\"><yellow><skin></yellow></click></hover> de <yellow><timestamp></yellow>", "skinsrestorer.success_favourites_line": "<dark_green>- <hover:show_text:'<dark_green>Clique para usar <yellow><skin></yellow>'><click:run_command:/skin set \"<skin_id>\"><yellow><skin></yellow></click></hover> de <yellow><timestamp></yellow>", "skinsrestorer.error_generic": "<dark_red>Erro<dark_gray>: <red><message>", "skinsrestorer.error_generic_skin": "Um erro ocorreu ao solicitar os dados da skin, tente novamente mais tarde!", "skinsrestorer.error_no_undo": "Não há skins.", "skinsrestorer.error_no_skin_to_favourite": "<PERSON>ão há skin para favoritar.", "skinsrestorer.error_skin_disabled": "A skin foi desabilitada por um administrador.", "skinsrestorer.error_skinurl_disallowed": "Este domínio não é permitido pelo administrador.", "skinsrestorer.error_updating_skin": "Ocorreu um erro ao atualizar sua skin. Tente novamente mais tarde!", "skinsrestorer.error_updating_url": "Você não pode atualizar skins de URL personalizados! <newline><red>Tente novamente usando /skin url", "skinsrestorer.error_updating_customskin": "A skin não pôde ser atualizada, pois, é customizada.", "skinsrestorer.error_invalid_urlskin": "Skin ou URL de formato inválido, <newline><red>Tente enviar a imagem para o imgur, clique com o botão direito do mouse, e clique em 'copiar endereço da imagem.' <newline><red>Para um guia detalhado, veja: <red><underlined><hover:show_text:'<dark_green>Clique para abrir'><click:open_url:'https://skinsrestorer.net/skinurl'>https://skinsrestorer.net/skinurl</click></hover></underlined>.", "skinsrestorer.error_admin_applyskin": "A skin do jogador NÃO pode ser recarregada!", "skinsrestorer.error_ms_full": "A API MineSkin demorou a responder. Por favor, tente novamente mais tarde.", "skinsrestorer.error_ms_api_failed": "A API MineSkin está sobrecarregada, tente novamente mais tarde!", "skinsrestorer.error_ms_api_key_invalid": "Chave de API MineSkin inválida! Contate o dono do servidor sobre isso!", "skinsrestorer.error_ms_unknown": "Erro desconhecido do MineSkin!", "skinsrestorer.error_no_history": "Não há histórico de skin.", "skinsrestorer.error_no_favourites": "Não há skins favoritas.", "skinsrestorer.error_player_refresh_no_mapping": "Não foi possível recarregar sua skin, pois sua versão do Minecraft não é suportada pelo SkinsRestorer. Por gentileza, peça para o administrador atualizar o plugin SkinsRestorer.", "skinsrestorer.not_connected_to_server": "<red>Você não está conectado a um servidor.", "skinsrestorer.divider": "<dark_aqua>----------------------------------------------", "skinsrestorer.admincommand_status_checking": "<gray>Executando a checagem de serviços...", "skinsrestorer.admincommand_status_uuid_api": "<gray>API UUID funcionais: <count>/<total>", "skinsrestorer.admincommand_status_profile_api": "<gray>APIs de Perfil funcionando: <count>/<total>", "skinsrestorer.admincommand_status_working": "<green>O plugin está atualmente em um estado funcional.", "skinsrestorer.admincommand_status_degraded": "<green>O plugin está degradado ou desatualizado, algumas funções podem não funcionar completamente.", "skinsrestorer.admincommand_status_broken": "<red>O plugin está atualmente em estado de mau funcionamento, nenhuma skin nova pode ser adicionada.", "skinsrestorer.admincommand_status_firewall": "<red>Conexões estão possivelmente bloqueadas devido a uma firewall.<newline>Por favor, <PERSON><PERSON> https://skinsrestorer.net/firewall para mais informações.", "skinsrestorer.admincommand_status_summary_server": "<gray>Versão do servidor: <gold><version>", "skinsrestorer.admincommand_status_summary_proxymode": "<gray>Modo Proxy: <gold><proxy_mode>", "skinsrestorer.admincommand_status_summary_commit": "<gray>Envio: <gold><hash>", "skinsrestorer.admincommand_drop_player_not_found": "<red><PERSON><PERSON><PERSON> <yellow><player></yellow> não encontrado.", "skinsrestorer.admincommand_drop_skin_not_found": "<red>Skin <yellow><skin></yellow> não encontrada.", "skinsrestorer.admincommand_drop_uuid_error": "<red>Nós não conseguimos conectar com a Mojang para conseguir o UUID do jogador.", "skinsrestorer.admincommand_info_checking": "<gray><PERSON><PERSON>do dados requeridos...", "skinsrestorer.admincommand_info_player": "<gray>UUID do jogador: <gold><uuid><newline><gray>Identificador da skin: <gold><identifier><newline><gray>Variante da skin: <gold><variant><newline><gray>Tipo de skin: <gold><type>", "skinsrestorer.admincommand_info_invalid_uuid": "<red>Você precisa especificar o UUID de um jogador.", "skinsrestorer.admincommand_info_no_set_skin": "<red>O jogador não tem uma skin explicitamente definida.", "skinsrestorer.admincommand_info_url_skin": "<gray>URL da Skin: <gold><click:open_url:'<url>'><url></click><newline><gray>ID no MineSkin: <gold><mine_skin_id>", "skinsrestorer.admincommand_info_hardcoded_skin": "<gray>Skin pré-definida: <gold><skin>", "skinsrestorer.admincommand_info_custom_skin": "<gray>Skin Personalizada: <gold><skin>", "skinsrestorer.admincommand_info_player_skin": "<gray> Skin do jogador: <gold><skin>", "skinsrestorer.admincommand_info_generic": "<gray>URL da Texture: <gold><click:open_url:'<url>'><url></click><newline><gray>Variante: <gold><variant><newline><gray>UUID do perfil: <gold><uuid><newline><gray>Name do perfil: <gold><name><newline><gray><PERSON><PERSON><PERSON><PERSON> requerido: <gold><request_time>", "skinsrestorer.admincommand_purgeolddata_success": "<green>Skins antigas deletadas com sucesso!", "skinsrestorer.admincommand_purgeolddata_error": "<dark_red>Um erro ocorreu enquanto deletava skins antigas!", "skinsrestorer.admincommand_dump_uploading": "<green>Enviando informações para bytebin.lucko.me...", "skinsrestorer.admincommand_dump_success": "<green><PERSON>vio su<PERSON>! <yellow><click:open_url:'<url>'><url></click>", "skinsrestorer.admincommand_dump_error": "<red>Erro no envio de informações para bytebin.lucko.me", "skinsrestorer.command_server_not_allowed_message": "<red>Os comandos foram desativados para o servidor <server>.", "skinsrestorer.command_unknown_player": "<PERSON><PERSON><PERSON> desconhecido: <name>", "skinsrestorer.command_no_targets_supplied": "Jogador não reconhecido.", "skinsrestorer.player_has_no_permission_skin": "<dark_red><PERSON>rro<dark_gray>: <red>Você não tem permissão para usar essa skin.", "skinsrestorer.player_has_no_permission_url": "<dark_red>Erro<dark_gray>: <red>Você não tem permissão para definir skins por URL.", "skinsrestorer.not_premium": "<dark_red><PERSON>rro<dark_gray>: <red>Não há uma conta de minecraft com este nome.", "skinsrestorer.only_allowed_on_console": "<dark_red>Erro<dark_gray>: <red>Este comando é apenas para o console!", "skinsrestorer.only_allowed_on_player": "<dark_red><PERSON>rro<dark_gray>: <red>Apenas jogadores podem usar este comando!", "skinsrestorer.invalid_player": "<dark_red><PERSON>rro<dark_gray>: <red><input> não é um nome de usuário/URL válido.", "skinsrestorer.skin_cooldown": "<dark_red>Erro<dark_gray>: <red>Você pode alterar sua skin novamente em <yellow><time></yellow> segundos", "skinsrestorer.ms_uploading_skin": "<dark_green>Fazendo upload da sua skin, por favor espere... (<PERSON><PERSON> levar algum tempo)", "skinsrestorer.wait_a_minute": "<dark_red><PERSON>rro<dark_gray>: <red>Por favor, espere um tempo antes de solicitar esta skin novamente. (Taxa de envio limitada)", "skinsrestorer.skinsmenu_open": "<dark_green>Abrindo menu de skins...", "skinsrestorer.skinsmenu_title_select": "<blue>Seleção de Menu", "skinsrestorer.skinsmenu_title_main": "<blue>Skins — <PERSON><PERSON><PERSON><PERSON> <page_number>", "skinsrestorer.skinsmenu_title_history": "<blue>His<PERSON><PERSON><PERSON><PERSON> — Página <page_number>", "skinsrestorer.skinsmenu_title_favourites": "<blue><PERSON><PERSON><PERSON><PERSON> — Página <page_number>", "skinsrestorer.skinsmenu_next_page": "<green><bold>»</bold> <gray>Próxima página</gray> <bold>»</bold>", "skinsrestorer.skinsmenu_previous_page": "<yellow><bold>«</bold> <gray>Página An<PERSON>ior</gray> <bold>«</bold>", "skinsrestorer.skinsmenu_clear_skin": "<red><bold>[</bold> <gray>Remover skin</gray> <bold>]</bold>", "skinsrestorer.skinsmenu_select_skin": "<gray>Clique para selecionar essa skin", "skinsrestorer.skinsmenu_history_lore": "<blue>Skin de <time>", "skinsrestorer.skinsmenu_set_favourite_lore": "<gray>Shift + clique para definir como favorita", "skinsrestorer.skinsmenu_remove_favourite_lore": "<gray>Shift + clique para remover dos favoritos", "skinsrestorer.skinsmenu_favourite_since_lore": "<blue>Favorita desde <time>", "skinsrestorer.skinsmenu_no_permission": "<red>Você não tem permissão para usar essa skin.", "skinsrestorer.skinsmenu_main_button": "<aqua><bold>[</bold> <gray>Skins</gray> <bold>]</bold>", "skinsrestorer.skinsmenu_history_button": "<aqua><bold>[</bold> <gray>Histórico</gray> <bold>]</bold>", "skinsrestorer.skinsmenu_favourites_button": "<aqua><bold>[</bold> <gray>Favoritos</gray> <bold>]</bold>", "skinsrestorer.skinsmenu_back_select_button": "<yellow><bold>«</bold> <gray>Seleção de Menu</gray> <bold>«</bold>", "skinsrestorer.skin_search_message": "<dark_green>Você pode encontrar skins <green><search></green> aqui: <newline><green><hover:show_text:'<dark_green>Clique para abrir'><click:open_url:'https://namemc.com/minecraft-skins/tag/<search>'>https://namemc.com/minecraft-skins/tag/<search></click></hover><newline><newline><aqua>Caso não encontre a skin desejada, poderá procurar em https://namemc.com/minecraft-skins/tag <newline>Você pode usar a skin usando o link:<newline>/skin https://namemc.com/skin/", "skinsrestorer.skin_edit_message": "<dark_green>Você pode editar sua skin em <u><aqua><hover:show_text:'<dark_green>Clique para abrir'><click:open_url:'<url>'>este link</click></hover></aqua></u> <newline><dark_green>Para aprender a usar skins editadas, visite: <newline><green><hover:show_text:'<dark_green>Clique para abrir'><click:open_url:'https://skinsrestorer.net/skinedit'>https://skinsrestorer.net/skinedit</click></hover>", "skinsrestorer.no_skin_data": "<dark_red><PERSON>rro<dark_gray>: <red>Nenhuma informação de skin encontrada! Este player tem uma skin?", "skinsrestorer.outdated": "<dark_red>Você está usando uma versão desatualizada do SkinsRestorer!<newline><red>Por favor, atualize para a versão mais recente no Modrinth: <newline><yellow><hover:show_text:'<dark_green>Clique para abrir'><click:open_url:'https://modrinth.com/plugin/skinsrestorer'>https://modrinth.com/plugin/skinsrestorer</click></hover>", "skinsrestorer.unsupported_java": "<dark_red>A versão Java (<version>) do seu <platform> não é compatível com SkinsRestorer!<newline><red>Atualize para Java 17 ou superior para usar SkinsRestorer sem problemas. As versões mais recentes do Java também podem executar servidores mais antigos, portanto, um servidor Minecraft 1.8 pode ser executado no Java 17. <PERSON><PERSON> as informações do console para obter mais detalhes.", "skinsrestorer.permission_player_wildcard": "Permissões adicionais de jogadores", "skinsrestorer.permission_command": "Permite acesso ao comando principal \"/sr\".", "skinsrestorer.permission_command_set": "Permite acesso para mudar sua skin.", "skinsrestorer.permission_command_set_url": "Permite acesso para mudar sua skin através de um URL.", "skinsrestorer.permission_command_clear": "Permite acesso para remover sua skin.", "skinsrestorer.permission_command_random": "Permite acesso para definir uma skin aleatória.", "skinsrestorer.permission_command_update": "Permite acesso para recarregar sua skin.", "skinsrestorer.permission_command_undo": "Permite acesso para reverter sua skin.", "skinsrestorer.permission_command_favourite": "Permite acesso para definir a skin como favorita.", "skinsrestorer.permission_command_search": "Permite acesso para procurar sua skin.", "skinsrestorer.permission_command_edit": "Permite acesso para editar sua skin.", "skinsrestorer.permission_command_gui": "Permite acesso ao menu de skins.", "skinsrestorer.permission_admin_wildcard": "Permissões adicionais de administradores", "skinsrestorer.permission_admincommand": "Permite acesso ao comando principal \"/sr\".", "skinsrestorer.permission_command_set_other": "Permite acesso para mudar a skin de outro jogador.", "skinsrestorer.permission_command_clear_other": "Permite acesso para remover a skin de outro jogador.", "skinsrestorer.permission_command_random_other": "Permite acesso para definir uma skin aleatória de outro jogador.", "skinsrestorer.permission_command_update_other": "Permite acesso para recarregar a skin de outro jogador.", "skinsrestorer.permission_command_favourite_other": "Permite acesso para definir uma skin como favorita para outro jogador.", "skinsrestorer.permission_command_undo_other": "Permite acesso para reverter a skin de outro jogador.", "skinsrestorer.permission_admincommand_skull": "Permite acesso ao comando \"/skull\".", "skinsrestorer.permission_admincommand_skull_get": "Permite acesso para obter uma cabeça.", "skinsrestorer.permission_admincommand_skull_get_url": "Permite acesso para obter uma cabeça por URL.", "skinsrestorer.permission_admincommand_skull_random": "Permite acesso para obter uma cabeça aleatória.", "skinsrestorer.permission_admincommand_skull_get_other": "Permite acesso para dar uma cabeça para outro jogador.", "skinsrestorer.permission_admincommand_skull_random_other": "Permite acesso para dar uma cabeça aleatora para outro jogador.", "skinsrestorer.permission_admincommand_reload": "Permite acesso para usar o comando \"/sr reload\".", "skinsrestorer.permission_admincommand_status": "Permite acesso para usar o comando \"/st status\".", "skinsrestorer.permission_admincommand_drop": "Permite acesso para remover um arquivo \".SKIN\".", "skinsrestorer.permission_admincommand_info": "Permite acesso para acessar as informações de um jogador ou uma skin.", "skinsrestorer.permission_admincommand_applyskin": "Permite acesso para mudar a skin de outro jogador.", "skinsrestorer.permission_admincommand_createcustom": "Permite acesso para criar uma skin global por URL.", "skinsrestorer.permission_admincommand_purgeolddata": "Permite acesso para remover dados de skins antigas.", "skinsrestorer.permission_admincommand_dump": "Permite acesso para enviar informações do servidor via \"/sr dump\".", "skinsrestorer.permission_bypasscooldown": "<PERSON><PERSON><PERSON> qualquer atraso de comando nas configurações.", "skinsrestorer.permission_bypassdisabled": "<PERSON><PERSON><PERSON> qualquer skin desativada nas configurações.", "skinsrestorer.permission_ownskin": "Permite alterar sua própria skin.", "skinsrestorer.duration_day": " dia", "skinsrestorer.duration_days": " dias", "skinsrestorer.duration_hour": " hora", "skinsrestorer.duration_hours": " horas", "skinsrestorer.duration_minute": " minuto", "skinsrestorer.duration_minutes": " minutos", "skinsrestorer.duration_second": " segundo", "skinsrestorer.duration_seconds": " segundos"}