<?xml version="1.0"?>
<!--

    DO NOT ALTER OR REMOVE COPYRIGHT NOTICES OR THIS HEADER.

    Copyright (c) 1997-2013 Oracle and/or its affiliates. All rights reserved.

    The contents of this file are subject to the terms of either the GNU
    General Public License Version 2 only ("GPL") or the Common Development
    and Distribution License("CDDL") (collectively, the "License").  You
    may not use this file except in compliance with the License.  You can
    obtain a copy of the License at
    http://glassfish.java.net/public/CDDL+GPL_1_1.html
    or packager/legal/LICENSE.txt.  See the License for the specific
    language governing permissions and limitations under the License.

    When distributing the software, include this License Header Notice in each
    file and include the License file at packager/legal/LICENSE.txt.

    GPL Classpath Exception:
    Oracle designates this particular file as subject to the "Classpath"
    exception as provided by Oracle in the GPL Version 2 section of the License
    file that accompanied this code.

    Modifications:
    If applicable, add the following below the License Header, with the fields
    enclosed by brackets [] replaced by your own identifying information:
    "Portions Copyright [year] [name of copyright owner]"

    Contributor(s):
    If you wish your version of this file to be governed by only the CDDL or
    only the GPL Version 2, indicate your decision by adding "[Contributor]
    elects to include this software in this distribution under the [CDDL or GPL
    Version 2] license."  If you don't indicate a single choice of license, a
    recipient has the option to distribute your version of this file under
    either the CDDL, the GPL Version 2 or to extend the choice of license to
    its licensees as provided above.  However, if you add GPL Version 2 code
    and therefore, elected the GPL Version 2 license, then the option applies
    only if the new code is made subject to such option by the copyright
    holder.

-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

    <parent>
        <groupId>net.java</groupId>
        <artifactId>jvnet-parent</artifactId>
        <version>4</version>
    </parent>
    
    <modelVersion>4.0.0</modelVersion>
    <groupId>javax.xml.bind</groupId>
    <artifactId>jaxb-api</artifactId>
    <version>2.2.12</version>
    <packaging>jar</packaging>

    <name>Java Architecture for XML Binding</name>
    <description>JAXB (JSR 222) API</description>
    <url>http://jaxb.java.net/</url>

    <organization>
        <name>Oracle Corporation</name>
        <url>http://www.oracle.com/</url>
    </organization>

    <developers>
        <developer>
            <name>Martin Grebac</name>
            <email><EMAIL></email>
            <organization>Oracle Corporation</organization>
        </developer>
        <developer>
            <name>Iaroslav Savytskyi</name>
            <email><EMAIL></email>
            <organization>Oracle Corporation</organization>
        </developer>
    </developers>
    
    <licenses>
        <license>
            <name>CDDL 1.1</name>
            <url>https://glassfish.java.net/public/CDDL+GPL_1_1.html</url>
            <distribution>repo</distribution>
        </license>
        <license>
            <name>GPL2 w/ CPE</name>
            <url>https://glassfish.java.net/public/CDDL+GPL_1_1.html</url>
            <distribution>repo</distribution>
        </license>
    </licenses>

    <scm>
        <connection>scm:svn:http://svn.java.net/svn/jsr222~svn/branches/jaxb-2_2</connection>
        <developerConnection>scm:svn:https://svn.java.net/svn/jsr222~svn/branches/jaxb-2_2</developerConnection>
        <url>http://java.net/projects/jsr222/sources/svn/show/branches/jaxb-2_2</url>
    </scm>
    <issueManagement>
        <system>jira</system>
        <url>http://java.net/jira/browse/JAXB</url>
    </issueManagement>

    <properties>
        <release.spec.feedback><EMAIL></release.spec.feedback>
        <release.spec.date>Dec 2009</release.spec.date>
        <findbugs.exclude>${project.basedir}/exclude.xml</findbugs.exclude>
        <findbugs.threshold>Low</findbugs.threshold>
    </properties>
    
    <build>

        <resources>
            <resource>
                <directory>src/main/java</directory>
                <excludes>
                    <exclude>**/*.java</exclude>
                </excludes>
            </resource>
        </resources>

        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.glassfish.build</groupId>
                    <artifactId>gfnexus-maven-plugin</artifactId>
                    <version>0.18</version>
                    <configuration>
                        <stagingRepos>
                            <stagingRepo>
                                <ref>javax.xml.bind:jaxb-api:${project.version}:jar</ref>
                                <profile>javax.xml.bind</profile>
                            </stagingRepo>
                        </stagingRepos>
                        <promotionProfile>metro</promotionProfile>
                        <message>JAXB_API-${project.version}</message>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
        
        <plugins>
            <plugin>
                <artifactId>maven-enforcer-plugin</artifactId>
                <version>1.0.1</version>
                <executions>
                    <execution>
                        <id>enforce-versions</id>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <rules>
                        <requireJavaVersion>
                            <version>[1.6,)</version>
                        </requireJavaVersion>
                        <requireMavenVersion>
                            <version>[3.0.3,)</version>
                        </requireMavenVersion>
                        <DependencyConvergence />
                    </rules>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>cobertura-maven-plugin</artifactId>
                <version>2.5.1</version>
                <configuration>
                    <formats>
                        <format>xml</format>
                    </formats>
                    <check>
                      <totalLineRate>45</totalLineRate>
                      <packageLineRate>45</packageLineRate>
                      <haltOnFailure>true</haltOnFailure>
                    </check>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.glassfish.copyright</groupId>
                <artifactId>glassfish-copyright-maven-plugin</artifactId>
                <version>1.29</version>
                <configuration>
                    <templateFile>${project.basedir}/copyright.txt</templateFile>
                    <excludeFile>${project.basedir}/copyright-exclude</excludeFile>
                    <!-- skip files not under SCM-->
                    <scmOnly>true</scmOnly>
                    <!-- turn off warnings -->
                    <warn>true</warn>
                    <!-- for use with repair -->
                    <update>false</update>
                    <!-- check that year is correct -->
                    <ignoreYear>false</ignoreYear>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>2.5.1</version>
                <configuration>
                    <source>1.5</source>
                    <target>1.5</target>
                </configuration>
            </plugin>
        
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>2.4</version>
                <configuration>
                    <archive>
                        <manifestFile>${project.build.outputDirectory}/META-INF/MANIFEST.MF</manifestFile>
                        <manifestEntries>
                            <Extension-Name>javax.xml.bind</Extension-Name>
                            <Implementation-Build-Id>${scmBranch}-${buildNumber}, ${timestamp}</Implementation-Build-Id>
                        </manifestEntries>
                        <manifest>
                            <addDefaultSpecificationEntries>true</addDefaultSpecificationEntries>
                        </manifest>
                    </archive>
                </configuration>
            </plugin>
            
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>buildnumber-maven-plugin</artifactId>
                <version>1.1</version>
                <configuration>
                    <getRevisionOnlyOnce>true</getRevisionOnlyOnce>
                    <timestampFormat>{0,date,yyyy-MM-dd'T'HH:mm:ssZ}</timestampFormat>
                    <providerImplementations>
                        <svn>javasvn</svn>
                    </providerImplementations>
                    <getRevisionOnlyOnce>true</getRevisionOnlyOnce>
                    <revisionOnScmFailure>false</revisionOnScmFailure>
                </configuration>
                <executions>
                    <execution>
                        <phase>validate</phase>
                        <goals>
                            <goal>create</goal>
                        </goals>
                    </execution>
                </executions>
                <dependencies>
                    <dependency>
                        <groupId>org.tmatesoft.svnkit</groupId>
                        <artifactId>svnkit</artifactId>
                        <version>1.7.4-v1</version>
                    </dependency>
                </dependencies>
            </plugin>                
            <plugin>
                <groupId>org.apache.felix</groupId>
                <artifactId>maven-bundle-plugin</artifactId>
                <extensions>true</extensions>
                <version>2.3.7</version>
                <configuration>
                    <archive>  
                        <manifestFile>${project.build.outputDirectory}/META-INF/MANIFEST.MF</manifestFile>
                    </archive>                     
                    <instructions>
                        <Export-Package>{local-packages};version="${project.version}"</Export-Package>
                        <Import-Package>
                            javax.activation,
                            javax.xml.bind;version="${project.version}",
                            javax.xml.bind.annotation;version="${project.version}",
                            javax.xml.bind.annotation.adapters;version="${project.version}",
                            javax.xml.bind.attachment;version="${project.version}",
                            javax.xml.bind.helpers;version="${project.version}",
                            javax.xml.bind.util;version="${project.version}",
                            javax.xml.datatype,
                            javax.xml.namespace,
                            javax.xml.parsers,
                            javax.xml.stream,
                            javax.xml.transform,
                            javax.xml.transform.dom,
                            javax.xml.transform.sax,
                            javax.xml.transform.stream,
                            javax.xml.validation,
                            org.w3c.dom,
                            org.xml.sax,
                            org.xml.sax.ext,
                            org.xml.sax.helpers    
                        </Import-Package>
                        <Bundle-SymbolicName>jaxb-api</Bundle-SymbolicName>
                        <Bundle-Name>jaxb-api</Bundle-Name>
                        <DynamicImport-Package>org.glassfish.hk2.osgiresourcelocator</DynamicImport-Package>
                    </instructions>
                </configuration>
                <executions>
                    <execution>
                        <id>bundle-manifest</id>
                        <phase>process-classes</phase>
                        <goals>    
                            <goal>manifest</goal>
                        </goals>   
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>findbugs-maven-plugin</artifactId>
                <version>2.5.2</version>
                <configuration>
                    <skip>${findbugs.skip}</skip>
                    <threshold>${findbugs.threshold}</threshold>
                    <findbugsXmlWithMessages>true</findbugsXmlWithMessages>
                    <excludeFilterFile>
                        exclude-common.xml,${findbugs.exclude}
                    </excludeFilterFile>
                    <fork>true</fork>
                    <jvmArgs>-Xms64m -Xmx256m</jvmArgs>    
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>org.glassfish.findbugs</groupId>
                        <artifactId>findbugs</artifactId>
                        <version>1.0</version>
                    </dependency>
                </dependencies>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <version>2.9</version>
                <configuration>
                    <nodeprecated>false</nodeprecated>
                    <use>false</use>
                    <author>true</author>
                    <version>true</version>
                    <doctitle><![CDATA[<br>
JAXB ${project.version} Runtime Library</h2>
${project.name} specification, ${release.spec.date}<br>
Comments to: <i><a href='mailto:${release.spec.feedback}'>${release.spec.feedback}</a></i><br>
More information at: <i><a target='_top'
href='http://jaxb.java.net'>http://jaxb.java.net</a></i><br>
&nbsp;<br>&nbsp;<br><hr width='65%'><h1>${project.name}</h1><hr width='75%'>
<br>&nbsp;<br>]]>
                    </doctitle>
                    <header><![CDATA[JAXB<br>v${project.version}]]>
                    </header>
                    <bottom><![CDATA[<font size=-1>
<br>Comments to: <a href='mailto:${release.spec.feedback}'><i>${release.spec.feedback}</i></a>
<br>More information at: <a target='_top'
href='http://jaxb.java.net'><i>http://jaxb.java.net</i></a>
<p>Copyright &copy; 2004-2011 Oracle </font>]]>
                    </bottom>
                    <detectJavaApiLink>false</detectJavaApiLink>
                    <offlineLinks>
                        <offlineLink>
                            <url>http://download.oracle.com/javase/6/docs/api/</url>
                            <location>${basedir}/offline-javadoc</location>
                        </offlineLink>
                    </offlineLinks>
                </configuration>  
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>2.2.1</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-release-plugin</artifactId>
                <version>2.1</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-gpg-plugin</artifactId>
                <version>1.1</version>
            </plugin>
            <plugin>
                <groupId>org.glassfish.build</groupId>
                <artifactId>gfnexus-maven-plugin</artifactId>
            </plugin>
        </plugins>

    </build>    

</project>
