"""
MC Web Manager - API路由模块
提供认证、服务器控制等API端点
"""

from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm
from datetime import timedelta
from typing import Dict, Any

# 导入认证和服务器控制模块
import sys
from pathlib import Path

# 添加app目录到Python路径
app_dir = Path(__file__).parent.parent
if str(app_dir) not in sys.path:
    sys.path.insert(0, str(app_dir))

from auth import (
    authenticate_user, create_access_token, get_current_admin_user,
    Token, User, auth_config
)
from core.server_control import server_controller

# 创建API路由器
router = APIRouter()

# 认证相关路由
@router.post("/auth/login", response_model=Token)
async def login(form_data: OAuth2PasswordRequestForm = Depends()):
    """用户登录接口"""
    user = authenticate_user(form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    access_token_expires = timedelta(minutes=auth_config.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.username}, expires_delta=access_token_expires
    )
    
    return {
        "access_token": access_token,
        "token_type": "bearer"
    }

@router.get("/auth/me", response_model=User)
async def read_users_me(current_user: User = Depends(get_current_admin_user)):
    """获取当前用户信息"""
    return current_user

@router.post("/auth/logout")
async def logout(current_user: User = Depends(get_current_admin_user)):
    """用户登出接口（客户端需要删除token）"""
    return {"message": "登出成功"}

# 服务器控制相关路由
@router.get("/server/status")
async def get_server_status(current_user: User = Depends(get_current_admin_user)) -> Dict[str, Any]:
    """获取服务器状态"""
    try:
        status_info = server_controller.get_server_status()
        return {
            "success": True,
            "data": status_info
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取服务器状态失败: {str(e)}"
        )

@router.post("/server/start")
async def start_server(current_user: User = Depends(get_current_admin_user)) -> Dict[str, Any]:
    """启动服务器"""
    try:
        result = server_controller.start_server()
        if result["success"]:
            return {
                "success": True,
                "message": result["message"],
                "data": result
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result["message"]
            )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"启动服务器失败: {str(e)}"
        )

@router.post("/server/stop")
async def stop_server(current_user: User = Depends(get_current_admin_user)) -> Dict[str, Any]:
    """停止服务器"""
    try:
        result = server_controller.stop_server()
        if result["success"]:
            return {
                "success": True,
                "message": result["message"],
                "data": result
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result["message"]
            )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"停止服务器失败: {str(e)}"
        )

@router.post("/server/restart")
async def restart_server(current_user: User = Depends(get_current_admin_user)) -> Dict[str, Any]:
    """重启服务器"""
    try:
        result = server_controller.restart_server()
        if result["success"]:
            return {
                "success": True,
                "message": result["message"],
                "data": result
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result["message"]
            )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"重启服务器失败: {str(e)}"
        )

# 系统信息相关路由
@router.get("/system/info")
async def get_system_info(current_user: User = Depends(get_current_admin_user)) -> Dict[str, Any]:
    """获取系统信息"""
    try:
        import psutil
        import platform
        from datetime import datetime
        
        # 获取系统基本信息
        system_info = {
            "platform": platform.system(),
            "platform_release": platform.release(),
            "platform_version": platform.version(),
            "architecture": platform.machine(),
            "hostname": platform.node(),
            "processor": platform.processor(),
            "python_version": platform.python_version(),
        }
        
        # 获取系统资源信息
        cpu_info = {
            "cpu_count": psutil.cpu_count(),
            "cpu_percent": psutil.cpu_percent(interval=1),
            "cpu_freq": psutil.cpu_freq()._asdict() if psutil.cpu_freq() else None,
        }
        
        memory = psutil.virtual_memory()
        memory_info = {
            "total": memory.total,
            "available": memory.available,
            "percent": memory.percent,
            "used": memory.used,
            "free": memory.free
        }
        
        disk = psutil.disk_usage('/')
        disk_info = {
            "total": disk.total,
            "used": disk.used,
            "free": disk.free,
            "percent": (disk.used / disk.total) * 100
        }
        
        return {
            "success": True,
            "data": {
                "system": system_info,
                "cpu": cpu_info,
                "memory": memory_info,
                "disk": disk_info,
                "timestamp": datetime.now().isoformat()
            }
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取系统信息失败: {str(e)}"
        )

# 健康检查路由
@router.get("/health")
async def health_check():
    """API健康检查"""
    return {
        "status": "healthy",
        "timestamp": "2024-01-01T00:00:00",
        "version": "1.0.0"
    }
