<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <groupId>com.google.code.gson</groupId>
  <artifactId>gson-parent</artifactId>
  <version>2.10.1</version>
  <packaging>pom</packaging>

  <name>Gson Parent</name>
  <description>Gson JSON library</description>
  <url>https://github.com/google/gson</url>

  <modules>
    <module>gson</module>
    <module>extras</module>
    <module>metrics</module>
    <module>proto</module>
  </modules>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <maven.compiler.release>7</maven.compiler.release>
  </properties>

  <scm>
    <url>https://github.com/google/gson/</url>
    <connection>scm:git:https://github.com/google/gson.git</connection>
    <developerConnection>scm:git:**************:google/gson.git</developerConnection>
    <tag>gson-parent-2.10.1</tag>
  </scm>

  <developers>
    <developer>
      <id>google</id>
      <organization>Google</organization>
      <organizationUrl>https://www.google.com</organizationUrl>
    </developer>
  </developers>

  <issueManagement>
    <system>GitHub Issues</system>
    <url>https://github.com/google/gson/issues</url>
  </issueManagement>

  <licenses>
    <license>
      <name>Apache-2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0.txt</url>
    </license>
  </licenses>

  <distributionManagement>
    <repository>
      <id>sonatype-nexus-staging</id>
      <name>Nexus Release Repository</name>
      <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
    </repository>
  </distributionManagement>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>junit</groupId>
        <artifactId>junit</artifactId>
        <version>4.13.2</version>
        <scope>test</scope>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-compiler-plugin</artifactId>
          <version>3.10.1</version>
          <configuration>
            <showWarnings>true</showWarnings>
            <showDeprecation>true</showDeprecation>
            <failOnWarning>true</failOnWarning>
            <compilerArgs>
              <!-- Enable all warnings, except for ones which cause issues when building with newer JDKs, see also
                https://docs.oracle.com/en/java/javase/11/tools/javac.html -->
              <compilerArg>-Xlint:all,-options</compilerArg>
            </compilerArgs>
            <jdkToolchain>
              <version>[11,)</version>
            </jdkToolchain>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-javadoc-plugin</artifactId>
          <version>3.4.1</version>
          <configuration>
            <jdkToolchain>
              <version>[11,)</version>
            </jdkToolchain>
            <!-- Specify newer JDK as target to allow linking to newer Java API, and to generate
              module overview in Javadoc for Gson's module descriptor -->
            <release>11</release>
            <!-- Exclude `missing` group because some tags have been omitted when they are redundant -->
            <doclint>all,-missing</doclint>
            <!-- Link against newer Java API Javadoc because most users likely 
              use a newer Java version than the one used for building this project -->
            <detectJavaApiLink>false</detectJavaApiLink>
            <links>
              <link>https://docs.oracle.com/en/java/javase/11/docs/api/</link>
            </links>
            <!-- Disable detection of offline links between Maven modules: 
              (1) Only `gson` module is published, so for other modules Javadoc links don't 
              matter much at the moment; (2) The derived URL for the modules is based on 
              the project URL (= Gson GitHub repo) which is incorrect because it is not 
              hosting the Javadoc (3) It might fail due to https://bugs.openjdk.java.net/browse/JDK-8212233 -->
            <detectOfflineLinks>false</detectOfflineLinks>
            <!-- Only show warnings and errors -->
            <quiet>true</quiet>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-jar-plugin</artifactId>
          <version>3.3.0</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-source-plugin</artifactId>
          <version>3.2.1</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-gpg-plugin</artifactId>
          <version>3.0.1</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-release-plugin</artifactId>
          <version>3.0.0-M7</version>
          <configuration>
            <autoVersionSubmodules>true</autoVersionSubmodules>
            <!-- Disable Maven Super POM release profile and instead use own one -->
            <useReleaseProfile>false</useReleaseProfile>
            <releaseProfiles>release</releaseProfiles>
            <!-- Run custom goals to replace version references, see plugin configuration below -->
            <!-- Also run `verify` to make sure tests still pass with new version number;
              also seems to be necessary because without `package`, goals fail for modules depending
              on each other; possibly same issue as https://issues.apache.org/jira/browse/MRELEASE-271 -->
            <preparationGoals>
              clean verify
              antrun:run@replace-version-placeholders
              antrun:run@replace-old-version-references
              antrun:run@git-add-changed
            </preparationGoals>
          </configuration>
        </plugin>
        <plugin>
          <artifactId>maven-antrun-plugin</artifactId>
          <version>3.1.0</version>
          <executions>
            <!-- Replaces version placeholders with the current version; this is mainly useful for
              Javadoc where this allows writing `@since $next-version$` -->
            <execution>
              <id>replace-version-placeholders</id>
              <goals>
                <goal>run</goal>
              </goals>
              <configuration>
                <target>
                  <replace token="$next-version$" value="${project.version}" encoding="${project.build.sourceEncoding}">
                    <!-- erroronmissingdir=false for gson-parent which does not have source directory -->
                    <fileset dir="${project.build.sourceDirectory}" includes="**" erroronmissingdir="false" />
                  </replace>
                </target>
              </configuration>
            </execution>
            <!-- Replaces references to the old version in the documentation -->
            <execution>
              <id>replace-old-version-references</id>
              <goals>
                <goal>run</goal>
              </goals>
              <configuration>
                <target>
                  <!-- Replace Maven and Gradle version references; uses regex lookbehind and lookahead -->
                  <replaceregexp match="(?&lt;=&lt;version&gt;).*(?=&lt;/version&gt;)|(?&lt;='com\.google\.code\.gson:gson:).*(?=')" flags="g" replace="${project.version}" encoding="${project.build.sourceEncoding}">
                    <fileset dir="${project.basedir}">
                      <include name="README.md" />
                      <include name="UserGuide.md" />
                    </fileset>
                  </replaceregexp>
                </target>
              </configuration>
              <!-- Only has to be executed for parent project; don't inherit this to modules -->
              <!-- This might be a bit hacky; execution with this ID seems to be missing for modules and Maven just executes default
                configuration which does not have any targets configured. (not sure if this behavior is guaranteed) -->
              <inherited>false</inherited>
            </execution>
            <!-- Adds changed files to the Git index; workaround because Maven Release Plugin does not support committing
              additional files yet (https://issues.apache.org/jira/browse/MRELEASE-798), and for workarounds with
              Maven SCM Plugin it is apparently necessary to know modified files in advance -->
            <!-- Maven Release Plugin then just happens to include these changed files in its Git commit;
              not sure if this behavior is guaranteed or if this relies on implementation details -->
            <execution>
              <id>git-add-changed</id>
              <goals>
                <goal>run</goal>
              </goals>
              <configuration>
                <target>
                  <exec executable="git" dir="${project.basedir}" failonerror="true">
                    <arg value="add" />
                    <!-- Don't add (unrelated) not yet tracked files -->
                    <arg value="--update" />
                    <arg value="." />
                  </exec>
                </target>
              </configuration>
            </execution>
          </executions>
        </plugin>
        <!-- Plugin for checking source and binary compatibility; used by GitHub workflow -->
        <plugin>
          <groupId>com.github.siom79.japicmp</groupId>
          <artifactId>japicmp-maven-plugin</artifactId>
          <version>0.17.1</version>
          <configuration>
            <oldVersion>
              <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>${project.artifactId}</artifactId>
                <!-- This is set by the GitHub workflow -->
                <version>JAPICMP-OLD</version>
              </dependency>
            </oldVersion>
            <newVersion>
              <file>
                <path>${project.build.directory}/${project.build.finalName}.${project.packaging}</path>
              </file>
            </newVersion>
            <parameter>
              <breakBuildOnSourceIncompatibleModifications>true</breakBuildOnSourceIncompatibleModifications>
              <breakBuildOnBinaryIncompatibleModifications>true</breakBuildOnBinaryIncompatibleModifications>
              <excludes>
                <exclude>com.google.gson.internal</exclude>
              </excludes>
              <onlyModified>true</onlyModified>
              <skipXmlReport>true</skipXmlReport>
              <reportOnlyFilename>true</reportOnlyFilename>
            </parameter>
          </configuration>
        </plugin>
      </plugins>
    </pluginManagement>
  </build>

  <profiles>
    <!-- Profile defining additional plugins to be executed for release -->
    <profile>
      <id>release</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-source-plugin</artifactId>
            <executions>
              <execution>
                <id>attach-sources</id>
                <goals>
                  <goal>jar-no-fork</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-javadoc-plugin</artifactId>
            <executions>
              <execution>
                <id>attach-javadocs</id>
                <goals>
                  <goal>jar</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-gpg-plugin</artifactId>
            <executions>
              <execution>
                <id>sign-artifacts</id>
                <phase>verify</phase>
                <goals>
                  <goal>sign</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>
</project>
