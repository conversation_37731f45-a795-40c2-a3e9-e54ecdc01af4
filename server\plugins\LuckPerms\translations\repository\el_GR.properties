luckperms.logs.actionlog-prefix=LOG
luckperms.logs.verbose-prefix=VB
luckperms.logs.export-prefix=ΕΞΑΓΩΓΗ
luckperms.commandsystem.available-commands=Χρησιμοποιήστε {0} για να δείτε τις διαθέσιμες εντολές
luckperms.commandsystem.command-not-recognised=Η εντολή δεν αναγνωρίζεται
luckperms.commandsystem.no-permission=Δεν έχεις άδεια για να εκτελέσεις αυτή την εντολή\!
luckperms.commandsystem.no-permission-subcommands=Δεν έχεις άδεια για να εκτελέσεις καμία από τις δευτερεύουσες εντολές
luckperms.commandsystem.already-executing-command=Μια άλλη εντολή εκτελείται, περιμένει να τελειώσει...
luckperms.commandsystem.usage.sub-commands-header=Δευτερεύουσες εντολές.
luckperms.commandsystem.usage.usage-header=Χρήση εντολών
luckperms.commandsystem.usage.arguments-header=Παράμετροι
luckperms.first-time.no-permissions-setup=Φαίνεται πως καμιά άδεια δεν έχει σεταριστεί ακόμα\!
luckperms.first-time.use-console-to-give-access=Πριν μπορέσεις να εκτελέσεις οποιαδήποτε εντολή του LuckPerms στο παιχνίδι, πρέπει να χρησιμοποιήσεις την κονσόλα για να δώσεις άδεια στον εαυτό σου
luckperms.first-time.console-command-prompt=Άνοιξε την κονσόλα σου και εκτέλεσε την εντολή
luckperms.first-time.next-step=Αφού το κάνεις αυτό, μπορείς να αρχίσεις να ορίζεις τις εκχωρήσεις και τις ομάδες αδειών σου
luckperms.first-time.wiki-prompt=Δεν ξέρεις απο που να ξεκινήσεις? Πάτα εδώ\: {0}
luckperms.login.try-again=Παρακαλώ δοκιμάστε ξανά αργότερα
luckperms.login.loading-database-error=Παρουσιάστηκε ένα σφάλμα της βάσης δεδομένων κατά τη φόρτωση των αδειών
luckperms.login.server-admin-check-console-errors=Εάν είστε διαχειριστής του server, ελέγξτε την κονσόλα για τυχόν σφάλματα
luckperms.login.server-admin-check-console-info=Ελέγξτε την κονσόλα του server περισσότερες πληροφορίες
luckperms.login.data-not-loaded-at-pre=Τα δεδομένα αδειών για τον χρήστη σας δεν φορτώθηκαν κατά το στάδιο πριν από την είσοδο
luckperms.login.unable-to-continue=Δεν μπορείτε να συνεχίσετε
luckperms.login.craftbukkit-offline-mode-error=Αυτό πιθανότατα οφείλεται σε αντίθεση μεταξύ του CraftBukkit και της ρύθμισης λειτουργίας σε απευθείας σύνδεση
luckperms.login.unexpected-error=Παρουσιάστηκε μη αναμενόμενο σφάλμα κατά τη ρύθμιση των δεδομένων αδειών σας
luckperms.opsystem.disabled=Το σύστημα OP vanilla είναι απενεργοποιημένο σε αυτόν τον server
luckperms.opsystem.sponge-warning=Λάβετε υπόψη ότι η κατάσταση του Διαχειριστή του server δεν επηρεάζει τους ελέγχους δικαιωμάτων στο Sponge όταν είναι εγκατεστημένο ένα plugin δικαιωμάτων, πρέπει να επεξεργαστείτε απευθείας τα δεδομένα χρήστη
luckperms.duration.unit.years.plural={0} χρόνια
luckperms.duration.unit.years.singular={0} χρόνο
luckperms.duration.unit.years.short={0}χ
luckperms.duration.unit.months.plural={0} μήνες
luckperms.duration.unit.months.singular={0} μήνας
luckperms.duration.unit.months.short={0}μ
luckperms.duration.unit.weeks.plural={0} εβδομάδες
luckperms.duration.unit.weeks.singular={0} εβδομάδα
luckperms.duration.unit.weeks.short={0}εβδ
luckperms.duration.unit.days.plural={0} μέρες
luckperms.duration.unit.days.singular={0} μέρα
luckperms.duration.unit.days.short={0}μ
luckperms.duration.unit.hours.plural={0} ώρες
luckperms.duration.unit.hours.singular={0} ώρα
luckperms.duration.unit.hours.short={0}ω
luckperms.duration.unit.minutes.plural={0} λεπτά
luckperms.duration.unit.minutes.singular={0} λεπτό
luckperms.duration.unit.minutes.short={0}λ
luckperms.duration.unit.seconds.plural={0} δευτερόλεπτα
luckperms.duration.unit.seconds.singular={0} δευτερόλεπτο
luckperms.duration.unit.seconds.short={0}δ
luckperms.duration.since={0} πριν
luckperms.command.misc.invalid-code=Μη έγκυρος κωδικός
luckperms.command.misc.response-code-key=Κωδικός Απάντησης
luckperms.command.misc.error-message-key=μήνυμα
luckperms.command.misc.bytebin-unable-to-communicate=Δεν είναι δυνατή η επικοινωνία με το bytebin
luckperms.command.misc.webapp-unable-to-communicate=Δεν είναι δυνατή η επικοινωνία με το web app
luckperms.command.misc.check-console-for-errors=Ελέγξτε την κονσόλα για σφάλματα
luckperms.command.misc.file-must-be-in-data=Το αρχείο {0} πρέπει να είναι άμεσο θυγατρικό του καταλόγου δεδομένων
luckperms.command.misc.wait-to-finish=Περιμένετέ το να τελειώσει και δοκιμάστε ξανά
luckperms.command.misc.invalid-priority=Μη έγκυρη προτεραιότητα {0}
luckperms.command.misc.expected-number=Αναμενόταν ένας αριθμός
luckperms.command.misc.date-parse-error=Δεν ήταν δυνατή η ανάλυση της ημερομηνίας {0}
luckperms.command.misc.date-in-past-error=Δεν μπορείτε να ορίσετε ημερομηνία στο παρελθόν\!
luckperms.command.misc.page=σελίδα {0} από {1}
luckperms.command.misc.page-entries={0} καταχωρήσεις
luckperms.command.misc.none=Κανένα
luckperms.command.misc.loading.error.unexpected=Προέκυψε ένα απρόσμενο σφάλμα
luckperms.command.misc.loading.error.user=Ο χρήστης δεν φορτώθηκε
luckperms.command.misc.loading.error.user-specific=Δεν είναι δυνατή η φόρτωση του χρήστη-στόχου {0}
luckperms.command.misc.loading.error.user-not-found=Δεν ήταν δυνατή η εύρεση ενός χρήστη για το {0}
luckperms.command.misc.loading.error.user-save-error=Παρουσιάστηκε σφάλμα κατά την αποθήκευση δεδομένων χρήστη για {0}
luckperms.command.misc.loading.error.user-not-online=Ο χρήστης {0} δεν είναι διαθέσιμος
luckperms.command.misc.loading.error.user-invalid=Το {0} δεν είναι έγκυρο όνομα χρήστη/uuid
luckperms.command.misc.loading.error.user-not-uuid=Ο χρήστης στόχευσης {0} δεν είναι έγκυρο uuid
luckperms.command.misc.loading.error.group=Η ομάδα δεν φορτώθηκε
luckperms.command.misc.loading.error.all-groups=Δεν είναι δυνατή η φόρτωση όλων των ομάδων
luckperms.command.misc.loading.error.group-not-found=Μια ομάδα που ονομάζεται {0} δεν βρέθηκε
luckperms.command.misc.loading.error.group-save-error=Υπήρξε ένα σφάλμα κατα την αποθήκευσή τω δεδομένα της ομάδας για {0}
luckperms.command.misc.loading.error.group-invalid={0} δεν είναι ένα έγκυρο όνομα ομάδας
luckperms.command.misc.loading.error.track=Η προτεραιότητα δεν φορτώθηκε
luckperms.command.misc.loading.error.all-tracks=Δεν ειναι δυνατή η φόρτωση όλων των προτεραιοτήτων
luckperms.command.misc.loading.error.track-not-found=Μια προτεραιότητα που ονομάζεται {0} δεν βρέθηκε
luckperms.command.misc.loading.error.track-save-error=Υπήρξε ένα σφάλμα κατα την αποθήκευσή τω δεδομένα της προτεραιότας για {0}
luckperms.command.misc.loading.error.track-invalid={0} δεν είναι ένα έγκυρο όνομα προτεραιότητας
luckperms.command.editor.no-match=Δεν είναι δυνατό το άνοιγμα του προγράμματος επεξεργασίας, κανένα αντικείμενο δεν ταιριάζει με τον επιθυμητό τύπο
luckperms.command.editor.start=Προετοιμασία νέας συνόδου επεξεργαστή, περιμένετε...
luckperms.command.editor.url=Κάντε κλικ στον παρακάτω σύνδεσμο για να ανοίξετε το πρόγραμμα επεξεργασίας
luckperms.command.editor.unable-to-communicate=Δεν είναι δυνατή η επικοινωνία με το προγραμμα επεξεργασίας
luckperms.command.editor.apply-edits.success=Τα δεδομένα του προγράμματος επεξεργασίας ιστού εφαρμόστηκαν με επιτυχία στο {0} {1}
luckperms.command.editor.apply-edits.success-summary={0} {1} και {2} {3}
luckperms.command.editor.apply-edits.success.additions=προσθήκες
luckperms.command.editor.apply-edits.success.additions-singular=προσθήκη
luckperms.command.editor.apply-edits.success.deletions=διαγραφές
luckperms.command.editor.apply-edits.success.deletions-singular=διαγραφή
luckperms.command.editor.apply-edits.no-changes=Δεν εφαρμόστηκαν αλλαγές από τον επεξεργαστή ιστού, τα δεδομένα που επιστράφηκαν δεν περιείχαν τροποποιήσεις
luckperms.command.editor.apply-edits.unknown-type=Δεν είναι δυνατή η εφαρμογή επεξεργασίας στον καθορισμένο τύπο αντικειμένου
luckperms.command.editor.apply-edits.unable-to-read=Δεν είναι δυνατή η ανάγνωση δεδομένων χρησιμοποιώντας τον δεδομένο κωδικό
luckperms.command.search.searching.permission=Αναζήτηση χρηστών και ομάδων με {0}
luckperms.command.search.searching.inherit=Αναζήτηση χρηστών και ομάδων που κληρονομούν από το {0}
luckperms.command.search.result=Βρέθηκαν {0} καταχωρίσεις από {1} χρήστες και {2} ομάδες
luckperms.command.search.result.default-notice=Σημείωση\: κατά την αναζήτηση μελών της προεπιλεγμένης ομάδας, δεν θα εμφανίζονται παίκτες εκτός σύνδεσης χωρίς άλλες άδειες\!
luckperms.command.search.showing-users=Εμφάνιση καταχωρίσεων χρήστη
luckperms.command.search.showing-groups=Εμφάνιση καταχωρίσεων ομάδας
luckperms.command.tree.start=Δημιουργία δέντρου αδειών, περιμένετε...
luckperms.command.tree.empty=Δεν ήταν δυνατή η δημιουργία δέντρου, δεν βρέθηκαν αποτελέσματα
luckperms.command.tree.url=URL δέντρου αδειών
luckperms.command.verbose.invalid-filter=Το {0} δεν είναι έγκυρο verbose φίλτρο
luckperms.command.verbose.enabled=Καταγραφέας αδειών {0} για ελέγχους που αντιστοιχούν {1}
luckperms.command.verbose.command-exec=Αναγκάζοντας την {0} εκτέλεση της εντολής {1} και την αναφορά όλων των ελέγχων που έγιναν...
luckperms.command.verbose.off=Καταγραφή αδειών {0}
luckperms.command.verbose.command-exec-complete=Η εκτέλεση της εντολής ολοκληρώθηκε
luckperms.command.verbose.command.no-checks=Η εκτέλεση της εντολής ολοκληρώθηκε, αλλά δεν έγιναν έλεγχοι αδειών
luckperms.command.verbose.command.possibly-async=Αυτό μπορεί να οφείλεται στο γεγονός ότι το πρόσθετο εκτελεί εντολές στο παρασκήνιο (async)
luckperms.command.verbose.command.try-again-manually=Μπορείτε ακόμα να χρησιμοποιήσετε το ρήμα χειροκίνητα για να εντοπίσετε ελέγχους που γίνονται έτσι
luckperms.command.verbose.enabled-recording=Καταγραφή αδειών {0} για ελέγχους που αντιστοιχούν {1}
luckperms.command.verbose.uploading=Καταγραφή αδειών {0}, ανέβασμα αποτελεσμάτων...
luckperms.command.verbose.url=Διεύθυνση URL αποτελεσμάτω του καταγραφέα αδειών
luckperms.command.verbose.enabled-term=ενεργό
luckperms.command.verbose.disabled-term=απενεργοποιημένο
luckperms.command.verbose.query-any=ΟΠΟΙΟΔΗΠΟΤΕ
luckperms.command.info.running-plugin=Λειτουργεί
luckperms.command.info.platform-key=Πλατφόρμα
luckperms.command.info.server-brand-key=Επωνυμία του server
luckperms.command.info.server-version-key=Έκδοση του server
luckperms.command.info.storage-key=Αποθηκευτικός χώρος
luckperms.command.info.storage-type-key=Τύπος
luckperms.command.info.storage.meta.split-types-key=Τύποι
luckperms.command.info.storage.meta.ping-key=Πινγκ
luckperms.command.info.storage.meta.connected-key=Συνδεδεμένος
luckperms.command.info.storage.meta.file-size-key=Μέγεθος αρχείου
luckperms.command.info.extensions-key=Επεκτάσεις
luckperms.command.info.messaging-key=Μηνύματα
luckperms.command.info.instance-key=Παράδειγμα
luckperms.command.info.static-contexts-key=Στατικά πλαίσια
luckperms.command.info.online-players-key=Συνδεδεμένοι παίκτες
luckperms.command.info.online-players-unique={0} μοναδικό
luckperms.command.info.uptime-key=Χρόνος λειτουργίας
luckperms.command.info.local-data-key=Τοπικά δεδομένα
luckperms.command.info.local-data={0} χρήστες, {1} ομάδες, {2} κομμάτια
luckperms.command.generic.create.success={0} δημιουργήθηκε με επιτυχία
luckperms.command.generic.create.error=Υπήρξε σφάλμα κατά την δημιουργεία {0}
luckperms.command.generic.create.error-already-exists={0} υπάρχει ήδη\!
luckperms.command.generic.delete.success={0} διαγράφηκε με επιτυχία
luckperms.command.generic.delete.error=Υπήρξε σφάλμα κατά την διαγραφή {0}
luckperms.command.generic.delete.error-doesnt-exist={0} δεν υπάρχει\!
luckperms.command.generic.rename.success={0} μετονομάστηκε σε {1} με επιτυχία
luckperms.command.generic.clone.success={0} αντιγράφτηκε σε {1} με επιτυχία
luckperms.command.generic.info.parent.title=Γονικές ομάδες
luckperms.command.generic.info.parent.temporary-title=Προσωρινές Γονικές Ομάδες
luckperms.command.generic.info.expires-in=λήγει σε
luckperms.command.generic.info.inherited-from=κληρονομήθηκε από
luckperms.command.generic.info.inherited-from-self=εαυτός
luckperms.command.generic.show-tracks.title=Οι προτεραιότητες του/των {0}
luckperms.command.generic.show-tracks.empty={0} δεν είναι κάποια προτεραιότητα
luckperms.command.generic.clear.node-removed=καταργήθηκαν {0} κόμβοι
luckperms.command.generic.clear.node-removed-singular=καταργήθηκε {0} κόμβος
luckperms.command.generic.clear=Οι κόμβοι του {0} διαγράφηκαν στο περιβάλλον {1}
luckperms.command.generic.permission.info.title=Άδειες του {0}
luckperms.command.generic.permission.info.empty=Δεν έχoυν οριστεί δικαιώματα για {0}
luckperms.command.generic.permission.info.click-to-remove=Κάντε κλικ για να καταργήσετε αυτόν τον κόμβο από {0}
luckperms.command.generic.permission.check.info.title=Δικαιώματα
luckperms.command.generic.permission.check.info.directly=Το {0} έχει άδεια {1} οριστεί σε {2} στο περιβάλλον {3}
luckperms.command.generic.permission.check.info.inherited={0} κληρονομεί {1} ορίστηκε σε {2} από {3} στο πλαίσιο {4}
luckperms.command.generic.permission.check.info.not-directly={0} does not have any tags. (Automatic Copy)
luckperms.command.generic.permission.check.info.not-inherited={0} δεν κληρονομεί {1}
luckperms.command.generic.permission.check.result.title=Έλεγχος δικαιωμάτων για {0}
luckperms.command.generic.permission.check.result.result-key=Αποτέλεσμα
luckperms.command.generic.permission.check.result.processor-key=Επεξεργαστής
luckperms.command.generic.permission.check.result.cause-key=Αιτία
luckperms.command.generic.permission.check.result.context-key=Περιγραφή
luckperms.command.generic.permission.set=Ορίστε {0} σε {1} για {2} σε περιβάλλον {3}
luckperms.command.generic.permission.already-has=Το {0} έχει ήδη {1} ρυθμιστεί σε περιβάλλον {2}
luckperms.command.generic.permission.set-temp=Ορίστε {0} σε {1} για {2} για διάρκεια {3} σε περιβάλλον {4}
luckperms.command.generic.permission.already-has-temp=Το {0} έχει ήδη {1} οριστεί προσωρινά σε περιβάλλον {2}
luckperms.command.generic.permission.unset=Απεγκατάσταση {0} για {1} σε περιβάλλον {2}
luckperms.command.generic.permission.doesnt-have=Το {0} δεν έχει {1} οριστεί στο περιβάλλον {2}
luckperms.command.generic.permission.unset-temp=Ορισμός προσωρινής άδειας {0} για {1} στο περιβάλλον {2}
luckperms.command.generic.permission.subtract=Ορισμός {0} σε {1} για {2} για διάρκεια {3} σε περιβάλλον {4}, {5} λιγότερο από ό, τι πριν
luckperms.command.generic.permission.doesnt-have-temp=Το {0} έχει ήδη {1} οριστεί προσωρινά σε περιβάλλον {2}
luckperms.command.generic.permission.clear=Οι κόμβοι του {0} διαγράφηκαν στο περιβάλλον {1}
luckperms.command.generic.parent.info.title=Γονείς {0}
luckperms.command.generic.parent.info.empty=Δεν έχoυν οριστεί δικαιώματα για {0}
luckperms.command.generic.parent.info.click-to-remove=Κάντε κλικ για να καταργήσετε αυτόν τον κόμβο από {0}
luckperms.command.generic.parent.add={0} τώρα κληρονομεί δικαιώματα από {1} στο πλαίσιο {2}
luckperms.command.generic.parent.add-temp={0} τώρα κληρονομεί δικαιώματα από {1} για μια διάρκεια {2} στο πλαίσιο {3}
luckperms.command.generic.parent.set={0} είχαν εκκαθαριστεί οι υπάρχουσες γονικές ομάδες τους στην προτεραιότητα {1} και τώρα κληρονομούν μόνο {2} στο περιβάλλον {3}
luckperms.command.generic.parent.set-track={0} είχαν εκκαθαριστεί οι υπάρχουσες γονικές ομάδες τους στην προτεραιότητα {1} και τώρα κληρονομούν μόνο {2} στο περιβάλλον {3}
luckperms.command.generic.parent.remove={0} τώρα κληρονομεί δικαιώματα από {1} στο πλαίσιο {2}
luckperms.command.generic.parent.remove-temp=δεν μεταβιβάζει προσωρινά δικαιώματα από το {1} στο περιβάλλον {2}
luckperms.command.generic.parent.subtract={0} τώρα κληρονομεί δικαιώματα από {1} για μια διάρκεια {2} στο πλαίσιο {3}
luckperms.command.generic.parent.clear=Οι κόμβοι του {0} διαγράφηκαν στο περιβάλλον {1}
luckperms.command.generic.parent.clear-track=Οι γονείς του {0} στην προτεραιότητα {1} διαγράφηκαν στο περιβάλλον {2}
luckperms.command.generic.parent.already-inherits=Το {0} έχει ήδη {1} ρυθμιστεί σε περιβάλλον {2}
luckperms.command.generic.parent.doesnt-inherit=Το {0} δεν έχει {1} οριστεί στο περιβάλλον {2}
luckperms.command.generic.parent.already-temp-inherits=Το {0} έχει ήδη {1} ρυθμιστεί σε περιβάλλον {2}
luckperms.command.generic.parent.doesnt-temp-inherit=Το {0} έχει ήδη {1} ρυθμιστεί σε περιβάλλον {2}
luckperms.command.generic.chat-meta.info.title-prefix={0}''s Prefixes
luckperms.command.generic.chat-meta.info.title-suffix={0}''s Suffixes
luckperms.command.generic.chat-meta.info.none-prefix={0} δεν έχει προθέματα
luckperms.command.generic.chat-meta.info.none-suffix={0} δεν έχει προθέματα
luckperms.command.generic.chat-meta.info.click-to-remove=Κάντε κλικ για να καταργήσετε αυτόν τον κόμβο από {0}
luckperms.command.generic.chat-meta.already-has={0} έχει ήδη {1} {2} οριστεί σε προτεραιότητα {3} στο πλαίσιο {4}
luckperms.command.generic.chat-meta.already-has-temp={0} έχει ήδη {1} {2} οριστεί προσωρινά σε προτεραιότητα {3} στο πλαίσιο {4}
luckperms.command.generic.chat-meta.doesnt-have={0} έχει ήδη {1} {2} οριστεί σε προτεραιότητα {3} στο πλαίσιο {4}
luckperms.command.generic.chat-meta.doesnt-have-temp={0} έχει ήδη {1} {2} οριστεί προσωρινά σε προτεραιότητα {3} στο πλαίσιο {4}
luckperms.command.generic.chat-meta.add={0} έχει ήδη {1} {2} οριστεί σε προτεραιότητα {3} στο πλαίσιο {4}
luckperms.command.generic.chat-meta.add-temp={0} έχει ήδη {1} {2} οριστεί σε προτεραιότητα {3} στο πλαίσιο {4}
luckperms.command.generic.chat-meta.remove={0} έχει ήδη {1} {2} οριστεί σε προτεραιότητα {3} στο πλαίσιο {4}
luckperms.command.generic.chat-meta.remove-bulk={0} έχει ήδη {1} {2} οριστεί σε προτεραιότητα {3} στο πλαίσιο {4}
luckperms.command.generic.chat-meta.remove-temp={0} έχει ήδη {1} {2} οριστεί σε προτεραιότητα {3} στο πλαίσιο {4}
luckperms.command.generic.chat-meta.remove-temp-bulk={0} έχει ήδη {1} {2} οριστεί σε προτεραιότητα {3} στο πλαίσιο {4}
luckperms.command.generic.meta.info.title={0}''s Meta
luckperms.command.generic.meta.info.none={0} δεν έχει meta
luckperms.command.generic.meta.info.click-to-remove=Κάντε κλικ για να καταργήσετε αυτόν τον κόμβο από {0}
luckperms.command.generic.meta.already-has={0} έχει ήδη το meta κλειδί {1} οριστεί σε {2} στο πλαίσιο {3}
luckperms.command.generic.meta.already-has-temp={0} έχει ήδη το meta κλειδί {1} οριστεί σε {2} στο πλαίσιο {3}
luckperms.command.generic.meta.doesnt-have=Το {0} δεν έχει {1} οριστεί στο περιβάλλον {2}
luckperms.command.generic.meta.doesnt-have-temp=Το {0} έχει ήδη {1} οριστεί προσωρινά σε περιβάλλον {2}
luckperms.command.generic.meta.set=Ορίστε {0} σε {1} για {2} σε περιβάλλον {3}
luckperms.command.generic.meta.set-temp=Ορίστε {0} σε {1} για {2} για διάρκεια {3} σε περιβάλλον {4}
luckperms.command.generic.meta.unset=Απεγκατάσταση {0} για {1} σε περιβάλλον {2}
luckperms.command.generic.meta.unset-temp=Ορισμός προσωρινής άδειας {0} για {1} στο περιβάλλον {2}
luckperms.command.generic.meta.clear=Ο τύπος αντιστοίχισης meta {1} του {0}εκκαθαρίστηκε στο πλαίσιο {2}
luckperms.command.generic.contextual-data.title=Θεματική βοήθεια
luckperms.command.generic.contextual-data.mode.key=λειτουργία
luckperms.command.generic.contextual-data.mode.server=server
luckperms.command.generic.contextual-data.mode.active-player=Ενεργοί παίκτες
luckperms.command.generic.contextual-data.contexts-key=Περιγραφή
luckperms.command.generic.contextual-data.prefix-key=Πρόθεμα
luckperms.command.generic.contextual-data.suffix-key=Επίθεμα
luckperms.command.generic.contextual-data.primary-group-key=Κύρια Ομάδα
luckperms.command.generic.contextual-data.meta-key=Meta
luckperms.command.generic.contextual-data.null-result=Κανένα
luckperms.command.user.info.title=Πληρ. Χρήστη
luckperms.command.user.info.uuid-key=Αποκλειστικό αναγνωριστικό ταυτοποίησης (UUID)
luckperms.command.user.info.uuid-type-key=τύπος
luckperms.command.user.info.uuid-type.mojang=Mojang
luckperms.command.user.info.uuid-type.not-mojang=εκτός σύνδεσης
luckperms.command.user.info.status-key=Κατάσταση
luckperms.command.user.info.status.online=Σε απευθείας σύνδεση
luckperms.command.user.info.status.offline=Εκτός σύνδεσης
luckperms.command.user.removegroup.error-primary=Δεν μπορείτε να καταργήσετε έναν χρήστη από την κύρια ομάδα του
luckperms.command.user.primarygroup.not-member=Ο χρήστης {0} δεν ήταν ήδη μέλος του {1}, προσθέτοντάς τους τώρα
luckperms.command.user.primarygroup.already-has=\nΤο {0} έχει ήδη {1} οριστεί ως η κύρια ομάδα του
luckperms.command.user.primarygroup.warn-option=Προειδοποίηση\: Η κύρια μέθοδος υπολογισμού ομάδας που χρησιμοποιείται από αυτόν τον διακομιστή ({0}) ενδέχεται να μην αντικατοπτρίζει αυτήν την αλλαγή
luckperms.command.user.primarygroup.set=Η κύρια ομάδα του χρήστη {0} ορίστηκε σε {1}\n
luckperms.command.user.track.error-not-contain-group={0} δεν είναι ήδη σε καμία ομάδα στο {1}
luckperms.command.user.track.unsure-which-track=Δεν είστε βέβαιοι ποιο κομμάτι θα χρησιμοποιήσεις, προσδιορίστε το ως επιχείρημα
luckperms.command.user.track.missing-group-advice=Δημιουργήστε την ομάδα ή αφαιρέστε την από την προτεραιότητα και δοκιμάστε ξανά
luckperms.command.user.promote.added-to-first={0} δεν είναι σε καμία ομάδα στο {1}, οπότε προστέθηκαν στην πρώτη ομάδα, {2} στο πλαίσιο {3}
luckperms.command.user.promote.not-on-track={0} δεν είναι σε καμία ομάδα στο {1}, οπότε δεν προωθήθηκε
luckperms.command.user.promote.success=Προώθηση {0} κατά μήκος του κομματιού {1} από {2} σε {3} στο πλαίσιο {4}
luckperms.command.user.promote.end-of-track=Έφτασε το τέλος της προτεραιότητας {0}, δεν ήταν δυνατή η προώθηση {1}
luckperms.command.user.promote.next-group-deleted=Η επόμενη ομάδα στο κομμάτι, {0}, δεν υπάρχει πλέον
luckperms.command.user.promote.unable-to-promote=Αδυναμία προώθησης του χρήστη
luckperms.command.user.demote.success=Demoting {0} along track {1} from {2} to {3} in context {4} (Automatic Copy)
luckperms.command.user.demote.end-of-track=Έφτασε το τέλος της προτεραιότητας {0}, οπότε το {1} καταργήθηκε από το {2}
luckperms.command.user.demote.end-of-track-not-removed=Το τέλος του κομματιού {0} επιτεύχθηκε, αλλά {1} δεν αφαιρέθηκε από την πρώτη ομάδα
luckperms.command.user.demote.previous-group-deleted=Η προηγούμενη ομάδα στο κομμάτι, {0}, δεν υπάρχει πλέον
luckperms.command.user.demote.unable-to-demote=Δεν είναι δυνατή η κατάργηση αυτού του χρήστη
luckperms.command.group.list.title=Group current count
luckperms.command.group.delete.not-default=Δεν μπορείς να διαγράψεις την προεπιλεγμένη ομάδα.
luckperms.command.group.info.title=Πληροφορίες ομάδας
luckperms.command.group.info.display-name-key=Εμφανιζόμενο Όνομα
luckperms.command.group.info.weight-key=Βάρος
luckperms.command.group.setweight.set=Ορισμός βάρους σε {0} για την ομάδα {1}
luckperms.command.group.setdisplayname.doesnt-have={0} δεν έχει οριστεί όνομα εμφάνισης
luckperms.command.group.setdisplayname.already-has={0} έχει ήδη ένα εμφανιζόμενο όνομα {1}
luckperms.command.group.setdisplayname.already-in-use=Το εμφανιζόμενο όνομα {0} χρησιμοποιείται ήδη από {1}
luckperms.command.group.setdisplayname.set=Ορίστε το όνομα εμφάνισης σε {0} για την ομάδα {1} στο πλαίσιο {2}
luckperms.command.group.setdisplayname.removed=Ορίστε το όνομα εμφάνισης σε {0} για την ομάδα {1} στο πλαίσιο {2}
luckperms.command.track.list.title=Διαδρομές
luckperms.command.track.path.empty=Κανένα
luckperms.command.track.info.showing-track=Εμφάνιση Κομματιού
luckperms.command.track.info.path-property=Διαδρομή
luckperms.command.track.clear=το κομμάτι ομάδων του {0}διαγράφηκε
luckperms.command.track.append.success=Η ομάδα {0} προστέθηκε στο κομμάτι {1}
luckperms.command.track.insert.success=Η ομάδα {0} εισήχθη στο κομμάτι {1} στη θέση {2}
luckperms.command.track.insert.error-number=Αναμενόμενος αριθμός, αλλά αντί αυτού ελήφθη\: {0}
luckperms.command.track.insert.error-invalid-pos=Αδυναμία εισαγωγής στη θέση {0}
luckperms.command.track.insert.error-invalid-pos-reason=Μη έγκυρη θέση
luckperms.command.track.remove.success=Η ομάδα {0} αφαιρέθηκε από το κομμάτι {1}
luckperms.command.track.error-empty={0} δεν μπορεί να χρησιμοποιηθεί επειδή είναι άδειο ή περιέχει μόνο μία ομάδα
luckperms.command.track.error-multiple-groups={0} είναι μέλος πολλών ομάδων σε αυτό το κομμάτι
luckperms.command.track.error-ambiguous=Αδυναμία προσδιορισμού της τοποθεσίας τους
luckperms.command.track.already-contains={0} ήδη περιέχει {1}
luckperms.command.track.doesnt-contain=το {0} δεν περιέχει {1}
luckperms.command.log.load-error=Το αρχείο %s δεν ήταν εφικτό να φορτωθεί.
luckperms.command.log.invalid-page=Μη έγκυρος αριθμός σελίδας
luckperms.command.log.invalid-page-range=Παρακαλούμε μια έγκυρη τιμή μεταξύ {0} και {1}.
luckperms.command.log.empty=Δεν υπάρχουν λεπτομέρειες για προβολή
luckperms.command.log.notify.error-console=Αδυναμία εναλλαγής ειδοποιήσεων για την κονσόλα
luckperms.command.log.notify.enabled-term=Ενεργό
luckperms.command.log.notify.disabled-term=Απενεργοποιημένο
luckperms.command.log.notify.changed-state={0} καταγραφή εξόδου
luckperms.command.log.notify.already-on=Λαμβάνετε ήδη ειδοποιήσεις
luckperms.command.log.notify.already-off=Λαμβάνετε ήδη ειδοποιήσεις
luckperms.command.log.notify.invalid-state=Κατάσταση άγνωστη. Αναμένεται {0} ή {1}
luckperms.command.log.show.search=Εμφάνιση πρόσφατων ενεργειών για το ερώτημα {0}
luckperms.command.log.show.recent=Εμφάνιση πρόσφατων ενεργειών
luckperms.command.log.show.by=Εμφάνιση πρόσφατων ενεργειών για το ερώτημα {0}
luckperms.command.log.show.history=Εμφάνιση ιστορικού για {0} {1}
luckperms.command.export.error-term=Σφάλμα
luckperms.command.export.already-running=Ένα άλλο αντίγραφο του %@ εκτελείται ήδη.
luckperms.command.export.file.already-exists=Το αρχείο "%s" υπάρχει ήδη
luckperms.command.export.file.not-writable=Filesystem is not writable.
luckperms.command.export.file.success=Επιτυχής εξαγωγή στο {0}
luckperms.command.export.file-unexpected-error-writing=Παρουσιάστηκε μη αναμενόμενο σφάλμα κατά την εγγραφή στο αρχείο
luckperms.command.export.web.export-code=Εξαγωγή κωδικού
luckperms.command.export.web.import-command-description=Χρησιμοποιήστε την ακόλουθη εντολή για να εισαγάγετε
luckperms.command.import.term=Εισαγωγή
luckperms.command.import.error-term=Σφάλμα
luckperms.command.import.already-running=Ένα άλλο αντίγραφο του %@ εκτελείται ήδη.
luckperms.command.import.file.doesnt-exist={0} δεν υπάρχει
luckperms.command.import.file.not-readable=Filesystem is not writable
luckperms.command.import.file.unexpected-error-reading=Παρουσιάστηκε μη αναμενόμενο σφάλμα κατά την εγγραφή στο αρχείο
luckperms.command.import.file.correct-format=είναι η σωστή μορφή?
luckperms.command.import.web.unable-to-read=Δεν είναι δυνατή η ανάγνωση δεδομένων χρησιμοποιώντας τον δεδομένο κωδικό
luckperms.command.import.progress.percent={0} Ολοκληρώθηκε
luckperms.command.import.progress.operations={0}/{1} εργασίες ολοκληρώθηκαν
luckperms.command.import.starting=Έναρξη διαδικασίας εισαγωγής
luckperms.command.import.completed=COMPLETED
luckperms.command.import.duration=σε {0} δευτερόλεπτα
luckperms.command.bulkupdate.must-use-console=Η εντολή μαζικής ενημέρωσης μπορεί να χρησιμοποιηθεί μόνο από την κονσόλα
luckperms.command.bulkupdate.invalid-data-type=Μη έγκυρος τύπος, περίμενε {0}
luckperms.command.bulkupdate.invalid-constraint=Μη έγκυροι περιορισμοί
luckperms.command.bulkupdate.invalid-constraint-format=Περιορισμοί πρέπει να είναι σε μορφή {0}
luckperms.command.bulkupdate.invalid-comparison=Μη έγκυρος τελεστής σύγκρισης {0}
luckperms.command.bulkupdate.invalid-comparison-format=Αναμενόταν ένα από τα ακόλουθα\: {0}
luckperms.command.bulkupdate.queued=Λειτουργία μαζικής ενημέρωσης στην ουρά
luckperms.command.bulkupdate.confirm=Εκτέλεση {0} για εκτέλεση της ενημέρωσης
luckperms.command.bulkupdate.unknown-id=Ο σύνδεσμος είτε δεν υπάρχει είτε έχει λήξει
luckperms.command.bulkupdate.starting=Εκτελούμενη μαζική ενημέρωση
luckperms.command.bulkupdate.success=Η ενημέρωση ολοκληρώθηκε με επιτυχία
luckperms.command.bulkupdate.success.statistics.nodes=Χρήστες που επηρεάζονται
luckperms.command.bulkupdate.success.statistics.users=Χρήστες που επηρεάζονται
luckperms.command.bulkupdate.success.statistics.groups=Χρήστες που επηρεάζονται
luckperms.command.bulkupdate.failure=Η μαζική ενημέρωση απέτυχε, ελέγξτε την κονσόλα για σφάλματα
luckperms.command.update-task.request=Έχει ζητηθεί μια εργασία ενημέρωσης, παρακαλώ περιμένετε
luckperms.command.update-task.complete=Ενημέρωση ολοκληρώθηκε
luckperms.command.update-task.push.attempting=Τώρα προσπαθεί να ωθήσει σε άλλους διακομιστές
luckperms.command.update-task.push.complete=Άλλοι διακομιστές ειδοποιήθηκαν μέσω {0} με επιτυχία
luckperms.command.update-task.push.error=Σφάλμα κατά την προώθηση αλλαγών σε άλλους διακομιστές
luckperms.command.update-task.push.error-not-setup=Αδυναμία ώθησης αλλαγών σε άλλους διακομιστές καθώς μια υπηρεσία ανταλλαγής μηνυμάτων δεν έχει ρυθμιστεί
luckperms.command.reload-config.success=Δεν βρέθηκε αρχείο ρυθμίσεων configuration
luckperms.command.reload-config.restart-note=ορισμένες επιλογές θα εφαρμοστούν μόνο μετά την επανεκκίνηση του διακομιστή
luckperms.command.translations.searching=Αναζήτηση για διαθέσιμες μεταφράσεις, παρακαλώ περιμένετε...
luckperms.command.translations.searching-error=Δεν είναι δυνατή η απόκτηση μιας λίστας διαθέσιμων μεταφράσεων
luckperms.command.translations.installed-translations=Μεταφράσεις εγκατεστημένων module
luckperms.command.translations.available-translations=Διαθέσιμες μεταφράσεις
luckperms.command.translations.percent-translated={0}% μεταφράστηκε
luckperms.command.translations.translations-by=από
luckperms.command.translations.installing=Εγκαθίσταται ο πίνακας ελέγχου, παρακαλώ περιμένετε... 
luckperms.command.translations.download-error=Αδυναμία λήψης μετάφρασης για {0}
luckperms.command.translations.installing-specific=Εγκατάσταση γλώσσας
luckperms.command.translations.install-complete=Εγκατάσταση επιτυχής\!
luckperms.command.translations.download-prompt=Χρησιμοποιήστε {0} για να κατεβάσετε και να εγκαταστήσετε ενημερωμένες εκδόσεις αυτών των μεταφράσεων που παρέχονται από την κοινότητα
luckperms.command.translations.download-override-warning=Παρακαλώ σημειώστε ότι αυτό θα παρακάμψει τυχόν αλλαγές που έχετε κάνει για αυτές τις γλώσσες
luckperms.usage.user.description=Ένα σύνολο εντολών για τη διαχείριση των χρηστών μέσα στο LuckPerms. (Ένας ''χρήστης'' στο LuckPerms είναι απλά ένας παίκτης και μπορεί να αναφέρεται σε ένα UUID ή όνομα χρήστη)
luckperms.usage.group.description=Ένα σύνολο εντολών για τη διαχείριση των ομάδων στο LuckPerms. Οι ομάδες είναι απλά συλλογές εκχωρήσεων δικαιωμάτων που μπορούν να δοθούν στους χρήστες. Οι νέες ομάδες γίνονται χρησιμοποιώντας την εντολή ''creategroup''.
luckperms.usage.track.description=Ένα σύνολο εντολών για τη διαχείριση των κομματιών μέσα στο LuckPerms. Τα ίχνη είναι μια ταξινομημένη συλλογή ομάδων που μπορεί να χρησιμοποιηθεί για τον καθορισμό προωθητικών ενεργειών και αποστάσεων.
luckperms.usage.log.description=Ένα σύνολο εντολών για τη διαχείριση της λειτουργικότητας καταγραφής στο LuckPerms.
luckperms.usage.sync.description=Επαναφόρτωση όλων των δεδομένων από τον αποθηκευτικό χώρο των plugins στη μνήμη, και εφαρμογή τυχόν αλλαγών που ανιχνεύονται.
luckperms.usage.info.description=Εκτυπώνει γενικές πληροφορίες σχετικά με την παρουσία του ενεργού πρόσθετου.
luckperms.usage.editor.description=Δημιουργεί μια νέα συνεδρία επεξεργαστή ιστού
luckperms.usage.editor.argument.type=οι τύποι που θα φορτωθούν στον επεξεργαστή. (''όλα'', ''χρήστες'' ή ''ομάδες'')
luckperms.usage.editor.argument.filter=άδεια φιλτραρίσματος καταχωρήσεων χρήστη κατά
luckperms.usage.verbose.description=Ελέγχει το σύστημα παρακολούθησης ελέγχου αδειών των προσθέτων.
luckperms.usage.verbose.argument.action=αν θα ενεργοποιήσετε/απενεργοποιήσετε την καταγραφή, ή να ανεβάσετε την έξοδο που καταγράφηκε
luckperms.usage.verbose.argument.filter=το φίλτρο για αντιστοίχιση καταχωρήσεων με
luckperms.usage.verbose.argument.commandas=ο παίκτης/εντολή που θα εκτελεστεί
luckperms.usage.tree.description=Δημιουργεί μια προβολή δέντρου (ταξινομημένη ιεραρχία λίστας) όλων των δικαιωμάτων που είναι γνωστά στο LuckPerms.
luckperms.usage.tree.argument.scope=η ρίζα του δέντρου. καθορίστε "." για να συμπεριλάβετε όλα τα δικαιώματα
luckperms.usage.tree.argument.player=το όνομα ενός online παίκτη για να το ελέγξει
luckperms.usage.search.description=Αναζήτηση για όλους τους χρήστες/ομάδες με συγκεκριμένη άδεια
luckperms.usage.search.argument.permission=την άδεια για αναζήτηση
luckperms.usage.search.argument.page=η σελίδα προς προβολή
luckperms.usage.network-sync.description=Συγχρονισμός αλλαγών με τον αποθηκευτικό χώρο και αίτημα όλοι οι άλλοι διακομιστές του δικτύου να κάνουν το ίδιο
luckperms.usage.import.description=Εισάγει δεδομένα από αρχείο εξαγωγής (προηγουμένως δημιουργημένο)
luckperms.usage.import.argument.file=το αρχείο που θα εισαχθεί από
luckperms.usage.import.argument.replace=αντικατάσταση υπαρχόντων δεδομένων αντί συγχώνευσης
luckperms.usage.import.argument.upload=ανεβάστε τα δεδομένα από μια προηγούμενη εξαγωγή
luckperms.usage.export.description=Εξάγει όλα τα δεδομένα δικαιωμάτων σε ένα αρχείο ''εξαγωγής''. Μπορεί να επανεισαχθεί αργότερα.
luckperms.usage.export.argument.file=το αρχείο που θα εισαχθεί από
luckperms.usage.export.argument.without-users=αποκλεισμός χρηστών από την εξαγωγή
luckperms.usage.export.argument.without-groups=αποκλεισμός χρηστών από την εξαγωγή
luckperms.usage.export.argument.upload=Εξάγει όλα τα δεδομένα δικαιωμάτων σε ένα αρχείο ''εξαγωγής''. Μπορεί να επανεισαχθεί αργότερα.
luckperms.usage.reload-config.description=Επαναφόρτωση μερικών από τις επιλογές ρυθμίσεων
luckperms.usage.bulk-update.description=Εκτέλεση μαζικών ερωτήσεων αλλαγής σε όλα τα δεδομένα
luckperms.usage.bulk-update.argument.data-type=ο τύπος των δεδομένων που αλλάζουν. (''όλα'', ''χρήστες'' ή ''ομάδες'')
luckperms.usage.bulk-update.argument.action=την ενέργεια που θα εκτελεστεί στα δεδομένα. (''ενημέρωση'' ή ''διαγραφή'')
luckperms.usage.bulk-update.argument.action-field=το πεδίο για να ενεργήσετε. απαιτείται μόνο για ''ενημέρωση''. (''άδεια'', ''διακομιστής'' ή ''κόσμος'')
luckperms.usage.bulk-update.argument.action-value=η τιμή προς αντικατάσταση. απαιτείται μόνο για ''ενημέρωση''.
luckperms.usage.bulk-update.argument.constraint=τους περιορισμούς που απαιτούνται για την ενημέρωση
luckperms.usage.translations.description=Διαχείριση μεταφράσεων
luckperms.usage.translations.argument.install=subcommand για εγκατάσταση μεταφράσεων
luckperms.usage.apply-edits.description=Εφαρμόζει τις αλλαγές δικαιωμάτων που έγιναν από τον επεξεργαστή ιστού
luckperms.usage.apply-edits.argument.code=ο μοναδικός κωδικός για τα δεδομένα
luckperms.usage.apply-edits.argument.target=ποιος να εφαρμόσει τα δεδομένα
luckperms.usage.create-group.description=Δημιουργία νέας ομάδας
luckperms.usage.create-group.argument.name=Μετονομασία προϊόντος
luckperms.usage.create-group.argument.weight=Μετονομασία προϊόντος
luckperms.usage.create-group.argument.display-name=Το εμφανιζόμενο όνομα του παροχέα.
luckperms.usage.delete-group.description=Διαγραφή ομάδας
luckperms.usage.delete-group.argument.name=Μετονομασία προϊόντος
luckperms.usage.list-groups.description=Λίστα όλων των ομάδων στην πλατφόρμα
luckperms.usage.create-track.description=Δημιουργία νέας εντολής
luckperms.usage.create-track.argument.name=Μετονομασία προϊόντος
luckperms.usage.delete-track.description=Διαγραφή διαδρομής;
luckperms.usage.delete-track.argument.name=Μετονομασία προϊόντος
luckperms.usage.list-tracks.description=Λίστα όλων των ομάδων στην πλατφόρμα
luckperms.usage.user-info.description=Δείχνει τις πληροφορίες ενός διακομιστή
luckperms.usage.user-switchprimarygroup.description=Εναλλαγή της κύριας ομάδας του χρήστη
luckperms.usage.user-switchprimarygroup.argument.group=η ομάδα που θα οριστεί σε
luckperms.usage.user-promote.description=Προωθεί τον χρήστη προς τα πάνω ένα κομμάτι
luckperms.usage.user-promote.argument.track=το κομμάτι για την προώθηση του χρήστη
luckperms.usage.user-promote.argument.context=τα πλαίσια για τον ορισμό της ομάδας
luckperms.usage.user-promote.argument.dont-add-to-first=προώθηση του χρήστη μόνο αν βρίσκονται ήδη στο κομμάτι
luckperms.usage.user-demote.description=Προωθεί τον χρήστη προς τα πάνω ένα κομμάτι
luckperms.usage.user-demote.argument.track=το κομμάτι για την προώθηση του χρήστη
luckperms.usage.user-demote.argument.context=τα πλαίσια για τον ορισμό της ομάδας
luckperms.usage.user-demote.argument.dont-remove-from-first=αποτροπή της κατάργησης του χρήστη από την πρώτη ομάδα
luckperms.usage.user-clone.description=Κλωνοποίηση του χρήστη
luckperms.usage.user-clone.argument.user=το όνομα/uuid του χρήστη για φιλτράρισμα κατά
luckperms.usage.group-info.description=Δίνει πληροφορίες σχετικά με το κομμάτι
luckperms.usage.group-listmembers.description=Εμφάνιση των χρηστών/ομάδων που κληρονομούν από αυτή την ομάδα
luckperms.usage.group-listmembers.argument.page=η σελίδα προς προβολή
luckperms.usage.group-setweight.description=Ορισμός βάρους ομάδων
luckperms.usage.group-setweight.argument.weight=το κλειδί που θα ορίσετε
luckperms.usage.group-set-display-name.description=Enter the group display name
luckperms.usage.group-set-display-name.argument.name=η τιμή που θα οριστεί
luckperms.usage.group-set-display-name.argument.context=τα πλαίσια για τον ορισμό της ομάδας
luckperms.usage.group-rename.description=Μετονομασία προϊόντος
luckperms.usage.group-rename.argument.name=το νέο όνομα
luckperms.usage.group-clone.description=Κλωνοποίηση ομάδας
luckperms.usage.group-clone.argument.name=το όνομα της ομάδας για κλωνοποίηση
luckperms.usage.holder-editor.description=Ανοίγει τον επεξεργαστή δικαιωμάτων web
luckperms.usage.holder-showtracks.description=Παραθέτει τα κομμάτια στα οποία βρίσκεται το αντικείμενο
luckperms.usage.holder-clear.description=Αφαιρεί όλα τα δικαιώματα, τους γονείς και το meta
luckperms.usage.holder-clear.argument.context=τα συμφραζόμενα φιλτραρίσματος κατά
luckperms.usage.permission.description=Επεξεργασία αδειών
luckperms.usage.parent.description=Επεξεργασία κληρονομιάς
luckperms.usage.meta.description=Επεξεργασία προκαθορισμένων τιμών
luckperms.usage.permission-info.description=Εμφανίζει τους κόμβους δικαιωμάτων που έχει το αντικείμενο
luckperms.usage.permission-info.argument.page=η σελίδα προς προβολή
luckperms.usage.permission-info.argument.sort-mode=πώς να ταξινομήσετε τις καταχωρήσεις
luckperms.usage.permission-set.description=Ορίζει δικαιώματα για το αντικείμενο
luckperms.usage.permission-set.argument.node=ο κόμβος δικαιωμάτων για ορισμό
luckperms.usage.permission-set.argument.value=η τιμή του κόμβου
luckperms.usage.permission-set.argument.context=τα συμφραζόμενα για να προσθέσετε τα δικαιώματα
luckperms.usage.permission-unset.description=Ορίζει δικαιώματα για το αντικείμενο
luckperms.usage.permission-unset.argument.node=ο κόμβος δικαιωμάτων για ορισμό
luckperms.usage.permission-unset.argument.context=τα συμφραζόμενα για να προσθέσετε τα δικαιώματα
luckperms.usage.permission-settemp.description=Ορίζει δικαιώματα για το αντικείμενο
luckperms.usage.permission-settemp.argument.node=ο κόμβος δικαιωμάτων για ορισμό
luckperms.usage.permission-settemp.argument.value=η τιμή του κόμβου
luckperms.usage.permission-settemp.argument.duration=η διάρκεια μέχρι να λήξει ο κόμβος άδειας
luckperms.usage.permission-settemp.argument.temporary-modifier=πώς θα πρέπει να εφαρμόζεται η προσωρινή άδεια
luckperms.usage.permission-settemp.argument.context=τα συμφραζόμενα για να προσθέσετε τα δικαιώματα
luckperms.usage.permission-unsettemp.description=Ορίζει δικαιώματα για το αντικείμενο
luckperms.usage.permission-unsettemp.argument.node=ο κόμβος δικαιωμάτων για ορισμό
luckperms.usage.permission-unsettemp.argument.duration=η διάρκεια αφαίρεσης
luckperms.usage.permission-unsettemp.argument.context=τα συμφραζόμενα για να προσθέσετε τα δικαιώματα
luckperms.usage.permission-check.description=Ελέγχει για να δει αν το αντικείμενο έχει ένα συγκεκριμένο κόμβο δικαιωμάτων
luckperms.usage.permission-check.argument.node=την άδεια για αναζήτηση
luckperms.usage.permission-clear.description=Κατάργηση όλων των δικαιωμάτων
luckperms.usage.permission-clear.argument.context=τα συμφραζόμενα φιλτραρίσματος κατά
luckperms.usage.parent-info.description=Εμφανίζει τις ομάδες από τις οποίες προέρχεται αυτό το αντικείμενο
luckperms.usage.parent-info.argument.page=η σελίδα προς προβολή
luckperms.usage.parent-info.argument.sort-mode=πώς να ταξινομήσετε τις καταχωρήσεις
luckperms.usage.parent-set.description=Αφαιρεί όλες τις άλλες ομάδες που το αντικείμενο κληρονομεί ήδη και τις προσθέτει σε αυτή που δίνεται
luckperms.usage.parent-set.argument.group=η ομάδα που θα οριστεί σε
luckperms.usage.parent-set.argument.context=τα πλαίσια για τον ορισμό της ομάδας
luckperms.usage.parent-add.description=Ορίζει μια άλλη ομάδα από την οποία το αντικείμενο θα κληρονομήσει δικαιώματα
luckperms.usage.parent-add.argument.group=η ομάδα που θα κληρονομήσει από
luckperms.usage.parent-add.argument.context=τα πλαίσια για τον ορισμό της ομάδας
luckperms.usage.parent-remove.description=Αφαιρεί έναν προηγουμένως καθορισμένο κανόνα κληρονομιάς
luckperms.usage.parent-remove.argument.group=η ομάδα που θα οριστεί σε
luckperms.usage.parent-remove.argument.context=τα πλαίσια για τον ορισμό της ομάδας
luckperms.usage.parent-set-track.description=Αφαιρεί όλες τις άλλες ομάδες που το αντικείμενο κληρονομεί ήδη και τις προσθέτει σε αυτή που δίνεται
luckperms.usage.parent-set-track.argument.track=το κομμάτι που θα ρυθμιστεί
luckperms.usage.parent-set-track.argument.group=την ομάδα ή έναν αριθμό σχετικά με τη θέση της ομάδας στη συγκεκριμένη τροχιά
luckperms.usage.parent-set-track.argument.context=τα πλαίσια για τον ορισμό της ομάδας
luckperms.usage.parent-add-temp.description=Ορίζει μια άλλη ομάδα από την οποία το αντικείμενο θα κληρονομήσει δικαιώματα
luckperms.usage.parent-add-temp.argument.group=η ομάδα που θα κληρονομήσει από
luckperms.usage.parent-add-temp.argument.duration=τη διάρκεια της ιδιότητας μέλους του ομίλου
luckperms.usage.parent-add-temp.argument.temporary-modifier=πώς θα πρέπει να εφαρμόζεται η προσωρινή άδεια
luckperms.usage.parent-add-temp.argument.context=τα πλαίσια για τον ορισμό της ομάδας
luckperms.usage.parent-remove-temp.description=Αφαιρεί έναν προηγουμένως καθορισμένο κανόνα κληρονομιάς
luckperms.usage.parent-remove-temp.argument.group=η ομάδα που θα οριστεί σε
luckperms.usage.parent-remove-temp.argument.duration=η διάρκεια αφαίρεσης
luckperms.usage.parent-remove-temp.argument.context=τα πλαίσια για τον ορισμό της ομάδας
luckperms.usage.parent-clear.description=Καθαρίζει όλα τα meta
luckperms.usage.parent-clear.argument.context=τα συμφραζόμενα φιλτραρίσματος κατά
luckperms.usage.parent-clear-track.description=Καθαρίζει όλους τους γονείς σε ένα συγκεκριμένο κομμάτι
luckperms.usage.parent-clear-track.argument.track=το κομμάτι που θα ρυθμιστεί
luckperms.usage.parent-clear-track.argument.context=τα συμφραζόμενα φιλτραρίσματος κατά
luckperms.usage.meta-info.description=Εμφανίζει όλα τα meta συνομιλίας
luckperms.usage.meta-set.description=Ορίζει μια τιμή μεταδεδομένων
luckperms.usage.meta-set.argument.key=το κλειδί που θα ορίσετε
luckperms.usage.meta-set.argument.value=η τιμή που θα οριστεί
luckperms.usage.meta-set.argument.context=τα συμφραζόμενα για να προσθέσετε τα δικαιώματα
luckperms.usage.meta-unset.description=Ορίζει μια τιμή μεταδεδομένων
luckperms.usage.meta-unset.argument.key=το κλειδί που θα ορίσετε
luckperms.usage.meta-unset.argument.context=τα συμφραζόμενα για να προσθέσετε τα δικαιώματα
luckperms.usage.meta-settemp.description=Ορίζει προσωρινά μια τιμή μεταδεδομένων
luckperms.usage.meta-settemp.argument.key=το κλειδί που θα ορίσετε
luckperms.usage.meta-settemp.argument.value=η τιμή που θα οριστεί
luckperms.usage.meta-settemp.argument.duration=η διάρκεια μέχρι να λήξει ο κόμβος άδειας
luckperms.usage.meta-settemp.argument.context=τα συμφραζόμενα για να προσθέσετε τα δικαιώματα
luckperms.usage.meta-unsettemp.description=Απαγόρευση προσωρινής τιμής meta
luckperms.usage.meta-unsettemp.argument.key=το κλειδί που θα ορίσετε
luckperms.usage.meta-unsettemp.argument.context=τα συμφραζόμενα για να προσθέσετε τα δικαιώματα
luckperms.usage.meta-addprefix.description=Προσθέτει ένα πρόθεμα
luckperms.usage.meta-addprefix.argument.priority=την προτεραιότητα να αφαιρέσετε το πρόθεμα στο
luckperms.usage.meta-addprefix.argument.prefix=η συμβολοσειρά προθέματος
luckperms.usage.meta-addprefix.argument.context=τα πλαίσια για τον ορισμό της ομάδας
luckperms.usage.meta-addsuffix.description=Προσθέτει ένα πρόθεμα
luckperms.usage.meta-addsuffix.argument.priority=την προτεραιότητα να αφαιρέσετε το πρόθεμα στο
luckperms.usage.meta-addsuffix.argument.suffix=η συμβολοσειρά προθέματος
luckperms.usage.meta-addsuffix.argument.context=τα πλαίσια για τον ορισμό της ομάδας
luckperms.usage.meta-setprefix.description=Προσθέτει ένα πρόθεμα
luckperms.usage.meta-setprefix.argument.priority=την προτεραιότητα να αφαιρέσετε το πρόθεμα στο
luckperms.usage.meta-setprefix.argument.prefix=η συμβολοσειρά προθέματος
luckperms.usage.meta-setprefix.argument.context=τα πλαίσια για τον ορισμό της ομάδας
luckperms.usage.meta-setsuffix.description=Προσθέτει ένα πρόθεμα
luckperms.usage.meta-setsuffix.argument.priority=την προτεραιότητα να αφαιρέσετε το πρόθεμα στο
luckperms.usage.meta-setsuffix.argument.suffix=η συμβολοσειρά προθέματος
luckperms.usage.meta-setsuffix.argument.context=τα πλαίσια για τον ορισμό της ομάδας
luckperms.usage.meta-removeprefix.description=Αφαιρεί ένα επίθημα
luckperms.usage.meta-removeprefix.argument.priority=την προτεραιότητα να αφαιρέσετε το πρόθεμα στο
luckperms.usage.meta-removeprefix.argument.prefix=η συμβολοσειρά προθέματος
luckperms.usage.meta-removeprefix.argument.context=τα πλαίσια για τον ορισμό της ομάδας
luckperms.usage.meta-removesuffix.description=Αφαιρεί ένα επίθημα
luckperms.usage.meta-removesuffix.argument.priority=την προτεραιότητα να αφαιρέσετε το πρόθεμα στο
luckperms.usage.meta-removesuffix.argument.suffix=η συμβολοσειρά προθέματος
luckperms.usage.meta-removesuffix.argument.context=τα πλαίσια για τον ορισμό της ομάδας
luckperms.usage.meta-addtemp-prefix.description=Προσθέτει προσωρινά ένα πρόθεμα
luckperms.usage.meta-addtemp-prefix.argument.priority=την προτεραιότητα να αφαιρέσετε το πρόθεμα στο
luckperms.usage.meta-addtemp-prefix.argument.prefix=η συμβολοσειρά προθέματος
luckperms.usage.meta-addtemp-prefix.argument.duration=η διάρκεια μέχρι να λήξει ο κόμβος άδειας
luckperms.usage.meta-addtemp-prefix.argument.context=τα πλαίσια για τον ορισμό της ομάδας
luckperms.usage.meta-addtemp-suffix.description=Προσθέτει προσωρινά ένα πρόθεμα
luckperms.usage.meta-addtemp-suffix.argument.priority=την προτεραιότητα να αφαιρέσετε το πρόθεμα στο
luckperms.usage.meta-addtemp-suffix.argument.suffix=η συμβολοσειρά προθέματος
luckperms.usage.meta-addtemp-suffix.argument.duration=η διάρκεια μέχρι να λήξει ο κόμβος άδειας
luckperms.usage.meta-addtemp-suffix.argument.context=τα πλαίσια για τον ορισμό της ομάδας
luckperms.usage.meta-settemp-prefix.description=Προσθέτει προσωρινά ένα πρόθεμα
luckperms.usage.meta-settemp-prefix.argument.priority=την προτεραιότητα να αφαιρέσετε το πρόθεμα στο
luckperms.usage.meta-settemp-prefix.argument.prefix=η συμβολοσειρά προθέματος
luckperms.usage.meta-settemp-prefix.argument.duration=η διάρκεια μέχρι να λήξει ο κόμβος άδειας
luckperms.usage.meta-settemp-prefix.argument.context=τα πλαίσια για τον ορισμό της ομάδας
luckperms.usage.meta-settemp-suffix.description=Προσθέτει προσωρινά ένα πρόθεμα
luckperms.usage.meta-settemp-suffix.argument.priority=την προτεραιότητα να αφαιρέσετε το πρόθεμα στο
luckperms.usage.meta-settemp-suffix.argument.suffix=η συμβολοσειρά προθέματος
luckperms.usage.meta-settemp-suffix.argument.duration=η διάρκεια μέχρι να λήξει ο κόμβος άδειας
luckperms.usage.meta-settemp-suffix.argument.context=τα πλαίσια για τον ορισμό της ομάδας
luckperms.usage.meta-removetemp-prefix.description=Αφαιρεί ένα προσωρινό πρόθεμα
luckperms.usage.meta-removetemp-prefix.argument.priority=την προτεραιότητα να αφαιρέσετε το πρόθεμα στο
luckperms.usage.meta-removetemp-prefix.argument.prefix=η συμβολοσειρά προθέματος
luckperms.usage.meta-removetemp-prefix.argument.context=τα πλαίσια για τον ορισμό της ομάδας
luckperms.usage.meta-removetemp-suffix.description=Αφαιρεί ένα προσωρινό πρόθεμα
luckperms.usage.meta-removetemp-suffix.argument.priority=την προτεραιότητα να αφαιρέσετε το πρόθεμα στο
luckperms.usage.meta-removetemp-suffix.argument.suffix=η συμβολοσειρά προθέματος
luckperms.usage.meta-removetemp-suffix.argument.context=τα πλαίσια για τον ορισμό της ομάδας
luckperms.usage.meta-clear.description=Καθαρίζει όλα τα meta
luckperms.usage.meta-clear.argument.type=ο τύπος του meta που θα αφαιρεθεί
luckperms.usage.meta-clear.argument.context=τα συμφραζόμενα φιλτραρίσματος κατά
luckperms.usage.track-info.description=Δίνει πληροφορίες σχετικά με το κομμάτι
luckperms.usage.track-editor.description=Ανοίγει τον επεξεργαστή δικαιωμάτων web
luckperms.usage.track-append.description=Προσθέτει μια ομάδα στο τέλος της τροχιάς
luckperms.usage.track-append.argument.group=την ομάδα που θα προσαρτήσει
luckperms.usage.track-insert.description=Εισάγει μια ομάδα σε μια δεδομένη θέση κατά μήκος της τροχιάς
luckperms.usage.track-insert.argument.group=την ομάδα που θα προσαρτήσει
luckperms.usage.track-insert.argument.position=η θέση εισαγωγής της ομάδας στην πρώτη θέση στην τροχιά 1)
luckperms.usage.track-remove.description=Αφαιρεί μια ομάδα από το κομμάτι
luckperms.usage.track-remove.argument.group=η ομάδα που θα οριστεί σε
luckperms.usage.track-clear.description=Καθαρίζει τις ομάδες στο κομμάτι
luckperms.usage.track-rename.description=Μετονομασία προϊόντος
luckperms.usage.track-rename.argument.name=το νέο όνομα
luckperms.usage.track-clone.description=Κλωνοποίηση ομάδας
luckperms.usage.track-clone.argument.name=το όνομα της ομάδας για κλωνοποίηση
luckperms.usage.log-recent.description=Εμφάνιση πρόσφατων ενεργειών
luckperms.usage.log-recent.argument.user=το όνομα/uuid του χρήστη για φιλτράρισμα κατά
luckperms.usage.log-recent.argument.page=ο αριθμός σελίδας για προβολή
luckperms.usage.log-search.description=Αναζήτηση στο αρχείο καταγραφής για μια καταχώρηση
luckperms.usage.log-search.argument.query=το ερώτημα προς αναζήτηση
luckperms.usage.log-search.argument.page=ο αριθμός σελίδας για προβολή
luckperms.usage.log-notify.description=Εναλλαγή ειδοποιήσεων καταγραφής
luckperms.usage.log-notify.argument.toggle=αν πρέπει να ενεργοποιήσετε ή να απενεργοποιήσετε
luckperms.usage.log-user-history.description=Προβολή ιστορικού χρήστη
luckperms.usage.log-user-history.argument.user=το όνομα/uuid του χρήστη
luckperms.usage.log-user-history.argument.page=ο αριθμός σελίδας για προβολή
luckperms.usage.log-group-history.description=Προβολή ιστορικού χρήστη
luckperms.usage.log-group-history.argument.group=Μετονομασία προϊόντος
luckperms.usage.log-group-history.argument.page=ο αριθμός σελίδας για προβολή
luckperms.usage.log-track-history.description=Προβολή ιστορικού χρήστη
luckperms.usage.log-track-history.argument.track=Μετονομασία προϊόντος
luckperms.usage.log-track-history.argument.page=ο αριθμός σελίδας για προβολή
luckperms.usage.sponge.description=Επεξεργασία επιπλέον δεδομένων σφουγγαριού
luckperms.usage.sponge.argument.collection=η συλλογή προς αναζήτηση
luckperms.usage.sponge.argument.subject=το θέμα της τροποποίησης
luckperms.usage.sponge-permission-info.description=Εμφανίζει πληροφορίες σχετικά με τα δικαιώματα του θέματος
luckperms.usage.sponge-permission-info.argument.contexts=τα συμφραζόμενα φιλτραρίσματος κατά
luckperms.usage.sponge-permission-set.description=Ορίζει δικαιώματα για το αντικείμενο
luckperms.usage.sponge-permission-set.argument.node=ο κόμβος δικαιωμάτων για ορισμό
luckperms.usage.sponge-permission-set.argument.tristate=η τιμή για να ορίσετε την άδεια
luckperms.usage.sponge-permission-set.argument.contexts=τα συμφραζόμενα για να προσθέσετε τα δικαιώματα
luckperms.usage.sponge-permission-clear.description=Καθαρίζει τις επιλογές θεμάτων
luckperms.usage.sponge-permission-clear.argument.contexts=τα συμφραζόμενα για την εκκαθάριση επιλογών
luckperms.usage.sponge-parent-info.description=Εμφανίζει πληροφορίες σχετικά με τα δικαιώματα του θέματος
luckperms.usage.sponge-parent-info.argument.contexts=τα συμφραζόμενα φιλτραρίσματος κατά
luckperms.usage.sponge-parent-add.description=Αφαιρεί έναν γονέα από το Θέμα
luckperms.usage.sponge-parent-add.argument.collection=τη συλλογή του θέματος, όπου το μητρικό θέμα είναι
luckperms.usage.sponge-parent-add.argument.subject=το όνομα του μητρικού θέματος
luckperms.usage.sponge-parent-add.argument.contexts=τα πλαίσια για τον ορισμό της ομάδας
luckperms.usage.sponge-parent-remove.description=Αφαιρεί έναν γονέα από το Θέμα
luckperms.usage.sponge-parent-remove.argument.collection=τη συλλογή του θέματος, όπου το μητρικό θέμα είναι
luckperms.usage.sponge-parent-remove.argument.subject=το όνομα του μητρικού θέματος
luckperms.usage.sponge-parent-remove.argument.contexts=τα πλαίσια για τον ορισμό της ομάδας
luckperms.usage.sponge-parent-clear.description=Καθαρίζει τις επιλογές θεμάτων
luckperms.usage.sponge-parent-clear.argument.contexts=τα συμφραζόμενα για την εκκαθάριση επιλογών
luckperms.usage.sponge-option-info.description=Εμφανίζει πληροφορίες σχετικά με τα δικαιώματα του θέματος
luckperms.usage.sponge-option-info.argument.contexts=τα συμφραζόμενα φιλτραρίσματος κατά
luckperms.usage.sponge-option-set.description=Ορίζει δικαιώματα για το αντικείμενο
luckperms.usage.sponge-option-set.argument.key=το κλειδί που θα ορίσετε
luckperms.usage.sponge-option-set.argument.value=η τιμή για να ορίσετε την άδεια
luckperms.usage.sponge-option-set.argument.contexts=τα πλαίσια για τον ορισμό της ομάδας
luckperms.usage.sponge-option-unset.description=Ορίζει δικαιώματα για το αντικείμενο
luckperms.usage.sponge-option-unset.argument.key=το κλειδί που θα ορίσετε
luckperms.usage.sponge-option-unset.argument.contexts=τα πλαίσια για τον ορισμό της ομάδας
luckperms.usage.sponge-option-clear.description=Καθαρίζει τις επιλογές θεμάτων
luckperms.usage.sponge-option-clear.argument.contexts=τα συμφραζόμενα για την εκκαθάριση επιλογών
