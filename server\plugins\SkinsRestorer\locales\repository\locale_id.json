{"skinsrestorer.prefix_format": "<yellow>[<dark_green>SkinsRestorer</dark_green>] <message>", "skinsrestorer.help_skin": "Mengubah skin milikmu.", "skinsrestorer.help_skins": "Membuka GUI skin.", "skinsrestorer.help_sr": "<PERSON><PERSON><PERSON> admin untuk SkinsRestorer.", "skinsrestorer.help_skin_help": "Menampilkan bantuan perintah ini.", "skinsrestorer.help_skin_set": "Mengubah skin-mu.", "skinsrestorer.help_skin_set_other": "Mengatur skin untuk pemain yang ditarget.", "skinsrestorer.help_skin_set_url": "Mengubah skin-mu dari sebuah URL.", "skinsrestorer.help_skin_clear": "<PERSON><PERSON><PERSON> skin-mu.", "skinsrestorer.help_skin_clear_other": "Membersihkan skin pemain yang ditarget.", "skinsrestorer.help_skin_random": "Memberikan skin acak.", "skinsrestorer.help_skin_random_other": "Mengatur skin acak untuk pemain yang ditarget.", "skinsrestorer.help_skin_search": "<PERSON><PERSON><PERSON> sebuah skin yang kamu inginkan.", "skinsrestorer.help_skin_edit": "Mengubah skin online kamu.", "skinsrestorer.help_skin_update": "Memperbarui skin-mu.", "skinsrestorer.help_skin_update_other": "Memperbarui skin pemain yang ditarget.", "skinsrestorer.help_skin_undo": "Mengembalikan skin-mu ke skin sebelumnya.", "skinsrestorer.help_skin_undo_other": "Mengembalikan skin pemain yang ditarget ke skin sebelumnya.", "skinsrestorer.help_skin_favourite": "Simpan skin mu sebagai favorit.", "skinsrestorer.help_skin_favourite_other": "Simpan skin pemain yang ditarget sebagai favorit.", "skinsrestorer.help_sr_reload": "Menyegarkan file konfigurasi.", "skinsrestorer.help_sr_status": "Mengecek layanan API yang dibutuhkan plugin.", "skinsrestorer.help_sr_drop": "Menghapus data pemain atau skin dari database.", "skinsrestorer.help_sr_info": "Menampilkan informasi tentang pemain atau skin.", "skinsrestorer.help_sr_apply_skin": "Menerapkan kembali skin untuk pemain yang ditarget.", "skinsrestorer.help_sr_create_custom": "Membuat skin lebar server kustom.", "skinsrestorer.help_sr_purge_old_data": "Membersihkan data skin lama yang lebih dari x hari lalu.", "skinsrestorer.help_sr_dump": "Unggah data dukungan ke bytebin.lucko.me.", "skinsrestorer.success_generic": "<dark_green><message>", "skinsrestorer.info_use_quotes": "URL harus dikutip. Contoh: <yellow>/skin set\n\"https://contoh.com/skin.png\"</yellow> (<PERSON><PERSON> dapat menekan tab untuk melengkapi kutipan secara otomatis)", "skinsrestorer.success_skin_change": "Skin kamu telah berubah.", "skinsrestorer.success_skin_change_other": "<PERSON><PERSON> skin <yellow><name></yellow>.", "skinsrestorer.success_skin_undo": "Skin mu <yellow><skin></yellow> telah di<PERSON>an ke kulit dari \n<yellow><timestamp></yellow>.", "skinsrestorer.success_skin_undo_other": "Skin <yellow><skin></yellow> dari <yellow><name></yellow> telah di<PERSON>an ke skin dari <yellow><timestamp></yellow>.", "skinsrestorer.success_skin_favourite": "Skin mu <yellow><skin></yellow> telah diatur menjadi favorit.", "skinsrestorer.success_skin_favourite_other": "Skin <yellow><skin></yellow> dari <yellow><name></yellow> telah diatur menjadi favorit.", "skinsrestorer.success_skin_unfavourite": "Skin favoritmu <yellow><skin></yellow> dari <yellow><timestamp></yellow> telah tidak disukai.", "skinsrestorer.success_skin_unfavourite_other": "Skin favorit <yellow><skin></yellow> dari <yellow><name></yellow> dari \n<yellow><timestamp></yellow> telah tidak disukai.", "skinsrestorer.success_skin_clear": "Skin-mu telah <PERSON>.", "skinsrestorer.success_skin_clear_other": "Skin dibersihkan untuk pemain <yellow><name></yellow>.", "skinsrestorer.success_updating_skin": "Skin-mu telah <PERSON>.", "skinsrestorer.success_updating_skin_other": "Skin diperbarui untuk pemain <yellow><name></yellow>.", "skinsrestorer.success_admin_applyskin": "Skin pemain telah disegarkan!", "skinsrestorer.success_admin_createcustom": "Skin <yellow><skin></yellow> telah dibuat!", "skinsrestorer.success_admin_setcustomname": "<PERSON>a skin <yellow><skin></yellow> telah diatur ke \n<yellow><display_name></yellow>.", "skinsrestorer.success_admin_drop": "Data <type> dijatuhkan untuk <target>.", "skinsrestorer.success_admin_reload": "Konfigurasi dan lokal telah dimuat ulang!", "skinsrestorer.success_history_line": "<dark_green>- <hover:show_text:'<dark_green>Klik untuk menggunakan \n<yellow><skin></yellow>'><click:run_command:/skin set \"<skin_id>\"><yellow><skin></yellow></click></hover> dari <yellow><timestamp></yellow>", "skinsrestorer.success_favourites_line": "<dark_green>- <hover:show_text:'<dark_green>Klik untuk menggunakan \n<yellow><skin></yellow>'><click:run_command:/skin set \"<skin_id>\"><yellow><skin></yellow></click></hover> dari <yellow><timestamp></yellow>", "skinsrestorer.error_generic": "<dark_red><PERSON><PERSON><PERSON><dark_gray>: <red><message>", "skinsrestorer.error_generic_skin": "<PERSON><PERSON><PERSON> kes<PERSON>han terjadi saat meminta data skin, coba lagi nanti!", "skinsrestorer.error_no_undo": "Kamu tidak memiliki skin untuk dikembalikan!", "skinsrestorer.error_no_skin_to_favourite": "Kamu tidak memiliki skin untuk diatur menjadi favorit!", "skinsrestorer.error_skin_disabled": "Skin ini dimatikan oleh administrator.", "skinsrestorer.error_skinurl_disallowed": "Domain ini tidak diizinkan oleh administrator.", "skinsrestorer.error_updating_skin": "<PERSON><PERSON><PERSON> kes<PERSON>han terjadi saat memperbarui skin-mu. <PERSON><PERSON> lagi nanti!", "skinsrestorer.error_updating_url": "Kamu tidak dapat memperbarui URL skin kustom! <newline><red>Minta lagi menggunakan /skin url", "skinsrestorer.error_updating_customskin": "Skin tidak dapat diperbarui karena itu kustom.", "skinsrestorer.error_admin_applyskin": "Skin pemain tidak dapat disegarkan!", "skinsrestorer.error_ms_full": "API MineSkin kehabisan waktu saat mengunggah skin mu. Silahkan coba lagi nanti.", "skinsrestorer.error_ms_api_failed": "API MineSkin kelebihan beban, silahkan coba lagi nanti!", "skinsrestorer.error_ms_api_key_invalid": "Kunci API MineSkin tidak valid! Hubungi pemilik server tentang hal ini!", "skinsrestorer.error_ms_unknown": "Kesalahan MineSkin tidak diketahui!", "skinsrestorer.error_no_history": "Kamu tidak memiliki riwayat skin!", "skinsrestorer.error_no_favourites": "Kamu tidak memiliki skin favorit!", "skinsrestorer.error_player_refresh_no_mapping": "Tidak dapat memuat ulang skin mu karena versi Minecraft ini tidak didukung oleh SkinsRestorer. Mohon beritahu admin server untuk memperbarui plugin SkinsRestorer.", "skinsrestorer.not_connected_to_server": "<red><PERSON><PERSON> tidak tersambung ke server manapun.", "skinsrestorer.divider": "<dark_aqua>----------------------------------------------", "skinsrestorer.admincommand_status_checking": "<gray><PERSON><PERSON><PERSON><PERSON> p<PERSON>...", "skinsrestorer.admincommand_status_uuid_api": "<gray>API UUID yang bekerja: <count>/<total>", "skinsrestorer.admincommand_status_profile_api": "<gray>API Profil yang bekerja: <count>/<total>", "skinsrestorer.admincommand_status_working": "<green>Plugin saat ini dalam keadaan bekerja.", "skinsrestorer.admincommand_status_degraded": "<green><PERSON><PERSON><PERSON> da<PERSON> kondisi terdegrad<PERSON>, be<PERSON><PERSON> fitur mungkin tidak bekerja.", "skinsrestorer.admincommand_status_broken": "<red>Plugin saat ini dalam keadaan rusak, tidak ada skin baru yang dapat diminta.", "skinsrestorer.admincommand_status_firewall": "<red><PERSON><PERSON><PERSON><PERSON> kem<PERSON>an diblokir karena firewall.<newline>Silahkan baca https://skinsrestorer.net/firewall untuk info lebih lanjut.", "skinsrestorer.admincommand_status_summary_server": "<gray>Server: <gold><version>", "skinsrestorer.admincommand_status_summary_proxymode": "<gray>ProxyMode: <gold><proxy_mode>", "skinsrestorer.admincommand_status_summary_commit": "<gray>Melakukan: <gold><hash>", "skinsrestorer.admincommand_drop_player_not_found": "<red><PERSON><PERSON><PERSON> <yellow><player></yellow> tidak <PERSON>.", "skinsrestorer.admincommand_drop_skin_not_found": "<red>Skin <yellow><skin></yellow> tidak di<PERSON>n.", "skinsrestorer.admincommand_drop_uuid_error": "<red><PERSON><PERSON> tidak dapat menghubungi Mojang untuk mendapatkan UUID pemain", "skinsrestorer.admincommand_info_checking": "<gray>Mengumpulkan data yang diminta...", "skinsrestorer.admincommand_info_player": "<gray>P<PERSON>in: <gold><player>", "skinsrestorer.admincommand_info_invalid_uuid": "<red><PERSON><PERSON> harus menentukan UUID pemain.", "skinsrestorer.admincommand_info_no_set_skin": "<red><PERSON><PERSON><PERSON> tidak memiliki skin eksplisit yang diatur.", "skinsrestorer.admincommand_info_hardcoded_skin": "<gray>Skin yang <PERSON>: <gold><skin>", "skinsrestorer.admincommand_info_custom_skin": "<gray>Skin Kustom: <gold><skin>", "skinsrestorer.admincommand_info_player_skin": "<gray>Skin Pemain: <gold><skin>", "skinsrestorer.admincommand_purgeolddata_success": "<green><PERSON><PERSON><PERSON><PERSON><PERSON>kan skin lama!", "skinsrestorer.admincommand_purgeolddata_error": "<dark_red><PERSON><PERSON><PERSON><PERSON> k<PERSON><PERSON>han saat <PERSON>ihkan skin lama!", "skinsrestorer.admincommand_dump_uploading": "<green>Mengunggah data ke bytebin.lucko.me...", "skinsrestorer.admincommand_dump_error": "<red>Kesalahan saat mengunggah data ke bytebin.lucko.me", "skinsrestorer.command_server_not_allowed_message": "<red><PERSON><PERSON>ah telah dinonaktifkan untuk server <server>.", "skinsrestorer.command_unknown_player": "<PERSON><PERSON><PERSON> tidak di<PERSON>: <name>", "skinsrestorer.command_no_targets_supplied": "Tidak ada pemain target yang disediakan.", "skinsrestorer.player_has_no_permission_skin": "<dark_red><PERSON><PERSON><PERSON><dark_gray>: <red><PERSON><PERSON> tidak memiliki izin untuk mengatur skin ini.", "skinsrestorer.player_has_no_permission_url": "<dark_red><PERSON><PERSON><PERSON><dark_gray>: <red><PERSON><PERSON> tidak memiliki izin untuk mengatur skin dengan URL.", "skinsrestorer.not_premium": "<dark_red><PERSON><PERSON><PERSON><dark_gray>: <red>Pemain premium dengan nama itu tidak ada.", "skinsrestorer.only_allowed_on_console": "<dark_red><PERSON><PERSON><PERSON><dark_gray>: <red><PERSON><PERSON> konsol yang dapat menjalankan perintah ini!", "skinsrestorer.only_allowed_on_player": "<dark_red><PERSON><PERSON><PERSON><dark_gray>: <red><PERSON><PERSON> pemain yang dapat menjalankan perintah ini!", "skinsrestorer.invalid_player": "<dark_red><PERSON><PERSON><PERSON><dark_gray>: <red><input> bukan sebuah username atau URL yang valid.", "skinsrestorer.skin_cooldown": "<dark_red><PERSON><PERSON><PERSON><dark_gray>: <red><PERSON><PERSON> dapat mengubah skin mu lagi dalam: \n<yellow><time></yellow>", "skinsrestorer.ms_uploading_skin": "<dark_green><PERSON><PERSON><PERSON><PERSON> skin, harap tunggu... (Ini mungkin memakan waktu)", "skinsrestorer.wait_a_minute": "<dark_red><PERSON><PERSON><PERSON><dark_gray>: <red><PERSON><PERSON> tunggu sebentar sebelum meminta skin itu lagi. (<PERSON><PERSON><PERSON> terbata<PERSON>)", "skinsrestorer.skinsmenu_open": "<dark_green>Membuka menu skin...", "skinsrestorer.skinsmenu_title_select": "<blue><PERSON><PERSON><PERSON>", "skinsrestorer.skinsmenu_title_main": "<blue><PERSON><PERSON> - <PERSON> <page_number>", "skinsrestorer.skinsmenu_title_history": "<blue><PERSON><PERSON> - <PERSON> <page_number>", "skinsrestorer.skinsmenu_title_favourites": "<blue><PERSON><PERSON> - <PERSON> <page_number>", "skinsrestorer.skinsmenu_next_page": "<green><bold>»</bold> <gray>Halaman Selanju<PERSON></gray> <bold>»</bold>", "skinsrestorer.skinsmenu_previous_page": "<yellow><bold>«</bold> <gray>Halaman Sebelumnya</gray> <bold>«</bold>", "skinsrestorer.skinsmenu_clear_skin": "<red><bold>[</bold> <gray>Hapus Skin</gray> <bold>]</bold>", "skinsrestorer.skinsmenu_select_skin": "<gray><PERSON><PERSON> untuk memilih skin ini", "skinsrestorer.skinsmenu_history_lore": "<blue>Skin dari <time>", "skinsrestorer.skinsmenu_set_favourite_lore": "<gray>Shift + klik untuk mengatur sebagai favorit", "skinsrestorer.skinsmenu_remove_favourite_lore": "<gray>Shift + klik untuk menghapus dari favorit", "skinsrestorer.skinsmenu_favourite_since_lore": "<blue><PERSON><PERSON><PERSON><PERSON> sejak <time>", "skinsrestorer.skinsmenu_no_permission": "<red><PERSON>mu tidak memiliki izin untuk menggunakan skin ini.", "skinsrestorer.skinsmenu_main_button": "<aqua><bold>[</bold> <gray><PERSON>u Skin</gray> <bold>]</bold>", "skinsrestorer.skinsmenu_history_button": "<aqua><bold>[</bold> <gray><PERSON>u Ri<PERSON></gray> <bold>]</bold>", "skinsrestorer.skinsmenu_favourites_button": "<aqua><bold>[</bold> <gray><PERSON><PERSON> Favorit</gray> <bold>]</bold>", "skinsrestorer.skinsmenu_back_select_button": "<yellow><bold>«</bold> <gray><PERSON>lihan menu</gray> <bold>«</bold>", "skinsrestorer.skin_search_message": "", "skinsrestorer.skin_edit_message": "", "skinsrestorer.no_skin_data": "<dark_red><PERSON><PERSON><PERSON><dark_gray>: <red>Tidak ada data skin ditemukan! Apakah pemain ini memiliki skin?", "skinsrestorer.unsupported_java": "<dark_red>Versi Java (<version>) pada <platform> mu tidak didukung oleh SkinsRestorer!<newline><red>Mohon update ke Java 17 atau lebih tinggi untuk menggunakan SkinsRestorer tanpa isu. Versi Java yang lebih baru juga dapat dijalankan server lama, jadi server Minecraft 1.8 dapat dijalankan di Java 17. Baca info konsol untuk detail lebih lanjut.", "skinsrestorer.permission_player_wildcard": "<PERSON><PERSON> wildcard untuk pemain", "skinsrestorer.permission_command": "Memungkinkan akses ke perintah utama \"/skin\".", "skinsrestorer.permission_command_set": "Memungkinkan akses untuk mengubah skin mu.", "skinsrestorer.permission_command_set_url": "Memungkinkan akses untuk mengubah skin mu dengan URL.", "skinsrestorer.permission_command_clear": "Memungkinkan akses untuk membersihkan skin mu.", "skinsrestorer.permission_command_random": "Memberikan akses untuk mengatur skin acak.", "skinsrestorer.permission_command_update": "Memungkinkan akses untuk memperbarui skin mu.", "skinsrestorer.permission_command_undo": "Memberikan akses untuk mengembalikan skin-mu kembali ke skin-mu yang sebelumnya.", "skinsrestorer.permission_command_favourite": "<PERSON><PERSON><PERSON> akses untuk mengatur skin sebagai favorit.", "skinsrestorer.permission_command_search": "Memungkinkan akses untuk mencari skin mu.", "skinsrestorer.permission_command_gui": "Memungkinkan akses untuk membuka GUI skin.", "skinsrestorer.permission_admin_wildcard": "<PERSON><PERSON> wildcard untuk admin", "skinsrestorer.permission_admincommand": "Memungkinkan akses ke perintah utama \"/sr\".", "skinsrestorer.permission_command_set_other": "Memungkinkan akses untuk mengatur skin pemain lain.", "skinsrestorer.permission_command_clear_other": "Memungkinkan akses untuk membersihkan skin pemain lain.", "skinsrestorer.permission_command_random_other": "<PERSON><PERSON><PERSON> akses untuk mengatur skin acak pada pemain lain.", "skinsrestorer.permission_command_update_other": "Memungkin<PERSON> akses untuk memperbarui skin pemain lain.", "skinsrestorer.permission_command_favourite_other": "<PERSON><PERSON><PERSON> akses untuk mengatur skin sebagai favorit untuk pemain lain.", "skinsrestorer.permission_command_undo_other": "Memberikan akses untuk mengembalikan skin pemain kembali ke skin mereka sebelumnya.", "skinsrestorer.permission_admincommand_reload": "Memungkinkan akses ke \"/sr reload\".", "skinsrestorer.permission_admincommand_status": "Memungkinkan akses ke \"/sr status\".", "skinsrestorer.permission_admincommand_drop": "Memungkinkan akses untuk menghapus sebuah file .SKIN .", "skinsrestorer.permission_admincommand_info": "Mengizinkan akses untuk mengambil informasi skin dari pemain atau skin.", "skinsrestorer.permission_admincommand_applyskin": "Memungkinkan akses untuk menerapkan kembali skin pemain lain.", "skinsrestorer.permission_admincommand_createcustom": "Memungkinkan akses untuk membuat skin global kustom dengan URL.", "skinsrestorer.permission_admincommand_purgeolddata": "Memungkinkan akses untuk menghapus data skin lama.", "skinsrestorer.permission_admincommand_dump": "Memungkinkan akses untuk mengunggah informasi server melalui \"/sr dump\".", "skinsrestorer.permission_bypasscooldown": "Melewati cooldown perintah apapun yang diatur dalam konfigurasi.", "skinsrestorer.permission_bypassdisabled": "<PERSON><PERSON><PERSON> semua skin nonaktif yang diatur dalam konfigurasi.", "skinsrestorer.permission_ownskin": "Memungkinkan akses untuk mengatur skin mu sendiri.", "skinsrestorer.duration_day": " hari", "skinsrestorer.duration_days": " hari", "skinsrestorer.duration_hour": " jam", "skinsrestorer.duration_hours": " jam", "skinsrestorer.duration_minute": " menit", "skinsrestorer.duration_minutes": " menit", "skinsrestorer.duration_second": " detik", "skinsrestorer.duration_seconds": " detik"}