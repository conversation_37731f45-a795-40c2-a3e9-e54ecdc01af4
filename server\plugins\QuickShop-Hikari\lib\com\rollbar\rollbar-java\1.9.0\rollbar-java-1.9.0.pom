<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <!-- This module was also published with a richer model, Gradle metadata,  -->
  <!-- which should be used instead. Do not delete the following line which  -->
  <!-- is to indicate to Gradle or any Gradle module metadata file consumer  -->
  <!-- that they should prefer consuming it instead. -->
  <!-- do_not_remove: published-with-gradle-metadata -->
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.rollbar</groupId>
  <artifactId>rollbar-java</artifactId>
  <version>1.9.0</version>
  <name>rollbar</name>
  <description>For connecting your applications built on the JVM to Rollbar for Error Reporting</description>
  <url>https://github.com/rollbar/rollbar-java</url>
  <licenses>
    <license>
      <name>MIT License</name>
      <url>http://www.opensource.org/licenses/mit-license.php</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>rokob</id>
      <name>Andrew Weiss</name>
    </developer>
    <developer>
      <id>basoko</id>
      <name>David Basoco</name>
    </developer>
    <developer>
      <id>diegov</id>
      <name>Diego Veralli</name>
    </developer>
  </developers>
  <scm>
    <connection>scm:git:**************:rollbar/rollbar-java.git</connection>
    <developerConnection>scm:git:**************:rollbar/rollbar-java.git</developerConnection>
    <url>https://github.com/rollbar/rollbar-java</url>
  </scm>
  <dependencies>
    <dependency>
      <groupId>com.rollbar</groupId>
      <artifactId>rollbar-api</artifactId>
      <version>1.9.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
      <version>1.7.25</version>
      <scope>compile</scope>
    </dependency>
  </dependencies>
</project>
