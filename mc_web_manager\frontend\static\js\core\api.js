/**
 * MC Web Manager - API调用封装
 * 统一的HTTP请求处理和错误管理
 */

class APIClient {
    constructor() {
        this.baseURL = '/api';
        this.token = localStorage.getItem('access_token');
    }

    /**
     * 设置认证令牌
     * @param {string} token - JWT令牌
     */
    setToken(token) {
        this.token = token;
        if (token) {
            localStorage.setItem('access_token', token);
        } else {
            localStorage.removeItem('access_token');
        }
    }

    /**
     * 获取认证令牌
     * @returns {string|null} JWT令牌
     */
    getToken() {
        return this.token || localStorage.getItem('access_token');
    }

    /**
     * 获取请求头
     * @returns {Object} 请求头对象
     */
    getHeaders() {
        const headers = {
            'Content-Type': 'application/json'
        };
        
        const token = this.getToken();
        if (token) {
            headers['Authorization'] = `Bearer ${token}`;
        }
        
        return headers;
    }

    /**
     * 发送HTTP请求
     * @param {string} method - HTTP方法
     * @param {string} endpoint - API端点
     * @param {Object} data - 请求数据
     * @returns {Promise} 请求Promise
     */
    async request(method, endpoint, data = null) {
        const url = `${this.baseURL}${endpoint}`;
        const config = {
            method: method.toUpperCase(),
            headers: this.getHeaders()
        };

        if (data) {
            if (method.toUpperCase() === 'GET') {
                // GET请求将数据作为查询参数
                const params = new URLSearchParams(data);
                url += `?${params}`;
            } else {
                // 其他请求将数据作为JSON body
                config.body = JSON.stringify(data);
            }
        }

        try {
            const response = await fetch(url, config);
            
            // 检查响应状态
            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new APIError(
                    errorData.detail || `HTTP ${response.status}: ${response.statusText}`,
                    response.status,
                    errorData
                );
            }

            // 解析JSON响应
            const result = await response.json();
            return result;

        } catch (error) {
            if (error instanceof APIError) {
                throw error;
            }
            
            // 网络错误或其他异常
            throw new APIError(
                `网络请求失败: ${error.message}`,
                0,
                { originalError: error }
            );
        }
    }

    // HTTP方法快捷方式
    async get(endpoint, params = null) {
        return this.request('GET', endpoint, params);
    }

    async post(endpoint, data = null) {
        return this.request('POST', endpoint, data);
    }

    async put(endpoint, data = null) {
        return this.request('PUT', endpoint, data);
    }

    async delete(endpoint, data = null) {
        return this.request('DELETE', endpoint, data);
    }

    // 认证相关API
    async login(username, password) {
        const formData = new FormData();
        formData.append('username', username);
        formData.append('password', password);

        const response = await fetch(`${this.baseURL}/auth/login`, {
            method: 'POST',
            body: formData
        });

        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new APIError(
                errorData.detail || '登录失败',
                response.status,
                errorData
            );
        }

        const result = await response.json();
        this.setToken(result.access_token);
        return result;
    }

    async logout() {
        try {
            await this.post('/auth/logout');
        } catch (error) {
            console.warn('登出请求失败:', error);
        } finally {
            this.setToken(null);
        }
    }

    async getCurrentUser() {
        return this.get('/auth/me');
    }

    // 服务器控制相关API
    async getServerStatus() {
        return this.get('/server/status');
    }

    async startServer() {
        return this.post('/server/start');
    }

    async stopServer() {
        return this.post('/server/stop');
    }

    async restartServer() {
        return this.post('/server/restart');
    }

    // 系统信息相关API
    async getSystemInfo() {
        return this.get('/system/info');
    }

    async healthCheck() {
        return this.get('/health');
    }
}

/**
 * API错误类
 */
class APIError extends Error {
    constructor(message, status = 0, data = {}) {
        super(message);
        this.name = 'APIError';
        this.status = status;
        this.data = data;
    }

    /**
     * 检查是否为认证错误
     * @returns {boolean}
     */
    isAuthError() {
        return this.status === 401;
    }

    /**
     * 检查是否为权限错误
     * @returns {boolean}
     */
    isForbiddenError() {
        return this.status === 403;
    }

    /**
     * 检查是否为服务器错误
     * @returns {boolean}
     */
    isServerError() {
        return this.status >= 500;
    }
}

// 创建全局API客户端实例
const apiClient = new APIClient();

// 导出API客户端和错误类
window.APIClient = APIClient;
window.APIError = APIError;
window.apiClient = apiClient;
