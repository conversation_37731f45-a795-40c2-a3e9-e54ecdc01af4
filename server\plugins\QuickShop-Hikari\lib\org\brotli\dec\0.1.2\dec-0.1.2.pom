<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.brotli</groupId>
    <artifactId>parent</artifactId>
    <version>0.1.2</version>
  </parent>
  <artifactId>dec</artifactId>
  <version>0.1.2</version>
  <packaging>jar</packaging>

  <name>${project.groupId}:${project.artifactId}</name>

  <properties>
    <manifestdir>${project.build.directory}/osgi</manifestdir>
    <manifestfile>${manifestdir}/MANIFEST.MF</manifestfile>
  </properties>

  <build>
    <sourceDirectory>../../..</sourceDirectory>
    <testSourceDirectory>../../..</testSourceDirectory>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <configuration>
          <includes>
            <include>**/dec/*.java</include>
          </includes>
          <excludes>
            <exclude>**/*Test*.java</exclude>
          </excludes>
          <testIncludes>
            <include>**/dec/*Test*.java</include>
          </testIncludes>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-source-plugin</artifactId>
        <version>2.4</version>
        <executions>
          <execution>
            <id>attach-sources</id>
            <phase>verify</phase>
            <goals>
              <goal>jar-no-fork</goal>
            </goals>
            <configuration>
              <includes>
                <include>**/dec/*.java</include>
              </includes>
              <excludes>
                <exclude>**/*Test*.java</exclude>
              </excludes>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-javadoc-plugin</artifactId>
        <version>2.10.4</version>
        <executions>
          <execution>
            <id>attach-javadocs</id>
            <phase>verify</phase>
            <goals>
              <goal>jar</goal>
            </goals>
            <configuration>
              <sourcepath>.</sourcepath>
              <sourceFileExcludes>
                <exclude>**/*Test*.java</exclude>
              </sourceFileExcludes>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.felix</groupId>
        <artifactId>maven-bundle-plugin</artifactId>
        <version>3.0.1</version>
        <configuration>
          <archive>
            <forced>true</forced>
          </archive>
          <excludeDependencies>true</excludeDependencies>
          <manifestLocation>${manifestdir}</manifestLocation>
          <instructions>
            <_nouses>true</_nouses>
            <Bundle-SymbolicName>org.brotli.${project.artifactId}</Bundle-SymbolicName>
            <Bundle-Description>${project.description}</Bundle-Description>
            <Export-Package>org.brotli.dec;version=${project.version};-noimport:=true</Export-Package>
            <Private-Package></Private-Package>
            <Import-Package>*</Import-Package>
            <DynamicImport-Package></DynamicImport-Package>
            <Bundle-DocURL>${project.url}</Bundle-DocURL>
          </instructions>
        </configuration>
        <executions>
          <execution>
            <id>bundle-manifest</id>
            <phase>process-classes</phase>
            <goals>
              <goal>manifest</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jar-plugin</artifactId>
        <version>2.5</version>
        <configuration>
          <archive>
            <manifestFile>${manifestfile}</manifestFile>
          </archive>
        </configuration>
      </plugin>
    </plugins>
  </build>

  <dependencies>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>4.12</version>
      <scope>test</scope>
    </dependency>
  </dependencies>
</project>
