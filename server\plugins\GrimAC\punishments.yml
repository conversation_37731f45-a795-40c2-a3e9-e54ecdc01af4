# %check_name% - 检查名称
# %vl% - 违规
# %verbose% - 额外信息
# %player% - 玩家名字
# [alert] - 警告的特殊命令
# [webhook] - 警告discord webhook 的特殊命令
# [proxy] - special command to alert to other servers connected to your proxy (BungeeCord/Velocity)
Punishments:
  Simulation:
    # 多少秒后重置VL
    remove-violations-after: 300
    # This section will match all checks with the name,
    # To exclude a check that would otherwise be matched, put an exclamation mark in front of the name
    # For example, !BadPacketsN
    checks:
      - "Simulation"
      - "GroundSpoof"
      - "Timer"
      - "NoFall"
    # Threshold:Interval Command
    #
    # 这是个例子, 当到达100VL时为了以 "incorrect movement!" 为理由踢出玩家
    # commands:
    # - "100:0 kick %player% incorrect movement!"
    # 0 means execute exactly once
    # - "100:50 say %player% is cheating"
    # Execute when the user hits flag 100, and after that, every 50th flag after 100
    #
    commands:
      - "100:40 [alert]"
      - "100:100 [webhook]"
      - "100:100 [proxy]"
  Knockback:
    remove-violations-after: 300
    checks:
      - "Knockback"
      - "Explosion"
    commands:
      - "5:5 [alert]"
      - "20:20 [webhook]"
      - "20:20 [proxy]"
  Post:
    remove-violations-after: 300
    checks:
      - "Post"
    commands:
      - "20:20 [alert]"
      - "40:40 [webhook]"
      - "40:40 [proxy]"
  BadPackets:
    remove-violations-after: 300
    checks:
      - "BadPackets"
      - "Crash"
    commands:
      - "20:20 [alert]"
      - "40:40 [webhook]"
      - "40:40 [proxy]"
  Inventory:
    remove-violations-after: 300
    checks:
      - "Inventory"
    commands:
      - "10:10 [alert]"
      - "20:20 [webhook]"
      - "20:20 [proxy]"
  Reach:
    remove-violations-after: 300
    checks:
      - "Reach"
    commands:
      - "5:5 [alert]"
      - "5:5 [webhook]"
      - "5:5 [proxy]"
  Misc:
    remove-violations-after: 300
    checks:
      - "EntityControl"
      - "NoSlow"
      - "Place"
      - "Baritone"
      - "FastBreak"
    commands:
      - "10:5 [alert]"
      - "20:10 [webhook]"
      - "20:10 [proxy]"
  Combat:
    remove-violations-after: 300
    checks:
      - "Killaura"
      - "Aim"
    commands:
      - "20:40 [alert]"
  # Grim2.0.10版本 没有连点器检查，Grim将在未来添加
  Autoclicker:
    remove-violations-after: 300
    checks:
      - "Autoclicker"
    commands:
      - "20:40 [alert]"
