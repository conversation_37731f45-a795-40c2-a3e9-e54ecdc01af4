"""
MC Web Manager - FastAPI应用主入口
基于FastAPI的Minecraft服务器Web管理系统
"""

from fastapi import FastAPI, Request, HTTPException
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
import os
from pathlib import Path

# 导入配置和路由模块
from .api.routes import router as api_router
from .auth import get_current_user

# 创建FastAPI应用实例
app = FastAPI(
    title="MC Web Manager",
    description="Minecraft服务器Web管理系统",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境中应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 获取项目根目录
BASE_DIR = Path(__file__).resolve().parent.parent.parent

# 配置静态文件服务
app.mount("/static", StaticFiles(directory=BASE_DIR / "frontend" / "static"), name="static")

# 配置模板引擎
templates = Jinja2Templates(directory=BASE_DIR / "frontend" / "templates")

# 包含API路由
app.include_router(api_router, prefix="/api")

@app.get("/")
async def root(request: Request):
    """根路径，重定向到登录页面"""
    return templates.TemplateResponse("login.html", {"request": request})

@app.get("/dashboard")
async def dashboard(request: Request):
    """仪表板页面"""
    return templates.TemplateResponse("dashboard.html", {"request": request})

@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {"status": "ok", "message": "MC Web Manager is running"}

if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
