break-shop-use-supertool: <yellow>Du kannst den Shop nicht mit dem super tool zerstören!.
fee-charged-for-price-change: <green>Sie haben bezahlt <red>{0}</red> , um den Preis zu ändern.
not-allowed-to-create: <red>Du kannst hier keinen Shop erstellen.
disabled-in-this-world: <red>QuickShop ist in dieser Welt deaktiviert
how-much-to-trade-for: <green>Gib im Chat ein, für wieviel du {1}x <yellow>{0}<green> handeln willst.
client-language-changed: <green>QuickShop hat festgestellt, dass deine Client-Spracheinstellungen geändert wurden. Wir verwenden jetzt die Spracheinstellung {0} für dich.
shops-backingup: Erstelle Shop-Backup von der Datenbank...
_comment: Hallo Übersetzer! Wenn du dies auf GitHub oder im Quellcode bearbeitest, solltest du zu https://crowdin.com/project/quickshop-hikari wechseln.
unlimited-shop-owner-changed: <yellow>Der Besitzer des unbegrenzten Shops wurde zu {0} geändert.
bad-command-usage-detailed: '<red>Falsche Befehlsargumente! Folgende Parameter sind erlaubt: <gray>{0}'
thats-not-a-number: <red>Ungültige Zahl
shop-name-disallowed: <red>Der Shopname <yellow>{0}</yellow> ist nicht erlaubt. Wähle einen anderen!
console-only-danger: <red>Dies ist ein gefährlicher Befehl, verwnde es also nur in der Konsole.
not-a-number: <red>Du kannst nur eine Zahl eingeben, Deine Eingabe war {0}.
not-looking-at-valid-shop-block: <red>Konnte keinen Block zum erstellen eines Shops finden. Stelle sicher dass du einen ansiehst.
shop-removed-cause-ongoing-fee: <red>Dein Shop bei den Koordinaten {0} wurde entfernt, da du nicht genug Geld hattest um ihn zu behalten!
tabcomplete:
  amount: '[Anzahl]'
  item: '[Item]'
  price: '[Preis]'
  name: '[Name]'
  range: '[Reichweite]'
  currency: '[Währungsname]'
  percentage: '[Prozentsatz%]'
taxaccount-unset: <green>Das Steuerkonto dieses Shops folgt nun den globalen Servereinstellungen.
blacklisted-item: <red>Dieses Item kann nicht verkauft werden, da es auf der Schwarzen Liste steht
command-type-mismatch: <red>Dieser Befehl kann nur von <aqua>{0}<red> ausgeführt werden.
server-crash-warning: '<red>Server kann nach Ausführung des /qs Reload-Befehls abstürzen, wenn Sie während des Laufens des Servers QuickShop-Plugin-Jar-Datei ersetzen/löschen.'
you-cant-afford-to-change-price: Es kostet {0} um den Preis deines Shops zu ändern.
safe-mode: <red>QuickShop ist im Sicherheitsmodus. Du kannst diesen Shop-Container nicht öffnen. Kontaktiere den Server Administrator um dieses Problem zu beheben.
forbidden-vanilla-behavior: <red>Die Operation ist verboten da Sie nicht mit dem Vanilla verhalten übereinstimmt
shop-out-of-space-name: <dark_purple>Dein Shop {0} ist voll!
paste-disabled: |-
  <red>Paste Funktion wurde deaktiviert! Du kannst keinen technischen Support anfordern.<newline>Grund: {0}
quick-fill:
  entry-own: ' <yellow>- <red>[Eigener] <aqua>{0} <light_purple>{1}'
  hover:
    - '<yellow>Name: <aqua>{0}'
    - '<yellow>Besitzer: <aqua>{0}'
    - '<yellow>Typ: <aqua>{0}'
    - '<yellow>Preis: <aqua>{0}'
    - '<yellow>Item: <aqua>{0}'
    - '<yellow>Ort: <aqua>{0}'
  hover-arg-filled:
    - '<yellow>Name: <aqua>{name}'
    - '<yellow>Besitzer: <aqua>{owner}'
    - '<yellow>Typ: <aqua>{type}'
    - '<yellow>Preis: <aqua>{price}'
    - '<yellow>Item: <aqua>{item}'
    - '<yellow>Ort: <aqua>{location}'
  header: '<yellow>Du hast mehrere Shops mit dem Namem "<green>{0}</green>", wähle einen um fortzufahren:'
  entry-normal: ' <yellow>- <aqua>{0} <light_purple>{1}'
  entry-cooperation: ' <yellow>- <red>[co] <aqua>{0} <light_purple>{1}'
3rd-plugin-build-check-failed-admin: '<gray>(Nur für Admins) <light_purple>{0}<dark_gray> hat Berechtigungsprüfungen abgelehnt. Sofern dies nicht gewollt ist, versuch <light_purple>{1} <gray>zur Listener-Blacklist hinzuzufügen. Konfigurations-Anleitung: https://ghost-chu.github.io/QuickShop-Hikari-Documents/docs/modules/shops/protection-checker'
average-price-nearby: '<green>Durchschnittspreis in der Umgebung: <yellow>{0}'
inventory-check-global-alert: "<red>[Inventar Prüfung] <gray>Warnung! Ein QuickShop Anzeigeitem <gold>{2}</gold> wurde in einem Inventar bei <aqua>{0}</aqua> <dark_gray>[{1}]</dark_gray> gefunden, was nicht passieren sollte. Dies bedeutet oftmals, dass jemand einen Exploit böswillig ausnutzt um das Anzeigeitem zu duplizieren."
digits-reach-the-limit: <red>Du hast das Dezimalstellenlimit für den Preis erreicht.
currency-unset: <green>Shopwährung erfolgreich entfernt. Verwende jetzt Standardeinstellungen.
you-cant-create-shop-in-there: <red>Du hast keine Berechtigung einen Shop an dieser Position zu erstellen.
no-pending-action: <red>Du hast keine ausstehenden Aktionen
refill-success: <green>Auffüllen erfolgreich
failed-to-paste: <red>Der Upload zu Pastebin war nicht möglich. Bitte überprüfe deine Internetverbindung und versuche es erneut. (Siehe Konsole für Details)
shop-out-of-stock-name: <dark_purple>Dein Shop {0} hat nicht mehr {1}!
shop-name-invalid: <red>Der Shopname <yellow>{0} <red>ist ungültig. Wähle einen anderen!
how-many-buy-stack: <yellow>Gib im Chat ein, wie viele Mengen du <aqua>KAUFEN</aqua> willst. Es hat <green>{0}</green> Items pro Menge und du kannst <green>{1}</green> Mengen kaufen. Gib <aqua>{2}</aqua> im Chat ein um alle zu kaufen.
exceeded-maximum: <red>Der Wert hat den Maximalwert in Java überschritten.
unlimited-shop-owner-keeped: '<yellow>Achtung: Der Shopeigentümer ist nach wie vor unlimitiert. Du musst den Besitzer manuell neu setzten.'
no-enough-money-to-keep-shops: <red>Du hattest nicht genug Geld um deine Shops zu behalten! Alle Shops wurden entfernt...
3rd-plugin-build-check-failed: <red>Das Drittanbieter Plugin <bold>{0}<reset><red> hat die Berechtigungsprüfung abgelehnt, hast du die richtigen Rechte eingerichtet?
not-a-integer: <red>Du kannst nur eine Zahl eingeben, Deine Eingabe war {0}.
translation-country: 'Sprache: Deutsch (de_DE)'
buying-more-than-selling: '<red>WARNUNG: Du kaufst Items für mehr als du sie verkaufst!'
purchase-failed: '<red>Kauf fehlgeschlagen: Interner Fehler. Bitte melde dies dem Server Administrator.'
denied-put-in-item: <red>Du kannst dieses Item nicht in deinen Shop setzen!
shop-has-changed: <red>Der Shop, den du gerade benutzt wolltest, wurde verändert seit dem du drauf geklickt hast!
flush-finished: <green>Die Nachrichten wurden erfolgreich abgerufen.
no-price-given: <red>Bitte gebe einen gültigen Preis ein.
shop-already-owned: <red>Dies ist bereits ein Shop.
backup-success: <green>Backup erfolgreich.
not-looking-at-shop: <red>Konnte keinen Shop finden! <yellow>Stell sicher, dass du einen ansiehst.
you-cant-afford-a-new-shop: <red>Du kannst dir dies nicht leisten. Es kosted <green>{0}</green> um einen neuen Shop zu erstellen.
success-created-shop: <green>Shop erfolgreich erstellt.
shop-creation-cancelled: <red>Die Shoperstellung wurde abgebrochen.
shop-owner-self-trade: <yellow>Du handelst mit deinem eigenen Shop. <gray>Du wirst kein Geld davon verdienen.
purchase-out-of-space: <red>Dieser Shop hat keinen platz mehr! Kontaktiere den Shopbesitzer oder einen Shopmitarbeiter damit er geleert wird.
reloading-status:
  success: <green>Plugin erfolgreich ohen Fehler neu geladen.
  scheduled: <green>Neu laden abgeschlossen! <gray>(<yellow>Manche Änderungen benötigen eine Weile um aktiv zu werden</yellow>)
  require-restart: <green>Neu laden abgeschlossen. <gray>(<yellow>Einige Änderungen benötigen einen Neustart des Servers um aktiv zu werden</yellow>)
  failed: <red>Neu laden fehlgeschlagen. Überprüfe die Serverkonsole für Fehler.
player-bought-from-your-store-tax: <green>{0} hat {1} {2} von deinem Shop gekauft und du hast {3} ({4} an Steuern) verdient.
not-enough-space: <red>Du hast nur Platz für <yellow>{0}</yellow> weitere!
shop-name-success: <green>Shopname erfolgreich geändert zu <yellow>{0}</yellow>.
shop-staff-added: <yellow>{0} <yellow>wurde erfolgreich als Shopmitarbeiter zu deinem Shop hinzugefügt.
shop-staff-empty: <yellow>Dieser Shop hat keine Mitarbeiter.
shops-recovering: Stelle Shops von Backups wieder her...
virtual-player-component-hover: "<gray>Dies ist ein virtueller Spieler.\n<gray>Bezieht sich auf ein Systemkonto mit dem selben Namen.</gray>\n<green>UUID: <yellow>{0}</yellow>\n<green>Benutzername: <yellow>{1}</yellow>\n<green>Angezeigt als: <yellow>{2}</yellow>"
real-player-component-hover: "<gray>Dies ist ein echter existierender Spieler.\n<green>UUID: <yellow>{0}</yellow>\n<green>Benutzername: <yellow>{1}</yellow>\n<green>Angezeigt als: <yellow>{2}</yellow>\n<gray>Wenn du ein virtuelles Systemkonto mit demselben Namen verwenden willst, füge <dark_gray>\"[]\"</dark_gray> an beiden enden des Namens hinzu: <dark_gray>[{1}]</dark_gray>."
menu:
  sell-tax: <green>Du hast <yellow>{0} <green>an Steuern zahlen müssen.
  owner: '<green>Besitzer: {0}'
  preview: <gray>[<aqua>Itemvorschau</aqua>]
  enchants: <dark_purple>Verzauberungen
  sell-tax-self: <green>Du musst keine Steuern zahlen, da du der Besitzer dieses Shops bist.
  shop-information: '<green>Shop Informationen:'
  item: '<green>Gegenstand: <yellow>{0}'
  price-per: <green>Preis pro <yellow>{0} <green>- <yellow>{1}
  item-name-and-price: <yellow>{0} {1} <green>für <yellow>{2}
  item-name-and-price-tax: <yellow>{0} {1} <green>für</green> {2} <gray>(<green>{3}</green> an Steuern)</gray>
  successful-purchase: '<green>Erfolgreich gekauft:'
  price-per-stack: <green>Preis pro <yellow>{2}x {0} - {1}
  stored-enchants: <dark_purple>Gespeicherte Verzauberungen
  item-holochat-error: <red>[Fehler]
  this-shop-is-selling: <green>Dieser Shop <aqua>VERKAUFT<green> Gegenstände.
  shop-stack: '<green>Menge: <yellow>{0}'
  space: '<green>Kapazität: <yellow>{0}'
  effects: <green>Effekte
  damage-percent-remaining: <yellow>{0}% <green>verbleibend.
  item-holochat-data-too-large: <red>[Error] Item NBT ist zu gross zum Anzeigen
  stock: '<green>Lagerbestand: <yellow>{0}'
  this-shop-is-buying: <green>Dieser Shop <light_purple>KAUFT<green> Gegenstände an.
  successfully-sold: '<green>Erfolgreich verkauft:'
  total-value-of-chest: '<green>Gesamtwert der Truhe: <yellow>{0}'
currency-not-exists: <red>Kann die Währung, welche du setzen willst, nicht finden. Vielleicht ist die Schreibweise falsch oder diese Währung ist in dieser Welt nicht verfügbar.
no-nearby-shop: <red>Keine nahegelegenen Shops welche {0} entsprechen.
translation-author: 'Ghost_chu, Andre_601'
integrations-check-failed-trade: <red>Integrierung {0} hat das Shop-handeln verweigert
shop-transaction-failed: <red>Sorry, aber ein interner Fehler ist während dem verarbeiten deines Kaufes aufgetreten. Der Kauf wurde abgebrochen und jegliche wirtschaftliche aktion wurde zurückgesetzt. Bitte kontaktiere einen Server-Administrator sollte dies weiterhin auftreten.
success-change-owner-to-server: <green>Der Shop wurde erfolgreich dem Server überschrieben.
shop-name-not-found: <red>Ein Shop mit dem Namen <yellow>{0}</yellow> existiert nicht.
shop-name-too-long: <red>Dieser Shopname ist zu lang (Maximal {0}). Bitte wähle einen anderen!
metric:
  header-player: '<yellow>{0}''s Transaktionen von {1} {2}:'
  action-hover: <yellow>{0}
  price-hover: <yellow>Total {0}, inklusive {1} steuern.
  unknown: <gray>(Unbekannt)
  undefined: <gray>(Kein Name)
  no-results: <red>Keine Transaktionen gefunden.
  action-description:
    DELETE: <yellow>Ein Spieler hat einen Shop gelöscht. Shop Erstellungskosten wurden Zurückerstattet, sofern möglich.
    ONGOING_FEE: <yellow>Spieler hat die laufenden Gebühren wegen der Zahlungsperiode bezahlt.
    PURCHASE_BUYING_SHOP: <yellow>Spieler hat Items an einen kaufenden Shop verkauft.
    CREATE: <yellow>Spieler hat einen Shop erstellt.
    PURCHASE_SELLING_SHOP: <yellow>Spieler hat einige Items von einem verkaufenden Shop gekauft
    PURCHASE: <yellow>Gekaufte Items mit Shop
  query-argument: 'Such-Argumente: {0}'
  amount-hover: <yellow>{0}x
  header-shop: '<yellow>Shop {0}''s Transaktionen von {1} {2}:'
  player-hover: |-
    <yellow>{0}<newline><gold>UUID: <gray>{1}
  looking-up: <yellow>Führe erstellung von Statistiken aus, bitte warten...
  tax-hover: <yellow>{0} Steuern
  header-global: '<yellow>Transaktionen des Server für {0} {1}:'
  na: <gray>N/A
  transaction-count: <yellow>{0} total
  shop-hover: |-
    <yellow>{0}<newline><gold>Pos: <gray>{1} {2} {3}, Welt: {4}<newline><gold>Besitzer: <gray>{5}<newline><gold>Shop-Art: <gray>{6}<newline><gold>Item: <gray>{7}<newline><gold>Preis: <gray>{8}
  time-hover: '<yellow>Zeit: {0}'
  amount-stack-hover: <yellow>{0}x Stacks
permission-denied-3rd-party: '<red>Berechtigung verweigert: Drittanbieterplugin [{0}].'
you-dont-have-that-many-items: <red>Du hast nur {0} {1}.
complete: <green>Abgeschlossen!
translate-not-completed-yet-url: 'Die Übersetzung von der Sprache {0} wurde noch nicht von {1} fertiggestellt. Möchtest du uns helfen, die Übersetzung zu verbessern? Klicke hier! {2}'
success-removed-shop: <green>Shop entfernt.
currency-set: <green>Shopwährung erfolgreich geändert zu {0}.
shop-purged-start: <green>Shop-Löschung gestartet. Überprüfe die Konsole für details.
economy-transaction-failed: <red>Sorry, aber ein interner Fehler ist während dem Bearbeiten der Transaktion aufgetreten. Die Transaktion wurde abgebrochen und jegliche wirtschaftliche Aktion wurde zurückgesetzt. Bitte kontaktiere einen Server-Administrator sollte dies weiterhin auftreten.
nothing-to-flush: <green>Es gibt keine neuen Nachrichten zu deinem Shop.
no-price-change: <red>Dies würde in keinen Preisunterschied enden!
edition-confilct: QuicShop-Hikari mit QuickShop-Reremake installiert können gegenseitige Konflikte auslösen. Deinstalliere eines davon.
inventory-unavailable: |-
  <red>Dieser Shop Inventar-Wrapper existiert nicht oder ist ungültig. Verwendest du ein Add-on um das Shop-Inventar neu zu verbinden? Information: Inventar-Wrapper={0}, Wrapper Bereitstellung={1}, Symbol-Link={2}. Bitte kontaktiere die Server-Administratoren.
file-test: Dies ist eine Text Testdatei. Wir verwenden sie um zu testen ob die messages.json kaputt ist. Du kannst sie mit jedem Easter Egg füllen wie du willst :)
unknown-player: <red>Dieser Spieler existiert nicht. Bitte überprüfe, ob der Name korrekt geschrieben wurde.
player-offline: <red>Der ausgewählte Spieler ist zurzeit Offline.
player-profile-format: <aqua>{0}</aqua><gold>(<yellow>{1}</yellow>)</gold>
shop-type:
  selling: VERKAUFEN
  buying: KAUFEN
language:
  qa-issues: '<yellow>Qualitätssicherungs-Probleme: <aqua>{0}%'
  code: '<yellow>Code: <gold>{0}'
  approval-progress: '<yellow>Genehmigungsfortschritt: <aqua>{0}%'
  translate-progress: '<yellow>Übersetzungsfortschritt: <aqua>{0}%'
  name: '<yellow>Name: <gold>{0}'
  help-us: <green>[Hilf uns, die Übersetzungsqualität zu verbessern]
warn-to-paste: |-
  <yellow>Sammle Daten und lade sie auf Pastebin hoch. Dies kann eine Weile dauern...<newline><red><bold>Warnung:</bold> Die Daten bleiben für eine Woche sichtbar! Es könnte deine Servereinstellungen und sensible Daten verraten. Stell sicher, dass du es nur an <bold>vertrauenswürdige Mitarbeiter/Entwickler</bold> weitergibst.
how-many-sell-stack: <green>Schreib im Chat, wie viele Mengen du gerne <light_purple>VERKAUFEN</yellow> willst. Es sind <yellow>{0}<green> Items pro Menge und du kannst <yellow>{1}<green> Mengen verkaufen. Schreibe <aqua>{2}<green> um alle zu verkaufen.
updatenotify:
  buttontitle: '[Jetzt Aktualisieren]'
  onekeybuttontitle: '[Ein Klick Update]'
  label:
    github: '[GitHub]'
    ore: '[Ore]'
    lts: '[LTS]'
    nbtapi: '[+NBTAPI]'
    qualityverifyed: '[Qualität]'
    master: '[Master]'
    unstable: '[Instabil]'
    paper: '[+Paper Optimiert]'
    stable: '[Stabil]'
    spigotmc: '[SpigotMC]'
    papermc: '[PaperMC]'
    bukkitdev: '[BukkitDev]'
    polymart: '[Polymart]'
    modrinth: '[Modrinth]'
    basic: '[Grundlegendes]'
  list:
    - '{0} wurde veröffentlicht. Du verwendest noch immer {1}!'
    - Boom! Neues update {0} kommt. Aktualisiere!
    - Überraschung! {0} ist erschienen. Du bist noch auf {1}
    - Sieht so aus als ob du aktualisieren musst. {0} wurde veröffentlicht!
    - Ooops! {0} wurde gerade veröffentlicht. Du bist noch auf {1}!
    - Ich schwöre dass QS zu {0} aktualisiert wurde. Warum hast du noch nicht aktualisiert?
    - Reparieren und neu lad... Sorry, aber {0} wurde veröffentlicht!
    - Err! Nein. Das ist kein Fehler. {0} wurde veröffentlicht!
    - OMG! {0} ist erschienen! Warum verwendest du immer noch {1}?
    - 'Heutige Neuigkeit: Es gibt eine neue Version von QuickShop {0}!'
    - Plugin K.I.A. Du solltest zu {0} aktualisieren!
    - Update {0} gezündet. Speichere Update!
    - Kommandeur es gibt ein Update! {0} ist grade erschienen!
    - Schau mich an---{0} update, du bist noch auf {1}
    - Ahhhhhhh! Neues Update {0}! Update!
    - Was denkst du? {0} wurde veröffentlicht! Aktualisiere!
    - Doktor, QuickShop hat ein neues Update {0}! Du solltest aktualisieren~
    - Ko~ko~da~yo~ QuickShop hat ein neues Update {0} ~
    - Paimont möchte dir sagen, dass QuickShop ein neues Update {0} hat!
  remote-disable-warning: '<red>Diese Version von QuickShop wurde vom Remote-Server als Deaktiviert markiert, was bedeuted, dass diese Version ernsthafte Probleme haben könnte. Erhalte vollständige Details auf unsere SpigotMC Seiter: {0}. Diese Warnung wird so lange auftauchen, bis du zu einer stabilen Version gewecheslt hast, wird aber die Performance deines Servers nicht beeinflussen.'
purchase-out-of-stock: <red>Dieser Shop hat keine Items mehr. Kontaktiere den Shopeigentümer oder Shop-Mitarbeiter um den Bestand wieder aufzufüllen.
nearby-shop-entry: '<green>- Info:{0} <green>Preis:<aqua>{1} <green>x:<aqua>{2} <green>y:<aqua>{3} <green>z:<aqua>{4} <green>Distanz: <aqua>{5} <green>Block/Blöcke'
chest-title: QuickShop Laden
console-only: <red>Dieser Befehl kann nur in der Konsole ausgeführt werden.
failed-to-put-sign: <red>Nicht genug Platz um den Shop herum um das Infoschild zu setzen.
shop-name-unset: <red>Name des Shops entfernt
shop-nolonger-freezed: <green>Du hast den Shop entfroren. Er ist jetzt wieder normal!
no-permission-build: <red>Du kannst keinen Shop hier erstellen.
tableformat:
  left_half_line: <dark_purple>+--------------------
  right_half_line: <dark_purple>--------------------+
  full_line: <dark_purple>+---------------------------------------------------+
  left_begin: '<dark_purple>| '
quickshop-gui-preview: QuickShop GUI Itemvorschau
translate-not-completed-yet-click: Die Übersetzung von der Sprache {0} wurde noch nicht von {1} fertiggestellt. Möchtest du uns helfen, die Übersetzung zu verbessern? Klicke hier!
taxaccount-invalid: <red>Zielkonto ist ungültig. Bitte verwende einen gültigen Spielernamen oder UUID (Mit Bindestrichen).
player-bought-from-your-store: <red>{0} hat {1} {2} von deinem Shop gekauft und du hast {3} verdient.
reached-maximum-can-create: <red>Du hast bereits das Maximum von {0}/{1} Shops erstellt!
reached-maximum-create-limit: <red>Du hast die maximale Anzahl an Shops erreicht, welche du erstellen kannst
translation-version: 'Unterstützte Version: Hikari'
no-double-chests: <red>Du kannst keinen Doppeltruhen-Shop erstellen.
price-too-cheap: <red>Preis muss höher als <yellow>${0}<red> sein
shop-not-exist: <red>Dieser Shop existiert nicht.
bad-command-usage: <red>Schlechte Befehlsargumente!
cleanghost-warning: <yellow>Dieser Befehlt entfernt <red>alle</red> Shops welche beschädigt sind, in nicht erlaubten Welten erstellt wurden, nicht erlaubte Items (ver)kaufen oder <red><bold>IN UNGELADENEN WELTEN EXISTIEREN</bold></red>. Erstell ein komplettes Backup deiner Shopdaten und fahre dann mit <aqua>/qs cleanghost confirm</aqua> fort.
cleanghost-starting: <green>Beginne mit der suche nach Geistershops (Fehlende Containerblöcke). Alle nicht-existierenden Shops werden entfernt...
cleanghost-deleting: <yellow>Ein beschädigter Shop <aqua>{0}</aqua> wurde gefunden aufgrund von {1}. Zum löschen markiert...
cleanghost-deleted: <green><yellow>{0}</yellow> Shops wurden insgesammt gelöscht.
shop-purchase-cancelled: <red>Shop-Kauf abgebrochen.
bypassing-lock: <red>Umgehe eine QuickShop Sicherung!
bungee-cross-server-msg: 'QuickShop CSM: <green>{0}'
saved-to-path: Die Sicherungsdatei wurde in {0} gespeichert.
shop-now-freezed: <green>Du hast den Shop eingefroren. Niemand kann jetzt mit diesem Shop handeln!
price-is-now: <green>Der neue Preis des Shops ist <yellow>{0}
shops-arent-locked: <red>Nicht vergessen, Shops sind NICHT vor Diebstahl geschützt! Wenn du Diebe stoppen willst, verwende LWC, Lockette, o.ä!
that-is-locked: <red>Dieser Shop ist geschützt.
shop-has-no-space: <red>Der Shop hat nur noch Platz für {0} weitere {1}.
safe-mode-admin: <red><bold>WARNUNG:</bold> Die QuickShop Version auf diesem Server läuft aktuell im Sicherheitsmodus. Jeglicher Funktionen sind deaktiviert. Bitte verwende den <yellow>/qs</yellow> Befehl um nach eventuellen Fehlern zu suchen.
shop-stock-too-low: <red>Der Shop hat nur noch {0}{1} übrig!
world-not-exists: <red>Die Welt <yellow>{0}<red> existiert nicht
how-many-sell: <green>Schreib im Chat, wie viele du gerne <light_purple>VERKAUFEN<green> willst. Du kannst <yellow>{0}<green> verkaufen. Schreibe <aqua>{1}<green> um alle zu verkaufen.
shop-freezed-at-location: <yellow>Dein Shop {0} bei {1} wurde eingefroren!
translation-contributors: 'Mitwirkende: Timtower, Netherfoam, KaiNoMood, Mgazul, JackTheChicken und Andre_601'
empty-success: <green>Shop erfolgreich geleert
taxaccount-set: <green>Das Steuerkonto dieses Shops wurde auf <yellow>{0} gesetzt
support-disable-reason:
  hot-reload: <yellow>Nicht unterstützter Hot reload vom Server. Starte den Server neu und versuch es erneut.
  outdated: <yellow>Diese Version von QuickShop ist nicht mehr aktuell. Aktualisiere es bevor du nach Support fragst!
  bad-hosts: |-
    <yellow>Die HOSTS des Servers wurden modifiziert und gewisse Funktionen von QuickShop benötigen eine Verbindung zu der Mojang API um zu funktionieren. Bitte behebe die HOSTS Einstellungen bevor du nach Support fragst.<newline>Windows: C:\windows\system32\drivers\etc\hosts<newline>Linux: /etc/hosts
  privacy: <yellow>Dieser Server läuft im Cracked (Offline) Modus. Wenn du einen Server unter einer Proxy betreibst und Online Modus ist auf dieser aktiviert, konfiguriere bitte die Proxy-relevanten Einstellungen korrekt.
  modified: <yellow>Datei-Integritätsprüfung fehlgeschlagen. Dieser QuickShop Build wurde von jemandem modifiziert.
  consolespamfix-installed: <yellow>ConsoleSpamFix ist installiert. Es wird Details von Fehlern verstecken. Deaktiviere es vorrübergehend während du nach Support fragst.
  authlib-injector-detected: <yellow>Dieser Server läuft mit einer Drittanbieter Authentifizierungs-Software wie Authlib-Injector.
  unsupported-server-software: <yellow>Nicht unterstützte Server-Software. Jede modifizierte Hybrid-Server-Software wird nicht unterstützt, einschliesslich, MCPC, Cauldron, CatServer, Mohist, Magma, Fukkit, Cardboard, etc.
supertool-is-disabled: <red>Super-Tool ist deaktiviert. Kann keine Shops zerstören.
unknown-owner: Unbekannt
restricted-prices: '<red>Eingeschränkter Preis für {0}: Mindestens {1}, maximal {2}'
nearby-shop-this-way: <green>Shop ist {0} Blöcke von dir entfernt.
owner-bypass-check: <yellow>Alle Prüfungen wurden umgangen. Handel erfolgreich! (Sie sind jetzt der Besitzer des Ladens!)
april-rick-and-roll-easter-egg: "<hover:show_text:'Anklicken um im Browser zu öffnen für eine limitierte Belohnung!'><click:open_url:'https://www.youtube.com/watch?v?dQw4w9WgXcQ'><green>Klicke hier um eine limitierte Belohnung von den QuickShop-Hikari Entwicklern zu erhalten!</green></click></hover>"
signs:
  item-right: ''
  out-of-space: Kein Platz
  unlimited: Unlimitiert
  stack-selling: Verkaufe {0}
  stack-price: '{0} pro {1}x {2}'
  status-unavailable: <red>
  out-of-stock: Ausverkauft
  stack-buying: Kaufe {0}
  freeze: Handeln ist deaktiviert
  price: '{0} p. St.'
  buying: Kauft {0}
  header: '{1}{0}'
  header-available: <green>{0}
  header-unavailable: <red>{0}
  selling: Verkauft {0}
  status-available: <green>
  item-left: ''
negative-amount: <red>Du kannst keine negative Anzahl handeln
display-turn-on: <green>Shop-Display erfolgreich aktiviert.
shop-staff-deleted: <green>{0} erfolgreich von deinem Shop als Shopmitarbeiter entfernt.
nearby-shop-header: '<green>Shops in der nähe welche <aqua>{0}<green> entsprechen:'
backup-failed: Kann Datenbank nicht sichern. Überprüfe die Konsole für Details.
shop-staff-cleared: <green>Alle Shopmitarbeiter erfolgreich von deinem Shop entfernt.
price-too-high: <red>Der Shoppreis ist zu hoch! Du kannst keinen Shop mit einem Preis über {0} erstellen.
plugin-cancelled: '<red>Vorgang abgebrochen, Grund: {0}'
player-sold-to-your-store: <green>{0} hat {1} {2} bei deinem Shop verkauft.
shop-out-of-stock: <dark_purple>Dein Shop bei den Koordinaten {0}, {1}, {2}, verfügt über keine {3} mehr!
how-many-buy: <green>Schreib im Chat, wie viele du gerne <aqua>KAUFEN</aqua> willst. Du kannst <yellow>{0}<green> kaufen. Schreibe <aqua>{1}<green> um alle zu kaufen.
language-info-panel:
  help: 'Hilf uns: '
  code: 'Code: '
  name: 'Sprache: '
  progress: 'Fortschritt: '
  translate-on-crowdin: '[Übersetze auf Crowdin]'
item-not-exist: <red>Das Item <yellow>{0} <red>existiert nicht, bitte überprüfe deine Schreibweise.
shop-creation-failed: <red>Shop-Erstellung fehlgeschlagen, bitte kontaktiere den Server Administrator.
inventory-space-full: <red>Dein vorhandener Inventarplatz kann nur <green>{1}</green> weitere Items aufnehmen. Versuche es zu leeren!
no-creative-break: <red>Du kannst Shops von anderen Spielern zerstören, solange du im Kreativmodus bist. Wechsle zum Überlebensmodus oder versuche das Super-Tool {0} zu verwenden.
booleanformat:
  success: <green>✔
  failed: <red>✘
controlpanel:
  stack: '<green>Pro-Menge-Anzahl: <aqua>{0} <yellow>[<light_purple><bold>Ändern</bold></light_purple>]'
  price-hover: Klicke, um den Preis für das Item zu ändern.
  remove: <red><bold>[Shop entfernen]
  mode-buying-hover: <yellow>Klicke, um den Shop in den VERKAUFEN-Modus zu ändern.
  empty: '<green>Leeren: Entferne alle Items <yellow>[<light_purple><bold>OK</bold></light_purple>]'
  stack-hover: <yellow>Klicke um die Anzahl an Items pro Menge zu ändern. Ändere es zu 1 für normales Verhalten.
  alwayscounting-hover: <yellow>Klicke um zu ändern, ob der Shop immer Zählen soll.
  alwayscounting: '<green>Immer zählen: {0} <yellow>[<light_purple><bold>Ändern</bold></light_purple>]'
  setowner: '<green>Besitzer: <aqua>{0} <yellow>[<light_purple><bold>Ändern</bold></light_purple>]'
  freeze: '<yellow>Eingefroren: <aqua>{0} <yellow>[<light_purple><bold>Ändern</bold></light_purple>]'
  price: '<green>Preis: <aqua>{0} <yellow>[<light_purple><bold>Ändern</bold></light_purple>]'
  currency-hover: <yellow>Klicke um die Währung zu setzen oder zu entfernen, welcher dieser Shop verwendet
  lock: '<yellow>Shop sperren: <aqua>{0}</aqua> [<light_purple><bold>Ändern</bold></light_purple>]'
  mode-selling: '<green>Shop-Modus: <aqua>Verkaufen <yellow>[<light_purple><bold>Ändern</bold></light_purple>]'
  currency: '<green>Währung: <aqua>{0} <yellow>[<light_purple><bold>Ändern</bold></light_purple>]'
  setowner-hover: <yellow>Klicke hier um den Besitzer zu wechseln.
  mode-buying: '<green>Shop-Modus: <aqua>Kaufen <yellow>[<light_purple><bold>Ändern</bold></light_purple>]'
  item: '<green>Shop Item: {0} <yellow>[<light_purple><bold>Ändern</bold></light_purple>]'
  unlimited: '<green>Unlimitiert: {0} <yellow>[<light_purple><bold>Ändern</bold></light_purple>]'
  unlimited-hover: <yellow>Klicke hier um umzuschalten, ob der Shop unlimitiert ist.
  refill-hover: <yellow>Klicke hier um den Shop aufzufüllen.
  remove-hover: <yellow>Klicke um diesen Shop zu entfernen.
  toggledisplay-hover: <yellow>Ändere den Status des Display-Items für den Shop
  refill: '<green>Auffüllen: Füllt die Items auf <yellow>[<light_purple><bold>OK</bold></light_purple>]'
  freeze-hover: <yellow>Ändere den Freeze-Status des Shops.
  lock-hover: <yellow>Aktiviere/Deaktiviere den Sperrschutz des Shops.
  item-hover: <yellow>Klicke um das Shop Item zu ändern
  infomation: '<green>Shop Kontrollpanel:'
  mode-selling-hover: <yellow>Klicke, um den Shop in den KAUF-Modus zu ändern.
  empty-hover: <yellow>Klicke hier um den Shop zu leeren.
  toggledisplay: '<green>Display-Item: <aqua>{0} <yellow>[<light_purple><bold>Ändern</bold></light_purple>]'
  history: '<green>Verlauf: <yellow>[<bold><light_purple>Ansehen</light_purple></bold>]</yellow>'
  history-hover: <yellow>Klicke, um die Shop-Verlauf-Logs zu sehen
timeunit:
  behind: hinter
  week: "{0} Woche"
  weeks: "{0} Wochen"
  year: "{0} Jahr"
  before: vor
  scheduled: Geplant
  years: "{0} Jahre"
  scheduled-in: Geplant in {0}
  second: "{0} Sekunde"
  std-past-format: 'vor {5}{4}{3}{2}{1}{0}'
  std-time-format: HH:mm:ss
  seconds: "{0} Sekunden"
  hour: "{0} Stunde"
  scheduled-at: Geplant am {0}
  after: nach
  day: "{0} Tag"
  recent: Kürzlich
  between: zwischen
  hours: "{0} Stunden"
  months: "{0} Monate"
  longtimeago: vor langer Zeit
  between-format: zwischen {0} und {1}
  minutes: "{0} Minuten"
  justnow: Gerade eben
  minute: "{0} Minute"
  std-format: dd.MM.yyyy HH:mm:ss
  future-plain-text: Zukunft
  month: "{0} Monat"
  future: in {0}
  days: "{0} Tage"
command:
  reloading: <green>Konfiguation neu geladen. <yellow>Manche Änderungen benötigen eventuell einen Neustart um aktiv zu werden.
  description:
    buy: <yellow>Ändert einen Shop zu <light_purple>KAUF</light_purple>-Modus
    about: <yellow>Zeigt QuickShop Informationen
    language: <yellow>Ändert die derzeitig verwendete Sprache
    purge: <yellow>Startet die Shop Bereinigungs-Aufgabe im Hintergrund
    paste: <yellow>Lädt Serverdaten zu Pastebin hoch
    title: <green>QuickShop Hilfe
    remove: <yellow>Entfernt den Shop, den du ansiehst
    ban: <yellow>Bannt einen Spieler vom Shop
    empty: <yellow>Entfernt alle Items eines Shops
    alwayscounting: <yellow>Lege fest, ob der Shop seinen Inhalt immer zählen soll, selbst wenn er unlimitiert ist
    setowner: <yellow>Ändert den Besitzer eines Shops.
    reload: <yellow>Lädt die config.yml von QuickShop neu
    freeze: <yellow>(De)aktiviere Shop-Handel
    price: <yellow>Ändert den Kauf-/Verkaufspreis eines Shops
    find: <yellow>Ermittelt den nächsten Shop in der Umgebung mit dem gesuchten Gegenstand.
    create: <yellow>Erstellt einen neuen Shop mit der angeschauten Truhe
    lock: <yellow>Wechsle den Lock-Status des Shops
    currency: <yellow>Setze oder Entferne die Währungseinstellungen des Shops
    removeworld: <yellow>Entferne ALLE Shops in einer bestimmten Welt
    info: <yellow>Zeigt QuickShop Statistiken
    owner: <yellow>Ändert den Besitzer eines Shops.
    amount: <yellow>Um die Anzahl Items zu setzen (Nützlich bei Chatproblemen)
    item: <yellow>Ändert das Shopitem eines Shops
    debug: <yellow>Aktiviert den Entwicklermodus
    unlimited: <yellow>Gibt einen Shop einen unbegrenzten Vorrat.
    sell: <yellow>Ändert einen Shop zu <aqua>VERKAUFEN</aqua>-Modus
    fetchmessage: <yellow>Zeigt ungelesene Shopnachrichten
    staff: <yellow>Verwalte deine Shopmitarbeiter
    clean: <yellow>Entferne alle (geladenen) Shops ohne Lagerbestand
    refill: <yellow>Fügt eine gegebene Anzahl an Gegenständen dem Shop hinzu.
    help: <yellow>Zeigt die QuickShop-Hilfe
    removeall: <yellow>Entferne ALLE Shops eines bestimmten Spielers
    unban: <yellow>Entbannt einen Spieler vom Shop
    transfer: <yellow>Übertrage ALLE Shops eines Spielers auf einen anderen
    transferall: <yellow>Übertrage ALLE Shops eines Spielers auf einen anderen
    transferownership: <yellow>Übertrage den Shop, welchen du gerade ansiehst, auf jemand anderen
    size: <yellow>Ändere die Pro-Menge Anzahl eines Shops
    supercreate: <yellow>Erstellen eines Shops, während alle Schutzprüfungen umgangen werden
    taxaccount: <yellow>Legt das Steuerkonto fest, welches der Shop verwenden soll
    name: <yellow>Ändert den Namen eines bestimmten Shops
    toggledisplay: <yellow>Ändere den Status des Display-Items für den Shop
    permission: <yellow>Shop-Berechtigungsverwaltung
    lookup: <yellow>Tabelle der nachschlagbaren Gegenstände verwalten
    database: <yellow>Zeige und verwalte die QuickShop Datenbank
    benefit: <yellow>Einstellungen zum Aufteilen von Vorteilen zwischen Shop-Eigentümer und anderen Spielern
    tag: <yellow>Shop-Tags hinzufügen, entfernen oder abfragen
    suggestprice: <yellow>Empfiehlt einen Preis für den angesehenen Shop, basierend auf anderen Shops
    history: <yellow>Zeige den Transaktionsverlauf eines Shops
    sign: <yellow>Ändere das Material des Schildes eines Shops
  bulk-size-not-set: '<red>Verwendung: /qs size <Anzahl>'
  no-type-given: '<red>Verwendung: /qs find <item>'
  feature-not-enabled: Dieses Feature ist nicht in der Config Datei aktiviert.
  now-debuging: <green>Entwicklermodus erfolgreich aktiviert. Lade QuickShop neu...
  no-amount-given: <red>Keine Menge angegeben. Verwende <green>/qs refill <Menge><red>
  no-owner-given: <red>Kein Eigentümer angegeben
  disabled: '<red>Dieser Befehl ist deaktiviert: <yellow>{0}'
  bulk-size-now: <green>Handle jetzt {0}x {1}
  toggle-always-counting:
    counting: <green>Shop zählt nun immer, selbst wenn Container unbegrenzt ist
    not-counting: <green>Shop zählt nicht länger seinen Inhalt
  cleaning: <green>Entferne Shops ohne Lager...
  now-nolonger-debuging: <green>Entwicklermodus erfolgreich deaktiviert. Lade QuickShop neu...
  toggle-unlimited:
    limited: <green>Der Shop ist nun limitiert
    unlimited: <green>Der Shop ist nun unlimitiert
  transfer-success-other: <yellow>{0} <green>Shops von <yellow>{1} <green>auf <yellow>{2} <green>übertragen
  no-trade-item: <green>Bitte halte das Item zum ändern in der Haupthand
  wrong-args: <red>Ungültiges Argument. Verwende <bold>/qs help</bold> um eine Liste an Befehlen zu sehen.
  some-shops-removed: <yellow>{0} <green>Shop(s) entfernt
  new-owner: '<green>Neuer Besitzer: <yellow>{0}'
  format: <green>/{0} {1} <yellow>- {2}
  transfer-success: <yellow>{0} <green>Shop(s) wurden zu <yellow>{1} <green>übertragen
  now-buying: <yellow>{0} <green>werden jetzt <light_purple>ANGEKAUFT
  now-selling: <yellow>{0} <green>werden jetzt <light_purple>VERKAUFT
  cleaned: <green>Entfernte <yellow>{0}<yellow>in Shop.
  trade-item-now: <green>Handle jetzt {0}x {1}
  no-world-given: <red>Bitte gib einen Welt namen an
  format-disabled: <red>/{0} {1} <gray>- {2}
  invalid-bulk-amount: <red>Die angegebene Menge {0} ist grösser als die maximal erlaubte Stackgrösse, oder weniger als eins
currency-not-support: <red>Das Economy-Plugin unterstützt die Multi-Economy Funktion nicht.
trading-in-creative-mode-is-disabled: <red>Du kannst mit dem Shop nicht handeln während du im Kreativmodus bist.
the-owner-cant-afford-to-buy-from-you: <red>Dies würde {0} kosten, aber der Besitzer hat nur {1}
you-cant-afford-shop-naming: <red>Du kannst dir keine Shopbenennung leisten. Es kosted {0} um einen Shop zu benennen.
inventory-error: |-
  <red>Fehler beim Verarbeiten des Inventar-Wrappers. Verwendest du ein Add-on welches das Shop-Inventar neu verbindet? Informationen: Fehler={0}, Inventar-Wrapper={1}, Wrapper-Typ={2}, Wrapper-Bereitstellung={3}, Symbol-Link={4}. Bitte kontaktiere die Server-Administratoren.
integrations-check-failed-create: <red>Integrierung {0} hat die Shop-Erstellung verweigert
shop-out-of-space: <dark_purple>Dein Shop bei {0}, {1}, {2} ist jetzt voll.
admin-shop: AdminShop
no-anythings-in-your-hand: <red>Es befindet sich nichts in deiner Hand.
no-permission: <red>Du hast keine Berechtigung um das zu tun.
chest-was-removed: <red>Die Truhe wurde entfernt.
you-cant-afford-to-buy: <red>Es kostet {0}, aber du hast nur {1}
shops-removed-in-world: <yellow>Ein total von <aqua>{0}<yellow> Shops wurden in der Welt <aqua>{1}<yellow> gelöscht.
display-turn-off: <green>Shop-Display erfolgreich deaktiviert.
client-language-unsupported: <yellow>QuickShop unterstützt deine Client-Sprache nicht. Wir verwenden die Spracheinstellung {0} jetzt.
language-version: '63'
not-managed-shop: <red>Du bist nicht der Besitzer oder ein Moderator dieses Shops
shop-cannot-trade-when-freezing: <red>Du kannst mit diesem Shop nicht handeln, da er eingefroren ist.
invalid-container: <red>Ungültiger Container. Du kannst nur einen Shop mit Blöcken erstellen, welche ein Inventar haben.
permission:
  header: <green>Details Shop-Berechtigungen
  header-player: <green>Details Shop-Berechtigungen für {0}
  header-group: <green>Details Shop-Berechtigungen für Gruppe {0}
  table: <gold><b>{0}</b>:</gold> <gray>{1}</gray>
  item:
    purchase: <yellow>Berechtigung um Spielern, welche es haben, das kaufen vom Shop zu erlauben (Beinhaltet kaufen und verkaufen).
    show-information: <yellow>Berechtigung welche Benutzern erlaubt, die Shop-Informationen zu sehen (Shop-Infopanel zu öffnen).
    preview-shop: <yellow>Berechtigung welche Benutzern erlaubt die Shop-Vorschau (Item-Vorschau) zu sehen.
    search: <yellow>Berechtigung um Spielern, welche es haben, zu erlauben nach dem Shop zu suchen (Das entfernen der Berechtigung versteckt den Shop von Suchresultaten).
    delete: <yellow>Berechtigung welche Benutzern erlaubt, den Shop zu löschen.
    receive-alert: <yellow>Berechtigung um Spielern, welche es haben, zu erlauben Warnungen zu erhalten (z.B. Ausverkauft oder neuer handel).
    access-inventory: <yellow>Berechtigung welche Benutzern Zugriff auf das Shop-Inventar erlaubt.
    ownership-transfer: <yellow>Berechtigung, welche Benutzern erlaubt, den Shop zu übertragen.
    management-permission: <yellow>Berechtigung um Spielern, welche es haben, zu erlauben die Gruppenberechtigungen und Benutzergruppen zu bearbeiten.
    toggle-display: <yellow>Berechtigung welche Benutzern erlaubt das anzeigen des Display-Item des Shops zu ändern.
    set-shoptype: <yellow>Berechtigung um Spielern, welche es haben, zu erlauben den Shop-Typ zu ändern (Zu kaufen/verkaufen ändern).
    set-price: <yellow>Berechtigung welche Benutzern erlaubt, den Shop-Preis zu ändern.
    set-item: <yellow>Berechtigung welche Benutzern erlaubt, das Shop-Item zu ändern.
    set-stack-amount: <yellow>Berechtigung welche Benutzern erlaubt, die Stackmenge zu ändern.
    set-currency: <yellow>Berechtigung welche Benutzern erlaubt, die Shop-Währung zu ändern.
    set-name: <yellow>Berechtigung welche Benutzern erlaubt, den Shopnamen zu ändern.
    set-sign-type: <yellow>Ändere das Material des am Shop hängenden Schilds.
    view-purchase-logs: <yellow>Berechtigung um die Shop Einkaufsprotokolle zu sehen.
  group:
    everyone: <yellow>Standardgruppe für alle Benutzer ausser dem Shopeigentümer.
    staff: <yellow>Systemgruppe für Shop-Mitarbeiter.
    administrator: <red>System-Gruppe für Administratoren. Benutzer in dieser Gruppe haben fast die gleichen Berechtigungen wie der Shop-Besitzer.
invalid-group: <red>Ungültiger Gruppenname.
invalid-permission: <red>Ungültige Berechtigung.
invalid-operation: <red>Ungültige Aktion. Nur {0} sind erlaubt.
player-no-group: <yellow>Spieler {0} ist in keiner Gruppe in diesem Shop.
player-in-group: <green>Spieler {0} ist in Gruppe <aqua>{1}</aqua> in diesem Shop.
permission-required: <red>Du hast keine Berechtigung {0} um das in diesem Shop zu tun.
no-permission-detailed: <red>Du hast keine Berechtigung <yellow>{0}</yellow> um dies zu tun.
paste-notice: "<yellow>Beachte: Wenn du einen Paste für die Fehlerbehebung erstellst, stell sicher, dass du den Fehler reproduzieren kannst, bevor du den Paste so schnell wie mögloch erstellst; wir benötigen die Protokolle, welche kurzzeitig im Pffer gehalten werden, für die Fehlerbehebung. Wenn du den Paste zu langsam erstellst, oder ohne zuerst den Fehler zu reproduzieren, oder den Server neu startest, ist die Paste-Aufnahme nutzlos."
paste-uploading: <aqua>Bitte warte... Lade den Paste auf Pastebin hoch...
paste-created: '<green>Paste erstellt. Klicke um im Browser zu öffnen: <yellow>{0}</yellow><newline><red>Warnung: <gray>Sende niemals Pastes zu Personen denen du nicht traust.'
paste-created-local: |-
  <green>Paste erstellt und auf deiner lokalen Festplatte gespeichert unter: {0}<newline><red>Warnung: <gray>Sende niemals Pastes zu Personen denen du nicht traust.
paste-created-local-failed: <red>Konnte Paste nicht auf lokaler Festplatte speicher, bitte überprüfe deine Festplatte.
paste-451: |-
  <gray><bold>TIPPS:</bold> Dein aktuelles Land scheint die Verbindung zum CloudFlare Workers Service blockiert zu haben. QuickShop Pastes können eventuell nicht korrekt geladen werden.<newline><gray>Wenn die nachfolgenden Aktionen fehlschalgen, versuche den --file Parameter hinzuzufügen um einen lokalen paste zu erstellen: <dark_gray>/qs paste --file</dark_gray>
paste-upload-failed: <red>Konnte Paste nicht auf {0} hochladen, versuche anderen Pastebin-Anbieter...
paste-upload-failed-local: <red>Hochladen von Paste fehlgeschlagen, versuche lokale Paste zu erstellen...
command-incorrect: '<red>Befehlsnutzung falsch, schreibe /qs help um die Hilfe zu sehen. Verwendung: {0}.'
successfully-set-player-group: <green>Spieler {0} Gruppe erfolgreich zu <aqua>{1}</aqua> geändert.
successfully-unset-player-group: <green>Spielergruppe erfolgreich vom Shop entfernt.
successfully-set-player-permission: <green>Spieler {0} Berechtigung erfolgreich zu <aqua>{1}</aqua> in Shop <aqua>{2}</aqua> geändert.
lookup-item-created: <green>Ein Item namens <aqua>{0}</aqua> wurde in der Nachschlagetabelle erstellt. Du kannst diesen Eintrag jetzt in den Konfigurationen verwenden.
lookup-item-exists: <red>Ein Gegenstand namens <yellow>{0}</yellow> existiert bereits in der Nachschlagetabelle, lösche ihn oder wähle einen anderen Namen.
lookup-item-not-found: <red>Ein Item namens <yellow>{0}</yellow> existiert nicht.
lookup-item-name-illegal: <red>Itemname unzulässig. Nur alphanumerische Zeichen und Unterstriche sind erlaubt.
lookup-item-name-regex: '<red>Der Name muss diesem Regex entsprechen: <aqua>{0}</aqua>.'
lookup-item-test-not-found: '<gold>Test: <yellow>Das Item in deiner Hand ist nicht in der Suchtabelle registriert.'
lookup-item-test-found: '<gold>Test: <green>Das Item in deiner Hand wurde als <aqua>{0}</aqua> in der Suchtabelle registriert.'
lookup-item-removed: <green>Das angegebene Item <aqua>{0}</aqua> wurde erfolgreich von der Suchtabelle entfernt.
internal-error: <red>Ein interner Fehler ist aufgetreten. Bitte kontaktiere den Serveradministrator.
argument-cannot-be: <red>Das Argument <aqua>{0}</aqua> kann nicht <yellow>{1}</yellow> sein.
argument-must-between: <red>Der Wert von <aqua>{0}</aqua> muss zwischen <yellow>{1}</yellow> und <yellow>{2}</yellow> sein
invalid-percentage: <red>Prozentsatz ungültig, du musst '%' nach der Nummer hinzufügen.
display-fallback: |-
  <red>Aufgrund eines internen Fehlers wird eine Fallback-Nachricht angezeigt.
  Der Wert dieses Items wird eventuell nicht lokalisiert oder richtig verarbeitet.
  Bitte kontaktiere den Server Administrator.
not-a-valid-time: |-
  <red>Der Text <yellow>{0}</yellow> ist kein gültiger Zeitstempel. Bitte gib eine <aqua>Zulu-Zeit (ISO 8601)</aqua> oder <aqua>Unix-Epoch-Zeit in Sekunden</aqua> ein.
  <gold>Gültiges Zeitstempel Beispiel: (Für Samstag, 17. Dez. 2022 10:31:37 GMT):</gold>
  <aqua>-</aqua> <yellow>2022-12-17T10:31:37Z</yellow> <grey>(Zulu-Teit)</grey>
  <aqua>-</aqua> <yellow>1671273097</yellow> <grey>(Unix-Epoch-Zeit in Sekunden)</grey>
invalid-past-time: <red>Du kannst keine Zeit in der Vergangenheit setzen.
debug:
  arguments-invalid: <red>Angegebene Argumente <yellow>{0}</yellow> sind ungültig.
  sign-located: '<green>Gültiges Schild: <yellow>{0}</yellow>.'
  operation-missing: <red>Du must eine Operation angeben.
  operation-invalid: <red>Du musst eine gültige Operation angeben.
  invalid-base64-encoded-sql: <red>Die angegebene SQL-Operation muss Base64 kodiert sein.
  warning-sql: |-
    <red><bold>Warnung:</bold></red> <yellow>Du führst eine SQL-Anweisung aus. Diese könnte deine Datenbank beschädigen oder sämtliche Daten in dieser vernichten, selbst wenn sie zu einem anderen Plugin gehört.
    <red>Bestätige diese Aktion nicht, wenn du dem sender dieser nicht traust.
  warning-sql-confirm: <yellow>Um diese gefährliche Aktion zu bestätigen, führe <aqua>/qs debug database sql confirm {0}</aqua> innerhalb der nächsten 60 Sekunden aus.
  warning-sql-confirm-hover: <yellow>Klicke um diese gefährliche Aktion zu bestätigen.
  sql-confirm-not-found: <yellow>Kann die angegebene Aktion nicht finden. Sie ist eventuell ungültig oder is abgelaufen.
  sql-executing: '<yellow>Führe SQL-Answeisung aus: <aqua>{0}</aqua>'
  sql-completed: <green>Aktion abgeschlossen! {0} Zeilen betroffen.
  sql-exception: <red>Ein Fehler ist beim ausführen der SQL-Abfrage aufgetreten. Überprüfe die Konsole für Details!
  sql-disabled: '<red>Aus Sicherheitsgründen sind SQL-Abfragen in diesem Server deaktiviert. Wenn du diese Funktion wirklich nutzen willst, kannst den folgenden Argument in deinen Start-Parametern aktivieren: <aqua>{0}</aqua> mit Wert `true`.'
  force-shop-reload: <yellow>Erzwinge erneutes Laden aller geladenen Shops...
  force-shop-reload-complete: <green>Für <aqua>{0}</aqua> Shops wird ein erneutes laden erzwungen.
  force-shop-loader-reload: <yellow>Erzwinge Shop-Loader neu laden...
  force-shop-loader-reload-unloading-shops: <yellow>Entlade <aqua>{0}</aqua> geladene Shops...
  force-shop-loader-reload-unloading-shops-from-memory: <yellow>Entferne <aqua>{0}</aqua> Shops aus Speicher...
  force-shop-loader-reload-reloading-shop-loader: <yellow>Rufe Shop-Loader erneut auf, um alle Shops aus der Datenbank neu zu laden...
  force-shop-loader-reload-complete: <green>Shop-Loader hat alle Shops neu geladen!
  toggle-shop-loaded-status: <aqua>Ändere den geladen Status dieses Shops zu <gold>{0}</gold>
  shop-internal-data: '<yellow>Die internen Daten dieses Shops: <reset>{0}</reset>'
  handler-list-not-valid-bukkit-event-class: <red>Die angegebene Class <yellow>{0}</yellow> ist keine gültige Bukkit Event-Class.
  update-player-shops-signs-no-username-given: <red>Du musst einen gültigen Spielernamen angeben.
  update-player-shops-signs-create-async-task: <yellow>Erstelle async Aufgaben zu erzwingen des neu laden von Schildern...
  update-player-shops-player-selected: '<yellow>Spieler ausgewählt: <gold>{0}</gold>'
  update-player-shops-player-shops: <yellow>Insgesamt <gold>{0}</gold> Shops warten auf Updates.
  update-player-shops-per-tick-threshold: '<yellow>Maximale Anzahl Shops die pro Tick aktualisiert werden können: <gold>{0}</gold>'
  update-player-shops-complete: <green>Aufgabe erfolgreich. Aktualisierung benötigte <yellow>{0}ms</yellow>.
  update-player-shops-task-started: <gold>Die Aufgaben wurden gestartet. Bitte warte, bis sie abgeschlossen sind.
  item-info-store-as-string: "<green>Der Shop, den du ansiehst: <gold>{0}</gold> Hash: <white>{1}</white>"
  item-info-hand-as-string: "<green>Der Gegenstand in deiner Hand: <gold>{0}</gold> Hash <white>{1}</white>"
  item-matching-result: "<green>Hand zu Laden: <aqua>{0}</aqua>, Laden zu Hand: <aqua>{1}</aqua>"
  hikari-cp-size-tweak: "<green>HikariCP MaxiumumPoolSize und MinimumIdle wurden zu <white>{0}</white> geändert"
  hikari-cp-testing: "<green>Bitte warten, teste HikariCP-Verbindung..."
  hikari-cp-working: "<green>Erfolf! HikariCP funktioniert gut!"
  hikari-cp-not-working: "<red>Fehlgeschlagen! Die Verbindung welche von HikariCP zurückgegeben wurde ist tot! (Test nach 1 Sekunde nicht bestanden)"
  hikari-cp-timeout: "<red>Zeitüberschreitung von HikariCP während dem versuch eine gültige Verbindung zu erhalten, Bitte säubere alle aktiven Abfragen um Verbindungsressourcen freizugeben."
  queries-stopped: "<green><white>{0}</white> aktive Abfragen gestoppt."
  queries-dumping: "<yellow>Entferne aktive Abfragen..."
  restart-database-manager: "<yellow>Starte SQLManager neu..."
  restart-database-manager-clear-executors: "<yellow>Reinige Executors..."
  restart-database-manager-unfinished-task: "<yellow>Unerledigte Aufgaben: <white>{0}</white>"
  restart-database-manager-unfinished-task-history-query: "<yellow>Unerledigte Aufgaben (Verlaufsabfrage): <white>{0}</white>"
  restart-database-manager-reconnect: "<yellow>Starte SQLManager per Startsequenz neu (Über asynchronen Executor)"
  restart-database-manager-done: "<green>Erledigt!"
  property-incorrect: "<yellow>Du musst eine (und nur eine) Schlüssel=Wert Eigenschaft eingeben. Z.B. aaa=bbb"
  property-security-block: "<red>Anfrage wurde aus sicherheitsgründen abgelehnt. Du kannst nur die Eigenschaft, welche mit <aqua>com.ghostchu.quickshop</aqua> oder <aqua>quickshop</aqua> startet ändern."
  property-removed: "<green>Eigenschaftsschlüssel <white>{0}</white> entfernt"
  property-changed: "<green>Eigenschaftsschlüssel <white>{0}</white> wurde von <white>{1}</white> zu <white>{2}</white> geändert"
  marked-as-dirty: "<green>Alle Läden wurden als schmutzig markiert und werden beim nächsten Auto-Save gespeichert. (Starte den Server neu um einen Speicherauftrag zu erzwingen)"
  display-removed: "<green><yellow>{0}</yellow> QuickShop Anzeige Gegenstände/Entitäten von den Welten entfernt."
database:
  scanning: <green>Scanne die isolierten Daten in der QuickShop Datenbank. Die Datenbankauslastung kann eventuell den Scanvorgang ehöhen. Dies kann eine Weile dauern...
  scanning-async: <yellow>Scanne die isolierten Daten in der QuickShop Datenbank auf einem Asyncronen Auftrags-Thread. Die Datenbankauslastung kann eventuell den Scanvorgang ehöhen. Dies kann eine Weile dauern. Wenn es schiefgeht, versuch es später erneut.
  already-scanning: <red>Ein Scanauftrag wurde bereits gestartet. Warte bis dieser zuende ist.
  trim-warning: <red><bold>Warnung:</bold> <yellow>Erstelle ein Backup der Datenbank bevor du mit der Datenbankbereinigung fortfährst, um Datenverlust zu vermeiden. Sobald du bereit bist, führe <aqua>/qs database trim confirm</aqua> aus.
  status: '<yellow>Status: {0}'
  status-good: <green>Gut
  status-bad: <yellow>Wartungsarbeiten erforderlich
  isolated: '<yellow>Isolierte Daten:'
  isolated-data-ids: '<aqua>└<yellow> Datensätze: <gold>{0}</gold>'
  isolated-shop-ids: '<aqua>└<yellow> Shop-Indexe: <gold>{0}</gold>'
  isolated-logs: '<aqua>└<yellow> Logs: <gold>{0}</gold>'
  isolated-external-caches: '<aqua>└<yellow> Externe Zwischenspeicher: <gold>{0}</gold>'
  last-purge-time: <yellow>Letzter Trimmzeitpunkt um {0}
  report-time: <yellow>Letzter scan am {0}
  auto-scan-alert: <yellow>Die QuickShop Datenbank auf diesem Server benötigt Wartungsarbeiten. <gold>{0}</gold> isolierte Daten zum entfernen gefunden.
  auto-trim: <green>Auto-Schneiden wurde auf diesem Server aktiviert. Kein manuelles Schneiden benötigt.
  trim-complete: <green>Datenbank-Schneiden abgeschlossen. <yellow>{0}</yellow> isolierte Daten entfernt.
  auto-trim-started: <green>Auto-Bereinigung gestartet. Bitte warten...
  trim-start: <green>Datenbank bereinigung gestartet. Bitte warten...
  trim-exception: <red>Datenbank-Bereinigung fehlgeschlagen. Ein Fehler ist während der Aktion aufgetreten. Überprüfe die Serverkonsole.
  generated-at: '<yellow>Generiert am: <gold>{0}</gold>'
  purge-date: <red>Du musst ein Datum angeben.
  purge-warning: <yellow>Diese Aktion wird den Verlauf welcher in deiner Datenbank gespeichert ist löschen, inklusive Shop erstellungen/änderungen/löschungen, käufe, transaktionen und System logs. Das löschen dieser Daten kann zwar Speicherplatz freigeben, aber semtliche Verlaufs-Metriken gehen verloren, und andere Plugins die diese benötigen werden nicht mehr funktionieren. Um diese Aktion weiter auszuführen, führe `/qs database purgelogs <vor-tage> confirm` aus
  purge-task-created: <green>Aufgabe erstellt! Datenbank wird die Verlaufs-Einträge leise im Hintergrund entfernen.
  purge-done-with-line: <green>Lösch-Auftrag beendet. Total <gold>{0}</gold> Linien von Datenbank gelöscht.
  purge-done-with-error: <red>Lösch-Auftrag fehlgeschlagen. Überprüfe die Server-Konsole für Details.
  purge-players-cache: <yellow>Bitte warten. Leere Spieler-Caches...
  purge-players-completed: |-
    <green>{0} Spieler-Caches erfolgreich aus dem Speicher und der Datenbank entfernt.<newline><aqua>Hinweis: Deine Serverleistung könnte durch diese Vorgang beeinträchtigt werden.
  purge-players-error: <red>Konnte Spieler-Caches nicht leeren, bitte überprüfe die Serverkonsole.
  suggestion:
    trim: <yellow>Diese Datenbank benötigt eine Bereinigung von isolierten Daten. Führe <aqua>/qs database trim</aqua> aus um die Datenbank zu bereinigen.
always-counting-removal-early-warning: <red>Die 'Immer zählen' Funktion ist für eine zukünftige entfernung geplant. Du solltest sie nicht mehr verwenden, da sie in naher Zukunft aufhören wird, zu funktionieren.
exporting-database: <green>Datenbank wird exportiert, bitte warten...
exporting-failed: <red>Der Export der Datenbank ist fehlgeschlagen, bitte überprüfe die Serverkonsole.
exported-database: <green>Datenbank exportiert nach <yellow>{0}</yellow>.
importing-not-found: <red>Datei <yellow>{0}</yellow> nicht gefunden. Stelle sicher, dass der Dateipfad gültig ist.
importing-early-warning: |-
  <red><bold>Warnung:</bold> <yellow>Das Backup wird in die aktuelle Datenbank importiert. Sämtliche bereits existierenden Daten werden entfernt und sind für immer verloren, sofern du kein Backup gemacht hast.<newline><red>Bist du sicher dass du das Import-Verfahren fortsetzen willst?</red> Schreibe <aqua>/qs recovery confirm</aqua> um fortzufahren.
importing-database: <green>Datenbank wird aus dem Backup importiert, bitte warten...
importing-failed: <red>Import der Datenbank fehlgeschlagen, bitte überprüfe die Serverkonsole.
imported-database: <green>Datenbank importiert von <yellow>{0}</yellow>.
transfer-sent: <green>Shop-Übertragungsanfrage an <yellow>{0}</yellow> gesendet.
transfer-request: <yellow>Spieler <aqua>{0}</aqua> möchte gerne seine Shops auf dich übertragen. Möchtest du diese Anfrage akzeptieren?
transfer-single-request: <yellow>Spieler <aqua>{0}</aqua> möchte gerne einen Shop auf dich übertragen. Möchtest du diese Anfrage annehmen?
transfer-ask: |-
  <gold>Schreibe <red>/qs transfer accept</red> zum akzeptieren oder <red>/qs transfer deny</red> zum ablehnen.<newline>Die Anfrage wird nach <red>{0}</red> Sekunden ablaufen.
transferall-ask: |-
  <gold>Schreibe <red>/quickshop transferall accept</red> zum Akzeptieren oder <red>/quickshop transferall deny</red> zum Ablehnen. Die Anfrage wird in <red>{0}</red> Sekunden ablaufen.
transfer-single-ask: |-
  <gold>Schreibe <red>/quickshop transferownership accept</red> um zu Akzeptieren und <red>/quickshop transferownership deny</red> um Abzulehnen. Die Anfrage wird nach <red>{0}</red> Sekunden ablaufen.
transfer-accepted-fromside: <green>Spieler <aqua>{0}</aqua> hat deinen Shop-Übertragungsantrag akzeptiert.
transfer-accepted-toside: <green>Du hast <aqua>{0}</aqua>'s Tranfer-Anfrage akzeptiert.
transfer-rejected-fromside: <red>Spieler <aqua>{0}</aqua> hat deine Shop-Übertragungsanfrage abgelehnt.
transfer-rejected-toside: <red>Du hast <aqua>{0}</aqua>'s Shop-Übertragungsanfrage abgelehnt.
transfer-no-pending-operation: <red>Du hast keine ausstehenden Transfer-Anfragen.
transfer-no-self: <red>Du kannst deine Shops nicht an dich selbst übertragen.
benefit-overflow: <red>Die Summe aller Vorteile kann nicht grösser oder gleich 100% sein.
benefit-exists: <red>Spieler ist bereits in der Vorteilsliste dieses Shops.
benefit-removed: <red>Spieler wurde von Shop-Vorteilen entfernt.
benefit-added: <green>Spieler <aqua>{0}</aqua> wurde zu Shop-Vorteilen hinzugefügt!
benefit-updated: <green>Spieler <aqua>{0}</aqua>'s Vorteile wurden aktualisiert!
benefit-query: <green>Dieser Shop hat <yellow>{0}</yellow> Spieler in der Vorteilsliste!
benefit-query-list: '<yellow> - </yellow><white>Spieler <gold>{0}</gold>. Vorteil: <gold>{1}%</gold>/white>'
tag-added: '<green><aqua>#{0}</aqua> erfolgreich als Tag zu diesem Shop hinzugefügt!'
tag-add-duplicate: '<red>Der Tag <aqua>#{0}</aqua> existiert bereits für diesen Shop!'
tag-removed: '<green>Tag <aqua>#{0}</aqua> efolgreich von diesem Shop entfernt!'
tag-remove-not-exists: '<red>Der Tag <aqua>#{0}</aqua> existiert für diesen Shop nicht!'
tag-cleared: <green>Alle Tags erfolgreich von diesem Shop entfernt!
tag-shops-cleared: '<green><aqua>#{0}</aqua> wurde erfolgreich von all deinen getaggten Shops entfernt!'
tag-query: '<green>Dieser Shop hat <yellow>{0}</yellow> Tags:'
tag-query-listing: '<yellow> - <aqua>#{0}</aqua>'
tag-query-no-tag: <red>Dieser Shop hat keine Tags.
tag-query-shops: '<green>Dieser Tag hat <yellow>{0}</yellow> Shops:'
tag-query-shops-listing: <yellow> - <aqua>{0}</aqua>
batch-operations-based-on-tags-no-failure: <green><yellow>{0}</yellow> Shops erfolgreich verarbeitet.
batch-operations-based-on-tags-have-failure: <yellow>{0} Shops wurden erfolgreich in der Massenverarbeitung abgeschlossen, aber <red>{1}</red> konnten nicht abgeschlossen werden.
batch-operations-based-on-tags-have-failure-with-reason: "<yellow>{0} Shops wurden erfolgreich in der Massenverarbeitung abgeschlossen, aber <red>{1}</red> konnten nicht abgeschlossen werden.\nGrund: <gold>{2}</gold>"
batch-operations-based-on-tags-have-failure-error-messages-may-appears-above: <gray>Die Fehlermeldungen wurden dir vor dieser Nachricht im Chat gesendet.
addon:
  towny:
    commands:
      town: <yellow>Setze oder entfernen einen Shop als Stadt-Shop
      nation: <yellow>Setze oder entferne einen Shop als Land-Shop
    make-shop-owned-by-town: <green>Du hast den Shop-Besitzer zur Stadt <yellow>{0}</yellow> geändert.
    make-shop-no-longer-owned-by-town: <green>Du hast den Shop-Besitzer zurückgesetzt. Er gehört nun wieder dem original Shop-Besitzer.
    make-shop-owned-by-nation: <green>Du hast den Shop-Besitzer zur Nation <yellow>{0}</yellow> geändert.
    make-shop-no-longer-owned-by-nation: <green>Du hast den Shop-Besitzer zurückgesetzt. Er gehört nun wieder dem original Shop-Besitzer.
    shop-owning-changing-notice: <grey>Dieser Shop gehört nun einer Stadt/Nation. Der ursprüngliche Shopeigentümer wurde automatisch als Administrator hinzugefügt. Ändere oder füge weitere Besitzer/Mitarbeiter mit dem Befehl /qs permission hinzu
    target-shop-already-is-town-shop: <red>Der Zielshop ist bereits in Besitz einer Stadt.
    target-shop-already-is-nation-shop: <red>Der Zielshop ist bereits im Besitz einer Nation.
    target-shop-not-in-town-region: <red>Der Zielshop ist nicht in der Stadt.
    target-shop-not-in-nation-region: <red>Der Zielshop ist nicht in der Nation.
    item-not-allowed: <red>Das Item dieses Shops ist nicht in Stadt/Nation Shops erlaubt. Wähle ein anderes aus!
    operation-disabled-due-shop-status: <red>Diese Shop-Operation wurde deaktiviert da es bereits ein Stadt/Nation-Shop ist.
    plot-type-disallowed: <red>Du kannst keinen Stadt-/Nationen-Shop auf dieser Art von Plot erstellen.
    flags:
      own: <red>Du kannst nur Shops in von dir geclaimten Shoptyp Towny Plots erstellen.
      modify: <red>Du hast keine Bauberechtigung auf diesem Towny Plot.
      shop-type: <red>Du musst den Shop auf einem Towny Plot vom Typ Shop erstellen.
  residence:
    creation-flag-denied: <red>Du hast keine Berechtigung in dieser Residenz einen Shop zu erstellen.
    trade-flag-denied: <red>Du hast keine Berechtigung in dieser Residenz etwas zu kaufen.
    you-cannot-create-shop-in-wildness: <red>Du kannst keinen Shop in der Wildnis erstellen.
  griefprevention:
    creation-denied: <red>Du hast keine Berechtigung einen Shop in diesem Claim zu erstellen.
    trade-denied: <red>Du hast keine Berechtigung einen Shop in diesem Claim zu kaufen.
  lands:
    world-not-enabled: <red>Du kannst keine Shops in dieser Welt kaufen oder erstellen.
    creation-denied: <red>Du hast keine Berechtigung einen Shop in dieser Landregion zu erstellen.
  plotsquared:
    no-plot-whitelist-creation: <red>Du kannst keinen Shop ausserhalb des Plots erstellen.
    no-plot-whitelist-trade: <red>Du kannst keine Shops ausserhalb des Plots kaufen.
    creation-denied: <red>Du hast keine Berechtigung Shops in diesem Plot zu erstellen.
    trade-denied: <red>Du hast keine Berechtigung Shops in diesem Plot zu kaufen.
    flag:
      create: Erstelle QuickShop-Hikari Shops
      trade: Kaufe QuickShop-Hikari Shops
  superiorskyblock:
    owner-create-only: <red>Nur der Inselbesitzer kann einen Shop hier erstellen.
    owner-member-create-only: <red>Nur der Inselbesitzer oder ein Mitglied können einen Shop hier erstellen.
  worldguard:
    creation-flag-test-failed: <red>Du hast keine Berechtigung einen Shop in dieser WorldGuard Region zu erstellen.
    trade-flag-test-failed: <red>Du hast keine Berechtigung einen Shop in dieser WorldGuard Region zu handeln.
    reached-per-region-amount-limit: "<red>Du hast die maximale Anzahl an Shops in dieser Region ereicht."
  discord:
    __to_translators: 'To translators: `addon.discord.discord-messages` use a special variable in strings, %%LANG_KEY%% instead of QuickShop''s style {LANG_INDEX}, do not translate anything inside double ''%'' section. BUT for any other section texts, it respect the normal QuickShop Text System syntax ({0},{1} etc.) This may be difficult to understand because two different variable forms are used in the same program at the same time, but trust me, it is reasonable. (you don''t need translate this string, it''s a comment)'
    __to_users: Genau wie das QuickShop Textsystem kann das Discord Addon die Benutzersprache automatisch erkennen und verwendet die Sprache des Benutzers um Discord Nachrichten zu senden, basierend auf QuickShop-Hikari's Sprachsystem Einstellungen.
    __to_message_designer: 'Erstelle deine Discord Nachricht mit GUI: https://glitchii.github.io/embedbuilder/, dann kopiere den JSON-Code und füge ihn in die Übersetzung ein und wir sind fertig!'
    discord-enabled: <aqua>QuickShop Discord Nachrichten wurden erfolgreich <green>aktiviert</green>. Du wirst nun Shop Nachrichten über Discord erhalten.
    discord-disabled: <aqua>QuickShop Discord Nachrichten wurden erfolgreich <red>deaktiviert</red>. Du wirst keine weiteren Shop Nachrichten per Discord erhalten.
    discord-not-integrated: <red>Du hast deinen Discord Account noch nicht verlinkt! Bitte verlinke es zuerst!
    feature-enabled-for-user: <aqua>Du hast die <gold>{0}</gold> Benachrichtigungen <green>aktiviert</green>.
    feature-disabled-for-user: <aqua>Du hast die <gold>{0}</gold> Benachrichtigungen <red>deaktiviert</red>.
    link-help: <yellow>Dieser Server benutzt <gold>{0}</gold> für Discord-funktionalität. Bitte verwende <green>{0}</green> um deinen Discord Account zu verbinden.
    save-notifaction-exception: <red>Ein Fehler ist beim Speichern deiner Discord Benachrichtigungseinstellungen aufgetreten. Bitte kontaktiere den Server Administrator.
    feature-status-changed: <green>Benachrichtigungsstatus für <aqua>{0}</aqua> erfolgreich zu <gold>{1}</gold> gesetzt
    commands:
      discord:
        description: <yellow>Verwalte deine QuickShop Discord Einstellungen
    discord-messages:
      sold-to-your-shop: |
        {
         "embed":
           {
             "title": ":inbox_tray: Jemand hat Items an deinen Shop verkauft",
             "description": "Der Spieler %%purchase.name%% hat %%purchase.amount%% x %%shop.item.name%% an deinen Shop verkauft.",
             "color": 52084,
             "author": {
               "name": "QuickShop-Hikari",
               "icon_url": ""
             },
             "thumbnail": {
               "url": ""
             },
             "image": {
               "url": ""
             },
             "footer": {
               "text": "QuickShop-Hikari Discord Benachrichtigung",
               "icon_url": ""
             },
             "fields": [
               {
                 "name": "Shop",
                 "value": "%%shop.display-name%%",
                 "inline": false
               },
               {
                 "name": "Käufer",
                 "value": "%%purchase.name%%",
                 "inline": true
               },
               {
                 "name": "Item",
                 "value": "%%shop.item.name%%",
                 "inline": true
               },
               {
                 "name": "Menge",
                 "value": "%%purchase.amount%% (x%%shop.stacking-amount%%)",
                 "inline": true
               },
               {
                 "name": "Du zahlst",
                 "value": "%%purchase.balance-formatted%%",
                 "inline": true
               },
               {
                 "name": "Steuern",
                 "value": "%%purchase.taxes-formatted%%",
                 "inline": true
               }
             ]
           }
        }
      bought-from-your-shop: |
        {
           "embed":
             {
               "title": ":outbox_tray: Jemand hat Items von deinem Shop gekauft",
               "description": "Der Spieler %%purchase.name%% hat %%purchase.amount%% x %%shop.item.name%% von deinem Shop gekauft.",
               "color": 52084,
               "author": {
                   "name": "QuickShop-Hikari",
                   "icon_url": ""
               },
               "thumbnail": {
                   "url": ""
               },
               "image": {
                   "url": ""
               },
               "footer": {
                   "text": "QuickShop-Hikari Discord Benachrichtigung",
                   "icon_url": ""
               },
               "fields": [
                   {
                       "name": "Shop",
                       "value": "%%shop.display-name%%",
                       "inline": false
                   },
                   {
                       "name": "Käufer",
                       "value": "%%purchase.name%%",
                       "inline": true
                   },
                   {
                       "name": "Item",
                       "value": "%%shop.item.name%%",
                       "inline": true
                   },
                   {
                       "name": "Menge",
                       "value": "%%purchase.amount%%(x%%shop.stacking-amount%%)",
                       "inline": true
                   },
                   {
                       "name": "Du verdienst",
                       "value": "%%purchase.balance-formatted%%",
                       "inline": true
                   },
                   {
                       "name": "Steuern",
                       "value": "%%purchase.taxes-formatted%%",
                       "inline": true
                   }
               ]
           }
         }
      mod-shop-purchase: |
        {
        "embed":
          {
              "title": ":dollar: Jemand hat in einem Shop eingekauft",
              "description": "Der Spieler %%purchase.name%% hat %%purchase.amount%% x %%shop.item.name%% von einem Shop gekauft.",
              "color": 6061450,
              "author": {
                  "name": "QuickShop-Hikari",
                  "icon_url": ""
              },
              "thumbnail": {
                  "url": ""
              },
              "image": {
                  "url": ""
              },
              "footer": {
                  "text": "QuickShop-Hikari Discord Benachrichtigung",
                  "icon_url": ""
              },
              "fields": [
                  {
                      "name": "Shop",
                      "value": "%%shop.display-name%%",
                      "inline": false
                  },
                  {
                      "name": "Käufer",
                      "value": "%%purchase.name%%",
                      "inline": true
                  },
                  {
                      "name": "Item",
                      "value": "%%shop.item.name%%",
                      "inline": true
                  },
                  {
                      "name": "Menge",
                      "value": "%%purchase.amount%% (x%%shop.stacking-amount%%)",
                      "inline": true
                  },
                  {
                      "name": "Guthaben",
                      "value": "%%purchase.balance-formatted%%",
                      "inline": true
                  },
                  {
                      "name": "Steuern",
                      "value": "%%purchase.taxes-formatted%%",
                      "inline": true
                  }
              ]
          }
        }
      out-of-space: |
        {
        "embed":
          {
              "title": ":mailbox_with_mail: Dein Shop hat keinen Platz mehr",
              "description": "Dein Shopinventar ist voller Items!\nDu musst deinen Shop leeren um weitere Items akzeptieren zu können.",
              "color": 16065893,
              "author": {
                  "name": "QuickShop-Hikari",
                  "icon_url": ""
              },
              "thumbnail": {
                  "url": ""
              },
              "image": {
                  "url": ""
              },
              "footer": {
                  "text": "QuickShop-Hikari Discord Benachrichtigung",
                  "icon_url": ""
              },
              "fields": [
                  {
                      "name": "Shop",
                      "value": "%%shop.display-name%%",
                      "inline": false
                  }
              ]
          }
        }
      out-of-stock: |
        {
            "embed":
                {
                    "title": ":mailbox_with_no_mail: Dein Shop ist ausverkauft",
                    "description": "Dein Shopinventar ist jetzt leer!\nDu musst ihn wieder auffüllen um weiterhin Items verkaufen zu können.",
                    "color": 16065893,
                    "author": {
                        "name": "QuickShop-Hikari",
                        "icon_url": ""
                    },
                    "thumbnail": {
                        "url": ""
                    },
                    "image": {
                        "url": ""
                    },
                    "footer": {
                        "text": "QuickShop-Hikari Discord Benachrichtigung",
                        "icon_url": ""
                    },
                    "fields": [
                        {
                            "name": "Shop",
                            "value": "%%shop.display-name%%",
                            "inline": false
                        }
                    ]
                }
        }
      mod-shop-created: |
        {
          "embed": {
            "title": ":classical_building: Ein neuer Shop wurde erstellt",
            "description": "Ein Spieler hat einen neuen Shop auf deinem Server erstellt!",
            "color": 6061450,
            "author": {
              "name": "QuickShop-Hikari",
              "icon_url": ""
            },
            "thumbnail": {
              "url": ""
            },
            "image": {
              "url": ""
            },
            "footer": {
              "text": "QuickShop-Hikari Discord Benachrichtigung",
              "icon_url": ""
            },
            "fields": [
              {
                "name": "Shop",
                "value": "%%shop.display-name%%",
                "inline": false
              },
              {
                "name": "Besitzer",
                "value": "%%shop.owner.name%%",
                "inline": true
              },
              {
                "name": "Item",
                "value": "%%shop.item.name%%",
                "inline": true
              },
              {
                "name": "Menge",
                "value": "%%shop.stacking-amount%%",
                "inline": true
              },
              {
                "name": "Art",
                "value": "%%shop.type%%",
                "inline": true
              }
            ]
          }
        }
      mod-remove-shop: |
        {
            "embed": {
                "title": ":recycle: Ein Shop wurde von diesem Server entfernt",
                "description": "Ein Shop wurde von diesem Server entfernt.",
                "color": 6061450,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Benachrichtigung",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "Grund",
                        "value": "%%delete.reason%%",
                        "inline": false
                    }
                ],
            }
        }
      shop-transfer-to-you: |
        {
            "embed": {
                "title": ":placard: Ein Shop wurde auf dich übertragen",
                "description": "Ein Shop wurde von einem anderen Spieler auf dich übertragen.",
                "color": 16172079,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Benachrichtigung",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "Von",
                        "value": "%%transfer.from%%",
                        "inline": false
                    }
                ]
            }
        }
      mod-shop-transfer: |
        {
            "embed": {
                "title": ":placard: Ein Shop wurde übertragen",
                "description": "Ein Shop wurde von einem Spieler zu einem anderen übertragen.",
                "color": 6061450,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Benachrichtigung",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "Von",
                        "value": "%%transfer.from%%",
                        "inline": true
                    },
                    {
                        "name": "An",
                        "value": "%%transfer.to%%",
                        "inline": true
                    }
                ]
            }
        }
      shop-price-changed: |
        {
            "embed": {
                "title": ":money_with_wings: Dein Shoppreis wurde geändert",
                "description": "Du oder ein Shopmitarbeiter hat den Shoppreis geändert.",
                "color": 16172079,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Benachrichtigung",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "Von",
                        "value": "%%change-price.from%%",
                        "inline": true
                    },
                    {
                        "name": "Zu",
                        "value": "%%change-price.to%%",
                        "inline": true
                    }
                ]
            }
        }
      mod-shop-price-changed: |
        {
            "embed": {
                "title": ":money_with_wings: Ein Shoppreis wurde geändert",
                "description": "Ein Shop hat seine Preiseinstellungen geändert.",
                "color": 6061450,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Benachrichtigung",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": true
                    },
                    {
                        "name": "Besitzer",
                        "value": "%%shop.owner.name%%",
                        "inline": true
                    },
                    {
                        "name": "",
                        "value": "",
                        "inline": true
                    },
                    {
                        "name": "Von",
                        "value": "%%change-price.from%%",
                        "inline": true
                    },
                    {
                        "name": "Zu",
                        "value": "%%change-price.to%%",
                        "inline": true
                    }
                ]
            }
        }
      shop-permission-changed: |
        {
            "embed": {
                "title": ":closed_lock_with_key: Die Berechtigungseinstellungen deines Shops wurden geändert",
                "description": "Die Berechtigungseinstellungen eines deiner Shops wurden geändert.",
                "color": 15879747,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Benachrichtigung",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "Spieler",
                        "value": "%%change-permission.player%%",
                        "inline": true
                    },
                    {
                        "name": "Zu Gruppe hinzugefügt",
                        "value": "%%change-permission.to-group%%",
                        "inline": true
                    },
                    {
                        "name": "",
                        "value": "",
                        "inline": true
                    },
                    {
                        "name": "Erlaubte Berechtigungen (Übernommen von Gruppe)",
                        "value": "```\n%%change-permission.perms-list%%\n```",
                        "inline": false
                    }
                ]
            }
        }
    field:
      player: Spieler
      item: Item
      amount: Menge
      balance: Guthaben
      balance-after-tax: Guthaben (Nach Steuern)
      account: Dein Kontostand
      taxes: Steuern
      cost: Kosten
  discount:
    commands:
      discount:
        description: <yellow>Füge einen Rabattcode hinzu oder verwalte deine Rabattcodes.
    tab-complete:
      discount:
        general:
          code: <code>
        create:
          rate: |
            Befehlshinweis:
            Argument: <rate>
            Beschreibung: Der aktuelle Prozentsatz oder Geld, welches du verdienen wirst
            Eingabe `30%` = Preis * 0.3
            Eingabe `50` = Preis -50
          max-usage: |
            Befehlshinweis:
            Argument: [Max verwendungen]
            Beschreibung: Wie oft dieser Code verwendet werden kann
            `-1` für kein Limit.
          threshold: |
            Befehlshinweis:
            Argument: [Schwellenwert]
            Beschreibung: Der mindestpreis auf welchen der Code angewendet werden kann
            `-1` für kein Limit
          expired: |
            Befehlshinweis:
            Argument: [Ablaufdatum]
            Beschreibung: Das Ablaufdatum des Codes.
            `-1` für kein Ablaufdatum
            Akzeptiert Zulu-Zeit und UNIX-Zeitstempel in Sekunden.
            Zulu Beispiel: 2022-12-17T10:31:37Z
            UNIX Beispiel: 1671273097
    discount-code-already-exists: <red>Sorry, der Name deines Rabattcodes wird bereits verwendet.
    invalid-discount-code-regex: '<red>Der Rabattcode muss dem Regex entsprechen: <yellow>{0}</yellow>'
    invalid-discount-code: <red>Der Rabattcode ist ungültig.
    discount-code-added: <green>Dein Rabattcode <yellow>{0}</yellow> wurde dem Shop <aqua>{1}</aqua> hinzugefügt.
    discount-code-removed: <green>Dein Rabattcode <yellow>{0}</yellow> wurde vom Shop <aqua>{1}</aqua> entfernt.
    invalid-code-type: <red>Der Code-Typ <yellow>{0}</yellow> ist ungültig.
    invalid-usage-restriction: <red>Die Nutzungsbeschränkung <yellow>{0}</yellow> ist ungültig.
    invalid-threshold-restriction: <red>Die Schwellenwerdbeschränkung <yellow>{0}</yellow> ist ungültig.
    invalid-effect-scope: <red>Der Bereich <yellow>{0}</yellow> ist ungültig.
    invalid-expire-time: <red>Du kannst keine Zeit in der Vergangenheit setzen.
    invalid-discount-rate: <red>Der Rabattsatz <yellow>{0}</yellow> ist ungültig. Es kann nur eine feste Nummer oder ein prozentsatz sein.
    discount-code-expired: <red>Ouch! Dein Rabattcode <yellow>{0}</yellow> ist abgelaufen!
    discount-code-installed: <green>Dein Rabattcode <gold>{0}</gold> wurde erfolgreich aktiviert. Der Rabatt wird automatisch auf all verfügbaren Käufe und zukünftigen Käufe während dieser Session angewendet. Zum deaktivieren des Codes, verwende <aqua>/qs discount uninstal {0}</aqua>.
    discount-code-uninstalled: <green>Du hast deinen Rabattcode deaktiviert.
    discount-code-query-nothing: <red>Du hattest keinen aktiven Rabattcode!
    discount-code-query: '<green>Du verwendest den Rabattcode <yellow>{0}</yellow>.'
    discount-code-applicable: '<color:#bcef26>Dein Rabattcode <yellow><bold>{0}</bold></yellow> ist in diesem Shop <bold>verwendbar</bold>!'
    discount-code-applicable-with-threshold: '<color:#bcef26>Dein Rabattcode <yellow><bold>{0}</bold></yellow> ist auf diesen Shop <bold>anwendbar</bold> aber nur auf einen Kauf von über <yellow>{1}</yellow> in einem einzigen Kauf.'
    discount-code-not-applicable: <red>Dein Rabattcode <yellow><bold>{0}</bold></yellow> ist <bold>nicht anwendbar</bold> auf diesen Shop!
    discount-code-reach-the-limit: <red>Du hast das Nutzungslimit für den Rabattcode <yellow><bold>{0}</bold></yellow> erreicht. Rabatte werden nicht mehr angewendet.
    discount-code-no-permission: <red>Du hast keine Berechtigung, einen Rabattcode in diesem Shop anzuwenden!
    discount-code-has-been-expired: <red>Dein Rabatt ist abgelaufen!
    discount-code-config-shop-added: <green>Shop erfolgreich in deine Rabattcode Erlaubt-Liste hinzugefügt.
    discount-code-config-shop-add-failure: <red>Dieser Shop ist bereits in deiner Rabattcode Erlaubt-Liste.
    discount-code-config-shop-removed: <green>Shop erfolgreich aus der Rabattcode Erlaubt-Liste entfernt.
    discount-code-config-shop-remove-failure: <red>Dieser Shop ist nicht in deiner Rabattcode Erlaubt-Liste.
    discount-code-config-expire: <green>Rabattcode Ablaufdatum erfolgreich geändert.
    discount-code-config-applied: <green>Rabattcode erfolgreich konfiguriert!
    discount-code-created-successfully: |
      <green>Dein Rabattcode <yellow>{0}</yellow> wurde erfolgreich erstellt!
      <gold>Geltungsbereich: <aqua>{1}</aqua></gold>.
      <gold>Mit anderen teilen: <color:#bcef26>{2}</color:#bcef26></gold>.
      <yellow>Geltungsbereich nur für <gray>bestimmte Shops</gray>: Um Shops zu deiner Rabattcode Erlaubt-Liste hinzuzufügen, schaue einen Shop an und verwende den <aqua>{3}</aqua> Befehl.
      <yellow>Du kannst <aqua>/qs discount config {0}</aqua> verwenden um deinen Rabattcode jedezeit zu ändern.
    discount-code-under-threshold: <red>Der Rabattcode konnte nicht auf deinen Kauf angewendet werden, da der totale Wert unter dem Rabattcode-Schwellenwert von <yellow>{0}</yellow> lag.
    percentage-off: '<bold><color:#bcef26>-{0}%</color:#bcef26></bold>'
    fixed-off: '<bold><color:#bcef26>-{0}</color:#bcef26></bold>'
    discount-code-list: '<gold>Auflistung deiner Rabattcodes:</gold>'
    discount-code-applied-in-purchase: '<color:#bcef26>Dein Rabattcode <yellow>{0}</yellow> wurde auf diesen Einkauf angewendet und du hast <gold>{1}</gold> gespart!'
    scope:
      this-shop: '{0}'
      your-shops-owned: All deine Shops (Im Besitz)
      your-shops-managed: All deine Shops (Verwaltet)
      server: Ganzer Server
    code-type:
      SERVER_ALL_SHOPS: Alle Shops in diesem Server
      PLAYER_ALL_SHOPS: Alle Shops welche vom Codebesitzer erstellt wurden
      SPECIFIC_SHOPS: Bestimmte Shops
    discount-code-details: |-
      <gold>Code: <yellow>{0}</yellow>
      <gold>Ersteller: <yellow>{1}</yellow>
      <gold>Angewendet auf: <yellow>{2}</yellow>
      <gold>Verbleibende Nutzungen: <yellow>{3}</yellow>
      <gold>Läuft ab: <yellow>{4}</yellow>
      <gold>Schwellenwert: <yellow>{5}</yellow>
      <gold>Rabatt: <yellow>{6}</yellow>
  list:
    commands:
      list: <yellow>Zeigt alle Shops welche dir oder einem bestimmten Spieler gehören
    table-prefix: '<yellow><green>{0}</green>''s Shops <gray>(Gesamt: {1})</gray>: '
    table-prefix-pageable: '<yellow><green>{0}</green>''s Shops <gray>(Seite {1}/{2})</gray>: '
    entry: "<hover:show_text:'<yellow>{1}</yellow>\n<gray>{2} {3}, {4}, {5}</gray>\n<aqua>Preis <yellow>{6}</yellow> - <light_purple>x{7}</light_purple> <yellow>{8}</yellow>\n<green>{9}'><yellow>{0}. <aqua>{1}</aqua></hover>"
  shopitemonly:
    message: <red>Du kannst keine Nicht-Shop items in den Shop-Container legen. Alle Nicht-Shop items werden an deiner Position fallen gelassen.
  limited:
    commands:
      limit: <yellow>Lege ein Limit fest, welches die Käufe des Spielers, in einem bestimmten Zeitraum einschränkt
    titles:
      title: <green>Kauf erfolgreich
      subtitle: <aqua>Du kannst auch <gold>{0}</gold> weitere in diesem Shop kaufen
    reach-the-limit: <red>Du hast das Limit in diesem Shop erreicht. Du kannst auch <green>{0}</green> Items kaufen, aber du versuchst <yellow>{1}</yellow> Items zu kaufen.
    success-reset: <green>Einschränkungen dieses Shops erfolgreich zurückgesetzt
    success-remove: <green>All Einschränkungen in diesem Shop wurden erfolgreich entfernt
    success-setup: <green>Die Einschränkungseinstellungen für diesen Shop wurden erfolgreich gespeichert
    trade-limit-reached-cancel-reason: <red>Einschränkung dieses Shops erreicht
    remains-limits: '<gold>Dein verbleibendes Kaufvolumen in diesem Shop: <yellow>{0}'
    remains-limits-before-reset-period-note: '<gold>Dieser Shop erneuert sein Kaufvolumen alle <yellow>{0}</yellow>'
  dynmap:
    markerset-title: QuickShop-Hikari Shops
    marker-name: '{0} @ {2} @ {3} für x{4} @ {5}'
    marker-description: |
      Name: {0}
      Besitzer: {1}
      Item: {2}
      Preis: {3} für x{4} Item(s)
      Typ: {5}
      Unbegrenzt: {6}
      Ort: {7}
  bluemap:
    markerset-title: QuickShop-Hikari Shops
    marker-name: '{0} @ {2} @ {3} für x{4} @ {5}'
    marker-description: |
      Name: {0}
      Besitzer: {1}
      Item: {2}
      Preis: {3} für x{4} Item(s)
      Typ: {5}
      Unbegrenzt: {6}
      Ort: {7}
  chestprotect:
    protection-exists: <red>Diese Region wird durch ChestProtect geschützt und du hast keine Berechtigung einen Shop hier zu erstellen.
    shops-exsts: <red>Die Region welche du schützen möchtest enthält Shops anderer Spieler, und du hast nicht die Berechtigung auf diese zugreifen zu können.
  displaycontrol:
    toggle: |-
      <green>QuickShop Anzeige erfolgreich zu <aqua>{0}</aqua> geändert.
      <yellow>Du musst eventuell rejoinen damit es aktiv wird.
    toggle-exception: <red>Konnte deine Anzeigeeinstellungen nicht ändern aufgrund eines internen Fehlers. Bitte melde dies dem Serveradministrator.
    command:
      displaycontrol: <yellow>Ändere deine QuickShop Anzeige
  reremake-migrator:
    commands:
      migratefromreremake: Migriere QuickShop-Reremake's Daten zu QuickShop-Hikari
    server-not-empty: "<red>Keine Spieler dürfen während des Konvertierungsprozesses online sein. Bitte aktiviere die Server-Whitelist oder einen Wartungsmodus."
    starting-convert-progress: "<gold>Starte den Konvertierungsprozess. <red>SCHALTE DEN SERVER NICHT AUS!</red>"
    executing: "<gold>Führe Migrationskomponente <aqua>{0}</aqua> aus <dark_gray>({1}/{2})</dark_gray>..."
    completed: "<green>Fertig! Migration abgeschlossen, bitte entferne QuickShop-Reremake von dem Plugins Ordner und starte deinen Server neu."
    join_blocking_converting: "<red>[QuickShop-Hikari Migrator] Dieser Server führt eine Daten Migration aus und du kannst ihn zurzeit nicht betreten. Versuch es später noch einmal!"
    join_blocking_finished: "<red>[QuickShop-Hikari Migrator] Dieser Server hat gerade eine Datenkonvertierung abgeschlossen und wartet auf einen Serverneustart um die Änderungen zu übernehmen. Versuch es später noch einmal!"
    failed: "<red>Migrationsfortschritt wurde mit einem Fehler beendet. Bitte überprüfe die Server-Konsole."
    modules:
      config:
        copy-values: "<yellow>Kopiere Werte (insgesamt {0} Einträge)..."
        copying-value: "<gray> - Kopiere <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        migrate-price-restriction: "<yellow>Migriere Preisebeschränkungs-bezogene Einstellungen..."
        migrate-price-restriction-entry: "<gray> - Migriere <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
      shop:
        start-migrate: "<yellow>Migiere Shops (insgesamt {0} Einträge)..."
        migrate-entry: "<gray> - Migriere <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        unloading-reremake: "<gold>Deaktiviere Reremake um Datenüberschreibungen zu verhindern..."
        register-entry: "<gray> - Registriere <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        save-entry: "<gray> - Speichere <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        saving-shops: "<yellow>Speichere <gold>{0}</gold> Shop(s). Dies kann einen Augenblick dauern™ (Die Zeit basiert auf die Anzahl an Shops)..."
        conflict: "<gray> - KONFLIKT > Konflikt zwischen Reremake Shop und einem existierenden Hikari Shop Platzierung erkannt. Führe vordefinierte Aktion aus: <dark_gray>{0}</dark_gray>"
      translation:
        start-migrate: "<yellow>Migriere Übersetzungen...</yellow>"
        copy-values: "<yellow>Kopiere Inhalte von Sprache <gold>{0}</gold> (insgesamt {1} Inhalte)..."
        migrate-entry: "<gray> - Migriere <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        copying-value: "<gray> - Kopiere <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
      shop-logs:
        start-migrate: "<yellow>Migriere Shop Logs (Details in Konsole), bitte warten...</yellow>"
        extract-history-files: "<gray>Bitte warte, bis wir alles entpackt und die Verlaufsprotokolle angehängt haben..."
        filter-history-files: "<gray>Bitte warte während wir die Verlaufsprotokolle filtern..."
        filtered-history-files: "<gray>{0} Linien aus der Warteschlange gefiltert."
        import-entry: "<gray> - Migriere <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
compat:
  advancedchests:
    created: <green>Du hast erfolgreich einen Shop auf einem AdvancedChests Container erstellt!
    permission-denied: <red>Sorry! Du hast keine Berechtigung um einen Shop auf einen AdvancedChests Container zu erstellen!
suite:
  dynmap:
    title: '{0} - {1} - {2}'
    description: |-
      Item: {0}
      Besitzer: {1}
      Typ: {2} {3}
      Preis: {4}
      Ort: {5} {6}, {7}, {8}
      Raum: {9}
      Lagerbestand: {10}
  limited:
    command-description: <yellow>Lege ein Limit fest, welches die Käufe des Spielers, in einem bestimmten Zeitraum einschränkt.
    reach-the-quota-limit: <red>Du hast das Kauflimit in diesem Shop erreicht ({0}/{1}).
    quota-reset-countdown: <yellow>Das Kontingent in diesem Shop wird in {0} zurückgesetzt.
    quota-reset-player-successfully: <green>Das Kontingent für den Spieler {0} in diesem Shop wurde erfolgreich zurückgesetzt.
    quota-reset-everybody-successfully: <green>Das Kontingent für alle in diesem Shop wurde erfolgreich zurückgesetzt.
    quota-setup: <green>Die Kaufeinschränkung gilt nun für diesen Shop!
    quota-remove: <green>Die Kaufeinschränkung wurde von diesem Shop entfernt!
    subtitles:
      title: <green>Kauf erfolgreich
      subtitle: <aqua>Du kannst <yellow>{0}</yellow> weitere in diesem Shop kaufen
  list:
    command-description: <yellow>Liste alle Shops die dir oder einem anderen Spieler gehören.
    table-prefix: <yellow>Dir gehören <aqua>{0}</aqua> Shops auf diesem Server.
    entry-syntax: <aqua>- <yellow>{0}
    entry-syntax-fallback-name: '<yellow>Item: {0} X: {1}, Y: {2}, Z: {3}, Welt: {4}'
    hover-text: |-
      <yellow>{0}
      <gray>{1} {2}, {3}, {4}
      <aqua>{5} - <yellow>{6}
      <yellow>{7} Items pro Stack.
  shopitemonly:
    message: <red>Du kannst keine Nicht-Shop items in den Shop-Container legen. Alle Nicht-Shop items werden an deiner Position fallen gelassen.
compatibility:
  elitemobs:
    soulbound-disallowed: Du kannst Items mit Seelenverzauberung von EliteMobs nicht handeln.
internet-paste-forbidden-privacy-reason: "<red>Fehlgeschlagen! Basierend auf deinen Privatsphäre-Einstellungen ist QuickShop nicht berechtigt, deinen Paste im Internet hochzuladen. Aktiviere DIAGNOSTIC Berechtigungen in den Privatsphäre-Einstellungen in der config.yml oder verwende <aqua>/quickshop paste --file</aqua>."
no-sign-type-given: "<red>Du musst ein Schildmaterial angeben. Verfügbare Schildmaterialien auf diesem Server: {0}"
sign-type-invalid: "<red>Der Typ <yellow>{0}</yellow> ist kein gültiges Schildmaterial."
delete-controlpanel-button-confirm: "<red>Möchtest du wirklich diesen Shop entfernen? Klicke den <bold>[Shop entfernen]</bold> Knopf innerhalb von {0} Sekunden erneut zum bestätigen."
cannot-suggest-price: "<red>Sorry, aktuell gibt es nicht genug Spieler welche das selbe Item handeln um daraus einen empfohlenen Preis zu generieren."
price-suggest: "<green>Basierend auf den Daten von <aqua>{0}</aqua> Shops ist der höchste Preis, für welchen ein Shop das Item verkauft <light_purple>{1}</light_purple>, der niedrigste Preis <light_purple>{2}</light_purple>, der durchschnittliche Preis <light_purple>{3}</light_purple> und der Median Preis<light_purple>{4}</light_purple>.<newline><yellow>Es wird empfohlen, dass du den Preis auf um die <gold>{5}</gold> setzt.</yellow>"
suggest-wait: "<green>Bitte warten... Berechne empfohlenen Preis."
history:
  shop:
    gui-title: "Zeige Kaufprotokolle"
    header-icon-multiple-shop: "<white>Das Abfrageergebnis in {0} shops</white>"
    header-icon-description:
      - "<white>Typ: <yellow>{0}</yellow></white>"
      - "<white>Besitzer: <yellow>{1}</yellow></white>"
      - "<white>Gegenstand: <yellow>{2}</yellow></white>"
      - "<white>Preis: <yellow>{3} <aqua>x{4}</aqua></yellow></white>"
      - "<white>Ort: <yellow>{5}</yellow></white>"
    log-icon-title: "<reset><green>Zeit: {0}</green>"
    log-icon-description:
      - "<white>Käufer: <yellow>{0}</yellow></white>"
      - "<white>Gegenstand: <yellow>{1} <aqua>x{2}</aqua></yellow></white>"
      - "<white>Guthaben: <yellow>{3}</yellow></white>"
      - "<white>Steuer: <yellow>{4}</yellow></white>"
    log-icon-description-with-store-name:
      - "<white>Laden: <yellow>{0}</yellow></white>"
      - "<white>Käufer: <yellow>{1}</yellow></white>"
      - "<white>Gegenstand: <yellow>{2} <aqua>x{3}</aqua></yellow></white>"
      - "<white>Guthaben: <yellow>{4}</yellow></white>"
      - "<white>Steuern: <yellow>{5}</yellow></white>"
    query-icon: "<gray>Bitte warten, abfrage...</gray>"
    previous-page: "<white><< Vorherige Seite</white>"
    next-page: "<white>Nächste Seite >></white>"
    current-page: "<white>Seite {0}</white>"
    summary-icon-title: "<green>Shop-Übersicht"
    recent-purchases: "<white>Neuste <aqua>{0}</aqua> Käufe: <yellow>{1}</yellow></white>"
    recent-purchase-balance: "<white>Neuste <aqua>{0}</aqua> Umsätze: <yellow>{1}</yellow></white>"
    total-purchases: "<white>Gesamtkäufe: <yellow>{0}</yellow></white>"
    total-balances: "<white>Gesamtumsätze: <yellow>{0}</yellow></white>"
    total-unique-purchasers: "<white>Gesamte Einzelkäufe: <yellow>{0}</yellow></white>"
    top-n-valuable-customers-title: "<gold>Top {0} reichste Kunden</gold>"
    top-n-valuable-customers-entry: "<white>- <yellow>{0}</yellow> <aqua>{1}</aqua></white>"
    no-result: "<white>Keine Ergebnisse</white>"
about:
  text:
    - "<b><aqua>QuickShop <yellow>{0}</yellow></aqua></b>"
    - "<aqua>Version <yellow>>></yellow> <green>{1}</green></aqua>"
    - "<aqua>Release <yellow>></yellow> <green>{2}</green></aqua>"
    - "<aqua>Entwickler <yellow>>></yellow> <color:#218bff><hover:show_text:'<yellow>Klicke um Mitwirkende zu sehen'><click:open_url:'https://github.com/Ghost-chu/QuickShop-Hikari/graphs/contributors'>[Zeige Mitwirkende auf GitHub]</click></hover></color></aqua>"
    - "<aqua>Lokalisierte Mitglieder <gold>({4})</gold>: <yellow>></yellow> <green>{5}</green></aqua>"
    - "  <yellow>+ <color:#43A047><hover:show_text:'<yellow>Klicke um Crowdin Übersetzungs-Seite zu öffnen'><click:open_url:'https://crowdin.com/project/quickshop-hikari'>[Hilf beim Übesetzen auf Crowdin]</click></hover></color></yellow>"
    - "<aqua>Spendenschlüssel <yellow>>></yellow> <green>{6}</green></aqua>"
    - "<gold>Unterstützt von der Community</gold> <red>Mit ❤ gemacht</red>"
  valid-donation-key: "<color:#00AFF1>Gebunden an <color:#F9C23C>{0}</color></color>"
  invalid-donation-key: "<gray>Ungültiger Spendenschlüssel</gray>"
  kofi-thanks: "<gold>Besonderer Dank geht an alle, welche QuickShop auf Ko-fi unterstützen :)</gold>"
history-command-leave-blank: "<Leer lassen um Laden zu sehen>"
shop-information-not-shown-due-an-internal-error: "<red>Ein interner Fehler ist aufgetreten. Das Laden-Informationsfenster wird möglicherweise unvollständig anzeigt. Bitte kontaktiere den Serveradministrator."
