break-shop-use-supertool: <yellow>你可以使用超級工具破壞商店。
fee-charged-for-price-change: <green>你花費了 <red>{0}</red> 變更商店定價。
not-allowed-to-create: <red>你無法在此建立商店。
disabled-in-this-world: <red>已停用此世界的 QuickShop
how-much-to-trade-for: <green>請在聊天室中輸入你想買賣 <yellow>{1} 個 {0} <green>的價格。
client-language-changed: <green>QuickShop 偵測到你的用戶端語言已切換，我們現在為你切換為 {0}。
shops-backingup: 正在把商店資料備份到資料庫……
_comment: 你好翻譯者！假若你正在從 Github 或其他代管平台編輯，你應此前往 https://crowdin.com/project/quickshop-hikari 進行翻譯。
unlimited-shop-owner-changed: <yellow>已變更此無限商店擁有者為 {0}。
bad-command-usage-detailed: '<red>無效的指令引數！下列為接受的引數：<gray>{0}'
thats-not-a-number: <red>無效的數字
shop-name-disallowed: <red>商店的名稱 <yellow>{0}</yellow> 已被禁止使用。請嘗試使用另一個名稱！
console-only-danger: <red>由於這個是一個有風險的指令，請你在控制台中執行。
not-a-number: <red>輸入內容只能是數字，但你輸入了 {0}。
not-looking-at-valid-shop-block: <red>無法找到建立商店的方塊，你需要先注視著商店方塊。
shop-removed-cause-ongoing-fee: <red>你在 {0} 的商店已被移除，因為你沒有足夠的資金去維護它！
tabcomplete:
  amount: '[數量]'
  item: '[物品]'
  price: '[價格]'
  name: '[name]'
  range: '[範圍]'
  currency: '[貨幣名稱]'
  percentage: '[percentage%]'
taxaccount-unset: <green>此商店的稅金帳戶現在按照伺服器全域設定。
blacklisted-item: <red>由於這是黑名單的物品，所以你不能賣
command-type-mismatch: <red>此指令只能由 <aqua>{0} <red>執行。
server-crash-warning: '<red>警告：使用 /quickshop reload 重新載入命令替換 / 刪除 QuickShop Jar 檔可能會在伺服器運行時崩潰。'
you-cant-afford-to-change-price: <red>你需要花費 {0} 來變更商品價格。
safe-mode: <red>QuickShop 現在處於安全模式，你無法打開此商店容器，請聯絡伺服器管理員以修復錯誤！
forbidden-vanilla-behavior: <red>此操作不符合原版行為，因此被禁止。
shop-out-of-space-name: <dark_purple>你的商店 {0} 已滿！
paste-disabled: |-
  <red>已停用 Paste 功能！你將無法請求技術支援。
  原因：{0}
quick-fill:
  entry-own: ' <yellow>- <red>[own] <aqua>{0} <light_purple>{1}'
  hover:
    - '<yellow>名稱：<aqua>{0}'
    - '<yellow>擁有者：<aqua>{0}'
    - '<yellow>類型：<aqua>{0}'
    - '<yellow>價格：<aqua>{0}'
    - '<yellow>物品：<aqua>{0}'
    - '<yellow>座標：<aqua>{0}'
  hover-arg-filled:
    - '<yellow>名稱：<aqua>{name}'
    - '<yellow>擁有者：<aqua>{owner}'
    - '<yellow>類型：<aqua>{type}'
    - '<yellow>價格：<aqua>{price}'
    - '<yellow>物品：<aqua>{item}'
    - '<yellow>座標：<aqua>{location}'
  header: '<yellow>你有多個名為「<green>{0}<yellow>」的商店，請選擇一個以繼續：'
  entry-normal: ' <yellow>- <aqua>{0} <light_purple>{1}'
  entry-cooperation: ' <yellow> <red> [co] <aqua> {0} <light_purple> {1}'
3rd-plugin-build-check-failed-admin: '<gray>（僅限管理員）<light_purple>{0} <dark_gray>拒絕權限檢查。如果不是預期所見到的，請新增 <light_purple>{1} <gray>進監聽黑名單。設定指南：https://ghost-chu.github.io/QuickShop-Hikari-Documents/docs/modules/shops/protection-checker'
average-price-nearby: '<green>附近的平均價格：<yellow>{0}'
inventory-check-global-alert: "<red>[庫存檢查] <gray>警告！找到商店全像顯示<gold>{2}</gold> 在庫存中 <aqua>{0}</aqua> <dark_gray>[{1}]</dark_gray>，這不應該發生，這通常表示有人惡意利用該漏洞來複製顯示物品。"
digits-reach-the-limit: <red>你的價格超過小數點後位數上限。
currency-unset: <green>已成功地移除商店貨幣。現在使用預設設定。
you-cant-create-shop-in-there: <red>你沒有在這個地方建立商店的權限。
no-pending-action: <red>你沒有待處理的操作
refill-success: <green>補貨成功
failed-to-paste: <red>無法將資料上傳至 Pastebin，請檢查你的連線後再試一次。（請查看控制台以取得詳細資訊）
shop-out-of-stock-name: <dark_purple>你在 {0} 出售 {1} 的商店已售罄一空！
shop-name-invalid: <red>商店的名稱 <yellow>{0}</yellow> 無效。請嘗試使用另一個名稱！
how-many-buy-stack: <yellow>在聊天室中輸入你想 <aqua>購買</aqua> 的數量，每份中有 <green>{0}</green> 個物品，你目前可以買 <green>{1}</green> 份。輸入 <aqua>{2}</aqua> 來購買所有物品。
exceeded-maximum: <red>此值已超過 Java 中的最大值。
unlimited-shop-owner-keeped: '<yellow>注意：此商店擁有者還是屬於無限商店類型，你需要自己重新設定新的商店擁有者。'
no-enough-money-to-keep-shops: <red>你沒有足夠的金錢維持你的商店經營！所以你所有的商店已被移除……
3rd-plugin-build-check-failed: <red>第三方外掛程式 <bold>{0}</bold> 拒絕檢查權限操作，請問你有權限建立商店嗎？
not-a-integer: <red>輸入一定是數字，但你輸入了 {0}。
translation-country: '語言：繁體中文 Chinese Traditional（zh_TW）'
buying-more-than-selling: '<red>警告：你收購的物品比出售的物品多！'
purchase-failed: '<red>交易失敗：發生錯誤，請向伺服器管理員報告此問題。'
denied-put-in-item: <red>你不能將此物品放入你的商店！
shop-has-changed: <red>你嘗試交易的商店在點擊後已有變動！
flush-finished: <green>成功地清除所有最近的商店記錄。
no-price-given: <red>請提供一個有效的價格。
shop-already-owned: <red>這已是一個商店了。
backup-success: <green>備份成功。
not-looking-at-shop: <red>無法找到商店！<yellow>你需要先注視著其中一個容器。
you-cant-afford-a-new-shop: <red>需要花費 <green>{0}<red> 才能建立商店。
success-created-shop: <green>已成功地建立商店。
shop-creation-cancelled: <red>已取消建立商店。
shop-owner-self-trade: <yellow>你正在跟自己開的商店進行交易。<gray>所以你的餘額不會改變。
purchase-out-of-space: <red>此商店空間不足！請聯絡商店擁有者或商店員工清空商店。
reloading-status:
  success: <green>外掛程式成功地重新載入且沒有發生任何錯誤。
  scheduled: <green>重新載入完成！<gray>（<yellow>一些更改需要一段時間後作用<gray>）
  require-restart: <green>重新載入完成。<gray>（<yellow>一些更改需要重新啟動伺服器後作用<gray>）
  failed: <red>無法重新載入，請檢查伺服器控制台錯誤
player-bought-from-your-store-tax: <green>{0} 從你的商店購買了 {1} 個 {2}，總共讓你進賺了 {3} （{4} 的稅） 。
not-enough-space: <red>你只剩下 <yellow>{0}<red> 格空間可以裝東西！
shop-name-success: <green>成功地將商店名稱設定為 <yellow>{0}<green>。
shop-staff-added: <green>已成功地新增員工 <yellow>{0}<green> 到你的商店。
shop-staff-empty: <yellow>此商店沒有任何員工
shops-recovering: 正在嘗試從備份檔案中還原所有商店……
virtual-player-component-hover: "<gray>這是一個虛擬玩家。\n<gray>引用具有相同名稱的此名稱的系統帳戶。</gray>\n<green>UUID：<yellow>{0}</yellow></green>\n<green>使用者名稱：<yellow>{1}</yellow></green>\n<green>顯示為：<yellow>{2}</yellow></green>"
real-player-component-hover: "<gray>這是一個真實存在的玩家。\n<green>UUID：<yellow>{0}</yellow></green>\n<green>使用者名稱：<yellow>{1}</yellow></green>\n<green>顯示為：<yellow>{2}</yellow></green>\n<gray>如果要使用同名的虛擬系統帳戶，請新增 <dark_gray>\"[]\"</dark_gray> 到使用者名的兩側：<dark_gray>[{1}]</dark_gray>。"
menu:
  sell-tax: <green>你付了 <yellow>{0}</yellow> 的稅。
  owner: '<green>擁有者：{0}'
  preview: <gray>[<aqua>物品預覽<gray>]
  enchants: <dark_purple>附魔
  sell-tax-self: <green>因為你是商店主人，所以不需要繳稅。
  shop-information: '<green>商店資訊：'
  item: '<green>商品：<yellow>{0}'
  price-per: <green>每個 <yellow>{0}<green> 價格 <yellow>{1}
  item-name-and-price: <yellow>{0} 個 {1} <green>為</green> {2}
  item-name-and-price-tax: <yellow>{0} 個 {1} <green>為</green> {2} <gray>(<green>{3}</green> 的稅 ）
  successful-purchase: '<green>已成功地購買：'
  price-per-stack: <green>每 <yellow>{2}<green> 個 <yellow>{0}<green> 組價格 <yellow>{1}
  stored-enchants: <dark_purple>附魔
  item-holochat-error: <red>[錯誤]
  this-shop-is-selling: <green>此商店正在<aqua>出售<green>物品。
  shop-stack: '<green>每份物品的數量：<yellow>{0}'
  space: '<green>空間：<yellow>{0}'
  effects: <green>藥水效果
  damage-percent-remaining: <green>剩餘 <yellow>{0}% <green>點耐久度。
  item-holochat-data-too-large: <red>[錯誤] 物品 NBT 太長無法顯示
  stock: '<green>庫存：<yellow>{0} 個'
  this-shop-is-buying: <green>此商店正在<light_purple>收購<green>物品。
  successfully-sold: '<green>成功地出售：'
  total-value-of-chest: '<green>儲物箱中的物品價值：<yellow>{0}'
currency-not-exists: <red>找不到你想要設定的貨幣，可能是拼寫錯誤或此世界無法使用貨幣。
no-nearby-shop: <red>找不到符合 {0} 的商店。
translation-author: 'Ghost_chu、Andre_601、flandretw'
integrations-check-failed-trade: <red>整合模組 {0} 拒絕了與商店交易的操作。
shop-transaction-failed: <red>抱歉，由於系統發生錯誤，你的購買已被取消，款項已返回原本的地方。如果問題持續發生，請聯絡伺服器管理員。
success-change-owner-to-server: <green>成功地將商店擁有者轉交給伺服器。
shop-name-not-found: <red>商店名稱：<yellow>{0}</yellow> 並不存在。
shop-name-too-long: <red>商店的名稱太長了（最大長度 {0}），請再想想別的吧！
metric:
  header-player: '<yellow>{0} 的 {1} {2} 交易：'
  action-hover: <yellow>{0}
  price-hover: <yellow>總共 {0}，包括 {1} 的稅。
  unknown: <gray>（未知）
  undefined: <gray>（無名）
  no-results: <red>找不到交易紀錄
  action-description:
    DELETE: <yellow>玩家刪除了商店，所以商店的任何建立費用已歸擁有人。
    ONGOING_FEE: <yellow>在購買時段，玩家支付了進行費。
    PURCHASE_BUYING_SHOP: <yellow>玩家從商店出售了一些物品。
    CREATE: <yellow>玩家建立了一個商店。
    PURCHASE_SELLING_SHOP: <yellow>玩家從商店購買了一些物品。
    PURCHASE: <yellow>從商店購買了物品
  query-argument: '查詢引數：{0}'
  amount-hover: <yellow>{0} 個
  header-shop: '<yellow>{0} 商店的 {1} {2} 交易：'
  player-hover: |-
    <yellow>{0}
    <gold>UUID：<gray>{1}
  looking-up: <yellow>正在進行指標搜尋，請稍候……
  tax-hover: <yellow>{0} 稅款
  header-global: '<yellow>伺服器{0} {1} 交易：'
  na: <gray>N/A
  transaction-count: <yellow>{0} 總共
  shop-hover: |-
    <yellow>{0}
    <gold>座標：<gray>{1} {2} {3}，世界：{4}
    <gold>擁有者：<gray>{5}
    <gold>商店類型：<gray>{6}
    <gold>物品：<gray>{7}
    <gold>價格：<gray>{8}
  time-hover: '<yellow>時間：{0}'
  amount-stack-hover: <yellow>{0} 組
permission-denied-3rd-party: <red>權限被第三方外掛程式 {0} 拒絕。
you-dont-have-that-many-items: <red>你的身上只有 {0} 個 {1}。
complete: <green>完成！
translate-not-completed-yet-url: '{0} 的翻譯完成度為 {1}，你想協助我們改進翻譯嗎？前往 Crowdin：{2}'
success-removed-shop: <green>已移除商店。
currency-set: <green>成功地設定商店貨幣為 {0}。
shop-purged-start: <green>已開始商店清理，請查看控制台以取得詳細資訊。
economy-transaction-failed: <red>抱歉，由於系統發生錯誤，你的交易已被取消，款項已返回原本的地方。如果問題持續發生，請聯絡伺服器管理員。
nothing-to-flush: <green>沒有關於商店的新訊息。
no-price-change: <red>這不會導致價格變動！
edition-confilct: QuickShop-Hikari 與 QuickShop-Reremake 同時安裝在伺服器中可能會相互衝突，你需要解除安裝其中一個。
inventory-unavailable: |-
  <red>不存在或無效的 InventoryWrapper。你是否使用外掛程式來重新綁定商店庫存？
  資訊：InventoryWrapper={0}、WrapperProvider={1}、SymbolLink={2}。請聯絡伺服器管理員。
file-test: 這是個測試的文字檔案，我們使用它來測試 messages.json 是否損壞，你可以在這裡填入任何喜歡的彩蛋。 :)
unknown-player: <red>找不到相對應的玩家，請檢查你輸入的玩家名稱是否正確。
player-offline: <red>目標玩家離線。
player-profile-format: <aqua>{0}</aqua><gold>(<yellow>{1}</yellow>)
shop-type:
  selling: 出售中
  buying: 收購中
language:
  qa-issues: '<yellow>品質認證問題：<aqua>{0}%'
  code: '<yellow>代碼：<gold>{0}'
  approval-progress: '<yellow>核准進度：<aqua>{0}%'
  translate-progress: '<yellow>翻譯進度：<aqua>{0}%'
  name: '<yellow>名稱：<gold>{0}'
  help-us: <green>[協助我們提升翻譯品質]
warn-to-paste: |-
  <yellow>收集資料並上傳到 Pastebin。這可能需要一段時間……
  <red><bold>警告：</bold>資料將會公開一週！所以可能會洩漏伺服器中的設定或其他資料。請確保傳送該連結的人是<bold>值得信賴的工作人員/開發人員。
how-many-sell-stack: <green>在聊天室中輸入你想 <aqua>出售<green> 的數量，每份中有 <yellow>{0}<green> 個物品，你目前可以售出 <yellow>{1}<green> 份。輸入 <aqua>{2}<green> 來出售所有物品。
updatenotify:
  buttontitle: '[現在更新]'
  onekeybuttontitle: '[一鍵更新]'
  label:
    github: '[GitHub]'
    ore: '[Ore]'
    lts: '[長期支援 LTS]'
    nbtapi: '[+NBTAPI]'
    qualityverifyed: '[品質認證 Quality]'
    master: '[主要分支 Master]'
    unstable: '[不穩定 Unstable]'
    paper: '[+Paper 最佳化]'
    stable: '[穩定 Stable]'
    spigotmc: '[SpigotMC]'
    papermc: '[PaperMC]'
    bukkitdev: '[BukkitDev]'
    polymart: '[Polymart]'
    modrinth: '[Modrinth]'
    basic: '[Basic]'
  list:
    - 'QuickShop {0} 已正式發布，但你還在使用 QuickShop {1}！'
    - 砰！新的 QuickShop {0} 來了，快更新！
    - 驚喜！QuickShop {0} 出來了，但你還在使用 QuickShop {1}。
    - 看來你需要更新，QuickShop {0} 已正式發布了！
    - 糟糕！QuickShop {0} 已正式發布了，但你還在使用 QuickShop {1}！
    - 我保證，QS 已更新到了 {0}，但你怎麼還沒有更新？
    - 修復並重……抱歉，QuickShop {0} 已正式發布了！
    - 錯誤！不，其實並沒有錯誤，QuickShop {0} 已正式發布！
    - 我的天啊！QuickShop {0} 已出來了！你怎麼還在用 QuickShop {1}？
    - '今日新聞：QuickShop 已正式發布 {0} 的更新！'
    - 外掛程式已陣亡，請更新到 {0}！
    - QuickShop {0} 更新發布了，現在就下載拯救更新吧！
    - 指揮官，QuickShop {0} 新版本發布了！
    - 看看我的新風格── QuickShop {0} 出來了，你還在使用 QuickShop {1}。
    - 啊啊啊啊啊啊啊！QuickShop 更新 {0} 來了！快更新！
    - 你在想什麼呢？QuickShop {0} 已正式發布！請更新！
    - 博士，QuickShop 有新的更新 {0}！你應該考慮一下更新了～
    - Ko～ko～da～yo～ QuickShop 有新的更新 {0}～
    - 派蒙想告訴你 QuickShop 有新的更新 {0}！
  remote-disable-warning: '<red>此版本的 QuickShop 已被遠端伺服器標記為停用，這意味著此版本可能存在嚴重問題，請從我們的 Spigot 外掛程式頁面取得詳細資訊：{0}。在你切換到穩定版本之前，此警告將繼續出現，但不會影響伺服器的效能。'
purchase-out-of-stock: <red>此商店已缺貨，請聯絡商店擁有者或商店員工補充庫存。
nearby-shop-entry: '<green>-資訊：{0} 價格：<aqua>{1} <green>X：<aqua>{2} <green>Y：<aqua>{3} <green>Z：<aqua>{4} <green>距離：<aqua>{5} <green> 個方塊'
chest-title: QuickShop 商店
console-only: <red>此指令只能在控制台執行。
failed-to-put-sign: <red>你的商店周圍沒有足夠的空間，放置商店的資訊告示牌。
shop-name-unset: <red>此商店名稱已被移除
shop-nolonger-freezed: <green>你解凍了此商店，現在已恢復正常！
no-permission-build: <red>你沒有建立商店的權限。
tableformat:
  left_half_line: <dark_purple>+--------------------
  right_half_line: <dark_purple>--------------------+
  full_line: <dark_purple>+---------------------------------------------------+
  left_begin: '<dark_purple>| '
quickshop-gui-preview: QuickShop 物品預覽介面
translate-not-completed-yet-click: '{0} 的翻譯完成度為 {1}，你想協助我們改進翻譯嗎？按這裡！'
taxaccount-invalid: <red>無效的目標帳戶，請輸入有效的玩家名稱或 uuid（須加破折號）。
player-bought-from-your-store: <red>{0} 從你的商店購買了 {1} 個 {2}，總共讓你進賺了 {3}。
reached-maximum-can-create: <red>你建立的商店數量已達到上限（你已建立 {0} 個，最多 {1} 個！）
reached-maximum-create-limit: <red>你創造的商店數量已達上限
translation-version: '支援版本：Hikari'
no-double-chests: <red>你沒有建立大型儲物箱商店的權限。
price-too-cheap: <red>價格必須大於 <yellow>${0}
shop-not-exist: <red>商店不存在。
bad-command-usage: <red>無效的指令引數！
cleanghost-warning: <yellow>此指令將清理<red>所有</red>在不允許建立商店的世界的出售 / 購買不允許的物品，以及<bold><red>所有在伺服器沒有載入世界的商店</red></bold>。請確保你的商店資料備份完整後繼續使用 <aqua>/quickshop cleanghost confirm </aqua> 以繼續。
cleanghost-starting: <green>正在開始檢查幽靈商店（缺少容器方塊）。所有不存在的商店將被移除……
cleanghost-deleting: <yellow>發現一個損壞的商店 <aqua>{0}</aqua> 因為 {1}，標記將它刪除……
cleanghost-deleted: <green>總共 <yellow>{0}</yellow> 個商店已被刪除。
shop-purchase-cancelled: <red>已取消購買。
bypassing-lock: <red>你繞過了 Quickshop 商店鎖！
bungee-cross-server-msg: '<yellow>QuickShop 跨服訊息（CSM）：<green>{0}'
saved-to-path: 備份檔案已被儲存到 {0}。
shop-now-freezed: <green>你已凍結了商店，現在沒有人可以與這間商店交易！
price-is-now: <green>現在你的商店定價是 <yellow>{0}。
shops-arent-locked: <red>請注意，商店無法防盜！如果你想要防盜功能，請使用 LWC、Lockette 等外掛程式鎖住商店！
that-is-locked: <red>此商店已上鎖。
shop-has-no-space: <red>此商店只能再裝下 {0} 個 {1}。
safe-mode-admin: <red><bold>警告：</bold>此伺服器上的 QuickShop 現在在安全模式下運行，沒有任何功能可供使用，請輸入 <yellow>/quickshop </yellow>指令以檢查任何錯誤！
shop-stock-too-low: <red>此商店只剩 {0} 個 {1}！
world-not-exists: <red>世界 <yellow>{0}<red> 不存在
how-many-sell: <green>在聊天室中輸入你想要 <light_purple>出售 <green>的數量，你有 <yellow>{0} <green>個可以出售。輸入 <aqua>{1} <green>賣出全部。
shop-freezed-at-location: <yellow>你在 {1} 的商店 {0} 被凍結了！
translation-contributors: '貢獻人員：Timtower、Netherfoam、KaiNoMood、Mgazul、JackTheChicken 和 Andre_601'
empty-success: <green>成功地清空商品
taxaccount-set: <green>已經設定 <yellow>{0} <green>為此商店的稅金帳戶
support-disable-reason:
  hot-reload: <yellow>我們不支援重新載入指令，請你重新啟動伺服器後再試。
  outdated: <yellow>此版本的 Quickshop 已經過時，在請求技術支援前，請更新至最新版本！
  bad-hosts: |-
    <yellow>此伺服器的 HOSTS 已被修改。QuickShop 需要特定的功能去正確地連接 Mojang API，請你在請求提供技術支援前先修改 HOSTS。
    Windows: C:\\windows\\system32\\drivers\\etc\\hosts
    Linux: /etc/hosts
  privacy: <yellow>此伺服器在破解版（離線版）環境底下運行。如果你正在使用代理伺服器下的 bukkit 伺服器及在代理伺服器設定 online-mode=true，請你將代理伺服器的相關設定正確。
  modified: <yellow>無法通過檔案完整度檢查：此版本的 QuickShop 並非官方正式版本，亦已經被其他人變更過。
  consolespamfix-installed: <yellow>已安裝 ConsoleSpamFix，任何錯誤詳細訊息已被隱藏。如果要得到技術支援，請先停用此功能。
  authlib-injector-detected: <yellow>此伺服器正在運行第三方 Auth-library，例如 authlib-injector。
  unsupported-server-software: <yellow>此伺服器並非運行受支援的伺服器軟體，任何模組化的伺服器軟體，例如 MCPC、Cauldron、CatServer、Mohist、Magma、Fukkit、Cardboard 將不會獲得技術支援。
supertool-is-disabled: <red>超級工具已被停用，現在不能使用超級工具破壞任何商店。
unknown-owner: 未知
restricted-prices: '<red>{0} 是無效的價格：最高 {1}，最低 {2}。'
nearby-shop-this-way: <green>商店距離你 {0} 格遠。
owner-bypass-check: <yellow>繞過所有檢查並完成交易（因為你是商店擁有者！）
april-rick-and-roll-easter-egg: "<hover:show_text:'Click to open in browser for time limited rewards!'><click:open_url:'https://www.youtube.com/watch?v=dQw4w9WgXcQ'><green>快來點擊此處領取 QuickShop-Hikari 開發人員提供的限時獎勵吧！</green></click></hover>"
signs:
  item-right: ''
  out-of-space: 空間不足
  unlimited: 無限
  stack-selling: 出售 {0}
  stack-price: '{0} {1} 個 {2}'
  status-unavailable: <red>
  out-of-stock: 無庫存
  stack-buying: 收購 {0} 組
  freeze: 已停用交易
  price: '每個 {0}'
  buying: 收購 {0} 個
  header: '{1}{0}'
  header-available: <green>{0}
  header-unavailable: <red>{0}
  selling: 出售 {0} 個
  status-available: <green>
  item-left: ''
negative-amount: <red>我的天啊，沒想到居然有人想要交易負數的數量。
display-turn-on: <green>成功地啟用商店懸浮物顯示。
shop-staff-deleted: <green>成功地將員工 {0} 從你的商店移除。
nearby-shop-header: '<green>附近匹配商店 <aqua>{0}</aqua>：'
backup-failed: 無法備份商店資料庫，請查看控制台以取得詳細資訊。
shop-staff-cleared: <green>成功地將所有員工從你的商店移除。
price-too-high: <red>商店價格太高了！請設定在 {0} 以內。
plugin-cancelled: '<red>操作被取消。原因：{0}'
player-sold-to-your-store: <green>{0} 在你的商店賣出了 {1} 個 {2}。
shop-out-of-stock: <dark_purple>你在 {0}, {1}, {2} 出售 {3} 的商店已售罄一空！
how-many-buy: <green>在聊天室中輸入你想要 <aqua>購買 <green>的數量，你可以購買 <yellow>{0} <green>個。輸入 <aqua>{1}<green>購買全部。
language-info-panel:
  help: '協助我們：'
  code: '代碼：'
  name: '語言：'
  progress: '進度：'
  translate-on-crowdin: '[在 Crowdin 上翻譯]'
item-not-exist: <red>物品 <yellow>{0}</yellow>不存在，請檢查你的拼字。
shop-creation-failed: <red>無法建立商店，請聯絡伺服器管理員。
inventory-space-full: <red>你的物品欄只有 <green>{1}</green> 個空位，請你先清空一下物品欄後再試！
no-creative-break: <red>你不能在創造模式中破壞其他玩家的商店，請使用生存模式或使用超級工具 {0} 來破壞。
booleanformat:
  success: <green>✔
  failed: <red>✘
controlpanel:
  stack: '<green>每次交易數量：<aqua>{0} <yellow>[<bold><light_purple>變更</light_purple></bold>]'
  price-hover: <yellow>點擊變更商品價格。
  remove: <bold><red>[刪除商店]
  mode-buying-hover: <yellow>點擊商店變更為出售模式。
  empty: '<green>清空：移除所有商品 <yellow>[<bold><light_purple>好</light_purple></bold>]'
  stack-hover: <yellow>點擊設定每份的物品數量。設定為 1 以恢復為普通單物品商店。
  alwayscounting-hover: <yellow>點擊以切換是否持續計算容器物品。
  alwayscounting: '<green>持續計算數量：{0} <yellow>[<bold><light_purple>變更</light_purple></bold>]'
  setowner: '<green>擁有者：<aqua>{0} <yellow>[<bold><light_purple>變更</light_purple></bold>]'
  freeze: '<yellow>凍結模式：<aqua>{0} <yellow>[<bold><light_purple>切換</light_purple></bold>]'
  price: '<green>價格：<aqua>{0} <yellow>[<bold><light_purple>變更</light_purple></bold>]'
  currency-hover: <yellow>點擊設定或移除目前正在使用的商店貨幣
  lock: '<yellow>商店上：<aqua>{0} <yellow>[<bold><light_purple>切換</light_purple></bold>]'
  mode-selling: '<green>商店模式：<aqua>出售 <yellow>[<bold><light_purple>變更</light_purple></bold>]'
  currency: '<green>貨幣：<aqua>{0} <yellow>[<bold><light_purple>設定</light_purple></bold>]'
  setowner-hover: <yellow>點擊變更擁有者。
  mode-buying: '<green>商店模式：<aqua>收購 <yellow>[<bold><light_purple>變更</light_purple></bold>]'
  item: '<green>商店物品：{0} <yellow>[<bold><light_purple>變更</light_purple></bold>]'
  unlimited: '<green>無限：{0} <yellow>[<bold><light_purple>變更</light_purple></bold>]'
  unlimited-hover: <yellow>點擊變更商店模式為無限。
  refill-hover: <yellow>點擊重新填充商品。
  remove-hover: <yellow>點擊移除此商店。
  toggledisplay-hover: <yellow>切換商店是否顯示懸浮物
  refill: '<green>補貨：重新填充商品 <yellow>[<bold><light_purple>好</light_purple></bold>]'
  freeze-hover: <yellow>切換商店凍結狀態。
  lock-hover: <yellow>啟用 / 停用商店鎖保護。
  item-hover: <yellow>點擊變更商店物品
  infomation: '<green>商店控制面板：'
  mode-selling-hover: <yellow>點擊商店變更為收購模式。
  empty-hover: <yellow>點擊清除商店內庫存。
  toggledisplay: '<green>商店懸浮物品顯示：<aqua>{0} <yellow>[<bold><light_purple>切換</light_purple></bold>]'
  history: '<green>歷史： <yellow>[<bold><light_purple>查看</light_purple></bold>]</yellow>'
  history-hover: <yellow>點擊來去查閱商店銷售紀錄
timeunit:
  behind: 後面
  week: "{0} 星期"
  weeks: "{0} 星期"
  year: "{0} 年"
  before: 之前
  scheduled: 已排程
  years: "{0} 年"
  scheduled-in: 以排程在{0}
  second: "{0} 秒"
  std-past-format: '{5}{4}{3}{2}{1}{0}前'
  std-time-format: HH:mm:ss
  seconds: "{0} 秒"
  hour: "{0} 小時"
  scheduled-at: 排定於 {0}
  after: 經過
  day: "{0} 天"
  recent: 最近
  between: 之間
  hours: "{0} 小時"
  months: "{0} 月"
  longtimeago: 很久以前
  between-format: 介於 {0} 與 {1} 之間
  minutes: "{0} 分鐘"
  justnow: 剛才
  minute: "{0} 分鐘"
  std-format: yyyy/MM/dd HH:mm:ss
  future-plain-text: 未來
  month: "{0} 月"
  future: 在 {0}
  days: "{0} 天"
command:
  reloading: <green>已重新載入組態設定。<yellow>某些變更可能需要重新啟動才能生效。
  description:
    buy: <yellow>變更成 <light_purple>收購 <yellow>模式
    about: <yellow>顯示關於 QuickShop 的資訊
    language: <yellow>變更正在使用的語言
    purge: <yellow>在背景啟動商店清理任務
    paste: <yellow>自動將伺服器資料傳送到 Pastebin 上
    title: <green>QuickShop 說明
    remove: <yellow>移除你注視的商店
    ban: <yellow>解除封鎖商店中的玩家
    empty: <yellow>清空商店空間
    alwayscounting: <yellow>設定商店是否會持續計數容器物品（即使是無限商店）
    setowner: <yellow>變更商店的擁有者。
    reload: <yellow>重新載入 QuickShop 的 config.yml
    freeze: <yellow>停用或啟用商店交易
    price: <yellow>變更 收購/出售 的價格
    find: <yellow>搜尋附近指定物品類型的商店。
    create: <yellow>在目標儲物箱建立新商店
    lock: <yellow>切換商店上鎖狀態
    currency: <yellow>設定或移除目前商店的貨幣設定
    removeworld: <yellow>移除指定世界的商店
    info: <yellow>顯示 QuickShop 統計資料
    owner: <yellow>變更商店的擁有者。
    amount: <yellow>設定物品數量（聊天室發生問題時很實用）
    item: <yellow>變更商店物品
    debug: <yellow>開啟用開發者模式
    unlimited: <yellow>將商店設為無限庫存。
    sell: <yellow>變更成 <light_purple>出售 <yellow>模式
    fetchmessage: <yellow>顯示未讀商店日誌
    staff: <yellow>管理你的商店員工
    clean: <yellow>移除所有（已載入）沒有商品的商店
    refill: <yellow>將一定數量的商品加到商店
    help: <yellow>顯示 QuickShop 說明
    removeall: <yellow>移除指定玩家的所有商店
    unban: <yellow>解除封禁商店中的玩家
    transfer: <yellow>將某人的所有商店轉移給其他人
    transferall: <yellow>將某人的所有商店轉移給其他人
    transferownership: <yellow>將你正在查看的商店轉讓給其他人
    size: <yellow>變更每份物品的數量
    supercreate: <yellow>繞過所有保護檢查並建立商店
    taxaccount: <yellow>設定商店使用的稅金帳戶
    name: <yellow>命名商店一個專有名稱
    toggledisplay: <yellow>切換商店顯示物品狀態
    permission: <yellow>商店權限管理
    lookup: <yellow>管理物品索引表
    database: <yellow>查看和維護 QuickShop 資料庫
    benefit: <yellow>設定商店擁有者和其他玩家之間的利益分配
    tag: <yellow>新增、刪除或查詢商店標籤
    suggestprice: <yellow>基於其他商店的情況，為商店提出建議價格
    history: <yellow>查看商店的歷史交易記錄
    sign: <yellow>變更商店告示牌材質
  bulk-size-not-set: '<red>用法：/quickshop size \<amount>'
  no-type-given: '<red>用法：/quickshop find \<item>'
  feature-not-enabled: 此功能沒有在組態設定檔案中啟用。
  now-debuging: <green>成功地啟用開發者模式，正在重新載入 QuickShop……
  no-amount-given: <red>沒有提供數量。請使用：<green>/quickshoo refill \<amount>
  no-owner-given: <red>未指定商店擁有者
  disabled: '<red>此指令已被停用：<yellow>{0}'
  bulk-size-now: <green>現在開始交易 <yellow>{0} 個 {1}
  toggle-always-counting:
    counting: <green>商店現在會持續計算容器物品（即使是無限商店）
    not-counting: <green>現在商店會視情況計算容器物品（無限商店無視容器物品）
  cleaning: <green>正在刪除沒有任何物品的商店……
  now-nolonger-debuging: <green>成功地停用開發者模式，正在重新載入 QuickShop……
  toggle-unlimited:
    limited: <green>此商店現在已恢復數量限制
    unlimited: <green>此商店現在已沒有數量限制
  transfer-success-other: <green>將<yellow> {0} {1}<green>的商店轉移到<yellow> {2}
  no-trade-item: <green>請在主手持有要變更的交易商品
  wrong-args: <red>無效的參數，使用 <bold>/quickshop help </bold>查看指令列表。
  some-shops-removed: <yellow>{0} <green>的商店已移除
  new-owner: '<green>新商店擁有者：<yellow>{0}'
  format: <green>/{0} {1} <yellow>- {2}
  transfer-success: <green>將<yellow> {0} <green>商店轉移到<yellow> {1}
  now-buying: <green>商店正在 <light_purple>收購 <yellow>{0}
  now-selling: <green>商店現在 <aqua>出售 </aqua>{0}
  cleaned: <green>已移除 <yellow>{0} </yellow>個商店。
  trade-item-now: <green>現在開始交易 <yellow>{0} 個 {1}
  no-world-given: <red>請指定世界名稱
  format-disabled: <red>/{0} {1} <gray>- {2}
  invalid-bulk-amount: <red>指定的值 {0} 大於最大堆疊或小於 1
currency-not-support: <red>此經濟外掛程式不支援多重經濟（multi-economy） 功能。
trading-in-creative-mode-is-disabled: <red>你不能在創造模式中使用商店交易。
the-owner-cant-afford-to-buy-from-you: <red>此商品價格 {0} 元，但商店擁有者只剩 {1} 了。
you-cant-afford-shop-naming: <red>你沒有足夠的金錢命名商店, 命名商店需要{0}.
inventory-error: |-
  <red>無法執行 InventoryWrapper。你有沒有使用其他外掛程式覆蓋了商店的物品欄存取？
  資訊：Exception={0}、InventoryWrapper={1}、WrapperType={2}、WrapperProvider={3}、SymbolLink={4}。請聯絡伺服器管理員。
integrations-check-failed-create: <red>集成模組 {0} 拒絕了建立商店的操作。
shop-out-of-space: <dark_purple>你在 {0}, {1}, {2} 的商店已滿了。
admin-shop: 系統商店
no-anythings-in-your-hand: <red>手上沒有任何東西。
no-permission: <dark_red>你沒有使用此指令的權限。
chest-was-removed: <red>儲物箱已被移除。
you-cant-afford-to-buy: <red>商品價格是 {0} ，但是你只有 {1}。
shops-removed-in-world: <yellow>世界 <aqua>{1} <yellow>總共有 <aqua>{0} <yellow>個商店被刪除。
display-turn-off: <green>成功地關閉商店懸浮物顯示。
client-language-unsupported: <yellow>QuickShop 不支援你的用戶端語言，我們現在將它還原為 {0}。
language-version: '63'
not-managed-shop: <red>你沒有管理此商店的權限
shop-cannot-trade-when-freezing: <red>你不能與這間商店交易，因為它已被凍結。
invalid-container: <red>無效的容器，你只能在有空間的方塊內建立商店。
permission:
  header: <green>商店權限細節
  header-player: <green>{0} 的商店權限細節
  header-group: <green>{0} 商店群的權限細節
  table: <gold><bold>{0}</bold>:</gold> <gray>{1}
  item:
    purchase: <yellow>擁有此權限者可以進行購買或出售。（商店物品或商店本身）
    show-information: <yellow>擁有此權限者可以查看商店資訊。（打開商店資訊面板）
    preview-shop: <yellow>擁有此權限者可以預覽商店。（預覽物品）
    search: <yellow>擁有此權限者可以利用搜尋找到此商店。（移除此權限將會使此商店從搜尋結果中隱藏）
    delete: <yellow>擁有此權限者得以刪商店。
    receive-alert: <yellow>擁有此權限者將可以收到警報。（例如當缺貨時或物品售出）
    access-inventory: <yellow>擁有此權限者可以打開商店的庫存。
    ownership-transfer: <yellow>擁有此權限者得以將商店所有權轉移。
    management-permission: <yellow>擁有此權限者得以編輯玩家身分組與設定身分組權限。
    toggle-display: <yellow>擁有此權限者可以控制商店懸浮物品顯示與否。
    set-shoptype: <yellow>擁有此權限者得以控制商店類型。（收購或出售）
    set-price: <yellow>擁有此權限者得以設定商品價格。
    set-item: <yellow>擁有此權限者得以設定商店所擺的物品。
    set-stack-amount: <yellow>擁有此權限者得以設定商店堆疊數量。
    set-currency: <yellow>擁有此權限者得以設定商店交易貨幣。
    set-name: <yellow>擁有此權限者得以設定商店名稱。
    set-sign-type: <yellow>變更商店告示牌材質
    view-purchase-logs: <yellow>查看商店購買歷史記錄的許可權。
  group:
    everyone: <yellow>預設身分組，商店擁有者不在其中。
    staff: <yellow>商店管理員的系統身分組
    administrator: <red>管理員的系統身分組，在其中的玩家會擁有與商店擁有者幾乎相同的權限。
invalid-group: <red>組名無效
invalid-permission: <red>無效的權限
invalid-operation: <red>無效的操作，只有 {0} 是被允許的
player-no-group: <yellow>玩家 {0} 在此商店不屬於任何身分組。
player-in-group: <green>玩家 {0} 在此商店屬於 <aqua>{1}</aqua> 身分組。
permission-required: <red>你需要權限 {0} 來在此商店執行此操作。
no-permission-detailed: <red>你需要權限 <yellow>{0}</yellow> 來在此商店執行此操作。
paste-notice: "<yellow>注意：如果你正在為故障排除目的建立 Paste 報告。一定要在建立 Paste 報告之前重新實現錯誤；我們需要在緩衝區短暫保留記錄檔以解決問題。如果您建立 Paste 報告速度太慢，或沒有重新實現錯誤或重新啟動伺服器，則 Paste 報告將不記錄任何內容且變得無用。"
paste-uploading: <aqua>請稍候，正在將複製內容上傳到 pastebin……
paste-created: '<green>已建立 Paste，點擊以在瀏覽器中打開：<yellow>{0}</yellow><br><red>警告：<gray>永遠別將 Paste 傳送給你不信任的人。'
paste-created-local: |-
  <green>已建立 Paste 並且已儲存在你的硬碟路徑 {0}
  <red>警告：<gray>永遠別將 Paste 傳送給你不信任的人。
paste-created-local-failed: <red>無法儲存 Paste 到硬碟，請檢查你的硬碟。
paste-451: |-
  <gray><bold>小提示：</bold>你目前所在的地區好像不讓你連結到 CloudFlare Workers 服務，因此 Paste 功能可能無法正常載入。
  如果接下來的無法操作，你可以試試加上 --file 變數來將 Paste 儲存在硬碟上：<dark_gray>/quickshop paste --file
paste-upload-failed: <red>無法上傳 Paste 到 {0}，正在嘗試其他的提供服務……
paste-upload-failed-local: <red>無法上傳 Paste，正在嘗試在硬碟上產生 Paste……
command-incorrect: '<red>指令用法錯誤，輸入/quickshop help 來尋求幫助。用法：{0}。'
successfully-set-player-group: <green>成功地將玩家 {0} 的身分組設定為 <aqua>{1}</aqua>。
successfully-unset-player-group: <green>成功地移除此商店中的玩家身分組。
successfully-set-player-permission: <green>成功地將玩家 {0} 在 <aqua>{2}</aqua> 商店的身分組設定為 <aqua>{1}</aqua>。
lookup-item-created: <green>物品 <aqua>{0}</aqua> 已經在索引表上被建立，你可以在組態設定中引用此物品了。
lookup-item-exists: <red>物品 <yellow>{0}</yellow> 已經存在索引表中，刪除它或取另外一個名字
lookup-item-not-found: <red>物品名稱：<yellow>{0}</yellow> 並不存在。
lookup-item-name-illegal: <red>無效的物品名稱，只能用數字、英文字母和底線
lookup-item-name-regex: '<red>物品名稱必須符合這個正規表示式：<aqua>{0}</aqua>。'
lookup-item-test-not-found: '<gold>測試：<yellow>在你手中的物品並未上傳到物品索引表。'
lookup-item-test-found: '<gold>測試：<green>你手中的物品在索引表中被命名為 <aqua>{0}</aqua>。'
lookup-item-removed: <green>物品 <aqua>{0}</aqua>已成功地自物品索引表中移除。
internal-error: <red>發生內部錯誤，請聯絡伺服器管理員。
argument-cannot-be: <red>參數 <aqua>{0}</aqua> 不能為 <yellow>{1}</yellow>。
argument-must-between: <red>參數 <aqua>{0}</aqua> 值必須在 <yellow>{1}</yellow> 和 <yellow>{2}</yellow> 之間
invalid-percentage: <red>百分比無效，你必須在百分比的數字後加上「%」。
display-fallback: |-
  <red>由於內部錯誤，正在顯示回饋訊息。
  此物品的值可能無法在地化或正確地處理。
  請聯絡伺服器管理員。
not-a-valid-time: |-
  <red>字串 <yellow>{0}</yellow> 不是一個有效的時間戳記，請輸入 <aqua>Zulu 時間（ISO 8601）</aqua> 或 <aqua>Unix Epoch 時間</aqua>。
  <gold>有效時間戳記的範例（適用於 GMT 2022 年 12 月 17 日 星期六 10:31:37 ）</gold><br><aqua>-<yellow>2022-12-17T10:31:37Z</yellow> <gray>（Zulu Time）</gray>
  - <yellow>1671273097</yellow> <gray>（Unix Epoch 時間以秒計）</gray><br>
invalid-past-time: <red>你無法指定過去的時間。
debug:
  arguments-invalid: <red>提供引數 <yellow>{0}</yellow> 無效。
  sign-located: '<green>告示牌有效：<yellow>{0}</yellow>。'
  operation-missing: <red>你必須指定一個操作。
  operation-invalid: <red>你必須指定一個有效的操作。
  invalid-base64-encoded-sql: <red>所提供的 SQL 必須為 base64 編碼。
  warning-sql: |-
    <bold><red>警告：</red></bold><yellow>你正在執行一個 SQL 敘述句，即使它屬於其他外掛程式，這仍可能會損壞你的資料庫或破壞資料庫中的任何資料。
    <red>如果你不信任向你傳送此指令的人，請不要確認。
  warning-sql-confirm: <yellow>若要確認此危險的操作，請在 60 秒內輸入 <aqua>/quickshop debug database sql confirm {0}</aqua>。
  warning-sql-confirm-hover: <yellow>點擊以確認此危險操作。
  sql-confirm-not-found: <yellow>沒有找到你給予的操作，此操作可能無效或過期。
  sql-executing: '<yellow>執行 SQL 敘述句：<aqua>{0}'
  sql-completed: <green>操作已完成！已影響 {0} 行。
  sql-exception: <red>執行 SQL 查詢時發生錯誤，請查看控制台了解詳情！
  sql-disabled: '<red>出於安全原因，此伺服器中停用了 SQL 查詢，如果你確實需要此功能，可以在啟動引數中新增以下標記：<aqua>{0}</aqua> 並使用「true」值來啟用它。'
  force-shop-reload: <yellow>正在強制重新載入以載入的商店……
  force-shop-reload-complete: <green>已強制重新載入 <aqua>{0}</aqua> 個商店。
  force-shop-loader-reload: <yellow>正在強制商店載入器重新載入……
  force-shop-loader-reload-unloading-shops: <yellow>正在取消載入 <aqua>{0}</aqua> 個已載入的商店……
  force-shop-loader-reload-unloading-shops-from-memory: <yellow>正在從記憶體中移除 <aqua>{0}</aqua> 個商店……
  force-shop-loader-reload-reloading-shop-loader: <yellow>正在重新調用商店載入器以從資料庫重新載入所有商店……
  force-shop-loader-reload-complete: <green>商店載入器已重新載入所有的商店！
  toggle-shop-loaded-status: <aqua>切換此商店載入狀態為 <gold>{0}
  shop-internal-data: '<yellow>此商店內部資料：</yellow>{0}'
  handler-list-not-valid-bukkit-event-class: <red>所提供的 class <yellow>{0}</yellow> 不是有效的 Bukkit event class。
  update-player-shops-signs-no-username-given: <red>你必須提供有效的玩家名稱。
  update-player-shops-signs-create-async-task: <yellow>正在為強制更新標誌建立異步任務……
  update-player-shops-player-selected: '<yellow>已選擇玩家：<gold>{0}'
  update-player-shops-player-shops: <yellow>共有 <gold>{0}</gold> 家商店等待更新。
  update-player-shops-per-tick-threshold: '<yellow>最大商店可以每（tick）更新：<gold>{0}'
  update-player-shops-complete: <green>已完成任務，花費 <yellow>{0} 毫秒</yellow>更新。
  update-player-shops-task-started: <gold>任務已開始，請等候它完成。
  item-info-store-as-string: "<green>你正在注視的商店：<gold>{0}</gold> Hash：<white>{1}</white>"
  item-info-hand-as-string: "<green>你手中的物品：<gold>{0}</gold> Hash：<white>{1}</white>"
  item-matching-result: "<green>Hand2Store：<aqua>{0}</aqua>、Store2Hand：<aqua>{1}</aqua>"
  hikari-cp-size-tweak: "<green>已設定為 HikariCP MaximumPoolSize 和 MinimumIdle <white>{0}</white>"
  hikari-cp-testing: "<green>請稍候，正在測試 HikariCP 連線……"
  hikari-cp-working: "<green>通過！HikariCP 運作正常！"
  hikari-cp-not-working: "<red>失敗！HikariCP 返回的連接無法運作！（未在 1 秒內通過測試）"
  hikari-cp-timeout: "<red>HikariCP 在取得有效連線時逾時，請清理所有活動查詢以釋放連線資源。"
  queries-stopped: "<green>已停止 <white>{0}</white> 活動查詢。"
  queries-dumping: "<yellow>正在轉除活動查詢……"
  restart-database-manager: "<yellow>正在重新啟動 SQLManager"
  restart-database-manager-clear-executors: "<yellow>正在清除執行器……"
  restart-database-manager-unfinished-task: "<yellow>未完成的任務：<white>{0}</white>"
  restart-database-manager-unfinished-task-history-query: "<yellow>未完成的任務（歷史查詢）：<white>{0}</white>"
  restart-database-manager-reconnect: "<yellow>透過初始化序列重新啟動 SQLManager（透過 async 執行器）"
  restart-database-manager-done: "<green>完成！"
  property-incorrect: "<yellow>您必須輸入屬性值（且只輸入一個）。例如 aaa=bbb"
  property-security-block: "<red>基於安全原因，請求遭到拒絕，你只能變更啟動指令碼 <aqua>com.ghostchu.quickshop</aqua> 或 <aqua>quickshop</aqua> 的屬性。"
  property-removed: "<green>已移除屬性鍵 <white>{0}</white>"
  property-changed: "<green>屬性鍵 <white>{0}</white> 已從 <white>{1}</white> 更改為 <white>{2}</white>"
  marked-as-dirty: "<green>將所有商店標記為 dirty 狀態，它們將在下一個自動儲存任務中強制儲存。（重新啟動伺服器以強制執行儲存工作）"
  display-removed: "<green>已從世界成功地移除 <yellow>{0}</yellow> QuickShop 顯示物品 / 實體。"
database:
  scanning: <green>正在掃描 QuickShop 資料庫中的孤立資料，在掃描過程中資料庫負載可能會增加，且這可能需要等候一段時間……
  scanning-async: <yellow>正在掃描非同步任務執行緒上 QuickShop 資料庫中的孤立資料，掃描過程中資料庫負載可能會增加，這可能需要等候一段時間，請稍後再試。
  already-scanning: <red>已啟動掃描任務，請等待完成。
  trim-warning: <red><bold>警告：</bold><yellow>在繼續資料庫修整之前，請先備份你的資料庫以避免資料遺失。準備好後執行 <aqua>/quickshop database trim confirm</aqua> 以繼續。
  status: '<yellow>狀態：{0}'
  status-good: <green>良好
  status-bad: <yellow>需要維護
  isolated: '<yellow>孤立資料：'
  isolated-data-ids: '<aqua>└<yellow> 資料記錄：<gold>{0}'
  isolated-shop-ids: '<aqua>└<yellow> 商店索引：<gold>{0}'
  isolated-logs: '<aqua>└<yellow> 記錄檔：<gold>{0}'
  isolated-external-caches: '<aqua>└<yellow>外部快取：<gold>{0}'
  last-purge-time: <yellow>上次修整時間為 {0}
  report-time: <yellow>上次掃描時間為 {0}
  auto-scan-alert: <yellow>此伺服器上的 QuickShop 資料庫需要維護。已發現 <gold>{0}</gold> 孤立資料等待修整。
  auto-trim: <green>此伺服器已啟用自動修整，所以無需手動修整。
  trim-complete: <green>資料庫修整已完成，<yellow>{0}</yellow> 已修整孤立資料。
  auto-trim-started: <green>已開始自動修整，請稍候……
  trim-start: <green>已開始修整資料庫，請稍候……
  trim-exception: <red>無法修整資料庫，在資料庫修整期間發生異常，請檢查伺服器控制台。
  generated-at: '<yellow>生成於：<gold>{0}'
  purge-date: <red>你必須給出一個日期。
  purge-warning: <yellow>此操作將清除儲存在資料庫中的歷史記錄，包括商店建立 / 修改 / 刪除、購買、交易和系統日誌。刪除這些資料可以釋出硬碟空間，但所有歷史資料都會遺失，依賴計量的其他外掛程式將停止運行。若要繼續執行此操作，請執行指令 `/quickshop database purgelogs \<before-days> 以確認`
  purge-task-created: <green>已建立任務！資料庫將在背景清除歷史記錄。
  purge-done-with-line: <green>已完成清除任務，總共 <gold>{0}</gold> 行已從資料庫中清除。
  purge-done-with-error: <red>無法清除任務，請檢查伺服器控制台取得詳細資訊。
  purge-players-cache: <yellow>請稍候，正在清理玩家快取
  purge-players-completed: |-
    <green>成功地從記憶體和資料庫清除了 {0} 個玩家的快取。
    <aqua>注意：此操作可能會影響伺服器效能。
  purge-players-error: <red>無法清除玩家快取，請檢查伺服器控制台。
  suggestion:
    trim: <yellow>此資料庫需要修整資料庫內孤立的資料，執行指令<aqua>/quickshop database trim</aqua> 以修整資料庫。
always-counting-removal-early-warning: <red>已計劃刪除始終計數功能，你不應再使用它，並且它將來會停止運作。
exporting-database: <green>正在匯出資料庫，請稍候……
exporting-failed: <red>無法匯出資料庫，請檢查伺服器控制台。
exported-database: <green>匯出資料庫至 <yellow>{0}</yellow>。
importing-not-found: <red>找不到檔案 <yellow>{0}</yellow>，請檢查檔案路徑是否正確。
importing-early-warning: |-
  <red><bold>警告：</bold><yellow>備份將被匯入目前的資料庫，如果你沒有備份，任何現有的資料都將被清除並永久遺失。
  <red>你確定要繼續進行匯入作業？</red>輸入 <aqua>/quickshop recovery confirm</aqua> 以繼續。
importing-database: <green>正在從備份中匯入資料庫，請稍候……
importing-failed: <red>無法匯入資料庫，請檢查伺服器控制台。
imported-database: <green>正在從 <yellow>{0}</yellow> 匯入資料庫。
transfer-sent: <green>商店轉移請求已傳送至 <yellow>{0}</yellow>。
transfer-request: <yellow>玩家 <aqua>{0}</aqua> 請求將他們的商店轉移給你。你想接受轉移嗎？
transfer-single-request: <yellow>玩家 <aqua>{0}</aqua> 請求將他們的商店轉移給你。你想接受轉移嗎？
transfer-ask: |-
  <gold>輸入 <red>/quickshop transfer accept</red> 以接受，或輸入 <red>/quickshop transfer deny</red> 以拒絕。
  請求將在 <red>{0}</red> 秒後過期。
transferall-ask: |-
  <gold>輸入 <red>/quickshop transferall accept</red> 以接受，或輸入 <red>/quickshop transferall deny</red> 以拒絕。
  請求將在 <red>{0}</red> 秒後過期。
transfer-single-ask: |-
  <gold>輸入 <red>/quickshop transferall accept</red> 以接受，或輸入 <red>/qs transferall deny</red> 以拒絕。
  請求將在 <red>{0}</red> 秒後過期。
transfer-accepted-fromside: <green>玩家 <aqua>{0}</aqua> 已接受你的商店轉移請求。
transfer-accepted-toside: <green>你已接受 <aqua>{0}</aqua> 的轉移請求。
transfer-rejected-fromside: <red>玩家 <aqua>{0}</aqua> 已拒絕你的商店轉移請求。
transfer-rejected-toside: <red>你已拒絕 <aqua>{0}</aqua> 的轉移請求。
transfer-no-pending-operation: <red>你還沒有待處理的轉移請求。
transfer-no-self: <red>你無法將你的商店轉移給自己。
benefit-overflow: <red>所有利益的總和不能大於或等於100%。
benefit-exists: <red>目標玩家已經在此商店的利益分配列表中。
benefit-removed: <red>目標玩家已從商店的利益分配中移除。
benefit-added: <green>玩家 <aqua>{0}</aqua> 已被新增到商店的利益分配中！
benefit-updated: <green>玩家 <aqua>{0}</aqua> 的利益分配已更新！
benefit-query: <green>此商店在利益分配列表中有 <yellow>{0}</yellow> 名玩家！
benefit-query-list: <yellow> - </yellow><white>玩家 <gold>{0}</gold>效益 <gold>{1}%
tag-added: '<green>成功地將 <aqua>#{0}</aqua> 新增到此商店！'
tag-add-duplicate: '<red>此商店已存在標籤 <aqua>#{0}</aqua>！'
tag-removed: '<green>成功地從此商店刪除 <aqua>#{0}</aqua>！'
tag-remove-not-exists: '此商店中不存在標籤 <aqua>#{0}</aqua>！'
tag-cleared: <green>成功地清除了此商店的所有標籤！
tag-shops-cleared: '<green>成功地從你所有標記的商店中清除 <aqua>#{0}</aqua>！'
tag-query: '<green>此商店有 <yellow>{0}</yellow> 個標籤：'
tag-query-listing: '<yellow> - <aqua>#{0}'
tag-query-no-tag: <red>此商店沒有標籤。
tag-query-shops: '<green>此標籤包含 <yellow>{0}</yellow> 個商店：'
tag-query-shops-listing: <yellow> - <aqua>{0}
batch-operations-based-on-tags-no-failure: <green>成功批次處理 <yellow>{0}</yellow> 個商店
batch-operations-based-on-tags-have-failure: <yellow>總共 {0} 個商店在批處理中成功完成，但是有 <red>{1}</red> 個請求未完成。
batch-operations-based-on-tags-have-failure-with-reason: '<yellow>總共 {0} 商店在批處理中成功完成，但是有 <red>{1}</red> 個請求未完成。原因：<gold>{2}'
batch-operations-based-on-tags-have-failure-error-messages-may-appears-above: <gray>錯誤訊息將傳送到聊天室的此訊息上方給你。
addon:
  towny:
    commands:
      town: <yellow>將商店設定或取消設置為城鎮（town）商店
      nation: <yellow>將商店設定或取消設置為國家（Nation）商店
    make-shop-owned-by-town: <green>你已將商店設為 <yellow>{0}</yellow> 鎮（town）擁有。
    make-shop-no-longer-owned-by-town: <green>你已重設商店所有權，它現在轉移回原來的商店擁有者。
    make-shop-owned-by-nation: <green>你已將商店設為 <yellow>{0}</yellow> 國（Nation）擁有。
    make-shop-no-longer-owned-by-nation: <green>你已重設商店所有權，它現在轉移回原來的擁有者。
    shop-owning-changing-notice: <gray>此商店現在由城鎮（town） / 國家（Nation）擁有，原始商店擁有者已自動新增到管理員列表中，使用指令 /quickshop permission 修改或新增任何新的擁有者 / 員工
    target-shop-already-is-town-shop: <red>目標商店已歸城鎮（town）所有。
    target-shop-already-is-nation-shop: <red>目標商店已歸國家（Nation）所有。
    target-shop-not-in-town-region: <red>目標商店不在城鎮（town）內。
    target-shop-not-in-nation-region: <red>目標商店不在國內（Nation）。
    item-not-allowed: <red>此商店的商品不允許城鎮（town） / 國家（Nation）商店使用，請再選一個！
    operation-disabled-due-shop-status: <red>此商店操作已被停用，因為這已經是一個城鎮（town） / （Nation）國家商店。
    plot-type-disallowed: <red>你不能在這種類型的畫布上建立城鎮（town） / 國家（Nation）商店。
    flags:
      own: <red>你只能在擁有的商店類型城鎮（town）土地上建立商店。
      modify: <red>你沒有在此城鎮（town）土地上建造的權限。
      shop-type: <red>你必須在城鎮（town）土地上建立商店。
  residence:
    creation-flag-denied: <red>你沒有在此領地建立商店的權限。
    trade-flag-denied: <red>你沒有在此領地購買的權限。
    you-cannot-create-shop-in-wildness: <red>你不能在野外建立商店。
  griefprevention:
    creation-denied: <red>你沒有在此聲明中建立商店的權限。
    trade-denied: <red>你沒有在此聲明中購買商店的權限。
  lands:
    world-not-enabled: <red>你不能在此世界中建立或購買商店。
    creation-denied: <red>你沒有在此領土建立商店的權限。
  plotsquared:
    no-plot-whitelist-creation: <red>你無法在畫布外建立商店。
    no-plot-whitelist-trade: <red>你無法在畫布外購買商店。
    creation-denied: <red>你沒有在此畫布中建立商店的權限。
    trade-denied: <red>你沒有在此畫布中購買商店的權限。
    flag:
      create: 建立 QuickShop-Hikari 商店
      trade: 購買 QuickShop-Hikari 商店
  superiorskyblock:
    owner-create-only: <red>只有島嶼擁有者能夠在那裡建立商店。
    owner-member-create-only: <red>只有島嶼擁有者或成員能夠在那裡建立商店。
  worldguard:
    creation-flag-test-failed: <red>你沒有在此 WorldGuard 保護區建立商店的權限。
    trade-flag-test-failed: <red>你沒有在此 WorldGuard 保護區與商店交易的權限。
    reached-per-region-amount-limit: "<red>您已達到該地區商店的最大數量。"
  discord:
    __to_translators: 'To translators: `addon.discord.discord-messages` use a special variable in strings, %%LANG_KEY%% instead of QuickShop''s style {LANG_INDEX}, do not translate anything inside double ''%'' section. BUT for any other section texts, it respect the normal QuickShop Text System syntax ({0},{1} etc.) This may be difficult to understand because two different variable forms are used in the same program at the same time, but trust me, it is reasonable. (you don''t need translate this string, it''s a comment)'
    __to_users: 和 QuickShop 的文字系統一樣，Discord 外掛程式也會自動偵測使用者語言並使用此語言傳送Discord 訊息， 它遵守 QuickShop-Hikar i的語言系統設定。
    __to_message_designer: '使用 GUI 設計你的 Discord 訊息：https://glitchiii.github.io/embedbuilder/，然後複製 JSON 代碼並貼到語言檔案中，然後在這裡開始！'
    discord-enabled: <aqua>已成功地 <green>開啟</green> 你的 QuickShop Discord 通知訊息，現在起你可以從 Discord 接收商店訊息。
    discord-disabled: <aqua>已成功 <red>停用</red> 你的 QuickShop Discord 通知訊息，現在你將不再收到來自 Discord 的商店訊息。
    discord-not-integrated: <red>你尚未連結你的 Discord 帳號！請先連結你的 Discord 帳號！
    feature-enabled-for-user: <aqua>你 <green>已啟用 </green> <gold>{0}</gold> 通知。
    feature-disabled-for-user: <aqua>你已 <red>停用</red> <gold>{0}</gold> 通知。
    link-help: <yellow>此伺服器使用 <gold>{0}</gold> 處理 Discord 驅動程式，請使用 <green>{0}</green> 連結你的 Discord 帳號。
    save-notifaction-exception: <red>儲存你的 Discord 通知設定時發生錯誤，請聯絡伺服器管理員。
    feature-status-changed: <green>成功地設定通知 <aqua>{0}</aqua> 狀態為 <gold>{1}
    commands:
      discord:
        description: <yellow>管理你的 QuickShop Discord 設定
    discord-messages:
      sold-to-your-shop: |
        {
         "embed":
           {
             "title": ":inbox_tray: 有人向你的商店出售物品",
             "description": "此玩家 %%purchase.name%% 出售 %%purchase.amount%% 個 %%shop.item.name%% 到你的商店。",
             "color": 52084,
             "author": {
               "name": "QuickShop-Hikari",
               "icon_url": ""
             },
             "thumbnail": {
               "url": ""
             },
             "image": {
               "url": ""
             },
             "footer": {
               "text": "QuickShop-Hikari Discord 訊息通知",
               "icon_url": ""
             },
             "fields": [
               {
                 "name": "商店",
                 "value": "%%shop.display-name%%",
                 "inline": false
               },
               {
                 "name": "玩家",
                 "value": "%%purchase.name%%",
                 "inline": true
               },
               {
                 "name": "物品",
                 "value": "%%shop.item.name%%",
                 "inline": true
               },
               {
                 "name": "數量",
                 "value": "%%purchase.amount%% (x%%shop.stacking-amount%%)",
                 "inline": true
               },
               {
                 "name": "你已支付",
                 "value": "%%purchase.balance-formatted%%",
                 "inline": true
               },
               {
                 "name": "稅",
                 "value": "%%purchase.taxes-formatted%%",
                 "inline": true
               }
             ]
           }
        }
      bought-from-your-shop: |
        {
        "embed":
          {
              "title": ":outbox_tray: 有人在商店購買了物品",
              "description": "此玩家 %%purchase.name%% 在你的商店購買了 %%purchase.amount%% 個 %%shop.item.name%%。",
              "color": 52084,
              "author": {
                  "name": "QuickShop-Hikari",
                  "icon_url": ""
              },
              "thumbnail": {
                  "url": ""
              },
              "image": {
                  "url": ""
              },
              "footer": {
                  "text": "QuickShop-Hikari Discord 訊息通知",
                  "icon_url": ""
              },
              "fields": [
                  {
                      "name": "商店",
                      "value": "%%shop.display-name%%",
                      "inline": false
                  },
                  {
                      "name": "玩家",
                      "value": "%%purchase.name%%",
                      "inline": true
                  },
                  {
                      "name": "物品",
                      "value": "%%shop.item.name%%",
                      "inline": true
                  },
                  {
                      "name": "數量",
                      "value": "%%purchase.amount%%(x%%shop.stacking-amount%%)",
                      "inline": true
                  },
                  {
                      "name": "收入",
                      "value": "%%purchase.balance-formatted%%",
                      "inline": true
                  },
                  {
                      "name": "稅",
                      "value": "%%purchase.taxes-formatted%%",
                      "inline": true
                  }
              ]
          }
        }
      mod-shop-purchase: |
        {
        "embed":
          {
              "title": ":dollar: 有人在商店裡購買了",
              "description": "此玩家 %%purchase.name%% 在商店裡購買了 %%purchase.amount%% 個 %%shop.item.name%%物品。",
              "color": 6061450,
              "author": {
                  "name": "QuickShop-Hikari",
                  "icon_url": ""
              },
              "thumbnail": {
                  "url": ""
              },
              "image": {
                  "url": ""
              },
              "footer": {
                  "text": "QuickShop-Hikari Discord 訊息通知",
                  "icon_url": ""
              },
              "fields": [
                  {
                      "name": "商店",
                      "value": "%%shop.display-name%%",
                      "inline": false
                  },
                  {
                      "name": "玩家",
                      "value": "%%purchase.name%%",
                      "inline": true
                  },
                  {
                      "name": "物品",
                      "value": "%%shop.item.name%%",
                      "inline": true
                  },
                  {
                      "name": "數量",
                      "value": "%%purchase.amount%% (x%%shop.stacking-amount%%)",
                      "inline": true
                  },
                  {
                      "name": "餘額",
                      "value": "%%purchase.balance-formatted%%",
                      "inline": true
                  },
                  {
                      "name": "稅",
                      "value": "%%purchase.taxes-formatted%%",
                      "inline": true
                  }
              ]
          }
        }
      out-of-space: |
        {
        "embed":
          {
              "title": ":mailbox_with_mail: 你的商店空間已不足",
              "description": "你的商店庫存已滿！\\n你需要清理你的商店以釋放空間。",
              "color": 16065893,
              "author": {
                  "name": "QuickShop-Hikari",
                  "icon_url": ""
              },
              "thumbnail": {
                  "url": ""
              },
              "image": {
                  "url": ""
              },
              "footer": {
                  "text": "QuickShop-Hikari Discord 訊息通知",
                  "icon_url": ""
              },
              "fields": [
                  {
                      "name": "商店",
                      "value": "%%shop.display-name%%",
                      "inline": false
                  }
              ]
          }
        }
      out-of-stock: |
        {
            "embed":
                {
                    "title": ":mailbox_with_no_mail: 你的商店已缺貨",
                    "description": "你的商店庫存已空！\\n你需要在商店中重新補貨以繼續出售物品！",
                    "color": 16065893,
                    "author": {
                        "name": "QuickShop-Hikari",
                        "icon_url": ""
                    },
                    "thumbnail": {
                        "url": ""
                    },
                    "image": {
                        "url": ""
                    },
                    "footer": {
                        "text": "QuickShop-Hikari Discord 訊息通知",
                        "icon_url": ""
                    },
                    "fields": [
                        {
                            "name": "商店",
                            "value": "%%shop.display-name%%",
                            "inline": false
                        }
                    ]
                }
        }
      mod-shop-created: |
        {
          "embed": {
            "title": ":classical_building: 新商店已被建立",
            "description": "玩家在你的伺服器中建立了一個商店！",
            "color": 6061450,
            "author": {
              "name": "QuickShop-Hikari",
              "icon_url": ""
            },
            "thumbnail": {
              "url": ""
            },
            "image": {
              "url": ""
            },
            "footer": {
              "text": "QuickShop-Hikari Discord 訊息通知",
              "icon_url": ""
            },
            "fields": [
              {
                "name": "商店",
                "value": "%%shop.display-name%%",
                "inline": false
              },
              {
                "name": "商店擁有者",
                "value": "%%shop.owner.name%%",
                "inline": true
              },
              {
                "name": "物品",
                "value": "%%shop.item.name%%",
                "inline": true
              },
              {
                "name": "數量",
                "value": "%%shop.stacking-amount%%",
                "inline": true
              },
              {
                "name": "商店類型",
                "value": "%%shop.type%%",
                "inline": true
              }
            ]
          }
        }
      mod-remove-shop: |
        {
          "embed": {
            "title": ":recycle: 商店在伺服器中已被刪除",
            "description": "玩家在伺服器中刪除了一個商店。",
            "color": 6061450,
            "author": {
              "name": "QuickShop-Hikari",
              "icon_url": ""
            },
            "thumbnail": {
              "url": ""
            },
            "image": {
              "url": ""
            },
            "footer": {
              "text": "QuickShop-Hikari Discord 訊息通知",
              "icon_url": ""
            },
            "fields": [
              {
                "name": "商店",
                "value": "%%shop.display-name%%",
                "inline": false
             },
                    {
                        "name": "理由",
                        "value": "%%delete.reason%%",
                        "inline": false
                    }
                ]
            }
        }
      shop-transfer-to-you: |
        {
            "embed": {
                "title": ":placard: 一個商店被轉讓給你",
                "description": "一個商店從其他玩家那裡轉讓給你。",
                "color": 16172079,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord 訊息通知",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "商店",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "轉讓來自",
                        "value": "%%transfer.from%%",
                        "inline": false
                    }
                ]
            }
        }
      mod-shop-transfer: |
        {
            "embed": {
                "title": ":placard: 商店已被轉移",
                "description": "商店從一個玩家轉移到了其他玩家。",
                "color": 6061450,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord 訊息通知",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "商店",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "轉移來自",
                        "value": "%%transfer.from%%",
                        "inline": true
                    },
                    {
                        "name": "轉移給",
                        "value": "%%transfer.to%%",
                        "inline": true
                    }
                ]
            }
        }
      shop-price-changed: |
        {
            "embed": {
                "title": ":money_with_wings: 你的商店價格已被變更",
                "description": "你或者是你的商店店員變更了商店價格。",
                "color": 16172079,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord 訊息通知",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "商店",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "原價格",
                        "value": "%%change-price.from%%",
                        "inline": true
                    },
                    {
                        "name": "現價格",
                        "value": "%%change-price.to%%",
                        "inline": true
                    }
                ]
            }
        }
      mod-shop-price-changed: |
        {
            "embed": {
                "title": ":money_with_wings:商店價格已被變更",
                "description": "商店變更了價格設定。",
                "color": 6061450,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord 訊息通知",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "商店",
                        "value": "%%shop.display-name%%",
                        "inline": true
                    },
                    {
                        "name": "商店擁有者",
                        "value": "%%shop.owner.name%%",
                        "inline": true
                    },
                    {
                        "name": "",
                        "value": "",
                        "inline": true
                    },
                    {
                        "name": "原價格",
                        "value": "%%change-price.from%%",
                        "inline": true
                    },
                    {
                        "name": "現價格",
                        "value": "%%change-price.to%%",
                        "inline": true
                    }
                ]
            }
        }
      shop-permission-changed: |
        {
            "embed": {
                "title": ":closed_lock_with_key: 商店權限設定已被更改",
                "description": "你的商店的權限設定已被更改。",
                "color": 15879747,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord 訊息通知",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "商店",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "玩家",
                        "value": "%%change-permission.player%%",
                        "inline": true
                    },
                    {
                        "name": "分配給組",
                        "value": "%%change-permission.to-group%%",
                        "inline": true
                    },
                    {
                        "name": "",
                        "value": "",
                        "inline": true
                    },
                    {
                        "name": "授予權限（繼承於組）",
                        "value": "```\\n%%change-permission.perms-list%%\\n```",
                        "inline": false
                    }
                ]
            }
        }
    field:
      player: 玩家
      item: 物品
      amount: 數量
      balance: 花費
      balance-after-tax: 餘額（稅後）
      account: 你的帳戶餘額
      taxes: 稅金
      cost: 花費
  discount:
    commands:
      discount:
        description: <yellow>套用優惠代碼或管理你的優惠代碼。
    tab-complete:
      discount:
        general:
          code: \<code>
        create:
          rate: |
            指令提示：
            參數：\<rate>
            描述：你將會得到的實際百分比或金錢
            輸入 `30%` = 價格 * 0.3
            輸入 `50` = 價格 - 50
          max-usage: |
            指令提示：
            參數： [max-usage]
            描述：可以對無限代碼使用
            `-1` 為無限
          threshold: |
            指令提示：
            參數： [threshold]
            說明：代碼可以套用的最低價格
            “-1”表示無限
          expired: |
            指令提示
            參數： [expired]
            描述：代碼過期時間。
            `-1`無限。
            接受 Zulu 時間和 UNIX 時間標記（以秒為單位）。
            Zulu 範例：2022-12-17T10:31:37Z
            UNIX 範例：1671273097
    discount-code-already-exists: <red>抱歉，你的優惠代碼名稱已被使用。
    invalid-discount-code-regex: '<red>優惠代碼必須匹配正則表達式：<yellow>{0}'
    invalid-discount-code: <red>此優惠代碼無效。
    discount-code-added: <green>你的優惠代碼 <yellow>{0}</yellow> 已新增到商店 <aqua>{1}
    discount-code-removed: <green>你的優惠代碼 <yellow>{0}</yellow> 已從商店 <aqua>{1} 移除
    invalid-code-type: <red>此優惠代碼類型 <yellow>{0}</yellow> 無效。
    invalid-usage-restriction: <red>使用限制 <yellow>{0}</yellow> 無效。
    invalid-threshold-restriction: <red>門檻限制 <yellow>{0}</yellow> 無效。
    invalid-effect-scope: <red>範圍 <yellow>{0}</yellow> 無效。
    invalid-expire-time: <red>你無法指定過去的時間。
    invalid-discount-rate: <red>折扣率 <yellow>{0}</yellow> 無效，它可以是固定數字或百分比。
    discount-code-expired: <red>金嗨（閩南語）！你的優惠代碼 <yellow>{0}</yellow> 已過期！
    discount-code-installed: <green>你已安裝優惠代碼 <gold>{0}</gold>，優惠代碼將自動套用於本次會話期間所有可用的交易。如果要將優惠代碼解除安裝，請執行 <aqua>/quickshop discount uninstall {0}</aqua>。
    discount-code-uninstalled: <green>你已將你的優惠代碼解除安裝。
    discount-code-query-nothing: <red>你還沒安裝優惠代碼！
    discount-code-query: '<green>你正在使用優惠代碼：<yellow>{0}</yellow>。'
    discount-code-applicable: '<#bcef26>你的優惠代碼 <bold><yellow>{0}</yellow></bold> 在這間商店中<bold>可套用</bold>！'
    discount-code-applicable-with-threshold: '<#bcef26>你的優惠代碼 <bold><yellow>{0}</yellow></bold> 在這間商店<bold>可套用</bold>，但僅在單筆交易中你的購買成本超過 <yellow>{1}</yellow>。'
    discount-code-not-applicable: <red>你的優惠代碼 <bold><yellow>{0}</yellow></bold> 在這間商店中<bold>無法套用</bold>！
    discount-code-reach-the-limit: <red>你已達到優惠代碼的使用限制 <bold><yellow>{0}</yellow></bold>，所以無法套用折扣。
    discount-code-no-permission: <red>你沒有在此商店使用任何優惠代碼的權限！
    discount-code-has-been-expired: <red>你的優惠代碼已過期！
    discount-code-config-shop-added: <green>成功地將此商店新增到你的優惠代碼允許列表。
    discount-code-config-shop-add-failure: <red>此商店已存在於你的優惠代碼允許列表。
    discount-code-config-shop-removed: <green>成功地將此商店從你的優惠代碼允許列表中移除。
    discount-code-config-shop-remove-failure: <red>此商店不存在於你的優惠代碼允許列表。
    discount-code-config-expire: <green>成功地變更了優惠代碼過期時間。
    discount-code-config-applied: <green>成功地設定優惠代碼！
    discount-code-created-successfully: |
      <green>已成功地建立你的優惠代碼 <yellow>{0}</yellow>！
      <gold>範圍：<aqua>{1}</aqua></gold>.
      <gold>與他人分享：<#bcef26>{2}</#bcef26></gold>
      <yellow>只適用於<gray>特定商店</gray> 範圍：將商店新增到你的優惠代碼允許列表，請注視著商店並執行指令 <aqua>{3}</aqua>。
      你可以隨時使用 <aqua>/quickshop discount config {0}</aqua> 編輯你的優惠代碼。
    discount-code-under-threshold: <red>由於總價值低於優惠代碼門檻，所以優惠未適用於你的交易 <yellow>{0}</yellow>。
    percentage-off: '<bold><#bcef26>-{0}%'
    fixed-off: '<bold><#bcef26>-{0}'
    discount-code-list: '<gold>正在列出你的優惠代碼：'
    discount-code-applied-in-purchase: '<#bcef26>你的優惠代碼 <yellow>{0}</yellow> 已經套用到此交易，並且為你節省了 <gold>{1}</gold>！'
    scope:
      this-shop: '{0}'
      your-shops-owned: 所有你的商店（已擁有）
      your-shops-managed: 所有你的商店（已管理）
      server: 整個伺服器
    code-type:
      SERVER_ALL_SHOPS: 所有此伺服器的商店
      PLAYER_ALL_SHOPS: 由優惠代碼擁有者建立的所有商店
      SPECIFIC_SHOPS: 指定商店
    discount-code-details: |-
      <gold>優惠代碼：<yellow>{0}</yellow>
      建立者：<yellow>{1}</yellow>
      套用於：<yellow>{2}</yellow>
      剩餘使用量：<yellow>{3}</yellow>
      過期時間：<yellow>{4}</yellow>
      門檻：<yellow>{5}</yellow>
      折扣：<yellow>{6}</yellow><br>
  list:
    commands:
      list: <yellow>列出你或指定玩家擁有的所有商店
    table-prefix: '<yellow><green>{0}</green>的商店 <gray>（總共 {1})</gray>：'
    table-prefix-pageable: '<yellow><green>{0}</green> 的商店<gray>（頁面 {1} / {2}）</gray>：'
    entry: <yellow><hover:show_text:'<yellow>{1}</yellow><br><gray>{2} {3},{4},{5}</gray><br><aqua>價格 <yellow>{6}</yellow> - <light_purple>x{7}</light_purple> <yellow>{8}</yellow><br><green>{9}'>{0}. <aqua>{1}
  shopitemonly:
    message: <red>你不能將非商店交易物品放入商店容器中，所有非商店交易的物品都會被直接丟到地上。
  limited:
    commands:
      limit: <yellow>限制玩家在時間段內的交易
    titles:
      title: <green>購買成功
      subtitle: <aqua>你還可以在此商店交易 <gold>{0}</gold> 個物品
    reach-the-limit: <red>你與此商店已達到交易限制。你還可以交易 <green>{0}</green> 個物品，但你正在嘗試交易 <yellow>{1}</yellow> 個物品。
    success-reset: <green>成功地重設此商店的限制
    success-remove: <green>成功地移除此商店的所有限制
    success-setup: <green>成功地保存此商店的限制設置
    trade-limit-reached-cancel-reason: <red>已達到了此商店的交易限制
    remains-limits: '<gold>你在此商店剩餘的交易數量：<yellow>{0}'
    remains-limits-before-reset-period-note: '<gold>此商店將重設他們的購買數量：<yellow>{0}'
  dynmap:
    markerset-title: QuickShop-Hikari 商店
    marker-name: '{0} @ {2} @ {3} x{4} @ {5}'
    marker-description: |
      名稱：{0}
      擁有者：{1}
      物品：{2}
      價格：{4} {3} 個
      類型：{5}
      無限商店：{6}
      座標：{7}
  bluemap:
    markerset-title: QuickShop-Hikari 商店
    marker-name: '{0} @ {2} @ {3} x{4} @ {5}'
    marker-description: |
      名稱：{0}
      擁有者：{1}
      物品：{2}
      價格：{4} {3} 個
      類型：{5}
      無限商店：{6}
      座標：{7}
  chestprotect:
    protection-exists: <red>此區域已受到 ChestProtect 保護，所以你沒有在此建立商店的權限。
    shops-exsts: <red>你想要保護的區域包含其他玩家的商店，所以你沒有訪問它們的權限。
  displaycontrol:
    toggle: |-
      <green>成功地切換 QuickShop 懸浮物顯示為 <aqua>{0}</aqua>。
      <yellow>你可能需要重新登入才會生效。
    toggle-exception: <red>由於內部錯誤，無法切換你的懸浮物設定，請聯絡伺服器管理員。
    command:
      displaycontrol: <yellow>切換你的 QuickShop 懸浮物顯示
  reremake-migrator:
    commands:
      migratefromreremake: 遷移 QuickShop-Reremake 的資料到 QuickShop-Hikari
    server-not-empty: "<red>轉換過程中不允許玩家在伺服器上在線上，請開啟伺服器白名單或維護模式。"
    starting-convert-progress: "<gold>正在開始遷移，<red>請勿關閉伺服器！</red>"
    executing: "<gold>正在執行遷移組件 <aqua>{0}</aqua> <dark_gray>（{1} / {2}）</dark_gray>……"
    completed: "<green>讚啦！遷移完成，請重新啟動伺服器並從外掛程式目錄中刪除 QuickShop-Reremake。"
    join_blocking_converting: "<red>[QuickShop-Hikari 遷移器] 此伺服器正在進行資料遷移，所以你現在無法加入伺服器，請稍後再試！"
    join_blocking_finished: "<red>[QuickShop-Hikari 轉換器] 此伺服器剛剛完成資料轉換，正在等待重新啟動以套用變更，請稍後再試！"
    failed: "<red>遷移進度退出並出現錯誤，請檢查伺服器控制台。"
    modules:
      config:
        copy-values: "<yellow>正在複製值（總共 {0} 個項目）……"
        copying-value: "<gray> - 正在複製 <aqua>{0}</aqua> <dark_gray>（{1} / {2}）</dark_gray>……"
        migrate-price-restriction: "<yellow>正在遷移與價格相關的設定……"
        migrate-price-restriction-entry: "<gray> - 正在遷移 <aqua>{0}</aqua> <dark_gray>（{1} / {2}）</dark_gray>……"
      shop:
        start-migrate: "<yellow>正在遷移商店（總共 {0} 個項目）……"
        migrate-entry: "<gray> - 正在遷移 <aqua>{0}</aqua> <dark_gray>（{1} / {2}）</dark_gray>……"
        unloading-reremake: "<gold>正在解除載入 Reremake 以避免資料覆蓋……"
        register-entry: "<gray> - 正在註冊 <aqua>{0}</aqua> <dark_gray>（{1} / {2}）</dark_gray>……"
        save-entry: "<gray> - 正在儲存 <aqua>{0}</aqua> <dark_gray>（{1} / {2}）</dark_gray>……"
        saving-shops: "<yellow>正在儲存 <gold>{0}</gold> 商店，這可能需要一段時間™（基於商店的數量）……"
        conflict: "<gray> - 衝突 > 偵測 Reremake 商店與現有 Hikari 商店位置之間的衝突，並採取預定義的行為：<dark_gray>{0}</dark_gray>"
      translation:
        start-migrate: "<yellow>正在遷移檔案……</yellow>"
        copy-values: "<yellow>複製區域設置中的值 <gold>{0}</gold>（總計 {1} 項目）……"
        migrate-entry: "<gray> - 正在遷移 <aqua>{0}</aqua> <dark_gray>（{1} / {2}）</dark_gray>……"
        copying-value: "<gray> - 正在複製 <aqua>{0}</aqua> <dark_gray>（{1} / {2}）</dark_gray>……"
      shop-logs:
        start-migrate: "<yellow>正在遷移商店記錄檔（請詳見控制台），請稍候……</yellow>"
        extract-history-files: "<gray>請等待我們解壓縮並附加歷史記錄檔……"
        filter-history-files: "<gray>請等待我們篩選歷史記錄檔……"
        filtered-history-files: "<gray>從序列中篩選出 {0} 行。"
        import-entry: "<gray> - 正在遷移 <aqua>{0}</aqua> <dark_gray>（{1} / {2}）</dark_gray>……"
compat:
  advancedchests:
    created: <green>你已成功在 AdvancedChests 容器上建立商店！
    permission-denied: <red>抱歉！你沒有在 AdvancedChests 容器上建立商店的權限！
suite:
  dynmap:
    title: '{0} - {1} - {2}'
    description: |-
      物品：{0}
      擁有者：{1}
      類型：{2} {3}
      價格：{4}
      座標：{5} {6}, {7}, {8}
      空間：{9}
      庫存：{10}
  limited:
    command-description: <yellow>限制玩家在時間段內的交易。
    reach-the-quota-limit: <red>你已經達到了這間商店的購買限制（{0}/{1}）。
    quota-reset-countdown: <yellow>此商店的交易限制將在 {0} 重設。
    quota-reset-player-successfully: <green>已成功地重設此商店中玩家 {0} 的交易限制。
    quota-reset-everybody-successfully: <green>已成功地重設此商店中所有人的交易限制。
    quota-setup: <green>此商店的交易限制已套用成功！
    quota-remove: <green>此商店的交易限制已成功移除！
    subtitles:
      title: <green>購買成功
      subtitle: <aqua>你還可以在此商店交易 <yellow>{0}</yellow> 個物品
  list:
    command-description: <yellow>列出自己或其他玩家擁有的所有商店。
    table-prefix: <yellow>你在此伺服器中擁有 <aqua>{0}</aqua> 個商店。
    entry-syntax: <aqua>- <yellow>{0}
    entry-syntax-fallback-name: '<yellow>物品：{0} X:{1}, Y:{2}, Z:{3}, 世界： {4}'
    hover-text: |-
      <yellow>{0}
      <gray>{1} {2}, {3}, {4}
      <aqua>{5} - <yellow>{6}
      每組 {7} 個物品。
  shopitemonly:
    message: <red>你不能將非商店交易物品放入商店容器中，所有非商店交易的物品都會被直接丟到地上。
compatibility:
  elitemobs:
    soulbound-disallowed: 你不能交易擁有 EliteMobs 靈魂綁定附魔的物品。
internet-paste-forbidden-privacy-reason: "<red>失敗！根據你的隱私權設定，不允許 QuickShop-Hikari 將你的 paste 上傳到網路上，在 config.yml 的 privacy setting（隱私權設定）中開啟 DIAGNOSTIC（診斷）權限或使用 <aqua>/quickshop paste --file</aqua> 代替。"
no-sign-type-given: "<red>你需要提供告示牌類型，此伺服器可供使用的告示牌類型：{0}"
sign-type-invalid: "<red>告示牌 <yellow>{0}</yellow> 是無效的材質。"
delete-controlpanel-button-confirm: "<red>你真的想刪除這家商店嗎？在 {0} 秒內按兩下 <bold>[刪除商店]</bold> 按鈕來再次確認。"
cannot-suggest-price: "<red>對不起，目前沒有更多的人和你交易相同的物品，也沒有足夠的資料來生成建議的價格。"
price-suggest: "<green>基於來自 <aqua>{0}</aqua> 商店的資料，價格最高的商店價格為 <light_purple>{1}</light_purple>，價格最低的商店價格為 <light_purple>{2}</light_purple>，平均價格為 <light_purple>{3}</light_purple>，中位數價格為 <light_purple>{4}</light_purple>。<newline><yellow>建議你將價格設定在 <gold>{5}</gold>。</yellow>"
suggest-wait: "<green>請稍候……正在計算建議價格。"
history:
  shop:
    gui-title: "查看購買歷史記錄"
    header-icon-multiple-shop: "<white>查詢結果 {0} 商店</white>"
    header-icon-description:
      - "<white>類型：<yellow>{0}</yellow></white>"
      - "<white>擁有者：<yellow>{1}</yellow></white>"
      - "<white>物品：<yellow>{2}</yellow></white>"
      - "<white>價格：<yellow>{3} <aqua>x{4}</aqua></yellow></white>"
      - "<white>座標：<yellow>{5}</yellow></white>"
    log-icon-title: "<reset><green>時間：{0}</green>"
    log-icon-description:
      - "<white>買家：<yellow>{0}</yellow></white>"
      - "<white>物品：<yellow>{1}<aqua>{2}</aqua></yellow></white>"
      - "<white>花費：<yellow>{3}</yellow></white>"
      - "<white>稅金：<yellow>{4}</yellow></white>"
    log-icon-description-with-store-name:
      - "<white>商店：<yellow>{0}</yellow></white>"
      - "<white>買家：<yellow>{1}</yellow></white>"
      - "<white>物品：<yellow>{2}<aqua>{3}</aqua></yellow></white>"
      - "<white>花費：<yellow>{4}</yellow></white>"
      - "<white>稅：<yellow>{5}</yellow></white>"
    query-icon: "<gray>請稍候，正在查詢……</gray>"
    previous-page: "<white>上一頁</white>"
    next-page: "<white>下一頁</white>"
    current-page: "<white>第{0}頁</white>"
    summary-icon-title: "<green>商店摘要"
    recent-purchases: "<white>最近 <aqua>{0}</aqua> 購買：<yellow>{1}</yellow></white>"
    recent-purchase-balance: "<white>最近 <aqua>{0}</aqua> 營業額：<yellow>{1}</yellow></white>"
    total-purchases: "<white>總購買：<yellow>{0}</yellow></white>"
    total-balances: "<white>總營業額：<yellow>{0}</yellow></white>"
    total-unique-purchasers: "<white>總交易者：<yellow>{0}</yellow></white>"
    top-n-valuable-customers-title: "<gold>消費次數排行榜TOP{0}</gold>"
    top-n-valuable-customers-entry: "<white>- <yellow>{0}</yellow> <aqua>{1}</aqua></white>"
    no-result: "<white>暫無結果</white>"
about:
  text:
    - "<b><aqua>QuickShop <yellow>{0}</yellow></aqua></b>"
    - "<aqua>版本 <yellow>>></yellow> <green>{1}</green></aqua>"
    - "<aqua>正式版 <yellow>>></yellow> <green>{2}</green></aqua>"
    - "<aqua>開發人員<yellow>>></yellow> <color:#218bff><hover:show_text:'<yellow>點擊查看貢獻者'><click:open_url:'https://github.com/Ghost -chu/QuickShop-Hikari/graphs/contributors'>[在 GitHub 上查看貢獻者]</click></hover></color></aqua>"
    - "<aqua>在地化成員 <gold>({4})</gold>: <yellow>>></yellow> <green>{5}</green></aqua>"
    - "  <yellow>+ <color:#43A047><hover:show_text:'<yellow>點擊開啟 Crowdin 翻譯頁面><click:open_url:'https://crowdin.com/project/quickshop-hikari'>[在 Crowdin 上協助翻譯]</click></hover></color></yellow>"
    - "<aqua>贊助金鑰 <yellow>>></yellow> <green>{6}</green></aqua>"
    - "<gold>由社區支援</gold> <red>用 ❤ 發電</red>"
  valid-donation-key: "<color:#00AFF1>綁定到 <color:#F9C23C>{0}</color></color>"
  invalid-donation-key: "<gray>無效的贊助金鑰</gray>"
  kofi-thanks: "<gold>特別感謝那些在 Ko-fi 上支持 QuickShop 的人 :)</gold>"
history-command-leave-blank: "<參數為空時查詢面前的商店>"
shop-information-not-shown-due-an-internal-error: "<red>發生內部錯誤。商店資訊面板可能顯示不完整，請聯絡伺服器管理員。"
