<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>easysql-parent</artifactId>
        <groupId>cc.carm.lib</groupId>
        <version>0.4.7</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <properties>
        <maven.compiler.source>${jdk.version}</maven.compiler.source>
        <maven.compiler.target>${jdk.version}</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.encoding>UTF-8</maven.compiler.encoding>
    </properties>

    <artifactId>easysql-hikaricp</artifactId>

    <name>EasySQL-HikariCP</name>
    <description>EasySQL的应用部分。此为HikariCP版本。</description>
    <url>https://github.com/CarmJos/EasySQL</url>

    <developers>
        <developer>
            <id>CarmJos</id>
            <name>Carm Jos</name>
            <email><EMAIL></email>
            <url>https://www.carm.cc</url>
            <roles>
                <role>Main Developer</role>
            </roles>
        </developer>
    </developers>

    <licenses>
        <license>
            <name>The MIT License</name>
            <url>https://opensource.org/licenses/MIT</url>
            <comments>EasySQL与HikariCP均采用MIT开源协议。</comments>
        </license>
    </licenses>

    <issueManagement>
        <system>GitHub Issues</system>
        <url>https://github.com/CarmJos/EasySQL/issues</url>
    </issueManagement>

    <ciManagement>
        <system>GitHub Actions</system>
        <url>https://github.com/CarmJos/EasySQL/actions/workflows/maven.yml</url>
    </ciManagement>

    <dependencies>

        <dependency>
            <groupId>${project.parent.groupId}</groupId>
            <artifactId>easysql-api</artifactId>
            <version>${project.parent.version}</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>${project.parent.groupId}</groupId>
            <artifactId>easysql-impl</artifactId>
            <version>${project.parent.version}</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <!--项目地址 https://github.com/brettwooldridge/HikariCP/  -->
            <groupId>com.zaxxer</groupId>
            <artifactId>HikariCP</artifactId>
            <!--suppress MavenPackageUpdate -->
            <version>4.0.3</version>
            <optional>true</optional>
            <scope>compile</scope>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
                <version>3.4.1</version>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <relocations>
                        <relocation>
                            <pattern>com.zaxxer.hikari</pattern>
                            <shadedPattern>cc.carm.lib.easysql.hikari</shadedPattern>
                        </relocation>
                    </relocations>
                    <filters>
                        <filter>
                            <artifact>*:*</artifact>
                            <excludes>
                                <exclude>META-INF/MANIFEST.MF</exclude>
                                <exclude>META-INF/*.txt</exclude>
                            </excludes>
                        </filter>
                    </filters>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>