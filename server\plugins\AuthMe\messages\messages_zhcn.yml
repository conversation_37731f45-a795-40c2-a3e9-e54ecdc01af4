# List of global tags:
# %nl% - Goes to new line.
# %username% - Replaces the username of the player receiving the message.
# %displayname% - Replaces the nickname (and colors) of the player receiving the message.

# Registration
registration:
    disabled: '&8[&6玩家系统&8] &c目前服务器暂时禁止注册，请到服务器论坛以得到更多资讯'
    name_taken: '&8[&6玩家系统&8] &c此用户已经在此服务器注册过'
    register_request: '&8[&6玩家系统&8] &c请输入“/register <密码> <再输入一次以确定密码>”以注册'
    command_usage: '&8[&6玩家系统&8] &c正确用法：“/register <密码> <再输入一次以确定密码>”'
    reg_only: '&8[&6玩家系统&8] &f只允许注册过的玩家进服！请到 https://example.cn 注册'
    success: '&8[&6玩家系统&8] &c已成功注册！'
    kicked_admin_registered: '有一位管理员刚刚为您完成了注册，请重新登录'

# Password errors on registration
password:
    match_error: '&8[&6玩家系统&8] &f密码不相同'
    name_in_password: '&8[&6玩家系统&8] &f你不能使用你的名字作为密码。 '
    unsafe_password: '&8[&6玩家系统&8] &f你不能使用安全性过低的密码。 '
    forbidden_characters: '&4您的密码包含了非法字符。可使用的字符: %valid_chars'
    wrong_length: '&8[&6玩家系统&8] &c你的密码不符合要求'

# Login
login:
    command_usage: '&8[&6玩家系统&8] &c正确用法：“/login <密码>”'
    wrong_password: '&8[&6玩家系统&8] &c错误的密码'
    success: '&8[&6玩家系统&8] &c已成功登录！'
    login_request: '&8[&6玩家系统&8] &c请输入“/login <密码>”以登录'
    timeout_error: '&8[&6玩家系统&8] &f登录超时'

# Errors
error:
    denied_command: '&c您需要先通过验证才能使用该命令！'
    denied_chat: '&c您需要先通过验证才能聊天！'
    unregistered_user: '&8[&6玩家系统&8] &c此用户名还未注册过'
    not_logged_in: '&8[&6玩家系统&8] &c你还未登录！'
    no_permission: '&8[&6玩家系统&8] &c没有权限'
    unexpected_error: '&8[&6玩家系统&8] &f发现错误，请联系管理员'
    max_registration: '&8[&6玩家系统&8] &f你不允许再为你的IP在服务器注册更多用户了！'
    logged_in: '&8[&6玩家系统&8] &c你已经登陆过了！'
    kick_for_vip: '&8[&6玩家系统&8] &cA VIP玩家加入了已满的服务器!'
    kick_unresolved_hostname: '&8[&6玩家系统&8] &c发生了一个错误: 无法解析玩家的Hostname'
    tempban_max_logins: '&c由于您登录失败次数过多，已被暂时禁止登录。'

# AntiBot
antibot:
    kick_antibot: '&8[&6玩家系统&8] &f验证程序已启用 !请稍等几分钟后再次进入服务器'
    auto_enabled: '&8[&6玩家系统&8] &f验证程序由于大量异常连接而启用'
    auto_disabled: '&8[&6玩家系统&8] &f验证程序由于异常连接减少而在 %m 分钟后停止'

# Unregister
unregister:
    success: '&8[&6玩家系统&8] &c成功删除此用户！'
    command_usage: '&8[&6玩家系统&8] &c正确用法：“/unregister <密码>”'

# Other messages
misc:
    account_not_activated: '&8[&6玩家系统&8] &f你的帐号还未激活，请查看你的邮箱！'
    password_changed: '&8[&6玩家系统&8] &c密码已成功修改！'
    logout: '&8[&6玩家系统&8] &c已成功登出！'
    reload: '&8[&6玩家系统&8] &f配置以及数据已经重新加载完毕'
    usage_change_password: '&8[&6玩家系统&8] &f正确用法：“/changepassword 旧密码 新密码”'
    accounts_owned_self: '您拥有 %count 个账户：'
    accounts_owned_other: '玩家 %name 拥有 %count 个账户：'

# Session messages
session:
    valid_session: '&8[&6玩家系统&8] &c欢迎回来，已帮你自动登录到此服务器'
    invalid_session: '&8[&6玩家系统&8] &f登录数据异常，请等待登录结束'

# Error messages when joining
on_join_validation:
    same_ip_online: '已有一个同IP玩家在游戏中了！'
    same_nick_online: '&8[&6玩家系统&8] &f同样的用户名现在在线且已经登录了！'
    name_length: '&8[&6玩家系统&8] &c你的用户名太短或者太长了'
    characters_in_name: '&8[&6玩家系统&8] &c你的用户名包含非法字母，用户名里允许的字母: %valid_chars'
    kick_full_server: '&8[&6玩家系统&8] &c抱歉，服务器已满!'
    country_banned: '这个服务器禁止该国家登陆'
    not_owner_error: '&8[&6玩家系统&8] &4警告！ &c你并不是此帐户的拥有者，请立即登出！ '
    invalid_name_case: '&8[&6玩家系统&8] &c你应该使用「%valid」而并非「%invalid」登入游戏。 '
    quick_command: '&8[&6玩家系统&8] &c您发送命令的速度太快了，请重新加入服务器再等待一会后再使用命令'

# Email
email:
    add_email_request: '&8[&6玩家系统&8] &c请输入“/email add <你的邮箱> <再输入一次以确认>”以添加您的邮箱到此帐号'
    usage_email_add: '&8[&6玩家系统&8] &f用法: /email add <邮箱> <确认邮箱地址> '
    usage_email_change: '&8[&6玩家系统&8] &f用法: /email change <旧邮箱> <新邮箱> '
    new_email_invalid: '&8[&6玩家系统&8] &f新邮箱无效!'
    old_email_invalid: '&8[&6玩家系统&8] &f旧邮箱无效!'
    invalid: '&8[&6玩家系统&8] &f无效的邮箱'
    added: '&8[&6玩家系统&8] &f邮箱已添加 !'
    add_not_allowed: '&8[&6玩家系统&8] &c服务器不允许添加邮箱地址'
    request_confirmation: '&8[&6玩家系统&8] &f确认你的邮箱 !'
    changed: '&8[&6玩家系统&8] &f邮箱已修改 !'
    change_not_allowed: '&8[&6玩家系统&8] &c服务器不允许修改邮箱地址'
    email_show: '&8[&6玩家系统&8] &2您当前的电子邮件地址为： &f%email'
    no_email_for_account: '&8[&6玩家系统&8] &2您当前并没有任何邮箱与该账号绑定'
    already_used: '&8[&6玩家系统&8] &4邮箱已被使用'
    incomplete_settings: '&8[&6玩家系统&8] 错误：必要设置未设定完成，请联系管理员'
    send_failure: '&8[&6玩家系统&8] 邮件发送失败，请联系管理员'
    change_password_expired: '&8[&6玩家系统&8] 您不能使用此命令更改密码'
    email_cooldown_error: '&8[&6玩家系统&8] &c邮件已在几分钟前发送，您需要等待 %time 后才能再次请求发送'

# Password recovery by email
recovery:
    forgot_password_hint: '&8[&6玩家系统&8] &c忘了你的密码？请输入：“/email recovery <你的邮箱>”'
    command_usage: '&8[&6玩家系统&8] &f用法: /email recovery <邮箱>'
    email_sent: '&8[&6玩家系统&8] &f找回密码邮件已发送 !'
    code:
        code_sent: '一个用于重置您的密码的验证码已发到您的邮箱'
        incorrect: '验证码不正确！ 使用 /email recovery [邮箱] 以生成新的验证码'
        tries_exceeded: '您已经达到输入验证码次数的最大允许次数。请使用 "/email recovery [邮箱]" 来生成一个新的'
        correct: '验证码正确！'
        change_password: '请使用 /email setpassword <新密码> 立即设置新的密码'

# Captcha
captcha:
    usage_captcha: '&8[&6玩家系统&8] &c正确用法：/captcha %captcha_code'
    wrong_captcha: '&8[&6玩家系统&8] &c错误的验证码，请输入：“/captcha %captcha_code”'
    valid_captcha: '&8[&6玩家系统&8] &c你的验证码是有效的！'
    captcha_for_registration: '注册前您需要先提供验证码，请使用指令：/captcha %captcha_code'
    register_captcha_valid: '&2有效的验证码！您现在可以使用 /register 注册啦！'

# Verification code
verification:
    code_required: '&3这个命令非常敏感，需要电子邮件验证！请检查您的收件箱，并遵循邮件的指导。'
    command_usage: '&c使用方法： /verification <验证码>'
    incorrect_code: '&c验证码错误， 请在聊天框输入 "/verification <验证码>"，使用您在电子邮件中收到的验证码。'
    success: '&2您的身份已经得到验证！您现在可以在当前会话中执行所有命令！'
    already_verified: '&2您已经可以在当前会话中执行任何敏感命令！'
    code_expired: '&3您的验证码已失效！执行另一个敏感命令以获得新的验证码！'
    email_needed: '&3为了验证您的身份，您需要将一个电子邮件地址与您的帐户绑定！！'

# Time units
time:
    second: '秒'
    seconds: '秒'
    minute: '分'
    minutes: '分'
    hour: '小时'
    hours: '小时'
    day: '天'
    days: '天'

# Two-factor authentication
two_factor:
    code_created: '&8[&6玩家系统&8] &a你的代码是 %code，你可以使用 %url 来进行扫描'
    confirmation_required: '&8[&6玩家系统&8] &3请输入 &a/2fa confirm <验证码> &3来确认双重认证'
    code_required: '&8[&6玩家系统&8] &c请输入 &a/2fa code <验证码> &c来提交双重认证验证码'
    already_enabled: '&8[&6玩家系统&8] &a双重认证已在您的账号上启用'
    enable_error_no_code: '&8[&6玩家系统&8] &c双重认证密钥不存在或已过期，请输入 &a/2fa add &c来添加'
    enable_success: '&8[&6玩家系统&8] &a已成功启用双重认证'
    enable_error_wrong_code: '&8[&6玩家系统&8] &c双重认证代码错误或者已经过期，请重新执行 &a/2fa add'
    not_enabled_error: '&8[&6玩家系统&8] &c双重认证码未在您的账号上启用，请使用 &a/2fa add &c来启用'
    removed_success: '&8[&6玩家系统&8] &c双重认证码已从您的账号上删除'
    invalid_code: '&8[&6玩家系统&8] &c无效的验证码'
