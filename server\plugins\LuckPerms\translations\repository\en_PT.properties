luckperms.logs.actionlog-prefix=PLANK
luckperms.logs.verbose-prefix=VB
luckperms.logs.export-prefix=EXPORT
luckperms.commandsystem.available-commands=Use {0} to see th'' orders available ta ye
luckperms.commandsystem.command-not-recognised=That order be missin''
luckperms.commandsystem.no-permission=Ye cannot give th'' crew that order, ye not th'' cap''n\!
luckperms.commandsystem.no-permission-subcommands=Ye cannot give th'' crew any sub orders, ye not th'' cap''n
luckperms.commandsystem.already-executing-command=A command is being execut''d, ye ''ave to wait for it to finish...
luckperms.commandsystem.usage.sub-commands-header=Nested Orders
luckperms.commandsystem.usage.usage-header=Use o'' th'' order
luckperms.commandsystem.usage.arguments-header=Arguments
luckperms.first-time.no-permissions-setup=It seems ye crew been spendin'' too much time sleepin'' and th'' deck still needin'' ta be scrubbed\!
luckperms.first-time.use-console-to-give-access=Before ye can use any o'' the luckyperms commands in-game, ye need to use the console to give yerself access
luckperms.first-time.console-command-prompt=Dock ashore ''n'' run
luckperms.first-time.next-step=After ye done this ''ere, ye can begin givin'' orders ''n'' dictatin'' what be allowed
luckperms.first-time.wiki-prompt=Lost at sea? Check ''ere\: {0}
luckperms.login.try-again=Belay that order, matey
luckperms.login.loading-database-error=Alas\! Ye treasure chest containin'' ta allowed orders not be workin''
luckperms.login.server-admin-check-console-errors=If ye be the ship''s cap''n, dock ashore and be on th'' lookout fer errors
luckperms.login.server-admin-check-console-info=Splice the mainbrace ashore fer that there information
luckperms.login.data-not-loaded-at-pre=Ye treasure chest with yer allowed orders fer yer crew not been loaded durin'' boardin''
luckperms.login.unable-to-continue=marooned
luckperms.login.craftbukkit-offline-mode-error=this here likely be caused by a conflict between CraftBukkit an'' the online-mode settin''
luckperms.login.unexpected-error=Somethin'' we weren''t lookin'' out fer happened when we be settin'' up yer allowed orders
luckperms.opsystem.disabled=Ye vanilla cap''n system be disabled on this ''ere server
luckperms.opsystem.sponge-warning=Ye been made aware that yer server cap''n status ''as no effect on checks o'' th'' allowed orders on Sponge when ye be usin'' a plugin ta control th'' allowed orders, ye must take control a'' th'' crew
luckperms.duration.unit.years.plural={0} yars
luckperms.duration.unit.years.singular={0} yar
luckperms.duration.unit.years.short={0}y
luckperms.duration.unit.months.plural={0} months
luckperms.duration.unit.months.singular={0} month
luckperms.duration.unit.months.short={0}mo
luckperms.duration.unit.weeks.plural={0} weeks out at sea
luckperms.duration.unit.weeks.singular={0} week
luckperms.duration.unit.weeks.short={0}w
luckperms.duration.unit.days.plural={0} days out at sea
luckperms.duration.unit.days.singular={0} day
luckperms.duration.unit.days.short={0}d
luckperms.duration.unit.hours.plural={0} hourglasses passed
luckperms.duration.unit.hours.singular={0} hourglass
luckperms.duration.unit.hours.short={0}h
luckperms.duration.unit.minutes.plural={0} minutes of sailing
luckperms.duration.unit.minutes.singular={0} minute
luckperms.duration.unit.minutes.short={0}m
luckperms.duration.unit.seconds.plural={0} seconds
luckperms.duration.unit.seconds.singular={0} second
luckperms.duration.unit.seconds.short={0}s
luckperms.duration.since={0} sailings ago
luckperms.command.misc.invalid-code=Yer code be gobbledygook
luckperms.command.misc.response-code-key=code a'' th'' received message
luckperms.command.misc.error-message-key=message
luckperms.command.misc.bytebin-unable-to-communicate=Unable ta parrot over a message ta bytebin
luckperms.command.misc.webapp-unable-to-communicate=Unable ta parrot over a message ta th'' web app
luckperms.command.misc.check-console-for-errors=Alas\! Dock ashore and be on th'' lookout fer errors
luckperms.command.misc.file-must-be-in-data=Book {0} gotta be in th'' data diary
luckperms.command.misc.wait-to-finish=Ye must wait fer it ta be finished an'' try again
luckperms.command.misc.invalid-priority=One a'' ye priorities be invalid {0}
luckperms.command.misc.expected-number=Ye order be needin'' a number to keep sailin''
luckperms.command.misc.date-parse-error={0} not be a date
luckperms.command.misc.date-in-past-error=Any seadog can tell ye don''t live in the past\!
luckperms.command.misc.page=lookin'' at chest {0} of {1}
luckperms.command.misc.page-entries={0} doubloons
luckperms.command.misc.none=Nothin''
luckperms.command.misc.loading.error.unexpected=A Jolly Roger appeared out ''a nowhere
luckperms.command.misc.loading.error.user=Scallywag not loaded
luckperms.command.misc.loading.error.user-specific=Unable ta load ye target lad {0}
luckperms.command.misc.loading.error.user-not-found=A lad fer {0} not be found
luckperms.command.misc.loading.error.user-save-error=There be an error whilst savin'' {0}''s data
luckperms.command.misc.loading.error.user-not-online=Crewmate {0} ain''t ''ere
luckperms.command.misc.loading.error.user-invalid=Arrrrgghhh, {0} not on me ship
luckperms.command.misc.loading.error.user-not-uuid=One a'' yer lads {0} be missin'' a valid id
luckperms.command.misc.loading.error.group=This crew not be ''ere
luckperms.command.misc.loading.error.all-groups=Some a'' ye crew be lost at sea
luckperms.command.misc.loading.error.group-not-found=A crew named {0} not ''ere
luckperms.command.misc.loading.error.group-save-error=Error saving thee booty for {0}
luckperms.command.misc.loading.error.group-invalid={0} be lost at sea
luckperms.command.misc.loading.error.track=This track be lost at sea
luckperms.command.misc.loading.error.all-tracks=Some a'' me tracks be lost at sea
luckperms.command.misc.loading.error.track-not-found=Ye track {0} not be found
luckperms.command.misc.loading.error.track-save-error=There be an error writing sailing routes for {0}
luckperms.command.misc.loading.error.track-invalid=Ye don''t have yourself a valid name with {0}
luckperms.command.editor.no-match=Can''t open yer editor, matey\! Not an object matched yer desired type\!
luckperms.command.editor.start=Fixin'' you up yer brand new editor session\! Hold yer horses...
luckperms.command.editor.url=Click on yer link below to enter yer editor abyss
luckperms.command.editor.unable-to-communicate=Can''t reach yer editor, matey\!
luckperms.command.editor.apply-edits.success=Yer editor''s info was applicated to {0} {1} with great success
luckperms.command.editor.apply-edits.success-summary={0} {1} and {2} {3}
luckperms.command.editor.apply-edits.success.additions=additions
luckperms.command.editor.apply-edits.success.additions-singular=addition
luckperms.command.editor.apply-edits.success.deletions=slaughters
luckperms.command.editor.apply-edits.success.deletions-singular=slaughter
luckperms.command.editor.apply-edits.no-changes=Ther'' be no changes applied from th'' web editor, not a single line be different
luckperms.command.editor.apply-edits.unknown-type=Arr\! You can''t apply an edit to th'' specified object type
luckperms.command.editor.apply-edits.unable-to-read=Arr\! Unable to read the data using th'' given code
luckperms.command.search.searching.permission=Lookin'' through th'' shiplog for crewmates or groups with {0}
luckperms.command.search.searching.inherit=Lookin'' through th'' shiplog for crewmates or groups who inherit from {0}
luckperms.command.search.result=Found {0} entries from ye {1} crewmates and {2} groups
luckperms.command.search.result.default-notice=Note\: when searching through shiplogs for crewmates of th'' default group, crewmates on land with no other permissions will not be shown\!
luckperms.command.search.showing-users=Showing ye crewmates diary entries
luckperms.command.search.showing-groups=Showing ye group diary entries
luckperms.command.tree.start=Ordering the quartermaster to write down a permission tree, wait while he retrieves his parchment...
luckperms.command.tree.empty=Unable to generate th'' tree, no results were given
luckperms.command.tree.url=Permission tree URL
luckperms.command.verbose.invalid-filter={0} is a carouser of a verbose filter
luckperms.command.verbose.enabled=Verbose loggin'' {0} fer checks matchin'' {1}
luckperms.command.verbose.command-exec=Ordering {0} to execute th'' command {1} and reporting all actions...
luckperms.command.verbose.off=Verrrbose loggin'' {0}
luckperms.command.verbose.command-exec-complete=Yer crew has carried out yer orders
luckperms.command.verbose.command.no-checks=Th'' command was executed me hearties, ''owever no permission checks ''ere made
luckperms.command.verbose.command.possibly-async=Tis ''ight be ''cause th'' plug''n runs commands in the back''round (async)
luckperms.command.verbose.command.try-again-manually=Ye can stil'' use verbose manually to detec'' checks made like tis
luckperms.command.verbose.enabled-recording=Verbose loggin'' {0} fer checks matchin'' {1}
luckperms.command.verbose.uploading=Verbose logging {0}, ordering the quartermaster to retrieve th'' results...
luckperms.command.verbose.url=Veerrrrbooose results URL
luckperms.command.verbose.enabled-term=setting sail
luckperms.command.verbose.disabled-term=avast
luckperms.command.verbose.query-any=ANY
luckperms.command.info.running-plugin=Runnin'' with full sails
luckperms.command.info.platform-key=Platform
luckperms.command.info.server-brand-key=Serrverr Brranding
luckperms.command.info.server-version-key=Serrverr Verrsion
luckperms.command.info.storage-key=Barrels
luckperms.command.info.storage-type-key=Type
luckperms.command.info.storage.meta.split-types-key=Types
luckperms.command.info.storage.meta.ping-key=The wind is
luckperms.command.info.storage.meta.connected-key=All tied togethe''
luckperms.command.info.storage.meta.file-size-key=Amount of th'' barrels on board
luckperms.command.info.extensions-key=Extensions
luckperms.command.info.messaging-key=Messaging
luckperms.command.info.instance-key=Instance
luckperms.command.info.static-contexts-key=Static contexts
luckperms.command.info.online-players-key=Crewmates Aboard
luckperms.command.info.online-players-unique={0} unique
luckperms.command.info.uptime-key=Ye ship''s age
luckperms.command.info.local-data-key=Local Data
luckperms.command.info.local-data={0} crewmates, {1} groups, {2} tracks
luckperms.command.generic.create.success=Ye quartermaster organized {0}
luckperms.command.generic.create.error=Arr\! Some sailor messed up whilst creating {0}
luckperms.command.generic.create.error-already-exists=Ye already have {0}\!
luckperms.command.generic.delete.success={0} was sentenced to the plank
luckperms.command.generic.delete.error=Arr\! Some sailor messed up whilst deleting {0}
luckperms.command.generic.delete.error-doesnt-exist=Yer had too much rum; {0} don''t exist\!
luckperms.command.generic.rename.success={0} successfully be renamed to {1}
luckperms.command.generic.clone.success={0} successfully be cloned onto {1}
luckperms.command.generic.info.parent.title=Parent Groups
luckperms.command.generic.info.parent.temporary-title=Temporary Parent Groups
luckperms.command.generic.info.expires-in=is fish bait in
luckperms.command.generic.info.inherited-from=inherited frrom
luckperms.command.generic.info.inherited-from-self=yerself
luckperms.command.generic.show-tracks.title={0}''s Tracks
luckperms.command.generic.show-tracks.empty={0} not be in any of yer tracks
luckperms.command.generic.clear.node-removed={0} nodes were sent off th'' plank
luckperms.command.generic.clear.node-removed-singular={0} node was sent off th'' plank
luckperms.command.generic.clear={0}''s nodes were sent off the plank in context {1}
luckperms.command.generic.permission.info.title={0}''s Permissions
luckperms.command.generic.permission.info.empty={0} be havin'' no permissions yet
luckperms.command.generic.permission.info.click-to-remove=Click to remove th'' node from {0}
luckperms.command.generic.permission.check.info.title=Ye prems dont work for The {0}
luckperms.command.generic.permission.check.info.directly={0} ''as {1} set to {2} in th'' context {3}
luckperms.command.generic.permission.check.info.inherited={0} in''erits {1} set to {2} from {3} in contex'' {4}
luckperms.command.generic.permission.check.info.not-directly={0} does nah ''ave {1} set
luckperms.command.generic.permission.check.info.not-inherited={0} does nah inherit {1}
luckperms.command.generic.permission.check.result.title=Test if {0} is eligible of the gold
luckperms.command.generic.permission.check.result.result-key=Ye result
luckperms.command.generic.permission.check.result.processor-key=Processar
luckperms.command.generic.permission.check.result.cause-key=Cause
luckperms.command.generic.permission.check.result.context-key=Context
luckperms.command.generic.permission.set=Set {0} to {1} for {2} in th'' context {3}
luckperms.command.generic.permission.already-has=Arr\! {0} already has {1} set in context {2}
luckperms.command.generic.permission.set-temp=Set {0} to {1} for {2} for the time o'' {3} in context {4}
luckperms.command.generic.permission.already-has-temp=Arr\! {0} already has {1} set temporarily in context {2}
luckperms.command.generic.permission.unset=Wiped {0} for {1} in context {2} off the deck
luckperms.command.generic.permission.doesnt-have=Arr\! {0} does not have {1} set in context {2}
luckperms.command.generic.permission.unset-temp=Unset th'' temporary permission {0} for {1} in context {2}
luckperms.command.generic.permission.subtract=Set {0} to {1} for {2} for the time o'' {3} in context {4}, {5} less than yer previous clock
luckperms.command.generic.permission.doesnt-have-temp=Arr\! {0} does not have {1} set temporarily in context {2}
luckperms.command.generic.permission.clear={0}''s permissions were sent off the plank in context {1}
luckperms.command.generic.parent.info.title={0}''s Parents
luckperms.command.generic.parent.info.empty={0} don''t have any parents defined
luckperms.command.generic.parent.info.click-to-remove=Click to remove th'' parent from {0}
luckperms.command.generic.parent.add={0} now inherits th'' permissions from {1} in context {2}
luckperms.command.generic.parent.add-temp={0} now inherits th'' permissions from {1} for a duration of {2} in context {3}
luckperms.command.generic.parent.set={0} had their existing parent groups cleared, and now only inherits {1} in th'' context {2}
luckperms.command.generic.parent.set-track={0} had their existing parent groups on track {1} sent off the ship, and now only inherits {2} in th'' context {3}
luckperms.command.generic.parent.remove={0} no longer inherits th'' permissions from {1} in context {2}
luckperms.command.generic.parent.remove-temp={0} no longer temporarily inherits th'' permissions from {1} in context {2}
luckperms.command.generic.parent.subtract={0} now inherits th'' permissions from {1} for a duration of {2} in context {3}, {4} less than earlier
luckperms.command.generic.parent.clear={0}''s parents were sent off the plank in context {1}
luckperms.command.generic.parent.clear-track={0}''s parents on track {1} were sent off the plank in context {2}
luckperms.command.generic.parent.already-inherits=Arr\! {0} already inherits from {1} in th'' context {2}
luckperms.command.generic.parent.doesnt-inherit=Arr\! {0} doesn''t inherit from {1} in th'' context {2}
luckperms.command.generic.parent.already-temp-inherits={0} already temporarily inherits from {1} in th'' context {2}
luckperms.command.generic.parent.doesnt-temp-inherit={0} doesn''t temporarily inherit from {1} in th'' context {2}
luckperms.command.generic.chat-meta.info.title-prefix={0}''s Titles
luckperms.command.generic.chat-meta.info.title-suffix={0}''s Suffixes
luckperms.command.generic.chat-meta.info.none-prefix={0} has no titles
luckperms.command.generic.chat-meta.info.none-suffix={0} has no suffixes
luckperms.command.generic.chat-meta.info.click-to-remove=Click to remove {0} from {1}
luckperms.command.generic.chat-meta.already-has={0} already has {1} {2} set at th'' priority of {3} in th'' context of {4}
luckperms.command.generic.chat-meta.already-has-temp={0} already has {1} {2} set temporarily at th'' priority of {3} in th'' context of {4}
luckperms.command.generic.chat-meta.doesnt-have={0} doesn''t have {1} {2} set at th'' priority of {3} in th'' context of {4}
luckperms.command.generic.chat-meta.doesnt-have-temp={0} doesn''t have {1} {2} set temporarily at th'' priority of {3} in th'' context of {4}
luckperms.command.generic.chat-meta.add={0} already has {1} {2} set at th'' priority of {3} in th'' context of {4}
luckperms.command.generic.chat-meta.add-temp={0} already has {1} {2} set at th'' priority of {3} for th'' duration of {4} in context {5}
luckperms.command.generic.chat-meta.remove={0} already has {1} {2} set at th'' priority {3} removed in th'' context {4}
luckperms.command.generic.chat-meta.remove-bulk={0} already has {1} at th'' priority {2} removed in th'' context {3}
luckperms.command.generic.chat-meta.remove-temp={0} had temporary {1} {2} at th'' priority {3} removed in th'' context {4}
luckperms.command.generic.chat-meta.remove-temp-bulk={0} had all temporary {1} at th'' priority {2} removed in th'' context {3}
luckperms.command.generic.meta.info.title={0}''s Meta
luckperms.command.generic.meta.info.none=Arrr\! {0} has no meta
luckperms.command.generic.meta.info.click-to-remove=Click to remove yer meta node from {0}
luckperms.command.generic.meta.already-has={0} already has th'' meta key {1} set to {2} in th'' context {3}
luckperms.command.generic.meta.already-has-temp={0} already has th'' meta key {1} temporarily set to {2} in th'' context {3}
luckperms.command.generic.meta.doesnt-have={0} doesn''t have meta key {1} set in th'' context {2}
luckperms.command.generic.meta.doesnt-have-temp={0} doesn''t have meta key {1} set temporarily in th'' context {2}
luckperms.command.generic.meta.set=Set meta key {0} to {1} for {2} in th'' context {3}
luckperms.command.generic.meta.set-temp=Set meta key {0} to {1} for {2} for the time o'' {3} in th'' context {4}
luckperms.command.generic.meta.unset=Wiped th'' meta key {0} for {1} in th'' context {2}
luckperms.command.generic.meta.unset-temp=Unset th'' temporary meta key {0} for {1} in th'' context {2}
luckperms.command.generic.meta.clear={0}''s meta matching type {1} was sent off the plank in th'' context {2}
luckperms.command.generic.contextual-data.title=Contextual Data
luckperms.command.generic.contextual-data.mode.key=mode
luckperms.command.generic.contextual-data.mode.server=server
luckperms.command.generic.contextual-data.mode.active-player=active matey
luckperms.command.generic.contextual-data.contexts-key=Contexts
luckperms.command.generic.contextual-data.prefix-key=Yer Title
luckperms.command.generic.contextual-data.suffix-key=Suffix
luckperms.command.generic.contextual-data.primary-group-key=Primary Group
luckperms.command.generic.contextual-data.meta-key=Meta
luckperms.command.generic.contextual-data.null-result=Me none here
luckperms.command.user.info.title=Shipmate info
luckperms.command.user.info.uuid-key=UUID
luckperms.command.user.info.uuid-type-key=type
luckperms.command.user.info.uuid-type.mojang=mojang
luckperms.command.user.info.uuid-type.not-mojang=not onboard
luckperms.command.user.info.status-key=Status
luckperms.command.user.info.status.online=Aboard
luckperms.command.user.info.status.offline=Out fishin''
luckperms.command.user.removegroup.error-primary=Ye cannot remove a sailor from their primary allegiance
luckperms.command.user.primarygroup.not-member={0} wasn''t already a crewmate of {1}, ordering the quartermaster to add them now
luckperms.command.user.primarygroup.already-has={0} already has {1} set as their primary group
luckperms.command.user.primarygroup.warn-option=Arr\! Th'' primary group calculation method yer using ({0}) may not reflect this change
luckperms.command.user.primarygroup.set={0}''s primary group was set to {1}
luckperms.command.user.track.error-not-contain-group={0} isn''t in any groups on {1}
luckperms.command.user.track.unsure-which-track=The quartermaster wasn''t given enough information about th'' track to use, specify it as an argument
luckperms.command.user.track.missing-group-advice=Either create th'' group or remove it from th'' track and re-send th'' order to th'' sailors
luckperms.command.user.promote.added-to-first={0} isn''t in any groups on {1}, so they were added to th'' first group, {2} in th'' context {3}
luckperms.command.user.promote.not-on-track={0} isn''t in any groups on {1}, so the quartermaster didn''t bother with promoting them
luckperms.command.user.promote.success=Promoting {0} along th'' track {1} from {2} to {3} in th'' context {4}
luckperms.command.user.promote.end-of-track=Th'' end of track {0} was reached, unable to promote {1}
luckperms.command.user.promote.next-group-deleted=Th'' next group on the track, {0}, has been disbanded
luckperms.command.user.promote.unable-to-promote=Unable to promote th'' crewmate
luckperms.command.user.demote.success=Demoting {0} along th'' track {1} from {2} to {3} in th'' context {4}
luckperms.command.user.demote.end-of-track=Th'' end of track {0} was reached, so {1} was removed from {2}
luckperms.command.user.demote.end-of-track-not-removed=Th'' end of track {0} was reached, so {1} was removed from th'' first group
luckperms.command.user.demote.previous-group-deleted=Th'' previous group on the track, {0}, has been disbanded
luckperms.command.user.demote.unable-to-demote=Unable to demote th'' user
luckperms.command.group.list.title=Groups
luckperms.command.group.delete.not-default=Ye cannot scuttle th'' default group
luckperms.command.group.info.title=Group info
luckperms.command.group.info.display-name-key=Display Name
luckperms.command.group.info.weight-key=Weight
luckperms.command.group.setweight.set=Set weight to {0} for th'' group {1}
luckperms.command.group.setdisplayname.doesnt-have={0} doesn''t have a display name set
luckperms.command.group.setdisplayname.already-has={0} already has th'' display name of {1}
luckperms.command.group.setdisplayname.already-in-use=Th'' display name {0} already be taken by {1}
luckperms.command.group.setdisplayname.set=Set display name to {0} for th'' group {1} in th'' context {2}
luckperms.command.group.setdisplayname.removed=Walked the display name for th'' group {0} in context {1} off th'' plank
luckperms.command.track.list.title=Tracks
luckperms.command.track.path.empty=None
luckperms.command.track.info.showing-track=Showin'' Trrack
luckperms.command.track.info.path-property=Path
luckperms.command.track.clear={0}''s groups track contents were walked off the plank
luckperms.command.track.append.success=Group {0} was ad''ed to ye old track {1}
luckperms.command.track.insert.success=Group {0} was inserted into th'' track {1} at position {2}
luckperms.command.track.insert.error-number=Arr\! Expectin'' a number but got\: {0}
luckperms.command.track.insert.error-invalid-pos=Unable to insert at th'' position {0}
luckperms.command.track.insert.error-invalid-pos-reason=invalid position
luckperms.command.track.remove.success=Group {0} was removed from th'' track {1}
luckperms.command.track.error-empty={0} can''t be used as it''s empty or contains only one group
luckperms.command.track.error-multiple-groups={0} is a member of multiple of th'' groups on this tracks
luckperms.command.track.error-ambiguous=Unable to determine their location
luckperms.command.track.already-contains={0} already contains {1}
luckperms.command.track.doesnt-contain={0} doesn''t contain {1}
luckperms.command.log.load-error=Th'' log couldn''t be loaded
luckperms.command.log.invalid-page=There be no chart of that number, captain
luckperms.command.log.invalid-page-range=Arr\! Enter a number between {0} and {1}
luckperms.command.log.empty=The ship''s logs are empty, captain
luckperms.command.log.notify.error-console=Th'' console''s bell can''t be muted
luckperms.command.log.notify.enabled-term=Setting sail
luckperms.command.log.notify.disabled-term=Docked
luckperms.command.log.notify.changed-state={0} loggin'' its output
luckperms.command.log.notify.already-on=''ur ship''s bell is already ringing
luckperms.command.log.notify.already-off=''ur ship''s bell is muted
luckperms.command.log.notify.invalid-state=Arr\! That state is unknown, expecting {0} or {1}
luckperms.command.log.show.search=Showing th'' recent actions for query {0}
luckperms.command.log.show.recent=Showing th'' ship''s logs
luckperms.command.log.show.by=Showing th'' ship''s logs by {0}
luckperms.command.log.show.history=Showing th'' history for {0} {1}
luckperms.command.export.error-term=Arr\! Some sailor messed up
luckperms.command.export.already-running=Arr\! Another export process is already running
luckperms.command.export.file.already-exists=File {0} alrrready exists
luckperms.command.export.file.not-writable=File {0} ain''t writeable
luckperms.command.export.file.success=Ye successfully exported to {0}
luckperms.command.export.file-unexpected-error-writing=Arr\! An unexpected error occurred while writing to th'' file
luckperms.command.export.web.export-code=Export code
luckperms.command.export.web.import-command-description=Use th'' following command to import
luckperms.command.import.term=Import
luckperms.command.import.error-term=Arr\! Some sailor messed up
luckperms.command.import.already-running=Arr\! Another import process is already running
luckperms.command.import.file.doesnt-exist=Yer had too much rum; File {0} don''t exist
luckperms.command.import.file.not-readable=File {0} ain''t readable
luckperms.command.import.file.unexpected-error-reading=Arr\! An unexpected error occurred while reading from th'' import file
luckperms.command.import.file.correct-format=is it th'' correct format?
luckperms.command.import.web.unable-to-read=Arr\! Unable to read the data using th'' given code
luckperms.command.import.progress.percent=Th'' journey is {0}% complete
luckperms.command.import.progress.operations={0}/{1} of th'' sails unfurled
luckperms.command.import.starting=Th'' anchor has been raised on the import process
luckperms.command.import.completed=COMPLETED
luckperms.command.import.duration=took {0} seconds
luckperms.command.bulkupdate.must-use-console=Th'' bulk update command can only be used in th'' console
luckperms.command.bulkupdate.invalid-data-type=Arr\! That be an invalid type matey\! LP be expecting {0}
luckperms.command.bulkupdate.invalid-constraint=Invalid constraint {0}, matey
luckperms.command.bulkupdate.invalid-constraint-format=Constraints should be in th'' format {0}
luckperms.command.bulkupdate.invalid-comparison=Invalid comparison operator {0}, what be you doing mate
luckperms.command.bulkupdate.invalid-comparison-format=Expected one of th'' following\: {0}
luckperms.command.bulkupdate.queued=Bulk update operation was queued
luckperms.command.bulkupdate.confirm=Run {0} to execute th'' update
luckperms.command.bulkupdate.unknown-id=Operation with id {0} does not be existing or has expired
luckperms.command.bulkupdate.starting=Runnin'' bulk update
luckperms.command.bulkupdate.success=Arrr\! Yar bulk update completed successfully
luckperms.command.bulkupdate.success.statistics.nodes=Total affected nodes
luckperms.command.bulkupdate.success.statistics.users=Total affected users
luckperms.command.bulkupdate.success.statistics.groups=Total affected groups
luckperms.command.bulkupdate.failure=Arr\! Bulk update failed, check th'' console for errors
luckperms.command.update-task.request=An update task has been requested, slow ''ur ship down matey
luckperms.command.update-task.complete=Update task has been completed
luckperms.command.update-task.push.attempting=Now attempting to send th'' info to other servers
luckperms.command.update-task.push.complete=Other servers were notified via {0} successfully
luckperms.command.update-task.push.error=Error whilst pushing th'' changes to other servers
luckperms.command.update-task.push.error-not-setup=Can''t push changes to yer other servers as yer messaging service has not been configured
luckperms.command.reload-config.success=Th'' configuration file was reloaded
luckperms.command.reload-config.restart-note=some options will only apply after th'' server has restarted
luckperms.command.translations.searching=Searching for th'' available translations, slow ''ur ship matey...
luckperms.command.translations.searching-error=Unable to obtain a list of available translations, ''ur stuck with pirate speak matey
luckperms.command.translations.installed-translations=Installed Translations
luckperms.command.translations.available-translations=Available Translations
luckperms.command.translations.percent-translated={0}% translated
luckperms.command.translations.translations-by=by
luckperms.command.translations.installing=Installing translations, slow ''ur ship down matey...
luckperms.command.translations.download-error=Unable to download th'' translation for {0}
luckperms.command.translations.installing-specific=Installing th'' language {0}...
luckperms.command.translations.install-complete=Installation complete
luckperms.command.translations.download-prompt=Use {0} to download and install th'' more recent versions of th'' translations provided by ''ur fellow pirates
luckperms.command.translations.download-override-warning=Note that this will override any changes you''ve made for these languages
luckperms.usage.user.description=A set of commands for managing crewmates within LuckPerms. (A ''user'' in LuckPerms is just a player, and can refer to a UUID or username)
luckperms.usage.group.description=A set of commands for managing groups with LuckPerms. Groups are just collections of permission assignments that can be given to mateys. New groups are made using th'' ''creategroup'' command.
luckperms.usage.track.description=A set of commands for managing tracks within LuckPerms. Tracks are an ordered collection of some groups which can be used for defining promotions and demotions.
luckperms.usage.log.description=A set of commands for managing th'' logging functionality within LuckPerms.
luckperms.usage.sync.description=Reloads all data from th'' plugins storage into memory and applies any changes that are noticed.
luckperms.usage.info.description=Prints general information about th'' active plugin instance.
luckperms.usage.editor.description=Creates a new web editor session
luckperms.usage.editor.argument.type=th'' types to load into th'' editor. (''all'', ''users'' or ''groups'')
luckperms.usage.editor.argument.filter=permission to be filtering user entries through
luckperms.usage.verbose.description=Controls th'' plugin''s verbose permission check system.
luckperms.usage.verbose.argument.action=whether to enable/disable logging or to upload th'' logged output
luckperms.usage.verbose.argument.filter=th'' filter to match entries against
luckperms.usage.verbose.argument.commandas=th'' player/command to be runnin''
luckperms.usage.tree.description=Generates a tree view (ordered list hierarchy) of all th'' permissions known by LuckPerms.
luckperms.usage.tree.argument.scope=th'' root of the tree. specify "." to include all th'' permissions
luckperms.usage.tree.argument.player=th'' name of an online matey to be checkin'' against
luckperms.usage.search.description=Looks far an'' wide for th'' users/groups wit'' a specific permis''ion
luckperms.usage.search.argument.permission=th'' permission to be lookin'' out for
luckperms.usage.search.argument.page=th'' parchment number to view
luckperms.usage.network-sync.description=Sync changes with th'' storage and request that all other servers on th'' network follow th'' order
luckperms.usage.import.description=Imports data from a (prreviously created) export file
luckperms.usage.import.argument.file=th'' file to be importing from
luckperms.usage.import.argument.replace=replace th'' existing data instead of merging
luckperms.usage.import.argument.upload=upload th'' data from ye previous export
luckperms.usage.export.description=Exports all th'' permissions data to an ''export'' file that can be imported at a later time.
luckperms.usage.export.argument.file=th'' file to be exporting to
luckperms.usage.export.argument.without-users=exclude users from th'' export
luckperms.usage.export.argument.without-groups=exclude users from th'' export
luckperms.usage.export.argument.upload=Upload all ye permission data t'' th'' webeditor. May be re-imported at a later time.
luckperms.usage.reload-config.description=Reload some of th'' config options
luckperms.usage.bulk-update.description=Execute bulk change queries on all th'' data
luckperms.usage.bulk-update.argument.data-type=th'' type of data being changed. (''all'', ''users'', or ''groups'')
luckperms.usage.bulk-update.argument.action=th'' action to be performing on th'' data. (''update'' or ''delete'')
luckperms.usage.bulk-update.argument.action-field=th'' field to be acting upon. only required for ''update''. (''permission'', ''server'', or ''world'')
luckperms.usage.bulk-update.argument.action-value=th'' value to be replacing with. only required for ''update''.
luckperms.usage.bulk-update.argument.constraint=th'' constraints to be following for th'' update
luckperms.usage.translations.description=Manage th'' translations
luckperms.usage.translations.argument.install=subcommand to install th'' translations
luckperms.usage.apply-edits.description=Applies permission changes made from th'' editor
luckperms.usage.apply-edits.argument.code=th'' unique code for th'' data
luckperms.usage.apply-edits.argument.target=who to be applying th'' data to
luckperms.usage.create-group.description=Create a new group
luckperms.usage.create-group.argument.name=th'' name of th'' group
luckperms.usage.create-group.argument.weight=th'' booty size of th'' crew
luckperms.usage.create-group.argument.display-name=th'' voyage of th'' crew
luckperms.usage.delete-group.description=Delete a group
luckperms.usage.delete-group.argument.name=th'' name of th'' group
luckperms.usage.list-groups.description=List all th'' groups on th'' platform
luckperms.usage.create-track.description=Create a new track
luckperms.usage.create-track.argument.name=th'' name of th'' track
luckperms.usage.delete-track.description=Delete a track
luckperms.usage.delete-track.argument.name=th'' name of th'' track
luckperms.usage.list-tracks.description=List all th'' tracks on th'' platform
luckperms.usage.user-info.description=Shows info ''bout th'' user
luckperms.usage.user-switchprimarygroup.description=Switches th'' user''s primary group
luckperms.usage.user-switchprimarygroup.argument.group=th'' group to switch to
luckperms.usage.user-promote.description=Promotes th'' user up a track
luckperms.usage.user-promote.argument.track=th'' track to be promoting th'' user up
luckperms.usage.user-promote.argument.context=th'' contexts to promote th'' prefix in
luckperms.usage.user-promote.argument.dont-add-to-first=only promote th'' user if they''re already on th'' track
luckperms.usage.user-demote.description=Demotes th'' user down yer track
luckperms.usage.user-demote.argument.track=tr'' track to be demoting th'' user down
luckperms.usage.user-demote.argument.context=th'' contexts to demote th'' user in
luckperms.usage.user-demote.argument.dont-remove-from-first=prevent th'' user from being removed from yer first group
luckperms.usage.user-clone.description=Clone th'' user
luckperms.usage.user-clone.argument.user=th'' name/uuid of th'' user to clone onto
luckperms.usage.group-info.description=Gives info about ye'' group
luckperms.usage.group-listmembers.description=Show the crewmates/groups tha'' inherit from th'' group
luckperms.usage.group-listmembers.argument.page=th'' parchment number to view
luckperms.usage.group-setweight.description=Sets th'' groups weight
luckperms.usage.group-setweight.argument.weight=th'' weight to set
luckperms.usage.group-set-display-name.description=Set th'' group''s display name
luckperms.usage.group-set-display-name.argument.name=th'' name to set
luckperms.usage.group-set-display-name.argument.context=th'' context to set th'' name in
luckperms.usage.group-rename.description=Rename yer group
luckperms.usage.group-rename.argument.name=th'' shiny new name
luckperms.usage.group-clone.description=Create a clone of th'' group
luckperms.usage.group-clone.argument.name=th'' name of th'' group to clone onto
luckperms.usage.holder-editor.description=Open th'' web editor
luckperms.usage.holder-showtracks.description=Lists th'' tracks that thy object be on
luckperms.usage.holder-clear.description=Walks all th'' permissions, parents, and meta off the plank
luckperms.usage.holder-clear.argument.context=th'' contexts to filter through
luckperms.usage.permission.description=Edit permissions
luckperms.usage.parent.description=Edit th'' inheritances
luckperms.usage.meta.description=Edit th'' metadata values
luckperms.usage.permission-info.description=Lists the permissions nodes th'' object has
luckperms.usage.permission-info.argument.page=th'' parchment number to view
luckperms.usage.permission-info.argument.sort-mode=how to sort th'' entries
luckperms.usage.permission-set.description=Sets a permission for th'' object
luckperms.usage.permission-set.argument.node=th'' permission node to set
luckperms.usage.permission-set.argument.value=th'' value of th'' node
luckperms.usage.permission-set.argument.context=th'' contexts to add th'' permission in
luckperms.usage.permission-unset.description=Wipes a permission for an object of th'' deck
luckperms.usage.permission-unset.argument.node=th'' permission node to unset
luckperms.usage.permission-unset.argument.context=th'' contexts to remove th'' permission in
luckperms.usage.permission-settemp.description=Sets a permission for th'' object temporarily
luckperms.usage.permission-settemp.argument.node=th'' permission node to set
luckperms.usage.permission-settemp.argument.value=th'' value of th'' node
luckperms.usage.permission-settemp.argument.duration=th'' duration till the permission node expires
luckperms.usage.permission-settemp.argument.temporary-modifier=how th'' temporary permission should be applied
luckperms.usage.permission-settemp.argument.context=th'' contexts to add th'' permission in
luckperms.usage.permission-unsettemp.description=Wipes a temporary permission for an object off th'' deck
luckperms.usage.permission-unsettemp.argument.node=th'' permission node to unset
luckperms.usage.permission-unsettemp.argument.duration=th'' duration to subtract
luckperms.usage.permission-unsettemp.argument.context=th'' contexts to remove th'' permission in
luckperms.usage.permission-check.description=Checks to see if the object be havin'' a certain permission node
luckperms.usage.permission-check.argument.node=th'' permission node to be lookin'' out for
luckperms.usage.permission-clear.description=Walks the permissions off the plank
luckperms.usage.permission-clear.argument.context=th'' contexts to filter through
luckperms.usage.parent-info.description=Lists th'' groups that thy object inherits from
luckperms.usage.parent-info.argument.page=th'' parchment number to view
luckperms.usage.parent-info.argument.sort-mode=how to sort th'' entries
luckperms.usage.parent-set.description=Rremoves all other groups th'' object inherits already and adds them to th'' one given
luckperms.usage.parent-set.argument.group=th'' group to set to
luckperms.usage.parent-set.argument.context=th'' contexts to set th'' group in
luckperms.usage.parent-add.description=Sets another group for th'' object to be inheriting permissions from
luckperms.usage.parent-add.argument.group=th'' group to be inheriting from
luckperms.usage.parent-add.argument.context=th'' contexts to inherit th'' group in
luckperms.usage.parent-remove.description=Rremoves a previously set inheritance law
luckperms.usage.parent-remove.argument.group=th'' group to plank
luckperms.usage.parent-remove.argument.context=th'' contexts to plank th'' group in
luckperms.usage.parent-set-track.description=Rremoves all other groups th'' object inherits already and adds them to th'' one given
luckperms.usage.parent-set-track.argument.track=th'' track to set on
luckperms.usage.parent-set-track.argument.group=th'' group to set to or a number that be relating to the position of the group on th'' given track
luckperms.usage.parent-set-track.argument.context=th'' contexts to set th'' group in
luckperms.usage.parent-add-temp.description=Sets another group for th'' object to be inheriting permissions from temporarily
luckperms.usage.parent-add-temp.argument.group=th'' group to be inheriting from
luckperms.usage.parent-add-temp.argument.duration=th'' duration of th'' group membership
luckperms.usage.parent-add-temp.argument.temporary-modifier=how th'' temporary permission should be applied
luckperms.usage.parent-add-temp.argument.context=th'' contexts to be inheriting th'' group in
luckperms.usage.parent-remove-temp.description=Rremoves a previously set temporary inheritance law
luckperms.usage.parent-remove-temp.argument.group=th'' group to plank
luckperms.usage.parent-remove-temp.argument.duration=th'' duration to subtract
luckperms.usage.parent-remove-temp.argument.context=th'' contexts to plank th'' group in
luckperms.usage.parent-clear.description=Wipe th'' deck of all parents
luckperms.usage.parent-clear.argument.context=th'' contexts to filter through
luckperms.usage.parent-clear-track.description=Walks all th'' parents on th'' given track off th'' plank
luckperms.usage.parent-clear-track.argument.track=th'' track to remove on
luckperms.usage.parent-clear-track.argument.context=th'' contexts to filter through
luckperms.usage.meta-info.description=Shows all th'' chat meta
luckperms.usage.meta-set.description=Sets th'' meta value
luckperms.usage.meta-set.argument.key=th'' key to set
luckperms.usage.meta-set.argument.value=th'' value to set
luckperms.usage.meta-set.argument.context=th'' contexts to add th'' meta pair in
luckperms.usage.meta-unset.description=Unsets th'' meta value
luckperms.usage.meta-unset.argument.key=th'' key to unset
luckperms.usage.meta-unset.argument.context=th'' contexts to remove th'' meta pair ''n
luckperms.usage.meta-settemp.description=Sets th'' meta value temporarily
luckperms.usage.meta-settemp.argument.key=th'' key to set
luckperms.usage.meta-settemp.argument.value=th'' value to set
luckperms.usage.meta-settemp.argument.duration=th'' duration till the meta value expires
luckperms.usage.meta-settemp.argument.context=th'' contexts to add th'' meta pair in
luckperms.usage.meta-unsettemp.description=Walks a temporary meta value off th'' plank
luckperms.usage.meta-unsettemp.argument.key=th'' key to unset
luckperms.usage.meta-unsettemp.argument.context=th'' contexts to remove th'' meta pair ''n
luckperms.usage.meta-addprefix.description=Adds th'' prefix
luckperms.usage.meta-addprefix.argument.priority=th'' priority to add th'' prefix
luckperms.usage.meta-addprefix.argument.prefix=th'' prefix string
luckperms.usage.meta-addprefix.argument.context=th'' contexts to add th'' prefix ''n
luckperms.usage.meta-addsuffix.description=Adds th'' prefix
luckperms.usage.meta-addsuffix.argument.priority=th'' priority to add th'' suffix
luckperms.usage.meta-addsuffix.argument.suffix=th'' suffix string
luckperms.usage.meta-addsuffix.argument.context=th'' contexts to add th'' suffix ''n
luckperms.usage.meta-setprefix.description=Sets th'' prefix
luckperms.usage.meta-setprefix.argument.priority=th'' priority to set th'' prefix
luckperms.usage.meta-setprefix.argument.prefix=th'' prefix string
luckperms.usage.meta-setprefix.argument.context=th'' context to set th'' prefix in
luckperms.usage.meta-setsuffix.description=Sets th'' suffix
luckperms.usage.meta-setsuffix.argument.priority=th'' priority to set th'' suffix
luckperms.usage.meta-setsuffix.argument.suffix=th'' suffix string
luckperms.usage.meta-setsuffix.argument.context=th'' contexts to set th'' suffix ''n
luckperms.usage.meta-removeprefix.description=Walks a prefix off the plank
luckperms.usage.meta-removeprefix.argument.priority=th'' priority to forget th'' prefix
luckperms.usage.meta-removeprefix.argument.prefix=th'' prefix string
luckperms.usage.meta-removeprefix.argument.context=th'' contexts to remove th'' prefix in
luckperms.usage.meta-removesuffix.description=Walks a suffix off the plank
luckperms.usage.meta-removesuffix.argument.priority=th'' priority to forget th'' suffix
luckperms.usage.meta-removesuffix.argument.suffix=th'' suffix string
luckperms.usage.meta-removesuffix.argument.context=th'' contexts to remove th'' suffix in
luckperms.usage.meta-addtemp-prefix.description=Adds a prrrefix temporarily
luckperms.usage.meta-addtemp-prefix.argument.priority=th'' priority to add th'' prefix
luckperms.usage.meta-addtemp-prefix.argument.prefix=th'' prefix string
luckperms.usage.meta-addtemp-prefix.argument.duration=th'' duration till the prefix expires
luckperms.usage.meta-addtemp-prefix.argument.context=th'' contexts to add th'' prefix in
luckperms.usage.meta-addtemp-suffix.description=Adds a suffix temporarily
luckperms.usage.meta-addtemp-suffix.argument.priority=th'' priority to add th'' suffix
luckperms.usage.meta-addtemp-suffix.argument.suffix=th'' suffix string
luckperms.usage.meta-addtemp-suffix.argument.duration=th'' duration till the suffix expires
luckperms.usage.meta-addtemp-suffix.argument.context=th'' contexts to add th'' suffix ''n
luckperms.usage.meta-settemp-prefix.description=Sets a prrrefix temporarily
luckperms.usage.meta-settemp-prefix.argument.priority=th'' priority to set th'' prefix
luckperms.usage.meta-settemp-prefix.argument.prefix=th'' prefix string
luckperms.usage.meta-settemp-prefix.argument.duration=th'' duration till the prefix expires
luckperms.usage.meta-settemp-prefix.argument.context=th'' context to set th'' prefix in
luckperms.usage.meta-settemp-suffix.description=Sets a suffix temporarily
luckperms.usage.meta-settemp-suffix.argument.priority=th'' priority to set th'' suffix
luckperms.usage.meta-settemp-suffix.argument.suffix=th'' suffix string
luckperms.usage.meta-settemp-suffix.argument.duration=th'' duration till the suffix expires
luckperms.usage.meta-settemp-suffix.argument.context=th'' contexts to set th'' suffix ''n
luckperms.usage.meta-removetemp-prefix.description=Rrremoves a temporrrary prefix
luckperms.usage.meta-removetemp-prefix.argument.priority=th'' priority to forget th'' prefix
luckperms.usage.meta-removetemp-prefix.argument.prefix=th'' prefix sting
luckperms.usage.meta-removetemp-prefix.argument.context=th'' contexts to remove th'' prefix in
luckperms.usage.meta-removetemp-suffix.description=Rrremoves a temporrrary suffix
luckperms.usage.meta-removetemp-suffix.argument.priority=th'' priority to forget th'' suffix
luckperms.usage.meta-removetemp-suffix.argument.suffix=th'' suffix sting
luckperms.usage.meta-removetemp-suffix.argument.context=th'' contexts to remove th'' suffix in
luckperms.usage.meta-clear.description=Wipe th'' deck of all meta
luckperms.usage.meta-clear.argument.type=th'' type of meta to rrremove
luckperms.usage.meta-clear.argument.context=th'' contexts to filter through
luckperms.usage.track-info.description=Gives info about ye'' track
luckperms.usage.track-editor.description=Opens ye'' ol'' web permis''ion edit''r
luckperms.usage.track-append.description=Tacks on a group to the end of ye'' track
luckperms.usage.track-append.argument.group=th'' group to append
luckperms.usage.track-insert.description=Inserts a group in some location along ye'' track
luckperms.usage.track-insert.argument.group=th'' group to insert
luckperms.usage.track-insert.argument.position=th'' position to insert th'' group (th'' first position is 1)
luckperms.usage.track-remove.description=Plank a group from th'' track
luckperms.usage.track-remove.argument.group=th'' group to plank
luckperms.usage.track-clear.description=Wipes the crews from th'' track
luckperms.usage.track-rename.description=Rename th'' track
luckperms.usage.track-rename.argument.name=th'' shiny new name
luckperms.usage.track-clone.description=Create a clone of th'' track
luckperms.usage.track-clone.argument.name=th'' name of th'' track to clone onto
luckperms.usage.log-recent.description=Rifle through th'' ship''s logs
luckperms.usage.log-recent.argument.user=th'' name/uuid of th'' user to filter through
luckperms.usage.log-recent.argument.page=the parchment number to be lookin'' out f''r
luckperms.usage.log-search.description=Look through th'' ship''s logs f''r an entry
luckperms.usage.log-search.argument.query=th'' query to search by
luckperms.usage.log-search.argument.page=the parchment number to be lookin'' out f''r
luckperms.usage.log-notify.description=Toggle the bell f''r the ship''s logs
luckperms.usage.log-notify.argument.toggle=whether to raise or lower the sails on this
luckperms.usage.log-user-history.description=Read a mate''s ship-logs
luckperms.usage.log-user-history.argument.user=th'' name/uuid of th'' user
luckperms.usage.log-user-history.argument.page=th'' parchment number to be lookin'' out f''r
luckperms.usage.log-group-history.description=View a crew''s ship-logs
luckperms.usage.log-group-history.argument.group=th'' name of the crew
luckperms.usage.log-group-history.argument.page=the parchment number to be lookin'' out f''r
luckperms.usage.log-track-history.description=View a track''s ship-logs
luckperms.usage.log-track-history.argument.track=th'' name of th'' track
luckperms.usage.log-track-history.argument.page=the parchment number to be lookin'' out f''r
luckperms.usage.sponge.description=Rewrite th'' Sponge writings
luckperms.usage.sponge.argument.collection=th'' collection to flip through
luckperms.usage.sponge.argument.subject=th'' subject to modify
luckperms.usage.sponge-permission-info.description=Shows info ''bout th'' subject''s permissions
luckperms.usage.sponge-permission-info.argument.contexts=th'' contexts to filter through
luckperms.usage.sponge-permission-set.description=Sets a permission for the Subject
luckperms.usage.sponge-permission-set.argument.node=th'' permission node to set
luckperms.usage.sponge-permission-set.argument.tristate=th'' value to set the permission to
luckperms.usage.sponge-permission-set.argument.contexts=th'' context to set th'' permission in
luckperms.usage.sponge-permission-clear.description=Walk the Subject''s permissions off th'' plank
luckperms.usage.sponge-permission-clear.argument.contexts=th'' contexts to clear th'' permissions in
luckperms.usage.sponge-parent-info.description=Shows info ''bout th'' subject''s parents
luckperms.usage.sponge-parent-info.argument.contexts=th'' contexts to filter through
luckperms.usage.sponge-parent-add.description=Add a parent to th'' Subject
luckperms.usage.sponge-parent-add.argument.collection=th'' subject collection where th'' parent Subject is
luckperms.usage.sponge-parent-add.argument.subject=th'' name of th'' parent Subject
luckperms.usage.sponge-parent-add.argument.contexts=th'' contexts to add th'' parent ''n
luckperms.usage.sponge-parent-remove.description=Rid th'' parent from the Subject
luckperms.usage.sponge-parent-remove.argument.collection=th'' subject collection where th'' parent Subject be
luckperms.usage.sponge-parent-remove.argument.subject=th'' name of th'' parent Subject
luckperms.usage.sponge-parent-remove.argument.contexts=th'' contexts to remove th'' parent in
luckperms.usage.sponge-parent-clear.description=Walk the Subject''s parents off th'' plank
luckperms.usage.sponge-parent-clear.argument.contexts=th'' contexts to clear th'' parents in
luckperms.usage.sponge-option-info.description=Shows info ''bout th'' subject''s options
luckperms.usage.sponge-option-info.argument.contexts=th'' contexts to filter through
luckperms.usage.sponge-option-set.description=Sets an option for th'' Subject
luckperms.usage.sponge-option-set.argument.key=th'' key to set
luckperms.usage.sponge-option-set.argument.value=th'' value to set the key to
luckperms.usage.sponge-option-set.argument.contexts=th'' context to set th'' option in
luckperms.usage.sponge-option-unset.description=Wipes an option for the Subject off th'' deck
luckperms.usage.sponge-option-unset.argument.key=th'' key to unset
luckperms.usage.sponge-option-unset.argument.contexts=th'' contexts to unset th'' key in
luckperms.usage.sponge-option-clear.description=Walk the Subject''s options off th'' plank
luckperms.usage.sponge-option-clear.argument.contexts=th'' contexts to clear th'' option in
