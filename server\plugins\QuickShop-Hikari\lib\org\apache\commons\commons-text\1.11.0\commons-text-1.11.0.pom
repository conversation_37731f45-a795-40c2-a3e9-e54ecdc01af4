<?xml version="1.0" encoding="UTF-8"?>
<!--
 Licensed to the Apache Software Foundation (ASF) under one or more
 contributor license agreements.  See the NOTICE file distributed with
 this work for additional information regarding copyright ownership.
 The ASF licenses this file to You under the Apache License, Version 2.0
 (the "License"); you may not use this file except in compliance with
 the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing, software
 distributed under the License is distributed on an "AS IS" BASIS,
 WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 See the License for the specific language governing permissions and
 limitations under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>org.apache.commons</groupId>
    <artifactId>commons-parent</artifactId>
    <version>64</version>
  </parent>
  <artifactId>commons-text</artifactId>
  <version>1.11.0</version>
  <name>Apache Commons Text</name>
  <description>Apache Commons Text is a library focused on algorithms working on strings.</description>
  <url>https://commons.apache.org/proper/commons-text</url>

  <properties>
    <project.build.sourceEncoding>ISO-8859-1</project.build.sourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <maven.compiler.source>1.8</maven.compiler.source>
    <maven.compiler.target>1.8</maven.compiler.target>

    <commons.componentid>text</commons.componentid>
    <commons.packageId>text</commons.packageId>
    <commons.module.name>org.apache.commons.text</commons.module.name>

    <commons.release.version>1.11.0</commons.release.version>
    <commons.release.next>1.11.1</commons.release.next>
    <commons.release.desc>(Java 8+)</commons.release.desc>

    <commons.jira.id>TEXT</commons.jira.id>
    <commons.jira.pid>12318221</commons.jira.pid>

    <commons.site.path>text</commons.site.path>
    <commons.scmPubUrl>https://svn.apache.org/repos/infra/websites/production/commons/content/proper/commons-text</commons.scmPubUrl>
    <commons.scmPubCheckoutDirectory>site-content</commons.scmPubCheckoutDirectory>

    <commons.bytebuddy.version>1.14.9</commons.bytebuddy.version>
    <commons.mockito.version>4.11.0</commons.mockito.version>

    <!-- 22.1.0 requires Java 11 -->
    <graalvm.version>********</graalvm.version>
    <commons.rng.version>1.5</commons.rng.version>

    <japicmp.skip>false</japicmp.skip>

    <jmh.version>1.37</jmh.version>

    <!-- Commons Release Plugin -->
    <!-- Previous version of the component (used for reporting binary compatibility check)-->
    <commons.bc.version>1.10.0</commons.bc.version>
    <commons.rc.version>RC1</commons.rc.version>
    <commons.release.isDistModule>true</commons.release.isDistModule>
    <commons.distSvnStagingUrl>scm:svn:https://dist.apache.org/repos/dist/dev/commons/${commons.componentid}</commons.distSvnStagingUrl>
    <!--
      Define the following in ~/.m2/settings.xml in an active profile:
      (or provide them on the command line)
      commons.releaseManagerName
      commons.releaseManagerKey
    -->
  </properties>

  <dependencies>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-lang3</artifactId>
      <version>3.13.0</version>
    </dependency>
    <!-- testing -->
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <!-- Java 21 support, revisit for Mockito 5 -->
      <groupId>net.bytebuddy</groupId>
      <artifactId>byte-buddy</artifactId>
      <version>${commons.bytebuddy.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <!-- Java 21 support, revisit for Mockito 5 -->
      <groupId>net.bytebuddy</groupId>
      <artifactId>byte-buddy-agent</artifactId>
      <version>${commons.bytebuddy.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.assertj</groupId>
      <artifactId>assertj-core</artifactId>
      <version>3.24.2</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>commons-io</groupId>
      <artifactId>commons-io</artifactId>
      <version>2.14.0</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <!--  Use mockito-inline instead of mockito-core to mock and spy on final classes. -->
      <artifactId>mockito-inline</artifactId>
      <version>${commons.mockito.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.graalvm.js</groupId>
      <artifactId>js</artifactId>
      <version>${graalvm.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.graalvm.js</groupId>
      <artifactId>js-scriptengine</artifactId>
      <version>${graalvm.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-rng-simple</artifactId>
      <version>${commons.rng.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.openjdk.jmh</groupId>
      <artifactId>jmh-core</artifactId>
      <version>${jmh.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.openjdk.jmh</groupId>
      <artifactId>jmh-generator-annprocess</artifactId>
      <version>${jmh.version}</version>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <defaultGoal>clean verify apache-rat:check japicmp:cmp checkstyle:check spotbugs:check javadoc:javadoc</defaultGoal>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.apache.rat</groupId>
          <artifactId>apache-rat-plugin</artifactId>
          <configuration>
            <excludes>
              <exclude>site-content/**</exclude>
              <exclude>src/site/resources/download_lang.cgi</exclude>
              <exclude>src/test/resources/org/apache/commons/text/stringEscapeUtilsTestData.txt</exclude>
              <exclude>src/test/resources/org/apache/commons/text/lcs-perf-analysis-inputs.csv</exclude>
              <exclude>src/site/resources/release-notes/RELEASE-NOTES-*.txt</exclude>
            </excludes>
          </configuration>
        </plugin><!-- override skip property of parent pom -->
        <plugin>
          <artifactId>maven-pmd-plugin</artifactId>
          <version>${commons.pmd.version}</version>
          <configuration>
            <targetJdk>${maven.compiler.target}</targetJdk>
          </configuration>
        </plugin>
      </plugins>
    </pluginManagement>
    <plugins>
      <plugin>
        <artifactId>maven-checkstyle-plugin</artifactId>
        <configuration>
          <enableRulesSummary>false</enableRulesSummary>
          <configLocation>src/conf/checkstyle.xml</configLocation>
          <headerLocation>src/conf/checkstyle-header.txt</headerLocation>
          <suppressionsLocation>src/conf/checkstyle-suppressions.xml</suppressionsLocation>
          <suppressionsFileExpression>src/conf/checkstyle-suppressions.xml</suppressionsFileExpression>
          <includeTestSourceDirectory>true</includeTestSourceDirectory>
          <excludes>**/generated/**.java,**/jmh_generated/**.java</excludes>
        </configuration>
      </plugin>
      <plugin>
        <groupId>com.github.spotbugs</groupId>
        <artifactId>spotbugs-maven-plugin</artifactId>
        <configuration>
          <excludeFilterFile>src/conf/spotbugs-exclude-filter.xml</excludeFilterFile>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-assembly-plugin</artifactId>
        <configuration>
          <descriptors>
            <descriptor>src/assembly/bin.xml</descriptor>
            <descriptor>src/assembly/src.xml</descriptor>
          </descriptors>
          <tarLongFileMode>gnu</tarLongFileMode>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jar-plugin</artifactId>
        <executions>
          <execution>
            <goals>
              <goal>test-jar</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <archive combine.children="append">
            <manifestEntries>
              <Automatic-Module-Name>${commons.module.name}</Automatic-Module-Name>
            </manifestEntries>
          </archive>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-scm-publish-plugin</artifactId>
        <configuration>
          <ignorePathsToDelete>
            <ignorePathToDelete>javadocs</ignorePathToDelete>
          </ignorePathsToDelete>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-javadoc-plugin</artifactId>
        <configuration>
          <source>${maven.compiler.source}</source>
        </configuration>
      </plugin>
    </plugins>
  </build>

  <reporting>
    <plugins>
      <plugin>
        <artifactId>maven-checkstyle-plugin</artifactId>
        <configuration>
          <enableRulesSummary>false</enableRulesSummary>
          <configLocation>src/conf/checkstyle.xml</configLocation>
          <headerLocation>src/conf/checkstyle-header.txt</headerLocation>
          <suppressionsLocation>src/conf/checkstyle-suppressions.xml</suppressionsLocation>
          <suppressionsFileExpression>src/conf/checkstyle-suppressions.xml</suppressionsFileExpression>
          <includeTestSourceDirectory>true</includeTestSourceDirectory>
          <excludes>**/generated/**.java,**/jmh_generated/**.java</excludes>
        </configuration>
        <reportSets>
          <reportSet>
            <reports>
              <report>checkstyle</report>
            </reports>
          </reportSet>
        </reportSets>
      </plugin>
      <!-- Requires setting 'export MAVEN_OPTS="-Xmx512m" ' -->
      <plugin>
        <groupId>com.github.spotbugs</groupId>
        <artifactId>spotbugs-maven-plugin</artifactId>
        <configuration>
          <excludeFilterFile>src/conf/spotbugs-exclude-filter.xml</excludeFilterFile>
        </configuration>
      </plugin>
      <plugin>
        <groupId>com.github.siom79.japicmp</groupId>
        <artifactId>japicmp-maven-plugin</artifactId>
      </plugin>
      <plugin>
        <artifactId>maven-pmd-plugin</artifactId>
        <configuration>
          <targetJdk>${maven.compiler.target}</targetJdk>
        </configuration>
        <reportSets>
          <reportSet>
            <reports>
              <report>pmd</report>
              <report>cpd</report>
            </reports>
          </reportSet>
        </reportSets>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>taglist-maven-plugin</artifactId>
        <version>3.0.0</version>
        <configuration>
          <tagListOptions>
            <tagClasses>
              <tagClass>
                <displayName>Needs Work</displayName>
                <tags>
                  <tag>
                    <matchString>TODO</matchString>
                    <matchType>exact</matchType>
                  </tag>
                  <tag>
                    <matchString>FIXME</matchString>
                    <matchType>exact</matchType>
                  </tag>
                  <tag>
                    <matchString>XXX</matchString>
                    <matchType>exact</matchType>
                  </tag>
                </tags>
              </tagClass>
              <tagClass>
                <displayName>Noteable Markers</displayName>
                <tags>
                  <tag>
                    <matchString>NOTE</matchString>
                    <matchType>exact</matchType>
                  </tag>
                  <tag>
                    <matchString>NOPMD</matchString>
                    <matchType>exact</matchType>
                  </tag>
                  <tag>
                    <matchString>NOSONAR</matchString>
                    <matchType>exact</matchType>
                  </tag>
                </tags>
              </tagClass>
            </tagClasses>
          </tagListOptions>
        </configuration>
      </plugin>
    </plugins>
  </reporting>

  <inceptionYear>2014</inceptionYear>

  <developers>
    <developer>
      <id>kinow</id>
      <name>Bruno P. Kinoshita</name>
      <email><EMAIL></email>
    </developer>
    <developer>
      <id>britter</id>
      <name>Benedikt Ritter</name>
      <email><EMAIL></email>
    </developer>
    <developer>
      <id>chtompki</id>
      <name>Rob Tompkins</name>
      <email><EMAIL></email>
    </developer>
    <developer>
      <id>ggregory</id>
      <name>Gary Gregory</name>
      <email>ggregory at apache.org</email>
      <url>https://www.garygregory.com</url>
      <organization>The Apache Software Foundation</organization>
      <organizationUrl>https://www.apache.org/</organizationUrl>
      <roles>
        <role>PMC Member</role>
      </roles>
      <timezone>America/New_York</timezone>
      <properties>
        <picUrl>https://people.apache.org/~ggregory/img/garydgregory80.png</picUrl>
      </properties>
    </developer>
    <developer>
      <id>djones</id>
      <name>Duncan Jones</name>
      <email><EMAIL></email>
    </developer>
  </developers>

  <contributors>
    <contributor>
      <name>Don Jeba</name>
      <email><EMAIL></email>
    </contributor>
    <contributor>
      <name>Sampanna Kahu</name>
    </contributor>
    <contributor>
      <name>Jarek Strzelecki</name>
    </contributor>
    <contributor>
      <name>Lee Adcock</name>
    </contributor>
    <contributor>
      <name>Amey Jadiye</name>
      <email><EMAIL></email>
    </contributor>
    <contributor>
      <name>Arun Vinud S S</name>
    </contributor>
    <contributor>
      <name>Ioannis Sermetziadis</name>
    </contributor>
    <contributor>
      <name>Jostein Tveit</name>
    </contributor>
    <contributor>
      <name>Luciano Medallia</name>
    </contributor>
    <contributor>
      <name>Jan Martin Keil</name>
    </contributor>
    <contributor>
      <name>Nandor Kollar</name>
    </contributor>
    <contributor>
      <name>Nick Wong</name>
    </contributor>
    <contributor>
      <name>Ali Ghanbari</name>
      <url>https://ali-ghanbari.github.io/</url>
    </contributor>
  </contributors>

  <scm>
    <connection>scm:git:https://gitbox.apache.org/repos/asf/commons-text</connection>
    <developerConnection>scm:git:https://gitbox.apache.org/repos/asf/commons-text</developerConnection>
    <url>https://gitbox.apache.org/repos/asf?p=commons-text.git</url>
  </scm>

  <issueManagement>
    <system>jira</system>
    <url>https://issues.apache.org/jira/browse/TEXT</url>
  </issueManagement>

  <distributionManagement>
    <site>
      <id>apache.website</id>
      <name>Apache Commons Site</name>
      <url>scm:svn:https://svn.apache.org/repos/infra/websites/production/commons/content/proper/commons-text/</url>
    </site>
  </distributionManagement>

  <profiles>
    <profile>
      <id>setup-checkout</id>
      <activation>
        <file>
          <missing>site-content</missing>
        </file>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-antrun-plugin</artifactId>
            <executions>
              <execution>
                <id>prepare-checkout</id>
                <goals>
                  <goal>run</goal>
                </goals>
                <phase>pre-site</phase>
                <configuration>
                  <target>
                    <exec executable="svn">
                      <arg line="checkout --depth immediates ${commons.scmPubUrl} ${commons.scmPubCheckoutDirectory}"/>
                    </exec>
                    <exec executable="svn">
                      <arg line="update --set-depth exclude ${commons.scmPubCheckoutDirectory}/javadocs"/>
                    </exec>
                    <pathconvert pathsep=" " property="dirs">
                      <dirset dir="${commons.scmPubCheckoutDirectory}" includes="*"/>
                    </pathconvert>
                    <exec executable="svn">
                      <arg line="update --set-depth infinity ${dirs}"/>
                    </exec>
                  </target>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>benchmark</id>
      <properties>
        <skipTests>true</skipTests>
        <benchmark>org.apache</benchmark>
      </properties>
      <build>
        <plugins>
          <plugin>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>exec-maven-plugin</artifactId>
            <version>3.1.0</version>
            <executions>
              <execution>
                <id>benchmark</id>
                <phase>test</phase>
                <goals>
                  <goal>exec</goal>
                </goals>
                <configuration>
                  <classpathScope>test</classpathScope>
                  <executable>java</executable>
                  <arguments>
                    <argument>-classpath</argument>
                    <classpath/>
                    <argument>org.openjdk.jmh.Main</argument>
                    <argument>-rf</argument>
                    <argument>json</argument>
                    <argument>-rff</argument>
                    <argument>target/jmh-result.${benchmark}.json</argument>
                    <argument>${benchmark}</argument>
                  </arguments>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>
</project>
