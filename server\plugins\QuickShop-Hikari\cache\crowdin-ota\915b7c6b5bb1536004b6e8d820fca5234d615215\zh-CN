break-shop-use-supertool: <yellow>您可以使用超级工具来破坏商店。
fee-charged-for-price-change: <green>您已支付 <red>{0}<green> 来更改了价格。
not-allowed-to-create: <red>您不能在这里创建商店
disabled-in-this-world: <red>QuickShop 在这个世界中被禁用
how-much-to-trade-for: <green>请在聊天中输入交易<yellow>{1}个{0}<green>所需的价格。
client-language-changed: <green>QuickShop检测到您的客户端语言已更改，我们正在为您使用 {0} 语言。
shops-backingup: 从数据库创建商店备份...
_comment: 嗨 翻译者！如果您正在Github上或直接编辑源文件的方式进行翻译，请您前往：https://crowdin.com/project/quickshop-hikari
unlimited-shop-owner-changed: <yellow>这个系统商店的店主已更改为 {0}。
bad-command-usage-detailed: '<red>错误的命令参数! 尝试以下参数: <gray>{0}'
thats-not-a-number: <red>输入的内容不是数字
shop-name-disallowed: <red>商店名称<yellow>{0} <yellow>不被允许。请换一个名称后再试！
console-only-danger: <red>这是一个危险的命令，只有控制台才能执行它。
not-a-number: <red>您只能输入一个数字，但您的输入是 {0}。
not-looking-at-valid-shop-block: <red>无法找到创建商店的方块，您需要注视着一个商店方块
shop-removed-cause-ongoing-fee: <red>你在 {0} 的商店已经被移除了，因为你没有足够的资金去维持它！
tabcomplete:
  amount: '[数量]'
  item: '[物品]'
  price: '[价格]'
  name: '[名称]'
  range: '[范围]'
  currency: '[货币名称]'
  percentage: '[百分比%]'
taxaccount-unset: <green>该商店的税务账户现在变为服务器的全局设置。
blacklisted-item: <red>这件物品已经被拉黑，你不能出售它
command-type-mismatch: <red>此命令只能由 <aqua>{0} 执行。
server-crash-warning: '<red>如果服务器运行时替换/删除QuickShop插件 Jar 文件，执行 /quickshop reload后服务器可能会崩溃。'
you-cant-afford-to-change-price: <red>你需要 {0} 才能修改商店的价格。
safe-mode: <red>QuickShop 现在处于安全模式，您不能打开这个商店方块，请与服务器管理员联系以修复错误。
forbidden-vanilla-behavior: <red>该操作有悖于原版行为，因此已被禁止
shop-out-of-space-name: <dark_purple>您名为 {0} 的商店已满！
paste-disabled: |-
  <red>Paste功能已被禁用！你不能请求技术支持。
  原因： {0}
quick-fill:
  entry-own: ' <yellow>- <red>[所有者] <aqua>{0} <light_purple>{1}'
  hover:
    - '<yellow>名称： <aqua>{0}'
    - '<yellow>所有者: <aqua>{0}'
    - '<yellow>类型: <aqua>{0}'
    - '<yellow>价格:<aqua>{0}'
    - '<yellow>物品:<aqua>{0}'
    - '<yellow>位置:<aqua>{0}'
  hover-arg-filled:
    - '<yellow>名称:<aqua>{name}'
    - '<yellow>所有者:<aqua>{owner}'
    - '<yellow>类型: <aqua>{type}'
    - '<yellow>价格:<aqua>{price}'
    - '<yellow>物品:<aqua>{item}'
    - '<yellow>位置:<aqua>{location}'
  header: '<yellow>您有多个名称为“<green>{0}</green>”的商店，选择其中一个继续：'
  entry-normal: ' <yellow>- <red>{0} <aqua>{1} <light_purple>'
  entry-cooperation: ' <yellow>- <red>[所有者] <aqua>{0} <light_purple>{1}'
3rd-plugin-build-check-failed-admin: '<gray>(管理员信息) <light_purple>{0} <dark_gray>拒绝了权限检查。如果这不是预期结果，请尝试添加 <light_purple>{1} <gray> 到监听器黑名单中。配置指南：https://quickshop-community.github.io/QuickShop-Hikari-Documents/docs/modules/shops/protection-checker#resolve-the-plugin-conflicting'
average-price-nearby: '<green>附近的平均价格： <yellow>{0}'
inventory-check-global-alert: "<red>[背包检查] <gray>警告！找到QuickShop悬浮物 <gold>{2}</gold> 在背包中 <aqua>{0}</aqua> <dark_gray>[{1}]</dark_gray>，这不应该发生，这通常意味着有人恶意利用漏洞来复制悬浮物。"
digits-reach-the-limit: <red>您的价格中的小数点后位数超过上限。
currency-unset: <green>商店货币已被成功删除。现在会使用默认货币。
you-cant-create-shop-in-there: <red>你没有在这个位置创建商店的权限。
no-pending-action: <red>你没有任何进行中的操作
refill-success: <green>补货成功
failed-to-paste: <red>无法将数据上传到Pastebin，请检查你的网络连接并重试。（检查控制台获取详情）
shop-out-of-stock-name: <dark_purple>你名为 {0} 的商店的 {1} 脱销了!
shop-name-invalid: <red>商店名称<yellow>{0} <red>无效。请换一个名称后再试！
how-many-buy-stack: <green>聊天栏中输入你想<aqua>买<green>的份数. 每份中有 <yellow>{0}<green> 个该物品, 你可以买 <yellow>{1}<green> 份. 输入 <aqua>{2}<green> 以买下所有该物品.
exceeded-maximum: <red>该值超过了Java 中的最大值。
unlimited-shop-owner-keeped: '<yellow>注意: 商店持有人依然是系统商店店主, 你需要重新设置新的商店店主'
no-enough-money-to-keep-shops: <red>你没有足够的金钱去维持你商店的运营！所有商店已被移除...
3rd-plugin-build-check-failed: <red>第三方插件 <bold>{0}<reset><red> 拒绝了权限检查操作，您在那里有建造权限吗？
not-a-integer: <red>您只能输入一个数字，您的输入是 {0}。
translation-country: '本地化语言：简体中文 (zh_CN)'
buying-more-than-selling: '<red>警告: 你收购的物品价格比出售的要高！'
purchase-failed: '<red>支付失败：内部错误，请联系服务器管理员'
denied-put-in-item: <red>您不能将此物品放入您的商店！
shop-has-changed: <red>在你点击此商店之后此商店被修改了，为了避免恶意修改，您的操作已被取消！
flush-finished: <green>成功地清除所有最近的商店记录 。
no-price-given: <red>请输入一个有效的价格。
shop-already-owned: <red>这已经是一个商店了。
backup-success: <green>备份成功
not-looking-at-shop: <red>无法找到商店，您需要注视着你想要操作的商店。
you-cant-afford-a-new-shop: <red>你需要 {0} 才能创建一个商店。
success-created-shop: <green>已成功地创建商店。
shop-creation-cancelled: <red>商店创建已取消。
shop-owner-self-trade: <yellow>你正在和自己的商店进行交易，所以你的金钱可能并不会变化。
purchase-out-of-space: <red>这个商店空间已不足，请联系商店所有者或助手清空商店
reloading-status:
  success: <green>重载完成，没有任何错误。
  scheduled: <green>重载成功。<gray>(一些更改需要一段时间才能生效)
  require-restart: <green>重载完成。<yellow>(一些更改需要重启服务器后生效)
  failed: <red>重载失败，请检查服务器控制台
player-bought-from-your-store-tax: <green>{0} 从你的商店里购买了 {1} {2}，你因此赚得了 {3} (扣税{4})
not-enough-space: <red>背包空间不足，剩余 {0}
shop-name-success: <green>成功地将商店名称设置为 <yellow>{0}<green>
shop-staff-added: <green>已成功地添加助手 <yellow>{0}<green> 到你的商店。
shop-staff-empty: <yellow>此商店没有商店助手。
shops-recovering: 正在从备份中恢复商店...
virtual-player-component-hover: "<gray>这是虚拟玩家\n<gray>是指一个具有相同名称的系统帐户</gray>\n<green>UUID： <yellow>{0}</yellow></green>\n<green>用户名： <yellow>{1}</yellow></green>\n<green>显示为： <yellow>{2}</yellow></green>"
real-player-component-hover: "<gray>这是一个真正存在的玩家\n<green>UUID： <yellow>{0}</yellow></green>\n<green>用户名： <yellow>{1}</yellow></green>\n<green>显示为： <yellow>{2}</yellow></green>\n<gray>如果您希望使用同名的虚拟系统帐户 将 <dark_gray>\"[]\"</dark_gray> 添加到用户名的两边： <dark_gray>[{1}]</dark_gray>"
menu:
  sell-tax: <green>你支付了<yellow>{0}<green>的税。
  owner: '<green>所有者：{0}'
  preview: <aqua>[物品预览]
  enchants: <dark_purple>附魔
  sell-tax-self: <green>你无需支付税，因为你是这家商店的所有者。
  shop-information: '<green>商店信息：'
  item: '<green>物品：<yellow>{0}'
  price-per: <green>每个<yellow>{0}<green>价格<yellow>{1}
  item-name-and-price: <yellow>{0} {1} <green>共 <yellow>{2}
  item-name-and-price-tax: <yellow>{0} 个 {1}<green> 为 </green> {2} <gray>（{3} 的税）
  successful-purchase: '<green>支付成功：'
  price-per-stack: <green>每<yellow>{2}<green>个<yellow>{0}<green>价格<yellow>{1}
  stored-enchants: <dark_purple>附魔
  item-holochat-error: <red>[错误]
  this-shop-is-selling: <green>这个商店现在正在<light_purple>出售<green>物品。
  shop-stack: '<green>每次交易物品数量：<yellow>{0}'
  space: '<green>空间: <yellow>{0}'
  effects: <green>效果
  damage-percent-remaining: <yellow>{0}% <green>剩余
  item-holochat-data-too-large: <red>[错误] 物品NBT太长，无法显示
  stock: '<green>库存:<yellow>{0}'
  this-shop-is-buying: <green>这个商店现在正在<light_purple>收购<green>物品。
  successfully-sold: '<green>出售成功：'
  total-value-of-chest: '<green>共计商店数: <yellow>{0}'
currency-not-exists: <red>无法找到您想要设置的货币，也许是拼写错误或该货币在这个世界不可用。
no-nearby-shop: <red>附近没有匹配 {0} 的商店。
translation-author: 'Ghost_chu, sandtechnology, Pemdow, YuanYuanOwO, RMSCA, 李馨雨'
integrations-check-failed-trade: <red>集成模块 {0} 拒绝了商店交易操作。
shop-transaction-failed: <red>抱歉，在处理您的购买时发生内部错误。购买已取消，任何交易操作都已被回滚。如果出现这种情况，请联系服务器管理员。
success-change-owner-to-server: <green>成功设置商店所有者为 服务器
shop-name-not-found: <red>名称为 <yellow>{0} <red> 的商店不存在。
shop-name-too-long: <red>此商店名称过长 (最大长度 {0})，请缩短名称后再试！
metric:
  header-player: '<yellow>{0}的 {1} {2} 交易：'
  action-hover: <yellow>{0}个
  price-hover: <yellow>总计 {0}, 包括 {1} 税
  unknown: <gray>(未知)
  undefined: <gray>(无名)
  no-results: <red>未找到交易。
  action-description:
    DELETE: <yellow>玩家删除了一个商店，如果可能的话，商店创建费将会退还给所有者
    ONGOING_FEE: <yellow>玩家支付了持续商店的费用，因为支付期限已过。
    PURCHASE_BUYING_SHOP: <yellow>玩家将一些物品卖给收购商店
    CREATE: <yellow>玩家创建了一个商店
    PURCHASE_SELLING_SHOP: <yellow>玩家从出售商店购买了一些物品
    PURCHASE: <yellow>通过商店购买的物品
  query-argument: '查询参数： {0}'
  amount-hover: <yellow>{0}个
  header-shop: '<yellow>商店 {0}的 {1} {2} 交易：'
  player-hover: |-
    <yellow>{0}
    <gold>UUID: <gray>{1}
  looking-up: <yellow>查询中，请稍候...
  tax-hover: <yellow>{0} 税
  header-global: '<yellow>服务器 {0} {1} 交易：'
  na: <gray>N/A
  transaction-count: <yellow> 总计{0}
  shop-hover: |-
    <yellow>{0}
    <gold>坐标: <gray>{1} {2} {3}, 世界: {4}
    <gold>店主: <gray>{5}
    <gold>商店类型: <gray>{6}
    <gold>物品: <gray>{7}
    <gold>价格: <gray>{8}
  time-hover: '<yellow>时间: {0}'
  amount-stack-hover: <yellow>{0}个
permission-denied-3rd-party: <red>没有权限：第三方插件 [{0}]。
you-dont-have-that-many-items: <red>你只有 {0} 个 {1}。
complete: <green>完成！
translate-not-completed-yet-url: '{0} 的翻译完成度为 {1}，您想帮助我们改进翻译吗？来这里吧： {2}'
success-removed-shop: <green>商店已删除！
currency-set: <green>商店货币已被成功设置为 {0}。
shop-purged-start: <green>商店清理已开始，请检查控制台获取详细信息。
economy-transaction-failed: <red>抱歉，在处理您的交易时发生了内部错误。 交易已被取消，任何交易操作都已被回滚。如果出现这种情况，请联系服务器管理员。
nothing-to-flush: <green>你没有新的商店消息
no-price-change: <red>这不会导致价格变动！
edition-confilct: QuickShop-Hikari 与 QuickShop-Reremake 同时安装在服务器中可能会相互冲突，你需要卸载其中一个
inventory-unavailable: |-
  <red>此商店InventoryWrapper不存在或无效。 你是否使用附属插件重新绑定商店库存？信息：InventoryWrapper={0}，WrapperProvider={1}，SymbolLink={2}。请联系服务器管理员。
file-test: 这是个测试文件，我们可以用它来测试 messages.json 文件是否损坏。你可以在这里填入任何喜欢的彩蛋 :)
unknown-player: <red>目标玩家不存在，请检查你输入的玩家名字。
player-offline: <red>目标玩家目前处于离线状态。
player-profile-format: <aqua>{0}</aqua><gold>(<yellow>{1}</yellow>)
shop-type:
  selling: 出售
  buying: 收购
language:
  qa-issues: '<yellow>质量问题：<aqua>{0}%'
  code: '<yellow>代码： <gold>{0}'
  approval-progress: '<yellow>核查进度: <aqua>{0}%'
  translate-progress: '<yellow>翻译进度: <aqua>{0}%'
  name: '<yellow>名称： <gold>{0}'
  help-us: <green>[帮助我们提高翻译质量]
warn-to-paste: |-
  <yellow>收集并上传数据到Pastebin，这会花费点时间。
  <red><bold>警告</bold>，数据将会在一周内对外可见，可能会导致你泄露服务器的配置和其他敏感信息，确保只向<bold>你信任的人/开发者发送链接。
how-many-sell-stack: <green>在聊天栏中输入你想 <light_purple>卖<green> 的份数. 每份中有 <yellow>{0}<green> 个该物品, 你目前可卖出 <yellow>{1}<green> 份这样的物品，输入 <aqua>{2}<green> 以卖出所有此项物品。
updatenotify:
  buttontitle: '[立即更新]'
  onekeybuttontitle: '[一键升级]'
  label:
    github: '[GitHub]'
    ore: '[Ore]'
    lts: '[长期支持]'
    nbtapi: '[+NBTAPI]'
    qualityverifyed: '[质量]'
    master: '[主分支]'
    unstable: '[不稳定]'
    paper: '[+Paper端优化]'
    stable: '[稳定]'
    spigotmc: '[SpigotMC]'
    papermc: '[PaperMC]'
    bukkitdev: '[BukkitDev]'
    polymart: '[Polymart]'
    modrinth: '[Modrinth]'
    basic: '[Basic]'
  list:
    - '新版本 {0} 已经发布，但您还在使用 {1} 版本！'
    - 噔噔咚！新版本{0} 已经发布，快升级!
    - 在？{0} 都来了，为什么还在用 {1}
    - 快 更 新，新版本 {0} 发布了！
    - 噢！{0}已经发布了呀,你还在用 {1}!
    - 我保证，QuickShop已经更新到了 {0}，但你怎么还没有更新？
    - 修复并重... 抱歉，但是新版本{0}已经发布了！
    - 错误！其实并没有错误，只是新版本 {0} 已经发布了！
    - 哦买噶！{0}已经出了呀！你为什么还在用 {1}?
    - '今日新闻：QuickShop已经更新至 {0} ！'
    - 此版本已弃坑，你现在应该升级插件到新版本 {0} ！
    - 更新 {0} 发布了。现在就下载拯救更新吧！
    - 这里是升级指挥官，{0}版本刚刚发布了！
    - 看看我的样式——{0} 发布了, 你为什么还在用 {1} 呢？
    - 啊啊啊啊啊啊啊啊啊啊！更新 {0} 来了！快升级！
    - 你在想什么呢？{0}已经发布了！快更新！
    - 博士，QuickShop有一个新的更新 {0}！你应该考虑一下更新了~
    - Ko~ko~da~yo~QuickShop有一个新的更新 {0}~
    - 派蒙想要告诉你QuickShop有新的更新 {0}！
  remote-disable-warning: '<red>这个版本的 QuickShop 被远程服务器标记为禁用，意味着这个版本可能存在严重的问题，可以在我们的 SpigotMC 插件页面获取详细信息。{0}. 此警告将继续出现，直到您切换到一个稳定的版本，但它不会影响您的服务器的性能。'
purchase-out-of-stock: <red>这个商店已缺货，请联系商店所有者或助手补充库存
nearby-shop-entry: '<green>- 详情:{0} 价格:<aqua>{1} <green>x:<aqua>{2} <green>y:<aqua>{3} <green>z:<aqua>{4} <green>距离: <aqua>{5} <green>个方块'
chest-title: QuickShop商店
console-only: <red>该命令只能由控制台执行
failed-to-put-sign: <red>没有足够的空间放置商店的信息牌子。
shop-name-unset: <red>商店名称已删除
shop-nolonger-freezed: <green>你解冻了此商店。现在一切恢复正常了！
no-permission-build: <red>你不能在这里建立商店。
tableformat:
  left_half_line: <dark_purple>+--------------------
  right_half_line: <dark_purple>--------------------+
  full_line: <dark_purple>+---------------------------------------------------+
  left_begin: '<dark_purple>| '
quickshop-gui-preview: QuickShop 物品预览界面
translate-not-completed-yet-click: '{0} 的翻译完成度为 {1}，您想帮助我们改进翻译吗？来这里吧： '
taxaccount-invalid: <red>目标帐户无效，请输入有效的玩家名称或uuid(带有破折号)。
player-bought-from-your-store: <red>{0} 从你的商店里购买了 {1} {2}，你因此赚得了 {3}
reached-maximum-can-create: <red>你创建的商店数量达到上限：{0}/{1}
reached-maximum-create-limit: <red>您已达到可以创建商店的最大数量
translation-version: '支持版本: Hikari'
no-double-chests: <red>你没有权限创建大箱子商店。
price-too-cheap: <red>价格必须大于<yellow>${0}
shop-not-exist: <red>这里没有商店
bad-command-usage: <red>错误的命令参数！
cleanghost-warning: <yellow>这个命令将清理<red>所有</red>在不允许创建商店的世界的、出售/购买不允许的物品的，以及 <bold><red>所有在服务器没有加载的世界</red></bold>的商店， 请在确保您的商店数据备份完整后使用 <aqua>/quickshop cleanghost confirm </aqua>来继续。
cleanghost-starting: <green>开始检查幽灵商店(缺少容器方块)。所有不存在的商店将被移除...
cleanghost-deleting: <yellow>发现一个损坏的商店 <aqua>{0}</aqua> 因为 {1}, 标记它删除...
cleanghost-deleted: <green>总计 <yellow>{0}</yellow> 家商店已被删除。
shop-purchase-cancelled: <red>您取消了交易操作。
bypassing-lock: <red>你绕过了QuickShop的锁！
bungee-cross-server-msg: '<yellow>QuickShop跨服信息: <green>{0}'
saved-to-path: 备份文件已保存到 {0}。
shop-now-freezed: <green>你已经冻结了商店。现在没有人可以与这家商店交易！
price-is-now: <green>商店的新价格是 <yellow>{0}
shops-arent-locked: <red>注意，商店无法防盗，如果您想要防盗功能，请使用Lockette，LWC，等插件锁住商店。
that-is-locked: <red>这个商店已上锁。
shop-has-no-space: <red>这个商店只能装下 {0} 个 {1} 了。
safe-mode-admin: '<red><bold>警告: <red>此服务器上的QuickShop正在安全模式下运行，任何功能都无法工作，请输入<yellow>/quickshop <red> 来检查错误。'
shop-stock-too-low: <red>这个商店只有 {0} 个 {1} 了。
world-not-exists: <red>世界 <yellow>{0}<red> 不存在
how-many-sell: <green>聊天栏中输入想 <light_purple>出售 <green>的物品数量. 您现在可以卖出 <yellow>{0} <green>件物品。输入 <aqua>{1}<green> 来出售全部物品。
shop-freezed-at-location: <yellow>你在 {1} 的商店 {0} 被冻结了！
translation-contributors: '开发贡献者：Timtower, Netherfoam, KaiNoMood JackTheChicken 和 Andre_601'
empty-success: <green>清空商店成功
taxaccount-set: <green>该商店的税务帐户已设置为 <yellow>{0}
support-disable-reason:
  hot-reload: <yellow>热重载不受支持，请重启服务器并再次检查。
  outdated: <yellow>该版本的QuickShop已过时，在请求帮助之前更新到最新版本！
  bad-hosts: |-
    <yellow>该服务器 HOSTS 已被修改，QuickShop 的一些功能需要连接到 Mojang API。
    在请求支持之前修复HOSTS, Windows 路径 C:\windows\system32\drivers\et\hosts
    Linux 路径/etc/hosts。
  privacy: <yellow>该服务器在破解（离线）模式下运行。如果你在代理下运行bukkit服务器，并且在代理上的online-mode=true，请正确配置代理相关设置。
  modified: <yellow>文件完整性检查失败，此QuickShop构建已被其他人修改。
  consolespamfix-installed: <yellow>检测到 ConsoleSpamFix 已安装，它将隐藏异常详情，在请求帮助前暂时禁用它。
  authlib-injector-detected: <yellow>该服务器在第三方authlib提供商（如authlib-injector）下运行。
  unsupported-server-software: <yellow>不受支持的服务端核心，任何修改过的混合服务端核心都不支持，包括MCPC, Cauldron, CatServer, Mohist, Magma, Fukkit, Cardboard等。
supertool-is-disabled: <red>超级工具已被禁用，现已不能使用超级工具破坏任何商店。
unknown-owner: 未知所有者
restricted-prices: '<red>物品 {0} 的价格限制为 {1}（包含）到{2}（包含）'
nearby-shop-this-way: <green>商店距离你 {0} 格远
owner-bypass-check: <yellow>已绕过所有检查并完成交易（因为你是商店所有者）！
april-rick-and-roll-easter-egg: "<green>---------------------------------------------------</green>\n<rainbow><bold>限时活动 -- QuickShop-Hikari</bold></rainbow>\n<yellow>获取你的 <gold>高级</gold> 粒子效果并在所有安装 QuickShop 的服务器上使用!</yellow>\n<aqua>点击下面的链接观看领取教程视频!</aqua>\n<hover:show_text:'<gold>点击以在浏览器中打开.</gold><click:open_url:'https://www.bilibili.com/video/BV1GJ411x7h7'>https://www.bilibili.com/video/BV1GJ411x7h7</click></hover>\n<green>---------------------------------------------------</green>"
signs:
  item-right: ''
  out-of-space: 空间不足
  unlimited: 无限
  stack-selling: 出售 {0}
  stack-price: '每{1}x {0}'
  status-unavailable: <red>
  out-of-stock: 缺货
  stack-buying: 收购 {0}
  freeze: 交易已被禁用
  price: '单价：{0}'
  buying: 收购 {0}
  header: '{1}{0}'
  header-available: <green>{0}
  header-unavailable: <red>{0}
  selling: 出售 {0}
  status-available: <green>
  item-left: ''
negative-amount: <red>你输入的数字是负数。
display-turn-on: <green>成功开启商店悬浮物显示
shop-staff-deleted: <green>成功移除 {0} 的商店助手权限
nearby-shop-header: '<green>匹配附近<aqua>{0}<green>的商店：'
backup-failed: 无法备份数据库，请查看控制台以获取详细信息。
shop-staff-cleared: <green>成功删除了该商店的所有助手。
price-too-high: <red>商品的价格太高了！你不能创建价格高于 {0} 的商店。
plugin-cancelled: '<red>操作被取消，原因： {0}'
player-sold-to-your-store: <green>{0} 在你的商店出售了 {1} 个 {2} 。
shop-out-of-stock: <dark_purple>你位于 {0}, {1}, {2}, 的商店的 {3} 脱销了。
how-many-buy: <green>在聊天中输入您想要<aqua>购买<green>的数量。您可以购买<yellow>{0}<green>个，输入<aqua>{1}<green>以购买全部。
language-info-panel:
  help: '帮助我们： '
  code: '语言代码：'
  name: '语言：'
  progress: '进度：'
  translate-on-crowdin: '[在 Crowdin 上翻译]'
item-not-exist: <red>物品 <yellow>{0}</yellow> 不存在，请检查您的拼写。
shop-creation-failed: <red>商店创建失败，请与服务器管理员联系。
inventory-space-full: <red>你的背包只能放入<green>{1}x <red>个物品。请尝试清理你的背包空间！
no-creative-break: <red>你不能在创造模式破坏其他玩家的商店, 请使用生存模式或用超级工具 {0} 来破坏。
booleanformat:
  success: <green>✔
  failed: <red>✘
controlpanel:
  stack: '<green>每次交易物品数量: <aqua>{0} <yellow>[<bold><light_purple>修改</light_purple>]'
  price-hover: <yellow>点击来给商店设置一个新的价格
  remove: <bold><red>[删除商店]
  mode-buying-hover: <yellow>点击可将商店切换为 出售 模式。
  empty: '<green>清空: 删除商店所有商品 <yellow>[<bold><light_purple>确认</light_purple></bold>]'
  stack-hover: <yellow>点击设置每次交易的物品数量。设置为1以恢复为普通单物品商店。
  alwayscounting-hover: <yellow>点击以切换是否总是计算容器物品。
  alwayscounting: '<green>总是计算容器物品: {0} <yellow>[<bold><light_purple>更改</light_purple>]'
  setowner: '<green>所有者: <aqua>{0} <yellow>[<bold><light_purple>修改</light_purple></bold>]'
  freeze: '<yellow>冻结模式： <aqua>{0} <yellow>[<bold><light_purple>切换</light_purple></bold>]'
  price: '<green>价格: <aqua>{0} <yellow>[<bold><light_purple>修改</light_purple></bold>]'
  currency-hover: <yellow>点击以设置或移除商店使用的交易货币
  lock: '<yellow>商店锁: <aqua>{0} <yellow>[<bold><light_purple>切换</light_purple></bold>]'
  mode-selling: '<green>商店模式: <aqua>出售 <yellow>[<bold><light_purple>更改</light_purple></bold>]'
  currency: '<green>货币: <aqua>{0} <yellow>[<bold><light_purple>设置</light_purple></bold>]'
  setowner-hover: <yellow>点击来修改店主
  mode-buying: '<green>商店模式: <aqua>收购 <yellow>[<bold><light_purple>更改</light_purple></bold>]'
  item: '<green>商店物品: {0} <yellow>[<bold><light_purple>修改</light_purple></bold>]'
  unlimited: '<green>无限模式: {0} <yellow>[<bold><light_purple>切换</light_purple></bold>]'
  unlimited-hover: <yellow>点击以切换无限模式
  refill-hover: <yellow>点击以重新填充商品
  remove-hover: <yellow>点击来删除商店
  toggledisplay-hover: <yellow>切换商店是否显示悬浮物
  refill: '<green>填充: 重新填充商品 <yellow>[<bold><light_purple>确认</light_purple></bold>]'
  freeze-hover: <yellow>切换商店冻结状态。
  lock-hover: <yellow>启用/禁用商店锁
  item-hover: <yellow>点击以更改商店物品
  infomation: '<green>商店控制面板'
  mode-selling-hover: <yellow>点击可将商店切换为 收购 模式。
  empty-hover: <yellow>点击清除商店库存
  toggledisplay: '<green>悬浮物显示: <aqua>{0} <yellow>[<bold><light_purple>切换</light_purple></bold>]'
  history: '<green>历史： <yellow>[<bold><light_purple>预览</light_purple></bold>]</yellow>'
  history-hover: <yellow>点击查看商店历史记录
timeunit:
  behind: 后面
  week: "{0} 周"
  weeks: "{0} 周"
  year: "{0} 年"
  before: 前
  scheduled: 已计划
  years: "{0} 年"
  scheduled-in: 计划在 {0}
  second: "{0} 秒"
  std-past-format: '{5}{4}{3}{2}{1}{0}前'
  std-time-format: HH:mm:ss
  seconds: "{0} 秒"
  hour: "{0} 时"
  scheduled-at: 计划于 {0}
  after: 之后
  day: "{0} 天"
  recent: 近期
  between: 之间
  hours: "{0} 时"
  months: "{0} 月"
  longtimeago: 很久前
  between-format: 在 {0} 和 {1} 之间
  minutes: "{0} 分"
  justnow: 现在
  minute: "{0} 分"
  std-format: yyyy/MM/dd HH:mm:ss
  future-plain-text: 未来
  month: "{0} 月"
  future: 在 {0}
  days: "{0} 天"
command:
  reloading: <green>配置文件重载成功. <yellow>一些更改可能需要重启才能生效.
  description:
    buy: <yellow>切换到 <light_purple>收购 <yellow>模式
    about: <yellow>显示QuickShop信息
    language: <yellow>切换正在使用的语言
    purge: <yellow>在控制台启动商店清理任务
    paste: <yellow>上传调试信息到Pastebin
    title: <green>QuickShop 帮助
    remove: <yellow>删除您正看向的商店
    ban: <yellow>封禁商店中的玩家
    empty: <yellow>清空商店库存
    alwayscounting: <yellow>设置商店是否总会计算容器物品（即使是无限商店）
    setowner: <yellow>为商店设置新的所有者。
    reload: <yellow>重载QuickShop 的配置文件
    freeze: <yellow>禁用或启用商店交易
    price: <yellow>修改 收购/出售 的价格
    find: <yellow>在附近寻找具有指定物品名称的商店
    create: <yellow>在目标箱子创建商店
    lock: <yellow>切换商店锁状态
    currency: <yellow>设置或移除该商店的货币类型设置
    removeworld: <yellow>删除指定世界中的所有商店
    info: <yellow>查看QuickShop统计信息
    owner: <yellow>为商店设置新的所有者。
    amount: <yellow>设置物品数量(在有聊天问题时有用)
    item: <yellow>改变商店物品
    debug: <yellow>切换开发者模式
    unlimited: <yellow>将商店设置为无限库存
    sell: <yellow>切换到 <aqua>出售 <yellow>模式
    fetchmessage: <yellow>获取未读的商店交易日志
    staff: <yellow>管理商店助手
    clean: <yellow>移除所有已加载的脱销的商店
    refill: <yellow>添加一定数量的物品到商店
    help: <yellow>显示QuickShop帮助
    removeall: <yellow>删除指定玩家的<aqua>所有<reset><yellow>商店
    unban: <yellow>解禁商店中的玩家
    transfer: <yellow>将某人的所有商店转移给其他人
    transferall: <yellow>将某人的所有商店转移给其他人
    transferownership: <yellow>将正在看向的商店转让给其他玩家
    size: <yellow>修改每次交易的物品数量
    supercreate: <yellow>越过所有区域保护检查创建商店
    taxaccount: <yellow>设置商店使用的税务账户
    name: <yellow>将商店命名为特定名称
    toggledisplay: <yellow>切换商店是否显示悬浮物
    permission: <yellow>商店权限管理
    lookup: <yellow>管理物品引用查找表
    database: <yellow>查看和维护 QuickShop 数据库
    benefit: <yellow>设置商店所有者和其他玩家之间的利益分配
    tag: <yellow>添加、 删除或查询商店标签
    suggestprice: <yellow>根据其他商店的情况, 为商店提出建议价格
    history: <yellow>查看商店的历史交易记录
    sign: <yellow>更改商店的告示牌类型
  bulk-size-not-set: '<red>用法: /quickshop size <数量>'
  no-type-given: '<red>用法: /quickshop find <物品名称>'
  feature-not-enabled: 该功能未在配置文件中启用。
  now-debuging: <green>成功切换到了开发者模式，插件重载中...
  no-amount-given: <red>未指定数量。使用方式为 <green>/quickshop refill <数量><red>
  no-owner-given: <red>未指定新的商店所有者
  disabled: '<red>此命令已禁用: <yellow>{0}'
  bulk-size-now: <green>现在开始交易 <yellow>{0} 个 {1}
  toggle-always-counting:
    counting: <green>商店现在总会计算容器物品（即使是无限商店）
    not-counting: <green>商店现在会视情况计算容器物品（无限商店无视容器物品）
  cleaning: <green>正在移除脱销的商店...
  now-nolonger-debuging: <green>成功切换到了生产模式，插件重载中...
  toggle-unlimited:
    limited: <green>商店已更改为普通模式
    unlimited: <green>商店已更改为无限模式
  transfer-success-other: <green>已将玩家<yellow> {1} 的<green> {0} 个商店转移到玩家<yellow> {2}
  no-trade-item: <green>请在主手手持想要切换到的新物品
  wrong-args: <red>无效参数。使用 <bold>/quickshop help <red>来查看命令帮助。
  some-shops-removed: <yellow>{0} <green>个商店已被移除
  new-owner: '<green>新的商店所有者: <yellow>{0}'
  format: <green>/{0} {1} <yellow>- {2}
  transfer-success: <green>已将<yellow> {0} <green>个商店转移到玩家<yellow> {1}
  now-buying: <green>现在开始 <light_purple>收购 <yellow>{0}
  now-selling: <green>现在开始 <aqua>出售 <yellow>{0}
  cleaned: <green>清理了 <yellow>{0} <green>个商店.
  trade-item-now: <green>现在开始交易 <yellow>{0} 个 {1}
  no-world-given: <red>请指定一个世界名称
  format-disabled: <red>/{0} {1} <gray>- {2}
  invalid-bulk-amount: <red>给定的值 {0} 大于最大堆叠大小或小于1
currency-not-support: <red>此经济插件不支持多货币功能。
trading-in-creative-mode-is-disabled: <red>你不能在创造模式下进行交易操作。
the-owner-cant-afford-to-buy-from-you: <red>这件商品标价 {0}，但是店主只剩 {1} 了。
you-cant-afford-shop-naming: <red>无法支付商店命名的费用，命名费用为 {0}。
inventory-error: |-
  <red>处理InventoryWrapper失败。 你是否使用附属插件重新绑定商店库存？<newline>信息：异常={0}，InventoryWrapper={1}，WrapperType={2}，WrapperProvider={3}，SymbolLink={4}。 请联系服务器管理员
integrations-check-failed-create: <red>集成模块 {0} 拒绝了创建商店操作。
shop-out-of-space: <dark_purple>你位于 {0}, {1}, {2}, 的商店库存已经满了。
admin-shop: 系统商店
no-anythings-in-your-hand: <red>你的手里没有任何物品。
no-permission: <red>你没有权限这样去操作。
chest-was-removed: <red>当前箱子已被移除。
you-cant-afford-to-buy: <red>余额不足！购买该商店物品需要 {0}，但是你只有 {1}。
shops-removed-in-world: <yellow>已删除世界 <aqua>{1}<yellow> 中的 <aqua>{0}<yellow> 个商店。
display-turn-off: <green>成功关闭商店悬浮物显示
client-language-unsupported: <yellow>QuickShop不支持您的客户端语言，我们现在退回到 {0} 语言。
language-version: '63'
not-managed-shop: <red>你不是该商店的所有者或助手
shop-cannot-trade-when-freezing: <red>您不能与这家商店交易，因为它已被冻结。
invalid-container: <red>无效的容器方块。您只能在有库存的方块上创建商店。
permission:
  header: <green>商店权限信息
  header-player: <green> {0} 的商店权限信息
  header-group: <green>商店权限信息 {0}
  table: <gold><bold>{0}</bold>:</gold> <gray>{1}
  item:
    purchase: <yellow>允许拥有此权限的用户与商店进行交易(包括购买和收购)
    show-information: <yellow>允许拥有此权限的用户查看商店信息。(查看商店控制面板)
    preview-shop: <yellow>允许拥有此权限的用户预览商店所交易的物品(物品预览)
    search: <yellow>允许拥有此权限的用户搜索目标商店(移除权限将商店从搜索结果中隐藏)
    delete: <yellow>允许拥有此权限的用户删除商店。
    receive-alert: <yellow>允许拥有此权限的用户接收商店消息 (商店缺货或新交易信息)。
    access-inventory: <yellow>允许拥有此权限的用户查看商店库存
    ownership-transfer: <yellow>允许拥有此权限的用户转移商店所有者。
    management-permission: <yellow>允许拥有此权限的用户管理组权限并编辑用户组
    toggle-display: <yellow>允许拥有此权限的用户切换商店悬浮物显示。
    set-shoptype: <yellow>允许拥有此权限的用户设置商店交易模式(切换购买或收购)。
    set-price: <yellow>允许拥有此权限的用户设置商店价格
    set-item: <yellow>允许拥有此权限的用户设置商店交易物品
    set-stack-amount: <yellow>允许拥有此权限的用户设置商店堆叠金额
    set-currency: <yellow>允许拥有此权限的用户设置商店货币。
    set-name: <yellow>允许拥有此权限的用户设置商店名称。
    set-sign-type: <yellow>更改商店牌子类型
    view-purchase-logs: <yellow>查看商店交易日志的权限
  group:
    everyone: <yellow>除商店店主外，所有用户的默认权限组。
    staff: <yellow>商店助手的系统权限组
    administrator: <red>管理员权限组，该组中的用户与商店店主拥有几乎相同的权限。
invalid-group: <red>无效的组名称
invalid-permission: <red>无效权限
invalid-operation: <red>无效的操作，只允许 {0}
player-no-group: <yellow>玩家 {0} 在这个商店中不属于组
player-in-group: <green>玩家 {0} 属于组 <aqua>{1}</aqua> 在这个商店
permission-required: <red>您在这个商店里没有权限 {0} 去做这个。
no-permission-detailed: <red>您没有权限 <yellow>{0}</yellow> 去做这个。
paste-notice: "<yellow>注意：如果您正在为故障排除目的创建Paste报告。 一定要在创建Paste报告之前重新复现错误; 我们需要在缓冲区短暂保留日志以解决问题。 如果您创建Paste报告速度太慢，或者没有重新复现错误或重新启动服务器，则Paste报告将不记录任何内容且无用。"
paste-uploading: <aqua>请稍候... 正在上传Paste到pastebin...
paste-created: '<green>Paste已创建，点击在浏览器中打开： <yellow>{0}</yellow><br><red>警告： <gray>永远不要将Paste发送给不信任的人。'
paste-created-local: |-
  <green>Paste创建并保存到您的本地磁盘： {0}<newline><red>警告： <gray>永远不要将Paste发送给不信任的人。
paste-created-local-failed: <red>保存Paste到本地磁盘失败，请检查您的磁盘。
paste-451: |-
  <gray><b>TIPS: </b> 您所在的国家或地区似乎已经阻止了CloudFlare Workers 服务, QuickShop Paste可能无法正确加载。<newline><gray>如果后续操作失败， 尝试添加 --file 附加参数以生成本地Paste： <dark_gray>/quickshop paste --file</dark_gray>
paste-upload-failed: <red>上传Paste到 {0} 失败，正在尝试其它的Pastebin...
paste-upload-failed-local: <red>上传Paste失败，尝试生成本地Paste...
command-incorrect: '<red>指令用法错误，输入/quickshop help检查帮助。用法： {0}。'
successfully-set-player-group: <green>成功地将玩家 {0} 组设置为 <aqua>{1}</aqua>。
successfully-unset-player-group: <green>成功地移除此商店中的玩家权限组。
successfully-set-player-permission: <green>成功设置玩家 {0} 权限 <aqua>{1}</aqua> 在商店 <aqua>{2}</aqua>
lookup-item-created: <green>一个名为 <aqua>{0}</aqua> 的物品成功注册到物品引用查找表中。 现在你可以在配置文件中引用此物品了。
lookup-item-exists: <red>一个名为 <yellow>{0}</yellow> 的物品已存在于物品引用查找表中，请先将其删除或者起一个新名字。
lookup-item-not-found: <red>物品 <yellow>{0}</yellow> 不存在于物品引用查找表中。
lookup-item-name-illegal: <red>非法物品名，只有数字、字母和下划线可以使用。
lookup-item-name-regex: '<red>物品名不符合正则表达式: <aqua>{0}</aqua>.'
lookup-item-test-not-found: '<gold>测试: <yellow>你手中的物品未注册到物品引用查找表中。'
lookup-item-test-found: '<gold>测试: <green>你手中的物品在物品引用查找表中注册的名字为 <aqua>{0}</aqua>。'
lookup-item-removed: <green>指定的物品 <aqua>{0}</aqua> 现已从物品引用查找表中删除。
internal-error: <red>发生了一个内部错误，请联系服务器管理员。
argument-cannot-be: <red>参数 <aqua>{0}</aqua> 不能为 <yellow>{1}</yellow>.
argument-must-between: <red>参数 <aqua>{0}</aqua> 值必须在 <yellow>{1}</yellow> 和 <yellow>{2}</yellow> 之间
invalid-percentage: <red>百分比无效，您必须在百分比的数字后添加'%'
display-fallback: |-
  <red>由于内部错误，正在显示回退消息。
  此项目的值可能无法本地化或处理正确。
  请与服务器管理员联系。
not-a-valid-time: |-
  <red>字符串 <yellow>{0}</yellow> 不是一个有效的时间戳, 请输入 <aqua>Zulu 时间 (ISO 8601)</aqua> 或 <aqua>Unix Epoch 时间</aqua>。
  <gold>有效时间戳示例(适用于 Sat, 17 December 2022 10:31:37 GMT)</gold>
  <aqua>-<aqua> <yellow>2022-12-17T10:31:37Z</yellow> <grey>(Zulu Time)</grey>
  <aqua>-<aqua> <yellow>1671273097</yellow> <grey>(Unix Epoch 时间以秒计)</grey>
invalid-past-time: <red>不能指定过去的时间。
debug:
  arguments-invalid: <red>提供的参数 <yellow>{0}</yellow> 无效。
  sign-located: '<green>有效信息牌： <yellow>{0}</yellow>。'
  operation-missing: <red>您必须指定一个操作。
  operation-invalid: <red>您必须指定一个有效的操作。
  invalid-base64-encoded-sql: <red>提供的 SQL 必须为 base64 编码。
  warning-sql: |-
    <bold><red>警告：</red></bold><yellow>您正在执行 SQL 语句 这可能会损坏您的数据库或摧毁数据库中的任何数据，即使它属于其他插件。
    <red>如果您不信任此操作或者无法预料执行此操作后的后果，请不要继续。
  warning-sql-confirm: <yellow>要确认这个危险的操作，请在 60 秒内输入 <aqua>/quickshop debug database sql confirm {0}</aqua>。
  warning-sql-confirm-hover: <yellow>点击以确认此危险操作。
  sql-confirm-not-found: <yellow>指定的操作无效或已过期
  sql-executing: '<yellow>正在执行 SQL 语句: <aqua>{0}'
  sql-completed: <green>已完成，共{0} 行受到影响。
  sql-exception: <red>执行 SQL 查询时发生错误，请检查控制台获取详细信息！
  sql-disabled: '<red>出于安全原因，SQL 查询在这个服务器上被禁用。如果你真的需要这个功能， 您可以添加以下参数到启动参数： <aqua>{0}</aqua> 并设置为 `true` 来启用它。'
  force-shop-reload: <yellow>正在强制重载已加载的商店……
  force-shop-reload-complete: <green>已强制重载 <aqua>{0}</aqua> 个商店。
  force-shop-loader-reload: <yellow>正在强制商店加载器重载……
  force-shop-loader-reload-unloading-shops: <yellow>正在取消 <aqua>{0}</aqua> 个已加载的商店……
  force-shop-loader-reload-unloading-shops-from-memory: <yellow>正在从内存中移除 <aqua>{0}</aqua> 个商店……
  force-shop-loader-reload-reloading-shop-loader: <yellow>正在重新调用商店加载器以从数据库重载所有商店……
  force-shop-loader-reload-complete: <green>商店加载器已重新加载所有的商店！
  toggle-shop-loaded-status: <aqua>切换商店加载状态为 <gold>{0}
  shop-internal-data: '<yellow>此商店内部数据：</yellow>{0}'
  handler-list-not-valid-bukkit-event-class: <red>所提供的 class <yellow>{0}</yellow> 不是有效的 Bukkit event class。
  update-player-shops-signs-no-username-given: <red>你必须提供有效的玩家名称。
  update-player-shops-signs-create-async-task: <yellow>正在为强制更新告示牌创建异步任务……
  update-player-shops-player-selected: '<yellow>已选择玩家：<gold>{0}'
  update-player-shops-player-shops: <yellow>共有 <gold>{0}</gold> 个商店等待更新。
  update-player-shops-per-tick-threshold: '<yellow>最大商店可以每（tick）更新：<gold>{0}'
  update-player-shops-complete: <green>任务已完成，已使用 <yellow>{0} 毫秒</yellow>更新。
  update-player-shops-task-started: <gold>任务已开始，请等待完成。
  item-info-store-as-string: "<green>您正在查看的商店： <gold>{0}</gold> Hash: <white>{1}</white>"
  item-info-hand-as-string: "<green>您手中的物品： <gold>{0}</gold> Hash： <white>{1}</white>"
  item-matching-result: "<green>Hand2Store: <aqua>{0}</aqua>, Store2Hand: <aqua>{1}</aqua>"
  hikari-cp-size-tweak: "<green>HikariCP MaximumPoolsize 和 MinimumIdle 已设置为 <white>{0}</white>"
  hikari-cp-testing: "<green>请稍候，正在测试 HikariCP 连接..."
  hikari-cp-working: "<green>通过！HikariCP运行良好！"
  hikari-cp-not-working: "<red>失败！HikariCP返回的连接未正常工作！(未在 1 秒内通过测试)"
  hikari-cp-timeout: "<red>HikariCP 在获取有效连接时超时，请清理所有活动查询以释放连接资源。"
  queries-stopped: "<green>停止 <white>{0}</white> 活动查询。"
  queries-dumping: "<yellow>正在转储活动查询..."
  restart-database-manager: "<yellow>重新启动 SQLManager..."
  restart-database-manager-clear-executors: "<yellow>正在清除 executors..."
  restart-database-manager-unfinished-task: "<yellow>未完成的任务： <white>{0}</white>"
  restart-database-manager-unfinished-task-history-query: "<yellow>未完成的任务（历史查询）： <white>{0}</white>"
  restart-database-manager-reconnect: "<yellow>通过初始序列重新启动 SQLManager (通过 async 执行器)"
  restart-database-manager-done: "<green>完成！"
  property-incorrect: "<yellow>您必须输入属性键值（且只输入一个）。例如，aaa=bbb"
  property-security-block: "<red>请求被拒绝，出于安全原因，您只能更改启动脚本 <aqua>com.ghostchu.quickshop</aqua> 或 <aqua>quickshop</aqua> 的属性。"
  property-removed: "<green>已删除属性键 <white>{0}</white>"
  property-changed: "<green>属性键 <white>{0}</white> 已从 <white>{1}</white> 更改为 <white>{2}</white>"
  marked-as-dirty: "<green>将所有商店标记为 dirty 状态，它们将在下一个自动保存任务中被强制保存。(重启服务器以强制执行存储保存任务）"
  display-removed: "<green>成功移除 <yellow>{0}</yellow> 个QuickShop的悬浮物/悬浮实体。"
database:
  scanning: <green>正在 QuickShop 数据库中扫描孤立的数据，扫描过程中数据库负载可能会增加，这可能需要一段时间...
  scanning-async: <yellow>正在异步任务线程上扫描 QuickShop 数据库中孤立的数据，扫描过程中数据库负载可能会增加，这可能需要一段时间，请稍后再试。
  already-scanning: <red>已有一个扫描任务正在进行中，请等待扫描完成。
  trim-warning: <yellow><red><bold>警告： </red>在继续数据库清理之前备份您的数据库以避免数据丢失。 一旦准备就绪，请执行 <aqua>/quickshop database trim confirm</aqua> 继续。
  status: '<yellow>状态: {0}'
  status-good: <green>良好
  status-bad: <yellow>需要维护
  isolated: '<yellow>孤立的数据：'
  isolated-data-ids: '<aqua>└<yellow> 数据记录项: <gold>{0}'
  isolated-shop-ids: '<aqua>└<yellow> 商店索引: <gold>{0}'
  isolated-logs: '<aqua>└<yellow> 日志: <gold>{0}'
  isolated-external-caches: '<aqua>└<yellow> 缓存项: <gold>{0}'
  last-purge-time: <yellow>最后清理时间 {0}
  report-time: <yellow>上次扫描时间 {0}
  auto-scan-alert: <yellow>此服务器上的 QuickShop 数据库需要维护。找到 <gold>{0}</gold> 个孤立的数据等待清理。
  auto-trim: <green>自动清理已在此服务器上启用，不需要手动清理。
  trim-complete: <green>数据库清理已完成， <yellow>{0}</yellow> 个孤立的数据已被清理。
  auto-trim-started: <green>自动清理已经开始，请稍候...
  trim-start: <green>数据库清理已开始，请稍候...
  trim-exception: <red>数据库清理失败，数据库清理过程中出现异常，请检查服务器控制台。
  generated-at: '<yellow>生成于： <gold>{0}'
  purge-date: <red>您必须给出一个日期
  purge-warning: <yellow>此操作将清除存储在数据库中的历史记录，包括商店创建/更改/删除、购买、交易和系统日志。 删除这些数据可以释放磁盘空间，但所有历史数据都会丢失，依赖计量的其它插件将停止工作。 要继续执行此操作，请执行指令以 `/quickshop database purgelogs <before-days> 确认`
  purge-task-created: <green>任务已创建! 数据库在控制台静默清除历史记录
  purge-done-with-line: <green>清除任务已完成，总共 <gold>{0}</gold> 行已从数据库中清除。
  purge-done-with-error: <red>清除任务失败，请检查服务器控制台获取详细信息。
  purge-players-cache: <yellow>请稍候，正在清理玩家缓存...
  purge-players-completed: |-
    <green>成功地从内存和数据库清除了 {0} 个玩家缓存。<aqua>注意：此操作可能会影响服务器性能。
  purge-players-error: <red>清除玩家缓存失败，请检查服务器控制台。
  suggestion:
    trim: <yellow>此数据库需要进行清理，执行命令 <aqua>/quickshop database trim</aqua> 来清理数据库。
always-counting-removal-early-warning: <red>总是计数功能将移除，您不应该再使用它，并将在将来停止工作。
exporting-database: <green>正在导出数据，请稍候...
exporting-failed: <red>导出数据失败，请检查服务器控制台。
exported-database: <green>数据已导出到 <yellow>{0}</yellow>。
importing-not-found: <red>找不到 <yellow>{0}</yellow> 文件，请检查文件路径。
importing-early-warning: |-
  <yellow><red><bold>警告: </bold></red>备份即将被导入到当前数据库。任何已存在的数据都将被永久清除并使用新的数据覆盖。<newline><red>您确定要继续导入吗？</red><yellow>输入 <aqua>/quickshop recovery confirm</aqua> 继续。
importing-database: <green>正在从备份导入数据，请稍候...
importing-failed: <red>导入数据失败，请检查服务器控制台。
imported-database: <green>数据从 <yellow>{0}</yellow> 导入。
transfer-sent: <green>商店转移请求已发送到 <yellow>{0}</yellow>。
transfer-request: <yellow>玩家 <aqua>{0}</aqua> 请求将他们的商店转移给您。您想接受这次来自 <aqua>{0}</aqua> 的商店吗？
transfer-single-request: <yellow>玩家 <aqua>{0}</aqua> 想要将商店转让给您。您想要接受请求吗？
transfer-ask: |-
  <gold>输入 <red>/quickshop transfer accept</red> 来接受。</gold><gold>输入 <red>/quickshop transfer deny</red> 来拒绝.</gold><newline><gold>请求将在 <red>{0}</red> 秒后过期。
transferall-ask: |-
  <gold>输入 <red>/quickshop transferall accept</red> 来接受。</gold><gold>输入 <red>/quickshop transferall deny</red> 来拒绝.</gold><newline><gold>请求将在 <red>{0}</red> 秒后过期。
transfer-single-ask: |-
  <gold>输入 <red>/quickshop transfer accept</red> 来接受。</gold><gold>输入 <red>/quickshop transfer deny</red> 来拒绝.</gold><newline><gold>请求将在 <red>{0}</red> 秒后过期。
transfer-accepted-fromside: <green>玩家 <aqua>{0}</aqua> 接受了您的商店转移请求。
transfer-accepted-toside: <green>您接受了 <aqua>{0}</aqua> 的商店转移请求。
transfer-rejected-fromside: <red>玩家 <aqua>{0}</aqua> 拒绝了您的商店转移请求。
transfer-rejected-toside: <red>您拒绝了 <aqua>{0}</aqua> 的商店转移请求。
transfer-no-pending-operation: <red>您没有待处理的商店转移请求。
transfer-no-self: <red>您不能将商店转移给自己
benefit-overflow: <red>所有利益的总和不能大于或等于100%
benefit-exists: <red>目标玩家已经在此商店的利益分配列表中。
benefit-removed: <red>目标玩家已被从商店的利益分配中移除。
benefit-added: <green>玩家 <aqua>{0}</aqua> 已被添加到商店的利益分配中！
benefit-updated: <green>玩家 <aqua>{0}</aqua> 的利益分配已更新！
benefit-query: <green>该商店在利益分配列表中有 <yellow>{0}</yellow> 名玩家！
benefit-query-list: <yellow> - </yellow><white>玩家 <gold>{0}</gold>，利益分配 <gold>{1}%
tag-added: '<green>成功将 <aqua>#{0}</aqua> 添加到该商店！'
tag-add-duplicate: '<red>此商店已经存在标签 <aqua>#{0}</aqua>！'
tag-removed: '<green>成功从该商店删除 <aqua>#{0}</aqua>！'
tag-remove-not-exists: '该商店中不存在标签 <aqua>#{0}</aqua>！'
tag-cleared: <green>成功清除了该商店的所有标签！
tag-shops-cleared: '<green>成功地从您所有标记的商店中清除 <aqua>#{0}</aqua>！'
tag-query: '<green>该商店有 <yellow>{0}</yellow> 标签：'
tag-query-listing: '<yellow> - <aqua>#{0}'
tag-query-no-tag: <red>此商店没有标签。
tag-query-shops: '<green>此标签包含 <yellow>{0}</yellow> 商店：'
tag-query-shops-listing: <yellow> - <aqua>{0}
batch-operations-based-on-tags-no-failure: <green>成功批量处理 <yellow>{0}</yellow> 个商店
batch-operations-based-on-tags-have-failure: <yellow>总 {0} 商店在批处理中成功完成，但是 <red>{1}</red> 请求未完成
batch-operations-based-on-tags-have-failure-with-reason: '<yellow>总 {0} 商店在批处理中成功完成，但是 <red>{1}</red> 请求未完成。原因： <gold>{2}'
batch-operations-based-on-tags-have-failure-error-messages-may-appears-above: <gray>错误消息将在聊天中的此消息上方发送给您。
addon:
  towny:
    commands:
      town: <yellow>将商店设置或取消设置为城镇商店
      nation: <yellow>将商店设置或取消设置为国家商店
    make-shop-owned-by-town: <green>你已将商店设为 <yellow>{0}</yellow> 镇拥有
    make-shop-no-longer-owned-by-town: <green>您已重置商店所有权，它现在转移回原店主
    make-shop-owned-by-nation: <green>你已将商店设为 <yellow>{0}</yellow> 国拥有
    make-shop-no-longer-owned-by-nation: <green>您已重置商店所有权，它现在转移回原店主
    shop-owning-changing-notice: <gray>此商店现在由城镇/国家拥有，商店店主已自动添加到管理员列表中，使用命令/quickshop permission 修改或添加任何新的商店店主/商店助手
    target-shop-already-is-town-shop: <red>目标商店已经归城镇所有。
    target-shop-already-is-nation-shop: <red>目标商店已归国家所有。
    target-shop-not-in-town-region: <red>目标商店不在城镇内
    target-shop-not-in-nation-region: <red>目标商店不在国家内
    item-not-allowed: <red>这个商店的物品不被城镇/国家商店使用，请尝试其它物品！
    operation-disabled-due-shop-status: <red>这个商店已被禁用，因为它已经是一个城镇/国家商店。
    plot-type-disallowed: <red>您不能在这种类型的地皮上创建城镇/国家商店。
    flags:
      own: <red>您只能在您拥有的商店类型牵引地创建商店。
      modify: <red>您没有在该Towny区域的建造权限
      shop-type: <red>您只能在Towny区域中创建商店
  residence:
    creation-flag-denied: <red>您没有在该领地创建商店的权限。
    trade-flag-denied: <red>您没有在该领地与商店交易的权限。
    you-cannot-create-shop-in-wildness: <red>你不能在野外创建商店
  griefprevention:
    creation-denied: <red>您没有在该领地创建商店的权限。
    trade-denied: <red>您没有在该领地与商店交易的权限。
  lands:
    world-not-enabled: <red>你不能在这个世界中交易或创建
    creation-denied: <red>您没有在该Land区域创建商店的权限
  plotsquared:
    no-plot-whitelist-creation: <red>你不能在地皮外创建商店
    no-plot-whitelist-trade: <red>你不能在地皮之外与商店交易
    creation-denied: <red>你没有权限在这块地皮创建商店
    trade-denied: <red>你没有权限在这块地皮与商店交易
    flag:
      create: 创建 QuickShop-Hikari 商店
      trade: 交易 QuickShop-Hikari 商店
  superiorskyblock:
    owner-create-only: <red>只有岛屿所有者才能在这里创建商店
    owner-member-create-only: <red>只有岛屿所有者或岛屿成员才能在这里创建商店
  worldguard:
    creation-flag-test-failed: <red>您没有在该WorldGuard区域创建商店的权限
    trade-flag-test-failed: <red>您没有在该WorldGuard区域与商店交易的权限。
    reached-per-region-amount-limit: "<red>您已达到该区域的最大可创建商店数量"
  discord:
    __to_translators: 'To translators: `addon.discord.discord-messages` use a special variable in strings, %%LANG_KEY%% instead of QuickShop''s style {LANG_INDEX}, do not translate anything inside double ''%'' section. BUT for any other section texts, it respect the normal QuickShop Text System syntax ({0},{1} etc.) This may be difficult to understand because two different variable forms are used in the same program at the same time, but trust me, it is reasonable. (you don''t need translate this string, it''s a comment)'
    __to_users: 和QuickShop的文字系统一样，Discord插件也会自动检测用户语言并使用此语言发送Discord消息， 它遵循QuickShop-Hikari的语言系统设置。
    __to_message_designer: '使用GUI设计您的Discord消息：https://glitchiii.github.io/embedbuilder/，然后复制JSON代码并粘贴到语言文件中，然后在这里开始！'
    discord-enabled: <aqua>成功 <green>启用</green> 您的商店 Discord 通知消息，现在您可以从 Discord 接收商店消息。
    discord-disabled: <aqua>成功 <red>禁用</red> 您的商店 Discord 通知消息，现在您将不再收到来自 Discord 的商店消息。
    discord-not-integrated: <red>您尚未链接您的 Discord 账户！请先链接您的 Discord 账户！
    feature-enabled-for-user: <aqua>您已 <green>启用</green> <gold>{0}</gold> 通知。
    feature-disabled-for-user: <aqua>您已禁用 <red></red> <gold>{0}</gold> 通知。
    link-help: <yellow>此服务器使用 <gold>{0}</gold> 处理 Discord 驱动，请使用 <green>{0}</green> 链接您的 Discord 账户。
    save-notifaction-exception: <red>保存您的 Discord 通知设置时发生错误，请与服务器管理员联系。
    feature-status-changed: <green>成功设置通知 <aqua>{0}</aqua> 状态为 <gold>{1}
    commands:
      discord:
        description: <yellow>管理QuickShop的Discord设置
    discord-messages:
      sold-to-your-shop: |
        {
         "embed":
           {
             "title": ":inbox_tray: 有人向你的商店出售物品",
             "description": "此玩家 %%purchase.name%% 出售 %%purchase.amount%% 个 %%shop.item.name%% 到你的商店.",
             "color": 52084,
             "author": {
               "name": "QuickShop-Hikari",
               "icon_url": ""
             },
             "thumbnail": {
               "url": ""
             },
             "image": {
               "url": ""
             },
             "footer": {
               "text": "QuickShop-Hikari Discord消息通知",
               "icon_url": ""
             },
             "fields": [
               {
                 "name": "商店",
                 "value": "%%shop.display-name%%",
                 "inline": false
               },
               {
                 "name": "玩家",
                 "value": "%%purchase.name%%",
                 "inline": true
               },
               {
                 "name": "物品",
                 "value": "%%shop.item.name%%",
                 "inline": true
               },
               {
                 "name": "数量",
                 "value": "%%purchase.amount%% (x%%shop.stacking-amount%%)",
                 "inline": true
               },
               {
                 "name": "您已支付",
                 "value": "%%purchase.balance-formatted%%",
                 "inline": true
               },
               {
                 "name": "税",
                 "value": "%%purchase.taxes-formatted%%",
                 "inline": true
               }
             ]
           }
        }
      bought-from-your-shop: |
        {
        "embed":
          {
              "title": ":outbox_tray: 有人在商店购买物品",
              "description": "此玩家 %%purchase.name%% 在你的商店购买了 %%purchase.amount%% 个 %%shop.item.name%% .",
              "color": 52084,
              "author": {
                  "name": "QuickShop-Hikari",
                  "icon_url": ""
              },
              "thumbnail": {
                  "url": ""
              },
              "image": {
                  "url": ""
              },
              "footer": {
                  "text": "QuickShop-Hikari Discord消息通知",
                  "icon_url": ""
              },
              "fields": [
                  {
                      "name": "商店",
                      "value": "%%shop.display-name%%",
                      "inline": false
                  },
                  {
                      "name": "玩家",
                      "value": "%%purchase.name%%",
                      "inline": true
                  },
                  {
                      "name": "物品",
                      "value": "%%shop.item.name%%",
                      "inline": true
                  },
                  {
                      "name": "数量",
                      "value": "%%purchase.amount%%(x%%shop.stacking-amount%%)",
                      "inline": true
                  },
                  {
                      "name": "收入",
                      "value": "%%purchase.balance-formatted%%",
                      "inline": true
                  },
                  {
                      "name": "税",
                      "value": "%%purchase.taxes-formatted%%",
                      "inline": true
                  }
              ]
          }
        }
      mod-shop-purchase: |
        {
        "embed":
          {
              "title": ":dollar: 有人在商店里购买",
              "description": "该玩家 %%purchase.name%% 在商店里购买了 %%purchase.amount%%个 %%shop.item.name%% 物品.",
              "color": 6061450,
              "author": {
                  "name": "QuickShop-Hikari",
                  "icon_url": ""
              },
              "thumbnail": {
                  "url": ""
              },
              "image": {
                  "url": ""
              },
              "footer": {
                  "text": "QuickShop-Hikari Discord消息通知",
                  "icon_url": ""
              },
              "fields": [
                  {
                      "name": "商店",
                      "value": "%%shop.display-name%%",
                      "inline": false
                  },
                  {
                      "name": "玩家",
                      "value": "%%purchase.name%%",
                      "inline": true
                  },
                  {
                      "name": "物品",
                      "value": "%%shop.item.name%%",
                      "inline": true
                  },
                  {
                      "name": "数量",
                      "value": "%%purchase.amount%% (x%%shop.stacking-amount%%)",
                      "inline": true
                  },
                  {
                      "name": "余额",
                      "value": "%%purchase.balance-formatted%%",
                      "inline": true
                  },
                  {
                      "name": "税",
                      "value": "%%purchase.taxes-formatted%%",
                      "inline": true
                  }
              ]
          }
        }
      out-of-space: |
        {
        "embed":
          {
              "title": ":mailbox_with_mail: 您的商店库存已不足",
              "description": "您的商店库存已满!\n您需要清理您的商店以释放空间.",
              "color": 16065893,
              "author": {
                  "name": "QuickShop-Hikari",
                  "icon_url": ""
              },
              "thumbnail": {
                  "url": ""
              },
              "image": {
                  "url": ""
              },
              "footer": {
                  "text": "QuickShop-Hikari Discord消息通知",
                  "icon_url": ""
              },
              "fields": [
                  {
                      "name": "商店",
                      "value": "%%shop.display-name%%",
                      "inline": false
                  }
              ]
          }
        }
      out-of-stock: |
        {
            "embed":
                {
                    "title": ":mailbox_with_no_mail: 您的商店已缺货",
                    "description": "您的商店库存已空!\n您需要在商店中重新补货以继续出售物品!",
                    "color": 16065893,
                    "author": {
                        "name": "QuickShop-Hikari",
                        "icon_url": ""
                    },
                    "thumbnail": {
                        "url": ""
                    },
                    "image": {
                        "url": ""
                    },
                    "footer": {
                        "text": "QuickShop-Hikari Discord消息通知",
                        "icon_url": ""
                    },
                    "fields": [
                        {
                            "name": "商店",
                            "value": "%%shop.display-name%%",
                            "inline": false
                        }
                    ]
                }
        }
      mod-shop-created: |
        {
          "embed": {
            "title": ":classical_building: 新商店已被创建",
            "description": "玩家在您的服务器中新建了一个商店!",
            "color": 6061450,
            "author": {
              "name": "QuickShop-Hikari",
              "icon_url": ""
            },
            "thumbnail": {
              "url": ""
            },
            "image": {
              "url": ""
            },
            "footer": {
              "text": "QuickShop-Hikari Discord消息通知",
              "icon_url": ""
            },
            "fields": [
              {
                "name": "商店",
                "value": "%%shop.display-name%%",
                "inline": false
              },
              {
                "name": "商店所有者",
                "value": "%%shop.owner.name%%",
                "inline": true
              },
              {
                "name": "物品",
                "value": "%%shop.item.name%%",
                "inline": true
              },
              {
                "name": "数量",
                "value": "%%shop.stacking-amount%%",
                "inline": true
              },
              {
                "name": "商店类型",
                "value": "%%shop.type%%",
                "inline": true
              }
            ]
          }
        }
      mod-remove-shop: |
        {
          "embed": {
            "title": ":recycle: 商店在服务器中已被删除",
            "description": "玩家在服务器中删除了一个商店.",
            "color": 6061450,
            "author": {
              "name": "QuickShop-Hikari",
              "icon_url": ""
            },
            "thumbnail": {
              "url": ""
            },
            "image": {
              "url": ""
            },
            "footer": {
              "text": "QuickShop-Hikari Discord消息通知",
              "icon_url": ""
            },
            "fields": [
              {
                "name": "商店",
                "value": "%%shop.display-name%%",
                "inline": false
             },
                    {
                        "name": "理由",
                        "value": "%%delete.reason%%",
                        "inline": false
                    }
                ]
            }
        }
      shop-transfer-to-you: |
        {
            "embed": {
                "title": ":placard: 一个商店被转让给你",
                "description": "一个商店从其他玩家那里转让给你.",
                "color": 16172079,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord消息通知",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "商店",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "转让来自",
                        "value": "%%transfer.from%%",
                        "inline": false
                    }
                ]
            }
        }
      mod-shop-transfer: |
        {
            "embed": {
                "title": ":placard: 商店已被转让",
                "description": "商店从一个玩家转让到了其他玩家.",
                "color": 6061450,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord消息通知",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "商店",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "转让来自",
                        "value": "%%transfer.from%%",
                        "inline": true
                    },
                    {
                        "name": "转让给",
                        "value": "%%transfer.to%%",
                        "inline": true
                    }
                ]
            }
        }
      shop-price-changed: |
        {
            "embed": {
                "title": ":money_with_wings: 您的商店价格已被修改",
                "description": "您或者是您的商店助手修改了商店价格.",
                "color": 16172079,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord消息通知",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "商店",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "原价格",
                        "value": "%%change-price.from%%",
                        "inline": true
                    },
                    {
                        "name": "现价格",
                        "value": "%%change-price.to%%",
                        "inline": true
                    }
                ]
            }
        }
      mod-shop-price-changed: |
        {
            "embed": {
                "title": ":money_with_wings:商店价格已被修改",
                "description": "商店修改了价格",
                "color": 6061450,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord消息通知",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "商店",
                        "value": "%%shop.display-name%%",
                        "inline": true
                    },
                    {
                        "name": "商店所有者",
                        "value": "%%shop.owner.name%%",
                        "inline": true
                    },
                    {
                        "name": "",
                        "value": "",
                        "inline": true
                    },
                    {
                        "name": "原价格",
                        "value": "%%change-price.from%%",
                        "inline": true
                    },
                    {
                        "name": "现价格",
                        "value": "%%change-price.to%%",
                        "inline": true
                    }
                ]
            }
        }
      shop-permission-changed: |
        {
            "embed": {
                "title": ":closed_lock_with_key: 商店权限设置已被更改",
                "description": "您的一个商店的权限设置已被更改.",
                "color": 15879747,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord消息通知",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "商店",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "玩家",
                        "value": "%%change-permission.player%%",
                        "inline": true
                    },
                    {
                        "name": "分配给组",
                        "value": "%%change-permission.to-group%%",
                        "inline": true
                    },
                    {
                        "name": "",
                        "value": "",
                        "inline": true
                    },
                    {
                        "name": "授予权限(继承于组)",
                        "value": "```\n%%change-permission.perms-list%%\n```",
                        "inline": false
                    }
                ]
            }
        }
    field:
      player: 玩家
      item: 物品
      amount: 数量
      balance: 余额
      balance-after-tax: 余额 (税后)
      account: 账户余额
      taxes: 税
      cost: 价格
  discount:
    commands:
      discount:
        description: <yellow>应用优惠代码或管理您的优惠代码
    tab-complete:
      discount:
        general:
          code: \<code>
        create:
          rate: |
            命令提示：
            参数： \<rate>
            描述：您将会得到的实际百分比或金钱
            输入 `30%` = 价格 * 0.3
            输入 `50` = 价格 - 50
          max-usage: |
            命令提示：
            参数： [max-usage]
            描述：可以对无限代码使用
            `-1` 为无限
          threshold: |
            命令提示：
            参数： [threshold]
            说明：代码可以应用的最低价格
            “-1”表示无限
          expired: |
            命令提示
            参数： [expired]
            说明：代码过期时间。
            `-1`无限。
            接受 Zulu 时间和 UNIX 时间戳（以秒为单位）。
            Zulu 示例：2022-12-17T10:31:37Z
            UNIX 示例：1671273097
    discount-code-already-exists: <red>抱歉，您的优惠码名称已被使用。
    invalid-discount-code-regex: '<red>优惠码必须匹配正则表达式： <yellow>{0}'
    invalid-discount-code: <red>优惠码无效。
    discount-code-added: <green>您的优惠码 <yellow>{0}</yellow> 已被添加到商店 <aqua>{1}
    discount-code-removed: <green>您的优惠码 <yellow>{0}</yellow> 已从商店删除 <aqua>{1}
    invalid-code-type: <red>代码类型 <yellow>{0}</yellow> 无效。
    invalid-usage-restriction: <red>使用限制 <yellow>{0}</yellow> 无效。
    invalid-threshold-restriction: <red>阈值限制 <yellow>{0}</yellow> 无效。
    invalid-effect-scope: <red>范围 <yellow>{0}</yellow> 无效。
    invalid-expire-time: <red>不能指定过去的时间。
    invalid-discount-rate: <red>折扣率 <yellow>{0}</yellow> 无效，它可以是固定数字或百分比。
    discount-code-expired: <red>哎呀！您的优惠码 <yellow>{0}</yellow> 已过期！
    discount-code-installed: <green>您已安装优惠码 <gold>{0}</gold>, 优惠码将自动应用于本次会话期间所有可用的交易。 要卸载优惠码，请执行 <aqua>/quickshop discount uninstall {0}</aqua>。
    discount-code-uninstalled: <green>您已卸载您的优惠码。
    discount-code-query-nothing: <red>您尚未安装优惠码！
    discount-code-query: '<green>您正在使用优惠码： <yellow>{0}</yellow>。'
    discount-code-applicable: '<#bcef26>您的优惠码 <bold><yellow>{0}</yellow></bold> <bold>适用于</bold> 这家商店！'
    discount-code-applicable-with-threshold: '<#bcef26>您的优惠码 <bold><yellow>{0}</yellow></bold> <bold>适用于此商店的</bold> ，但仅在单笔交易中您的购买成本超过 <yellow>{1}</yellow> 。'
    discount-code-not-applicable: <red>您的优惠码 <bold><yellow>{0}</yellow></bold> <bold>不适用于</bold> 这家商店！
    discount-code-reach-the-limit: <red>您已达到优惠码的使用限制 <bold><yellow>{0}</yellow></bold>，则不应用折扣。
    discount-code-no-permission: <red>您没有权限在此商店使用任何优惠码！
    discount-code-has-been-expired: <red>您的折扣已过期！
    discount-code-config-shop-added: <green>成功地将此商店添加到您的优惠码允许列表。
    discount-code-config-shop-add-failure: <red>该商店已存在于您的优惠码允许列表
    discount-code-config-shop-removed: <green>成功地将此商店从您的优惠码允许列表中删除。
    discount-code-config-shop-remove-failure: <red>此商店不存在于您的优惠码允许列表。
    discount-code-config-expire: <green>成功地更改了优惠码过期时间。
    discount-code-config-applied: <green>成功地配置优惠码！
    discount-code-created-successfully: |
      <green>您的优惠码 <yellow>{0}</yellow> 已成功地创建！
      <gold>范围：<aqua>{1}</aqua></gold>.
      <gold>与他人分享：<#bcef26>{2}</#bcef26></gold>
      <yellow>只适用于 <gray>特定商店</gray> 范围：将商店添加到您的优惠码允许列表，请看着一个商店并执行 <aqua>{3}</aqua> 命令。
      您可以随时使用 <aqua>/quickshop discount config {0}</aqua> 编辑您的优惠码。
    discount-code-under-threshold: <red>由于总价值低于优惠码阈值，所以优惠未适用于您的交易 <yellow>{0}</yellow>。
    percentage-off: '<bold><#bcef26>-{0}%'
    fixed-off: '<bold><#bcef26>-{0}'
    discount-code-list: '<gold>列出您的优惠码：'
    discount-code-applied-in-purchase: '<#bcef26>您的优惠码 <yellow>{0}</yellow> 已经应用到此交易，并且您节省了 <gold>{1}</gold>！'
    scope:
      this-shop: '{0}'
      your-shops-owned: 您的所有商店（所有）
      your-shops-managed: 您的所有商店（已管理）
      server: 本服务器
    code-type:
      SERVER_ALL_SHOPS: 此服务器上的所有商店
      PLAYER_ALL_SHOPS: 由优惠码所有者创建的所有商店
      SPECIFIC_SHOPS: 特定商店
    discount-code-details: |-
      <gold>优惠码: <yellow>{0}</yellow>
      创建者: <yellow>{1}</yellow>
      应用于: <yellow>{2}</yellow>
      剩余使用量: <yellow>{3}</yellow>
      过期时间: <yellow>{4}</yellow>
      阈值: <yellow>{5}</yellow>
      折扣: <yellow>{6}</yellow><br>
  list:
    commands:
      list: <yellow>列出自己或其他玩家拥有的所有商店
    table-prefix: '<yellow><green>{0}</green>的商店 <gray>(总计 {1})</gray>: '
    table-prefix-pageable: '<yellow><green>{0}</green>的商店 <gray>（页面 {1}/{2})</gray>: '
    entry: <yellow><hover:show_text:'<yellow>{1}</yellow><br><gray>{2} {3},{4},{5}</gray><br><aqua>价格 <yellow>{6}</yellow> - <light_purple>x{7}</light_purple> <yellow>{8}</yellow><br><green>{9}'>{0}. <aqua>{1}
  shopitemonly:
    message: <red>您不能将非商店交易物品放入商店容器中，所有非商店交易的物品都会被扔到地上
  limited:
    commands:
      limit: <yellow>限制玩家在时间段内的交易
    titles:
      title: <green>交易成功
      subtitle: <aqua>您还可以在这个商店交易 <gold>{0}</gold> 个物品
    reach-the-limit: <red>您与这个商店已达到交易限制。您还可以交易 <green>{0}</green> 个物品，但您正在尝试交易 <yellow>{1}</yellow> 个物品。
    success-reset: <green>成功重置这个商店的限制
    success-remove: <green>成功删除此商店的所有限制
    success-setup: <green>该商店的限制设置已成功保存
    trade-limit-reached-cancel-reason: <red>达到了这个商店的交易限制
    remains-limits: '<gold>您在该商店剩余的可交易数量： <yellow>{0}'
    remains-limits-before-reset-period-note: '<gold>该商店将重置玩家们的可交易数量： <yellow>{0}'
  dynmap:
    markerset-title: QuickShop-Hikari 商店
    marker-name: '{0} @ {2} @ {3} x{4} @ {5}'
    marker-description: |
      名称: {0}
      所有者: {1}
      物品: {2}
      价格: {3} 个{4} 物品
      类型: {5}
      无限模式: {6}
      位置: {7}
  bluemap:
    markerset-title: QuickShop-Hikari 商店
    marker-name: '{0} @ {2} @ {3} x{4} @ {5}'
    marker-description: |
      名称: {0}
      所有者: {1}
      物品: {2}
      价格: {3} 个{4} 物品
      类型: {5}
      无限模式: {6}
      位置: {7}
  chestprotect:
    protection-exists: <red>这个区域已经受到ChestProtect的保护，你没有权限在这里创建商店。
    shops-exsts: <red>您要保护的区域包含其他玩家的商店，并且您无权访问它们
  displaycontrol:
    toggle: |-
      <green>成功切换您的商店显示到 <aqua>{0}</aqua>。
      <yellow>您可能需要重新加入才能生效。
    toggle-exception: <red>由于内部错误，无法切换您的悬浮物显示状态，请联系服务器管理员。
    command:
      displaycontrol: <yellow>切换您的 QuickShop 悬浮物显示
  reremake-migrator:
    commands:
      migratefromreremake: 将 QuickShop-Remake 的数据迁移到 QuickShop-Hikari
    server-not-empty: "<red>在转换过程中不允许玩家在服务器中。请启用服务器白名单或维护模式。"
    starting-convert-progress: "<gold>开始转换， <red>不要关闭服务器！</red>"
    executing: "<gold>正在执行转换组件 <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
    completed: "<green>完成！转换成功，请从插件目录中删除 QuickShop-Reremake 并重新启动您的服务器 。"
    join_blocking_converting: "<red>[QuickShop-Hikari 转换器] 该服务器正在进行数据转换，您现在不能加入服务器，请稍后再试！"
    join_blocking_finished: "<red>[QuickShop-Hikari 转换器] 该服务器刚刚完成数据转换，正在等待重启以应用更改，请稍后再试 ！"
    failed: "<red>转换过程因错误而退出，请检查服务器控制台。"
    modules:
      config:
        copy-values: "<yellow>正在复制值 (总计 {0} 条目)..."
        copying-value: "<gray> - 复制 <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        migrate-price-restriction: "<yellow>转换与价格限制相关的设置..."
        migrate-price-restriction-entry: "<gray> - 转换 <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
      shop:
        start-migrate: "<yellow>转换商店(总计 {0} 条目)..."
        migrate-entry: "<gray> - 转换 <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        unloading-reremake: "<gold>正在卸载 Reremake 以避免数据覆盖..."
        register-entry: "<gray> -注册 <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        save-entry: "<gray> - 保存 <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        saving-shops: "<yellow>正在保存 <gold>{0}</gold> 商店，这可能需要一段时间™（取决于商店数量）..."
        conflict: "<gray> -冲突 > 检测 Reremake 商店与现有 Hikari 商店位置之间的冲突，采取预先定义的行为: <dark_gray>{0}</dark_gray>"
      translation:
        start-migrate: "<yellow>正在迁移翻译文件...</yellow>"
        copy-values: "<yellow>在语言文件中复制值 <gold>{0}</gold> （总计 {1} 条目）..."
        migrate-entry: "<gray> - 转换 <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        copying-value: "<gray> - 复制 <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
      shop-logs:
        start-migrate: "<yellow>正在迁移商店日志（详情请检查控制台），请稍候...</yellow>"
        extract-history-files: "<gray>请等待解压并追加历史日志..."
        filter-history-files: "<gray>请等待我们筛选历史日志..."
        filtered-history-files: "<gray>从队列中筛选出 {0} 行。"
        import-entry: "<gray> - 转换 <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
compat:
  advancedchests:
    created: <green>您已成功在 AdvancedChests 里创建了一个商店！
    permission-denied: <red>抱歉！您没有权限在AdvancedChests上创建商店！
suite:
  dynmap:
    title: '{0} - {1} - {2}'
    description: |-
      物品: {0}
      所有者: {1}
      类型: {2} {3}
      价格: {4}
      位置: {5} {6}, {7}, {8}
      空间: {9}
      库存: {10}
  limited:
    command-description: <yellow>限制玩家在时间段内的交易。
    reach-the-quota-limit: <red>您已达到该商店的交易限制（{0}/{1}）。
    quota-reset-countdown: <yellow>此商店的交易限制将在 {0} 重置。
    quota-reset-player-successfully: <green>已成功重置该商店中玩家 {0} 的交易限制
    quota-reset-everybody-successfully: <green>已成功地重置此商店中所有人的交易限制。
    quota-setup: <green>该商店的交易限制已设置成功！
    quota-remove: <green>该商店的交易限制已成功重置！
    subtitles:
      title: <green>交易成功
      subtitle: <aqua>您还可以在这个商店交易 <yellow>{0}</yellow> 个物品
  list:
    command-description: <yellow>列出自己或其他玩家拥有的所有商店
    table-prefix: <yellow>您在此服务器中拥有 <aqua>{0}</aqua> 个商店。
    entry-syntax: <aqua>- <yellow>{0}
    entry-syntax-fallback-name: '<yellow>物品:{0} X:{1}, Y:{2}, Z:{3}, 世界: {4}'
    hover-text: |-
      <yellow>{0}
      <gray>{1} {2}、 {3}、 {4}
      <aqua>{5} - <yellow>{6}
      {7} 物品
  shopitemonly:
    message: <red>您不能将非商店交易物品放入商店容器中，所有非商店交易的物品都会被扔到地上
compatibility:
  elitemobs:
    soulbound-disallowed: 你不能交易拥有 EliteMobs 灵魂绑定附魔的物品。
internet-paste-forbidden-privacy-reason: "<red>失败！根据您的隐私设置，QuickShop-Hikari不允许将您的Paste上传到互联网，在配置文件中启用DIAGNOSTIC配置项。 或使用 <aqua>/quickshop paste --file</aqua> 代替。"
no-sign-type-given: "<red>你需要给出牌子类型，该服务器上可用的牌子类型： {0}"
sign-type-invalid: "<red><yellow>{0}</yellow>不是有效的牌子类型。"
delete-controlpanel-button-confirm: "<red>您真的想要删除这家商店吗？再次点击 <bold>[删除商店]</bold> 按钮并在 {0} 秒内确认。"
cannot-suggest-price: "<red>对不起，暂时没有更多的玩家与您交易相同的物品，没有足够的数据来生成建议的价格。"
price-suggest: "<green>基于来自 <aqua>{0}</aqua> 商店的数据 最高价格的商店价格为 <light_purple>{1}</light_purple>价格最低的商店价格为 <light_purple>{2}</light_purple>平均价格为 <light_purple>{3}</light_purple>，中位价格为 <light_purple>{4}</light_purple> <newline><yellow>建议您将价格设置在 <gold>{5}</gold>。</yellow>"
suggest-wait: "<green>请稍候... 计算推荐价格。"
history:
  shop:
    gui-title: "查看交易记录"
    header-icon-multiple-shop: "<white>查询结果 {0} 商店</white>"
    header-icon-description:
      - "<white>类型： <yellow>{0}</yellow></white>"
      - "<white>所有者: <yellow>{1}</yellow></white>"
      - "<white>物品： <yellow>{2}</yellow></white>"
      - "<white>价格： <yellow>{3} <aqua>x{4}</aqua></yellow></white>"
      - "<white>位置： <yellow>{5}</yellow></white>"
    log-icon-title: "<reset><green>时间： {0}</green>"
    log-icon-description:
      - "<white>交易者: <yellow>{0}</yellow></white>"
      - "<white>物品： <yellow>{1} <aqua>x{2}</aqua></yellow></white>"
      - "<white>余额： <yellow>{3}</yellow></white>"
      - "<white>税： <yellow>{4}</yellow></white>"
    log-icon-description-with-store-name:
      - "<white>商店： <yellow>{0}</yellow></white>"
      - "<white>交易者: <yellow>{1}</yellow></white>"
      - "<white>物品： <yellow>{2} <aqua>x{3}</aqua></yellow></white>"
      - "<white>余额： <yellow>{4}</yellow></white>"
      - "<white>税： <yellow>{5}</yellow></white>"
    query-icon: "<gray>请稍候，正在查询...</gray>"
    previous-page: "<white><< 前一页</white>"
    next-page: "<white>下一页 >></white>"
    current-page: "<white>页面 {0}</white>"
    summary-icon-title: "<green>商店摘要"
    recent-purchases: "<white>最近 <aqua>{0}</aqua> 购买： <yellow>{1}</yellow></white>"
    recent-purchase-balance: "<white>最近 <aqua>{0}</aqua> 营业额： <yellow>{1}</yellow></white>"
    total-purchases: "<white>交易总数： <yellow>{0}</yellow></white>"
    total-balances: "<white>总营业额： <yellow>{0}</yellow></white>"
    total-unique-purchasers: "<white>交易者总数： <yellow>{0}</yellow></white>"
    top-n-valuable-customers-title: "<gold>前 {0} 个最有价值客户</gold>"
    top-n-valuable-customers-entry: "<white>- <yellow>{0}</yellow> <aqua>{1}</aqua></white>"
    no-result: "<white>没有结果</white>"
about:
  text:
    - "<b><aqua>QuickShop <yellow>{0}</yellow></aqua></b>"
    - "<aqua>版本 <yellow>>></yellow> <green>{1}</green></aqua>"
    - "<aqua>构建 <yellow>>></yellow> <green>{2}</green></aqua>"
    - "<aqua>开发者 <yellow>>></yellow> <color:#218bff><hover:show_text:'<yellow>点击查看贡献者'><click:open_url:'https://github.com/Ghost-chu/QuickShop-Hikari/graphs/contributors'>[在 GitHub 上查看贡献者]</click></hover></color></aqua>"
    - "<aqua>本地化成员 <gold>({4})</gold>: <yellow>>></yellow> <green>{5}</green></aqua>"
    - "  <yellow>+ <color:#43A047><hover:show_text:'<yellow>点击打开 Crowdin 翻译页面'><click:open_url:'https://crowdin.com/project/quickshop-hikari'>[在 Crowdin 上帮助翻译]</click></hover></color></yellow>"
    - "<aqua>捐赠密钥 <yellow>>></yellow> <green>{6}</green></aqua>"
    - "<gold>由社区提供支持</gold> <red>用 ❤</red>"
  valid-donation-key: "<color:#00AFF1>绑定到 <color:#F9C23C>{0}</color></color>"
  invalid-donation-key: "<gray>无效的捐赠密钥</gray>"
  kofi-thanks: "<gold>特别感谢那些在 Ko-Fi 上支持 QuickShop 的人 :)</gold>"
history-command-leave-blank: "参数为空时查询所指向的商店"
shop-information-not-shown-due-an-internal-error: "<red>发生内部错误。商店信息面板可能显示不完整，请与服务器管理员联系。"
