# QuickShop-Hikari Plugin Configuration

# Do not touch this if you don't know what you're doing!
config-version: 1029

# Set the default language code the plugin should use
# Set it to default will use your system language.
# You can find the valid language code in your client language settings, like en_us
game-language: default

# Choose which languages should be enabled
# Any client connect to server that using disabled language, will fallback to game-language option there
# Set to - '*' to enable all available languages
# The language files will automatically update thorough Crowdin OTA system, you can translate it there:
# https://crowdin.com/project/quickshop-hikari
# If you want custom the language file, use language override system:
# https://ghost-chu.github.io/QuickShop-Hikari-Documents/docs/modules/localization
enabled-languages:
- '*'

# Setting the game language file download channel, like item name, effect translations etc.
# We will get those files from mojang resources server:
# 0=Official
# 1=BMCLAPI (Unofficial China Mirror, https://bmclapidoc.bangbang93.com)
# 2=MCBBSMirror (Another OpenBMCLAPI Mirror, https://www.mcbbs.net)
# You can choose which one you want to use for downloading resources.
# By default, QuickShop use Mojang official server to downloading resources.
mojangapi-mirror: 0

# This enables the developer mode, do not touch this if you don't not know what it does!
dev-mode: false

# Tax amount (decimal)
# Example: P1 buys $50 worth of stuff from P2.  Therefore, P1 loses $50, P2 gains $(1-0.05)*50, and the tax-account gains $(0.05)*50.
tax: 0.05

# The fake player where the tax money goes to.
# Set this to "" to disable it (Taxing will still work but not deposit to any account)
tax-account: tax

# Disable taxes for unlimited shop
tax-free-for-unlimited-shop: false

# Turn on this option will migrate shop owner to specified account when you set a shop to unlimited.
# It won't affect exists shops, you will need switch to unlimited again to apply it.
unlimited-shop-owner-change: false

# Set the account used by unlimited-shop-owner-change option
# Only take effect when unlimited-shop-owner-change enabled
unlimited-shop-owner-change-account: quickshop

# Whether to show taxes paid when selling to a shop
show-tax: false

# Whether to respect item flags (e.g. hide the enchantment info when item have HIDE_ENCHANTS flag).
respect-item-flag: true

# Should we try to send cross-server message for the player?
# It could cause the kicking on some network (which have multiple server that using different minecraft versions)
bungee-cross-server-msg: false

# The currency used to create shops.
# Set this to "" to use default currency.
# Only required if you use multiple currencies.
currency: ''

logging:
  # Enable logging?
  # The log file is located at "plugins/QuickShop/qs.log".
  enable: true
  # Should QuickShop log shop trading/creations?
  log-actions: true
  # Should QuickShop log balance before and after when trading?
  log-balance: true
  # The target size (in MB) to split log files to the "plugins\QuickShop\logs" folder.
  file-size: 10.0
  # Log data storage location (0 to file, 1 to database)
  location: 1

# Force bukkit chat handler (https://github.com/KaiKikuchi/QuickShop/issues/10)
force-bukkit-chat-handler: false

# Should QS be allowed to check for updates on SpigotMC.org?
updater: true

# Should QS be allowed to automatically report errors to the author?
auto-report-errors: true

# Should QS use OfflinePlayer list for TabComplete?
# This might improve player experience, but may cause lag due to searching what can be a long list.
# false = Only use the OnlinePlayer list.
# true = A list of all players that have ever joined your server.
include-offlineplayer-list: false

# What economy provider should QuickShop use?
# 0=Vault
# 3=GemsEconomy with Multi-Currency on
# 4=TNE with Multi-Currency on
# DO NOT TOUCH THIS IF YOU DON'T KNOW WHAT IT DOES. ASK FOR SUPPORT BEFORE TOUCHING THIS!
economy-type: 0

# Whether to use decimal format to display money.
use-decimal-format: false

# The decimal format to display money in.
# The default is #,###.##
decimal-format: '#,###.##'

# Allow QuickShop to send alerts when someone tries to make a displayItem exploit.
send-display-item-protection-alert: false

# Allow QuickShop to send alerts when someone tries to steal from a shop.
send-shop-protection-alert: false

# Still WIP
server-platform: 0

# Select which chat system QuickShop should use.
# 0=BungeeChat (Spigot default)
# 1=Adventure (Removed In Current Version)
chat-type: 0

# MySQL database settings.
database:
  # false = use local H2 database.
  # true = use local/remote MySQL database.
  mysql: false
  # The database address. (Only required if mysql is true)
  host: localhost
  # The database port. (Only required if mysql is true)
  port: 3306
  # The database names. (Only required if mysql is true)
  database: quickshop
  # The database username. (Only required if mysql is true)
  user: root
  # The database password. (Only required if mysql is true)
  password: passwd
  # Set prefix to "none" to remove prefix (Both local and remote will be used).
  prefix: qs_
  # Should QuickShop use SSL for database connections?  (Only required if mysql is true)
  usessl: false
  # Properties for creating connections, you can add your own properties for datasource here. (Both local and remote will be used).
  properties:
    idle-timeout: 60000
    maxLifeTime: 60000
    cachePrepStmts: true
    prepStmtCacheSize: 250
    prepStmtCacheSqlLimit: 2048
    useUnicode: true
    characterEncoding: utf8
    allowPublicKeyRetrieval: true
    connectionTimeout: 60000
    maximumPoolSize: 10
    minimumIdle: 10

# Limits the amount of shops a player can create and own.
limits:
  # Leave this as false if you're not using this feature! QuickShop will ignore the rest of this section.
  use: false
  
  # The default amount of shops a player can create.
  default: 10
  
  # Use old algorithm to calculate unlimited shops into limits.
  old-algorithm: false
  
  # Here you can bind the limit to specific permissions.
  # Using the same format, you can add as many as you want!
  ranks:
    quickshop:
      # Players with quickshop.example permission can create 20 shops.
      example: 20

# The list of blocks that can be used to create shops.
# By default, chests are added to this list.
# This will only work for blocks that implement an InventoryHolder!
# In other words, You cannot create shops with normal blocks like dirt or stone.
# May cause unexpected behavior with some blocks...eg:
# -Hopper shops suck the display item in,
# -Furnace shops allow players whatever item they want in all 3 slots,
# -Dispenser shops aren't protected from redstone,
# -Etc...
shop-blocks:
- CHEST
- TRAPPED_CHEST
- ENDER_CHEST
- BARREL

shop:
  # The cost to make a shop.
  cost: 10
  
  # Should QS refund the player when their shops are deleted/removed/broken?
  refund: false
  
  # Should QS refund from the tax-account?
  # refund is as much as possible, player may won't get all refund if the tax-account had no enough money.
  refund-from-tax-account: false
  
  # Should QS send stock messages to staffs?
  sending-stock-message-to-staffs: false
  
  # Should QS disable trading in creative mode?
  disable-creative-mode-trading: false
  
  # Should QS disable the super tool?
  # The Super Tool is a Golden Axe that allows creative mode players to break shops.
  disable-super-tool: false
  
  # Should QS allow the owner of a shop to break the sign to let players replace the sign?
  # This may cause a sign dupe when auto-sign is true!
  allow-owner-break-shop-sign: false
  
  # Should there be a fee for changing shop prices?
  # This can help with endless price undercutting.
  price-change-requires-fee: true
  # The amount of this fee.
  fee-for-price-change: 50
  
  # Should QS try to lock shops for other players?
  # This prevents players from stealing from them.
  lock: true
  
  # Should QS disable the quick creation of shop?
  # To quickly create a shop click a chest with an Item.
  disable-quick-create: false
  
  # Should we automatically create the sign for the chest?
  auto-sign: true
  
  # Sign use glowing effects
  sign-glowing: false
  
  # Sign use dye color  https://hub.spigotmc.org/javadocs/spigot/org/bukkit/DyeColor.html
  # sign-dye-color: WHITE
  sign-dye-color: ''
  
  # Should we pay/take money to/from unlimited shops owners?
  pay-unlimited-shop-owners: false
  
  # Should we place display items on the chests?
  # This may cause duped items, especially for modded servers if you're using Real DisplayItem!
  display-items: true
  
  # QuickShop will check if the display item is in a valid position every specified amount of ticks.
  # Set to 0 to disable it.
  # It doesn't work under Virtual DisplayItem.
  display-items-check-ticks: 6000
  
  # The display type you want use.
  # Old mode, ArmorStand (display-type=1) is Outdated and could not be used
  # 0=Normal Dropped Item
  # 2=Virtual Item (Requires ProtocolLib, fallback to type 0 if ProtocolLib is not installed)
  display-type: 2
  
  # Allow QuickShop to automatically despawn displays when no players are in range of the shop.
  display-auto-despawn: false
  
  # The range at which displays will despawn.
  display-despawn-range: 20
  
  # The interval (in ticks) at which QS checks the shops range to despawn/spawn displays.
  # It doesn't work under Virtual DisplayItem.
  display-check-time: 40
  
  # Allow displays to show stack effects when stack creation is turned on.
  # It doesn't work under Virtual DisplayItem.
  display-allow-stacks: false
  
  # [BETA] Enable beta per-player shop sign feature for i18n.
  # Hikari will send an extra sign content packet to override original shop info sign text to the text that use player's locale
  # ProtocolLib Required
  per-player-shop-sign: false
  
  # /quickshop find <itemName> command settings.
  # This command lets users shop quickly without wasting time searching.
  finding:
    # Should QS use the old logic? (looking for closest shop and let players look at it)
    oldLogic: false
    # How far should QS search? (in blocks)
    distance: 45
    # How many nearby shop should be displayed?
    limit: 10
    # Should non-loaded shops be incuded in the search?
    all: true
    # Should QS exclude out-of-stock/space shops?
    # This may improve performance.
    exclude-out-of-stock: false
  
  # If vault doesn't return a currency symbol, QuickShop will use this symbol.
  alternate-currency-symbol: $
  
  # This setting is for multi-currency setups.
  # If nothing matches in this list, it falls back to the name of currency itself.
  alternate-currency-symbol-list:
  - USD;$
  - CNY;￥
  
  # This should fix Vault or an economy plugin incorrectly processing the price and returning a "0" result.
  # If you enable this setting, then QS will force use the "alternate-currency-symbol".
  disable-vault-format: false
  
  # If the currency symbol should be displayed on the right side (eg 1234$).
  # By default, it is on the left side (eg $1234).
  # Only works when disable-vault-format is true.
  currency-symbol-on-right: false
  
  # Ignore trade messages if the shop is unlimited.
  ignore-unlimited-shop-messages: true
  
  # Allow auto fetching of player shops.
  # If you disable this, then the players need to use "/quickshop fetchmessage" to fetch the shops history messages.
  # Disabling it may cause database issues!
  auto-fetch-shop-messages: true
  
  # Ignore cancelled chat event, can sometimes improve compatibility with some chat plugins.
  ignore-cancel-chat-event: false
  
  # Allow shops to be created in places where a sign cannot be placed?
  # (e.g There are blocks on all sides of the shop / The player doesn't have permission to put down a sign at that location)
  allow-shop-without-space-for-sign: false
  
  # The maximum number of digits after the decimal.
  # Set this to -1 to disable it. (Unlimited maximum digits)
  maximum-digits-in-price: -1
  
  # If a player that has quickshop.setowner permission changes the owner of an adminshop,
  # should the owner's uuid get printed in the control panel?
  show-owner-uuid-in-controlpanel-if-op: false
  
  # The wall sign material.
  # ACACIA_WALL_SIGN
  # BIRCH_WALL_SIGN
  # DARK_OAK_WALL_SIGN
  # JUNGLE_WALL_SIGN
  # OAK_WALL_SIGN
  # SPRUCE_WALL_SIGN
  sign-material: OAK_WALL_SIGN
  
  # If this is enabled, the enchantment name will be shown
  # in the sign instead of "Enchanted Book".
  # NOTE: Only one enchantment is shown and is randomly chosen.
  use-enchantment-for-enchanted-book: false
  
  # If this is enabled, the effect name will be shown
  # in the sign instead of "Potion".
  # NOTE: Only one effect is shown and is randomly chosen.
  use-effect-for-potion-item: false
  
  # The list of worlds in which creating new shops is disabled.
  blacklist-world:
  - disabled_world_name
  
  # The list of lore keywords that are blocked from being sold.
  # This will not affect existing shops!
  blacklist-lores:
  - SoulBound
  
  # Protection check
  # This will send a FAKE BlockBreakEvent to check if you can break a block.
  # This may conflict with some protection plugins!
  # Integration checking will be checked after this, so disable it when it's conflicting with integration
  # If you don't want this, then please disable it and use the integration below instead.
  protection-checking: true
  
  # Cancel the fake BlockBreakEvent before it passed to MONITOR level listeners.
  cancel-protection-fake-event-before-reach-monitor-listeners: true
  
  # The list of worlds in which protection checking is disabled.
  protection-checking-blacklist:
  - disabled_world
  
  # The listener list to skip when protection-checking.
  # Examples:
  # - xx.xxx.xxx.xxxxx.BlockBreakListenerClass
  #  This will skip calling the xx.xxx.xxx.xxxxx.BlockBreakListenerClass listener.
  #
  # - xx.xx.xxx.xxxxx
  #  This will skip calling all listener that class name start with xx.xx.xxx.xxxxx.
  #
  # - @PluginName
  #  This will skip calling all listeners registered by this plugin.
  #
  # - REGEX
  #  It also supports regex.
  protection-checking-listener-blacklist:
  - ignored_listener
  
  # How many shops should at max be checked at once for existance?
  max-shops-checks-in-once: 100
  
  # Add display name to the spawned DisplayItem entity?
  display-item-use-name: false
  
  # Update the signs when something (eg Hoppers) triggers InventoryMoveItemEvent?
  # Disable this if you think that it impacts your server performance.
  update-sign-when-inventory-moving: false
  
  # Allow player loans? (Requires an economy plugin with support)
  allow-economy-loan: false
  
  # The word to trade all items in player inventory.
  word-for-trade-all-items: all
  
  # Sets the name cost
  name-fee: 0
  # Sets the max length for shop naming
  name-max-length: 32
  
  # The shop ongoing fee.
  # If the shop owner doesn't have enough money, the shop will automatically be removed.
  ongoing-fee:
    enable: false
    # How often should the ongoing fee be due for payment?
    ticks: 42000
    # How much should the ongoing fee be?
    cost-per-shop: 2
    # Should we ignore unlimited shops?
    ignore-unlimited: true
  
  # Allow QuickShop to use a hack util to force-load shops from higher Minecraft versions.
  # WARNING: This may destroy your server data and QuickShop shops, backup before enabling it!
  force-load-downgrade-items:
    enable: false
    # WorkMode
    # 0=Call Bukkit to try to update the ItemStack
    # 1=Call Bukkit to directly load the ItemStack
    method: 0
  
  # If a block/entity has trigged the protection, should QuickShop break/kill it?
  remove-protection-trigger: true
  
  # Allow QuickShop to sell/buy multiple items in one transaction?
  allow-stacks: false
  
  # This setting controls if QuickShop should ignore the item's custom display name.
  force-use-item-original-name: false
  
  # [BETA] Disable the quickshop /quickshop size command stack size check to allow stack size go up the vanilla limit
  disable-max-size-check-for-size-command: false
  
  # Use shop cache if possible to improve shop lookup performance
  use-cache: true
  info-panel:
    show-enchantments: true
    show-effects: true
    show-durability: true
# List of items that can't be sold in shops.
# Anyone with the quickshop.bypass.<itemID> permission can bypass it.
# Add the reference the item lookup table by adding @ before the name.
# For referenced item, the permission quickshop.bypass.reference will be used.
# SUPPORT Item Expression!
# https://ghost-chu.github.io/QuickShop-Hikari-Documents/docs/modules/item-expression
blacklist:
- Bedrock

# Enable or disable plugin support.
plugin:
  # If you have Multiverse-Core, QuickShop will try to load worlds when the shop's world doesn't exist.
  # This will throw an exception and automatically repair the shops.
  Multiverse-Core: true
  # If you have PlaceHolderAPI, QuickShop will use it to process all messages.
  PlaceHolderAPI:
    enable: true
    # The PAPI request caching expire time. (ms)
    # Low value may increase the database load and hurt server performance.
    cache: 900000
  # If you have WorldEdit, QuickShop will try migrating shops in EditSession. [BETA]
  WorldEdit: true
# Special in-game effect
effect:
  # Sound related
  sound:
    # Should we play sound while player trying tab completing?
    ontabcomplete: true
    # Should we play sound while player executing commands?
    oncommand: true
    # Should we play sound while player clicking shop container/sign?
    onclick: true
# Item matcher.
# Do not touch it if you don't know what you're doing!
matcher:
  # Matcher type
  # 0= QuickShop item matcher with configurable options below.
  # 1= Bukkit item matcher, can be more accurate.
  # 2= Stricter Bukkit matcher, equals method.
  work-type: 1
  # For Item (Only works under QuickShop ItemMatcher)
  item:
    # Should the Plugin check the item damage?
    damage: true
    # Should the Plugin check the item repair cost?
    repaircost: false
    # Should the Plugin check the item display name?
    displayname: true
    # Should the Plugin check the item lores?
    lores: true
    # Should the Plugin check the item enchs?
    enchs: true
    # Should the Plugin check the item potions?
    potions: true
    # Should the Plugin check the item attributes?
    attributes: true
    # Should the Plugin check the item itemflags?
    itemflags: true
    # Should the Plugin check the item custommodeldata?
    custommodeldata: true
    # Should the Plugin check the item bookmetas?
    books: true
    # Should the Plugin check the banner meta?
    banner: true
    # Should the Plugin check the skull meta?
    skull: true
    # Should the Plugin check the firework meta?
    firework: true
    # Should the Plugin check the map meta?
    map: true
    # Should the Plugin check the leather armor meta?
    leatherArmor: true
    # Should the Plugin check the fishBucket meta?
    fishBucket: true
    # Should the Plugin check the suspiciousStew meta?
    suspiciousStew: true
    # Should the Plugin check the shulkerBox contents?
    shulkerBox: true
    # Should the Plugin check the bundle contents?
    bundle: true

# The protection that a shop should check.
protect:
  explode: true
  hopper: true
  hopper-owner-exclude: false
  entity: true

# The policy that QuickShop-Hikari will backup the databases
backup-policy:
  # Backup before purge the shops
  shops-auto-purge: false
  # Backup the database before perform database upgrade
  database-upgrade: true
  # Backup on every startup
  startup: false
  # Backup old databases when recovery the database from file
  recovery: true

# This option allows you to set custom item max stacksizes.
# If nothing matches, then all items (*) will be used.
# * means all items.
# Examples:
#
# custom-item-stacksize:
# - "*:128"
# - STONE:128
#
custom-item-stacksize: []

# Shops purger
# Will purge all shops that owner offline x days while use command or server startup
purge:
  enabled: false
  # Whether to purge shops automatically when server started
  at-server-startup: true
  # The days of offline need to be purged
  days: 60
  # At this moment, QuickShop only compatible with Vanilla banning system (e.g Essentials using)
  banned: true
  # At this moment, QuickShop only compatible with Vanilla OP system, LuckPerms may need add in future.
  skip-op: true

debug:
  # Should we disable the debug logger?
  # This will save a lot of execution time but will make reporting bugs
  # and getting support harder because of the lack of information.
  disable-debuglogger: false
  # Log stacktrace when deleting shop?
  shop-deletion: false
  # Delete corrupt shops while loading?
  # Setting this to false will disable the loading of corrupt shops!
  delete-corrupt-shops: false

# Legacy data updater
# Turn on this will make QuickShop to execute extra checks and may affect performance.
# Only turn it on when you updating from old versions.
legacy-updater:
  shop-sign: false

# Set the command alias for qs main command
# - playershop
# - pshop
custom-commands:
- qs
- shop
- chestshop
- cshop

# Let this stay false, DO NOT TOUCH IT!
config-damaged: false

# Specfic the translation key mapping to a fixed String.
# Example:
#  - "ecoenchants:enchantment.damage_artifact=Artifact Damage"
custom-translation-key: []


# Privacy Controller allows you turn on/off some options that related to privacy. This is our move towards privacy transparency.
# By audit all privacy reviewing activities, execute `/quickshop paste` to generate a paste and check `Privacy Logs` section.
# To out-put from bStats completely, go to /plugins/bStats/config.yml.
privacy:
  # Category Control
  type:
    # Including `/qs paste` and Auto Error Reporter, disable DIAGNOSTIC category will disable online paste, rollbar error reporter error reporting.
    DIAGNOSTIC: true
    # Including bStats metrics with `Statistic - ` prefix, see all metrics we collected on https://bstats.org/plugin/bukkit/QuickShop-Hikari/14281
    STATISTIC: true
    # Including bStats metrics with `Research - ` prefix, see all metrics we collected on https://bstats.org/plugin/bukkit/QuickShop-Hikari/14281
    RESEARCH: true
#  SOME_MODULE_NAME_HERE: false

# Record transactions for metric purpose,
# all transaction will be record into database can used by other plugins for analyse trade activities.
# Turn off this will stop recording transactions for metrics.
transaction-metric:
  enable: true
server-uuid: 8d9c37b2-e27a-46e8-a6be-ba8e4675880e
donation-key: ''
