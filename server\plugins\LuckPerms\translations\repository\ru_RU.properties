luckperms.logs.actionlog-prefix=ЛОГ
luckperms.logs.verbose-prefix=VB
luckperms.logs.export-prefix=ЭКСПОРТ
luckperms.commandsystem.available-commands=Используйте {0} для просмотра доступных команд
luckperms.commandsystem.command-not-recognised=Команда не распознана
luckperms.commandsystem.no-permission=У вас нет разрешения на использование этой команды.
luckperms.commandsystem.no-permission-subcommands=У вас нет разрешения на использование подкоманд
luckperms.commandsystem.already-executing-command=Была запущена другая команда, ожидание ее выполнения для завершения...
luckperms.commandsystem.usage.sub-commands-header=подкоманды
luckperms.commandsystem.usage.usage-header=Использование команд
luckperms.commandsystem.usage.arguments-header=Аргументы
luckperms.first-time.no-permissions-setup=Похоже, права ещё не настроены\!
luckperms.first-time.use-console-to-give-access=Прежде чем вы сможете использовать команды LuckPerms в игре, вам нужно использовать консоль для получения доступа
luckperms.first-time.console-command-prompt=Откройте консоль и выполните
luckperms.first-time.next-step=После того как вы сделаете это, вы сможете начать назначать разрешения и группы
luckperms.first-time.wiki-prompt=Не знаете с чего начать? Посмотрите здесь\: {0}
luckperms.login.try-again=Пожалуйста, повторите позже
luckperms.login.loading-database-error=Произошла ошибка базы данных при загрузке данных о разрешениях
luckperms.login.server-admin-check-console-errors=Если вы администратор сервера, пожалуйста, проверьте консоль на наличие ошибок
luckperms.login.server-admin-check-console-info=Пожалуйста, проверьте консоль сервера для получения дополнительной информации
luckperms.login.data-not-loaded-at-pre=Данные разрешений для вашего пользователя не были загружены на стадии предварительного входа
luckperms.login.unable-to-continue=невозможно продолжить
luckperms.login.craftbukkit-offline-mode-error=это, вероятно, вызвано конфликтом между CraftBukkit и настройкой параметра online-mode
luckperms.login.unexpected-error=Произошла непредвиденная ошибка при подготовке данных ваших разрешений
luckperms.opsystem.disabled=Стандартная система операторов на данном сервере отключена
luckperms.opsystem.sponge-warning=Пожалуйста, обратите внимание, что статус оператора сервера не влияет на проверку разрешений Sponge при установке плагина разрешений, вы должны редактировать данные пользователя непосредственно
luckperms.duration.unit.years.plural={0} лет
luckperms.duration.unit.years.singular={0} год
luckperms.duration.unit.years.short={0}г
luckperms.duration.unit.months.plural={0} мес.
luckperms.duration.unit.months.singular={0} месяц
luckperms.duration.unit.months.short={0}мес
luckperms.duration.unit.weeks.plural={0} нед.
luckperms.duration.unit.weeks.singular={0} неделя
luckperms.duration.unit.weeks.short={0}нед
luckperms.duration.unit.days.plural={0} дн.
luckperms.duration.unit.days.singular={0} день
luckperms.duration.unit.days.short={0}дн
luckperms.duration.unit.hours.plural={0} ч.
luckperms.duration.unit.hours.singular={0} час
luckperms.duration.unit.hours.short={0}ч
luckperms.duration.unit.minutes.plural={0} мин.
luckperms.duration.unit.minutes.singular={0} минута
luckperms.duration.unit.minutes.short={0}мин
luckperms.duration.unit.seconds.plural={0} сек.
luckperms.duration.unit.seconds.singular={0} секунда
luckperms.duration.unit.seconds.short={0}сек
luckperms.duration.since={0} назад
luckperms.command.misc.invalid-code=Неверный код
luckperms.command.misc.response-code-key=код ответа
luckperms.command.misc.error-message-key=сообщение
luckperms.command.misc.bytebin-unable-to-communicate=Не удалось связаться с bytebin
luckperms.command.misc.webapp-unable-to-communicate=Не удалось связаться с веб-приложением
luckperms.command.misc.check-console-for-errors=Проверьте консоль на наличие ошибок
luckperms.command.misc.file-must-be-in-data=Файл {0} должен быть прямым потомком каталога данных
luckperms.command.misc.wait-to-finish=Пожалуйста, дождитесь окончания и попробуйте еще раз
luckperms.command.misc.invalid-priority=Неверный приоритет {0}
luckperms.command.misc.expected-number=Ожидалось число
luckperms.command.misc.date-parse-error=Не удалось прочитать дату {0}
luckperms.command.misc.date-in-past-error=Нельзя установить дату, которая уже прошла.
luckperms.command.misc.page=страница {0} из {1}
luckperms.command.misc.page-entries={0} записей
luckperms.command.misc.none=нет
luckperms.command.misc.loading.error.unexpected=Произошла непредвиденная ошибка
luckperms.command.misc.loading.error.user=Пользователь не загружен
luckperms.command.misc.loading.error.user-specific=Не удается загрузить целевого пользователя {0}
luckperms.command.misc.loading.error.user-not-found=Невозможно найти пользователя для {0}
luckperms.command.misc.loading.error.user-save-error=Произошла ошибка при сохранении пользовательских данных для {0}
luckperms.command.misc.loading.error.user-not-online=Пользователь {0} не в сети
luckperms.command.misc.loading.error.user-invalid={0} является недопустимым именем пользователя или UUID
luckperms.command.misc.loading.error.user-not-uuid=Пользователь {0} имеет недопустимый UUID
luckperms.command.misc.loading.error.group=Группа не загружена
luckperms.command.misc.loading.error.all-groups=Невозможно загрузить все группы
luckperms.command.misc.loading.error.group-not-found=Группа {0} не найдена
luckperms.command.misc.loading.error.group-save-error=Произошла ошибка при сохранении данных для группы {0}
luckperms.command.misc.loading.error.group-invalid={0} недопустимое название группы
luckperms.command.misc.loading.error.track=Трек не загружен
luckperms.command.misc.loading.error.all-tracks=Невозможно загрузить все треки
luckperms.command.misc.loading.error.track-not-found=Трек {0} не найден
luckperms.command.misc.loading.error.track-save-error=Произошла ошибка при сохранении данных для трека {0}
luckperms.command.misc.loading.error.track-invalid={0} не является допустимым названием трека
luckperms.command.editor.no-match=Невозможно открыть веб-редактор, ни один объект не подошел под указанный тип
luckperms.command.editor.start=Подготовка новой сессии веб-редактора, пожалуйста, подождите...
luckperms.command.editor.url=Нажмите на ссылку ниже, чтобы открыть веб-редактор
luckperms.command.editor.unable-to-communicate=Не удалось связаться с веб-редактором
luckperms.command.editor.apply-edits.success=Данные веб-редактора были успешно применены к {0} {1}
luckperms.command.editor.apply-edits.success-summary={0} {1} и {2} {3}
luckperms.command.editor.apply-edits.success.additions=добавлено
luckperms.command.editor.apply-edits.success.additions-singular=дополнение
luckperms.command.editor.apply-edits.success.deletions=удалено
luckperms.command.editor.apply-edits.success.deletions-singular=удаление
luckperms.command.editor.apply-edits.no-changes=Ничего не произошло, так как в веб-редакторе не было выполнено никаких изменений
luckperms.command.editor.apply-edits.unknown-type=Невозможно применить изменения к указанному типу объекта
luckperms.command.editor.apply-edits.unable-to-read=Не удалось получить данные, используя данный код
luckperms.command.search.searching.permission=Ищем пользователей и группы с {0}
luckperms.command.search.searching.inherit=Выполняется поиск пользователей и групп, которые наследуют разрешения от {0}
luckperms.command.search.result=Найдено {0} записей от {1} пользователей и {2} групп
luckperms.command.search.result.default-notice=Примечание\: при поиске пользователей с основной группой, пользователи без других разрешений, которые офлайн, не будут показаны.
luckperms.command.search.showing-users=Отображение записей пользователя
luckperms.command.search.showing-groups=Отображение записей группы
luckperms.command.tree.start=Генерируем дерево разрешений, пожалуйста, подождите...
luckperms.command.tree.empty=Невозможно сгенерировать дерево, результаты не найдены
luckperms.command.tree.url=URL дерева разрешений
luckperms.command.verbose.invalid-filter={0} является недопустимым фильтром
luckperms.command.verbose.enabled=Подробное логгирование {0} для проверок, соответствующих {1}
luckperms.command.verbose.command-exec=Вынужденно {0} выполнить команду {1} и сообщить о всех проверках...
luckperms.command.verbose.off=Подробное логгирование {0}
luckperms.command.verbose.command-exec-complete=Выполнение команды завершено
luckperms.command.verbose.command.no-checks=Выполнение команды завершено, но проверка разрешений была не выполнена
luckperms.command.verbose.command.possibly-async=Это может происходить потому, что плагин выполняет команды в фоне (асинхронно)
luckperms.command.verbose.command.try-again-manually=Вы можете использовать подробное логгирование для обнаружения проверок
luckperms.command.verbose.enabled-recording=Подробное логгирование {0} для проверок, соответствующих {1}
luckperms.command.verbose.uploading=Подробное логгирование {0}, загрузка результатов...
luckperms.command.verbose.url=URL подробных результатов
luckperms.command.verbose.enabled-term=включено
luckperms.command.verbose.disabled-term=выключено
luckperms.command.verbose.query-any=ЛЮБОЙ
luckperms.command.info.running-plugin=Работает на
luckperms.command.info.platform-key=Платформа
luckperms.command.info.server-brand-key=Вендор платформы
luckperms.command.info.server-version-key=Версия ядра
luckperms.command.info.storage-key=Хранилище
luckperms.command.info.storage-type-key=Тип
luckperms.command.info.storage.meta.split-types-key=Типы
luckperms.command.info.storage.meta.ping-key=Пинг
luckperms.command.info.storage.meta.connected-key=Подключено
luckperms.command.info.storage.meta.file-size-key=Размер
luckperms.command.info.extensions-key=Расширения
luckperms.command.info.messaging-key=Сообщения
luckperms.command.info.instance-key=Экземпляр
luckperms.command.info.static-contexts-key=Статические контексты
luckperms.command.info.online-players-key=Игроки в сети
luckperms.command.info.online-players-unique={0} уникальных
luckperms.command.info.uptime-key=Время работы
luckperms.command.info.local-data-key=Локальные данные
luckperms.command.info.local-data={0} пользователей, {1} групп, {2} треков
luckperms.command.generic.create.success={0} успешно создан
luckperms.command.generic.create.error=Произошла ошибка во время создания {0}
luckperms.command.generic.create.error-already-exists={0} уже существует
luckperms.command.generic.delete.success={0} успешно удалён
luckperms.command.generic.delete.error=Произошла ошибка во время удаления {0}
luckperms.command.generic.delete.error-doesnt-exist={0} не существует.
luckperms.command.generic.rename.success={0} успешно переименован в {1}
luckperms.command.generic.clone.success={0} успешно клонирован в {1}
luckperms.command.generic.info.parent.title=Родительские группы
luckperms.command.generic.info.parent.temporary-title=Временные родительские группы
luckperms.command.generic.info.expires-in=истекает через
luckperms.command.generic.info.inherited-from=унаследован из/от
luckperms.command.generic.info.inherited-from-self=самого себя
luckperms.command.generic.show-tracks.title=Треки {0}
luckperms.command.generic.show-tracks.empty={0} не находится в каком-либо треке
luckperms.command.generic.clear.node-removed={0} записей удалено
luckperms.command.generic.clear.node-removed-singular=Разрешение {0} удалено
luckperms.command.generic.clear=Записи {0} в контексте {1} были очищены
luckperms.command.generic.permission.info.title=Разрешения {0}
luckperms.command.generic.permission.info.empty={0} не имеет установленных разрешений
luckperms.command.generic.permission.info.click-to-remove=Нажмите, чтобы удалить это разрешение из {0}
luckperms.command.generic.permission.check.info.title=Информация о разрешении {0}
luckperms.command.generic.permission.check.info.directly={0} имеет {1} с установленным значением {2} в контексте {3}
luckperms.command.generic.permission.check.info.inherited={0} наследует {1} с установленным значением {2} от {3} в контексте {4}
luckperms.command.generic.permission.check.info.not-directly={0} не имеет установленного {1}
luckperms.command.generic.permission.check.info.not-inherited={0} не наследуется от {1}
luckperms.command.generic.permission.check.result.title=Проверка разрешения {0}
luckperms.command.generic.permission.check.result.result-key=Результат
luckperms.command.generic.permission.check.result.processor-key=Процессор
luckperms.command.generic.permission.check.result.cause-key=Причина
luckperms.command.generic.permission.check.result.context-key=Контекст
luckperms.command.generic.permission.set=Установить {0} в {1} для {2} в контексте {3}
luckperms.command.generic.permission.already-has={0} уже имеет разрешение {1}, установленное в контексте {2}
luckperms.command.generic.permission.set-temp=Установлено право {0} со значением {1} для {2} с сроком действия {3} в контексте {4}
luckperms.command.generic.permission.already-has-temp={0} уже имеет временно установленное разрешение {1} в контексте {2}
luckperms.command.generic.permission.unset=Снимает {0} для {1} в контексте {2}
luckperms.command.generic.permission.doesnt-have={0} не имеет {1}, установленное в контексте {2}
luckperms.command.generic.permission.unset-temp=Снимает временное разрешение {0} для {1} в контексте {2}
luckperms.command.generic.permission.subtract=Устанавливает {0} в {1} для {2} на срок {3} в контексте {4}, на {5} меньше, чем раньше
luckperms.command.generic.permission.doesnt-have-temp={0} не имеет {1}, временно установленное в контексте {2}
luckperms.command.generic.permission.clear=Разрешения {0} в контексте {1} были очищены 
luckperms.command.generic.parent.info.title=Родители {0}
luckperms.command.generic.parent.info.empty={0} не имеет родительских прав
luckperms.command.generic.parent.info.click-to-remove=Нажмите, чтобы удалить данное родительское разрешение {0}
luckperms.command.generic.parent.add={0} теперь наследует разрешения от {1} в контексте {2}
luckperms.command.generic.parent.add-temp={0} теперь наследует разрешения от {1} в течение {2} в контексте {3}
luckperms.command.generic.parent.set=Ранее установленные родительские группы {0} очищены и теперь этот пользователь наследует только {1} в контексте {2}
luckperms.command.generic.parent.set-track=Ранее установленные родительские группы {0} в треке {1} очищены и теперь этот пользователь наследует только {2} в контексте {3}
luckperms.command.generic.parent.remove={0} больше не наследует разрешения от {1} в контексте {2}
luckperms.command.generic.parent.remove-temp={0} больше временно не наследует разрешения от {1} в контексте {2}
luckperms.command.generic.parent.subtract={0} теперь наследует разрешения от {1} на срок {2} в контексте {3} длительностью на {4} меньше, чем ранее
luckperms.command.generic.parent.clear=Родительские группы {0} в контексте {1} очищены
luckperms.command.generic.parent.clear-track=Родительские группы {0} в треке {1} в контексте {2} были очищены
luckperms.command.generic.parent.already-inherits=Группа {0} уже наследуется от {1} в контексте {2}
luckperms.command.generic.parent.doesnt-inherit=Группа {0} не наследуется от {1} в контексте {2}
luckperms.command.generic.parent.already-temp-inherits=Группа {0} уже временно наследуется от {1} в контексте {2}
luckperms.command.generic.parent.doesnt-temp-inherit=Группа {0} временно не наследуется от {1} в контексте {2}
luckperms.command.generic.chat-meta.info.title-prefix=Префиксы {0}
luckperms.command.generic.chat-meta.info.title-suffix=Суффиксы {0}
luckperms.command.generic.chat-meta.info.none-prefix={0} не имеет префиксов
luckperms.command.generic.chat-meta.info.none-suffix={0} не имеет суффиксов
luckperms.command.generic.chat-meta.info.click-to-remove=Нажмите, чтобы удалить {0} из {1}
luckperms.command.generic.chat-meta.already-has={0} уже имеет {1} {2}, установленную с приоритетом {3} в контексте {4}
luckperms.command.generic.chat-meta.already-has-temp={0} уже имеет {1} {2}, временно установленную с приоритетом {3} в контексте {4}
luckperms.command.generic.chat-meta.doesnt-have={0} не имеет {1} {2}, установленную с приоритетом {3} в контексте {4}
luckperms.command.generic.chat-meta.doesnt-have-temp={0} уже имеет {1} {2}, временно установленную с приоритетом {3} в контексте {4}
luckperms.command.generic.chat-meta.add={0} теперь имеет {1} {2}, установленный с приоритетом {3} в контексте {4}
luckperms.command.generic.chat-meta.add-temp={0} теперь имеет {1} {2}, временно установленный с приоритетом {3} на срок {4} в контексте {5}
luckperms.command.generic.chat-meta.remove={0} теперь не имеет {1} {2}, который был установлен с приоритетом {3} в контексте {4}
luckperms.command.generic.chat-meta.remove-bulk={0} теперь не имеет все {1}, которые были установлены с приоритетом {2} в контексте {3}
luckperms.command.generic.chat-meta.remove-temp={0} теперь не имеет {1} {2}, который был временно установлен с приоритетом {3} в контексте {4}
luckperms.command.generic.chat-meta.remove-temp-bulk={0} теперь не имеет все {1}, которые были временно установлены с приоритетом {2} в контексте {3}
luckperms.command.generic.meta.info.title=Мета группы {0}
luckperms.command.generic.meta.info.none={0} не имеет мета
luckperms.command.generic.meta.info.click-to-remove=Нажмите, чтобы удалить эту мета информацию из группы {0}
luckperms.command.generic.meta.already-has=Группа {0} уже имеет мета ключ {1} с установленным значением {2} в контексте {3}
luckperms.command.generic.meta.already-has-temp={0} уже имеет мета ключ {1} с временно установленным значением {2} в контексте {3}
luckperms.command.generic.meta.doesnt-have=Группа {0} не имеет мета ключа {1}, установленного в контексте {2}
luckperms.command.generic.meta.doesnt-have-temp={0} не имеет мета ключа {1}, временно установленного в контексте {2}
luckperms.command.generic.meta.set=Установить мета ключ {0} со значением {1} для {2} в контексте {3}
luckperms.command.generic.meta.set-temp=Временно установить мета ключ {0} со значением {1} для {2} на {3} в контексте {4}
luckperms.command.generic.meta.unset=Снять мета ключ {0} со значением {1} в контексте {2}
luckperms.command.generic.meta.unset-temp=Снять временный мета ключ {0} со значением {1} в контексте {2}
luckperms.command.generic.meta.clear=Мета для {0}, соответствующая типу {1}, была очищена в контексте {2}
luckperms.command.generic.contextual-data.title=Контекстные данные
luckperms.command.generic.contextual-data.mode.key=режим
luckperms.command.generic.contextual-data.mode.server=сервер
luckperms.command.generic.contextual-data.mode.active-player=активный игрок
luckperms.command.generic.contextual-data.contexts-key=Контексты
luckperms.command.generic.contextual-data.prefix-key=Префикс
luckperms.command.generic.contextual-data.suffix-key=Суффикс
luckperms.command.generic.contextual-data.primary-group-key=Основная группа
luckperms.command.generic.contextual-data.meta-key=Мета
luckperms.command.generic.contextual-data.null-result=нет
luckperms.command.user.info.title=Информация о пользователе
luckperms.command.user.info.uuid-key=UUID
luckperms.command.user.info.uuid-type-key=тип
luckperms.command.user.info.uuid-type.mojang=mojang
luckperms.command.user.info.uuid-type.not-mojang=не в сети
luckperms.command.user.info.status-key=Статус
luckperms.command.user.info.status.online=Онлайн
luckperms.command.user.info.status.offline=Не в сети
luckperms.command.user.removegroup.error-primary=Нельзя удалить пользователя из его основной группы
luckperms.command.user.primarygroup.not-member={0} добавлен в трек {1}, так как ранее этот пользователь еще не был связан с ним
luckperms.command.user.primarygroup.already-has={0} уже имеет группу {1}, установленной в качестве основной группы
luckperms.command.user.primarygroup.warn-option=Предупреждение\: Метод подсчета пользователей с основной группой, используемый этим сервером ({0}), может не отражать данное изменение
luckperms.command.user.primarygroup.set=Основная группа {0} была установлена на группу {1}
luckperms.command.user.track.error-not-contain-group={0} еще не имеет ни одной группы, которая находится в треке {1}
luckperms.command.user.track.unsure-which-track=Не удалось выбрать нужный трек для использования, пожалуйста, укажите его название в качестве аргумента
luckperms.command.user.track.missing-group-advice=Либо создайте группу, либо удалите её из трека и попробуйте снова
luckperms.command.user.promote.added-to-first={0} добавлен в группу {2} в контексте {3}, так как пользователь не имел групп, которые находятся в треке {1}
luckperms.command.user.promote.not-on-track=Не удалось повысить группу {0}, так как этот пользователь не имеет групп, которые находятся в треке {1}
luckperms.command.user.promote.success=Повышение группы {0} в треке {1} с {2} до {3} в контексте {4}
luckperms.command.user.promote.end-of-track=Достигнут конец трека {0}, не удалось повысить группу {1}
luckperms.command.user.promote.next-group-deleted=Следующая группа в треке — {0} — больше не существует
luckperms.command.user.promote.unable-to-promote=Не удалось повысить группу пользователя
luckperms.command.user.demote.success=Понижение группы {0} в треке {1} с {2} до {3} в контексте {4}
luckperms.command.user.demote.end-of-track=Достигнут конец трека {0}, поэтому {1} был удален из {2}
luckperms.command.user.demote.end-of-track-not-removed=Достигнут конец трека {0}, но {1} не был удален из первой группы в треке
luckperms.command.user.demote.previous-group-deleted=Предыдущая группа в треке — {0} — больше не существует
luckperms.command.user.demote.unable-to-demote=Не удалось понизить группу пользователя
luckperms.command.group.list.title=Группы
luckperms.command.group.delete.not-default=Нельзя удалить группу по умолчанию
luckperms.command.group.info.title=Информация о группе
luckperms.command.group.info.display-name-key=Отображаемое имя
luckperms.command.group.info.weight-key=Вес
luckperms.command.group.setweight.set=Установлен вес {0} для группы {1}
luckperms.command.group.setdisplayname.doesnt-have=Группа {0} не имеет установленного отображаемого имени
luckperms.command.group.setdisplayname.already-has=Группа {0} уже имеет отображаемое имя {1}
luckperms.command.group.setdisplayname.already-in-use=Отображаемое имя {0} уже используется для группы {1}
luckperms.command.group.setdisplayname.set=Установлено отображаемое имя {0} для группы {1} в контексте {2}
luckperms.command.group.setdisplayname.removed=Отображаемое имя для группы {0} в контексте {1} удалено
luckperms.command.track.list.title=Треки
luckperms.command.track.path.empty=Нет
luckperms.command.track.info.showing-track=Отображение трека
luckperms.command.track.info.path-property=Цепочка
luckperms.command.track.clear=Все группы трека {0} очищены
luckperms.command.track.append.success=Группа {0} добавлена в трек {1}
luckperms.command.track.insert.success=Группа {0} добавлена в трек {1} с позицией {2}
luckperms.command.track.insert.error-number=Для задания позиции требуется указать число, введено\: {0}
luckperms.command.track.insert.error-invalid-pos=Не удалось добавить в позицию {0}
luckperms.command.track.insert.error-invalid-pos-reason=неверный номер позиции
luckperms.command.track.remove.success=Группа {0} удалена из трека {1}
luckperms.command.track.error-empty=Трек {0} не может быть использован, так как он пустой или содержит только одну группу
luckperms.command.track.error-multiple-groups={0} является участников нескольких групп в этом треке
luckperms.command.track.error-ambiguous=Не удалось определить расположение в треке
luckperms.command.track.already-contains=Группа {1} уже находится в треке {0}
luckperms.command.track.doesnt-contain={0} не содержит {1}
luckperms.command.log.load-error=Не удалось загрузить лог
luckperms.command.log.invalid-page=Неверный номер страницы
luckperms.command.log.invalid-page-range=Пожалуйста, укажите значение от {0} до {1}
luckperms.command.log.empty=Нет записей лога для отображения
luckperms.command.log.notify.error-console=Нельзя изменять настройки оповещений для консоли
luckperms.command.log.notify.enabled-term=включены
luckperms.command.log.notify.disabled-term=отключены
luckperms.command.log.notify.changed-state=Лог-оповещения {0}
luckperms.command.log.notify.already-on=Получение лог-оповещений уже включено
luckperms.command.log.notify.already-off=Получение оповещений уже выключено
luckperms.command.log.notify.invalid-state=Неверное значение, выберите {0} или {1}
luckperms.command.log.show.search=Отображение недавних действий по запросу {0}
luckperms.command.log.show.recent=Отображение недавних действий
luckperms.command.log.show.by=Отображение недавних действий {0}
luckperms.command.log.show.history=Отображение истории действий для {0} {1}
luckperms.command.export.error-term=Ошибка
luckperms.command.export.already-running=В данный момент выполняется другой процесс экспортирования
luckperms.command.export.file.already-exists=Файл с именем {0} уже существует
luckperms.command.export.file.not-writable=Файл {0} недоступен для записи
luckperms.command.export.file.success=Успешно экспортировано в {0}
luckperms.command.export.file-unexpected-error-writing=Произошла непредвиденная ошибка при записи в файл
luckperms.command.export.web.export-code=Код для экспорта
luckperms.command.export.web.import-command-description=Для импортирования используйте следующую команду
luckperms.command.import.term=Импортировать
luckperms.command.import.error-term=Ошибка
luckperms.command.import.already-running=В данный момент выполняется другой процесс импортирования
luckperms.command.import.file.doesnt-exist=Файл {0} не существует
luckperms.command.import.file.not-readable=Файл {0} не доступен для чтения
luckperms.command.import.file.unexpected-error-reading=Произошла непредвиденная ошибка при чтении данных из файла импорта
luckperms.command.import.file.correct-format=это верный формат?
luckperms.command.import.web.unable-to-read=Не удалось получить информацию с использованием данного кода
luckperms.command.import.progress.percent={0}% завершено
luckperms.command.import.progress.operations={0}/{1} операций завершено
luckperms.command.import.starting=Запуск процесса импорта
luckperms.command.import.completed=ЗАВЕРШЕНО
luckperms.command.import.duration=заняло {0} сек.
luckperms.command.bulkupdate.must-use-console=Выполнение команды массового обновления возможно только из консоли
luckperms.command.bulkupdate.invalid-data-type=Неверный тип, ожидалось {0}
luckperms.command.bulkupdate.invalid-constraint=Неверное ограничение {0}
luckperms.command.bulkupdate.invalid-constraint-format=Ограничения должны быть в формате {0}
luckperms.command.bulkupdate.invalid-comparison=Неверный оператор сравнения {0}
luckperms.command.bulkupdate.invalid-comparison-format=Ожидался один из следующих типов\: {0}
luckperms.command.bulkupdate.queued=Операция массового обновления добавлена в очередь
luckperms.command.bulkupdate.confirm=Введите {0} чтобы выполнить обновление
luckperms.command.bulkupdate.unknown-id=Операция с ID {0} не существует или ее срок ожидания истек
luckperms.command.bulkupdate.starting=Выполнение массового обновления
luckperms.command.bulkupdate.success=Процесс массового обновления успешно завершен
luckperms.command.bulkupdate.success.statistics.nodes=Затронутых разрешений всего\:
luckperms.command.bulkupdate.success.statistics.users=Затронутых пользователей всего\: 
luckperms.command.bulkupdate.success.statistics.groups=Затронутых групп всего\:
luckperms.command.bulkupdate.failure=Ну удалось выполнить массовое обновление, проверьте консоль на наличие ошибок
luckperms.command.update-task.request=Обновление данных запрошено, пожалуйста, подождите
luckperms.command.update-task.complete=Обновление данных завершено
luckperms.command.update-task.push.attempting=Выполняем попытку отправки изменений на другие серверы
luckperms.command.update-task.push.complete=Другие серверы были успешно оповещены через {0}
luckperms.command.update-task.push.error=Произошла ошибка при отправке изменений на другие серверы
luckperms.command.update-task.push.error-not-setup=Не удалось отправить изменения на другие серверы, так как раздел "messaging service" не был настроен
luckperms.command.reload-config.success=Файл конфигурации перезагружен
luckperms.command.reload-config.restart-note=некоторые параметры применяются только после перезагрузки сервера
luckperms.command.translations.searching=Поиск доступных переводов, пожалуйста, подождите...
luckperms.command.translations.searching-error=Не удалось получить список доступных переводов
luckperms.command.translations.installed-translations=Установленные переводы
luckperms.command.translations.available-translations=Доступные переводы
luckperms.command.translations.percent-translated=переведено {0}%
luckperms.command.translations.translations-by=от
luckperms.command.translations.installing=Установка переводов, пожалуйста, подождите...
luckperms.command.translations.download-error=Не удалось загрузить перевод на {0}
luckperms.command.translations.installing-specific=Установка языка {0}...
luckperms.command.translations.install-complete=Установка завершена
luckperms.command.translations.download-prompt=Используйте {0} для загрузки и установки последних версий переводов, сделанных сообществом
luckperms.command.translations.download-override-warning=Обратите внимание, что все изменения, внесенные вами для этих языков, будут переопределены
luckperms.usage.user.description=Набор команд для управления пользователями в LuckPerms (пользователь в LuckPerms — это игрок, может ссылаться на UUID или ник-нейм)
luckperms.usage.group.description=Набор команд для управления группами в LuckPerms. Группы — это сгруппированные разрешения, которые могут быть выданы пользователям. Новые группы могут быть созданы с помощью команды ''creategroup''.
luckperms.usage.track.description=Набор команд для управления треками в LuckPerms. Треки — это упорядоченная цепочка групп, которые могут быть использованы для повышения или понижения группы.
luckperms.usage.log.description=Набор команд для управления функцией логирования в LuckPerms.
luckperms.usage.sync.description=Загружает всю информацию из хранилища плагина в память и применяет все обнаруженные изменения.
luckperms.usage.info.description=Выводит основную информацию об активном экземпляре плагина.
luckperms.usage.editor.description=Создает новую сессию веб-редактора
luckperms.usage.editor.argument.type=типы для загрузки в веб-редактор (''all'', ''users'' или ''groups'')
luckperms.usage.editor.argument.filter=разрешение для фильтрации записей пользователя
luckperms.usage.verbose.description=Управление системой мониторинга разрешений плагинов.
luckperms.usage.verbose.argument.action=включить/выключить логирование или загрузить лог
luckperms.usage.verbose.argument.filter=фильтр, соответствующий записям
luckperms.usage.verbose.argument.commandas=игрок/команда для выполнения
luckperms.usage.tree.description=Генерирует вид дерева (упорядоченная иерархия списков) всех разрешений, известных LuckPerms.
luckperms.usage.tree.argument.scope=корневой каталог древа. Укажите ".", чтобы выбрать все разрешения
luckperms.usage.tree.argument.player=ник-нейм онлайн игрока для проверки
luckperms.usage.search.description=Выполняет поиск всех пользователей/групп с указанным разрешением
luckperms.usage.search.argument.permission=разрешение для поиска
luckperms.usage.search.argument.page=страница для просмотра
luckperms.usage.network-sync.description=Синхронизировать изменения с хранилищем и отправить другим серверам сети задачу для выполнения синхронизации
luckperms.usage.import.description=Импортировать данные из ранее созданного файла экспорта
luckperms.usage.import.argument.file=файл, из которого необходимо выполнить импорт
luckperms.usage.import.argument.replace=заменить существующие данные вместо их слияния
luckperms.usage.import.argument.upload=загрузить данные из уже существующего файла экспорта
luckperms.usage.export.description=Экспортировать данные разрешений в специальный файл для экспорта. С помощью него вы сможете восстановить данные, импортировав его.
luckperms.usage.export.argument.file=файл, в который необходимо выполнить экспорт
luckperms.usage.export.argument.without-users=не экспортировать данные пользователей
luckperms.usage.export.argument.without-groups=не экспортировать данные о группах
luckperms.usage.export.argument.upload=Загрузить данные о разрешениях в веб-редактор. Эти данные могут быть импортированы вами позже.
luckperms.usage.reload-config.description=Перезагрузить некоторые параметры конфигурации
luckperms.usage.bulk-update.description=Выполнить запросы массового обновления для всех данных
luckperms.usage.bulk-update.argument.data-type=тип данных, которые необходимо изменить (''all'', ''users'' или ''groups'')
luckperms.usage.bulk-update.argument.action=действие, которое необходимо выполнить над данными (''update'' или ''delete'')
luckperms.usage.bulk-update.argument.action-field=поле для выбора действия. Требуется только для опции ''update'' (''permission'', ''server'' или ''world'')
luckperms.usage.bulk-update.argument.action-value=значение для замены. Требуется только для опции ''update''.
luckperms.usage.bulk-update.argument.constraint=ограничение, необходимые для обновления
luckperms.usage.translations.description=Управление переводами
luckperms.usage.translations.argument.install=субкоманда для установки переводов
luckperms.usage.apply-edits.description=Применяет изменения в разрешениях, выполненные в веб-редакторе
luckperms.usage.apply-edits.argument.code=уникальный код для применения изменений
luckperms.usage.apply-edits.argument.target=кто применяет изменения данных
luckperms.usage.create-group.description=Создать новую группу
luckperms.usage.create-group.argument.name=имя группы
luckperms.usage.create-group.argument.weight=вес группы
luckperms.usage.create-group.argument.display-name=отображаемое имя группы
luckperms.usage.delete-group.description=Удалить группу
luckperms.usage.delete-group.argument.name=имя группы
luckperms.usage.list-groups.description=Показывает список всех групп
luckperms.usage.create-track.description=Создать новый трек
luckperms.usage.create-track.argument.name=имя трека
luckperms.usage.delete-track.description=Удалить трек
luckperms.usage.delete-track.argument.name=имя трека
luckperms.usage.list-tracks.description=Показать список всех треков
luckperms.usage.user-info.description=Показывает информацию о пользователе
luckperms.usage.user-switchprimarygroup.description=Изменяет основную группу пользователя
luckperms.usage.user-switchprimarygroup.argument.group=группа, на которую необходимо заменить существующую
luckperms.usage.user-promote.description=Повышает группу пользователя далее по треку
luckperms.usage.user-promote.argument.track=трек для повышения группы пользователя
luckperms.usage.user-promote.argument.context=контексты, в которых должно быть выполнено повышение группы пользователя
luckperms.usage.user-promote.argument.dont-add-to-first=повышает группу пользователя, если он уже связан с треком
luckperms.usage.user-demote.description=Понижает группу пользователя на предыдущую в треке
luckperms.usage.user-demote.argument.track=трек для понижения группы пользователя
luckperms.usage.user-demote.argument.context=контексты, в которых должно быть выполнено понижение группы пользователя
luckperms.usage.user-demote.argument.dont-remove-from-first=предотвратить удаление пользователя из первой группы трека при понижении группы
luckperms.usage.user-clone.description=Скопировать пользователя
luckperms.usage.user-clone.argument.user=ник-нейм/UUID пользователя, в которого необходимо выполнить клонирование
luckperms.usage.group-info.description=Дает информацию о группе
luckperms.usage.group-listmembers.description=Показать пользователей/группы, которые наследуют разрешения от этой группы
luckperms.usage.group-listmembers.argument.page=страница для просмотра
luckperms.usage.group-setweight.description=Установить вес группы
luckperms.usage.group-setweight.argument.weight=вес, который необходимо установить
luckperms.usage.group-set-display-name.description=Установить отображаемое имя групп
luckperms.usage.group-set-display-name.argument.name=имя для установки
luckperms.usage.group-set-display-name.argument.context=контексты, в которых будет установлено отображаемое имя
luckperms.usage.group-rename.description=Переиемновать группу
luckperms.usage.group-rename.argument.name=новое имя
luckperms.usage.group-clone.description=Клонирует группу
luckperms.usage.group-clone.argument.name=имя клонируемой группы на
luckperms.usage.holder-editor.description=Открывает веб-редактор разрешений
luckperms.usage.holder-showtracks.description=Отображает список треков, в которых находится объект
luckperms.usage.holder-clear.description=Удаляет все разрешения, родительские группы и мета
luckperms.usage.holder-clear.argument.context=контексты для фильтрации
luckperms.usage.permission.description=Редактировать разрешения
luckperms.usage.parent.description=Редактировать наследования
luckperms.usage.meta.description=Редактировать значения мета
luckperms.usage.permission-info.description=Отображает список разрешений, которые имеет объект
luckperms.usage.permission-info.argument.page=страница для просмотра
luckperms.usage.permission-info.argument.sort-mode=как сортировать записи
luckperms.usage.permission-set.description=Устанавливает разрешение для объекта
luckperms.usage.permission-set.argument.node=разрешение для установки
luckperms.usage.permission-set.argument.value=значение разрешения
luckperms.usage.permission-set.argument.context=контексты, в которых должны быть добавлены разрешения
luckperms.usage.permission-unset.description=Снимает разрешение у пользователя
luckperms.usage.permission-unset.argument.node=разрешение, которые необходимо снять
luckperms.usage.permission-unset.argument.context=контексты, в которых должны быть удалены разрешения
luckperms.usage.permission-settemp.description=Временно устанавливает разрешение для объекта
luckperms.usage.permission-settemp.argument.node=разрешение для установки
luckperms.usage.permission-settemp.argument.value=значение разрешения
luckperms.usage.permission-settemp.argument.duration=срок, до истечения которого будет действовать разрешение
luckperms.usage.permission-settemp.argument.temporary-modifier=как временное разрешение должно быть применено
luckperms.usage.permission-settemp.argument.context=контексты, в которых должны быть добавлены разрешения
luckperms.usage.permission-unsettemp.description=Снимает временное разрешение для пользователя
luckperms.usage.permission-unsettemp.argument.node=разрешение для снятия
luckperms.usage.permission-unsettemp.argument.duration=длительность для вычитания
luckperms.usage.permission-unsettemp.argument.context=контексты, в которых должны быть удалены разрешения
luckperms.usage.permission-check.description=Проверяет, есть ли у объекта определенное разрешение
luckperms.usage.permission-check.argument.node=разрешение для проверки
luckperms.usage.permission-clear.description=Очищает все разрешения
luckperms.usage.permission-clear.argument.context=контексты для фильтрации
luckperms.usage.parent-info.description=Отображает список групп, которые наследует этот объект
luckperms.usage.parent-info.argument.page=страница для просмотра
luckperms.usage.parent-info.argument.sort-mode=как сортировать записи
luckperms.usage.parent-set.description=Удаляет все родительские группы, которые уже наследует пользователь, и добавляет указанную
luckperms.usage.parent-set.argument.group=название группы для установки
luckperms.usage.parent-set.argument.context=контексты, в которых должна быть установлена группа
luckperms.usage.parent-add.description=Добавляет группу пользователю для наследования разрешений от неё
luckperms.usage.parent-add.argument.group=группа для наследования
luckperms.usage.parent-add.argument.context=контексты, в которых должна наследоваться группа
luckperms.usage.parent-remove.description=Удаляет ранее установленное наследование группы
luckperms.usage.parent-remove.argument.group=группа для удаления
luckperms.usage.parent-remove.argument.context=контексты, в которых должна быть удалена группа
luckperms.usage.parent-set-track.description=Удаляет все группы, которые уже наследует пользователь в указанном треке, и добавляет указанную
luckperms.usage.parent-set-track.argument.track=трек для установки
luckperms.usage.parent-set-track.argument.group=название группы или номер расположения группы в данном треке для установки
luckperms.usage.parent-set-track.argument.context=контексты, в которых должна быть установлена группа
luckperms.usage.parent-add-temp.description=Временно добавляет группу пользователю для наследования разрешений от неё
luckperms.usage.parent-add-temp.argument.group=группа для наследования
luckperms.usage.parent-add-temp.argument.duration=длительность членства в группе
luckperms.usage.parent-add-temp.argument.temporary-modifier=как временное разрешение должно быть применено
luckperms.usage.parent-add-temp.argument.context=контексты, в которых должна наследоваться группа
luckperms.usage.parent-remove-temp.description=Удаляет ранее установленное правило наследования
luckperms.usage.parent-remove-temp.argument.group=группа для удаления
luckperms.usage.parent-remove-temp.argument.duration=срок для вычитания
luckperms.usage.parent-remove-temp.argument.context=контексты, в которых должна быть удалена группа
luckperms.usage.parent-clear.description=Очищает все родительские группы
luckperms.usage.parent-clear.argument.context=контексты для фильтрации
luckperms.usage.parent-clear-track.description=Очищает все родительские группы, находящиеся в данном треке
luckperms.usage.parent-clear-track.argument.track=трек для удаления
luckperms.usage.parent-clear-track.argument.context=контексты для фильтрации
luckperms.usage.meta-info.description=Показывает все мета для чата
luckperms.usage.meta-set.description=Устанавливает значение для мета
luckperms.usage.meta-set.argument.key=ключ для установки
luckperms.usage.meta-set.argument.value=значение для установки
luckperms.usage.meta-set.argument.context=контексты, в которых должна быть добавлена мета
luckperms.usage.meta-unset.description=Снимает значение мета
luckperms.usage.meta-unset.argument.key=ключ для снятия
luckperms.usage.meta-unset.argument.context=контексты, в которых должна быть удалена мета
luckperms.usage.meta-settemp.description=Временно устанавливает значение мета
luckperms.usage.meta-settemp.argument.key=ключ для установки
luckperms.usage.meta-settemp.argument.value=значение для установки
luckperms.usage.meta-settemp.argument.duration=отрезок времени после которого срок метаданных истечёт
luckperms.usage.meta-settemp.argument.context=контексты, в которых должна быть добавлена мета
luckperms.usage.meta-unsettemp.description=Снимает временное значение мета
luckperms.usage.meta-unsettemp.argument.key=ключ для снятия
luckperms.usage.meta-unsettemp.argument.context=контексты, в которых должна быть удалена мета
luckperms.usage.meta-addprefix.description=Добавляет префикс
luckperms.usage.meta-addprefix.argument.priority=приоритет, с которым должен быть добавлен префикс
luckperms.usage.meta-addprefix.argument.prefix=префикс
luckperms.usage.meta-addprefix.argument.context=контексты, в которых должен быть добавлен префикс
luckperms.usage.meta-addsuffix.description=Добавляет суффикс
luckperms.usage.meta-addsuffix.argument.priority=приоритет, с которым должен быть добавлен суффикс
luckperms.usage.meta-addsuffix.argument.suffix=суффикс
luckperms.usage.meta-addsuffix.argument.context=контексты, в которых должен быть добавлен суффикс
luckperms.usage.meta-setprefix.description=Устанавливает префикс
luckperms.usage.meta-setprefix.argument.priority=приоритет, с которым должен быть установлен префикс
luckperms.usage.meta-setprefix.argument.prefix=префикс
luckperms.usage.meta-setprefix.argument.context=контексты, в которых должен быть установлен префикс
luckperms.usage.meta-setsuffix.description=Устанавливает суффикс
luckperms.usage.meta-setsuffix.argument.priority=приоритет, с которым должен быть установлен суффикс
luckperms.usage.meta-setsuffix.argument.suffix=суффикс
luckperms.usage.meta-setsuffix.argument.context=контексты, в которых должен быть установлен суффикс
luckperms.usage.meta-removeprefix.description=Удаляет префикс
luckperms.usage.meta-removeprefix.argument.priority=приоритет, который имеет удаляемый префикс
luckperms.usage.meta-removeprefix.argument.prefix=префикс
luckperms.usage.meta-removeprefix.argument.context=контексты, в которых должен быть удален префикс
luckperms.usage.meta-removesuffix.description=Удаляет суффикс
luckperms.usage.meta-removesuffix.argument.priority=приоритет, который имеет удаляемый суффикс
luckperms.usage.meta-removesuffix.argument.suffix=суффикс
luckperms.usage.meta-removesuffix.argument.context=контексты, в которых должен быть удален суффикс
luckperms.usage.meta-addtemp-prefix.description=Добавляет временный префикс
luckperms.usage.meta-addtemp-prefix.argument.priority=приоритет, с которым должен быть добавлен префикс
luckperms.usage.meta-addtemp-prefix.argument.prefix=префикс
luckperms.usage.meta-addtemp-prefix.argument.duration=срок, до истечения которого будет действовать префикс
luckperms.usage.meta-addtemp-prefix.argument.context=контексты, в которых должен быть добавлен префикс
luckperms.usage.meta-addtemp-suffix.description=Устанавливает временный суффикс
luckperms.usage.meta-addtemp-suffix.argument.priority=приоритет, с которым должен быть добавлен суффикс
luckperms.usage.meta-addtemp-suffix.argument.suffix=суффикс
luckperms.usage.meta-addtemp-suffix.argument.duration=срок, до истечения которого будет действовать суффикс
luckperms.usage.meta-addtemp-suffix.argument.context=контексты, в которых должен быть добавлен суффикс
luckperms.usage.meta-settemp-prefix.description=Устанавливает временный префикс
luckperms.usage.meta-settemp-prefix.argument.priority=приоритет, с которым должен быть установлен префикс
luckperms.usage.meta-settemp-prefix.argument.prefix=префикс
luckperms.usage.meta-settemp-prefix.argument.duration=срок, до истечения которого будет действовать префикс
luckperms.usage.meta-settemp-prefix.argument.context=контексты, в которых должен быть установлен префикс
luckperms.usage.meta-settemp-suffix.description=Устанавливает временный суффикс
luckperms.usage.meta-settemp-suffix.argument.priority=приоритет, с которым должен быть установлен суффикс
luckperms.usage.meta-settemp-suffix.argument.suffix=суффикс
luckperms.usage.meta-settemp-suffix.argument.duration=срок, до истечения которого будет действовать суффикс
luckperms.usage.meta-settemp-suffix.argument.context=контексты, в которых должен быть установлен суффикс
luckperms.usage.meta-removetemp-prefix.description=Удаляет временный префикс
luckperms.usage.meta-removetemp-prefix.argument.priority=приоритет, который имеет удаляемый префикс
luckperms.usage.meta-removetemp-prefix.argument.prefix=префикс
luckperms.usage.meta-removetemp-prefix.argument.context=контексты, в которых должен быть удален префикс
luckperms.usage.meta-removetemp-suffix.description=Удаляет временный префикс
luckperms.usage.meta-removetemp-suffix.argument.priority=приоритет, который имеет удаляемый суффикс
luckperms.usage.meta-removetemp-suffix.argument.suffix=суффикс
luckperms.usage.meta-removetemp-suffix.argument.context=контексты, в которых должен быть удален суффикс
luckperms.usage.meta-clear.description=Очищает все мета значения
luckperms.usage.meta-clear.argument.type=тип мета для удаления
luckperms.usage.meta-clear.argument.context=контексты для фильтрации
luckperms.usage.track-info.description=Дает информацию о треке
luckperms.usage.track-editor.description=Открывает веб-редактор разрешений
luckperms.usage.track-append.description=Добавляет группу в конец трека
luckperms.usage.track-append.argument.group=группа для добавления
luckperms.usage.track-insert.description=Добавляет группу с заданной позицией в трек
luckperms.usage.track-insert.argument.group=группа для добавления
luckperms.usage.track-insert.argument.position=позиция для добавления группы, начиная с 1
luckperms.usage.track-remove.description=Удаляет группу из трека
luckperms.usage.track-remove.argument.group=группа для удаления
luckperms.usage.track-clear.description=Очищает список групп в треке
luckperms.usage.track-rename.description=Переименовать трек
luckperms.usage.track-rename.argument.name=новое имя
luckperms.usage.track-clone.description=Клонирует трек
luckperms.usage.track-clone.argument.name=имя трека, в который необходимо выполнить клонирование
luckperms.usage.log-recent.description=Просмотр недавних действий
luckperms.usage.log-recent.argument.user=ник-нейм/UUID пользователя для фильтрации по
luckperms.usage.log-recent.argument.page=номер страницы для просмотра
luckperms.usage.log-search.description=Поиск записи в логе
luckperms.usage.log-search.argument.query=запрос для поиска
luckperms.usage.log-search.argument.page=номер страницы для просмотра
luckperms.usage.log-notify.description=Включить оповещения о логировании
luckperms.usage.log-notify.argument.toggle=включить/выключить
luckperms.usage.log-user-history.description=Просмотр истории пользователя
luckperms.usage.log-user-history.argument.user=ник-нейм/UUID пользователя
luckperms.usage.log-user-history.argument.page=номер страницы для просмотра
luckperms.usage.log-group-history.description=Просмотр истории для группы
luckperms.usage.log-group-history.argument.group=имя группы
luckperms.usage.log-group-history.argument.page=номер страницы для просмотра
luckperms.usage.log-track-history.description=Просмотр истории для трека
luckperms.usage.log-track-history.argument.track=имя трека
luckperms.usage.log-track-history.argument.page=номер страницы для просмотра
luckperms.usage.sponge.description=Редактировать дополнительные данные Sponge
luckperms.usage.sponge.argument.collection=набор для запроса
luckperms.usage.sponge.argument.subject=субъект для изменения
luckperms.usage.sponge-permission-info.description=Показывает информацию о разрешениях субъекта
luckperms.usage.sponge-permission-info.argument.contexts=контексты для фильтрации
luckperms.usage.sponge-permission-set.description=Устанавливает разрешение для субъекта
luckperms.usage.sponge-permission-set.argument.node=разрешение для установки
luckperms.usage.sponge-permission-set.argument.tristate=значение для установки разрешения
luckperms.usage.sponge-permission-set.argument.contexts=контексты, в которых должно быть установлено разрешение
luckperms.usage.sponge-permission-clear.description=Очищает разрешения субъекта
luckperms.usage.sponge-permission-clear.argument.contexts=контексты, в которых должны быть очищены разрешения
luckperms.usage.sponge-parent-info.description=Показывает информацию о родительских группах субъекта
luckperms.usage.sponge-parent-info.argument.contexts=контексты для фильтрации
luckperms.usage.sponge-parent-add.description=Добавляет родительскую группу субъекту
luckperms.usage.sponge-parent-add.argument.collection=коллекция субъекта, где родительским элементом является субъект
luckperms.usage.sponge-parent-add.argument.subject=имя родительского субъекта
luckperms.usage.sponge-parent-add.argument.contexts=контексты, в которых должна быть добавлена родительская группа
luckperms.usage.sponge-parent-remove.description=Удаляет родительскую группу у субъекта
luckperms.usage.sponge-parent-remove.argument.collection=коллекция субъекта, где родительским элементом является субъект
luckperms.usage.sponge-parent-remove.argument.subject=имя родительского субъекта
luckperms.usage.sponge-parent-remove.argument.contexts=контексты, в которых должны быть удалены родительские группы
luckperms.usage.sponge-parent-clear.description=Очищает родительские группы субъекта
luckperms.usage.sponge-parent-clear.argument.contexts=контексты, в которых должны быть очищены родительские группы
luckperms.usage.sponge-option-info.description=Отображает информацию об опциях субъекта
luckperms.usage.sponge-option-info.argument.contexts=контексты для фильтрации
luckperms.usage.sponge-option-set.description=Устанавливает опцию для субъекта
luckperms.usage.sponge-option-set.argument.key=ключ для установки
luckperms.usage.sponge-option-set.argument.value=значение ключа для установки
luckperms.usage.sponge-option-set.argument.contexts=контексты, в которых должна быть установлена опция
luckperms.usage.sponge-option-unset.description=Снимает опции у субъекта
luckperms.usage.sponge-option-unset.argument.key=ключ для снятия
luckperms.usage.sponge-option-unset.argument.contexts=контексты, в которых должны быть сняты ключи
luckperms.usage.sponge-option-clear.description=Очищает опции субъектов
luckperms.usage.sponge-option-clear.argument.contexts=контексты, в которых должны быть очищены опции
