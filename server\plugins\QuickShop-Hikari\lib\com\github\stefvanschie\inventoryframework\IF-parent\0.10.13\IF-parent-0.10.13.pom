<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <modules>
        <module>IF</module>
        <module>nms/abstraction</module>
        <module>nms/1_20_3-4</module>
        <module>nms/1_20_2</module>
        <module>nms/1_20_0-1</module>
        <module>nms/1_19_4</module>
        <module>nms/1_19_3</module>
        <module>nms/1_19_2</module>
        <module>nms/1_19_1</module>
        <module>nms/1_19_0</module>
        <module>nms/1_18_2</module>
        <module>nms/1_18_1</module>
        <module>nms/1_18_0</module>
        <module>nms/1_17_1</module>
        <module>nms/1_17_0</module>
        <module>nms/1_16_4-5</module>
        <module>nms/1_16_2-3</module>
        <module>nms/1_16_1</module>
        <module>nms/1_15</module>
        <module>nms/1_14</module>
        <module>adventure-support</module>
    </modules>

    <properties>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <maven.deploy.skip>true</maven.deploy.skip>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <adventure.version>4.14.0</adventure.version>
    </properties>

    <groupId>com.github.stefvanschie.inventoryframework</groupId>
    <artifactId>IF-parent</artifactId>
    <version>0.10.13</version>
    <packaging>pom</packaging>

    <name>IF</name>
    <description>An inventory framework for managing GUIs</description>
    <url>https://github.com/stefvanschie/IF</url>

    <licenses>
        <license>
            <name>The Unlicense</name>
            <url>http://unlicense.org/</url>
            <distribution>repo</distribution>
        </license>
    </licenses>

    <scm>
        <url>https://github.com/stefvanschie/IF.git</url>
    </scm>

    <developers>
        <developer>
            <name>Stef van Schie</name>
            <email><EMAIL></email>
        </developer>
    </developers>

    <dependencies>
        <dependency>
            <groupId>org.jetbrains</groupId>
            <artifactId>annotations</artifactId>
            <version>24.0.1</version>
            <scope>provided</scope>
        </dependency>
    </dependencies>

    <distributionManagement>
        <snapshotRepository>
            <id>ossrh</id>
            <url>https://oss.sonatype.org/content/repositories/snapshots</url>
        </snapshotRepository>
        <repository>
            <id>ossrh</id>
            <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
        </repository>
    </distributionManagement>

    <profiles>
        <profile>
            <id>deploy</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-gpg-plugin</artifactId>
                        <version>3.1.0</version>
                        <executions>
                            <execution>
                                <id>sign-artifacts</id>
                                <phase>verify</phase>
                                <goals>
                                    <goal>sign</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>3.1.2</version>
                <dependencies>
                    <dependency>
                        <groupId>org.apache.maven.surefire</groupId>
                        <artifactId>surefire-junit-platform </artifactId>
                        <version>3.1.2</version>
                    </dependency>
                    <dependency>
                        <groupId>org.junit.jupiter</groupId>
                        <artifactId>junit-jupiter-engine</artifactId>
                        <version>5.10.0</version>
                    </dependency>
                </dependencies>
            </plugin>
            <plugin>
                <groupId>org.sonatype.plugins</groupId>
                <artifactId>nexus-staging-maven-plugin</artifactId>
                <version>1.6.13</version>
                <extensions>true</extensions>
                <configuration>
                    <serverId>ossrh</serverId>
                    <nexusUrl>https://oss.sonatype.org/</nexusUrl>
                    <autoReleaseAfterClose>true</autoReleaseAfterClose>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.3.0</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar-no-fork</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <version>3.5.0</version>
                <executions>
                    <execution>
                        <id>attach-javadocs</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <links>
                        <link>https://hub.spigotmc.org/javadocs/bukkit/</link>
                        <link>https://jd.adventure.kyori.net/api/4.10.0/</link>
                    </links>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>