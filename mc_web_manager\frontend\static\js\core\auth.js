/**
 * MC Web Manager - 认证管理模块
 * 处理用户登录状态和权限验证
 */

class AuthManager {
    constructor() {
        this.currentUser = null;
        this.isAuthenticated = false;
        this.loginCallbacks = [];
        this.logoutCallbacks = [];
        
        // 初始化时检查本地存储的token
        this.initializeAuth();
    }

    /**
     * 初始化认证状态
     */
    async initializeAuth() {
        const token = localStorage.getItem('access_token');
        if (token) {
            try {
                // 验证token有效性
                const user = await apiClient.getCurrentUser();
                this.setAuthenticatedUser(user);
            } catch (error) {
                console.warn('Token验证失败:', error);
                this.clearAuth();
            }
        }
    }

    /**
     * 用户登录
     * @param {string} username - 用户名
     * @param {string} password - 密码
     * @returns {Promise<Object>} 登录结果
     */
    async login(username, password) {
        try {
            const result = await apiClient.login(username, password);
            
            // 获取用户信息
            const user = await apiClient.getCurrentUser();
            this.setAuthenticatedUser(user);
            
            // 触发登录回调
            this.triggerLoginCallbacks(user);
            
            return {
                success: true,
                user: user,
                message: '登录成功'
            };
            
        } catch (error) {
            console.error('登录失败:', error);
            return {
                success: false,
                error: error.message,
                message: error.message || '登录失败'
            };
        }
    }

    /**
     * 用户登出
     */
    async logout() {
        try {
            await apiClient.logout();
        } catch (error) {
            console.warn('登出请求失败:', error);
        }
        
        this.clearAuth();
        this.triggerLogoutCallbacks();
    }

    /**
     * 设置已认证用户
     * @param {Object} user - 用户信息
     */
    setAuthenticatedUser(user) {
        this.currentUser = user;
        this.isAuthenticated = true;
    }

    /**
     * 清除认证信息
     */
    clearAuth() {
        this.currentUser = null;
        this.isAuthenticated = false;
        apiClient.setToken(null);
    }

    /**
     * 获取当前用户
     * @returns {Object|null} 当前用户信息
     */
    getCurrentUser() {
        return this.currentUser;
    }

    /**
     * 检查是否已认证
     * @returns {boolean} 认证状态
     */
    isLoggedIn() {
        return this.isAuthenticated;
    }

    /**
     * 检查是否为管理员
     * @returns {boolean} 管理员状态
     */
    isAdmin() {
        return this.isAuthenticated && this.currentUser && this.currentUser.is_admin;
    }

    /**
     * 添加登录回调
     * @param {Function} callback - 回调函数
     */
    onLogin(callback) {
        this.loginCallbacks.push(callback);
    }

    /**
     * 添加登出回调
     * @param {Function} callback - 回调函数
     */
    onLogout(callback) {
        this.logoutCallbacks.push(callback);
    }

    /**
     * 触发登录回调
     * @param {Object} user - 用户信息
     */
    triggerLoginCallbacks(user) {
        this.loginCallbacks.forEach(callback => {
            try {
                callback(user);
            } catch (error) {
                console.error('登录回调执行失败:', error);
            }
        });
    }

    /**
     * 触发登出回调
     */
    triggerLogoutCallbacks() {
        this.logoutCallbacks.forEach(callback => {
            try {
                callback();
            } catch (error) {
                console.error('登出回调执行失败:', error);
            }
        });
    }

    /**
     * 检查并处理认证错误
     * @param {APIError} error - API错误
     * @returns {boolean} 是否为认证错误
     */
    handleAuthError(error) {
        if (error instanceof APIError && error.isAuthError()) {
            console.warn('认证失效，自动登出');
            this.logout();
            return true;
        }
        return false;
    }

    /**
     * 要求用户登录
     * @param {string} message - 提示消息
     */
    requireLogin(message = '请先登录') {
        if (!this.isLoggedIn()) {
            alert(message);
            this.redirectToLogin();
            return false;
        }
        return true;
    }

    /**
     * 要求管理员权限
     * @param {string} message - 提示消息
     */
    requireAdmin(message = '需要管理员权限') {
        if (!this.isAdmin()) {
            alert(message);
            return false;
        }
        return true;
    }

    /**
     * 重定向到登录页面
     */
    redirectToLogin() {
        if (window.location.pathname !== '/') {
            window.location.href = '/';
        }
    }

    /**
     * 重定向到仪表板
     */
    redirectToDashboard() {
        if (window.location.pathname !== '/dashboard') {
            window.location.href = '/dashboard';
        }
    }
}

// 创建全局认证管理器实例
const authManager = new AuthManager();

// 导出认证管理器
window.AuthManager = AuthManager;
window.authManager = authManager;
