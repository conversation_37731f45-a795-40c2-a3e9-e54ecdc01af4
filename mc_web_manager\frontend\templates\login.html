{% extends "base.html" %}

{% block title %}登录 - MC Web Manager{% endblock %}

{% block extra_css %}
<style>
.login-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    padding: 2rem;
    width: 100%;
    max-width: 400px;
}

.login-header {
    text-align: center;
    margin-bottom: 2rem;
}

.login-header h2 {
    color: #333;
    margin-bottom: 0.5rem;
}

.login-header p {
    color: #666;
    margin: 0;
}

.form-control {
    border-radius: 10px;
    border: 2px solid #e1e5e9;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.btn-login {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 10px;
    padding: 0.75rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-login:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.alert {
    border-radius: 10px;
    border: none;
}
</style>
{% endblock %}

{% block content %}
<div class="login-container">
    <div class="login-card">
        <div class="login-header">
            <h2><i class="fas fa-cube"></i> MC Web Manager</h2>
            <p>Minecraft服务器管理系统</p>
        </div>
        
        <div id="alert-container"></div>
        
        <form id="loginForm">
            <div class="mb-3">
                <label for="username" class="form-label">
                    <i class="fas fa-user"></i> 用户名
                </label>
                <input type="text" class="form-control" id="username" name="username" required>
            </div>
            
            <div class="mb-3">
                <label for="password" class="form-label">
                    <i class="fas fa-lock"></i> 密码
                </label>
                <input type="password" class="form-control" id="password" name="password" required>
            </div>
            
            <button type="submit" class="btn btn-primary btn-login w-100">
                <i class="fas fa-sign-in-alt"></i> 登录
            </button>
        </form>
        
        <div class="text-center mt-3">
            <small class="text-muted">
                默认账户: admin / admin123
            </small>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const loginForm = document.getElementById('loginForm');
    const alertContainer = document.getElementById('alert-container');

    // 创建调试信息容器
    const debugContainer = document.createElement('div');
    debugContainer.style.cssText = 'position: fixed; top: 10px; left: 10px; background: #000; color: #0f0; padding: 10px; font-family: monospace; font-size: 12px; z-index: 9999; max-width: 400px; border-radius: 5px; max-height: 300px; overflow-y: auto;';
    document.body.appendChild(debugContainer);

    function debugLog(message) {
        console.log('[LOGIN DEBUG]', message);
        debugContainer.innerHTML += new Date().toLocaleTimeString() + ': ' + message + '<br>';
        debugContainer.scrollTop = debugContainer.scrollHeight;
    }

    debugLog('页面DOM加载完成');
    debugLog('检查全局对象 - apiClient: ' + (typeof apiClient));
    debugLog('检查全局对象 - authManager: ' + (typeof authManager));
    debugLog('检查全局对象 - APIClient: ' + (typeof APIClient));
    debugLog('检查全局对象 - AuthManager: ' + (typeof AuthManager));

    // 检查localStorage
    const token = localStorage.getItem('access_token');
    debugLog('localStorage中的token: ' + (token ? '存在(' + token.substring(0, 20) + '...)' : '不存在'));

    // 检查是否已登录
    if (typeof authManager !== 'undefined') {
        debugLog('authManager存在，检查登录状态');
        try {
            const isLoggedIn = authManager.isLoggedIn();
            debugLog('authManager.isLoggedIn()返回: ' + isLoggedIn);

            if (isLoggedIn) {
                debugLog('检测到已登录状态，准备跳转到仪表板');
                debugLog('调用authManager.redirectToDashboard()');
                authManager.redirectToDashboard();
                return;
            } else {
                debugLog('未登录状态，继续显示登录表单');
            }
        } catch (error) {
            debugLog('检查登录状态时出错: ' + error.message);
        }
    } else {
        debugLog('错误: authManager未定义，可能脚本加载失败');
    }
    
    loginForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const username = document.getElementById('username').value;
        const password = document.getElementById('password').value;
        const submitBtn = loginForm.querySelector('button[type="submit"]');
        
        // 显示加载状态
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 登录中...';
        
        debugLog('开始登录流程 - 用户名: ' + username);

        try {
            // 第一步：直接测试API调用
            debugLog('步骤1: 直接调用登录API');
            const formData = new FormData();
            formData.append('username', username);
            formData.append('password', password);

            debugLog('发送POST请求到 /api/auth/login');
            const response = await fetch('/api/auth/login', {
                method: 'POST',
                body: formData
            });

            debugLog('API响应状态: ' + response.status + ' ' + response.statusText);

            if (!response.ok) {
                const errorText = await response.text();
                debugLog('API错误响应: ' + errorText);
                showAlert('登录API调用失败: ' + response.status, 'danger');
                return;
            }

            const apiResult = await response.json();
            debugLog('API返回数据: ' + JSON.stringify(apiResult));

            // 第二步：手动保存token
            if (apiResult.access_token) {
                debugLog('步骤2: 手动保存token到localStorage');
                localStorage.setItem('access_token', apiResult.access_token);

                const savedToken = localStorage.getItem('access_token');
                debugLog('token保存验证: ' + (savedToken ? '成功' : '失败'));

                // 第三步：验证authManager状态
                debugLog('步骤3: 更新authManager状态');
                if (authManager && authManager.setToken) {
                    authManager.setToken(apiResult.access_token);
                    debugLog('authManager.setToken() 调用完成');
                }

                // 第四步：获取用户信息
                debugLog('步骤4: 获取用户信息');
                try {
                    const userResponse = await fetch('/api/auth/me', {
                        headers: {
                            'Authorization': 'Bearer ' + apiResult.access_token
                        }
                    });

                    if (userResponse.ok) {
                        const userData = await userResponse.json();
                        debugLog('用户信息: ' + JSON.stringify(userData));

                        // 手动设置认证状态
                        if (authManager && authManager.setAuthenticatedUser) {
                            authManager.setAuthenticatedUser(userData);
                            debugLog('authManager用户状态已设置');
                        }
                    } else {
                        debugLog('获取用户信息失败: ' + userResponse.status);
                    }
                } catch (userError) {
                    debugLog('获取用户信息异常: ' + userError.message);
                }

                // 第五步：最终验证和跳转
                debugLog('步骤5: 最终状态验证');
                const finalToken = localStorage.getItem('access_token');
                const isLoggedIn = authManager ? authManager.isLoggedIn() : false;
                debugLog('最终token状态: ' + (finalToken ? '存在' : '不存在'));
                debugLog('最终登录状态: ' + isLoggedIn);

                showAlert('登录成功！正在跳转...', 'success');

                setTimeout(() => {
                    debugLog('执行跳转到仪表板');
                    window.location.href = '/dashboard';
                }, 1500);

            } else {
                debugLog('API响应中没有access_token字段');
                showAlert('登录响应格式错误', 'danger');
            }

        } catch (error) {
            debugLog('登录过程中发生异常: ' + error.message);
            debugLog('异常堆栈: ' + (error.stack || '无堆栈信息'));
            showAlert('登录失败: ' + error.message, 'danger');
        } finally {
            submitBtn.disabled = false;
            submitBtn.innerHTML = '<i class="fas fa-sign-in-alt"></i> 登录';
        }
    });
    
    function showAlert(message, type) {
        alertContainer.innerHTML = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
    }
});
</script>
{% endblock %}
