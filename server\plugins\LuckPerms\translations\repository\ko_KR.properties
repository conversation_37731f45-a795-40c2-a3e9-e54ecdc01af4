luckperms.logs.actionlog-prefix=기록
luckperms.logs.verbose-prefix=VB
luckperms.logs.export-prefix=내보내기
luckperms.commandsystem.available-commands=사용 가능한 명령어를 확인하려면 {0} 을(를) 사용하십시오
luckperms.commandsystem.command-not-recognised=인식할 수 없는 명령입니다
luckperms.commandsystem.no-permission=이 명령어를 사용할 권한이 없습니다\!
luckperms.commandsystem.no-permission-subcommands=하위 명령어를 사용할 권한이 없습니다
luckperms.commandsystem.already-executing-command=다른 명령어가 실행 중입니다. 완료될 때까지 기다립니다...
luckperms.commandsystem.usage.sub-commands-header=하위 명령어
luckperms.commandsystem.usage.usage-header=명령어 사용법
luckperms.commandsystem.usage.arguments-header=인수
luckperms.first-time.no-permissions-setup=어떠한 권한도 설정되지 않은 것으로 보입니다\!
luckperms.first-time.use-console-to-give-access=게임 내에서 LuckPerms 명령어를 사용하기 전에, 콘솔을 사용하여 접근 권한을 당신에게 위임해야 합니다
luckperms.first-time.console-command-prompt=콘솔을 열고 실행하십시오
luckperms.first-time.next-step=이 작업을 완료한 후, 권한 할당 및 그룹 정의를 시작할 수 있습니다
luckperms.first-time.wiki-prompt=어디서부터 시작해야 할지 모르겠다고요? 여기를 확인하세요\: {0}
luckperms.login.try-again=잠시 후 다시 시도해 주세요
luckperms.login.loading-database-error=권한 데이터를 로드하는 도중 데이터베이스 오류가 발생하였습니다
luckperms.login.server-admin-check-console-errors=만약 당신이 서버 관리자인 경우, 콘솔에서 오류가 있는지 확인해 주십시오
luckperms.login.server-admin-check-console-info=서버 콘솔에서 더 자세한 정보를 확인하실 수 있습니다
luckperms.login.data-not-loaded-at-pre=사전 로그인 단계에서 사용자의 권한 데이터가 로드되지 않았습니다
luckperms.login.unable-to-continue=계속할 수 없습니다
luckperms.login.craftbukkit-offline-mode-error=이는 주로 CraftBukkit과 online-mode 설정의 충돌로 인해 발생합니다
luckperms.login.unexpected-error=귀하의 권한 데이터를 설정하는 도중 예기치 못한 오류가 발생했습니다
luckperms.opsystem.disabled=이 서버의 기본 OP 시스템이 비활성화되어 있습니다
luckperms.opsystem.sponge-warning=권한 플러그인이 설치된 경우 서버 관리자 상태는 Sponge 권한 검사에 영향을 미치지 않으므로 사용자 데이터를 직접 편집해야 합니다
luckperms.duration.unit.years.plural={0}년
luckperms.duration.unit.years.singular={0}년
luckperms.duration.unit.years.short={0}년
luckperms.duration.unit.months.plural={0}개월
luckperms.duration.unit.months.singular={0}개월
luckperms.duration.unit.months.short={0}달
luckperms.duration.unit.weeks.plural={0}주
luckperms.duration.unit.weeks.singular={0}주
luckperms.duration.unit.weeks.short={0}주
luckperms.duration.unit.days.plural={0}일
luckperms.duration.unit.days.singular={0}일
luckperms.duration.unit.days.short={0}일
luckperms.duration.unit.hours.plural={0}시간
luckperms.duration.unit.hours.singular={0}시간
luckperms.duration.unit.hours.short={0}시간
luckperms.duration.unit.minutes.plural={0}분
luckperms.duration.unit.minutes.singular={0}분
luckperms.duration.unit.minutes.short={0}분
luckperms.duration.unit.seconds.plural={0}초
luckperms.duration.unit.seconds.singular={0}초
luckperms.duration.unit.seconds.short={0}초
luckperms.duration.since={0} 전
luckperms.command.misc.invalid-code=올바르지 않은 코드
luckperms.command.misc.response-code-key=응답 코드
luckperms.command.misc.error-message-key=메시지
luckperms.command.misc.bytebin-unable-to-communicate=bytebin과 통신할 수 없습니다
luckperms.command.misc.webapp-unable-to-communicate=웹 앱과 통신할 수 없습니다
luckperms.command.misc.check-console-for-errors=콘솔에서 오류를 확인하세요
luckperms.command.misc.file-must-be-in-data=파일 {0}은(는) 데이터 디렉터리 바로 아래 위치해야 합니다
luckperms.command.misc.wait-to-finish=완료될 때까지 기다린 후 다시 시도하십시오
luckperms.command.misc.invalid-priority=유효하지 않은 우선 순위 {0}
luckperms.command.misc.expected-number=숫자가 필요합니다
luckperms.command.misc.date-parse-error=날짜 {0}을(를) 분석할 수 없습니다
luckperms.command.misc.date-in-past-error=날짜는 과거로 설정할 수 없습니다\!
luckperms.command.misc.page=총 {1}페이지 중 {0}페이지
luckperms.command.misc.page-entries={0}개의 항목
luckperms.command.misc.none=없음
luckperms.command.misc.loading.error.unexpected=예상치 못한 오류가 발생했습니다
luckperms.command.misc.loading.error.user=사용자가 로드되지 않았습니다
luckperms.command.misc.loading.error.user-specific=대상 사용자 {0}을(를) 로드할 수 없습니다
luckperms.command.misc.loading.error.user-not-found=사용자 {0}을(를) 찾을 수 없습니다
luckperms.command.misc.loading.error.user-save-error={0}에 대한 사용자 데이터를 저장하는 도중 오류가 발생했습니다
luckperms.command.misc.loading.error.user-not-online=사용자 {0}은(는) 온라인이 아닙니다
luckperms.command.misc.loading.error.user-invalid={0}은(는) 유효하지 않은 사용자 혹은 UUID 입니다
luckperms.command.misc.loading.error.user-not-uuid=대상 사용자 {0}은(는) 유효하지 않은 UUID입니다
luckperms.command.misc.loading.error.group=그룹이 로드되지 않았습니다
luckperms.command.misc.loading.error.all-groups=모든 그룹을 불러올 수 없습니다
luckperms.command.misc.loading.error.group-not-found=그룹 {0}을(를) 찾을 수 없습니다
luckperms.command.misc.loading.error.group-save-error={0}에 대한 그룹 데이터를 저장하는 도중 오류가 발생했습니다
luckperms.command.misc.loading.error.group-invalid={0}은(는) 올바르지 않은 그룹 이름입니다
luckperms.command.misc.loading.error.track=트랙이 로드되지 않았습니다
luckperms.command.misc.loading.error.all-tracks=모든 트랙을 로드할 수 없습니다
luckperms.command.misc.loading.error.track-not-found=트랙 {0}을(를) 찾을 수 없습니다
luckperms.command.misc.loading.error.track-save-error={0}에 대한 트랙 데이터를 저장하는 도중 오류가 발생했습니다
luckperms.command.misc.loading.error.track-invalid={0}은(는) 올바르지 않은 트랙 이름입니다
luckperms.command.editor.no-match=편집기를 열 수 없습니다. 요구하는 유형과 일치하는 개체가 없습니다
luckperms.command.editor.start=새로운 편집기 세션을 준비하는 중입니다. 잠시만 기다려 주세요...
luckperms.command.editor.url=아래 링크를 클릭하여 에디터를 열 수 있습니다
luckperms.command.editor.unable-to-communicate=편집기에 연결할 수 없습니다
luckperms.command.editor.apply-edits.success=웹 편집기 데이터가 {0} {1}에 성공적으로 적용되었습니다
luckperms.command.editor.apply-edits.success-summary={0} {1} 과(와) {2} {3}
luckperms.command.editor.apply-edits.success.additions=추가
luckperms.command.editor.apply-edits.success.additions-singular=추가
luckperms.command.editor.apply-edits.success.deletions=삭제
luckperms.command.editor.apply-edits.success.deletions-singular=삭제
luckperms.command.editor.apply-edits.no-changes=웹 편집기에서 변경된 사항이 없었으며, 반환된 데이터에 변경된 내용이 포함되어 있지 않았습니다
luckperms.command.editor.apply-edits.unknown-type=지정된 개체 유형에 변경 사항을 적용할 수 없습니다
luckperms.command.editor.apply-edits.unable-to-read=주어진 코드를 사용해 데이터를 읽을 수 없습니다
luckperms.command.search.searching.permission={0}에 대한 사용자 및 그룹을 검색합니다
luckperms.command.search.searching.inherit={0}에서 상속된 사용자 및 그룹을 검색합니다
luckperms.command.search.result={1}명의 사용자와 {2}개의 그룹에서 {0}개의 항목을 찾았습니다
luckperms.command.search.result.default-notice=참고\: 기본 그룹의 구성원을 검색할 때 다른 권한이 없는 오프라인 플레이어는 표시되지 않습니다\!
luckperms.command.search.showing-users=사용자 항목 표시
luckperms.command.search.showing-groups=그룹 항목 표시
luckperms.command.tree.start=권한 트리를 생성하고 있습니다. 잠시만 기다려 주세요...
luckperms.command.tree.empty=트리를 생성할 수 없습니다, 결과를 찾지 못했습니다
luckperms.command.tree.url=권한 트리 URL
luckperms.command.verbose.invalid-filter={0}은(는) 유효하지 않은 상세 필터입니다
luckperms.command.verbose.enabled={1}과(와) 일치하는 것에 대한 상세 기록 {0}
luckperms.command.verbose.command-exec={0}이(가) 명령 {1}을(를) 강제로 실행하도록 하고, 모든 검사를 보고합니다...
luckperms.command.verbose.off=자세한 로깅 {0}
luckperms.command.verbose.command-exec-complete=명령어 실행 완료
luckperms.command.verbose.command.no-checks=명령어 실행이 완료되었지만, 권한 확인이 이루어지지 않았습니다
luckperms.command.verbose.command.possibly-async=플러그인이 백그라운드(비동기)에서 명령을 실행하기 때문일 수 있습니다
luckperms.command.verbose.command.try-again-manually=아직 상세 기록을 사용하여 이와 같은 확인을 감지할 수 있습니다
luckperms.command.verbose.enabled-recording={1}과(와) 일치하는 것에 대한 상세 기록 {0}
luckperms.command.verbose.uploading=자세한 로깅 {0}, 결과 업로드 중...
luckperms.command.verbose.url=자세한 결과 URL
luckperms.command.verbose.enabled-term=활성화됨
luckperms.command.verbose.disabled-term=비활성화됨
luckperms.command.verbose.query-any=아무거나
luckperms.command.info.running-plugin=실행 중\:
luckperms.command.info.platform-key=플랫폼
luckperms.command.info.server-brand-key=서버 브랜드
luckperms.command.info.server-version-key=서버 버전
luckperms.command.info.storage-key=저장소
luckperms.command.info.storage-type-key=유형
luckperms.command.info.storage.meta.split-types-key=유형
luckperms.command.info.storage.meta.ping-key=지연 시간
luckperms.command.info.storage.meta.connected-key=연결됨
luckperms.command.info.storage.meta.file-size-key=파일 크기
luckperms.command.info.extensions-key=확장 기능
luckperms.command.info.messaging-key=메시징
luckperms.command.info.instance-key=인스턴스
luckperms.command.info.static-contexts-key=정적 컨텍스트
luckperms.command.info.online-players-key=온라인 플레이어
luckperms.command.info.online-players-unique={0} 신규
luckperms.command.info.uptime-key=업타임
luckperms.command.info.local-data-key=로컬 데이터
luckperms.command.info.local-data={0} 사용자, {1} 그룹, {2} 트랙
luckperms.command.generic.create.success={0}(이)가 성공적으로 생성되었습니다
luckperms.command.generic.create.error={0}(을)를 생성하는 도중 오류가 발생하였습니다
luckperms.command.generic.create.error-already-exists={0}(이)가 이미 존재합니다\!
luckperms.command.generic.delete.success={0}(이)가 성공적으로 삭제되었습니다
luckperms.command.generic.delete.error={0}(을)를 삭제하는 도중 오류가 발생했습니다
luckperms.command.generic.delete.error-doesnt-exist={0}은(는) 존재하지 않습니다\!
luckperms.command.generic.rename.success={0}의 이름이 {1}(으)로 변경되었습니다
luckperms.command.generic.clone.success={0}을(를) {1}에 복제했습니다
luckperms.command.generic.info.parent.title=상위 그룹
luckperms.command.generic.info.parent.temporary-title=임시 상위 그룹
luckperms.command.generic.info.expires-in=만료되기까지
luckperms.command.generic.info.inherited-from=다음에서 상속됨\:
luckperms.command.generic.info.inherited-from-self=자신
luckperms.command.generic.show-tracks.title={0}의 트랙
luckperms.command.generic.show-tracks.empty={0}은(는) 트랙을 가지고 있지 않습니다
luckperms.command.generic.clear.node-removed=노드 {0}이(가) 제거되었습니다
luckperms.command.generic.clear.node-removed-singular=노드 {0}이(가) 제거되었습니다
luckperms.command.generic.clear=노드 {0}이(가) {1} 컨텍스트에서 제거되었습니다
luckperms.command.generic.permission.info.title={0}의 권한
luckperms.command.generic.permission.info.empty={0}은(는) 어떠한 권한도 설정되어 있지 않습니다
luckperms.command.generic.permission.info.click-to-remove=클릭하여 {0}에서 이 노드를 제거합니다
luckperms.command.generic.permission.check.info.title={0}에 대한 권한 정보
luckperms.command.generic.permission.check.info.directly={0}은(는) {3} 컨텍스트에서 {2}으(로) 설정된 {1}을(를) 가지고 있습니다
luckperms.command.generic.permission.check.info.inherited={0}은(는) {4} 컨텍스트에서 {2}으(로) 설정된 {1}을(를) {3}에 의해 상속받고 있습니다
luckperms.command.generic.permission.check.info.not-directly={0}에게 {1}이(가) 설정되어 있지 않습니다
luckperms.command.generic.permission.check.info.not-inherited={0}은(는) {1}을(를) 상속하지 않습니다
luckperms.command.generic.permission.check.result.title={0}에 대한 권한 확인
luckperms.command.generic.permission.check.result.result-key=결과
luckperms.command.generic.permission.check.result.processor-key=처리자
luckperms.command.generic.permission.check.result.cause-key=이유
luckperms.command.generic.permission.check.result.context-key=컨텍스트
luckperms.command.generic.permission.set={2}의 {0}을(를) {3} 컨텍스트에 대해 {1}으(로) 설정했습니다
luckperms.command.generic.permission.already-has={0}은(는) {2} 컨텍스트에 대한 {1}을(를) 이미 가지고 있습니다
luckperms.command.generic.permission.set-temp={2}의 {0}을(를) {4} 컨텍스트에 대해 {3} 동안 {1}으(로) 설정했습니다
luckperms.command.generic.permission.already-has-temp={0}은(는) {2} 컨텍스트에 대한 일시적인 {1}을(를) 이미 가지고 있습니다
luckperms.command.generic.permission.unset={1}의 {2} 컨텍스트에 대한 {0}을(를) 설정 해제했습니다
luckperms.command.generic.permission.doesnt-have={0}의 {2} 컨텍스트에 대한 {1}은(는) 설정되어 있지 않습니다
luckperms.command.generic.permission.unset-temp={1}의 {2} 컨텍스트에 대한 임시 {0}을(를) 설정 해제했습니다
luckperms.command.generic.permission.subtract={2}의 {0}을(를) {4} 컨텍스트에 대해 {3} 동안 {1}으(로) 설정했습니다 (이전보다 {5} 감소)
luckperms.command.generic.permission.doesnt-have-temp={0}의 {2} 컨텍스트에 대한 임시 {1}은(는) 설정되어 있지 않습니다
luckperms.command.generic.permission.clear={1} 컨텍스트에 대한 {0}의 모든 권한이 초기화 되었습니다
luckperms.command.generic.parent.info.title={0}의 상위 항목
luckperms.command.generic.parent.info.empty={0}은(는) 상위 항목이 지정되지 않았습니다
luckperms.command.generic.parent.info.click-to-remove=클릭하여 {0}에서 이 상위 항목을 제거합니다
luckperms.command.generic.parent.add={0}은(는) 이제 {2} 컨텍스트에서 {1}의 권한을 상속합니다
luckperms.command.generic.parent.add-temp={0}은(는) 이제 {2} 동안 {3} 컨텍스트에서 {1}의 권한을 상속합니다
luckperms.command.generic.parent.set={0}의 기존 상위 그룹이 제거되었으며, 이제 {2} 컨텍스트에서 {1}의 권한을 상속합니다
luckperms.command.generic.parent.set-track=트랙 {1}에 있는 {0}의 기존 상위 그룹이 제거되었으며, 이제 {3} 컨텍스트에서 {2}의 권한을 상속합니다
luckperms.command.generic.parent.remove={0}은(는) 더 이상 {2} 컨텍스트에서 {1}의 권한을 상속받지 않습니다
luckperms.command.generic.parent.remove-temp={0}은(는) 더 이상 {2} 컨텍스트에서 {1}의 권한을 임시로 상속받지 않습니다
luckperms.command.generic.parent.subtract={0}은(는) {2} 동안 {3} 컨텍스트에서 {1}의 권한을 상속받을 것입니다. (이전보다 {4} 감소)
luckperms.command.generic.parent.clear={0}의 상위 항목이 {1} 컨텍스트에서 삭제되었습니다
luckperms.command.generic.parent.clear-track=트랙 {1}에 있는 {0}의 상위 항목이 {2} 컨텍스트에서 제거되었습니다
luckperms.command.generic.parent.already-inherits={0}은(는) 이미 {2} 컨텍스트에서 {1}에게 상속받고 있습니다
luckperms.command.generic.parent.doesnt-inherit={0}은(는) {2} 컨텍스트에서 {1}에게 상속받고 있지 않습니다
luckperms.command.generic.parent.already-temp-inherits={0}은(는) 이미 {2} 컨텍스트에서 {1}에게 임시로 상속받고 있습니다
luckperms.command.generic.parent.doesnt-temp-inherit={0}은(는) {2} 컨텍스트에서 {1}에게 임시로 상속받고 있지 않습니다
luckperms.command.generic.chat-meta.info.title-prefix={0}의 접두사
luckperms.command.generic.chat-meta.info.title-suffix={0}의 접미사
luckperms.command.generic.chat-meta.info.none-prefix={0}은(는) 접두사를 가지고 있지 않습니다
luckperms.command.generic.chat-meta.info.none-suffix={0}은(는) 접미사를 가지고 있지 않습니다
luckperms.command.generic.chat-meta.info.click-to-remove=클릭하여 {1}에서 {0}을(를) 제거합니다
luckperms.command.generic.chat-meta.already-has={0}은(는) 이미 {4} 컨텍스트에서 우선 순위가 {3}으(로) 설정된 {1} {2}을(를) 가지고 있습니다
luckperms.command.generic.chat-meta.already-has-temp={0}은(는) 이미 {4} 컨텍스트에서 우선 순위가 {3}으(로) 설정된 임시적인 {1} {2}을(를) 가지고 있습니다
luckperms.command.generic.chat-meta.doesnt-have={0}은(는) {4} 컨텍스트에서 우선 순위가 {3}으(로) 설정된 {1} {2}을(를) 가지고 있지 않습니다
luckperms.command.generic.chat-meta.doesnt-have-temp={0}은(는) {4} 컨텍스트에서 우선 순위가 {3}으(로) 설정된 임시적인 {1} {2}을(를) 가지고 있지 않습니다
luckperms.command.generic.chat-meta.add={0}은(는) 이제 {4} 컨텍스트에서 우선 순위가 {3}으(로) 설정된 {1} {2}을(를) 가집니다
luckperms.command.generic.chat-meta.add-temp={0}은(는) 이제 {5} 컨텍스트에서 우선 순위가 {3}으(로) 설정된 {1} {2}을(를) {4} 동안 가집니다
luckperms.command.generic.chat-meta.remove={0}은(는) 이제 {4} 컨텍스트에서 우선 순위가 {3}으(로) 설정된 {1} {2}을(를) 가지지 않습니다
luckperms.command.generic.chat-meta.remove-bulk={0}은(는) 이제 {3} 컨텍스트에서 우선 순위가 {2}인 모든 {1}을(를) 가지지 않습니다
luckperms.command.generic.chat-meta.remove-temp={0}은(는) 이제 {4} 컨텍스트에서 우선 순위가 {3}인 일시적인 {1} {2}을(를) 가지지 않습니다
luckperms.command.generic.chat-meta.remove-temp-bulk={0}은(는) 이제 {3} 컨텍스트에서 우선 순위가 {2}인 모든 일시적인 {1}을(를) 가지지 않습니다
luckperms.command.generic.meta.info.title={0}의 메타 데이터
luckperms.command.generic.meta.info.none={0}은(는) 메타 데이터를 가지고 있지 않습니다
luckperms.command.generic.meta.info.click-to-remove=클릭하여 {0}에서 이 메타 데이터 노드를 제거합니다
luckperms.command.generic.meta.already-has={0}은(는) {3} 컨텍스트에서 {2}으(로) 설정된 메타 데이터 키 {1}을(를) 이미 가지고 있습니다
luckperms.command.generic.meta.already-has-temp={0}은(는) {3} 컨텍스트에서 {2}으(로) 임시로 설정된 메타 데이터 키 {1}을(를) 이미 가지고 있습니다
luckperms.command.generic.meta.doesnt-have={0}의 {2} 컨텍스트에 대한 메타 데이터 키 {1}은(는) 설정되어 있지 않습니다
luckperms.command.generic.meta.doesnt-have-temp={0}의 {2} 컨텍스트에 대한 임시 메타 데이터 키 {1}은(는) 설정되어 있지 않습니다
luckperms.command.generic.meta.set={2}의 메타 데이터 키 {0}을(를) 컨텍스트 {3}에 대해 {1}으(로) 설정했습니다
luckperms.command.generic.meta.set-temp={2}의 메타 데이터 키 {0}을(를) 컨텍스트 {4}에 대해 {3} 동안 {1}으(로) 유지되도록 설정했습니다
luckperms.command.generic.meta.unset={1}의 메타 데이터 키 {0}을(를) {2} 컨텍스트에 대해 초기화 했습니다
luckperms.command.generic.meta.unset-temp={1}의 임시 메타 데이터 키 {0}을(를) {2} 컨텍스트에 대해 초기화 했습니다
luckperms.command.generic.meta.clear={0}의 {1}과(와) 일치하는 메타 데이터 유형이 {2} 컨텍스트에서 에서 제거되었습니다
luckperms.command.generic.contextual-data.title=상황별 데이터
luckperms.command.generic.contextual-data.mode.key=모드
luckperms.command.generic.contextual-data.mode.server=서버
luckperms.command.generic.contextual-data.mode.active-player=활동적인 플레이어
luckperms.command.generic.contextual-data.contexts-key=컨텍스트
luckperms.command.generic.contextual-data.prefix-key=접두사
luckperms.command.generic.contextual-data.suffix-key=접미사
luckperms.command.generic.contextual-data.primary-group-key=주 그룹
luckperms.command.generic.contextual-data.meta-key=메타 데이터
luckperms.command.generic.contextual-data.null-result=없음
luckperms.command.user.info.title=사용자 정보
luckperms.command.user.info.uuid-key=UUID
luckperms.command.user.info.uuid-type-key=유형
luckperms.command.user.info.uuid-type.mojang=모장
luckperms.command.user.info.uuid-type.not-mojang=오프라인
luckperms.command.user.info.status-key=상태
luckperms.command.user.info.status.online=온라인
luckperms.command.user.info.status.offline=오프라인
luckperms.command.user.removegroup.error-primary=사용자를 주 그룹에서 제거할 수 없습니다
luckperms.command.user.primarygroup.not-member={0}은(는) 아직 {1} 소속이 아닙니다. 지금 추가해보세요
luckperms.command.user.primarygroup.already-has={0}은(는) 이미 {1}을(를) 주 그룹으로 설정했습니다
luckperms.command.user.primarygroup.warn-option=경고\: 이 서버({0})에서 사용 중인 주 그룹 계산 방법은 변경 사항을 반영하지 않을 수도 있습니다
luckperms.command.user.primarygroup.set={0}의 기본 그룹이 {1}으(로) 설정되었습니다
luckperms.command.user.track.error-not-contain-group={0}은(는) {1}의 어떤 그룹에도 속해 있지 않습니다
luckperms.command.user.track.unsure-which-track=사용할 트랙이 확실하지 않습니다. 매개 변수에 지정해 주세요
luckperms.command.user.track.missing-group-advice=그룹을 만들거나 트랙에서 제거한 후 다시 시도하십시오
luckperms.command.user.promote.added-to-first={0}은(는) {1}의 어떤 그룹에도 속해 있지 않으므로 {3} 컨텍스트에서 {2} 첫번째 그룹에 추가되었습니다
luckperms.command.user.promote.not-on-track={0}은(는) {1}의 어떤 그룹에도 속해 있지 않으므로 승격되지 않았습니다
luckperms.command.user.promote.success={4} 컨텍스트에서 트랙 {1}을(를) 따라 {0}을(를) {2}에서 {3}으(로) 승격합니다
luckperms.command.user.promote.end-of-track=트랙 {0}의 마지막에 도달하여 {1}을(를) 더 이상 승격할 수 없습니다
luckperms.command.user.promote.next-group-deleted=트랙의 다음 그룹 {0}이(가) 더 이상 존재하지 않습니다
luckperms.command.user.promote.unable-to-promote=사용자를 승격시킬 수 없습니다
luckperms.command.user.demote.success={4} 컨텍스트에서 트랙 {1}을(를) 따라 {0}을(를) {2}에서 {3}으(로) 강등합니다
luckperms.command.user.demote.end-of-track=트랙 {0}의 마지막에 도달하여 {1}이(가) {2}에서 제거되었습니다
luckperms.command.user.demote.end-of-track-not-removed=트랙 {0}의 마지막에 도달하였지만 {1}은(는) 첫 번째 그룹에서 제거되지 않았습니다
luckperms.command.user.demote.previous-group-deleted=트랙의 이전 그룹 {0}이(가) 더 이상 존재하지 않습니다
luckperms.command.user.demote.unable-to-demote=사용자를 강등시킬 수 없습니다
luckperms.command.group.list.title=그룹
luckperms.command.group.delete.not-default=기본 그룹은 삭제할 수 없습니다
luckperms.command.group.info.title=그룹 정보
luckperms.command.group.info.display-name-key=표시 이름
luckperms.command.group.info.weight-key=가중치
luckperms.command.group.setweight.set={1}의 가중치를 {0}으(로) 설정했습니다
luckperms.command.group.setdisplayname.doesnt-have={0}에는 표시 이름이 설정되어 있지 않습니다
luckperms.command.group.setdisplayname.already-has={0}은(는) 이미 표시 이름 {1}을(를) 가지고 있습니다
luckperms.command.group.setdisplayname.already-in-use=표시 이름 {0}은(는) 이미 {1}에서 사용되었습니다
luckperms.command.group.setdisplayname.set=그룹 {1}의 {2} 컨텍스트 표시 이름을 {0}으(로) 설정했습니다
luckperms.command.group.setdisplayname.removed=그룹 {0}의 {1} 컨텍스트 표시 이름을 제거했습니다
luckperms.command.track.list.title=트랙
luckperms.command.track.path.empty=없음
luckperms.command.track.info.showing-track=트랙 표시
luckperms.command.track.info.path-property=경로
luckperms.command.track.clear={0}의 그룹 트랙이 제거되었습니다
luckperms.command.track.append.success=그룹 {0}이(가) 트랙 {1}에 추가되었습니다
luckperms.command.track.insert.success=그룹 {0}이(가) 트랙 {1}의 {2} 위치에 삽입되었습니다
luckperms.command.track.insert.error-number=예상된 숫자이지만 대신 수신되었습니다\: {0}
luckperms.command.track.insert.error-invalid-pos={0} 위치에 삽입할 수 없습니다
luckperms.command.track.insert.error-invalid-pos-reason=유효하지 않은 위치입니다
luckperms.command.track.remove.success=그룹 {0}이(가) 트랙 {1}에서 제거되었습니다
luckperms.command.track.error-empty={0}은(는) 비어 있거나 하나의 그룹만 포함하므로 사용할 수 없습니다
luckperms.command.track.error-multiple-groups={0}은(는) 이 트랙에 있는 여러 그룹의 구성원입니다
luckperms.command.track.error-ambiguous=위치를 확인할 수 없습니다
luckperms.command.track.already-contains={0}은(는) 이미 {1}을(를) 포함하고 있습니다
luckperms.command.track.doesnt-contain={0}은(는) {1}을(를) 포함하고 있지 않습니다
luckperms.command.log.load-error=로그가 로드될 수 없었습니다
luckperms.command.log.invalid-page=유효하지 않은 페이지 번호
luckperms.command.log.invalid-page-range={0}과(와) {1} 사이의 값을 입력해 주세요
luckperms.command.log.empty=표시할 로그 항목이 없습니다
luckperms.command.log.notify.error-console=콘솔 알림은 전환할 수 없습니다
luckperms.command.log.notify.enabled-term=활성화됨
luckperms.command.log.notify.disabled-term=비활성화됨
luckperms.command.log.notify.changed-state=로깅 출력 {0}
luckperms.command.log.notify.already-on=당신은 이미 알림을 받고 있습니다
luckperms.command.log.notify.already-off=당신은 현재 알림을 받고 있지 않습니다
luckperms.command.log.notify.invalid-state=알 수 없는 상태입니다. {0} 또는 {1} 을(를) 예상합니다
luckperms.command.log.show.search=검색어 {0}에 대한 최근 작업 표시
luckperms.command.log.show.recent=최근 작업 표시
luckperms.command.log.show.by={0}의 최근 작업 표시
luckperms.command.log.show.history={0} {1}에 대한 기록 표시
luckperms.command.export.error-term=오류
luckperms.command.export.already-running=다른 내보내기 프로세스가 이미 실행 중입니다
luckperms.command.export.file.already-exists=파일 {0}이(가) 이미 존재합니다
luckperms.command.export.file.not-writable=파일 {0}이(가) 읽기 전용입니다
luckperms.command.export.file.success={0}에 성공적으로 내보냈습니다
luckperms.command.export.file-unexpected-error-writing=파일에 쓰는 동안 예기치 않은 오류가 발생했습니다
luckperms.command.export.web.export-code=내보내기 코드
luckperms.command.export.web.import-command-description=다음 명령을 사용하여 가져옵니다
luckperms.command.import.term=불러오기
luckperms.command.import.error-term=오류
luckperms.command.import.already-running=다른 불러오기 프로세스가 이미 실행 중입니다
luckperms.command.import.file.doesnt-exist=파일 {0}이(가) 존재하지 않습니다
luckperms.command.import.file.not-readable=파일 {0}은(는) 읽을 수 없습니다
luckperms.command.import.file.unexpected-error-reading=가져오기 파일을 읽는 도중 예기치 않은 오류가 발생했습니다
luckperms.command.import.file.correct-format=올바른 형식입니까?
luckperms.command.import.web.unable-to-read=주어진 코드를 사용해 데이터를 읽을 수 없습니다
luckperms.command.import.progress.percent={0}% 완료
luckperms.command.import.progress.operations={0}/{1} 작업 완료됨
luckperms.command.import.starting=불러오기 작업을 시작합니다
luckperms.command.import.completed=완료됨
luckperms.command.import.duration={0}초가 소요되었습니다
luckperms.command.bulkupdate.must-use-console=대량 업데이트 명령은 콘솔에서만 진행할 수 있습니다
luckperms.command.bulkupdate.invalid-data-type=유효하지 않은 유형입니다. {0}이(가) 필요합니다
luckperms.command.bulkupdate.invalid-constraint={0}은(는) 올바르지 않은 제약 조건입니다
luckperms.command.bulkupdate.invalid-constraint-format=제약 조건은 {0} 형식이여야 합니다
luckperms.command.bulkupdate.invalid-comparison={0}은(는) 올바르지 않은 비교 연산자입니다
luckperms.command.bulkupdate.invalid-comparison-format=다음 중 하나가 필요합니다\: {0}
luckperms.command.bulkupdate.queued=대량 업데이트 작업이 대기열에 추가되었습니다
luckperms.command.bulkupdate.confirm={0}을(를) 사용해 업데이트를 실행합니다
luckperms.command.bulkupdate.unknown-id=동작 ID {0}이(가) 존재하지 않거나 만료되었습니다
luckperms.command.bulkupdate.starting=대량 업데이트 실행
luckperms.command.bulkupdate.success=대량 업데이트가 성공적으로 완료되었습니다
luckperms.command.bulkupdate.success.statistics.nodes=영향을 받은 총 노드
luckperms.command.bulkupdate.success.statistics.users=영향을 받은 총 사용자
luckperms.command.bulkupdate.success.statistics.groups=영향을 받은 총 그룹
luckperms.command.bulkupdate.failure=대량 업데이트에 실패했습니다. 콘솔에서 오류를 확인하십시오
luckperms.command.update-task.request=업데이트 작업이 요쳥되었습니다. 잠시만 기다려 주세요
luckperms.command.update-task.complete=업데이트 작업이 완료되었습니다
luckperms.command.update-task.push.attempting=이제 다른 서버로 전송을 시도합니다
luckperms.command.update-task.push.complete=다른 서버는 {0}을(를) 통해 알림을 성공적으로 받았습니다
luckperms.command.update-task.push.error=변경 사항을 다른 서버로 전송하는 도중 오류가 발생했습니다
luckperms.command.update-task.push.error-not-setup=메시징 서비스가 구성되지 않았으므로 변경 사항을 다른 서버에 전송할 수 없습니다
luckperms.command.reload-config.success=설정 파일이 다시 로드되었습니다
luckperms.command.reload-config.restart-note=일부 설정은 서버가 다시 시작된 후에 적용됩니다
luckperms.command.translations.searching=사용 가능한 번역을 검색하는 중입니다. 잠시만 기다려 주세요...
luckperms.command.translations.searching-error=사용 가능한 번역 목록을 가져올 수 없습니다
luckperms.command.translations.installed-translations=설치된 번역
luckperms.command.translations.available-translations=사용 가능한 번역
luckperms.command.translations.percent-translated={0}% 번역됨
luckperms.command.translations.translations-by=기여자\:
luckperms.command.translations.installing=번역을 설치하는 중입니다. 잠시만 기다려 주세요...
luckperms.command.translations.download-error={0}에 대한 번역을 다운로드할 수 없습니다
luckperms.command.translations.installing-specific=언어 {0} 설치 중...
luckperms.command.translations.install-complete=설치가 완료되었습니다
luckperms.command.translations.download-prompt={0} 을(를) 사용하여 커뮤니티에서 제공하는 번역의 최신 버전을 다운로드하고 설치하실 수 있습니다
luckperms.command.translations.download-override-warning=참고하세요, 이는 해당 언어에 대한 모든 변경 내용을 덮어씁니다
luckperms.usage.user.description=LuckPerms 내에서 사용자를 관리하기 위한 명령어 집합입니다. (LuckPerms의 ''사용자''는 플레이어일 뿐이며, UUID 또는 사용자 이름을 참조할 수 있습니다)
luckperms.usage.group.description=LuckPerms 내에서 그룹을 관리하기 위한 명령어 집합입니다. 그룹은 사용자에게 부여할 수 있는 권한 할당 모음일 뿐입니다. 새 그룹은 ''create group'' 명령을 사용하여 만들 수 있습니다.
luckperms.usage.track.description=LuckPerms 내에서 트랙을 관리하기 위한 명령어 집합입니다. 트랙은 승급 및 강등을 정의하는데 사용할 수 있는 정렬된 그룹 모음입니다.
luckperms.usage.log.description=LuckPerms 내에서 로깅 기능을 관리하기 위한 명령 집합입니다.
luckperms.usage.sync.description=플러그인의 모든 데이터를 메모리로 다시 로드하고, 변경된 모든 사항을 적용합니다
luckperms.usage.info.description=활성 플러그인 호출에 대한 일반적인 정보를 인쇄합니다
luckperms.usage.editor.description=새 웹 편집기 세션을 만듭니다
luckperms.usage.editor.argument.type=편집기에 로드할 유형들입니다. (''all'', ''users'' 또는 ''groups'')
luckperms.usage.editor.argument.filter=사용자 항목을 필터링할 수 있는 권한
luckperms.usage.verbose.description=플러그인 상세 권한 검사 모니터링 시스템을 제어합니다
luckperms.usage.verbose.argument.action=로깅을 활성화/비활성화 할지 또는 로깅된 출력을 업로드할 지에 대한 여부
luckperms.usage.verbose.argument.filter=항목을 일치시킬 필터
luckperms.usage.verbose.argument.commandas=실행할 플레이어 및 명령어
luckperms.usage.tree.description=LuckPerms에 알려진 모든 권한의 트리(순서 목록 계층 구조)를 생성합니다.
luckperms.usage.tree.argument.scope=트리의 최상위 권한. "."을(를) 지정하여 모든 권한을 포함함
luckperms.usage.tree.argument.player=확인할 온라인 플레이어의 이름
luckperms.usage.search.description=특정 권한이 있는 모든 사용자 및 그룹을 검색합니다
luckperms.usage.search.argument.permission=검색할 권한
luckperms.usage.search.argument.page=확인할 페이지
luckperms.usage.network-sync.description=변경 사항을 저장소화 동기화하고 네트워크의 다른 모든 서버가 동일한 작업을 수행하도록 요청합니다
luckperms.usage.import.description=(이전에 생성된) 내보낸 파일에서 데이터를 가져옵니다
luckperms.usage.import.argument.file=불러올 파일
luckperms.usage.import.argument.replace=병합하는 대신 기존 데이터 교체
luckperms.usage.import.argument.upload=이전 내보내기에서 데이터 업로드
luckperms.usage.export.description=모든 권한 데이터를 ''export'' 파일에 내보냅니다. 추후 다시 불러올 수 있습니다.
luckperms.usage.export.argument.file=내보낼 파일
luckperms.usage.export.argument.without-users=내보내기에서 사용자 제외
luckperms.usage.export.argument.without-groups=내보내기에서 그룹 제외
luckperms.usage.export.argument.upload=모든 권한 데이터를 웹 에디터에 업로드합니다. 추후 다시 불러올 수 있습니다.
luckperms.usage.reload-config.description=일부 설정을 다시 로드합니다
luckperms.usage.bulk-update.description=모든 대에터에 대해 대량 변경 쿼리 실행
luckperms.usage.bulk-update.argument.data-type=변경되는 데이터의 유형. (''all'', ''users'' 또는 ''groups'')
luckperms.usage.bulk-update.argument.action=데이터에 대해 수행할 작업. (''update'' 또는 ''delete'')
luckperms.usage.bulk-update.argument.action-field=동작 유형. ''update''에만 필요합니다. (''permission'', ''server'' 또는 ''world'')
luckperms.usage.bulk-update.argument.action-value=대체할 값. ''update''에만 필요합니다
luckperms.usage.bulk-update.argument.constraint=업데이트에 필요한 제약
luckperms.usage.translations.description=번역 관리
luckperms.usage.translations.argument.install=번역을 설치하는 하위 명령
luckperms.usage.apply-edits.description=웹 편집기에서 변경한 권한을 적용합니다
luckperms.usage.apply-edits.argument.code=데이터의 고유 코드
luckperms.usage.apply-edits.argument.target=데이터를 적용할 대상
luckperms.usage.create-group.description=새로운 그룹을 생성합니다
luckperms.usage.create-group.argument.name=그룹 이름
luckperms.usage.create-group.argument.weight=그룹의 가중치
luckperms.usage.create-group.argument.display-name=그룹 표시 이름
luckperms.usage.delete-group.description=그룹을 삭제합니다
luckperms.usage.delete-group.argument.name=그룹 이름
luckperms.usage.list-groups.description=플랫폼의 모든 그룹을 나열합니다
luckperms.usage.create-track.description=새로운 트랙을 만듭니다
luckperms.usage.create-track.argument.name=트랙 이름
luckperms.usage.delete-track.description=트랙을 제거합니다
luckperms.usage.delete-track.argument.name=트랙 이름
luckperms.usage.list-tracks.description=플랫폼의 모든 트랙을 나열합니다
luckperms.usage.user-info.description=사용자에 대한 정보를 표시합니다
luckperms.usage.user-switchprimarygroup.description=사용자의 기본 그룹을 변경합니다
luckperms.usage.user-switchprimarygroup.argument.group=변경할 그룹
luckperms.usage.user-promote.description=사용자를 트랙에서 승급시킵니다
luckperms.usage.user-promote.argument.track=사용자를 승급시킬 트랙
luckperms.usage.user-promote.argument.context=승급이 적용될 컨텍스트
luckperms.usage.user-promote.argument.dont-add-to-first=사용자가 이미 트랙에 있는 경우에만 승급
luckperms.usage.user-demote.description=사용자를 트랙에서 강등시킵니다
luckperms.usage.user-demote.argument.track=사용자를 강등시킬 트랙
luckperms.usage.user-demote.argument.context=강등이 적용될 컨텍스트
luckperms.usage.user-demote.argument.dont-remove-from-first=사용자가 첫 번째 그룹에서 제거되지 않도록 하기
luckperms.usage.user-clone.description=사용자를 복제합니다
luckperms.usage.user-clone.argument.user=복제 대상 사용자 이름 혹은 UUID
luckperms.usage.group-info.description=그룹에 대한 정보를 제공합니다
luckperms.usage.group-listmembers.description=이 그룹에서 상속된 사용자 및 그룹 표시
luckperms.usage.group-listmembers.argument.page=확인할 페이지
luckperms.usage.group-setweight.description=그룹의 가중치를 설정합니다
luckperms.usage.group-setweight.argument.weight=설정할 가중치
luckperms.usage.group-set-display-name.description=그룹의 표시 이름을 설정합니다
luckperms.usage.group-set-display-name.argument.name=설정할 이름
luckperms.usage.group-set-display-name.argument.context=표시 이름이 적용될 컨텍스트
luckperms.usage.group-rename.description=그룹의 이름을 변경합니다
luckperms.usage.group-rename.argument.name=새로운 이름
luckperms.usage.group-clone.description=그룹을 복제합니다
luckperms.usage.group-clone.argument.name=복제 대상 그룹 이름
luckperms.usage.holder-editor.description=웹 권한 에디터를 엽니다
luckperms.usage.holder-showtracks.description=개체가 있는 트랙을 나열합니다
luckperms.usage.holder-clear.description=모든 권한, 상속, 메타 데이터를 제거합니다
luckperms.usage.holder-clear.argument.context=필터링할 컨텍스트
luckperms.usage.permission.description=권한 편집
luckperms.usage.parent.description=상속을 편집합니다
luckperms.usage.meta.description=메타 데이터 값을 편집합니다
luckperms.usage.permission-info.description=개체에 있는 권한 노드를 나열합니다
luckperms.usage.permission-info.argument.page=확인할 페이지
luckperms.usage.permission-info.argument.sort-mode=항목을 정렬할 방법
luckperms.usage.permission-set.description=개체의 권한을 설정합니다
luckperms.usage.permission-set.argument.node=설정할 권한 노드
luckperms.usage.permission-set.argument.value=설정할 값
luckperms.usage.permission-set.argument.context=권한을 추가할 컨텍스트
luckperms.usage.permission-unset.description=개체의 권한을 설정 해제합니다
luckperms.usage.permission-unset.argument.node=초기화할 권한 노드
luckperms.usage.permission-unset.argument.context=권한을 제거할 컨텍스트
luckperms.usage.permission-settemp.description=개체에 대한 권한을 일시적으로 설정합니다
luckperms.usage.permission-settemp.argument.node=설정할 권한 노드
luckperms.usage.permission-settemp.argument.value=설정할 값
luckperms.usage.permission-settemp.argument.duration=권한 노드가 만료되기까지의 시간
luckperms.usage.permission-settemp.argument.temporary-modifier=일시적인 권한을 적용하는 방법
luckperms.usage.permission-settemp.argument.context=권한을 추가할 컨텍스트
luckperms.usage.permission-unsettemp.description=개체의 일시적인 권한을 설정 해제합니다
luckperms.usage.permission-unsettemp.argument.node=설정 해제할 권한 노드
luckperms.usage.permission-unsettemp.argument.duration=차감할 시간
luckperms.usage.permission-unsettemp.argument.context=권한을 제거할 컨텍스트
luckperms.usage.permission-check.description=개체가 특정 권한 노드를 가지고 있는지 확인합니다
luckperms.usage.permission-check.argument.node=확인할 권한
luckperms.usage.permission-clear.description=모든 권한 제거
luckperms.usage.permission-clear.argument.context=필터링할 컨텍스트
luckperms.usage.parent-info.description=이 개체가 상속하는 그룹을 나열합니다
luckperms.usage.parent-info.argument.page=확인할 페이지
luckperms.usage.parent-info.argument.sort-mode=항목을 정렬할 방법
luckperms.usage.parent-set.description=객체가 이미 상속한 모든 그룹을 제거하고 주어진 그룹에 추가합니다
luckperms.usage.parent-set.argument.group=설정할 그룹
luckperms.usage.parent-set.argument.context=그룹이 설정될 컨텍스트
luckperms.usage.parent-add.description=개체가 권한을 상속할 다른 그룹을 설정합니다
luckperms.usage.parent-add.argument.group=상속받을 그룹
luckperms.usage.parent-add.argument.context=상속받을 그룹이 설정될 컨텍스트
luckperms.usage.parent-remove.description=이전에 설정한 상속 규칙을 제거합니다
luckperms.usage.parent-remove.argument.group=제거할 그룹
luckperms.usage.parent-remove.argument.context=그룹에 제거될 컨텍스트
luckperms.usage.parent-set-track.description=주어진 트랙에서 이미 객체가 상속받은 다른 모든 그룹을 제거하고 주어진 그룹에 추가합니다
luckperms.usage.parent-set-track.argument.track=설정할 트랙
luckperms.usage.parent-set-track.argument.group=설정할 그룹. 또는 주어진 트랙에서 그룹의 위치와 관련된 숫자
luckperms.usage.parent-set-track.argument.context=그룹이 설정될 컨텍스트
luckperms.usage.parent-add-temp.description=개체가 일시적으로 권한을 상속하도록 다른 그룹을 설정합니다
luckperms.usage.parent-add-temp.argument.group=상속받을 그룹
luckperms.usage.parent-add-temp.argument.duration=그룹 멤버십 기간
luckperms.usage.parent-add-temp.argument.temporary-modifier=일시적인 권한을 적용하는 방법
luckperms.usage.parent-add-temp.argument.context=상속받을 그룹이 설정될 컨텍스트
luckperms.usage.parent-remove-temp.description=이전에 설정한 일시적인 상속 규칙을 제거합니다
luckperms.usage.parent-remove-temp.argument.group=제거할 그룹
luckperms.usage.parent-remove-temp.argument.duration=차감할 시간
luckperms.usage.parent-remove-temp.argument.context=그룹에 제거될 컨텍스트
luckperms.usage.parent-clear.description=모든 상위 그룹을 지웁니다
luckperms.usage.parent-clear.argument.context=필터링할 컨텍스트
luckperms.usage.parent-clear-track.description=주어진 트랙에서 모든 상위 항목을 지웁니다
luckperms.usage.parent-clear-track.argument.track=지울 트랙
luckperms.usage.parent-clear-track.argument.context=필터링할 컨텍스트
luckperms.usage.meta-info.description=모든 대화 메타 데이터를 표시합니다
luckperms.usage.meta-set.description=메타 데이터 값을 설정합니다
luckperms.usage.meta-set.argument.key=설정할 키
luckperms.usage.meta-set.argument.value=설정할 값
luckperms.usage.meta-set.argument.context=메타 데이터 쌍을 추가할 컨텍스트
luckperms.usage.meta-unset.description=메타 데이터 값을 설정 해제합니다
luckperms.usage.meta-unset.argument.key=설정 해제할 키
luckperms.usage.meta-unset.argument.context=메타 데이터 쌍을 제거할 컨텍스트
luckperms.usage.meta-settemp.description=메타 데이터 값을 일시적으로 설정합니다
luckperms.usage.meta-settemp.argument.key=설정할 키
luckperms.usage.meta-settemp.argument.value=설정할 값
luckperms.usage.meta-settemp.argument.duration=메타 데이터 값이 만료되기까지의 시간
luckperms.usage.meta-settemp.argument.context=메타 데이터 쌍을 추가할 컨텍스트
luckperms.usage.meta-unsettemp.description=일시적인 메타 데이터 값을 설정 해제합니다
luckperms.usage.meta-unsettemp.argument.key=설정 해제할 키
luckperms.usage.meta-unsettemp.argument.context=메타 데이터 쌍을 제거할 컨텍스트
luckperms.usage.meta-addprefix.description=접두사 추가
luckperms.usage.meta-addprefix.argument.priority=접두어가 추가될 우선 순위
luckperms.usage.meta-addprefix.argument.prefix=접두어 문자열
luckperms.usage.meta-addprefix.argument.context=접두어를 추가할 컨텍스트
luckperms.usage.meta-addsuffix.description=접미사 추가
luckperms.usage.meta-addsuffix.argument.priority=추가할 접미어의 우선 순위
luckperms.usage.meta-addsuffix.argument.suffix=접미어 문자열
luckperms.usage.meta-addsuffix.argument.context=접미어를 추가할 컨텍스트
luckperms.usage.meta-setprefix.description=접두사 설정
luckperms.usage.meta-setprefix.argument.priority=접두어가 설정될 우선 순위
luckperms.usage.meta-setprefix.argument.prefix=접두어 문자열
luckperms.usage.meta-setprefix.argument.context=접두어가 설정될 컨텍스트
luckperms.usage.meta-setsuffix.description=접미사 설정
luckperms.usage.meta-setsuffix.argument.priority=접미어를 설정할 우선 순위
luckperms.usage.meta-setsuffix.argument.suffix=접미어 문자열
luckperms.usage.meta-setsuffix.argument.context=접미어가 설정될 컨텍스트
luckperms.usage.meta-removeprefix.description=접두사 제거
luckperms.usage.meta-removeprefix.argument.priority=접두어가 제거될 우선 순위
luckperms.usage.meta-removeprefix.argument.prefix=접두어 문자열
luckperms.usage.meta-removeprefix.argument.context=접두어가 제거될 컨텍스트
luckperms.usage.meta-removesuffix.description=접미사 제거
luckperms.usage.meta-removesuffix.argument.priority=접미어가 제거될 우선 순위
luckperms.usage.meta-removesuffix.argument.suffix=접미어 문자열
luckperms.usage.meta-removesuffix.argument.context=접미어가 제거될 컨텍스트
luckperms.usage.meta-addtemp-prefix.description=일시적으로 적용되는 접두어를 추가합니다
luckperms.usage.meta-addtemp-prefix.argument.priority=접두어가 추가될 우선 순위
luckperms.usage.meta-addtemp-prefix.argument.prefix=접두어 문자열
luckperms.usage.meta-addtemp-prefix.argument.duration=접두어가 만료되기까지의 시간
luckperms.usage.meta-addtemp-prefix.argument.context=접두어를 추가할 컨텍스트
luckperms.usage.meta-addtemp-suffix.description=일시적으로 적용되는 접미어를 추가합니다
luckperms.usage.meta-addtemp-suffix.argument.priority=추가할 접미어의 우선 순위
luckperms.usage.meta-addtemp-suffix.argument.suffix=접미어 문자열
luckperms.usage.meta-addtemp-suffix.argument.duration=접미어가 만료되기까지의 시간
luckperms.usage.meta-addtemp-suffix.argument.context=접미어를 추가할 컨텍스트
luckperms.usage.meta-settemp-prefix.description=일시적으로 적용되는 접두어를 설정합니다
luckperms.usage.meta-settemp-prefix.argument.priority=접두어가 설정될 우선 순위
luckperms.usage.meta-settemp-prefix.argument.prefix=접두어 문자열
luckperms.usage.meta-settemp-prefix.argument.duration=접두어가 만료되기까지의 시간
luckperms.usage.meta-settemp-prefix.argument.context=접두어가 설정될 컨텍스트
luckperms.usage.meta-settemp-suffix.description=일시적으로 적용되는 접미어를 설정합니다
luckperms.usage.meta-settemp-suffix.argument.priority=접미어를 설정할 우선 순위
luckperms.usage.meta-settemp-suffix.argument.suffix=접미어 문자열
luckperms.usage.meta-settemp-suffix.argument.duration=접미어가 만료되기까지의 시간
luckperms.usage.meta-settemp-suffix.argument.context=접미어가 설정될 컨텍스트
luckperms.usage.meta-removetemp-prefix.description=일시적으로 적용되는 접두어를 제거합니다
luckperms.usage.meta-removetemp-prefix.argument.priority=접두어가 제거될 우선 순위
luckperms.usage.meta-removetemp-prefix.argument.prefix=접두어 문자열
luckperms.usage.meta-removetemp-prefix.argument.context=접두어가 제거될 컨텍스트
luckperms.usage.meta-removetemp-suffix.description=일시적으로 적용되는 접미어를 제거합니다
luckperms.usage.meta-removetemp-suffix.argument.priority=접미어가 제거될 우선 순위
luckperms.usage.meta-removetemp-suffix.argument.suffix=접미어 문자열
luckperms.usage.meta-removetemp-suffix.argument.context=접미어가 제거될 컨텍스트
luckperms.usage.meta-clear.description=모든 메타 데이터를 지웁니다
luckperms.usage.meta-clear.argument.type=제거할 메타 데이터 유형
luckperms.usage.meta-clear.argument.context=필터링할 컨텍스트
luckperms.usage.track-info.description=트랙에 대한 정보를 제공합니다
luckperms.usage.track-editor.description=웹 권한 에디터를 엽니다
luckperms.usage.track-append.description=트랙 마지막에 그룹을 추가합니다
luckperms.usage.track-append.argument.group=추가할 그룹
luckperms.usage.track-insert.description=트랙의 주어진 위치에 그룹을 삽입합니다
luckperms.usage.track-insert.argument.group=삽입할 그룹
luckperms.usage.track-insert.argument.position=그룹을 삽입할 위치 (트랙의 첫 번째 위치는 1임)
luckperms.usage.track-remove.description=트랙에서 그룹을 제거합니다
luckperms.usage.track-remove.argument.group=제거할 그룹
luckperms.usage.track-clear.description=트랙에서 모든 그룹을 지웁니다
luckperms.usage.track-rename.description=트랙의 이름을 변경합니다
luckperms.usage.track-rename.argument.name=새로운 이름
luckperms.usage.track-clone.description=트랙을 복제합니다
luckperms.usage.track-clone.argument.name=복제 대상 트랙 이름
luckperms.usage.log-recent.description=최근 작업을 봅니다
luckperms.usage.log-recent.argument.user=필터링할 사용자 이름 혹은 UUID
luckperms.usage.log-recent.argument.page=확인할 페이지 번호
luckperms.usage.log-search.description=로그에서 항목을 검색합니다
luckperms.usage.log-search.argument.query=검색어
luckperms.usage.log-search.argument.page=확인할 페이지 번호
luckperms.usage.log-notify.description=로그 알림 상태를 전환합니다
luckperms.usage.log-notify.argument.toggle=on 혹은 off
luckperms.usage.log-user-history.description=사용자의 기록을 확인합니다
luckperms.usage.log-user-history.argument.user=사용자 이름 혹은 UUID
luckperms.usage.log-user-history.argument.page=확인할 페이지 번호
luckperms.usage.log-group-history.description=그룹의 기록을 확인합니다
luckperms.usage.log-group-history.argument.group=그룹 이름
luckperms.usage.log-group-history.argument.page=확인할 페이지 번호
luckperms.usage.log-track-history.description=트랙의 기록을 확인합니다
luckperms.usage.log-track-history.argument.track=트랙 이름
luckperms.usage.log-track-history.argument.page=확인할 페이지 번호
luckperms.usage.sponge.description=추가적인 Sponge 데이터를 편집합니다
luckperms.usage.sponge.argument.collection=쿼리할 컬렉션
luckperms.usage.sponge.argument.subject=수정할 주제
luckperms.usage.sponge-permission-info.description=주제의 권한에 대한 정보를 표시합니다
luckperms.usage.sponge-permission-info.argument.contexts=필터링할 컨텍스트
luckperms.usage.sponge-permission-set.description=주제의 권한을 설정합니다
luckperms.usage.sponge-permission-set.argument.node=설정할 권한 노드
luckperms.usage.sponge-permission-set.argument.tristate=권한을 설정할 값
luckperms.usage.sponge-permission-set.argument.contexts=권한을 설정할 컨텍스트
luckperms.usage.sponge-permission-clear.description=주제의 모든 권한을 지웁니다
luckperms.usage.sponge-permission-clear.argument.contexts=권한을 지울 컨텍스트
luckperms.usage.sponge-parent-info.description=주제의 상위 항목에 대한 정보를 표시합니다
luckperms.usage.sponge-parent-info.argument.contexts=필터링할 컨텍스트
luckperms.usage.sponge-parent-add.description=주제에 상위 항목을 추가합니다
luckperms.usage.sponge-parent-add.argument.collection=상위 주제가 있는 주제 컬렉션
luckperms.usage.sponge-parent-add.argument.subject=상위 주제의 이름
luckperms.usage.sponge-parent-add.argument.contexts=상위 항목이 추가될 컨텍스트
luckperms.usage.sponge-parent-remove.description=주제에서 상위 항목을 제거합니다
luckperms.usage.sponge-parent-remove.argument.collection=상위 주제가 있는 주제 컬렉션
luckperms.usage.sponge-parent-remove.argument.subject=상위 주제의 이름
luckperms.usage.sponge-parent-remove.argument.contexts=상위 항목이 제거될 컨텍스트
luckperms.usage.sponge-parent-clear.description=주제의 모든 상위 항목을 지웁니다
luckperms.usage.sponge-parent-clear.argument.contexts=상위 항목을 지울 컨텍스트
luckperms.usage.sponge-option-info.description=주제의 옵션에 대한 정보를 표시합니다
luckperms.usage.sponge-option-info.argument.contexts=필터링할 컨텍스트
luckperms.usage.sponge-option-set.description=주제의 옵션을 설정합니다
luckperms.usage.sponge-option-set.argument.key=설정할 키
luckperms.usage.sponge-option-set.argument.value=설정할 값
luckperms.usage.sponge-option-set.argument.contexts=설정될 컨텍스트
luckperms.usage.sponge-option-unset.description=주제의 옵션을 설정 해제합니다
luckperms.usage.sponge-option-unset.argument.key=설정 해제할 키
luckperms.usage.sponge-option-unset.argument.contexts=키가 설정 해제될 컨텍스트
luckperms.usage.sponge-option-clear.description=주제의 모든 옵션을 지웁니다
luckperms.usage.sponge-option-clear.argument.contexts=옵션을 지울 컨텍스트
