{"skinsrestorer.help_skin": "Cambia la tua skin.", "skinsrestorer.help_skins": "Apri il menu delle skin.", "skinsrestorer.help_sr": "Comandi amministratori per SkinsRestorer.", "skinsrestorer.help_skin_help": "Mostra questo comando di aiuto.", "skinsrestorer.help_skin_set": "Reimposta la tua skin.", "skinsrestorer.help_skin_set_other": "Imposta la skin di un giocatore.", "skinsrestorer.help_skin_set_url": "Cambia la pelle da un collegamento URL.", "skinsrestorer.help_skin_clear": "Reimposta la tua skin.", "skinsrestorer.help_skin_clear_other": "Reimposta la skin di un giocatore.", "skinsrestorer.help_skin_random": "Dà una skin casuale.", "skinsrestorer.help_skin_random_other": "Imposta una skin casuale per un altro giocatore.", "skinsrestorer.help_skin_search": "Cerca una skin che vuoi.", "skinsrestorer.help_skin_update": "Aggiorna la tua skin.", "skinsrestorer.help_skin_update_other": "Aggiorna la skin di un giocatore.", "skinsrestorer.help_skin_undo": "Ritorna alla skin precedente.", "skinsrestorer.help_skin_undo_other": "<PERSON><PERSON><PERSON><PERSON> la skin di un altro giocatorea quella precedente.", "skinsrestorer.help_skin_favourite": "Salva la tua skin come preferita.", "skinsrestorer.help_skin_favourite_other": "Salva la skin di un altro giocatore come preferita.", "skinsrestorer.help_sr_reload": "Ricarica il file di configurazione.", "skinsrestorer.help_sr_status": "Controlla i servizi API plugin richiesti.", "skinsrestorer.help_sr_drop": "Rimuove i dati del giocatore o della skin dal database.", "skinsrestorer.help_sr_info": "Visualizza informazioni su un giocatore o una skin.", "skinsrestorer.help_sr_apply_skin": "Riapplica la skin di un giocatore.", "skinsrestorer.help_sr_create_custom": "Crea una skin su larga scala per il server.", "skinsrestorer.help_sr_purge_old_data": "Elimina i dati delle skin vecchi da oltre x giorni fa.", "skinsrestorer.help_sr_dump": "Carica i dati di supporto su bytebin.lucko.me.", "skinsrestorer.success_generic": "<dark_green><message>", "skinsrestorer.info_use_quotes": "Gli URL devono avere le virgolette. Esempio: <yellow>/skin set \"https://esempio.com/skin.png\"</yellow> (Puoi premere TAB per completare le virgolette)", "skinsrestorer.success_skin_change": "La tua skin è stata cambiata correttamente.", "skinsrestorer.success_skin_change_other": "<PERSON> cambiato la skin di <yellow><name></yellow>.", "skinsrestorer.success_skin_undo": "La tua skin <yellow><skin></yellow> è stata ripristinata alla skin del <yellow><timestamp></yellow>.", "skinsrestorer.success_skin_undo_other": "La skin <yellow><skin></yellow> di <yellow><name></yellow> è stata ripristinata alla skin del <yellow><timestamp></yellow>.", "skinsrestorer.success_skin_favourite": "La tua skin <yellow><skin></yellow> è stata impostata come preferita.", "skinsrestorer.success_skin_favourite_other": "La skin <yellow><skin></yellow> di <yellow><name></yellow> è stata impostata come preferita.", "skinsrestorer.success_skin_unfavourite": "La tua skin preferita <yellow><skin></yellow> di <yellow><timestamp></yellow> è stata tolta dai preferiti.", "skinsrestorer.success_skin_unfavourite_other": "La skin preferita <yellow><skin></yellow> di <yellow><name></yellow> del <yellow><timestamp></yellow> è stata rimossa dai favoriti.", "skinsrestorer.success_skin_clear": "La tua skin è stata cancellata.", "skinsrestorer.success_skin_clear_other": "Skin cancellata per il giocatore <yellow><name></yellow>.", "skinsrestorer.success_updating_skin": "La tua skin è stata aggiornata.", "skinsrestorer.success_updating_skin_other": "Skin aggiornata per il giocatore <yellow><name></yellow>.", "skinsrestorer.success_admin_applyskin": "La skin è stata aggiornata!", "skinsrestorer.success_admin_createcustom": "Skin <yellow><skin></yellow> creata!", "skinsrestorer.success_admin_setcustomname": "Il nome della skin <yellow><skin></yellow> è stato impostato su <yellow><display_name></yellow>.", "skinsrestorer.success_admin_drop": "Dati <type> cancellati per <target>.", "skinsrestorer.success_admin_reload": "Configurazione e localizzazione ricaricati correttamente!", "skinsrestorer.success_history_line": "<dark_green>- <hover:show_text:'<dark_green><PERSON><PERSON><PERSON> per usare <yellow><skin></yellow>'><click:run_command:/skin set \"<skin_id>\"><yellow><skin></yellow></click></hover> del <yellow><timestamp></yellow>", "skinsrestorer.success_favourites_line": "<dark_green>- <hover:show_text:'<dark_green><PERSON><PERSON><PERSON> per usare <yellow><skin></yellow>'><click:run_command:/skin set \"<skin_id>\"><yellow><skin></yellow></click></hover> da <yellow><timestamp></yellow>", "skinsrestorer.error_generic": "<dark_red>Errore<dark_gray>: <red><message>", "skinsrestorer.error_generic_skin": "Si è verificato un errore durante la richiesta di dati della skin, per favore riprova più tardi!", "skinsrestorer.error_no_undo": "Non hai nessuna skin a cui tornare indietro!", "skinsrestorer.error_no_skin_to_favourite": "Non hai nessuna skin da impostare come preferita!", "skinsrestorer.error_skin_disabled": "Questa skin è stata disabilitata da un amministratore.", "skinsrestorer.error_skinurl_disallowed": "Questo dominio non è stato autorizzato dall'amministratore.", "skinsrestorer.error_updating_skin": "Si è verificato un errore durante l'aggiornamento della tua skin. Riprova più tardi!", "skinsrestorer.error_updating_url": "Non puoi aggiornare skin URL personalizzate! <newline><red>Richiedi di nuovo utilizzando /skin URL", "skinsrestorer.error_updating_customskin": "La skin non può essere aggiornata perché è personalizzata.", "skinsrestorer.error_admin_applyskin": "La skin del giocatore NON può essere aggiornata!", "skinsrestorer.error_ms_full": "Il collegamento con la API di MineSkin è scaduto durante il caricamento della tua skin. Riprova più tardi.", "skinsrestorer.error_ms_api_failed": "L'API MineSkin è sovraccarica, riprova più tardi!", "skinsrestorer.error_ms_api_key_invalid": "Chiave API MineSkin non valida! Contatta un amministratore per segnalare il problema!", "skinsrestorer.error_ms_unknown": "Errore MineSkin sconosciuto!", "skinsrestorer.error_no_history": "Non hai nessuna storco della skin!", "skinsrestorer.error_no_favourites": "Non hai skin preferite!", "skinsrestorer.not_connected_to_server": "<red>Non sei connesso a nessun server.", "skinsrestorer.divider": "<dark_aqua>--------------------------------------", "skinsrestorer.admincommand_status_checking": "<gray>Verifica dei servizi necessari affinché SR funzioni correttamente...", "skinsrestorer.admincommand_status_uuid_api": "<gray>UUID API funzionanti: <count>/<total>", "skinsrestorer.admincommand_status_summary_server": "<gray>Server: <gold><version>", "skinsrestorer.admincommand_status_summary_proxymode": "<gray>ProxyMode: <gold><proxy_mode>", "skinsrestorer.skinsmenu_open": "{prefix} &2Apertura del menù delle skin...", "skinsrestorer.skinsmenu_next_page": "<green><bold>»</bold> <gray>Pro<PERSON><PERSON></gray> <bold>»</bold>", "skinsrestorer.skinsmenu_clear_skin": "<red><bold>[</bold> <gray>Rimuovi Skin</gray> <bold>]</bold>", "skinsrestorer.skinsmenu_favourites_button": "<aqua><bold>[</bold> <gray>Menu Preferiti</gray> <bold>]</bold>", "skinsrestorer.skinsmenu_back_select_button": "<yellow><bold>«</bold> <gray>Menu selezione</gray> <bold>«</bold>", "skinsrestorer.no_skin_data": "<dark_red>Errore<dark_gray>: <red><PERSON><PERSON><PERSON> dato skin trovato!", "skinsrestorer.unsupported_java": "<dark_red>La versione Java (<version>) della tua <platform> non è supportata da SkinsRestorer!<newline><red>Si prega di aggiornare a Java 17 o superiore per utilizzare SkinsRestorer senza problemi. Le versioni più recenti di Java possono anche eseguire server più vecchi, quindi un server Minecraft 1.8 può essere eseguito su Java 17. Leggi le informazioni sulla console per maggiori dettagli.", "skinsrestorer.permission_player_wildcard": "Autorizzazione per i giocatori", "skinsrestorer.permission_command": "Consente l'accesso ai comandi \"/skin\" principali.", "skinsrestorer.permission_command_set": "Consente di cambiare la tua skin.", "skinsrestorer.permission_command_set_url": "Consente di modificare la tua skin da una URL.", "skinsrestorer.permission_command_clear": "Consente di eliminare la tua skin.", "skinsrestorer.permission_command_random": "Permette di impostare una skin casuale.", "skinsrestorer.permission_command_update": "Consente di aggiornare la tua skin.", "skinsrestorer.permission_command_undo": "Consente di ripristinare la tua skin a quella precedente.", "skinsrestorer.permission_command_favourite": "Consente di impostare una skin come preferita.", "skinsrestorer.permission_command_search": "Consente di cercare la tua skin.", "skinsrestorer.permission_command_gui": "Consente di aprire l'interfaccia grafica delle skin.", "skinsrestorer.permission_admin_wildcard": "Autorizzazioni per amministratori", "skinsrestorer.permission_admincommand": "Consente l'accesso ai comandi \"/sr\" principali.", "skinsrestorer.permission_command_set_other": "Consente di impostare la skin di un altro giocatore.", "skinsrestorer.permission_command_clear_other": "Consente di eliminare la skin di un altro giocatore.", "skinsrestorer.permission_command_random_other": "Permette di impostare una skin casuale ad un altro giocatore.", "skinsrestorer.permission_command_update_other": "Consente di aggiornare la skin di un altro giocatore.", "skinsrestorer.permission_command_favourite_other": "Permette di impostare una skin come preferita per un altro giocatore.", "skinsrestorer.permission_command_undo_other": "Consente ad un altro giocatore di ripristinare la propria skin a quella precedente.", "skinsrestorer.permission_admincommand_reload": "Consente l'accesso a \"/sr reload\".", "skinsrestorer.permission_admincommand_status": "Consente l'accesso a \"/sr status\".", "skinsrestorer.permission_admincommand_drop": "Consente di rimuovere un file .SKIN.", "skinsrestorer.permission_admincommand_info": "Consente di ottenere le informazioni sulla skin di un giocatore o di una skin.", "skinsrestorer.permission_admincommand_applyskin": "Consente di ri-applicare la skin ad un altro giocatore.", "skinsrestorer.permission_admincommand_createcustom": "Consente di creare una skin globale personalizzata tramite URL.", "skinsrestorer.permission_admincommand_purgeolddata": "Consente di eliminare i vecchi dati delle skin.", "skinsrestorer.permission_admincommand_dump": "Consente di caricare le informazioni del server tramite \"/sr dump\".", "skinsrestorer.permission_bypasscooldown": "Ignora qualsiasi tempo di attesa dei comandi impostato nella configurazione.", "skinsrestorer.permission_bypassdisabled": "Ignora qualsiasi disabilitazione di skin impostata nella configurazione.", "skinsrestorer.permission_ownskin": "Consente l'accesso per impostare la tua skin.", "skinsrestorer.duration_day": " <PERSON>ior<PERSON>", "skinsrestorer.duration_days": " <PERSON>ior<PERSON>", "skinsrestorer.duration_hour": " ora", "skinsrestorer.duration_hours": " ore", "skinsrestorer.duration_minute": " minuto", "skinsrestorer.duration_minutes": " minuti", "skinsrestorer.duration_second": " secondo", "skinsrestorer.duration_seconds": " secondi"}