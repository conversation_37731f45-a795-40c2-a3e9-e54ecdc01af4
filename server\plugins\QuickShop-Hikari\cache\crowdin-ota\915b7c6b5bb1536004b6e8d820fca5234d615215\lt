break-shop-use-supertool: <yellow>Galite sulaužyti parduotuvę naudodami super įrankį.
fee-charged-for-price-change: <green>You paid <red>{0}<green> to change the price.
not-allowed-to-create: <red>Tu negali čia sukurti parduotuvės.
disabled-in-this-world: <red>Parduotuvės yra išjungtos šiame pasaulyje
how-much-to-trade-for: <green>Enter in chat, how much you wish to trade <yellow>{1}x {0}<green> for.
client-language-changed: <green>QuickShop detected that your client language has been changed, we now use the {0} locale for you.
shops-backingup: Su<PERSON><PERSON><PERSON> parduotuvių atkurimo failas iš duomenų bazės...
_comment: Hi translator! If you're editing this on GitHub or through source code should you go to https://crowdin.com/project/quickshop-hikari instead.
unlimited-shop-owner-changed: <yellow>Parduotuvės savininkas buvo pakeistas į {0}.
bad-command-usage-detailed: '<red>Bad command arguments! Accepts the following parameters: <gray>{0}'
thats-not-a-number: <red>Neg<PERSON><PERSON> skai<PERSON>
shop-name-disallowed: <red>The shop name <yellow>{0}</yellow> is disallowed. Pick another one!
console-only-danger: <red>This is a dangerous command, so that only the Console can execute it.
not-a-number: <red>Galite įvesti tik skaičių, jūsų įvestis buvo {0}.
not-looking-at-valid-shop-block: <red>Could not create a shop. Please look a supported block-type and try again.
shop-removed-cause-ongoing-fee: <red> Jūsų parduotuvė esanti {0} buvo ištrinta nes jūs neturite pakankamai pinigų!
tabcomplete:
  amount: '[Kiekis]'
  item: 'Daiktas'
  price: '[Kaina]'
  name: '[Pavadinimas]'
  range: '[atstumas]'
  currency: '[Valiutos pavadinimas]'
  percentage: '[Procentai%]'
taxaccount-unset: <green>This shop's tax account now following server global setting.
blacklisted-item: <red>You cannot sell this item because it is on the blacklist
command-type-mismatch: <red>This command can only be executed by <aqua>{0}.
server-crash-warning: 'If you want custom the language file, use language override system'
you-cant-afford-to-change-price: <red>Tai kainuoja {0} kad pakeisti kaina jūsų parduotuvėje.
safe-mode: <red>QuickShop now in safe-mode, you cannot open this shop container, please contact with server administrator to fix the errors.
forbidden-vanilla-behavior: <red>This operation is forbidden due to it not being consistent with vanilla behaviour
shop-out-of-space-name: <dark_purple>Jūsų parduotuvė{0} yra pilna!
paste-disabled: |-
  <red>Paste function has been disabled! You cannot request technical support.
  Reason: {0}
quick-fill:
  entry-own: ' <yellow><red>[Greitas užpildymas]<aqua>{0}<light_purple>{1}'
  hover:
    - '<yellow>Vardas:<aqua>{0}'
    - '<yellow>Savininkas:<aqua>{0}'
    - '<yellow>Tipas:<aqua>{0}'
    - '<yellow>Kaina:<aqua>{0}'
    - '<yellow>Daiktas:<aqua>{0}'
    - '<yellow>Vieta:<aqua>{0}'
  hover-arg-filled:
    - '<yellow>Vardas:<aqua>{name}'
    - '<yellow>Savininkas:<aqua>{owner}'
    - '<yellow>Tipas:<aqua>{type}'
    - '<yellow>Kaina:<aqua>{price}'
    - '<yellow>Daiktas:<aqua>{item}'
    - '<yellow>Vieta:<aqua>{location}'
  header: '<yellow>You have multiple shops with name "<green>{0}</green>", choose one to continue:'
  entry-normal: ' <yellow><aqua>{0}<light_purple>{1}'
  entry-cooperation: ' <yellow><red>[co]<aqua>{0}<light_purple>{1}'
3rd-plugin-build-check-failed-admin: '<gray>(AdminOnly) <light_purple>{0}<dark_gray> denied the permission checks. If this not expected, try adding <light_purple>{1} <gray>to the listener blacklist. Configuration Guide:https://ghost-chu.github.io/QuickShop-Hikari-Documents/docs/modules/shops/protection-checker'
average-price-nearby: '<green>Netoli esančios vidutinės kainos:<yellow>{0}'
inventory-check-global-alert: "<red>[Inventory Check] <gray>Warning! Found a QuickShop display item <gold>{2}</gold> in inventory at <aqua>{0}</aqua> <dark_gray>[{1}]</dark_gray>, which it shouldn't happened, This usually means that someone is maliciously exploiting the exploit to duplicating the display item."
digits-reach-the-limit: <red>Jūs pasiekėte limita rašant skaičius po kablelio.
currency-unset: <green>Parduotuvės valiuta buvo pašalinta sėkmingai. Nuo šiol naudojami numatyti/standartiniai nustatymai.
you-cant-create-shop-in-there: <red>You don't have permissions to create a shop at this location.
no-pending-action: <red>Jūs neturite jokių eilėje esančių veiksmų
refill-success: <green>Papildymas sėkmingai baigtas
failed-to-paste: <red>Failed to upload the data to pastebin. Check your internet connection and try again. (See console for details)
shop-out-of-stock-name: <dark_purple>Jūsų parduotuvė{0} ištuštinta ir joje trūksta{1}!
shop-name-invalid: <red>The shop name <yellow>{0} <red>is invalid. Pick another one!
how-many-buy-stack: <yellow>Enter in chat, how many bulks you wish to <aqua>BUY<green>. There are <yellow>{0}<green> items in each bulk and you can buy <yellow>{1}<green> bulks. Enter <aqua>{2}<green> in chat to buy all.
exceeded-maximum: <red>Vertė pasiekė didžiausia galima verte Java programoje.
unlimited-shop-owner-keeped: '<yellow>Attention: The Shop Owner is still an unlimited Shop Owner, and you have to re-set the new Shop Owner by yourself.'
no-enough-money-to-keep-shops: <red>You didn't had enough money to keep your shops! All your shops have been removed...
3rd-plugin-build-check-failed: <red>3rd-party plugin <bold>{0}<reset><red> denied the permission checks, did you have permission set up in there?
not-a-integer: <red>Jūs galite įvesti tik sveikus skaičius, jūsų skaičius buvo{0}.
translation-country: 'Kalbų zona: Anglų (en_US)'
buying-more-than-selling: '<red>ĮSPĖJIMAS: Jūs perkate daiktus kurių vertė didesne, nei jūs pardavinėjate!'
purchase-failed: '<red>Purchase failed: Internal Error. Please contact the Server Administrator.'
denied-put-in-item: <red>Jūs negalite įdėti šios prekės į savo parduotuvę!
shop-has-changed: <red>Parduotuvė, kuria bandėte pasinaudoti, pasikeitė nuo paskutinio karto!
flush-finished: <green>Sėkmingai išvalėte naujausias žinutes.
no-price-given: <red>Prašome nurodyti leistiną/galiojančia pardavimo kainą.
shop-already-owned: <red>Ši parduotuvė jau yra užpatentuota/yra naudojama.
backup-success: <green>Backup successfull.
not-looking-at-shop: <red>Couldn't find a QuickShop. Make sure you look at one.
you-cant-afford-a-new-shop: <red>It costs {0} to create a new shop.
success-created-shop: <green>Shop created.
shop-creation-cancelled: <red>Shop creation cancelled.
shop-owner-self-trade: <yellow>You trade with your own shop. You may not gain any money.
purchase-out-of-space: <red>This shop has run out of space. Contact the shop owner or staff to get it emptied.
reloading-status:
  success: <green>Reload completed without any errors.
  scheduled: <green>Reload complete. <gray>(Some changes take a while to become active)
  require-restart: <green>Reload complete. <yellow>(Some changes require a server restart to take effect)
  failed: <red>Reload failed, check the server console
player-bought-from-your-store-tax: <green>{0} Kažkas nusipirko{1}{2} iš jūsų parduotuvės ir jūs gavote{3}({4} atskaičius mokesčius).
not-enough-space: <red>You only have room left for {0} more!
shop-name-success: <green>Successfully set the shop name to <yellow>{0}<green>.
shop-staff-added: <green>Successfully added {0} as a staff member to your shop.
shop-staff-empty: <yellow>This shop have no staff members.
shops-recovering: Atkuriama parduotuvė iš atsarginių failų...
virtual-player-component-hover: "<gray>This is a virtual player.\n<gray>Refers to a system account of this name with the same name.</gray>\n<green>UUID: <yellow>{0}</yellow></green>\n<green>Username: <yellow>{1}</yellow></green>\n<green>Display as: <yellow>{2}</yellow></green>"
real-player-component-hover: "<gray>This is a real exists player.\n<green>UUID: <yellow>{0}</yellow></green>\n<green>Username: <yellow>{1}</yellow></green>\n<green>Display as: <yellow>{2}</yellow></green>\n<gray>If you wish to use a virtual system account with the same name, add <dark_gray>\"[]\"</dark_gray> to the both side of the username: <dark_gray>[{1}]</dark_gray>."
menu:
  sell-tax: <green>You paid <yellow>{0} <green>in taxes.
  owner: '<green>Savininkas:{0}'
  preview: <aqua>[Item Preview]
  enchants: <dark_purple>Enchantai
  sell-tax-self: <green>You didn't had to pay taxes because you own this shop.
  shop-information: '<green>Parduotuvės informacija:'
  item: '<green>Daiktas:<yellow>{0}'
  price-per: <green>Kaina už <yellow>{0}<green><yellow>{1}
  item-name-and-price: <yellow>{0} {1} <green>for <yellow>{2}
  item-name-and-price-tax: <yellow>{0} {1} <green>for</green> {2} <gray>(<green>{3}</green> in taxes)
  successful-purchase: '<green>Sėkmingai nusipirkote:'
  price-per-stack: <green>kaina už <yellow>{2}x{0}-{1}
  stored-enchants: <dark_purple>Saugomi enchantai
  item-holochat-error: <red>[Error]
  this-shop-is-selling: <green>This shop is <aqua>SELLING<green> items.
  shop-stack: '<green>Kaina už stacka:<yellow>{0}'
  space: '<green>Vieta:<yellow>{0}'
  effects: <green>efektai
  damage-percent-remaining: <yellow>{0}% <green>Liko.
  item-holochat-data-too-large: <red>[Error] Daiktų kiekis per didelis, kad būtų atvaizduotas
  stock: '<green>Atsargos: <yellow>{0}'
  this-shop-is-buying: <green>Ši parduotuvė <light_purple>SUPERKA<green> daiktus.
  successfully-sold: '<green>Sėkmingai parduota:'
  total-value-of-chest: '<green>Galutinė chesto vertė:<yellow>{0}'
currency-not-exists: <red>Nepavyksta rasti valiutos, kurią norite nustatyti. Galbūt rašyba neteisinga arba šios valiutos nėra.
no-nearby-shop: <red>Nėra netoliese esančių parduotuvių {0}.
translation-author: 'Ghost_chu, Andre_601'
integrations-check-failed-trade: <red>Integracija {0} paneigė Parduotuvės siūloma trade
shop-transaction-failed: <red>Sorry, but an internal error occurred while processing your purchase. The purchase has been cancelled and any operation has been rollback. Please contact the server administrators if it keeping occurred.
success-change-owner-to-server: <green> Sėkmingai nustatėte parduotuvės savininką.
shop-name-not-found: <red>The shop named <yellow>{0}</yellow> not exists.
shop-name-too-long: <red>Parduotuvės pavadinimas per ilgas (Maksimalus ilgis {0}), prašome susikurti kitoki!
metric:
  header-player: '<yellow>{0} eurų. {1} {2} Pervedimo operacijos:'
  action-hover: <yellow>{0}
  price-hover: <yellow>Išviso{0}, įskaitant{1} atskaičius mokesčius.
  unknown: <gray>(nežinomas)
  undefined: <gray>(noname)
  no-results: <red>Jokių mokesčių nėra.
  action-description:
    DELETE: <yellow>Player deleted a shop. And refunded the shop creation fee to owner if possible.
    ONGOING_FEE: <yellow>Player paid the ongoing fee because the payment period.
    PURCHASE_BUYING_SHOP: <yellow>Žaidėjas nusipirko daiktų iš supirkimo parduotuvės.
    CREATE: <yellow>Žaidėjas sukurė parduotuvę.
    PURCHASE_SELLING_SHOP: <yellow>Žaidėjas nusipirko daiktų iš parduodamos parduotuvės
    PURCHASE: <yellow>Įsigyta prekė parduotuvėje
  query-argument: 'Užklausos argumentas: {0}'
  amount-hover: <yellow>{0}x
  header-shop: '<yellow>Parduotuvės {0} eurų. {1} {2} Pervedimo operacijos:'
  player-hover: |-
    <yellow>{0}
    <gold>UUID: <gray>{1}
  looking-up: <yellow>Atliekama metrikos paiešką, palaukite...
  tax-hover: <yellow>{0} Mokesčiai
  header-global: '<yellow>Serverio {0} {1} Pervedimo operacijos:'
  na: <gray>N/A
  transaction-count: <yellow>{0} Išviso
  shop-hover: |-
    <yellow>{0}
    <gold>Pos: <gray>{1} {2} {3}, World: {4}
    <gold>Owner: <gray>{5}
    <gold>Shop Type: <gray>{6}
    <gold>Item: <gray>{7}
    <gold>Price: <gray>{8}
  time-hover: '<yellow>Laikas: {0}'
  amount-stack-hover: <yellow>{0}x Stackas
permission-denied-3rd-party: <red>Leidimas neleidžiamas naudojant 3rd-party pluginą {0}.
you-dont-have-that-many-items: <red>Jūs turite tik {0} {1}.
complete: <green>Užbaigta!
translate-not-completed-yet-url: 'Kalbos vertimas {0} dar pilnai nebaigtas {1}. Ar norite padėti mums tobulinti vertimą? Nuoroda: {2}'
success-removed-shop: <green>Parduotuvė pašalinta.
currency-set: <green>Parduotuvės valiuta sėkmingai nustatyta į {0}.
shop-purged-start: <green>Prasidėjo parduotuvės valymas, patikrinkite konsolę, kad gautumėte išsamesne informacija.
economy-transaction-failed: <red>Sorry, but an internal error occurred while processing your transaction. The transaction has been cancelled and any economy operation has been rollback. Please contact the server administrators if it keeping occurred.
nothing-to-flush: <green>Jūs neturite naujų pranešimų iš parduotuvės.
no-price-change: <red>Tai nekeičia kainos!
edition-confilct: QuicShop-Hikari with QuickShop-Reremake installed may conflict each other, uninstall one of them.
inventory-unavailable: |-
  <red>Non-existant or invalid InventoryWrapper. Do you use an Addon to re-bind the Shop Inventory?
  Information: InventoryWrapper={0}, WrapperProvider={1}, SymbolLink={2}. Please contact the Server administrators.
file-test: This is a test textfile. We use it to test if the messages.json file is broken. You can fill it with any easter eggs you like :)
unknown-player: <red>Parašyto žaidėjo šiuo momentu nėra. Patikrinkite įvestą nickname.
player-offline: <red>The target player currently is offline.
player-profile-format: <aqua>{0}</aqua><gold>(<yellow>{1}</yellow>)
shop-type:
  selling: PARDUODAMA
  buying: SUPERKAMA
language:
  qa-issues: '<yellow>Kokybės užtikrinimo problemos: <aqua>{0}%'
  code: '<yellow>Kodas: <gold>{0}'
  approval-progress: '<yellow>Patvirtintas progresas: <aqua>{0}%'
  translate-progress: '<yellow>Vertimo užkrovimo progresas: <aqua>{0}%'
  name: '<yellow>Pavadinimas:<gold>{0}'
  help-us: <green>[Padėkite mums pagerinti vertimo kokybę]
warn-to-paste: |-
  <yellow>Collecting data and uploading to Pastebin. This may take a while...
  <red><bold>Warning:</bold> The data is kept public for one week! It may leak your server configuration and other sensitive information. Make sure you only send it to <bold>trusted staff/developers.
how-many-sell-stack: <green>Enter in chat, how many bulk you wish to <light_purple>SELL</light_purple>. There are <yellow>{0}<green> items per bulk and you can sell <yellow>{1}</yellow> bulks. Enter <aqua>{2}</aqua> in chat to sell all.
updatenotify:
  buttontitle: 'Atnaujinti dabar'
  onekeybuttontitle: '[OneKey atnaujinimas]'
  label:
    github: '[GitHub]'
    ore: '[Ore]'
    lts: '[LTS]'
    nbtapi: '[+NBTAPI]'
    qualityverifyed: '[Quality]'
    master: '[Master]'
    unstable: '[Unstable]'
    paper: '[+Paper Optimized]'
    stable: '[Stable]'
    spigotmc: '[SpigotMC]'
    papermc: '[PaperMC]'
    bukkitdev: '[BukkitDev]'
    polymart: '[Polymart]'
    modrinth: '[Modrinth]'
    basic: '[Basic]'
  list:
    - '{0} Buvo išleistas. Jūs vis dar naudojate {1}!'
    - Boom! Naujas atnaujinimas {0} atkeliauja. Atnaujinti!
    - Siurprizas! {0} išėjo. Šiuo momentu naudojate {1}
    - Panašu, kad jums reikia atsinaujinti. {0} buvo išleistas!
    - Ooops! {0} dabar buvo išleistas. Jūs dabar naudojate {1}!
    - QS buvo atnaujintas į {0}. Kodėl dar neatsinaujinai?
    - Sutvarkymas ir... Atsiprašau, bet {0} buvo išleistas!
    - Klaidos! Ne. Tai nėra klaida. {0} buvo išleistas!
    - OMG! {0} išėjo! Kodėl vis dar naudojate {1}?
    - 'Šiandienos naujienos: "Parduotuvių pluginas" atnaujintas į {0}!'
    - Pluginas k.i.a. Turėtumėte atnaujinti į {0}!
    - Atnaujinimas {0} isižiebė/atsirado. Išsaugoti atnaujinimą!
    - Naujas atnaujinimas jau yra Komendante. {0} ką tik buvo išleistas!
    - Pažiūrėk į mano stilių---{0} Atnaujinta. Jūs vis dar naudojate {1}
    - Ahhhhhhh! Naujas atnaujinimas {0}! Atnaujinti!
    - Ką manai? {0} buvo išleistas! Atnaujinti!
    - Daktare, "Parduotuvių pluginas" turi naują atnaujinimą {0}! Turėtumėte atnaujinti ~
    - Ko~ko~da~yo~ "Parduotuvių pluginas" turi naują atnaujinimą {0} ~
    - Paimon nori pasakyti, kad "Parduotuvių pluginas" turi naują atnaujinimą {0}!
  remote-disable-warning: '<red>This version of QuickShop is marked as disabled by the remote server, which means this version may have serious problem, get details from our SpigotMC page: {0}. This warning will continue to appear until you switch to a stable version, but it will not affect your server''s performance.'
purchase-out-of-stock: <red>Šioje parduotuvėje baigėsi atsargos. Susisiekite su parduotuvės savininku arba darbuotojais, kad atsargos būtų papildytos.
nearby-shop-entry: '<green>- Info: {0} Price: <aqua>{1} <green>X: <aqua>{2} <green>Y: <aqua>{3} <green>Z: <aqua>{4} <green>Distance: <aqua>{5} <green>block(s)'
chest-title: '"Parduotuvės plugino" parduotuvė'
console-only: <red>This command can only be executed by Console.
failed-to-put-sign: <red>Nepakanka vietos aplink, kad būtų galima pastatyti informacine lentele.
shop-name-unset: <red>This shop's name now removed
shop-nolonger-freezed: <green>Jūs atidarėte parduotuve vėl. Dabar ji veikia standartiškai!
no-permission-build: <red>Jūs čia negalite statyti parduotuvės.
tableformat:
  left_half_line: <dark_purple>+--------------------
  right_half_line: <dark_purple>--------------------+
  full_line: <dark_purple>+---------------------------------------------------+
  left_begin: '<dark_purple>| '
quickshop-gui-preview: '"Parduotuvės plugino" GUI elementų apžiūra'
translate-not-completed-yet-click: Kalbos vertimas iš {0} dar nebaigtas {1}. Ar norite padėti mums tobulinti vertimą? Spauskite čia!
taxaccount-invalid: <red>Target account not invalid, please enter a valid player name or uuid(with dashes).
player-bought-from-your-store: <green>{0} Kažkas nusipirko{1}{2} iš jūsų parduotuvės ir jūs gavote{3}.
reached-maximum-can-create: <red>Jūs jau sukūrėte maksimumalų kiekį {0}/{1} parduotuvių!
reached-maximum-create-limit: <red>Pasiekėte parduotuvių sukūrimo limitą
translation-version: 'Palaikymo versija: Hikari'
no-double-chests: <red>Jūs negalite sukurti double chest parduotuvės.
price-too-cheap: <red>Kaina turi būti didesnė nei <yellow>${0}
shop-not-exist: <red>Čia parduotuvės nėra.
bad-command-usage: <red>Blogi komandų/os argumentai!
cleanghost-warning: <yellow>This command will purge <red>all</red> shops if the shop is corrupted, was created in not allowed worlds, is selling/buying not allowed items or <bold><red>EXISTS IN A UNLOADED WORLD</red></bold>. Make sure to create a full backup of your shop data first and use <aqua>/quickshop cleanghost confirm</aqua> to continue.
cleanghost-starting: <green>Pradedama tikrinti, ar nėra vaiduoklių parduotuvių. Visos neegzistuojančios parduotuvės bus pašalintos...
cleanghost-deleting: <yellow>Radome sugadintą parduotuvę <aqua>{0}</aqua> nes {1}, pažymėkite jį, kad ištrintumėte...
cleanghost-deleted: <green>Bendrai <yellow>{0}</yellow> parduotuvės buvo ištrintos.
shop-purchase-cancelled: <red>Pirkimas buvo atšauktas.
bypassing-lock: <red>Apeinama "Parduotuvės plugino" užraktą!
bungee-cross-server-msg: '<yellow>"Parduotuvės plugino" CSM: <green>{0}'
saved-to-path: Atsarginė kopija buvo išsaugota {0}.
shop-now-freezed: <green>Jūs uždarėte laikinai parduotuvę. Niekas negali prekiauti su šia parduotuve dabar!
price-is-now: <green>Nauja parduotuvės kaina – <yellow>{0}
shops-arent-locked: <red>Atminkite, kad parduotuvės NĖRA apsaugotos nuo vagysčių! Jei norite sustabdyti vagis, užrakinkite jį naudojant LWC, Lockette ar panašiai!
that-is-locked: <red>Ši parduotuvė užrakinta.
shop-has-no-space: <red>Parduotuvėje turi vietos tik {0} Daugiau {1}.
safe-mode-admin: <red><bold>WARNING:</bold> The QuickShop version on this server is currently running in safe mode. Features won't work. Please use the <yellow>/quickshop</yellow> command to check for any errors.
shop-stock-too-low: <red>Parduotuvėje turi tik {0} {1} likutį!
world-not-exists: <red>The world <yellow>{0}<red> doesn't exist
how-many-sell: <green>Enter in chat, how much you wish to <light_purple>SELL<green>. You can sell <yellow>{0}<green>. Enter <aqua>{1}<green> in chat, to sell all.
shop-freezed-at-location: <yellow>Jūsų parduotuvė {0} prie {1} buvo uždaryta!
translation-contributors: 'Autoriai: Timtower, Netherfoam, KaiNoMood, Mgazul, JackTheChicken ir Andre_601'
empty-success: <green>Emptying shop successful
taxaccount-set: <green>Šios parduotuvės mokesčių sąskaita nustatyta į <yellow>{0}
support-disable-reason:
  hot-reload: <yellow>Unsupported hot reloaded, restart the server and check again.
  outdated: <yellow>This version of QuickShop is already outdated. Update before requesting support!
  bad-hosts: |-
    <yellow>The server's HOSTS have been modified and certain QuickShop-functions require a connection to the Mojang API to work. Please fix the HOSTS settings before asking for support.
    Windows: C:\\windows\\system32\\drivers\\etc\\hosts
    Linux: /etc/hosts
  privacy: <yellow>This server is running in Cracked (Offline) mode. If you're running the server under a proxy and online-mode is set to true in the proxy, configure the proxy-related settings correctly.
  modified: <yellow>File integrity check failed, this QuickShop build has been modified by others.
  consolespamfix-installed: <yellow>ConsoleSpamFix installed, it will hidden exception details, temporarily disable it during asking support.
  authlib-injector-detected: <yellow>This server running under 3rd-party authlib provider like authlib-injector.
  unsupported-server-software: <yellow>Unsupported server software, Any modded hybrid server software is unsupported including MCPC, Cauldron, CatServer, Mohist, Magma, Fukkit, Cardboard etc.
supertool-is-disabled: <red>Supertool is disabled. Cannot break any shops.
unknown-owner: Nežinomas/a
restricted-prices: '<red>Ribota kaina nuo {0}: Min {1}. max {2}'
nearby-shop-this-way: <green>Parduotuvė yra {0} blokais toliau nuo jūsų.
owner-bypass-check: <yellow>Praėjote visus patikrtinimus. Mainai sėkmingai pavyko! (Nuo šiol jūs esate šios parduotuvės savininkas)
april-rick-and-roll-easter-egg: "<hover:show_text:'Click to open in browser for time limited rewards!'><click:open_url:'https://www.youtube.com/watch?v=dQw4w9WgXcQ'><green>Click here to acquire your time limited rewards that provided by QuickShop-Hikari developer!</green></click></hover>"
signs:
  item-right: ''
  out-of-space: Nepakanka vietos
  unlimited: Nesibaigiantis
  stack-selling: Parduodama {0}
  stack-price: '{0} už {1}x {2}'
  status-unavailable: <red>
  out-of-stock: Išparduota
  stack-buying: Superkama {0}
  freeze: Trading disabled
  price: '{0} vienas'
  buying: Pirkti{0}
  header: '{1}{0}'
  header-available: <green>{0}
  header-unavailable: <red>{0}
  selling: Parduodama{0}
  status-available: <green>
  item-left: ''
negative-amount: <red>Negalimi pardavinėti neigiamomis sumomis
display-turn-on: <green>Successfully turn on the shop display.
shop-staff-deleted: <green>Sėkmingai pašalintas {0} kaip prižiūrėtojas iš jūsų parduotuvės.
nearby-shop-header: '<green>Nearby Shops matching <aqua>{0}<green>:'
backup-failed: Nepavyksta sukurti atsarginės duomenų bazės kopijos. Išsamesnės informacijos ieškokite konsolėje.
shop-staff-cleared: <green>Sėkmingai pašalinote visus prižiūrėtojus iš jūsų parduotuvės.
price-too-high: <red>Parduotuvės kaina yra per didelė! Negalite sukurti parduotuvės, kurios kaina yra didesnė nei {0}.
plugin-cancelled: '<red>Operation cancelled, Reason: {0}'
player-sold-to-your-store: <green>{0} Gauta {1} {2} į jūsų parduotuvę.
shop-out-of-stock: <dark_purple>Jūsų parduotuvė {0}, {1}, {2} ištuštinta ir joje trūksta {3}!
how-many-buy: <green>Enter in chat, how many you wish to <aqua>BUY<green>. You can buy <yellow>{0}</yellow>. Enter <aqua>{1}</aqua> to buy them all.
language-info-panel:
  help: 'Padėkite mums: '
  code: 'Kodas: '
  name: 'Kalba: '
  progress: 'Progresas: '
  translate-on-crowdin: '[Verstimas "Crowdin" platformoje]'
item-not-exist: <red>The item <yellow>{0} <red>does not exist, please check your spelling.
shop-creation-failed: <red>Shop creation failed, please contact with server administrator.
inventory-space-full: <red>Your remaining inventory space can only have <green>{1}x</green> more items added to it. Try emptying your inventory!
no-creative-break: <red>You cannot break the shops of other players while in creative mode. Switch to survival mode or try to use the supertool {0} instead.
booleanformat:
  success: <green>✔
  failed: <red>✘
controlpanel:
  stack: '<green>Per bulk amount: <aqua>{0} <yellow>[<bold><light_purple>Change</light_purple></bold>]'
  price-hover: <yellow>Spustelėkite, kad nustatytumėte naują prekės kainą.
  remove: <bold><red>[Remove Shop]
  mode-buying-hover: <yellow>Spustelėkite, kad pakeistumėte parduotuvės nuostatas į pardavimo režimą.
  empty: '<green>Empty: Remove all items <yellow>[<bold><light_purple>OK</light_purple></bold>]'
  stack-hover: <yellow>Click to set the amount of item per bulk. Set to 1 for normal behaviour.
  alwayscounting-hover: <yellow>Paspausk, jei norite, kad parduotuvė skaičiuotu prekių kieki esanti jūsų parduotuvėje.
  alwayscounting: '<green>Always counting: {0} <yellow>[<bold><light_purple>Change</light_purple></bold>]'
  setowner: '<green>Owner: <aqua>{0} <yellow>[<bold><light_purple>Change</light_purple></bold>]'
  freeze: '<yellow>Freeze mode: <aqua>{0} <yellow>[<bold><light_purple>Toggle</light_purple></bold>]'
  price: '<green>Price: <aqua>{0} <yellow>[<bold><light_purple>Change</light_purple></bold>]'
  currency-hover: <yellow>Spustelėkite, kad nustatytumėte arba pašalintumėte šios parduotuvės naudojamą valiutą
  lock: '<yellow>Shop lock: <aqua>{0} <yellow>[<bold><light_purple>Toggle</light_purple></bold>]'
  mode-selling: '<green>Shop mode: <aqua>Selling <yellow>[<bold><light_purple>Change</light_purple></bold>]'
  currency: '<green>Currency: <aqua>{0} <yellow>[<bold><light_purple>Set</light_purple></bold>]'
  setowner-hover: <yellow>Spustelėkite, kad pakeistumėte savininką.
  mode-buying: '<green>Shop mode: <aqua>Buying <yellow>[<bold><light_purple>Change</light_purple></bold>]'
  item: '<green>Shop Item: {0} <yellow>[<bold><light_purple>Change</light_purple></bold>]'
  unlimited: '<green>Unlimited: {0} <yellow>[<bold><light_purple>Change</light_purple></bold>]'
  unlimited-hover: <yellow>Spustelėkite, jei jūsų parduotuvė neturi jokių limitų.
  refill-hover: <yellow>Spustelėkite norėdami papildyti parduotuvę.
  remove-hover: <yellow>Spustelėkite, kad pašalintumėte šią parduotuvę.
  toggledisplay-hover: <yellow>Toggle the shop's displayitem status
  refill: '<green>Refill: Refill the items <yellow>[<bold><light_purple>OK</light_purple></bold>]'
  freeze-hover: <yellow>Perjunkti parduotuve į "laikinai uždarytą" statusą.
  lock-hover: <yellow>Įjunkite / išjunkite parduotuvės užraktą.
  item-hover: <yellow>Click to change shop Item
  infomation: '<green> Pardotuvės kontrolės lentelė:'
  mode-selling-hover: <yellow>Spustelėkite, kad pakeistumėte parduotuvės nuostatas į supirkimo režimą.
  empty-hover: <yellow>Spustelėkite, kad išvalytumėte parduotuvėje esančius daiktus.
  toggledisplay: '<green>Display-Item: <aqua>{0} <yellow>[<bold><light_purple>Toggle</light_purple></bold>]'
  history: '<green>History: <yellow>[<bold><light_purple>View</light_purple></bold>]</yellow>'
  history-hover: <yellow>Click to view shop history logs
timeunit:
  behind: už
  week: "{0} week"
  weeks: "{0} weeks"
  year: "{0} year"
  before: prieš
  scheduled: suplanuota
  years: "{0} years"
  scheduled-in: suplanuota po {0}
  second: "{0} second"
  std-past-format: '{5}{4}{3}{2}{1}{0} prieš'
  std-time-format: Valandos:Minutes:Sekundes
  seconds: "{0} seconds"
  hour: "{0} hour"
  scheduled-at: numatyta {0}
  after: po
  day: "{0} day"
  recent: neseniai
  between: tarp
  hours: "{0} hours"
  months: "{0} months"
  longtimeago: prieš daug laiko
  between-format: tarp {0} ir {1}
  minutes: "{0} minutes"
  justnow: tikką
  minute: "{0} minute"
  std-format: Mėnesiai/dienos/metai Valandos:minutės:sekundės
  future-plain-text: ateitis
  month: "{0} month"
  future: po {0}
  days: "{0} days"
command:
  reloading: '<green>Configuration reloaded. <yellow>Some changes may require reboot to affect. <newline><gray>(Notice: Reloading behavior has been changed after ********, we now only reload configuration but not whole plugin to ensure the server won''t crashed.)'
  description:
    buy: <yellow>Changes a shop to <light_purple>BUY<yellow> mode
    about: <yellow>Rodyti "Parduotuvės Pluginas" informacija
    language: <yellow>Pakeisti šiuo metu naudojama kalbą
    purge: <yellow>Start the shop purge task in background
    paste: <yellow>Įkelia serverio duomenis į Pastebin
    title: <green>"Pplugino pagalba
    remove: <yellow>Pašalinti parduotuvę, į kurią žiūrite
    ban: <yellow>Uždrauskite žaidėjui pirkti iš šios parduotuvės
    empty: <yellow>Pašalinti visas prekes iš parduotuvės
    alwayscounting: <yellow>Set if shop always counting container even is unlimited
    setowner: <yellow>Pakeisti parduotuvės savininką.
    reload: <yellow>Iš naujo įkeliama "Parduotuvės plugino" config.yml
    freeze: <yellow>Išjungti arba Įjungti parduotuve
    price: <yellow>Keičia parduotuvės pirkimo/pardavimo kainą
    find: <yellow>Randa artimiausią konkretaus tipo parduotuvę.
    create: <yellow>Sukuria naują parduotuvę nuo nutaikytos parduotuvės
    lock: <yellow>Parduotuvės užrakto statuso pakeitimas
    currency: <yellow>Parduotuvės valiutos nustatymas arba pašalinimas
    removeworld: <yellow>Pašalinkite VISAS parduotuves nurodytame pasaulyje
    info: <yellow>Rodyti "Parduotuvių plugino" statistiką
    owner: <yellow>Pakeisti parduotuvės savininką.
    amount: <yellow>To set item amount (Useful when having chat issues)
    item: <yellow>Pakeisti parduotuvės parduodama daiktą
    debug: <yellow>Įjungti kūrėjo režimą
    unlimited: <yellow>Suteikia parduotuvei neribotas atsargas.
    sell: <yellow>Changes a shop to <aqua>SELL<yellow> mode
    fetchmessage: <yellow>Rodyti neskaitytas su parduotuve susijusias žinutes
    staff: <yellow>Valdyti savo parduotuvės administratorius
    clean: <yellow>Pašalina visas (esamas) parduotuves kurios neturi jokių atsargų
    refill: <yellow>Prideda nurodytą daiktų skaičių parduotuvėje
    help: <yellow>Shows QuickShop help
    removeall: <yellow>Pašalinkite VISAS parduotuves nurodytame specifiniame pasaulyje
    unban: <yellow>Atbockuoti (unbaninti) žaidėją iš parduotuvės
    transfer: <yellow>Transfer someone's ALL shops to other
    transferall: <yellow>Transfer someone's ALL shops to other
    transferownership: <yellow>Transfer the shop you're looking at to someone else
    size: <yellow>Change per bulk amount of a shop
    supercreate: <yellow>Sukurkite parduotuvę apeidami visus apsaugos patikrinimus
    taxaccount: <yellow>Set the tax account that shop using
    name: <yellow>Naming a shop to specific name
    toggledisplay: <yellow>Toggle the shop display item status
    permission: <yellow>Parduotuvės leidimų valdymas
    lookup: <yellow>Peržiūrėti matomų daiktų lentele
    database: <yellow>View and maintain QuickShop database
    benefit: <yellow>Settings of divide benefits between shop owner and other players
    tag: <yellow>Add, remove or query tags of a shop
    suggestprice: <yellow>Suggest a recommend price for looking shop, based on other shops
    history: <yellow>View the history transactions of a shop
    sign: <yellow>Change the sign material of a shop
  bulk-size-not-set: '<red>Usage: /quickshop size \<amount>'
  no-type-given: '<red>Usage: /quickshop find \<item>'
  feature-not-enabled: Ši funkcija neįjungta config faile.
  now-debuging: <green>Sėkmingai įjungtas kūrėjo režimas. "ParduotuviųPluginas" įkeliamas iš naujo...
  no-amount-given: <red>No amount provided. Use <green>/quickshop refill \<amount>
  no-owner-given: <red>Savininkas nėra nustatytas
  disabled: '<red>Ši komanda yra išjungta:<yellow>{0}'
  bulk-size-now: <green>Now trading <yellow>{0}x {1}
  toggle-always-counting:
    counting: <green>Parduotuvė dabar visada skaičiuoja parduotuvės turinį, net jei parduotuvė nustatyta kaip neribota
    not-counting: <green>Shop is now respect if shop is unlimited
  cleaning: <green>Pašalinamos parduotuves neturinčios jokių atsargų...
  now-nolonger-debuging: <green>Sėkmingai išjungtas kūrėjo režimas. "ParduotuviųPluginas" įkeliamas iš naujo...
  toggle-unlimited:
    limited: <green>Parduotuvė dabar turi limitavimo režimą
    unlimited: <green>Parduotuve dabar neturi atsargų limito
  transfer-success-other: <green>Transferred <yellow>{0} {1}<green>'s shop(s) to <yellow>{2}
  no-trade-item: <green>Please hold a trade item to change in main hand
  wrong-args: <red>Invalid argument. Use <bold>/quickshop help</bold> to see a list of commands.
  some-shops-removed: <yellow>{0} <green>parduotuvė (-ės) pašalinta (-os)
  new-owner: '<green>Naujas savininkas: <yellow>{0}'
  format: <green>/{0} {1} <yellow>- {2}
  transfer-success: <green>Transferred <yellow>{0} <green>shop(s) to <yellow>{1}
  now-buying: <green>Now <light_purple>BUYING <yellow>{0}
  now-selling: <green>Now <aqua>SELLING <yellow>{0}
  cleaned: <green>Removed <yellow>{0}<green> shops.
  trade-item-now: <green>Now trading <yellow>{0}x {1}
  no-world-given: <red>Nurodykite pasaulio varda
  format-disabled: <red>/{0} {1} <gray>- {2}
  invalid-bulk-amount: <red>The given value {0} is larger than max stack size or lower than one
currency-not-support: <red>Ekonomikos pluginas nepalaiko multi-ekonomikos funkcijos.
trading-in-creative-mode-is-disabled: <red>Negalite mainytis su šia parduotuve esant kūrybiniam režimui.
the-owner-cant-afford-to-buy-from-you: <red>Šio daikto vertė yra {0}, tačiau parduotuvės savininkas turi tik {1}
you-cant-afford-shop-naming: <red>You can't afford shop naming, it costs {0} to naming.
inventory-error: |-
  <red>Failed to process the InventoryWrapper. Do you use an addon to re-bind the shop Inventory?
  Information: Exception={0}, InventoryWrapper={1}, WrapperType={2}, WrapperProvider={3}, SymbolLink={4}. Please contact the server administrators.
integrations-check-failed-create: <red>Integracija {0} atmetė parduotuvės kūrimo procesą
shop-out-of-space: <dark_purple>Jūsų parduotuvė esanti {0}, {1}, {2} yra pilna!
admin-shop: SavininkoParduotuve
no-anythings-in-your-hand: <red>Sistema jūsų rankoje nieko nerado.
no-permission: <red>Jūs neturite leidimo daryti šiuos veiksmus.
chest-was-removed: <red> Chestas buvo pašalintas.
you-cant-afford-to-buy: <red>Tai kainuoja {0}, bet jūs turite tik {1}
shops-removed-in-world: <yellow>A total of <aqua>{0}<yellow> shops have been deleted in world <aqua>{1}<yellow>.
display-turn-off: <green>Successfully turn off the shop display.
client-language-unsupported: <yellow>QuickShop doesn't support your client language, falling back to {0} locale...
language-version: '63'
not-managed-shop: <red>You are not the owner nor moderator of this Shop
shop-cannot-trade-when-freezing: <red>Jūs negalite mainytis su šia parduotuve, nes ji yra laikinai uždaryta.
invalid-container: <red>Invalid container, You can only create the shop on block who have inventory.
permission:
  header: <green>Parduotuvės suteiktų leidimų detalės
  header-player: <green>Shop Permission Details for {0}
  header-group: <green>Parduotuvės suteiktų leidimų informacija priskirtai grupei {0}
  table: <gold><bold>{0}</bold>:</gold> <gray>{1}
  item:
    purchase: <yellow>Permission to allow users who have this to purchase the shop. (including buying and selling)
    show-information: <yellow>Leidimas leisti naudotojams, kuriem duotas leidimas, matyti parduotuvės informaciją. (atidaryti parduotuvės control panel)
    preview-shop: <yellow>Leidimas žaidėjams, kurie turi šį leidimą, galės peržiūrėti parduotuvės vidų. (Leist matyti parduodamus daiktus cheste bei jų kiekį)
    search: <yellow>Permission to allow users who have this to search the target shop. (remove permission will hide shop from search result)
    delete: <yellow>Leidimas žaidėjams, kurie turi šį leidimą, galės pašalinti jūsų parduotuvę.
    receive-alert: <yellow>Permission to allow users who have this to receive alert message (e.g out-of-stock or new trade).
    access-inventory: <yellow>Leidimas žaidėjams, kurie turi šį leidimą, turės prieigą prie jūsų parduotuvės inventoriaus.
    ownership-transfer: <yellow>Leidimas žaidėjams, kurie turi šį leidimą, turės prieigą/galimybę perduoti parduotuve kitam asmeniui.
    management-permission: <yellow>Permission to allow users who have this to manage the groups permissions and edit user's group.
    toggle-display: <yellow>Leidimas žaidėjams, kurie turi šį leidimą, perjungti parduotuvės rodomą animacini daiktą.
    set-shoptype: <yellow>Permission to allow users who have this to set the shop type (switch buying or selling).
    set-price: <yellow>Leidimas žaidėjams, kurie turi šį leidimą, galės pakeisti/nustatyti parduotuvės kaina.
    set-item: <yellow>Leidimas žaidėjams, kurie turi šį leidimą, galės pakeisti/nustatyti kokį daiktą pardavinės jūsų parduotuvė.
    set-stack-amount: <yellow>Permission to allow users who have this to set the shop stack amount.
    set-currency: <yellow>Leidimas žaidėjams, kurie turi šį leidimą, galės pakeisti/nustatyti parduotuvės valiutą.
    set-name: <yellow>Leidimas žaidėjams, kurie turi šį leidimą, galės pakeisti/nustatyti parduotuvės pavadinimą.
    set-sign-type: <yellow>Change the sign material that attached on the shop.
    view-purchase-logs: <yellow>Permission to view shop purchase logs.
  group:
    everyone: <yellow>Default group for all users excepted shop owner.
    staff: <yellow>System group for shop staffs.
    administrator: <red>System group for administrators, the users in this group will have permissions almost same with shop owner.
invalid-group: <red>Neteisingas/nežinomas grupės pavadinimas.
invalid-permission: <red>Negaliojantis leidimas.
invalid-operation: <red>Negalimas veiksmas, šis veiksmas yra leidžiamas tik {0} šiems asmenims.
player-no-group: <yellow>Žaidėjas {0} nėra nei vienoje iš grupių šioje parduotuvėje.
player-in-group: <green>Player {0} is in group <aqua>{1}</aqua> in this shop.
permission-required: <red>You don't have permission {0} in this shop to do this.
no-permission-detailed: <red>You don't have permission <yellow>{0}</yellow> to do this.
paste-notice: "<yellow>Note: If you are creating a Paste for troubleshooting purposes, be sure to reproduce the error before creating the Paste as quickly as possible; we need the logs retained briefly in the buffer to troubleshoot. If you create a Paste too slowly or without first reproducing the error or restarted server, the Paste will record nothing and useless."
paste-uploading: <aqua>Please wait... Uploading the paste to pastebin......
paste-created: '<green>Paste created, click to open in browser: <yellow>{0}</yellow><br><red>Warning: <gray>Never send pastea to people you don''t trust.'
paste-created-local: |-
  <green>Paste created and saved to your local disk at {0}
  <red>Warning: <gray>Never send pastes to people you don't trust.
paste-created-local-failed: <red>Failed to save paste to your local disk, please check your disk.
paste-451: |-
  <gray><bold>TIPS:</bold> Your current country or region seems to have blocked the CloudFlare Workers service and QuickShop Paste may not be loaded properly.
  If the subsequent operation fails, try adding the --file argument to generate a local Paste: <dark_gray>/quickshop paste --file
paste-upload-failed: <red>Failed to upload paste to {0}, trying other pastebin provider...
paste-upload-failed-local: <red>Failed to upload paste, trying generate local paste...
command-incorrect: '<red>Command usage incorrect, type /quickshop help to check help. Usage: {0}.'
successfully-set-player-group: <green>Successfully set player {0} group to <aqua>{1}</aqua>.
successfully-unset-player-group: <green>Successfully remove player group in this shop.
successfully-set-player-permission: <green>Successfully set player {0} permission <aqua>{1}</aqua> in shop <aqua>{2}</aqua>.
lookup-item-created: <green>An item named <aqua>{0}</aqua> has been created in the lookup table. You can refer this item in configurations now.
lookup-item-exists: <red>An item named <yellow>{0}</yellow> already exists in the lookup table, delete it or pick another name.
lookup-item-not-found: <red>An item named <yellow>{0}</yellow> not exists.
lookup-item-name-illegal: <red>Item name illegal. Only alphanumeric characters and underscores are allowed.
lookup-item-name-regex: '<red>The name must match this regex: <aqua>{0}</aqua>.'
lookup-item-test-not-found: '<gold>Test: <yellow>The item in your hand not registered in lookup table.'
lookup-item-test-found: '<gold>Test: <green>The item in your hand was registered as name <aqua>{0}</aqua> in lookup table.'
lookup-item-removed: <green>The specified item <aqua>{0}</aqua> has been successfully removed from lookup table.
internal-error: <red>An internal error occurred, please contact the server administrator.
argument-cannot-be: <red>The argument <aqua>{0}</aqua> cannot be <yellow>{1}</yellow>.
argument-must-between: <red>The argument <aqua>{0}</aqua> value must between <yellow>{1}</yellow> and <yellow>{2}
invalid-percentage: <red>Percentage invalid, you must add '%' after the number of percentage.
display-fallback: |-
  <red>Due to an internal failure, a fallback message is being displayed.
  The value of this item may not be localized or processed correctly.
  Please contact the server administrator.
not-a-valid-time: |-
  <red>The string <yellow>{0}</yellow> not a valid timestamp, please enter a <aqua>Zulu Time (ISO 8601)</aqua> or <aqua>Unix Epoch Time in seconds</aqua>.
  <gold>Valid timestamp example: (for Sat, 17 Dec 2022 10:31:37 GMT)</gold><br><aqua>- <yellow>2022-12-17T10:31:37Z</yellow> <gray>(Zulu Time)</gray>
  - <yellow>1671273097</yellow> <gray>(Unix Epoch Time in seconds)</gray><br>
invalid-past-time: <red>You can't specify a time in the past.
debug:
  arguments-invalid: <red>Provided arguments <yellow>{0}</yellow> invalid.
  sign-located: '<green>Sign valid: <yellow>{0}</yellow>.'
  operation-missing: <red>You must specific an operation.
  operation-invalid: <red>You must specific an valid operation.
  invalid-base64-encoded-sql: <red>The SQL provided must be base64 encoded.
  warning-sql: |-
    <bold><red>Warning:</red></bold> <yellow>You're executing an SQL statement. This may corrupt your database or destroy any data in the database, even when it belongs to another plugin.
    <red>Don't confirm this if you don't trust who send you this.
  warning-sql-confirm: <yellow>To confirm this dangerous action, type <aqua>/quickshop debug database sql confirm {0}</aqua> in the next 60 seconds.
  warning-sql-confirm-hover: <yellow>Click to confirm this dangerous operation.
  sql-confirm-not-found: <yellow>Didn't found the operation that you given, it may invalid or expired.
  sql-executing: '<yellow>Executing SQL statement: <aqua>{0}'
  sql-completed: <green>Completed, {0} rows affected.
  sql-exception: <red>An error occurred while executing the SQL query, check the Console for details!
  sql-disabled: '<red>For security reason, SQL queries are disabled in this server, If you really need this feature, you can add the following flag to startup arguments: <aqua>{0}</aqua> with `true` value to enable it.'
  force-shop-reload: <yellow>Forcing reload all loaded shops...
  force-shop-reload-complete: <green>Forced <aqua>{0}</aqua> shops to be reloaded.
  force-shop-loader-reload: <yellow>Forcing shop-loader reloading...
  force-shop-loader-reload-unloading-shops: <yellow>Unloading <aqua>{0}</aqua> loaded shops...
  force-shop-loader-reload-unloading-shops-from-memory: <yellow>Removing <aqua>{0}</aqua> shops from memory...
  force-shop-loader-reload-reloading-shop-loader: <yellow>Re-calling shop-loader for reloading all shops from database...
  force-shop-loader-reload-complete: <green>Shop-loader has been reloaded all shops!
  toggle-shop-loaded-status: <aqua>Toggle this shop loaded status to <gold>{0}
  shop-internal-data: '<yellow>The internal data of this shop: </yellow>{0}'
  handler-list-not-valid-bukkit-event-class: <red>The provided class <yellow>{0}</yellow> is not a valid Bukkit event class.
  update-player-shops-signs-no-username-given: <red>You must provide a valid player username.
  update-player-shops-signs-create-async-task: <yellow>Creating async tasks for force updating signs...
  update-player-shops-player-selected: '<yellow>Player selected: <gold>{0}'
  update-player-shops-player-shops: <yellow>Total <gold>{0}</gold> shops waiting for updates.
  update-player-shops-per-tick-threshold: '<yellow>Max shops can be update per tick: <gold>{0}'
  update-player-shops-complete: <green>Task completed, Used <yellow>{0}ms</yellow> for updating.
  update-player-shops-task-started: <gold>The tasks has been started, please wait for it to complete.
  item-info-store-as-string: "<green>The store you looking at: <gold>{0}</gold> Hash: <white>{1}</white>"
  item-info-hand-as-string: "<green>The item in your hand: <gold>{0}</gold> Hash: <white>{1}</white>"
  item-matching-result: "<green>Hand2Store: <aqua>{0}</aqua>, Store2Hand: <aqua>{1}</aqua>"
  hikari-cp-size-tweak: "<green>HikariCP MaximumPoolSize and MinimumIdle was set to <white>{0}</white>"
  hikari-cp-testing: "<green>Please wait, testing HikariCP connection..."
  hikari-cp-working: "<green>Pass! HikariCP working well!"
  hikari-cp-not-working: "<red>Failed! The connection that returned by HikariCP is dead! (Not passed test in 1 second)"
  hikari-cp-timeout: "<red>HikariCP is timed out while getting a valid connection, please clean up all active queries to release connection resources."
  queries-stopped: "<green>Stopped <white>{0}</white> active queries."
  queries-dumping: "<yellow>Dumping active queries..."
  restart-database-manager: "<yellow>Restarting SQLManager..."
  restart-database-manager-clear-executors: "<yellow>Clearing executors..."
  restart-database-manager-unfinished-task: "<yellow>Unfinished task: <white>{0}</white>"
  restart-database-manager-unfinished-task-history-query: "<yellow>Unfinished task (History Query): <white>{0}</white>"
  restart-database-manager-reconnect: "<yellow>Re-launching SQLManager via initial sequence (via async executor)"
  restart-database-manager-done: "<green>Done!"
  property-incorrect: "<yellow>You must enter a (and only one) property key=value set. E.g   aaa=bbb"
  property-security-block: "<red>Request was rejected, for security reason, you can only change the property that startsWith <aqua>com.ghostchu.quickshop</aqua> or <aqua>quickshop</aqua>."
  property-removed: "<green>Removed property key <white>{0}</white>"
  property-changed: "<green>Property key <white>{0}</white> was changed from <white>{1}</white> to <white>{2}</white>"
  marked-as-dirty: "<green>Marked all stores as dirty status, they will be force save in next auto-save task. (Restart the server to force execute store saving task)"
  display-removed: "<green>Successfully removed <yellow>{0}</yellow> QuickShop display items/entities from the worlds."
database:
  scanning: <green>Scanning the isolated data in QuickShop databases, the database load may increase in scan progress, this might need a while...
  scanning-async: <yellow>Scanning the isolated data in QuickShop databases on async task thread, the database load may increase in scan progress, this might need a while, try again later.
  already-scanning: <red>An scan task has been started, please wait until it finished.
  trim-warning: <red><bold>Warning:</bold> <yellow>Backup your database before continuing the Database trim to avoid data loss. Once you're ready, execute <aqua>/quickshop database trim confirm</aqua> to continue.
  status: '<yellow>Status: {0}'
  status-good: <green>Good
  status-bad: <yellow>Maintenance Required
  isolated: '<yellow>Isolated Data:'
  isolated-data-ids: '<aqua>└<yellow> Data Records: <gold>{0}'
  isolated-shop-ids: '<aqua>└<yellow> Shop Indexes: <gold>{0}'
  isolated-logs: '<aqua>└<yellow> Logs: <gold>{0}'
  isolated-external-caches: '<aqua>└<yellow> External Caches: <gold>{0}'
  last-purge-time: <yellow>Last time of trim at {0}
  report-time: <yellow>Last time of scan at {0}
  auto-scan-alert: <yellow>The QuickShop database on this server require a maintenance. Found <gold>{0}</gold> isolated data wait for trimming.
  auto-trim: <green>Auto trim has been enabled on this server, no manually trim required.
  trim-complete: <green>Database trim completed, <yellow>{0}</yellow> isolated trimmed.
  auto-trim-started: <green>Auto trim has been started, please wait...
  trim-start: <green>Database trim started, please wait...
  trim-exception: <red>Database trim failed, an exception raised during database trimming, check the server console.
  generated-at: '<yellow>Generated at: <gold>{0}'
  purge-date: <red>Turite nurodyti datą.
  purge-warning: <yellow>This operation will purge the history that stored in your database including shop creates/changes/deletes, purchases, transactions and system logs. Delete those data can free the disk space but all history metric will lost, and other plugins that depend on metrics will stop working. To continue execute this operation, execute `/quickshop database purgelogs \<before-days> confirm`
  purge-task-created: <green>Task created! Database are silent purging history records in background.
  purge-done-with-line: <green>Purge task completed, total <gold>{0}</gold> lines purged from database.
  purge-done-with-error: <red>Purge task failed, check the server console for details.
  purge-players-cache: <yellow>Please wait, purging players caches...
  purge-players-completed: |-
    <green>Successfully purged {0} players caches from both memory and database.
    <aqua>Note: You server performance may impact by this operation.
  purge-players-error: <red>Failed to purge players caches, please check the server console.
  suggestion:
    trim: <yellow>This database requires a trimming of isolated data. Execute <aqua>/quickshop database trim</aqua> to trim the database.
always-counting-removal-early-warning: <red>Always Counting feature is scheduled for removal, you shouldn't use it anymore and will stop work in future.
exporting-database: <green>Exporting database, please wait...
exporting-failed: <red>Failed to export database, please check the server console.
exported-database: <green>Database exported to <yellow>{0}</yellow>.
importing-not-found: <red>The <yellow>{0}</yellow> file not found, please check the file path.
importing-early-warning: |-
  <red><bold>Warning:</bold> <yellow>The backup will be imported into the current database. Any existing data will be purged and is lost forever unless you have a backup.
  <red>Are you sure to continue the import-procedure?</red> Type <aqua>/quickshop recovery confirm</aqua> to continue.
importing-database: <green>Importing database from backup, please wait...
importing-failed: <red>Failed to import database, please check the server console.
imported-database: <green>Database imported from <yellow>{0}</yellow>.
transfer-sent: <green>Shops transfer request sent to <yellow>{0}</yellow>.
transfer-request: <yellow>Player <aqua>{0}</aqua> requested to transfer their shops to you. Did you want accept the transfer?
transfer-single-request: <yellow>Player <aqua>{0}</aqua> would like to transfer a shop to you. Do you want to accept the request?
transfer-ask: |-
  <gold>Type <red>/quickshop transfer accept</red> to accept or <red>/quickshop transfer deny</red> to deny.
  The request will expire after <red>{0}</red> seconds.
transferall-ask: |-
  <gold>Type <red>/quickshop transferall accept</red> to accept or <red>/quickshop transferall deny</red> to deny.
  The request will expire after <red>{0}</red> seconds.
transfer-single-ask: |-
  <gold>Type <red>/quickshop transferownership accept</red> to accept or <red>/quickshop transferownership deny</red> to deny.
  The request will expire after <red>{0}</red> seconds.
transfer-accepted-fromside: <green>Player <aqua>{0}</aqua> accepted your shop transfer request.
transfer-accepted-toside: <green>You accepted <aqua>{0}</aqua>'s transfer request.
transfer-rejected-fromside: <red>Player <aqua>{0}</aqua> rejected your shop transfer request.
transfer-rejected-toside: <red>You rejected <aqua>{0}</aqua>'s shop transfer request.
transfer-no-pending-operation: <red>You don't have pending transfer request yet.
transfer-no-self: <red>You can't transfer your shops to yourself.
benefit-overflow: <red>The sum of all benefits cannot be greater than or equal to 100%.
benefit-exists: <red>Target player already in benefits list on this shop.
benefit-removed: <red>Target player has been removed from shop benefits.
benefit-added: <green>Player <aqua>{0}</aqua> has been added into shop benefits!
benefit-updated: <green>Player <aqua>{0}</aqua>'s benefits has been updated!
benefit-query: <green>This shop have <yellow>{0}</yellow> players in benefits list!
benefit-query-list: <yellow> - </yellow><white>Player <gold>{0}</gold>, Benefit <gold>{1}%
tag-added: '<green>Successfully added <aqua>#{0}</aqua> to this shop!'
tag-add-duplicate: '<red>The tag <aqua>#{0}</aqua> already exists at this shop!'
tag-removed: '<green>Successfully removed <aqua>#{0}</aqua> from this shop!'
tag-remove-not-exists: 'The tag <aqua>#{0}</aqua> not exists at this shop!'
tag-cleared: <green>Successfully cleared all tags from this shop!
tag-shops-cleared: '<green>Successfully cleared <aqua>#{0}</aqua> from all your tagged shops!'
tag-query: '<green>This shop have <yellow>{0}</yellow> tags:'
tag-query-listing: '<yellow> - <aqua>#{0}'
tag-query-no-tag: <red>This shop have no tags.
tag-query-shops: '<green>This tag contains <yellow>{0}</yellow> shops:'
tag-query-shops-listing: <yellow> - <aqua>{0}
batch-operations-based-on-tags-no-failure: <green>Successfully batch processed <yellow>{0}</yellow> shops.
batch-operations-based-on-tags-have-failure: <yellow>Total {0} shops in batch processing successfully completed, but <red>{1}</red> requests not completed.
batch-operations-based-on-tags-have-failure-with-reason: '<yellow>Total {0} shops in batch processing successfully completed, but <red>{1}</red> requests not completed. Reason: <gold>{2}'
batch-operations-based-on-tags-have-failure-error-messages-may-appears-above: <gray>The error messages will send to you above this message in the chat.
addon:
  towny:
    commands:
      town: <yellow>Set or unset a shop to a Town shop
      nation: <yellow>Set or unset a shop to a Nation shop
    make-shop-owned-by-town: <green>You have made the shop owned by town <yellow>{0}</yellow>.
    make-shop-no-longer-owned-by-town: <green>You have reset shop ownership, it now transfer back to original shop owner.
    make-shop-owned-by-nation: <green>You have made the shop owned by nation <yellow>{0}</yellow>.
    make-shop-no-longer-owned-by-nation: <green>You have reset shop ownership, it now transfer back to original shop owner.
    shop-owning-changing-notice: <gray>This shop now owned by a town/nation, original shop owner has been automatically added into administrator list, modify or add any new owner / staff by using command /quickshop permission
    target-shop-already-is-town-shop: <red>The target shop is already owned by a town.
    target-shop-already-is-nation-shop: <red>The target shop is already owned by a nation.
    target-shop-not-in-town-region: <red>The target shop is not in the town.
    target-shop-not-in-nation-region: <red>The target shop is not in the nation.
    item-not-allowed: <red>This shop's item are not allowed used by town/nation shop, pick another one!
    operation-disabled-due-shop-status: <red>This shop operation has been disabled due this already is a town/nation shop.
    plot-type-disallowed: <red>You can't create town/nation shop on this type of plot.
    flags:
      own: <red>You can only create shops at your owned shop type towny plot.
      modify: <red>You didn't have build permission on this towny plot.
      shop-type: <red>You must create shop on a shop type towny plot.
  residence:
    creation-flag-denied: <red>You don't have permission to create shops in this residence.
    trade-flag-denied: <red>You don't have permission to purchase in this residence.
    you-cannot-create-shop-in-wildness: <red>You can't create shop in wildness.
  griefprevention:
    creation-denied: <red>You don't have permission to create shops in this claim.
    trade-denied: <red>You don't have permission to purchase shops in this claim.
  lands:
    world-not-enabled: <red>You are not allowed to create or purchase in this world.
    creation-denied: <red>You don't have permission to create shops in this Land area.
  plotsquared:
    no-plot-whitelist-creation: <red>You cannot create shop outside of Plot.
    no-plot-whitelist-trade: <red>You cannot purchase shop outside of Plot.
    creation-denied: <red>You don't have permission to create shops in this Plot.
    trade-denied: <red>You don't have permission to purchase shops in this Plot.
    flag:
      create: Create QuickShop-Hikari shops
      trade: Purchase QuickShop-Hikari shops
  superiorskyblock:
    owner-create-only: <red>Only island owner can create shop there.
    owner-member-create-only: <red>Only island owner or member can create shop there.
  worldguard:
    creation-flag-test-failed: <red>You don't have permission create shop in this WorldGuard area.
    trade-flag-test-failed: <red>You don't have permission trade shop in this WorldGuard area.
    reached-per-region-amount-limit: "<red>You have reached the maximum amount of shops in this region."
  discord:
    __to_translators: 'To translators: `addon.discord.discord-messages` use a special variable in strings, %%LANG_KEY%% instead of QuickShop''s style {LANG_INDEX}, do not translate anything inside double ''%'' section. BUT for any other section texts, it respect the normal QuickShop Text System syntax ({0},{1} etc.) This may be difficult to understand because two different variable forms are used in the same program at the same time, but trust me, it is reasonable. (you don''t need translate this string, it''s a comment)'
    __to_users: Like the QuickShop Text System, Discord addon will also auto-detect user language and use user's language to send Discord message, it following the QuickShop-Hikari's language system settings.
    __to_message_designer: 'Design your Discord message with GUI: https://glitchii.github.io/embedbuilder/, then copy the JSON code and paste it into the translation and here we go!'
    discord-enabled: <aqua>Successfully <green>enabled</green> your QuickShop discord messages, now you can receive the shop messages from Discord.
    discord-disabled: <aqua>Successfully <red>disabled</red> your QuickShop discord messages, now you won't receive the shop messages from Discord anymore.
    discord-not-integrated: <red>You hadn't link your Discord yet! Please link your Discord account first!
    feature-enabled-for-user: <aqua>You have <green>enabled</green> <gold>{0}</gold> notification.
    feature-disabled-for-user: <aqua>You have <red>disabled</red> <gold>{0}</gold> notification.
    link-help: <yellow>This server using <gold>{0}</gold> for Discord driver, please use <green>{0}</green> to link your Discord account.
    save-notifaction-exception: <red>An error occurred while saving your Discord notifaction settings, please contact the server administrator.
    feature-status-changed: <green>Successfully set the notifaction <aqua>{0}</aqua> status to <gold>{1}
    commands:
      discord:
        description: <yellow>Managing your QuickShop Discord settings
    discord-messages:
      sold-to-your-shop: |
        {
         "embed":
           {
             "title": ":inbox_tray: Somebody sold items to your shop",
             "description": "The player %%purchase.name%% sold x%%purchase.amount%% %%shop.item.name%% to your shop.",
             "color": 52084,
             "author": {
               "name": "QuickShop-Hikari",
               "icon_url": ""
             },
             "thumbnail": {
               "url": ""
             },
             "image": {
               "url": ""
             },
             "footer": {
               "text": "QuickShop-Hikari Discord Notification",
               "icon_url": ""
             },
             "fields": [
               {
                 "name": "Shop",
                 "value": "%%shop.display-name%%",
                 "inline": false
               },
               {
                 "name": "Purchaser",
                 "value": "%%purchase.name%%",
                 "inline": true
               },
               {
                 "name": "Item",
                 "value": "%%shop.item.name%%",
                 "inline": true
               },
               {
                 "name": "Amount",
                 "value": "%%purchase.amount%% (x%%shop.stacking-amount%%)",
                 "inline": true
               },
               {
                 "name": "Your paid",
                 "value": "%%purchase.balance-formatted%%",
                 "inline": true
               },
               {
                 "name": "Taxes",
                 "value": "%%purchase.taxes-formatted%%",
                 "inline": true
               }
             ]
           }
        }
      bought-from-your-shop: |
        {
           "embed":
             {
               "title": ":outbox_tray: Somebody bought items from shop",
               "description": "The player %%purchase.name%% bought x%%purchase.amount%% %%shop.item.name%% from your shop.",
               "color": 52084,
               "author": {
                   "name": "QuickShop-Hikari",
                   "icon_url": ""
               },
               "thumbnail": {
                   "url": ""
               },
               "image": {
                   "url": ""
               },
               "footer": {
                   "text": "QuickShop-Hikari Discord Notification",
                   "icon_url": ""
               },
               "fields": [
                   {
                       "name": "Shop",
                       "value": "%%shop.display-name%%",
                       "inline": false
                   },
                   {
                       "name": "Purchaser",
                       "value": "%%purchase.name%%",
                       "inline": true
                   },
                   {
                       "name": "Item",
                       "value": "%%shop.item.name%%",
                       "inline": true
                   },
                   {
                       "name": "Amount",
                       "value": "%%purchase.amount%%(x%%shop.stacking-amount%%)",
                       "inline": true
                   },
                   {
                       "name": "Your earn",
                       "value": "%%purchase.balance-formatted%%",
                       "inline": true
                   },
                   {
                       "name": "Taxes",
                       "value": "%%purchase.taxes-formatted%%",
                       "inline": true
                   }
               ]
           }
         }
      mod-shop-purchase: |
        {
        "embed":
          {
              "title": ":dollar: Somebody purchased in a shop",
              "description": "The player %%purchase.name%% purchase x%%purchase.amount%% %%shop.item.name%% from the shop.",
              "color": 6061450,
              "author": {
                  "name": "QuickShop-Hikari",
                  "icon_url": ""
              },
              "thumbnail": {
                  "url": ""
              },
              "image": {
                  "url": ""
              },
              "footer": {
                  "text": "QuickShop-Hikari Discord Notification",
                  "icon_url": ""
              },
              "fields": [
                  {
                      "name": "Shop",
                      "value": "%%shop.display-name%%",
                      "inline": false
                  },
                  {
                      "name": "Purchaser",
                      "value": "%%purchase.name%%",
                      "inline": true
                  },
                  {
                      "name": "Item",
                      "value": "%%shop.item.name%%",
                      "inline": true
                  },
                  {
                      "name": "Amount",
                      "value": "%%purchase.amount%% (x%%shop.stacking-amount%%)",
                      "inline": true
                  },
                  {
                      "name": "Balance",
                      "value": "%%purchase.balance-formatted%%",
                      "inline": true
                  },
                  {
                      "name": "Taxes",
                      "value": "%%purchase.taxes-formatted%%",
                      "inline": true
                  }
              ]
          }
        }
      out-of-space: |
        {
        "embed":
          {
              "title": ":mailbox_with_mail: Your shop has run out of space",
              "description": "Your shop inventory is full of items!\\nYou need empty your shop container to free the space then you can continue to accept new items.",
              "color": 16065893,
              "author": {
                  "name": "QuickShop-Hikari",
                  "icon_url": ""
              },
              "thumbnail": {
                  "url": ""
              },
              "image": {
                  "url": ""
              },
              "footer": {
                  "text": "QuickShop-Hikari Discord Notification",
                  "icon_url": ""
              },
              "fields": [
                  {
                      "name": "Shop",
                      "value": "%%shop.display-name%%",
                      "inline": false
                  }
              ]
          }
        }
      out-of-stock: |
        {
            "embed":
                {
                    "title": ":mailbox_with_no_mail: Your shop has run out of stock",
                    "description": "Your shop inventory is empty now!\\nYou need to refill the shop container with items to continue selling items!",
                    "color": 16065893,
                    "author": {
                        "name": "QuickShop-Hikari",
                        "icon_url": ""
                    },
                    "thumbnail": {
                        "url": ""
                    },
                    "image": {
                        "url": ""
                    },
                    "footer": {
                        "text": "QuickShop-Hikari Discord Notification",
                        "icon_url": ""
                    },
                    "fields": [
                        {
                            "name": "Shop",
                            "value": "%%shop.display-name%%",
                            "inline": false
                        }
                    ]
                }
        }
      mod-shop-created: |
        {
          "embed": {
            "title": ":classical_building: A new shop has been created",
            "description": "Player created a new shop on your server!",
            "color": 6061450,
            "author": {
              "name": "QuickShop-Hikari",
              "icon_url": ""
            },
            "thumbnail": {
              "url": ""
            },
            "image": {
              "url": ""
            },
            "footer": {
              "text": "QuickShop-Hikari Discord Notification",
              "icon_url": ""
            },
            "fields": [
              {
                "name": "Shop",
                "value": "%%shop.display-name%%",
                "inline": false
              },
              {
                "name": "Owner",
                "value": "%%shop.owner.name%%",
                "inline": true
              },
              {
                "name": "Item",
                "value": "%%shop.item.name%%",
                "inline": true
              },
              {
                "name": "Amount",
                "value": "%%shop.stacking-amount%%",
                "inline": true
              },
              {
                "name": "Type",
                "value": "%%shop.type%%",
                "inline": true
              }
            ]
          }
        }
      mod-remove-shop: |
        {
            "embed": {
                "title": ":recycle: A shop was removed from this server",
                "description": "A shop was removed from this server.",
                "color": 6061450,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "Reason",
                        "value": "%%delete.reason%%",
                        "inline": false
                    }
                ]
            }
        }
      shop-transfer-to-you: |
        {
            "embed": {
                "title": ":placard: A shop was transfer to you",
                "description": "A shop transfer to you from another player.",
                "color": 16172079,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "From",
                        "value": "%%transfer.from%%",
                        "inline": false
                    }
                ]
            }
        }
      mod-shop-transfer: |
        {
            "embed": {
                "title": ":placard: A shop was transferred",
                "description": "A shop transfer to a player from another player.",
                "color": 6061450,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "From",
                        "value": "%%transfer.from%%",
                        "inline": true
                    },
                    {
                        "name": "To",
                        "value": "%%transfer.to%%",
                        "inline": true
                    }
                ]
            }
        }
      shop-price-changed: |
        {
            "embed": {
                "title": ":money_with_wings: Your shop price was changed",
                "description": "You or your shop staff changed your shop price.",
                "color": 16172079,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "From",
                        "value": "%%change-price.from%%",
                        "inline": true
                    },
                    {
                        "name": "To",
                        "value": "%%change-price.to%%",
                        "inline": true
                    }
                ]
            }
        }
      mod-shop-price-changed: |
        {
            "embed": {
                "title": ":money_with_wings: A shop price has been changed",
                "description": "A shop changed its price settings.",
                "color": 6061450,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": true
                    },
                    {
                        "name": "Owner",
                        "value": "%%shop.owner.name%%",
                        "inline": true
                    },
                    {
                        "name": "",
                        "value": "",
                        "inline": true
                    },
                    {
                        "name": "From",
                        "value": "%%change-price.from%%",
                        "inline": true
                    },
                    {
                        "name": "To",
                        "value": "%%change-price.to%%",
                        "inline": true
                    }
                ]
            }
        }
      shop-permission-changed: |
        {
            "embed": {
                "title": ":closed_lock_with_key: Your shop permission settings has been changed",
                "description": "One of your shop's permission setting has been changed.",
                "color": 15879747,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "Player",
                        "value": "%%change-permission.player%%",
                        "inline": true
                    },
                    {
                        "name": "Assign to group",
                        "value": "%%change-permission.to-group%%",
                        "inline": true
                    },
                    {
                        "name": "",
                        "value": "",
                        "inline": true
                    },
                    {
                        "name": "Permission granted (Inherit from group)",
                        "value": "```\\n%%change-permission.perms-list%%\\n```",
                        "inline": false
                    }
                ]
            }
        }
    field:
      player: Žaidėjas
      item: Daiktas
      amount: Kiekis
      balance: Balansas
      balance-after-tax: Balance (after tax)
      account: Your account balance
      taxes: Taxes
      cost: Cost
  discount:
    commands:
      discount:
        description: <yellow>Apply a discount code or manage your discount codes.
    tab-complete:
      discount:
        general:
          code: \<code>
        create:
          rate: |
            Command Hint:
            Argument: \<rate>
            Description: The actual percentage or money you will earn
            Input `30%` = price * 0.3
            Input `50` = price - 50
          max-usage: |
            Command Hint:
            Argument: [max-usage]
            Description: The times that code can be used
            `-1` for unlimited.
          threshold: |
            Command Hint:
            Argument: [threshold]
            Description: The minimum price that code can be applied
            `-1` for unlimited
          expired: |
            Command Hint
            Argument: [expired]
            Description: The code expired time.
            `-1` for unlimited duration.
            Accept both Zulu time and UNIX timestamp in seconds.
            Zulu Example: 2022-12-17T10:31:37Z
            UNIX Example: 1671273097
    discount-code-already-exists: <red>Sorry, the name of your discount code already in use.
    invalid-discount-code-regex: '<red>The discount code must matches the regex: <yellow>{0}'
    invalid-discount-code: <red>This discount code are invalid.
    discount-code-added: <green>Your discount code <yellow>{0}</yellow> has been added into shop <aqua>{1}
    discount-code-removed: <green>Your discount code <yellow>{0}</yellow> has been removed from shop <aqua>{1}
    invalid-code-type: <red>The code type <yellow>{0}</yellow> are invalid.
    invalid-usage-restriction: <red>The usage restriction <yellow>{0}</yellow> are invalid.
    invalid-threshold-restriction: <red>The threshold restriction <yellow>{0}</yellow> are invalid.
    invalid-effect-scope: <red>The scope <yellow>{0}</yellow> are invalid.
    invalid-expire-time: <red>You can't specify a time in the past.
    invalid-discount-rate: <red>The discount rate <yellow>{0}</yellow> are invalid, it can be a fixed number or percentage.
    discount-code-expired: <red>Ouch! Your discount code <yellow>{0}</yellow> has been expired!
    discount-code-installed: <green>You have installed discount code <gold>{0}</gold>, the discount code will be automatically applied to the available purchase in all the following purchases during this session. To uninstall discount code, execute <aqua>/quickshop discount uninstall {0}</aqua>.
    discount-code-uninstalled: <green>You have uninstalled your discount code.
    discount-code-query-nothing: <red>You hadn't install a discount code yet!
    discount-code-query: '<green>You are using discount code: <yellow>{0}</yellow>.'
    discount-code-applicable: '<#bcef26>Your discount code <bold><yellow>{0}</yellow></bold> are <bold>applicable</bold> in this shop!'
    discount-code-applicable-with-threshold: '<#bcef26>Your discount code <bold><yellow>{0}</yellow></bold> are <bold>applicable</bold> in this shop, but only your purchase cost over <yellow>{1}</yellow> in single transaction.'
    discount-code-not-applicable: <red>Your discount code <bold><yellow>{0}</yellow></bold> are <bold>not applicable</bold> in this shop!
    discount-code-reach-the-limit: <red>You have reach the usage limit of discount code <bold><yellow>{0}</yellow></bold>, discount are not applied.
    discount-code-no-permission: <red>You didn't have permission to use any discount code in this shop!
    discount-code-has-been-expired: <red>Your discount has been expired!
    discount-code-config-shop-added: <green>Successfully to add this shop into your discount code allow list.
    discount-code-config-shop-add-failure: <red>This shop already in your discount code allow list.
    discount-code-config-shop-removed: <green>Successfully to remove this shop from your discount code allow list.
    discount-code-config-shop-remove-failure: <red>This shop not exists in your discount code allow list.
    discount-code-config-expire: <green>Successfully changed your code expire time.
    discount-code-config-applied: <green>Config the discount code successfully!
    discount-code-created-successfully: |
      <green>Your discount code <yellow>{0}</yellow> has been successfully created!
      <gold>Scope: <aqua>{1}</aqua></gold>.
      <gold>Share with others: <#bcef26>{2}</#bcef26></gold>.
      <yellow>For <gray>Specific Shops</gray> scope only: To add shops to your discount code allow list, please looking an quickshop and execute <aqua>{3}</aqua> command.
      You can use <aqua>/quickshop discount config {0}</aqua> to edit your discount code anytime.
    discount-code-under-threshold: <red>The discount not applied to your purchase because the total value under the discount code threshold <yellow>{0}</yellow>.
    percentage-off: '<bold><#bcef26>-{0}%'
    fixed-off: '<bold><#bcef26>-{0}'
    discount-code-list: '<gold>Listing your discount codes:'
    discount-code-applied-in-purchase: '<#bcef26>Your discount code <yellow>{0}</yellow> has been applied to this purchase and you saved <gold>{1}</gold>!'
    scope:
      this-shop: '{0}'
      your-shops-owned: all your shops (owned)
      your-shops-managed: all your shops (managed)
      server: whole server
    code-type:
      SERVER_ALL_SHOPS: All shops on this server
      PLAYER_ALL_SHOPS: All shops that created by code owner
      SPECIFIC_SHOPS: Specific shops
    discount-code-details: |-
      <gold>Code: <yellow>{0}</yellow>
      Creator: <yellow>{1}</yellow>
      Applied to: <yellow>{2}</yellow>
      Remaining usage: <yellow>{3}</yellow>
      Expired on: <yellow>{4}</yellow>
      Threshold: <yellow>{5}</yellow>
      Discount: <yellow>{6}</yellow><br>
  list:
    commands:
      list: <yellow>Listing all shops owned by your or specific player
    table-prefix: '<yellow><green>{0}</green>''s shops <gray>(Total {1})</gray>: '
    table-prefix-pageable: '<yellow><green>{0}</green>''s shops <gray>(Page {1}/{2})</gray>: '
    entry: <yellow><hover:show_text:'<yellow>{1}</yellow><br><gray>{2} {3},{4},{5}</gray><br><aqua>Price <yellow>{6}</yellow> - <light_purple>x{7}</light_purple> <yellow>{8}</yellow><br><green>{9}'>{0}. <aqua>{1}
  shopitemonly:
    message: <red>Negalite įdėti draudžiamų prekių į parduotuve, visos ne parduotuvei skirtos prekės bus išmestos jūsų esamoje vietoje.
  limited:
    commands:
      limit: <yellow>Set a limit that restrict the purchase that player can make in period
    titles:
      title: <green>Purchase Success
      subtitle: <aqua>You can also purchase <gold>{0}</gold> more in this shop
    reach-the-limit: <red>You have reached the limit in this shop. You can also purchase <green>{0}</green> items, but you trying purchase <yellow>{1}</yellow> items.
    success-reset: <green>Reset the restriction of this shop successfully
    success-remove: <green>Successfully remove the all restriction of this shop
    success-setup: <green>Restriction settings for this shop were saved successfully
    trade-limit-reached-cancel-reason: <red>Reached the restriction of this shop
    remains-limits: '<gold>Your remaining purchase quota in this shop: <yellow>{0}'
    remains-limits-before-reset-period-note: '<gold>This shop will reset their purchase quota every: <yellow>{0}'
  dynmap:
    markerset-title: QuickShop-Hikari Shops
    marker-name: '{0} @ {2} @ {3} for x{4} @ {5}'
    marker-description: |
      Name: {0}
      Owner: {1}
      Item: {2}
      Price: {3} for x{4} item(s)
      Type: {5}
      Unlimited: {6}
      Location: {7}
  bluemap:
    markerset-title: QuickShop-Hikari Shops
    marker-name: '{0} @ {2} @ {3} for x{4} @ {5}'
    marker-description: |
      Name: {0}
      Owner: {1}
      Item: {2}
      Price: {3} for x{4} item(s)
      Type: {5}
      Unlimited: {6}
      Location: {7}
  chestprotect:
    protection-exists: <red>This region already protected by ChestProtect and you don't have permission to create shops at here.
    shops-exsts: <red>The region that you want to protect contains other player's shops and you don't have permission to access them.
  displaycontrol:
    toggle: |-
      <green>Successfully toggle your QuickShop display showcasing to <aqua>{0}</aqua>.
      <yellow>You may need re-join to take effect.
    toggle-exception: <red>Failed to toggle your display showcase settings due an internal error, please contact server administrator.
    command:
      displaycontrol: <yellow>Toggle your QuickShop display showcasing
  reremake-migrator:
    commands:
      migratefromreremake: Migrate QuickShop-Reremake's data to QuickShop-Hikari
    server-not-empty: "<red>No players are allowed to be online on the server during the conversion progress, please enable server whitelist or maintenance mode."
    starting-convert-progress: "<gold>Starting the convert progress, <red>DO NOT SHUTDOWN YOUR SERVER!</red>"
    executing: "<gold>Executing migrate component <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
    completed: "<green>Done! Migrate completed, please restart your server and remove QuickShop-Reremake from plugins directory."
    join_blocking_converting: "<red>[QuickShop-Hikari Migrator] This server is performing a data conversion and you cannot join the server at this time, try again later!"
    join_blocking_finished: "<red>[QuickShop-Hikari Migrator] This server has just completed a data conversion and is waiting for a reboot to apply the changes, try again later!"
    failed: "<red>Migration progress exited with error, please check the server console."
    modules:
      config:
        copy-values: "<yellow>Copying values (total {0} entries)..."
        copying-value: "<gray> - Copying <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        migrate-price-restriction: "<yellow>Migrating price-restriction related settings..."
        migrate-price-restriction-entry: "<gray> - Migrating <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
      shop:
        start-migrate: "<yellow>Migrating shops (total {0} entries)..."
        migrate-entry: "<gray> - Migrating <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        unloading-reremake: "<gold>Unloading Reremake to avoid data overwrite..."
        register-entry: "<gray> - Registering <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        save-entry: "<gray> - Saving <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        saving-shops: "<yellow>Saving <gold>{0}</gold> shops, this may need a while™ (the time based on shops amounts)..."
        conflict: "<gray> - CONFLICT > Detects a conflict between a Reremake store and an existing Hikari store location, taking a predefined behavior: <dark_gray>{0}</dark_gray>"
      translation:
        start-migrate: "<yellow>Migrating translation files...</yellow>"
        copy-values: "<yellow>Copying values in locale <gold>{0}</gold> (total {1} entries)..."
        migrate-entry: "<gray> - Migrating <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        copying-value: "<gray> - Copying <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
      shop-logs:
        start-migrate: "<yellow>Migrating shop logs (details in console), please wait...</yellow>"
        extract-history-files: "<gray>Please wait for us to unzip and append the history logs..."
        filter-history-files: "<gray>Please wait for us to filter the history log..."
        filtered-history-files: "<gray>Filtered {0} lines out of queue."
        import-entry: "<gray> - Migrating <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
compat:
  advancedchests:
    created: <green>You're successfully created a shop on AdvancedChests container!
    permission-denied: <red>Sorry! You don't have the permission to create a shop on a AdvancedChests container!
suite:
  dynmap:
    title: '{0} - {1} - {2}'
    description: |-
      Item: {0}
      Owner: {1}
      Type: {2} {3}
      Price: {4}
      Location: {5} {6}, {7}, {8}
      Space: {9}
      Stock: {10}
  limited:
    command-description: <yellow>Set a limit that restrict the purchase that player can make in period.
    reach-the-quota-limit: <red>You have reach the purchase limit in this shop ({0}/{1}).
    quota-reset-countdown: <yellow>The quota in this shop will reset in {0}.
    quota-reset-player-successfully: <green>The quota for player {0} in this shop has been reset successfully.
    quota-reset-everybody-successfully: <green>The quota for everyone in this shop has been reset successfully.
    quota-setup: <green>The purchase restriction now apply to this shop!
    quota-remove: <green>The purchase restriction now removed from this shop!
    subtitles:
      title: <green>Purchase Success
      subtitle: <aqua>You can purchase <yellow>{0}</yellow> more in this shop
  list:
    command-description: <yellow>List all shops owned by yourself or another player.
    table-prefix: <yellow>You owned total <aqua>{0}</aqua> shops on this server.
    entry-syntax: <aqua>- <yellow>{0}
    entry-syntax-fallback-name: '<yellow>Prekė:{0} X:{1}, Y:{2}, Z:{3} Pasaulis: {4}'
    hover-text: |-
      <yellow>{0}
      <gray>{1} {2}, {3}, {4}
      <aqua>{5} - <yellow>{6}
      {7} items each stack.
  shopitemonly:
    message: <red>Negalite įdėti draudžiamų prekių į parduotuve, visos ne parduotuvei skirtos prekės bus išmestos jūsų esamoje vietoje.
compatibility:
  elitemobs:
    soulbound-disallowed: Negalite prekiauti prekėmis, kurios dropinamos su "EliteMobs plugino" enchantais.
internet-paste-forbidden-privacy-reason: "<red>Failed! According your privacy setting, QuickShop-Hikari are not allowed upload your paste to Internet, turn on DIAGNOSTIC permission in privacy setting in config.yml or use <aqua>/quickshop paste --file</aqua> instead."
no-sign-type-given: "<red>You need give a sign material, the sign materials that available on this server: {0}"
sign-type-invalid: "<red>The type <yellow>{0}</yellow> not a valid sign material."
delete-controlpanel-button-confirm: "<red>Do you really want remove this shop? Click the <bold>[Remove Shop]</bold> button again in {0} seconds to confirm."
cannot-suggest-price: "<red>Sorry, For the time being, no more people are trading the same item as you and there is not enough data to generate a suggested price."
price-suggest: "<green>Based on data from <aqua>{0}</aqua> stores, the highest priced store is priced at <light_purple>{1}</light_purple>, the lowest priced store is priced at <light_purple>{2}</light_purple>, the average price is <light_purple>{3}</light_purple>, and the median price is <light_purple>{4}</light_purple>. <newline><yellow>It's recommended that you set your price around <gold>{5}</gold>.</yellow>"
suggest-wait: "<green>Please wait... Calculating recommend price."
history:
  shop:
    gui-title: "View purchase history"
    header-icon-multiple-shop: "<white>The query result across {0} shops</white>"
    header-icon-description:
      - "<white>Type: <yellow>{0}</yellow></white>"
      - "<white>Owner: <yellow>{1}</yellow></white>"
      - "<white>Item: <yellow>{2}</yellow></white>"
      - "<white>Price: <yellow>{3} <aqua>x{4}</aqua></yellow></white>"
      - "<white>Location: <yellow>{5}</yellow></white>"
    log-icon-title: "<reset><green>Time: {0}</green>"
    log-icon-description:
      - "<white>Purchaser: <yellow>{0}</yellow></white>"
      - "<white>Item: <yellow>{1} <aqua>x{2}</aqua></yellow></white>"
      - "<white>Balance: <yellow>{3}</yellow></white>"
      - "<white>Tax: <yellow>{4}</yellow></white>"
    log-icon-description-with-store-name:
      - "<white>Store: <yellow>{0}</yellow></white>"
      - "<white>Purchaser: <yellow>{1}</yellow></white>"
      - "<white>Item: <yellow>{2} <aqua>x{3}</aqua></yellow></white>"
      - "<white>Balance: <yellow>{4}</yellow></white>"
      - "<white>Tax: <yellow>{5}</yellow></white>"
    query-icon: "<gray>Please wait, querying...</gray>"
    previous-page: "<white><< Previous Page</white>"
    next-page: "<white>Next Page >></white>"
    current-page: "<white>Page {0}</white>"
    summary-icon-title: "<green>Shop Summary"
    recent-purchases: "<white>Recent <aqua>{0}</aqua> purchases: <yellow>{1}</yellow></white>"
    recent-purchase-balance: "<white>Recent <aqua>{0}</aqua> turnover: <yellow>{1}</yellow></white>"
    total-purchases: "<white>Total purchases: <yellow>{0}</yellow></white>"
    total-balances: "<white>Total turnover: <yellow>{0}</yellow></white>"
    total-unique-purchasers: "<white>Total unique purchasers: <yellow>{0}</yellow></white>"
    top-n-valuable-customers-title: "<gold>Top {0} valuable customers</gold>"
    top-n-valuable-customers-entry: "<white>- <yellow>{0}</yellow> <aqua>{1}</aqua></white>"
    no-result: "<white>No result</white>"
about:
  text:
    - "<b><aqua>QuickShop <yellow>{0}</yellow></aqua></b>"
    - "<aqua>Version <yellow>>></yellow> <green>{1}</green></aqua>"
    - "<aqua>Release <yellow>>></yellow> <green>{2}</green></aqua>"
    - "<aqua>Developers <yellow>>></yellow> <color:#218bff><hover:show_text:'<yellow>Click to check contributors'><click:open_url:'https://github.com/Ghost-chu/QuickShop-Hikari/graphs/contributors'>[View contributors on GitHub]</click></hover></color></aqua>"
    - "<aqua>Localized members <gold>({4})</gold>: <yellow>>></yellow> <green>{5}</green></aqua>"
    - "  <yellow>+ <color:#43A047><hover:show_text:'<yellow>Click to open Crowdin translate page'><click:open_url:'https://crowdin.com/project/quickshop-hikari'>[Help translate on Crowdin]</click></hover></color></yellow>"
    - "<aqua>Donation Key <yellow>>></yellow> <green>{6}</green></aqua>"
    - "<gold>Powered by community</gold> <red>Made with ❤</red>"
  valid-donation-key: "<color:#00AFF1>Bound to <color:#F9C23C>{0}</color></color>"
  invalid-donation-key: "<gray>Invalid donation key</gra>"
  kofi-thanks: "<gold>Special thanks to those who support QuickShop on Ko-fi :)</gold>"
history-command-leave-blank: "<Leave blank for seeing store>"
shop-information-not-shown-due-an-internal-error: "<red>An internal error occurred. The store information panel may be displayed incompletely, please contact the server administrator."
