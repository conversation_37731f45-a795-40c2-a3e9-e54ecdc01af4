# 繁體中文由 flandretw 翻譯，你可以在 GitHub、Crowdin、Discord 甚至是 YouTube 上找到我。
# 注意如果你想修改此檔案，強烈建議你先複製一份再修改它。
# 此檔案將由 Residence 自動更新。當檢查到新版本時，你的變更將被覆蓋。
# 當擁有此檔案的副本時，變更 Residence config.yml 下的 Language: 為 ChineseTW
# 或是你所修改過後的檔案名稱。

Language:
  Invalid:
    Player: '&c無效的玩家名稱……'
    PlayerOffline: '&c玩家離線。'
    World: '&c無效的世界名稱……'
    Residence: '&c無效的領地……'
    Subzone: '&c無效的子區域領地……'
    Direction: '&c無效的方向……'
    Amount: '&c無效的數量……'
    Cost: '&c無效的花費……'
    Days: '&c無效的天數……'
    Material: '&c無效的物品……'
    Boolean: '&c無效的值，值必須為 &6true（t）&c或 &6false（f）&c。'
    Area: '&c無效的區域……'
    Group: '&c無效的權限組……'
    Location: '&c無效的位置……'
    MessageType: '&c訊息類型必須為 enter（進入）、leave（離開）或 remove（移除）。'
    Flag: '&c無效的權限……'
    FlagType:
      Fail: '&c無效的權限……此權限只能用在 %1。'
      Player: Player
      Residence: Residence
    FlagState: '&c無效的權限狀態，狀態必須為 &6true（t）&c、&6false（f）&c或 &6remove（r）&c。'
    List: '&e未知的列表類型，列表必須為 &6blacklist（黑名單）&e或 &6ignorelist（忽略名單）&e。'
    Page: '&e無效的頁數……'
    Help: '&c無效的說明頁數……'
    NameCharacters: '&c名稱包含不允許字元……'
    PortalDestination: '&c傳送門目的地位於禁止區域內，已取消建立傳送門。&7請尋找新位置。'
    FromConsole: '&c此指令只能在控制台執行！'
    Ingame: '&c此指令無法在控制台執行！'
  Area:
    Exists: '&c已存在相同區域名稱。'
    Create: '&e已建立領地 ID：&6%1&e。'
    DiffWorld: '&c區域和領地存在不同的世界。'
    Collision: '&c區域和領地 &6%1 &c衝突。'
    TooClose: '&c太靠近另外一個領地了。你需要至少 &e%1 &c格方塊的間距。'
    SubzoneCollision: '&c區域和子區域領地 &6%1 &c衝突。'
    NonExist: '&c區域不存在。'
    InvalidName: '&c無效的區域名稱……'
    ToSmallX: '&c你選擇的區域 &6X 軸&c方向長度（&6%1&c）過小。&e此值必須為 &6%2 &e或更大。'
    ToSmallY: '&c你選擇的區域 &6Y 軸&c方向高度（&6%1&c）過小。&e此值必須為 &6%2 &e或更大。'
    ToSmallZ: '&c你選擇的區域 &6Z 軸&c方向長度（&6%1&c）過小。&e此值必須為 &6%2 &e或更大。'
    ToBigX: '&c你選擇的區域 &6X 軸&c方向長度（&6%1&c）過大。&e此值必須為 &6%2 &e或更小。'
    ToBigY: '&c你選擇的區域 &6Y 軸&c方向高度（&6%1&c）過大。&e此值必須為 &6%2 &e或更小。'
    ToBigZ: '&c你選擇的區域 &6Z 軸&c方向長度（&6%1&c）過大。&e此值必須為 &6%2 &e或更小。'
    Rename: '&e已重新命名區域 &6%1 &e為 &6%2&e。'
    Remove: '&e已移除區域 &6%1&e……'
    Name: '&e名稱：&2%1&e。'
    ListAll: '&a{&eID：&c%1 &eP1：&c（%2,%3,%4）&eP2：&c（%5,%6,%7）&e（尺寸：&c%8&e）&a}'
    RemoveLast: '&c無法移除領地中的最後區域。'
    NotWithinParent: '&c區域不在父區域內。'
    Update: '&e已更新區域……'
    MaxPhysical: '&e你已達到了領地最大允許體積。'
    SizeLimit: '&e區域大小超出了允許的尺寸上限。'
    HighLimit: '&c你不能保護這麼高的領地，高度上限為 &6%1&c。'
    LowLimit: '&c你不能保護這麼深的領地，深度上限為 &6%1&c。'
    WeirdShape: '&3領地大小不規則。&6%1 &3邊比 &6%3 &3邊大 &6%2 &3倍。'
  Select:
    Points: '&e執行指令前要先選擇兩個點！'
    Overlap: '&c選擇點和區域 &6%1 &c重疊！'
    WorldGuardOverlap: '&c選擇點與 WorldGuard 區域 &6%1 &c重疊！'
    KingdomsOverlap: '&c選擇點與 Kingdoms 領土 &6%1 &c重疊！'
    Success: '&e選擇成功！'
    Fail: '&c無效的選擇指令……'
    Bedrock: '&e將選擇區域擴展到最低深度。'
    Sky: '&e將選擇區域擴展到最高高度。'
    Area: '&e已選擇 &6%2 &e領地的 &6%1 &e區域。'
    Tool: '&e- 選擇工具：&6%1&e。'
    PrimaryPoint: '&e已選擇&6第一個&e選擇點 %1&e。'
    SecondaryPoint: '&e已選擇&6第二個&e選擇點 %1&e。'
    Primary: '&e第一個選擇點：&6%1&e。'
    Secondary: '&e第二個選擇點：&6%1&e。'
    TooHigh: '&c警告，選擇區域已超過地圖頂部，現在已裁剪。'
    TooLow: '&c警告，選擇區域已超過地圖底部，現在已裁剪。'
    TotalSize: '&e選擇區域總計尺寸：&6%1&e。'
    AutoEnabled: '&e自動選擇模式&6啟用&e，使用 &6/res select auto &e停用。'
    AutoDisabled: '&e自動選擇模式&6停用&e，使用 &6/res select auto &e再次打開。'
    Disabled: '&c你沒有使用選擇指令的權限。'
  Sign:
    Updated: '&6%1 &e已更新告示牌！'
    TopLine: '[market]'
    DateFormat: YY/MM/dd HH:mm
    ForRentTopLine: '&0出租'
    ForRentPriceLine: '&0%1&f/&0%2&f/&0%3'
    ForRentResName: '&0%1'
    ForRentBottomLine: '&9可供使用'
    RentedAutorenewTrue: '&2%1'
    RentedAutorenewFalse: '&c%1'
    RentedTopLine: '%1'
    RentedPriceLine: '&0%1&f/&0%2&f/&0%3'
    RentedResName: '&0%1'
    RentedBottomLine: '&1%1'
    ForSaleTopLine: '&0出售'
    ForSalePriceLine: '&0%1'
    ForSaleResName: '&0%1'
    ForSaleBottom: '&0%1 立方格'
    LookAt: '&c你前方沒有告示牌。'
    TooMany: '&c此領地有太多的告示牌。'
    ResName: '&0%1'
    Owner: '&0%1'
  Raid:
    NotEnabled: '&c尚未啟用侵略模式！'
    NotIn: '&c你不在侵略中！'
    CantLeave: '&c你不能在侵略中離開領地（%1）！'
    CantKick: '&c無法踢出領地擁有者（%1）！'
    Kicked: '&e從領地 &7%2 侵略中踢出 &7%1&e！'
    StartsIn: '&7侵略開始於：[autoTimeLeft] &2防守方：%1 人 &4進攻方：%2 人'
    EndsIn: '&c侵略結束於：[autoTimeLeft] &2防守方：%1 人 &4進攻方：%2 人'
    Ended: '&7領地侵略 &4%1 &7結束！'
    cantDo: '&c無法在領地侵略時做此事情！'
    left: '&7你已離開了 &4%1 &7侵略。'
    noFlagChange: '&c無法在領地侵略時變更權限。'
    noRemoval: '&c無法在領地侵略時移除領地'
    immune: '&e設定下一波侵略 %1&e。'
    notImmune: '&e不再進行設定。'
    notInRaid: '&e玩家不在侵略中。'
    attack:
      Joined: '&7已加入 &2%1 &7侵略！'
      Started: '&7侵略開始！'
      cooldown: '&c太早進行下一場領地侵略！請稍候 %1！'
      immune: '&c此領地免除侵略！請稍候 %1！'
      playerImmune: '&c領地擁有者免除侵略！請稍候 %1！'
      isOffline: '&c無法在領地擁有者離線時侵略！'
      noSubzones: '&c無法侵略子區域領地！'
      noSelf: '&c無法侵略你自己的領地！'
      alreadyInAnother: '&c因為你已在另外一場侵略（%1），所以你無法加入這場侵略！'
    defend:
      Joined: '&7已加入 &2%1 &7防守方！'
      Sent: '&7已確認加入侵略防守方，正在等待確認。'
      Join: '&7加入 &6%1 &7侵略防守方'
      Invitation: '&7從 &6%1 &7接受侵略防守方！'
      JoinedDef: '&2%1&7 加入了防守方！'
      IsOffline: '&c無法在領地擁有者離線時加入侵略防守方！'
      noSelf: '&c你已在此領地中防守方'
      notRaided: '&c領地目前沒有正在侵略！'
      alreadyInAnother: '&c因為你已在另外一場侵略防守方（%1），所以你無法加入這場侵略防守方！'
    status:
      title: '&7----------- &f%1（%2）&7-----------'
      immune: '&e對接下來的侵略免除：%1&e。'
      starts: '&7領地侵略將在以下時間後開始：%1&7。'
      attackers: '&7攻擊者：&4%1&7。'
      defenders: '&7防守方：&4%1&7。'
      ends: '&7領地侵略將在以下時間後結束：%1&7。'
      canraid: '&2可以被侵略。'
      raidin: '&e可能的侵略：%1&e。'
    stopped: '&e侵略 &6%1 &e被停止。'
  info:
    years: '&e%1 &6年 '
    oneYear: '&e%1 &6年 '
    day: '&e%1 &6天 '
    oneDay: '&e%1 &6天 '
    hour: '&e%1 &6小時 '
    oneHour: '&e%1 &6小時 '
    min: '&e%1 &6分鐘 '
    sec: '&e%1 &6秒 '
    listSplitter: '、'
    click: '&7點擊'
    clickToConfirm: '&7點擊確認'
  server:
    land: Server_Land
  Flag:
    p1Color: '&2'
    p2Color: '&a'
    haveColor: '&2'
    havePrefix: ''
    lackColor: '&7'
    lackPrefix: ''
    others: '&e和 &2%1 &e其它'
    Set: '&e領地 &6%2 &e的 &6%1 &e權限已設定為 &6%3&e。'
    SetFailed: '&c你沒有設定 &6%1&c 的權限。'
    CheckTrue: '&e玩家 &6%2 &e已在領地 &6%3 &e中將 &6%1 &e權限設定為 &6%4&e。'
    CheckFalse: '&e玩家 &6%2 &e沒有在領地中設定 &6%1 &e權限。'
    Cleared: '&e已清除權限。'
    RemovedAll: '&e已清除 &6%1 &e在領地 &6%2 &e的所有權限。'
    RemovedGroup: '&e已清除 &6%1 &e組在領地 &6%2 &e的所有權限。'
    Default: '&e已恢復預設權限。'
    Deny: '&c你沒有 &6%1&c 的權限。'
    SetDeny: '&c擁有者不能設定 &6%1 &c的權限。'
    ChangeDeny: '&c因為領地內有 &6%2 &c名玩家，所以你無法更改 &6%1 &c的權限。'
    ChangedForOne: '&e已為領地 &6%2 &e更改 &6%1 &e的權限。'
    ChangedFor: '&e已檢查 &6%2 &e領地更改 &6%1 &e的權限。'
    reset: '&e已重設領地 &6%1 &e的權限。'
    resetAll: '&e已重設領地 &6%1 &e的權限。'
  Bank:
    NoAccess: '&c你沒有使用銀行的權限。'
    Name: ' &e銀行：&6%1'
    NoMoney: '&c銀行存款不足。'
    Deposit: '&e你向領地銀行存款 &6%1&e。'
    Withdraw: '&e你從領地銀行中提款 &6%1&e。'
    rentedWithdraw: '&c無法向租用領地進行銀行提款。'
    full: '&e領地銀行已滿！'
  Subzone:
    Rename: '&e子區域領地 &6%1 &e已被重新命名為 &6%2&e。'
    Remove: '&e已移除子區域領地 &6%1&e。'
    Create: '&e成功建立子區域領地 &6%1&e。'
    CreateFail: '&c無法建立子區域領地 &6%1&c。'
    Exists: '&c已存在子區域領地 &6%1 &c。'
    Collide: '&c子區域領地與 &6%1 &c衝突。'
    MaxAmount: '&c你已達到子區域領地允許的最大分區數量。'
    MaxDepth: '&c你已達到子區域領地允許的最大分區深度。'
    SelectInside: '&e兩個選擇點必須都在同領地內。'
    CantCreate: '&c你沒有建立子區域領地的權限。'
    CantDelete: '&c你沒有移除子區域領地的權限。'
    CantDeleteNotOwnerOfParent: '&c你不是此子區域所在領地的擁有者，無法移除子區域。'
    CantContract: '&c你沒有縮小子區域領地的權限。'
    CantExpand: '&c你沒有擴大子區域領地的權限。'
    DeleteConfirm: '&e如果你確定要移除子區域 &6%1&e，使用 &6/res confirm &e來確定操作。'
    OwnerChange: '&e已將子區域 &6%1 &e的擁有者更改為 &6%2&e。'
  Residence:
    DontOwn: '&e沒有可供顯示的內容。'
    Hidden: ' &e（&6隱藏&e）'
    Bought: '&e你已購買領地 &6%1&e。'
    Buy: '&6%1 &e購買了你的 &6%2 &e領地。'
    BuyTooBig: '&c你無法購買尺寸這麼大的領地。'
    NotForSale: '&c此領地目前並未出售。'
    ForSale: '&e領地 &6%1 &e現在正以 &6%2 &e的價格出售。'
    StopSelling: '&c此領地已不再出售。'
    TooMany: '&c你擁有的領地數量達到了上限。'
    MaxRent: '&c你租用的領地數量達到了上限。'
    AlreadyRent: '&c此領地已被出租……'
    NotForRent: '&c此領地沒有出租……'
    NotForRentOrSell: '&c此領地沒有出售或出租……'
    NotRented: '&c此領地還沒被出租。'
    Unrent: '&e領地 &6%1 &e已取消出租。'
    RemoveRentable: '&e領地 &6%1 &e不再可供出租。'
    ForRentSuccess: '&e領地 &6%1 &e已被設定為每 &6%3 &e天以 &6%2 &e出租。'
    RentSuccess: '&e你租用了領地 &6%1 &6%2 &e天。'
    EndingRent: '&e出租給 &6%1 &e的領地 &6%2 &e已到期。'
    AlreadyRented: '&e領地 &6%1 &e已出租給 &6%2&e。'
    CantAutoPay: '&e領地不允許自動付款，自動付款將設定為 &6false&e。'
    AlreadyExists: '&c已存在名稱為 &6%1 &c的領地。'
    Create: '&e已建立領地：&6%1&e！'
    Rename: '&e領地 &6%1 &e已被重新命名為 &6%2'
    Remove: '&e領地 &6%1 &e已被移除……'
    CantRemove: '&c領地 &6%1 &c無法移除因為 &6%2 &c子區域還在出租給 &6%3&c。'
    MoveDeny: '&c你沒有領地 &6%1 的移動（move）權限。'
    TeleportNoFlag: '&c你沒有領地的傳送（tp）權限。'
    FlagDeny: '&c你沒有領地 &6%2 &6%1&c 的權限。'
    BaseFlagDeny: '&c你沒有 &6%1 &c的權限。'
    GiveLimits: '&c目標玩家領地超過限制，無法給予領地。'
    GiveConfirm: '&7點擊確認將領地 &6%1 &7從 &6%2 &7轉移到 &6%3&7。'
    Give: '&e你將領地 &6%1 &e轉移到 &6%2&e。'
    Recieve: '&e你從玩家 &6%2 &e收到領地 &6%1&e。'
    ResList: ' &a%1. &e%2 &e- &6%3 %4&6%5'
    TrustedResList: ' &a%1. &f%2 &e- &6%3 %4&6%5'
    List: ' &e%2 &e- &6%3'
    Near: '&e附近的領地：&7%1&e。'
    TeleportNear: '&e已傳送到領地的附近。'
    SetTeleportLocation: '&e設定傳送點位置……'
    PermissionsApply: '&e已設定領地權限。'
    NotOwner: '&c你不是此領地的擁有者。'
    RemovePlayersResidences: '&e移除所有屬於 &6%1 &e的領地。'
    NotIn: '&c你不在領地內。'
    PlayerNotIn: '&c玩家不在領地區域內。'
    Kicked: '&e你在領地中被踢出。'
    CantKick: '&e無法踢出此玩家。'
    In: '&e你目前在領地 &6%1&e。'
    OwnerChange: '&e已將領地 &6%1 &e的擁有者更改成 &6%2&e。'
    NonAdmin: '&c你沒有領地的管理權限。'
    Line: '&e領地：&6%1 '
    RentedBy: '&e租用者：&6%1&e'
    MessageChange: '&e已設定訊息……'
    CantDeleteResidence: '&c你沒有移除領地的權限。'
    CantExpandResidence: '&c你沒有擴展領地的權限。'
    CantContractResidence: '&c你沒有縮小領地的權限。'
    NoResHere: '&c領地不存在。'
    OwnerNoPermission: '&c領地擁有者沒有執行這項操作的權限。'
    ParentNoPermission: '&c你沒有設定父區域的權限。'
    ChatDisabled: '&e已停用領地聊天……'
    DeleteConfirm: '&e如果你確定要移除領地 &6%1&e，使用 &6/res confirm &e來確定操作。'
    ChangedMain: '&e更改主要領地到 &6%1&e。'
    LwcRemoved: '&e在 &6%2 &e毫秒內移除 &6%1 &eLwc 保護。'
    CanBeRented: '&6%1&e 可以被出租給 &6%2 &6%3 &e天，&6/res market rent&e。'
    CanBeBought: '&6%1&e 可以被出售給 &6%2&e，&6/res market buy&e。'
    IsForRent: '&6（出租）'
    IsForSale: '&6（出售）'
    IsRented: '&6（已出租）'
  Rent:
    Disabled: '&c已停用出租……'
    DisableRenew: '&e領地 &6%1 &e不會在租約到期時自動續租。'
    EnableRenew: '&e領地 &6%1 &e將會在租約到期時自動續租。'
    NotByYou: '&c領地不是你租的。'
    isForRent: '&2可供出租的領地。'
    MaxRentDays: '&c你不能一次出租超過 &6%1 &c天。'
    OneTime: '&c無法延長領地的出租時間。'
    Extended: '&e已為領地 &6%2 &e延長租約 &6%1 &e天。'
    Expire: '&e租約到期時間：&6%1&e。'
    AutoPayTurnedOn: '&e已&2啟用&e自動付款。'
    AutoPayTurnedOff: '&e已&c停用&e自動付款。'
    ModifyDeny: '&c無法變更租用的領地。'
    Days: '&e租用天數：&6%1&e。'
    Rented: ' &6（已出租）'
    RentList: ' &6%1&e. &6%2&e（&6%3&e/&6%4&e/&6%5&e）- &6%6 &6%7'
    EvictConfirm: '&e使用 &3/res market confirm &e確認從市場中驅逐領地 &6%1 &e。'
    UnrentConfirm: '&e使用 &3/res market confirm &e確認從市場中取消出租領地 &6%1 &e。'
    ReleaseConfirm: '&e使用 &3/res market confirm &e確認從市場中移除領地 &6%1 &e。'
  command:
    addedAllow: '&e新增領地 &6%1 &e的允許指令。'
    removedAllow: '&e移除領地 &6%1 &e的允許指令。'
    addedBlock: '&e新增領地 &6%1 &e的拒絕指令。'
    removedBlock: '&e移除領地 &6%1 &e的拒絕指令。'
    Blocked: '&e拒絕的指令：&6%1&e。'
    Allowed: '&e允許的指令：&6%1&e。'
    Parsed: '%1'
    PlacehlderList: '&e%1. &6%2'
    PlacehlderResult: ' &e結果：&6%1'
  Rentable:
    Land: '&e出租領地：&6'
    AllowRenewing: '&e可供續租：&6%1&e。'
    StayInMarket: '&e保留出租項目在市場：&6%1&e。'
    AllowAutoPay: '&e允許自動付款：&6%1&e。'
    DisableRenew: '&6%1 &e不會在到期時自動更新租用狀態。'
    EnableRenew: '&6%1 &e將會在到期時自動更新租用狀態。'
  Economy:
    LandForSale: '&e出售領地：'
    NotEnoughMoney: '&c你沒有足夠的金錢。'
    MoneyCharged: '&e已從你的帳戶中支出 &8%1&e。'
    MoneyAdded: '&e你的帳戶增加了 &8%1&e。'
    MoneyCredit: '&e你的帳戶收到了 &8%1&e。'
    RentReleaseInvalid: '&e領地 &6%1 &e沒有被租用或出租。'
    RentSellFail: '&c無法出售正在出租的領地。'
    SubzoneRentSellFail: '&c無法出售，此子區域領地出租中。'
    ParentRentSellFail: '&c無法出售出租中的領地。'
    SubzoneSellFail: '&c無法出售子區域領地。'
    SellRentFail: '&c無法出租正在出售的領地。'
    ParentSellRentFail: '&c無法租用出售中的領地。'
    OwnerBuyFail: '&c不能購買自己的領地。'
    OwnerRentFail: '&c不能租用自己的領地。'
    AlreadySellFail: '&e領地已在出售中。'
    LeaseRenew: '&e租約有效期到 &6%1&e。'
    LeaseRenewMax: '&e允許的最大租約續租。'
    LeaseNotExpire: '&e租約不存在或未過期。'
    LeaseRenewalCost: '&e續租 &6%1 &e的花費為 &6%2。'
    LeaseInfinite: '&e出租時間已設定為無限……'
    MarketDisabled: '&c已停用經濟系統。'
    SellAmount: '&e出售數量：&2%1&e。'
    SellList: ' &6%1&e. &6%2&e（&6%3&e）- &6%4&e'
    LeaseExpire: '&e租約到期時間：&2%1&e。'
    LeaseList: '&6%1. &e%2 &2%3 &e%4'
  Expanding:
    North: '&e向北方擴展 &6%1 &e格方塊。'
    West: '&e向西方擴展 &6%1 &e格方塊。'
    South: '&e向南方擴展 &6%1 &e格方塊。'
    East: '&e向東方擴展 &6%1 &e格方塊。'
    Up: '&e向上方擴展 &6%1 &e格方塊。'
    Down: '&e向下方擴展 &6%1 &e格方塊。'
  Contracting:
    North: '&e向北方縮小 &6%1 &e格方塊。'
    West: '&e向西方縮小 &6%1 &e格方塊。'
    South: '&e向南方縮小 &6%1 &e格方塊。'
    East: '&e向東方縮小 &6%1 &e格方塊。'
    Up: '&e向上方縮小 &6%1 &e格方塊。'
    Down: '&e向下方縮小 &6%1 &e格方塊。'
  Shifting:
    North: '&e向北方平移 &6%1 &e格方塊。'
    West: '&e向西方平移 &6%1 &e格方塊。'
    South: '&e向南方平移 &6%1 &e格方塊。'
    East: '&e向東方平移 &6%1 &e格方塊。'
    Up: '&e向上方平移 &6%1 &e格方塊。'
    Down: '&e向下方平移 &6%1 &e格方塊。'
  Limits:
    PGroup: '&7- &e權限組：&3%1&e'
    RGroup: '&7- &e領地組：&3%1&e'
    Admin: '&7- &e領地管理員：&3%1&e'
    CanCreate: '&7- &e允許建立領地：&3%1&e'
    MaxRes: '&7- &e最大領地數量：&3%1&e'
    MaxEW: '&7- &e東西方向最大：&3%1&e'
    MaxNS: '&7- &e南北方向最大：&3%1&e'
    MaxUD: '&7- &e上下方向最大：&3%1&e'
    MinMax: '&7- &e最小 / 最大高度：&3%1 到 %2&e'
    MaxSubzones: '&7- &e最大子區域：&3%1&e'
    MaxSubDepth: '&7- &e最大子區域深度：&3%1&e'
    MaxRents: '&7- &e最多租用：&3%1&e'
    MaxRentDays: ' &e最多租用天數：&3%1&e'
    EnterLeave: '&7- &e可設定加入離開訊息：&3%1&e'
    NumberOwn: '&7- &e擁有的領地數量：&3%1&e'
    Cost: '&7- &e領地每方塊花費：&3%1&e'
    Sell: '&7- &e領地出售每方塊花費：&3%1&e'
    Flag: '&7- &e領地權限：&3%1&e'
    MaxDays: '&7- &e最大租約時間：&3%1&e'
    LeaseTime: '&7- &e租約續租時間：&3%1&e'
    RenewCost: '&7- &e續租每方塊花費：&3%1&e'
  Gui:
    Set:
      Title: '&6%1 權限'
    Pset:
      Title: '&6%1 %2 權限'
    Actions:
    - '&2點擊左鍵變更為啟用'
    - '&c點擊右鍵變更為停用'
    - '&eShift + 點擊左鍵變更為未設定'
  InformationPage:
    Top: '&e___/ &a %1 - %2 &e \___'
    TopSingle: '&e___/ &a %1 &e \___'
    Page: '&e-----< &6%1 &e>-----'
    NextPage2: '&e-----< &6%1 &e>-----'
    NoNextPage: '&e-----------------------'
    GeneralList: '&2 %1 &6- &e%2'
    FlagsList: '&2 %1 &6- &e%2'
    SmallSeparator: '&6------'
  Chat:
    ChatChannelChange: '&e切換到 &6%1 &e領地聊天！'
    ChatChannelLeave: '&e離開領地聊天。'
    ChatMessage: '%1 %2%3: %4%5'
    ChatListeningMessage: '&2[正在傾聽 %6]%1 %2%3: %4%5'
    JoinFirst: '&4請先加入領地聊天頻道……'
    InvalidChannel: '&4無效的頻道……'
    InvalidColor: '&4錯誤的顏色代碼。'
    NotInChannel: '&4玩家不在頻道中。'
    Kicked: '&6%1 &e已被從頻道 &6%2 &e踢出。'
    InvalidPrefixLength: '&4前綴過長，允許的長度：%1&4。'
    ChangedColor: '&e領地聊天頻道顏色更改成 %1&e。'
    ChangedPrefix: '&e領地聊天頻道前綴更改成 %1&e。'
  Shop:
    ListTopLine: '&6%1 &e商店列表 - 第 &6%2 &e頁，共 &6%3 %4。'
    List: ' &e%1. &6%2&e（&6%3&e）%4'
    ListVoted: '&e%1（&6%2&e）'
    ListLiked: '&7讚：&7%1'
    VotesTopLine: '&6%1 &e%2 投票列表 &6- &e第 &6%3 &e頁，共 &6%4 %5'
    VotesList: ' &e%1. &6%2 &e%3 &7%4'
    NoDesc: '&6沒有描述'
    Desc: |-
      &6描述：
      %1
    DescChange: '&6描述已更改為：%1&6。'
    ChantChange: '&4無法更改商店權限為 true（t）。'
    NewBoard: '&6成功新增商店宣傳面板。'
    BoardExist: '&c此位置已存在商店宣傳面板。'
    DeleteBoard: '&6點擊右鍵移除商店宣傳面板。'
    DeletedBoard: '&6已移除商店宣傳面板。'
    IncorrectBoard: '&c這不是商店宣傳面板，請重新點擊正確的商店宣傳面板。'
    InvalidSelection: '&c使用選擇工具，先點擊左上角的告示牌，再點擊右下角的告示牌。'
    ToBigSelection: '&c選擇的範圍太大，最多允許 16 個方塊。'
    ToDeapSelection: '&c選擇的範圍太高，最大允許為 16x16x1 的範圍。'
    VoteChanged: '&6領地 &e%3 &6的投票由 &e%1 &6更改為 &e%2&6。'
    Voted: '&6你給 &e%2 &6評了 &e%1 &6分。'
    Liked: '&6你對領地 &e%1 &6按讚。'
    AlreadyLiked: '&6你已對領地 &e%1 &6按讚過了。'
    NoVotes: '&c此領地還沒有投票。'
    CantVote: '&c此領地還沒啟用商店權限。'
    VotedRange: '&6只能評出 &e%1 &6分到 &e%2 &6分。'
    SignLines:
      '1': '&e--== &8%1 &e==--'
      '2': '&9%1'
      '3': '&4%1'
      '4': '&8%1&e（&8%2&e）'
      Likes4: '&9讚：&8%2'
  RandomTeleport:
    TpLimit: '&e傳送過於頻繁，請稍候 &6%1 &e秒後再次傳送。'
    TeleportSuccess: '&e已傳送到座標 X:&6%1 &eY:&6%2 &eZ:&6%3&e。'
    IncorrectLocation: '&e找不到正確的傳送座標，請稍候 &6%1 &e秒後再試。'
    Disabled: '&c此世界停用隨機傳送。'
    TeleportStarted: '&e傳送將在 &6%4 &e秒後開始，不要移動。'
    WorldList: '&e可供使用的世界：&6%1&e。'
  Permissions:
    variableColor: '&f'
    permissionColor: '&6'
    cmdPermissionColor: '&2'
  General:
    DisabledWorld: '&c此世界停用領地。'
    CantCreate: '&c無法在此世界建立領地。'
    UseNumbers: '&c請使用數字……'
    # 將所有文字替換為 '' 以停用此訊息
    CantPlaceLava: '&c無法在沒有領地的地方放置岩漿。'
    # 將所有文字替換為 '' 以停用此訊息
    CantPlaceWater: '&c無法在沒有領地的地方放置水。'
    CantPlaceChest: '&c你沒有放置儲物箱的權限。'
    NoPermission: '&c你沒有執行此操作的權限。'
    info:
      NoPlayerPermission: '&c[playerName] 沒有 [permission] 權限。'
    NoCmdPermission: '&c沒有此指令的權限。'
    DefaultUsage: '&e使用 &6/%1 ? &e查看説明。'
    MaterialGet: '&eID &6%1 &e的物品名稱為 &6%2'
    MarketList: '&e---- &6市場列表 &e----'
    Separator: '&e------------------------------------------------'
    AdminOnly: '&c只有管理員才能執行此指令。'
    InfoTool: '&e- 資訊工具：&6%1'
    ListMaterialAdd: '&6%1 &e已被新增到領地 &6%2&e。'
    ListMaterialRemove: '&6%1 &e已從領地 &6%2 &e移除。'
    ItemBlacklisted: '&c此處禁止使用此物品。'
    WorldPVPDisabled: '&c已停用世界 PVP。'
    NoPVPZone: '&c非 PVP 區域。'
    NoFriendlyFire: '&c非友軍傷害區域。'
    InvalidHelp: '&c無效說明頁數。'
    TeleportDeny: '&c沒有傳送的權限。'
    TeleportSuccess: '&e已傳送。'
    TeleportConfirmLava: '&c傳送不安全，你將會掉進&6岩漿&c，若仍要傳送請使用 &6/res tpconfirm &c來確定操作。'
    TeleportConfirmVoid: '&c傳送不安全，你將會掉進&6虛空&c，若仍要傳送請使用 &6/res tpconfirm &c來確定操作。'
    TeleportConfirm: '&c傳送不安全，你將會掉落 &6%1 &c格高的方塊，若仍要傳送請使用 &6/res tpconfirm &c來確定操作。'
    TeleportStarted: '&e已開始傳送到 &6%1 &e，在 &6%2 &e秒內請勿移動。'
    TeleportTitle: '&e傳送中！'
    TeleportTitleTime: '&6%1'
    TeleportCanceled: '&e已取消傳送！'
    NoTeleportConfirm: '&e沒有需要確認的傳送！'
    HelpPageHeader2: '&e說明頁面 - &6%1 &e- 頁 <&6%2 &eof &6%3&e>'
    ListExists: '&c已存在列表……'
    ListRemoved: '&e已移除列表……'
    ListCreate: '&e已建立列表 &6%1&e。'
    PhysicalAreas: '&e區域'
    CurrentArea: '&e目前區域：&6%1'
    TotalResSize: '&e總計尺寸：&6%1 立方格&e（&6%2 平方格&e）'
    ResSize:
      eastWest: '&e東 / 西：&6%1 格'
      northSouth: '&e北 / 南：&6%1 格'
      upDown: '&e上 / 下：&6%1 格'
    TotalWorth: '&e總計價值：&6立方格價為 %1&e（&6平方格價為 %2&e）'
    TotalSubzones: '&e子區域領地：&6%1&e（&6%2&e）'
    NotOnline: '&e目標玩家一定要在線上。'
    GenericPages: '&e第 &6%1 &e頁（共 &6%2 &e頁）（&6%3&e 頁）'
    WorldEditNotFound: '&c未偵測到 WorldEdit。'
    CoordsTop: '&eX:&6%1 &eY:&6%2 &eZ:&6%3'
    CoordsBottom: '&eX:&6%1 &eY:&6%2 &eZ:&6%3'
    CoordsLiner: '&7（&3%1&7；%2&7）'
    AllowedTeleportIcon: '&2T'
    BlockedTeleportIcon: '&7T'
    AllowedMovementIcon: '&2M'
    BlockedMovementIcon: '&7M'
    AdminToggleTurnOn: '&6已啟用&e自動管理模式'
    AdminToggleTurnOff: '&6已停用&e自動管理模式'
    NoSpawn: '&e你沒有重生點 &6移動（move） &e的權限，正在重新定位。'
    CompassTargetReset: '&e已重設你的羅盤。'
    CompassTargetSet: '&e你的羅盤目前指向 &6%1&e。'
    Ignorelist: '&2忽略名單：&6'
    Blacklist: '&c黑名單：&6'
    LandCost: '&e領地總計花費：&6%1'
    'True': '&2啟用'
    'False': '&c停用'
    Removed: '&6未設定'
    FlagState: '&e權限狀態：%1'
    Land: '&e領地：&6%1'
    Cost: '&e花費：每 &6%2 &e天 &6%1'
    Status: '&e狀態：%1'
    Available: '&2可供使用'
    Size: ' &e尺寸：&6%1'
    ResidenceFlags: '&e領地權限：&6%1'
    PlayersFlags: '&e玩家權限：&6%1'
    GroupFlags: '&e組權限：&6%1'
    OthersFlags: '&e其他人權限：&6%1'
    Moved: '&e已移動……'
    Name: '&e名稱：&6%1'
    Lists: '&e列表：&6'
    Residences: '&e領地&6'
    CreatedOn: '&e建立於：&6%1'
    Owner: '&e擁有者：&6%1'
    World: '&e世界：&6%1'
    Subzones: '&e子區域'
    # 以下幾行代表 Residence 傳送給玩家的各種訊息。
    # 請注意，某些訊息具有在執行時插入的變數，例如 %1。
    NewPlayerInfo: '&e如果要建立領地，請使用伺服器指定的工具選擇領地的兩個對角，然後使用指令 &2/res create [領地名稱]&e。'
CommandHelp:
  Description: 內涵領地說明
  SubCommands:
    res:
      Description: 領地的主要指令
      Info:
      - '&2使用 &6/res [指令] ? <頁數> &2查看更多資訊。'
      SubCommands:
        auto:
          Info:
          - '&e用法：&6/res auto （領地名稱） （半徑）'
          Description: 在你周圍建立最大允許範圍的領地
        select:
          Info:
          - 此指令用於選擇領地的範圍。
          - /res select [x] [y] [z] - 選擇以你為中心一定範圍的方塊。
          Description: 選擇指令
          SubCommands:
            coords:
              Description: 顯示已選擇區域的座標
              Info:
              - '&e用法：&6/res select coords'
            size:
              Description: 顯示已選擇區域的大小
              Info:
              - '&e用法：&6/res select size'
            auto:
              Description: 啟用自動選擇工具
              Info:
              - '&e用法：&6/res select auto [玩家名稱]'
            cost:
              Description: 顯示已選擇區域的價格
              Info:
              - '&e用法：&6/res select cost'
            vert:
              Description: 垂直擴展選擇區域
              Info:
              - '&e用法：&6/res select vert'
              - 將選擇區域擴展頂部和底部。
            sky:
              Description: 擴展選擇區域到天空
              Info:
              - '&e用法：&6/res select sky'
              - 將選擇區域擴展到頂部。
            bedrock:
              Description: 擴展選擇區域到基岩
              Info:
              - '&e用法：&6/res select bedrock'
              - 將選擇區域擴展到底部。
            expand:
              Description: 向面對的方向擴展選擇區域
              Info:
              - '&e用法：&6/res select expand <數量>'
              - 向面對的方向擴展 <數量> 格方塊。
            shift:
              Description: 向面對的方向平移選擇區域
              Info:
              - '&e用法：&6/res select shift <數量>'
              - 向面對的方向平移 <數量> 格方塊。
            chunk:
              Description: 選擇你目前所在的區塊
              Info:
              - '&e用法：&6/res select chunk'
              - 選擇你目前所在的區塊。
            residence:
              Description: 選擇領地中存在的區域
              Info:
              - '&e用法：&6/res select residence <領地名稱>'
              - 選擇領地中存在的區域。
            worldedit:
              Description: 使用 WorldEdit 選擇區域
              Info:
              - '&e用法：&6/res select worldedit'
              - 使用 WorldEdit 選擇區域。
        padd:
          Info:
          - '&e用法：&6/res padd <領地名稱> [玩家名稱]'
          - 新增玩家的基本權限。
          Description: 新增玩家到領地
        placeholders:
          Info:
          - '&e用法：&6/res placeholders （parse） （placeholder） （玩家名稱）'
          Description: 列出 Placeholder 列表
          parse: '[result]'
        signconvert:
          Info:
          - '&e用法：&6/res signconvert'
          - 嘗試從第三方插件轉換儲存告示牌資料。
          Description: 轉換 ResidenceSign 插件的告示牌
        listallhidden:
          Info:
          - '&e用法：&6/res listhidden <頁數>'
          - 顯示伺服器所有隱藏的領地。
          Description: 顯示所有隱藏的領地
        bank:
          Info:
          - '&e用法：&6/res bank [deposit/withdraw] <領地名稱> [金額]'
          - 你必須站在領地中或指定領地名稱，
          - 且擁有銀行（bank）權限。
          Description: 管理領地銀行
        create:
          Info:
          - '&e用法：&6/res create [領地名稱]'
          Description: 建立領地
        listall:
          Info:
          - '&e用法：&6/res listall <頁數> <世界名稱> <-a/-f>'
          - 列出所有的領地
          Description: 列出所有的領地
        info:
          Info:
          - '&e用法：&6/res info <領地名稱>'
          - 留空 <領地名稱> 會顯示你目前所在的領地資訊。
          Description: 顯示領地的資訊
        area:
          Description: 管理領地區域
          Info:
          - ''
          SubCommands:
            list:
              Description: 列出領地區域
              Info:
              - '&e用法：&6/res area list [領地名稱] <頁數>'
            listall:
              Description: 列出所有區域的座標和詳細資訊
              Info:
              - '&e用法：&6/res area listall [領地名稱] <頁數>'
            add:
              Description: 為領地新增區域
              Info:
              - '&e用法：&6/res area add [領地名稱] [區域 ID]'
              - 必須先選中兩個選擇點。
            remove:
              Description: 移除領地區域
              Info:
              - '&e用法：&6/res area remove [領地名稱] [區域 ID]'
            replace:
              Description: 替換領地區域
              Info:
              - '&e用法：&6/res area replace [領地名稱] [區域 ID]'
              - 必須先選中兩個選擇點。
              - 如果新的區域更大，二者之間將會被填充。
        give:
          Info:
          - '&e用法：&6/res give <領地名稱> [玩家名稱] <-s>'
          - 將你的領地轉移給目標玩家。
          Description: 給予其他玩家領地
        renamearea:
          Info:
          - '&e用法：&6/res removeworld [領地名稱] [舊區域名稱] [新區域名稱]'
          Description: 重新命名區域
        contract:
          Info:
          - '&e用法：&6/res contract （領地名稱） [縮小單位]'
          - 向你面對的方向縮小領地，
          - 領地名稱是可選的。
          Description: 向你面對的方向縮小領地
        gset:
          Info:
          - '&e用法：&6/res gset <領地名稱> [群組] [權限] [true/false/remove]'
          - 使用 /res flags ? 查看權限列表。
          Description: 為某個組設定領地的特定權限
        check:
          Info:
          - '&e用法：&6/res check [領地名稱] [權限] （玩家名稱）'
          Description: 為你檢查權限狀態
        list:
          Info:
          - '&e用法：&6/res list <玩家名稱> <頁數> <世界名稱>'
          - 列出指定玩家擁有的所有領地（除了隱藏領地）。
          - 如果顯示自己的領地，隱藏領地也會被顯示。
          - 如果要列出所有玩家的領地，請使用指令 /res listall。
          Description: 顯示領地列表
        version:
          Info:
          - '&e用法：&6/res version'
          Description: 顯示領地插件版本
        tool:
          Info:
          - '&e用法：&6/res tool'
          Description: 顯示領地選擇工具和領地資訊工具名稱
        pdel:
          Info:
          - '&e用法：&6/res pdel <領地名稱> [玩家名稱]'
          - 移除玩家的基本權限。
          Description: 從領地移除玩家
        market:
          Info:
          - '&e用法：&6/res market ? &e查看更多資訊。'
          Description: 購買、出售、租用領地
          SubCommands:
            Info:
              Description: 查看領地的經濟資訊
              Info:
              - '&e用法：&6/res market Info [領地名稱]'
              - 顯示領地是否正在出租、出售以及領地的花費。
            list:
              Description: 顯示可出租與可出售的領地
              Info:
              - '&e用法：&6/res market list [rent/sell]'
              SubCommands:
                rent:
                  Description: 列出可以被出租的領地
                  Info:
                  - '&e用法：&6/res market list rent'
                sell:
                  Description: 列出可以被出售的領地
                  Info:
                  - '&e用法：&6/res market list sell'
            sell:
              Description: 出售領地
              Info:
              - '&e用法：&6/res market sell [領地名稱] [金額]'
              - 將領地以 [金額] 的價格出售。
              - 其他玩家可以使用 /res market buy 購買。
            sign:
              Description: 設定市場告示牌
              Info:
              - '&e用法：&6/res market sign [領地名稱]'
              - 將看向的告示牌設定為領地市場告示牌。
            buy:
              Description: 購買領地
              Info:
              - '&e用法：&6/res market buy [領地名稱]'
              - 如果此領地正在出售，則購買此領地。
            unsell:
              Description: 取消出售領地
              Info:
              - '&e用法：&6/res market unsell [領地名稱]'
            rent:
              Description: 租用領地
              Info:
              - '&e用法：&6/res market rent [領地名稱] <自動續租付款>'
              - 租用領地。自動續租可設定為 true 或 false，如果是 true 並且獲得領地擁有者允許，領地將會在租約到期之前自動續租。
            rentable:
              Description: 出租領地
              Info:
              - '&e用法：&6/res market rentable [領地名稱] [花費] [天數] <AllowRenewing> <StayInMarket>
                <AllowAutoPay>'
              - 使領地出租以 [天數] 來計算 [花費]
              - 如果啟用 <AllowRenewing>，則在租約到期前可再次出租。
              - 如果啟用 <StayInMarket>，則在租約到期後留在市場上。
              - 如果啟用 <AllowAutoPay>，則出租到期時直接自動續租付款。
            autopay:
              Description: 設定領地自動續租付款
              Info:
              - '&e用法：&6/res market autopay [領地名稱] [true/false]'
            payrent:
              Description: 支付定額領地的租金
              Info:
              - '&e用法：&6/res market payrent [領地名稱]'
            confirm:
              Description: 確認領地 unrent/release 行動
              Info:
              - '&e用法：&6/res market confirm'
            unrent:
              Description: 取消領地租用或出租狀態
              Info:
              - '&e用法：&6/res market unrent [領地名稱]'
              - 如果你是租用者，此指令將會退租領地。
              - 如果你是擁有者，此指令將會收回領地。
        rc:
          Info:
          - '&e用法：&6/res rc （領地名稱）'
          - 加入領地聊天頻道。
          Description: 加入目前或指定的領地聊天頻道
          SubCommands:
            leave:
              Description: 離開目前的領地聊天頻道
              Info:
              - '&e用法：&6/res rc leave'
              - 如果你在領地聊天頻道內，那你將離開領地聊天頻道。
            setcolor:
              Description: 設定領地聊天頻道文字顏色
              Info:
              - '&e用法：&6/res rc setcolor [顏色代碼]'
              - 設定領地聊天頻道文字顏色。
            setprefix:
              Description: 設定領地聊天頻道前綴
              Info:
              - '&e用法：&6/res rc setprefix [新前綴]'
              - 設定領地聊天頻道前綴。
            kick:
              Description: 從領地聊天頻道中踢出玩家
              Info:
              - '&e用法：&6/res rc kick [玩家名稱]'
              - 從領地聊天頻道中踢出玩家。
        expand:
          Info:
          - '&e用法：&6/res expand （領地名稱） [擴展單位]'
          - 向你面對的方向擴展領地規模，
          - 領地名稱是可選的。
          Description: 向你面對的方向擴展領地
        compass:
          Info:
          - '&e用法：&6/res compass <領地名稱>'
          Description: 設定指向領地的羅盤
        lists:
          Info:
          - 預先定義的權限列表可以被應用到領地上。
          Description: 預先指定權限列表
          SubCommands:
            add:
              Description: 新增列表
              Info:
              - '&e用法：&6/res lists add <列表名稱>'
            remove:
              Description: 移除列表
              Info:
              - '&e用法：&6/res lists remove <列表名稱>'
            apply:
              Description: 應用領地列表
              Info:
              - '&e用法：&6/res lists apply <列表名稱> <領地名稱>'
            set:
              Description: 設定權限
              Info:
              - '&e用法：&6/res lists set <列表名稱> <權限> [值]'
            pset:
              Description: 設定玩家權限
              Info:
              - '&e用法：&6/res lists pset <列表名稱> <玩家名稱> <權限> [值]'
            gset:
              Description: 設定組權限
              Info:
              - '&e用法：&6/res lists gset <列表名稱> <玩家名稱> <權限> [值]'
            view:
              Description: 查看列表
              Info:
              - '&e用法：&6/res lists view <列表名稱>'
        reset:
          Info:
          - '&e用法：&6/res reset <領地名稱/all>'
          - 將領地的所有權限重設。
          Description: 將領地設定還原為預設值
        listhidden:
          Info:
          - '&e用法：&6/res listhidden <玩家名稱> <頁數>'
          - 列出玩家擁有的隱藏領地。
          Description: 列出隱藏領地
        raid:
          Info:
          - '&e用法：&6/res raid start [領地名稱] （玩家名稱）'
          - '&6/res raid stop [領地名稱]'
          - '&6/res raid kick [玩家名稱]'
          - '&6/res raid immunity [add/take/set/clear] [領地名稱/目前所在領地] [時間]'
          Description: 管理領地侵略
        setmain:
          Info:
          - '&e用法：&6/res setmain （領地名稱）'
          - 將指定的領地設為 main。
          Description: 將定義的領地名稱作為前綴顯示在聊天中
        server:
          Info:
          - '&e用法：&6/resadmin server [領地名稱]'
          - 建立伺服器的領地。
          Description: 建立伺服器領地
        rt:
          Info:
          - '&e用法：&6/res rt （世界名稱） （玩家名稱）'
          - 傳送到指定世界的隨機位置。
          Description: 傳送到世界的隨機位置
        mirror:
          Info:
          - '&e用法：&6/res mirror [原領地名稱] [目標領地名稱]'
          - 將權限從此領地鏡像到另一個領地，你必須是兩者的所有者或管理員才能執行此操作。
          Description: 鏡像權限
        shop:
          Info:
          - 管理領地的商店功能。
          Description: 管理領地商店
          SubCommands:
            list:
              Description: 顯示領地商店列表
              Info:
              - '&e用法：&6/res shop list'
              - 顯示所有作為商店的領地。
            vote:
              Description: 為領地商店投票
              Info:
              - '&e用法：&6/res shop vote <領地名稱> [票數]'
              - 為目前或指定領地商店投票。
            like:
              Description: 為領地商店按讚
              Info:
              - '&e用法：&6/res shop like <領地名稱>'
              - 為領地商店按讚。
            votes:
              Description: 顯示領地商店投票
              Info:
              - '&e用法：&6/res shop votes <領地名稱> <頁數>'
              - 顯示目前或指定領地商店的投票列表。
            likes:
              Description: 顯示領地商店的按讚
              Info:
              - '&e用法：&6/res shop likes <領地名稱> <頁數>'
              - 顯示目前或指定的領地商店按讚列表。
            setdesc:
              Description: 設定領地商店描述
              Info:
              - '&e用法：&6/res shop setdesc [文字]'
              - 設定領地商店描述，並且支援顏色代碼，用 /n 表示換行。
            createboard:
              Description: 建立商店宣傳面板
              Info:
              - '&e用法：&6/res shop createboard [位置]'
              - 在選擇區域建立商店宣傳面板，放置位置 - 即宣傳面板的起始位置。
            deleteboard:
              Description: 移除商店宣傳面板
              Info:
              - '&e用法：&6/res shop deleteboard'
              - 對宣傳面板的告示牌點擊右鍵以移除宣傳面板。
        lset:
          Info:
          - '&e用法：&6/res lset <領地名稱> [blacklist/ignorelist] [物品 ID]'
          - '&e用法：&6/res lset <領地名稱> Info'
          - 將物品加入黑名單，物品將被阻止放置在領地中，
          - 將物品加入忽略名單，物品將不會被領地所保護。
          Description: 更改黑名單和忽略名單選項
        pset:
          Info:
          - '&e用法：&6/res pset <領地名稱> [玩家名稱] [權限] [true/false/remove]'
          - '&e用法：&6/res pset <領地名稱> [玩家名稱] removeall'
          - 使用 /res flags ? 查看權限列表。
          Description: 為玩家設定領地的特定權限
        raidstatus:
          Info:
          - '&e用法：&6/res raidstatus （領地名稱/玩家名稱）。'
          Description: 查看領地的侵略狀態
        show:
          Info:
          - '&e用法：&6/res show <領地名稱>。'
          Description: 顯示領地邊界
        flags:
          Info:
          - 設定領地權限時，true 的意思為允許操作，false 的意思為禁止操作。
          Description: 權限列表
          SubCommands:
            anvil:
              Translated: 使用鐵砧（anvil）
              Description: 允許或禁止使用鐵砧
              Info:
              - '&e用法：&6/res set/pset <領地名稱> anvil true/false/remove'
            admin:
              Translated: 管理權（admin）
              Description: 給予玩家管理員權限更改領地的權限
              Info:
              - '&e用法：&6/res pset <領地名稱> admin true/false/remove'
            animalkilling:
              Translated: 擊殺動物（animalkilling）
              Description: 允許或禁止擊殺動物
              Info:
              - '&e用法：&6/res set/pset <領地名稱> animalkilling true/false/remove'
            animals:
              Translated: 生成動物（animals）
              Description: 允許或禁止生成動物
              Info:
              - '&e用法：&6/res set <領地名稱> animals true/false/remove'
            anchor:
              Translated: 使用重生錨（anchor）
              Description: 允許或禁止使用重生錨
              Info:
              - '&e用法：&6/res set/pset <領地名稱> anchor true/false/remove'
            anvilbreak:
              Translated: 鐵砧損壞（anvilbreak）
              Description: 允許或禁止領地內鐵砧損壞
              Info:
              - '&e用法：&6/res set <領地名稱> anvilbreak true/false/remove'
            safezone:
              Translated: 安全區域（safezone）
              Description: 設定為 true 會使領地清除成員負面效果
              Info:
              - '&e用法：&6/res set <領地名稱> safezone true/false/remove'
            backup:
              Translated: 備份與還原（backup）
              Description: 如果設定為 true，則回復回原本的領地樣貌（需要 WordEdit）
              Info:
              - '&e用法：&6/res set <領地名稱> backup true/false/remove'
            bank:
              Translated: 使用銀行（bank）
              Description: 允許或禁止從領地銀行存款/取款
              Info:
              - '&e用法：&6/res set/pset <領地名稱> bank true/false/remove'
            bed:
              Translated: 使用床（bed）
              Description: 允許或禁止玩家使用床
              Info:
              - '&e用法：&6/res set/pset <領地名稱> bed true/false/remove'
            honey:
              Translated: 摘取蜂蜜（honey）
              Description: 允許或禁止玩家摘取蜂蜜
              Info:
              - '&e用法：&6/res set/pset <領地名稱> honey true/false/remove'
            honeycomb:
              Translated: 摘取蜂巢（honeycomb）
              Description: 允許或禁止玩家摘取蜂巢
              Info:
              - '&e用法：&6/res set/pset <領地名稱> honeycomb true/false/remove'
            beacon:
              Translated: 使用烽火台（beacon）
              Description: 允許或禁止使用烽火台
              Info:
              - '&e用法：&6/res set/pset <領地名稱> beacon true/false/remove'
            brew:
              Translated: 使用釀造台（brew）
              Description: 允許或禁止玩家使用釀造台
              Info:
              - '&e用法：&6/res set/pset <領地名稱> brew true/false/remove'
            build:
              Translated: 建造（build）
              Description: 允許或禁止建造
              Info:
              - '&e用法：&6/res set/pset <領地名稱> build true/false/remove'
            burn:
              Translated: 生物燃燒（burn）
              Description: 允許或禁止生物在領地內燃燒
              Info:
              - '&e用法：&6/res set <領地名稱> burn true/false/remove'
            button:
              Translated: 使用按鈕（button）
              Description: 允許或禁止玩家使用按鈕
              Info:
              - '&e用法：&6/res set/pset <領地名稱> button true/false/remove'
            brush:
              Translated: 使用刷子（brush）
              Description: 允許或禁止刷掃方塊
              Info:
              - '&e用法：&6/res set/pset <領地名稱> brush true/false/remove'
            cake:
              Translated: 吃蛋糕（cake）
              Description: 允許或禁止玩家吃蛋糕
              Info:
              - '&e用法：&6/res set/pset <領地名稱> cake true/false/remove'
            canimals:
              Translated: 生成自定義動物（canimals）
              Description: 允許或禁止自訂義動物生成
              Info:
              - '&e用法：&6/res set <領地名稱> canimals true/false/remove'
            chorustp:
              Translated: 吃歌萊果傳送（chorustp）
              Description: 允許或禁止吃歌萊果進行傳送
              Info:
              - '&e用法：&6/res set/pset <領地名稱> chorustp true/false/remove'
            chat:
              Translated: 聊天（chat）
              Description: 允許或禁止玩家加入領地聊天頻道
              Info:
              - '&e用法：&6/res set/pset <領地名稱> chat true/false/remove'
            cmonsters:
              Translated: 生成自訂義怪物（cmonsters）
              Description: 允許或禁止自訂義怪物生成
              Info:
              - '&e用法：&6/res set <領地名稱> cmonsters true/false/remove'
            commandblock:
              Translated: 使用指令方塊（commandblock）
              Description: 允許或禁止使用指令方塊
              Info:
              - '&e用法：&6/res set/pset <領地名稱> commandblock true/false/remove'
            command:
              Translated: 使用指令（command）
              Description: 允許或禁止在領地內使用指令
              Info:
              - '&e用法：&6/res set/pset <領地名稱> command true/false/remove'
            container:
              Translated: 使用容器（container）
              Description: 允許或禁止使用熔爐、儲物箱、發射器……
              Info:
              - '&e用法：&6/res set/pset <領地名稱> container true/false/remove'
            coords:
              Translated: 顯示座標（coords）
              Description: 顯示領地座標
              Info:
              - '&e用法：&6/res set <領地名稱> coords true/false/remove'
            copper:
              Translated: 變更銅方塊（copper）
              Description: 允許變更銅方塊
              Info:
              - '&e用法：&6/res set/pset <領地名稱> copper true/false/remove'
            craft:
              Translated: 使用工藝界面（craft）
              Description: 給予工作台、附魔台、釀造台等的權限
              Info:
              - '&e用法：&6/res set <領地名稱> craft true/false/remove'
            creeper:
              Translated: 苦力怕爆炸（creeper）
              Description: 允許或禁止苦力怕爆炸
              Info:
              - '&e用法：&6/res set <領地名稱> creeper true/false/remove'
            dragongrief:
              Translated: 終界龍破壞（dragongrief）
              Description: 允許或禁止終界龍破壞任何方塊
              Info:
              - '&e用法：&6/res set <領地名稱> dragongrief true/false/remove'
            day:
              Translated: 白天（day）
              Description: 設定領地時間為白天
              Info:
              - '&e用法：&6/res set <領地名稱> day true/false/remove'
            dye:
              Translated: 羊染色（dye）
              Description: 允許或禁止羊染色
              Info:
              - '&e用法：&6/res set/pset <領地名稱> dye true/false/remove'
            damage:
              Translated: 實體傷害（damage）
              Description: 允許或禁止所有領地實體傷害
              Info:
              - '&e用法：&6/res set <領地名稱> damage true/false/remove'
            decay:
              Translated: 樹葉腐爛（decay）
              Description: 允許或禁止領地內樹葉腐爛
              Info:
              - '&e用法：&6/res set <領地名稱> decay true/false/remove'
            destroy:
              Translated: 破壞（destroy）
              Description: 允許或禁止破壞方塊（將覆蓋 build 權限）
              Info:
              - '&e用法：&6/res set/pset <領地名稱> destroy true/false/remove'
            dryup:
              Translated: 耕地乾涸（dryup）
              Description: 允許或禁止耕地乾涸
              Info:
              - '&e用法：&6/res set <領地名稱> dryup true/false/remove'
            diode:
              Translated: 使用紅石中繼器（diode）
              Description: 允許或禁止玩家使用紅石中繼器
              Info:
              - '&e用法：&6/res set/pset <領地名稱> diode true/false/remove'
            door:
              Translated: 使用門（door）
              Description: 允許或禁止玩家使用門或地板門
              Info:
              - '&e用法：&6/res set/pset <領地名稱> door true/false/remove'
            egg:
              Translated: 觸碰傳送龍蛋（egg）
              Description: 允許或禁止玩家觸碰傳送龍蛋
              Info:
              - '&e用法：&6/res set/pset <領地名稱> egg true/false/remove'
            enchant:
              Translated: 使用附魔台（enchant）
              Description: 允許或禁止玩家使用附魔台
              Info:
              - '&e用法：&6/res set/pset <領地名稱> enchant true/false/remove'
            explode:
              Translated: 爆炸（explode）
              Description: 允許或禁止在領地內爆炸
              Info:
              - '&e用法：&6/res set <領地名稱> explode true/false/remove'
            elytra:
              Translated: 使用鞘翅（elytra）
              Description: 允許或禁止在領地內使用鞘翅
              Info:
              - '&e用法：&6/res set/pset <領地名稱> elytra true/false/remove'
            enderpearl:
              Translated: 使用終界珍珠傳送（enderpearl）
              Description: 允許或禁止在領地內使用終界珍珠傳送
              Info:
              - '&e用法：&6/res set/pset <領地名稱> enderpearl true/false/remove'
            fallinprotection:
              Translated: 防止方塊掉落（fallinprotection）
              Description: 防止方塊掉落領地內
              Info:
              - '&e用法：&6/res set <領地名稱> fallinprotection true/false/remove'
            falldamage:
              Translated: 玩家摔落（falldamage）
              Description: 允許或禁止玩家摔落傷害
              Info:
              - '&e用法：&6/res set <領地名稱> falldamage true/false/remove'
            feed:
              Translated: 自動回復飽食度（feed）
              Description: 如果設定為 true，則讓在領地內的玩家自動回復飽食度
              Info:
              - '&e用法：&6/res set <領地名稱> feed true/false/remove'
            friendlyfire:
              Translated: 友軍傷害（friendlyfire）
              Description: 允許或禁止友軍傷害
              Info:
              - '&e用法：&6/res pset <領地名稱> friendlyfire true/false/remove'
            fireball:
              Translated: 使用火焰彈（fireball）
              Description: 允許或禁止在領地內使用火焰彈
              Info:
              - '&e用法：&6/res set <領地名稱> fireball true/false/remove'
            firespread:
              Translated: 火焰蔓延（firespread）
              Description: 允許或禁止火焰蔓延
              Info:
              - '&e用法：&6/res set <領地名稱> firespread true/false/remove'
            flowinprotection:
              Translated: 防止外部液體流動（flowinprotection）
              Description: 允許或禁止液體流入領地
              Info:
              - '&e用法：&6/res set <領地名稱> flowinprotection true/false/remove'
            flow:
              Translated: 內部液體流動（flow）
              Description: 允許或禁止在領地內液體流動
              Info:
              - '&e用法：&6/res set <領地名稱> flow true/false/remove'
            flowerpot:
              Translated: 使用花盆（flowerpot）
              Description: 允許或禁止使用花盆
              Info:
              - '&e用法：&6/res set/pset <領地名稱> flowerpot true/false/remove'
            grow:
              Translated: 植物生長（grow）
              Description: 允許或禁止植物生長
              Info:
              - '&e用法：&6/res set <領地名稱> grow true/false/remove'
            glow:
              Translated: 玩家發光（glow）
              Description: 玩家進入領地後會開始發光
              Info:
              - '&e用法：&6/res set <領地名稱> glow true/false/remove'
            goathorn:
              Translated: 吹奏山羊角（goathorn）
              Description: 允許或禁止使用山羊角
              Info:
              - '&e用法：&6/res set/pset <領地名稱> goathorn true/false/remove'
            harvest:
              Translated: 收割（harvest）
              Description: 允許收割作物
              Info:
              - '&e用法：&6/res set/pset <領地名稱> harvest true/false/remove'
            hotfloor:
              Translated: 岩漿塊傷害（hotfloor）
              Description: 允許或禁止踩在岩漿塊上的傷害
              Info:
              - '&e用法：&6/res set <領地名稱> hotfloor true/false/remove'
            hidden:
              Translated: 隱藏領地列表（hidden）
              Description: 在 list 或 listall 列表中隱藏領地
              Info:
              - '&e用法：&6/res set <領地名稱> hidden true/false/remove'
            hook:
              Translated: 使用釣竿釣鉤實體（hook）
              Description: 允許或禁止使用釣竿釣鉤實體
              Info:
              - '&e用法：&6/res set/pset <領地名稱> hook true/false/remove'
            healing:
              Translated: 自動回復血量（healing）
              Description: 如果設定為 true，則讓在領地內的玩家自動回復血量
              Info:
              - '&e用法：&6/res set <領地名稱> healing true/false/remove'
            iceform:
              Translated: 水結冰（iceform）
              Description: 允許或禁止水結冰
              Info:
              - '&e用法：&6/res set <領地名稱> iceform true/false/remove'
            icemelt:
              Translated: 冰融化（icemelt）
              Description: 允許或禁止冰融化
              Info:
              - '&e用法：&6/res set <領地名稱> icemelt true/false/remove'
            ignite:
              Translated: 使用打火石（ignite）
              Description: 允許或禁止使用打火石
              Info:
              - '&e用法：&6/res set/pset <領地名稱> ignite true/false/remove'
            itemdrop:
              Translated: 物品掉落（itemdrop）
              Description: 允許或禁止物品掉落
              Info:
              - '&e用法：&6/res set/pset <領地名稱> itemdrop true/false/remove'
            itempickup:
              Translated: 物品撿取（itempickup）
              Description: 允許或禁止物品撿取
              Info:
              - '&e用法：&6/res set/pset <領地名稱> itempickup true/false/remove'
            jump2:
              Translated: 兩格高跳躍（jump2）
              Description: 允許或禁止跳躍兩格高
              Info:
              - '&e用法：&6/res set <領地名稱> jump2 true/false/remove'
            jump3:
              Translated: 三格高跳躍（jump3）
              Description: 允許或禁止跳躍三格高
              Info:
              - '&e用法：&6/res set <領地名稱> jump3 true/false/remove'
            keepinv:
              Translated: 防噴裝（keepinv）
              Description: 允許或禁止在玩家死後保持物品欄狀態
              Info:
              - '&e用法：&6/res set <領地名稱> keepinv true/false/remove'
            keepexp:
              Translated: 防噴經驗（keepexp）
              Description: 允許或禁止在玩家死後保持經驗值狀態
              Info:
              - '&e用法：&6/res set <領地名稱> keepexp true/false/remove'
            lavaflow:
              Translated: 內部岩漿流動（lavaflow）
              Description: 允許或禁止岩漿流動（將覆蓋 flow 權限）
              Info:
              - '&e用法：&6/res set <領地名稱> lavaflow true/false/remove'
            leash:
              Translated: 使用拴繩（leash）
              Description: 允許或禁止對動物使用拴繩
              Info:
              - '&e用法：&6/res set/pset <領地名稱> leash true/false/remove'
            lever:
              Translated: 使用控制桿（lever）
              Description: 允許或禁止玩家使用控制桿
              Info:
              - '&e用法：&6/res set/pset <領地名稱> lever true/false/remove'
            mobexpdrop:
              Translated: 生物死後掉落經驗（mobexpdrop）
              Description: 允許或禁止生物在死後掉落經驗
              Info:
              - '&e用法：&6/res set <領地名稱> mobexpdrop true/false/remove'
            mobitemdrop:
              Translated: 生物死後掉落物品（mobitemdrop）
              Description: 允許或禁止生物在死後掉落物品
              Info:
              - '&e用法：&6/res set <領地名稱> mobitemdrop true/false/remove'
            mobkilling:
              Translated: 擊殺生物（mobkilling）
              Description: 允許或禁止擊殺生物
              Info:
              - '&e用法：&6/res set/pset <領地名稱> mobkilling true/false/remove'
            monsters:
              Translated: 生成怪物（monsters）
              Description: 允許或禁止生成怪物
              Info:
              - '&e用法：&6/res set <領地名稱> monsters true/false/remove'
            move:
              Translated: 玩家移動（move）
              Description: 允許或禁止在領地內移動
              Info:
              - '&e用法：&6/res set/pset <領地名稱> move true/false/remove'
            nametag:
              Translated: 命名（nametag）
              Description: 允許或禁止使用命名牌
              Info:
              - '&e用法：&6/res set/pset <領地名稱> nametag true/false/remove'
            nanimals:
              Translated: 原版生物生成（nanimals）
              Description: 允許或禁止原版生物生成
              Info:
              - '&e用法：&6/res set <領地名稱> nanimals true/false/remove'
            nmonsters:
              Translated: 原版怪物生成（nmonsters）
              Description: 允許或禁止原版怪物生成
              Info:
              - '&e用法：&6/res set <領地名稱> nmonsters true/false/remove'
            night:
              Translated: 夜晚（night）
              Description: 設定領地時間為夜晚
              Info:
              - '&e用法：&6/res set <領地名稱> night true/false/remove'
            nofly:
              Translated: 禁止飛行（nofly）
              Description: 允許或禁止在領地內飛行
              Info:
              - '&e用法：&6/res set/pset <領地名稱> nofly true/false/remove'
            fly:
              Translated: 自動飛行（fly）
              Description: 自動啟用或停用領地內的玩家飛行
              Info:
              - '&e用法：&6/res set/pset <領地名稱> fly true/false/remove'
            nomobs:
              Translated: 防止怪物進入（nomobs）
              Description: 防止怪物進入領地，需要在配置檔案啟用 AutoMobRemoval
              Info:
              - '&e用法：&6/res set <領地名稱> nomobs true/false/remove'
            note:
              Translated: 使用音階盒（note）
              Description: 允許或禁止玩家使用音階盒
              Info:
              - '&e用法：&6/res set/pset <領地名稱> note true/false/remove'
            nodurability:
              Translated: 防止物品降低耐久度（nodurability）
              Description: 防止物品降低耐久度
              Info:
              - '&e用法：&6/res set <領地名稱> nodurability true/false/remove'
            overridepvp:
              Translated: 覆蓋戰鬥（overridepvp）
              Description: 覆蓋任何插件的戰鬥保護
              Info:
              - '&e用法：&6/res set <領地名稱> overridepvp true/false/remove'
            pressure:
              Translated: 使用壓力板（pressure）
              Description: 允許或禁止玩家使用壓力板
              Info:
              - '&e用法：&6/res set/pset <領地名稱> pressure true/false/remove'
            piston:
              Translated: 使用活塞（piston）
              Description: 允許或禁止活塞在領地內推動或拉動方塊
              Info:
              - '&e用法：&6/res set <領地名稱> piston true/false/remove'
            pistonprotection:
              Translated: 防止外部活塞（pistonprotection）
              Description: 允許或禁止活塞從領地外面推動或拉動領地內的方塊
              Info:
              - '&e用法：&6/res set <領地名稱> pistonprotection true/false/remove'
            place:
              Translated: 放置（place）
              Description: 允許或禁止放置方塊（將覆蓋 build 權限）
              Info:
              - '&e用法：&6/res set/pset <領地名稱> place true/false/remove'
            pvp:
              Translated: 戰鬥（pvp）
              Description: 允許或禁止在領地內戰鬥
              Info:
              - '&e用法：&6/res set <領地名稱> pvp true/false/remove'
            rain:
              Translated: 下雨（rain）
              Description: 設定領地天氣為雨天
              Info:
              - '&e用法：&6/res set <領地名稱> rain true/false/remove'
            respawn:
              Translated: 自動重生（respawn）
              Description: 自動重生玩家
              Info:
              - '&e用法：&6/res set <領地名稱> respawn true/false/remove'
            riding:
              Translated: 騎乘（riding）
              Description: 允許或禁止騎乘生物
              Info:
              - '&e用法：&6/res set/pset <領地名稱> riding true/false/remove'
            shoot:
              Translated: 射擊（shoot）
              Description: 允許或禁止在區域內射擊
              Info:
              - '&e用法：&6/res set <領地名稱> shoot true/false/remove'
            sun:
              Translated: 晴天（sun）
              Description: 設定領地天氣為晴天
              Info:
              - '&e用法：&6/res set <領地名稱> sun true/false/remove'
            shop:
              Translated: 商店（shop）
              Description: 新增領地到指定的商店列表
              Info:
              - '&e用法：&6/res set <領地名稱> shop true/false/remove'
            snowtrail:
              Translated: 積雪（snowtrail）
              Description: 允許或禁止雪人生成積雪路徑
              Info:
              - '&e用法：&6/res set <領地名稱> snowtrail true/false/remove'
            spread:
              Translated: 方塊擴散（spread）
              Description: 允許或禁止草地或菌絲土擴散
              Info:
              - '&e用法：&6/res set <領地名稱> spread true/false/remove'
            snowball:
              Translated: 雪球擊退（snowball）
              Description: 允許或禁止雪球擊退
              Info:
              - '&e用法：&6/res set <領地名稱> snowball true/false/remove'
            sanimals:
              Translated: 使用生怪磚或生怪蛋生成動物（sanimals）
              Description: 允許或禁止生怪磚或生怪蛋生成動物
              Info:
              - '&e用法：&6/res set <領地名稱> sanimals true/false/remove'
            shear:
              Translated: 剪羊毛（shear）
              Description: 允許或禁止剪羊毛
              Info:
              - '&e用法：&6/res set/pset <領地名稱> shear true/false/remove'
            smonsters:
              Translated: 使用生怪磚或生怪蛋生成怪物（smonsters）
              Description: 允許或禁止生怪磚或生怪蛋生成怪物
              Info:
              - '&e用法：&6/res set <領地名稱> smonsters true/false/remove'
            subzone:
              Translated: 建立子區域（subzone）
              Description: 允許或禁止玩家建立子區域領地
              Info:
              - '&e用法：&6/res set/pset <領地名稱> subzone true/false/remove'
            title:
              Translated: 加入或離開訊息（title）
              Description: 顯示 或 隱藏領地的加入訊息
              Info:
              - '&e用法：&6/res set <領地名稱> title true/false/remove'
            table:
              Translated: 使用工作台（table）
              Description: 允許或禁止玩家使用工作台
              Info:
              - '&e用法：&6/res set/pset <領地名稱> table true/false/remove'
            tnt:
              Translated: TNT 爆炸（tnt）
              Description: 允許或禁止 TNT 爆炸
              Info:
              - '&e用法：&6/res set <領地名稱> tnt true/false/remove'
            tp:
              Translated: 傳送（tp）
              Description: 允許或禁止傳送到領地
              Info:
              - '&e用法：&6/res set/pset <領地名稱> tp true/false/remove'
            trade:
              Translated: 村民交易（trade）
              Description: 允許或禁止在領地內與村民交易
              Info:
              - '&e用法：&6/res set/pset <領地名稱> trade true/false/remove'
            trample:
              Translated: 踩踏破壞耕地（trample）
              Description: 允許或禁止踩踏破壞耕地
              Info:
              - '&e用法：&6/res set <領地名稱> trample true/false/remove'
            use:
              Translated: 使用（use）
              Description: 允許或禁止使用門、控制桿、按鈕……
              Info:
              - '&e用法：&6/res set/pset <領地名稱> use true/false/remove'
            vehicledestroy:
              Translated: 破壞載具（vehicledestroy）
              Description: 允許或禁止破壞載具
              Info:
              - '&e用法：&6/res set/pset <領地名稱> vehicledestroy true/false/remove'
            witherspawn:
              Translated: 生成凋零怪（witherspawn）
              Description: 允許或禁止生成凋零怪
              Info:
              - '&e用法：&6/res set <領地名稱> witherspawn true/false/remove'
            phantomspawn:
              Translated: 生成夜魅（phantomspawn）
              Description: 允許或禁止生成夜魅
              Info:
              - '&e用法：&6/res set <領地名稱> phantomspawn true/false/remove'
            witherdamage:
              Translated: 凋零怪傷害（witherdamage）
              Description: 允許或禁止凋零怪傷害
              Info:
              - '&e用法：&6/res set <領地名稱> witherdamage true/false/remove'
            witherdestruction:
              Translated: 凋零怪破壞方塊（witherdestruction）
              Description: 允許或禁止凋零怪破壞方塊
              Info:
              - '&e用法：&6/res set <領地名稱> witherdestruction true/false/remove'
            waterflow:
              Translated: 水流動（waterflow）
              Description: 允許或禁止水流動（將覆蓋 flow 權限）
              Info:
              - '&e用法：&6/res set <領地名稱> waterflow true/false/remove'
            wspeed1:
              Translated: 減速（wspeed1）
              Description: 減慢在領地內的玩家行走速度
              Info:
              - '&e用法：&6/res set <領地名稱> wspeed1 true/false/remove'
            wspeed2:
              Translated: 加速（wspeed2）
              Description: 加快在領地內的玩家行走速度
              Info:
              - '&e用法：&6/res set <領地名稱> wspeed2 true/false/remove'
        remove:
          Info:
          - '&e用法：&6/res remove [領地名稱]'
          Description: 移除領地
        signupdate:
          Info:
          - '&e用法：&6/res signupdate'
          Description: 更新領地告示牌
        current:
          Info:
          - '&e用法：&6/res current'
          Description: 顯示你目前所在的領地
        setowner:
          Info:
          - '&e用法：&6/resadmin setowner [領地名稱] [玩家名稱] （-keepflags）'
          Description: 更改領地擁有者
        reload:
          Info:
          - '&e用法：&6/res reload [config/lang/groups/flags]'
          Description: 重新載入語言或設定檔案
        leaveraid:
          Info:
          - '&e用法：&6/res leaveraid'
          Description: 離開領地侵略
        defend:
          Info:
          - '&e用法：&6/res defend [領地名稱] （玩家名稱）'
          Description: 加入領地侵略防守方
        attack:
          Description: 開始領地侵略
          Info:
          - '&e用法：&6/res attack [領地名稱]'
        unstuck:
          Info:
          - '&e用法：&6/res unstuck'
          Description: 傳送到領地外
        subzone:
          Info:
          - '&e用法：&6/res subzone <領地名稱> [子區域名稱]'
          - 如果領地名稱被忽略，將嘗試使用原有的領地名稱。
          Description: 在領地中建立子區域
        removeworld:
          Info:
          - '&e用法：&6/res removeworld [世界名稱]'
          - 此指令只能在控制台執行。
          Description: 移除指定世界中的所有領地
        limits:
          Info:
          - '&e用法：&6/res limits （玩家名稱）'
          - 顯示你在建立和管理領地方面的各種限制。
          Description: 顯示你的領地限制
        set:
          Info:
          - '&e用法：&6/res set <領地名稱> [權限] [true/false/remove]'
          - 使用 &6/res flags ? 查看權限列表，
          - 這些權限適用所有的玩家（詳見 /res pset ?）。
          Description: 為領地設定通用權限
        clearflags:
          Info:
          - '&e用法：&6/res clearflags <領地名稱>'
          Description: 移除領地所有權限
        message:
          Info:
          - '&e用法：&6/res message <領地名稱> [enter/leave] [訊息]'
          - 設定領地加入或離開的訊息。
          - '&e用法：&6/res message <領地名稱> remove [enter/leave]'
          - 移除加入或離開的訊息。
          Description: 管理領地加入或離開訊息
        command:
          Info:
          - '&e用法：&6/res command <領地名稱> <allow/block/list> <指令>'
          - 顯示列表，新增或移除領地中允許或禁止的指令。
          - 使用「_」可包含多個變量的指令。
          Description: 管理允許或禁止的領地指令
        confirm:
          Description: 確認移除領地
          Info:
          - '&e用法：&6/res confirm'
          - 確認移除領地。
        tpset:
          Info:
          - '&e用法：&6/res tpset'
          - 把你站的位置設定為領地的傳送點。
          - 前提是你一定要在領地內，
          - 且你還一定要是領地擁有者或者擁有管理員權限。
          Description: 設定傳送點
        resadmin:
          Info:
          - '&e用法：&6/res resadmin [on/off]'
          Description: 啟用或禁用領地管理員
        tpconfirm:
          Info:
          - '&e用法：&6/res tpconfirm'
          - 即使傳送點不安全，仍然強制傳送到領地。
          Description: 忽略不安全傳送警告
        removeall:
          Info:
          - '&e用法：&6/res removeall [擁有者]'
          - 移除自己的所有領地。
          - 如果你對除你自己以外的任何人使用它，則需要 /resadmin。
          Description: 移除指定玩家的所有領地
        material:
          Info:
          - '&e用法：&6/res material [物品 ID]'
          Description: 檢查物品是否存在其 ID
        kick:
          Info:
          - '&e用法：&6/res kick <玩家名稱>'
          - 你必須是領地擁有者，
          - 且玩家必須在線上。
          Description: 踢出指定玩家
        rename:
          Info:
          - '&e用法：&6/res rename [舊領地名稱] [新領地名稱]'
          - 你必須是擁有者或管理員才能進行此項操作，
          - 新名稱不能與現有領地名稱重複。
          Description: 重新命名領地
        sublist:
          Info:
          - '&e用法：&6/res sublist <領地名稱> <頁數>'
          - 列出所有子區域。
          Description: 列出所有子區域
        setallfor:
          Info:
          - '&e用法：&6/res setallfor [玩家名稱] [權限] [true/false/remove]'
          Description: 在指定玩家的所有領地上設定通用權限
        lease:
          Info:
          - '&e用法：&6/res lease [renew/cost] [領地名稱]'
          - /res lease cost 顯示續租領地的花費。
          - /res lease renew 將會續租指定的領地。
          Description: 管理領地租約
          SubCommands:
            set:
              Description: 設定租約時間
              Info:
              - '&e用法：&6/resadmin lease set [領地名稱] [#days/infinite]'
              - 將租約時間設定為特定天數或無限制。
            renew:
              Description: 續租租約時間
              Info:
              - '&e用法：&6/resadmin lease renew <領地名稱>'
              - 續租目前或指定的領地租約。
            list:
              Description: 顯示目前住所的租約列表
              Info:
              - '&e用法：&6/resadmin lease list <領地名稱> <頁數>'
              - 顯示目前所有子區域的租約時間。
            expires:
              Description: 租約到期時間
              Info:
              - '&e用法：&6/resadmin lease expires <領地名稱>'
              - 顯示領地租約到期的時間。
            cost:
              Description: 顯示續租花費
              Info:
              - '&e用法：&6/resadmin lease cost <領地名稱>'
              - 顯示續租領地所需的花費。
        tp:
          Info:
          - '&e用法：&6/res tp [領地名稱]'
          - 傳送到你指定的領地，你必須擁有傳送（tp）權限或是擁有者才能執行此操作。
          - 伺服器管理員需要給予你設定的權限。
          Description: 傳送到領地
        setall:
          Info:
          - '&e用法：&6/res setall [權限] [true/false/remove]'
          Description: 在所有的領地上設定通用權限
        resreload:
          Description: 重新載入領地插件
          Info:
          - '&e用法：&6/resreload'
        resload:
          Description: 載入領地存檔
          Info:
          - '&e用法：&6/resload'
          - 此指令不安全，它不會先保存領地狀態，
          - 請確定變更完領地後再重新載入存檔。
