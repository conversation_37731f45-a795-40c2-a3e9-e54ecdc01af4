# of this file and modify that instead. This file will be updated automatically by Residence
# when a newer version is detected, and your changes will be overwritten.  Once you 
# have a copy of this file, change the Language: option under the Residence config.yml
# to whatever you named your copy.

Language:
  Invalid:
    Player: '&cNeplat<PERSON><PERSON> j<PERSON> hr<PERSON>če...'
    World: '&cNeplatný svět...'
    Residence: '&cNeplatná Residence...'
    Subzone: '&cNeplatná subzóna...'
    Direction: '&cNeplatný směr...'
    Amount: '&cNeplatná částka...'
    Cost: '&cNeplatná cena...'
    Days: '&cNeplatný počet dní...'
    Material: '&cNeplatný materiál...'
    Boolean: '&cNeplatná hodnota, musí být &6true(t) &cnebo &6false(f)'
    Area: '&cNeplatná oblast...'
    Group: '&cNeplatná skupina...'
    MessageType: '&cTyp zpr<PERSON><PERSON> mus<PERSON> b<PERSON>t enter nebo remove.'
    Flag: '&cNeplatná vlajka...'
    FlagState: '&cNeplatný status vlajky, musí být &6true(t)&c, &6false(f)&c, nebo
      &6remove(r)'
    List: '&eNeznámý seznam, musí být &6blacklist &enebo &6ignorelist.'
    Page: '&eNeplatná stránka...'
    Help: '&cNeplatná stránka nápovědy...'
    NameCharacters: '&cJméno obsahuje nepovolene symboly...'
  Area:
    Exists: '&cJméno oblasti je již obsazené.'
    Create: '&eOblast residence vytvořena, ID &6%1'
    DiffWorld: '&cOblast je v jiném světě, než je residence.'
    Collision: '&cOblast koliduje s residencí &6%1 a nelze ji tak vytvořit!'
    SubzoneCollision: '&cOblast koliduje se subzónou &6%1'
    NonExist: '&cTaková oblast neexistuje.'
    InvalidName: '&cNeplatné jméno oblasti...'
    ToSmallX: '&cTvůj výběr &6X (&6%1&c) je moc krátký. &eJe povoleno &6%2 &ea více.'
    ToSmallY: '&cTvůj výběr (&6%1&c) je moc nízký. &eJe povoleno &6%2 &ea více.'
    ToSmallZ: '&cTvůj výběr &6Z (&6%1&c) je moc úzký. &eJe povoleno &6%2 &ea více.'
    ToBigX: '&cTvůj výběr délky &6X (&6%1&c) je moc velký. &eJe povoleno &6%2 &ea
      méně.'
    ToBigY: '&cTvůj výběr výšky &6Y (&6%1&c) je moc velký. &eJe povoleno &6%2 &ea
      méně.'
    ToBigZ: '&cTvůj výběr délky &6Z (&6%1&c) je moc velký. &eJe povoleno &6%2 &ea
      méně.'
    Rename: '&eOblast &6%1 &epřejmenována na &6%2'
    Remove: '&eOblast &6%1 odstraněna...'
    Name: '&eJméno: &2%1'
    RemoveLast: '&cNelze odstranit poslední oblast v residenci.'
    NotWithinParent: '&cOblast není ve své rodičovské oblasti.'
    Update: '&eOblast aktualizována...'
    MaxPhysical: '&eByl dosažen maximalní počet oblastí v residenci.'
    SizeLimit: '&eNemůžeš vytvořit takhle velkou oblast.'
    HighLimit: '&cNemůžeš chránit tak vysoko, tvůj limit je &6%1'
    LowLimit: '&cNemůžeš chránit tak hluboko, tvůj limit je &6%1'
  Select:
    Points: '&eVyber 2 body před použitím tohoto příkazu!'
    Overlap: '&cVybraná oblast se překrývá s regionem &6%1'
    WorldGuardOverlap: '&cVybraná oblast se překrývá s WorldGuard regionem &6%1!'
    KingdomsOverlap: '&cVybraná oblast se překrývá s &6%1 &cKingdoms land regionem!'
    Success: '&eVýběr úspěšný!'
    Fail: '&cNeplatný výběrový příkaz...'
    Bedrock: '&eVýběr rozšířen na nejnižší povolenou úroveň.'
    Sky: '&eVýběr rozšířen na nejvyšší povolenou úroveň.'
    Area: '&eVybraná oblast &6%1 &eresidence &6%2'
    Tool: '&e- Výběrový nástroj: &6%1'
    PrimaryPoint: '&eVybrán &6primární &evýběrový bod %1'
    SecondaryPoint: '&eVybrán &6sekundární &evýběrový bod %1'
    Primary: '&ePrimární výběr: &6%1'
    Secondary: '&eSekundární výběr: &6%1'
    TooHigh: '&cVýběr je vyšší než maximální výška stavění, omezuji.'
    TooLow: '&cVýběr je pod bedrockem, omezuji.'
    TotalSize: '&eVelikost výběru: &6%1'
    AutoEnabled: '&eAutomatický výběrový režim &6ZAPNUT&e. Vypni pomocí &6/res select
      auto'
    AutoDisabled: '&eAutomatický výběrový režim &6VYPNUT&e. Opět zapni pomocí &6/res
      select auto'
    Disabled: '&cNemáš práva pro použití výběrových příkazů'
  Sign:
    Updated: '&6%1 &ecedule aktualizovány!'
    TopLine: '[market]'
    DateFormat: YY/MM/dd HH:mm
    ForRentTopLine: '&8K pronájmu'
    ForRentPriceLine: '&8%1&f/&8%2&f/&8%3'
    ForRentResName: '&8%1'
    ForRentBottomLine: '&9Dostupné'
    RentedAutorenewTrue: '&2%1'
    RentedAutorenewFalse: '&c%1'
    RentedTopLine: '%1'
    RentedPriceLine: '&8%1&f/&8%2&f/&8%3'
    RentedResName: '&8%1'
    RentedBottomLine: '&1%1'
    ForSaleTopLine: '&8K prodeji'
    ForSalePriceLine: '&8%1'
    ForSaleResName: '&8%1'
    ForSaleBottom: '&5%1m³'
    LookAt: '&cNekoukáš se na ceduli'
    ResName: '&8%1'
    Owner: '&5%1'
  Siege:
    Started: '&7Obléhání začalo!'
    noSelf: '&cNelze obléhat vlastní residenci!'
    isOffline: '&cNelze obléhat cizí rezidenci pokud je její vlastník offline!'
    cooldown: '&cMoc brzy na nové obléhání této rezidence! Počkej %1'
  info:
    years: '&e%1 &6roků '
    oneYear: '&e%1 &6rok '
    day: '&e%1 &6dnů '
    oneDay: '&e%1 &6den '
    hour: '&e%1 &6hodin '
    oneHour: '&e%1 &6hodina '
    min: '&e%1 &6min '
    sec: '&e%1 &6sek '
    click: '&7Klik'
  server:
    land: Server_Land
  Flag:
    ownColor: '&4'
    p1Color: '&2'
    p2Color: '&a'
    haveColor: '&2'
    havePrefix: ''
    denyColor: '&4'
    denyPrefix: ''
    Set: '&eVlajka (&6%1&e) &6%2 &enastavená na &6%3 &estate'
    SetFailed: '&cNemáš přístup k vlajce &6%1'
    CheckTrue: '&eVlajka &6%1 &ese vztahuje na hráče &6%2 &epro residenci &6%3&e,
      hodnota = &6%4'
    CheckFalse: '&eVlajka &6%1 &ese nevztahuje na hráče &6%2 &ev residenci.'
    Cleared: '&eVlajky vyčištěny.'
    RemovedAll: '&eVšechny vlajky odstraněny pro &6%1 &ev &6%2 &eresidenci.'
    RemovedGroup: '&eVšechny vlajky odstraněny pro &6%1 &eskupinu v &6%2 &eresidenci.'
    Default: '&eNastaveny výchozí hodnoty vlajek.'
    Deny: '&cZde nemáš oprávnění na &6%1.'
    SetDeny: '&cVlastník residence nemá práva na &6%1'
    ChangeDeny: '&cNemůžeš změnit vlajku &6%1 &cdokud jsou hráči &6%2 &c uvnitř residence.'
    ChangedForOne: '&eZměněny &6%1 &evlajky pro &6%2 &eresidenci'
    ChangedFor: '&eZměněny &6%1 &evlajky z &6%2 &eresidence checked'
    reset: '&eReset vlajek pro &6%1 &eresidenci'
    resetAll: '&eReset vlajek pro &6%1 &eresidence'
  Bank:
    NoAccess: '&cNemáš přístup k bance.'
    Name: ' &eBanka: &6%1'
    NoMoney: '&cNedostatek peněz v bance.'
    Deposit: '&eVložil jsi &6%1 &edo banku residence.'
    Withdraw: '&eVybral jsi &6%1 &ez banku residence.'
  Subzone:
    Rename: '&eSubzóna byla přejmenována z &6%1 &ena &6%2'
    Remove: '&eSubzóna &6%1 &eodstraněna.'
    Create: '&eVytvořena subzóna &6%1'
    CreateFail: '&cNepovedlo se vytvořit subzónu &6%1'
    Exists: '&cSubzóna &6%1 &cjiž existuje.'
    Collide: '&cSubzóna se překrývá se subzónou &6%1'
    MaxAmount: '&cDosáhl jsi maximálního počtu subzón v této residenci.'
    MaxDepth: '&cDosáhl jsi maximální možné hloubky subzóny.'
    SelectInside: '&eOba výběrové body musí být uvnitř residence.'
    CantCreate: '&cNemáš práva na vytvoření subzón v residenci.'
    CantDelete: '&cNemáš práva na mazání subzón v residenci.'
    CantDeleteNotOwnerOfParent: '&cNejsi vlastníkem nadřízené residence, nelze smazat
      tuto subzónu.'
    CantContract: '&cNemáš oprávnění na zmenšení subzóny.'
    CantExpand: '&cNemáš oprávnění na zvětšení subzóny.'
    DeleteConfirm: '&ePokud opravdu chceš smazat subzónu &6%1&e, napiš &6/res confirm
      &epro potvrzení.'
    OwnerChange: '&eVlastník subzóny &6%1 &eowner změněn na &6%2'
  Residence:
    DontOwn: '&eNic k vidění'
    Hidden: ' &e(&6Skrytý&e)'
    Bought: '&eKoupil jsi residenci &6%1'
    Buy: '&6%1 &esi od tebe koupil residenci &6%2 &e.'
    BuyTooBig: '&cTato residence má větší oblasti, než je tvoje povolené maximum.'
    NotForSale: '&cResidence není na prodej.'
    ForSale: '&eResidence &6%1 &eje nyní k prodeji za &6%2'
    StopSelling: '&cResidence už není na prodej.'
    TooMany: '&cJiž vlastníš maximální počet residencí.'
    MaxRent: '&cJiž pronajímáš maximální počet residencí.'
    AlreadyRent: '&cResidence je již pronajímána...'
    NotForRent: '&cResidence není k pronájmu...'
    NotForRentOrSell: '&cResidence není k pronájmu, či k prodeji...'
    NotRented: '&cResidence není pronajata.'
    Unrent: '&eResidenci &6%1 &eskončil pronájem.'
    RemoveRentable: '&eResidence &6%1 &ejiž není k pronájmu.'
    ForRentSuccess: '&eResidence &6%1 &eje pronajímána na &6%2 &ekaždé &6%3 &ední.'
    RentSuccess: '&ePronajmul sis residenci &6%1 &ena &6%2 &ední.'
    EndingRent: '&ePronájem končí pro &6%1 &e- &6%2'
    AlreadyRented: '&eResidence &6%1 &eje pronajata hráči &6%2'
    CantAutoPay: '&eResidenci nelze automaticky prodat, bude nastavena hodnota &6false'
    AlreadyExists: '&cResidence se jménem &6%1 &cuž existuje.'
    Create: '&eVytvořil jsi residenci &6%1&e!'
    Rename: '&ePřejmenována residence z &6%1 &ena &6%2'
    Remove: '&eResidence &6%1 &ebyla odstraněna...'
    CantRemove: '&cResidenci &6%1 &cnelze odstranit jako &6%2 &csubzóna je pronajímána
      dále &6%3'
    MoveDeny: '&cNemáš právo k pohybu v residenci &6%1'
    TeleportNoFlag: '&cNemáš právo teleportovat se na onu residenci.'
    FlagDeny: '&cNemáš právo &6%1 &cv residenci &6%2'
    GiveLimits: '&cNemůžeš dát residenci cílovému hráči, protože ten již maximální
      počet residencí má.'
    GiveConfirm: '&7klikni pro potvrzení &6%1 &7residenci převést z &6%2 &7na &6%3'
    Give: '&eDal jsi residenci &6%1 &ehráči &6%2'
    Recieve: '&eObdržel jsi residenci &6%1 &eod hráče &6%2'
    ResList: ' &a%1. &e%2 &e- &6%3 %4&6%5'
    List: ' &a%1%2 &e- &6Svět&e: &6%3'
    TeleportNear: '&eTeleportován vedle residence.'
    SetTeleportLocation: '&ePozice pro teleportaci nastavena...'
    PermissionsApply: '&ePráva applikována na residenci.'
    NotOwner: '&cNejsi vlastník residence.'
    RemovePlayersResidences: '&eOdstraněny všechny residence náležící hráči &6%1'
    NotIn: '&cNejsi v residenci.'
    PlayerNotIn: '&cHráč nestojí v oblasti tvé residence.'
    Kicked: '&eByl jsi vyhozen z residence'
    CantKick: '&eTohoto hráče nelze vykopnout'
    In: '&eStojíš v residenci &6%1'
    OwnerChange: '&eVlastník residence &6%1 &ezměněn na &6%2'
    NonAdmin: '&cNejsi Residenční Admin.'
    Line: '&eResidence: &6%1 '
    RentedBy: '&ePronajmuta hráčem: &6%1'
    MessageChange: '&eZpráva nastavena...'
    CantDeleteResidence: '&cNemáš práva na smazání residence.'
    CantExpandResidence: '&cNemáš práva na rozšíření residence.'
    CantContractResidence: '&cNemáš právo na zmenšení residence.'
    NoResHere: '&cNení tu žádná residence.'
    OwnerNoPermission: '&cVlastník nemá na toto práva.'
    ParentNoPermission: '&cNemáš práva měnit vlastnosti nadřízené zóny.'
    ChatDisabled: '&eChat v residenci zakázán...'
    DeleteConfirm: '&ePokud chceš opravdu smazat residenci &6%1&e, napiš &6/res confirm
      &epro potvrzení.'
    ChangedMain: '&eHlavní residence změněna na &6%1'
    LwcRemoved: '&eRemoved &6%1 &eLwc protections in &6%2ms'
    CanBeRented: '&6%1&e lze pronajmout za &6%2 &ena &6%3 &edny. &6/res market rent'
    CanBeBought: '&6%1&e lze koupit za &6%2&e. &6/res market buy'
    IsForRent: '&6(K pronájmu)'
    IsForSale: '&6(K prodeji)'
    IsRented: '&6(Pronajaté)'
  Rent:
    Disabled: '&cPronajímání je zakázáno...'
    DisableRenew: '&eNájem residence &6%1 &ese již nebude automaticky prodlužovat.'
    EnableRenew: '&eNájem residence &6%1 &ese nyní bude automaticky prodlužovat.'
    NotByYou: '&cResidence není pronajímána tebou.'
    isForRent: '&2Residence je k dispozici k pronájmu.'
    MaxRentDays: '&cNemůžeš pronajímat více jak &6%1 &cdní najednou.'
    OneTime: '&cNelze prodloužit čas pronájmu této residence.'
    Extended: '&ePronájem prodloužen o dalších &6%1 &ední pro &6%2 &eresidenci'
    Expire: '&eNájem vyprší: &6%1'
    AutoPayTurnedOn: '&eAutoprodej je &2ZAPNUTÝ'
    AutoPayTurnedOff: '&eAutoprodej je &cVYPNUTÝ'
    ModifyDeny: '&cNelze modifikovat pronajatou residenci.'
    Days: '&eDny pronájmu: &6%1'
    Rented: ' &6(Pronajaté)'
    RentList: ' &6%1&e. &6%2 &e(&6%3&e/&6%4&e/&6%5&e) - &6%6 &6%7'
    EvictConfirm: '&eNapiš &6/res market confirm &epro vystěhování nájemce z residence
      &6%1'
    UnrentConfirm: '&eNapiš &6/res market confirm &epro odejmutí pronájmu residence
      &6%1'
    ReleaseConfirm: '&eNapiš &6/res market confirm &epro odejmutí residence &6%1 &ez
      marketu'
  command:
    addedAllow: '&ePřidán nový povolený příkaz pro &6%1 &eresidenci'
    removedAllow: '&eOdebrán nový povolený příkaz pro &6%1 &eresidenci'
    addedBlock: '&ePřidán nový blokovaný příkaz pro &6%1 &eresidenci'
    removedBlock: '&eOdebrán nový blokovaný příkaz pro &6%1 &eresidenci'
    Blocked: '&eBlokované příkazy: &6%1'
    Allowed: '&ePovolené příkazy: &6%1'
  Rentable:
    Land: '&ePronajímatelný pozemek: &6'
    AllowRenewing: '&eLze obnovit: &6%1'
    StayInMarket: '&ePronajímatelné zůstane na marketu: &6%1'
    AllowAutoPay: '&ePronajímatelné povoluje auto platbu: &6%1'
    DisableRenew: '&6%1 &enebude po vypršení pronajímatelná.'
    EnableRenew: '&6%1 &ebude automaticky prodloužen status pronajímatelnosti.'
  Economy:
    LandForSale: '&ePozemek na prodej:'
    NotEnoughMoney: '&cNemáš dostatek peněz.'
    MoneyCharged: '&eOdebráno &6%1 &ez tvého účtu &6%2.'
    MoneyAdded: '&ePřidáno &6%1 &edo tvého účtu &6%2.'
    MoneyCredit: '&ePřipsáno &6%1 &etvému účtu &6%2.'
    RentReleaseInvalid: '&eResidence &6%1 &enení pronajata, nebo není k pronájmu.'
    RentSellFail: '&cNelze prodat residenci, pokud je k pronájmu.'
    SubzoneRentSellFail: '&cNelze prodat rezidenci pokud je její subzóna dána k pronájmu.'
    ParentRentSellFail: '&cNelze prodat rezidenci pokud je její rodičovská zóna dána
      k pronájmu.'
    SubzoneSellFail: '&cNelze prodat subzónu.'
    SellRentFail: '&cNelze pronajmout residenci, pokud je na prodej.'
    ParentSellRentFail: '&cNelze pronajmout residenci, pokud je její rodičovská zóna
      dána k prodeji.'
    OwnerBuyFail: '&cNelze si koupit svoji vlastní plochu!'
    OwnerRentFail: '&cNelze si pronajmout svoji vlastní plochu!'
    AlreadySellFail: '&eResidence už je na prodej!'
    LeaseRenew: '&ePronájem validní do &6%1'
    LeaseRenewMax: '&ePronájem prodloužen na maximální délku'
    LeaseNotExpire: '&eŽádný takový pronájem neexistuje, nebo je na neomezeně dlouhou
      dobu.'
    LeaseRenewalCost: '&eCena k pronájmu oblasti &6%1 &eje &6%2'
    LeaseInfinite: '&eDoba pronájmu nastavena na nekonečno...'
    MarketDisabled: '&cEkonomie je zakázána!'
    SellAmount: '&eČástka k prodeji: &2%1'
    SellList: ' &6%1&e. &6%2 &e(&6%3&e) - &6%4'
    LeaseExpire: '&ePronájem propadá: &2%1'
    LeaseList: '&6%1. &e%2 &2%3 &e%4'
  Expanding:
    North: '&eRozšiřuji severně o &6%1 &ebloků'
    West: '&eRozšiřuji západně o &6%1 &ebloků'
    South: '&eRozšiřuji jižně o &6%1 &ebloků'
    East: '&eRozšiřuji východně o &6%1 &ebloků'
    Up: '&eRozšiřuji nahoru o &6%1 &ebloků'
    Down: '&eRozšiřuji dolů o &6%1 &ebloků'
  Contracting:
    North: '&eZmenšuji severně o &6%1 &ebloků'
    West: '&eZmenšuji západně o &6%1 &ebloků'
    South: '&eZmenšuji jižně o &6%1 &ebloků'
    East: '&eZmenšuji východně o &6%1 &ebloků'
    Up: '&eZmenšuji nahoru o &6%1 &ebloků'
    Down: '&eZmenšuji dolů o &6%1 &ebloků'
  Shifting:
    North: '&ePosouvám severně o &6%1 &ebloků'
    West: '&ePosouvám západně o &6%1 &ebloků'
    South: '&ePosouvám jižně o &6%1 &ebloků'
    East: '&ePosouvám východně o &6%1 &ebloků'
    Up: '&ePosouvám nahoru o &6%1 &ebloků'
    Down: '&ePosouvám dolů o &6%1 &ebloků'
  Limits:
    PGroup: '&7- &eSkupina oprávnění:&3 %1'
    RGroup: '&7- &eSkupina residence:&3 %1'
    Admin: '&7- &eResidenční Admin:&3 %1'
    CanCreate: '&7- &eMůže vytvářet residence:&3 %1'
    MaxRes: '&7- &eMaximální počet residencí:&3 %1'
    MaxEW: '&7- &eMaximální zapadní/vychodní velikost:&3 %1'
    MaxNS: '&7- &eMaximální severní/jižní velikost:&3 %1'
    MaxUD: '&7- &eMaximální výška/hloubka:&3 %1'
    MinMax: '&7- &eMinimalní/Maximální výška:&3 %1 to %2'
    MaxSubzones: '&7- &eMaximální počet subzón:&3 %1'
    MaxSubDepth: '&7- &eMaximální počet podsubzón:&3 %1'
    MaxRents: '&7- &eMaximální počet pronájmů:&3 %1'
    MaxRentDays: ' &eMaximální počet dnů pronájmu:&3 %1'
    EnterLeave: '&7- &eMůže nastavit uvítací zprávu:&3 %1'
    NumberOwn: '&7- &ePočet residencí, které vlastníš:&3 %1'
    Cost: '&7- &eCena residence za blok:&3 %1'
    Sell: '&7- &ePeníze za prodání residence za blok:&3 %1'
    Flag: '&7- &ePráva na vlajky:&3 %1'
    MaxDays: '&7- &eMaximální délka pronájmu (dny):&3 %1'
    LeaseTime: '&7- &eDélka nájmu daná při prodloužení:&3 %1'
    RenewCost: '&7- &eCena znovuobnovení nájmu za blok:&3 %1'
  Gui:
    Set:
      Title: '&6%1 vlajky'
    Pset:
      Title: '&6%1 %2 vlajky'
    Actions:
    - '&2Levým klikem povolíš'
    - '&cPravým klikem zakážeš'
    - '&eShift + levým klikem odstraníš'
  InformationPage:
    TopLine: '&e---< &a %1 &e >---'
    Page: '&e-----< &6%1 &e>-----'
    NextPage: '&e-----< &6%1 &e>-----'
    NoNextPage: '&e-----------------------'
    GeneralList: '&2 %1 &6- &e%2'
    FlagsList: '&2 %1 &6- &e%2'
    SmallSeparator: '&6------'
  Chat:
    ChatChannelChange: '&eZměněn chat channel residence na &6%1!'
    ChatChannelLeave: '&eOpustil chat v residenci'
    JoinFirst: '&4Nejdříve se připoj do residenčního chatu...'
    InvalidChannel: '&4Neplatný channel...'
    InvalidColor: '&4Nesprávný kód barvy'
    NotInChannel: '&4Hráč není v channelu'
    Kicked: '&6%1 &ebyl vyhozen z channelu &6%2'
    InvalidPrefixLength: '&4Prefix je moc dlouhý. Povolená délka: %1'
    ChangedColor: '&eBarva chatu v residenci změněna na %1'
    ChangedPrefix: '&ePrefix chatu v residenci změněn na %1'
  Shop:
    ListTopLine: '&6%1 &eseznam obchodů - Strana &6%2&e/&6%3 %4'
    List: ' &e%1. &6%2 &e(&6%3&e) %4'
    ListVoted: '&e%1 (&6%2&e)'
    ListLiked: '&ePočet lajků: &0%1'
    VotesTopLine: '&6%1 &e%2 residence vote list &6- &eStrana &6%3&e/&6%4 %5'
    VotesList: ' &e%1. &6%2 &e%3 &7%4'
    NoDesc: '&6Žádný popis'
    Desc: |-
      &6Popis:
      %1
    DescChange: '&6Popis změněn na: %1'
    NewBoard: '&6Uspěšně přidána nová obchodní cedule'
    BoardExist: '&cObchodní cedule již v této lokaci existuje'
    DeleteBoard: '&6Klikni pravým tlačítkem na ceduli pro její smazání'
    DeletedBoard: '&6Cedule smazána'
    IncorrectBoard: '&cToto není cedulka, zkuste zadat příkaz znovu a klikněte na
      cedulku'
    InvalidSelection: '&cLeft click with selection tool top left sign and then right
      click bottom right'
    ToBigSelection: '&cTvůj výběr je velký, maximální povolená velikost je 16 bloků'
    ToDeapSelection: '&cYour selection is too deap, max allowed is 16x16x1 blocks'
    VoteChanged: '&6Hlasování změněno od &e%1 &6to &e%2 &6pro &e%3 &6residenci'
    Voted: '&6Hlasoval jsi a dal &e%1 &6hlasu do residence &e%2.'
    Liked: '&6Líbí se ti residence &e%1.'
    AlreadyLiked: '&6Už se ti líbí tato residence &e%1'
    NoVotes: '&cTato residence nemá žádné lajky'
    CantVote: '&cResidence nemá vlajku shop nastavenou na true'
    VotedRange: '&6Rozsah hlasování je od &e%1 &6do &e%2'
    SignLines:
      '1': '&e--== &8%1 &e==--'
      '2': '&9%1'
      '3': '&4%1'
      '4': '&8%1&e (&8%2&e)'
      Likes4: '&9Líbí se: &8%2'
  RandomTeleport:
    TpLimit: '&eNemůžeš se teleportovat tak rychle, prosím počkej &6%1 &esekund'
    TeleportSuccess: '&eTeleportováno na souřadnice X:&6%1&e, Y:&6%2&e, Z:&6%3'
    IncorrectLocation: '&6Nepovedlo se teleportovat do lokace &e%1, počkej &6sekund
      a zkus to znovu.'
    Disabled: '&cNáhodná teleportace je v tomto světě vypnuta'
    TeleportStarted: '&eTeleportace začala, nehýbej se dalších &6%4 &esekund.'
    WorldList: '&eMožé světy: &6%1'
  Permissions:
    variableColor: '&f'
    permissionColor: '&6'
    cmdPermissionColor: '&2'
  General:
    DisabledWorld: '&cResidence plugin je v tomto světě zakázán'
    UseNumbers: '&cProsím použij čísla...'
    # Replace all text with '' to disable this message
    CantPlaceLava: '&cNemůžeš položit lávu mimo residenci a výš, než &6%1 &cbloků
      vysoko.'
    # Replace all text with '' to disable this message
    CantPlaceWater: '&cNemůžeš položit vodu mimo residenci a výš, než &6%1 &cbloků
      vysoko'
    CantPlaceChest: '&cNemůžeš položit truhlu na toto místo'
    NoPermission: '&cNa toto nemáš oprávnění.'
    info:
      NoPlayerPermission: '&c[playerName] nemá oprávnění [permission]'
    NoCmdPermission: '&cNemáš oprávnění pro tento příkaz.'
    DefaultUsage: '&ePro více informací napiš &6/%1 ?'
    MaterialGet: '&eNázev materiálu pro ID &6%1 &eje &6%2'
    MarketList: '&e---- &6Seznam obchodů &e----'
    Separator: '&e----------------------------------------------------'
    AdminOnly: '&cJenom admini mohou napsat tento příkaz.'
    InfoTool: '&e- Info nástroj: &6%1'
    ListMaterialAdd: '&6%1 &epřidán do residence &6%2'
    ListMaterialRemove: '&6%1 &eodstraněn z residence &6%2'
    ItemBlacklisted: '&cToto zde nemůžeš použít.'
    WorldPVPDisabled: '&cPVP je vypnuto.'
    NoPVPZone: '&cTady není PVP zóna.'
    NoFriendlyFire: '&cNo friendly fire'
    InvalidHelp: '&cNeplatná stránka help.'
    TeleportDeny: '&cNemůžeš se teleportovat.'
    TeleportSuccess: '&eTeleportován!'
    TeleportConfirm: '&cTento teleport není bezpečný, spadneš &6%1 &cbloků. Napiš
      &6/res tpconfirm &cpokud si seš jistý.'
    TeleportStarted: '&eTeleportace na &6%1 &ezačíná, nehýbej se dalších &6%2 &esekund.'
    TeleportTitle: '&eTeleporting!'
    TeleportTitleTime: '&6%1'
    TeleportCanceled: '&eTeleportace zrušena!'
    NoTeleportConfirm: '&eŽádné teleportační žádosti ke schválení!'
    HelpPageHeader: '&eNápověda - &6%1 &e- Strana <&6%2 &ez &6%3&e>'
    ListExists: '&cSeznam již existuje...'
    ListRemoved: '&eSeznam smazán...'
    ListCreate: '&eVytvořen seznam &6%1'
    PhysicalAreas: '&eFyzické oblasti'
    CurrentArea: '&eSoučasná oblast: &6%1'
    TotalResSize: '&eCelková velikost: &6%1m³ (%2m²)'
    TotalWorth: '&eCelková hodnota residence: Nákupní cena &6%1&e, Prodejní cena (&6%2&e)'
    TotalSubzones: '&eSubzóny v residenci: &6%1 &e(&6%2&e)'
    NotOnline: '&eCílový hráč musí být online.'
    NextInfoPage: '&2| &eDalší strana &2>>>'
    PrevInfoPage: '&2<<< &ePředchozí strana &2|'
    GenericPages: '&eStrana &6%1 &ez &6%2 &e(&6%3&e)'
    WorldEditNotFound: '&cWorldEdit nebyl nalezen.'
    CoordsTop: '&eX:&6%1 &eY:&6%2 &eZ:&6%3'
    CoordsBottom: '&eX:&6%1 &eY:&6%2 &eZ:&6%3'
    AdminToggleTurnOn: '&eAutomatický resadmin &6ZAPNUTO'
    AdminToggleTurnOff: '&eAutomatický resadmin &6VYPNUTO'
    NoSpawn: '&eNemáš právo k &6pohybu &ena tvém spawnu. Přesunuji...'
    CompassTargetReset: '&eTvůj kompas byl resetován'
    CompassTargetSet: '&eTvůj kompas nyní míří na &6%1'
    Ignorelist: '&2Ignorelist:&6'
    Blacklist: '&cBlacklist:&6'
    LandCost: '&eCena země: &6%1'
    'True': '&2Povoleno'
    'False': '&cZakázáno'
    Removed: '&6Odstraněno'
    FlagState: '&eStatus vlajky: %1'
    Land: '&eZemě: &6%1'
    Cost: '&eCena: &6%1 &ena &6%2 &ední'
    Status: '&eStatus: %1'
    Available: '&2Dostupné'
    Size: ' &eVelikost: &6%1'
    ResidenceFlags: '&eVlajky residence: &6%1'
    PlayersFlags: '&eVlajky hráčů: &6%1'
    GroupFlags: '&eSkupinové vlajky: &6%1'
    OthersFlags: '&eDalší vlajky: &6%1'
    Moved: '&ePřesunuto...'
    Name: '&eJméno: &6%1'
    Lists: '&eSeznamy: &6'
    Residences: '&eResidence&6'
    CreatedOn: '&eVytvořeno dne: &6%1'
    Owner: '&eVlastník: &6%1'
    World: '&eSvět: &6%1'
    Subzones: '&eSubzóny'
    # The below lines represent various messages residence sends to the players.
    # Note that some messages have variables such as %1 that are inserted at runtime.
    NewPlayerInfo: '&ePokud si chceš ochránit dům, prosím použij tyčku k diagonálnímu
      označení oblastní a napiš &2/res create VášNázevResidence'
CommandHelp:
  Description: Nápověda k Residenci
  SubCommands:
    res:
      Description: Hlavní příkaz residence
      Info:
      - '&2Použij &6/res [příkaz] ? <strana> &2pro zobrazení další nápovědy.'
      SubCommands:
        auto:
          Description: Vytvořit maximální počet residencí kolem tebe
          Info:
          - '&ePoužití: &6/res auto (jméno residence) (rádius)'
        select:
          Description: Výběrové příkazy
          Info:
          - Tímto příkazem vybíráš oblasti pro použití s residencemi.
          - /res select [x] [y] [z] - vybere rádius bloků okolo tebe.
          SubCommands:
            coords:
              Description: Zobrazit vybrané souřadnice
              Info:
              - '&ePoužití: &6/res select coords'
            size:
              Description: Zobrazit vybranou velikost
              Info:
              - '&ePoužití: &6/res select size'
            auto:
              Description: Zapne automatický výběrový režim
              Info:
              - '&ePoužití: &6/res select auto [jméno hráče]'
            cost:
              Description: Zobrazit cenu výběru
              Info:
              - '&ePoužití: &6/res select cost'
            vert:
              Description: Rozšířit výběr vertikálně
              Info:
              - '&ePoužití: &6/res select vert'
              - Rozšířit výběr od spoda až nahoru
            sky:
              Description: Rozšířit výběr nahoru
              Info:
              - '&ePoužití: &6/res select sky'
              - Rozšířit výběr co nejvýš
            bedrock:
              Description: Rozšířit výběr nahoru
              Info:
              - '&ePoužití: &6/res select bedrock'
              - Rozšířit výběr co nejníž
            expand:
              Description: Rozšířit výběr určitým směrem
              Info:
              - '&ePoužití: &6/res select expand <počet>'
              - Rozšířit výběr o <počet> bloků ve směru tvého pohledu.
            shift:
              Description: Posunout výběr po směru.
              Info:
              - '&ePoužití: &6/res select shift <počet>'
              - Posune tvůj výběr o <počet> bloků ve směru tvého pohledu.
            chunk:
              Description: Vybere chunk, ve kterém právě stojíš.
              Info:
              - '&ePoužití: &6/res select chunk'
              - Vybere chunk, ve kterém právě stojíš.
            residence:
              Description: Vybere existující oblast v residenci.
              Info:
              - '&ePoužití: &6/res select residence <název residence>'
              - Vybere existující oblast v residenci.
            worldedit:
              Description: Nastavit výběr pomocí WorldEditu.
              Info:
              - '&ePoužití: &6/res select worldedit'
              - Nastaví oblast z WorldEdit nástroje.
        padd:
          Description: Přidat hráče do residence.
          Info:
          - '&ePoužití: &6/res padd <název residence> [jméno hráče]'
          - Přidá hráče a dá mu základní práva/vlajky v residenci
        signconvert:
          Description: Převést značku z ResidenceSign pluginu
          Info:
          - '&ePoužití: &6/res signconvert'
          - Pokusí se převést uložená data značek z externího pluginu
        listallhidden:
          Description: Zobrazit všechny skryté residence
          Info:
          - '&ePoužití: &6/res listhidden <strana>'
          - Zobrazit všechny skryté residence
        bank:
          Description: Manipulovat s penězi v residenci
          Info:
          - '&ePoužití: &6/res bank [deposit/withdraw] <název residence> [počet]'
          - Musíš stát v residenci nebo použít název residence
          - Musíš mít vlajku +bank.
        create:
          Description: Vytvořit residenci.
          Info:
          - '&ePoužití: &6/res create <název residence>'
        listall:
          Description: Zobrazit všechny residence
          Info:
          - '&ePoužití: &6/res listall <strana>'
          - Zobrazit všechny residence
        info:
          Description: Zobrazit info o residenci.
          Info:
          - '&ePoužití: &6/res info <název residence>'
          - Vynech <název residence> aby si zobrazil informace o oblasti, ve které
            právě stojíš.
        area:
          Description: Spravovat oblasti residence.
          SubCommands:
            list:
              Description: Zobrazit všechny oblasti residence.
              Info:
              - '&ePoužití: &6/res area list [název residence] <strana>'
            listall:
              Description: Zobrazit souřadnice a další informace o oblastech.
              Info:
              - '&ePoužití: &6/res area listall [název residence] <strana>'
            add:
              Description: Přidat oblasti residence.
              Info:
              - '&ePoužití: &6/res area add [název residence] [areaID]'
              - Nejprve musíš vybrat 2 body.
            remove:
              Description: Odstranit oblasti residence.
              Info:
              - '&ePoužití: &6/res area remove [název residence] [areaID]'
            replace:
              Description: Vyměnit oblasti residence.
              Info:
              - '&ePoužití: &6/res area replace [název residence] [areaID]'
              - Nejprve musíš vybrat 2 body.
              - Vyměnění oblasti residence tě může stát peníze, pokud je cílová oblast
                větší.
        give:
          Description: Dá hráči residenci.
          Info:
          - '&ePoužití: &6/res give <název residence> [jméno hráče]'
          - Dá tvoji residenci určenému hráči
        renamearea:
          Description: Přejmenovat oblast residence
          Info:
          - '&ePoužití: &6/res removeworld [název residence] [starý název oblasti]
            [nový název oblasti]'
        contract:
          Description: Zmenšit residenci ve směru, kam koukáš
          Info:
          - '&ePoužití: &6/res contract (název residence [počet])'
          - Zmenší residenci ve směru, kam se koukáš
          - Zadání jména residence je volitelné
        check:
          Description: Kontrola stavu tvé vlajky
          Info:
          - '&ePoužití: &6/res check [název residence] [vlajka] (jméno hráče)'
        gset:
          Description: Nastavit vlajky pro specifickou skupinu.
          Info:
          - '&ePoužití: &6/res gset <název residence> [skupina] [vlajka] [true/false/remove]'
          - Pro zobrazení seznamu vlajek napiš /res flags ?
        list:
          Description: Zobrazit seznam residencí
          Info:
          - '&ePoužití: &6/res list <jméno hráče> <strana>'
          - Zobrazit všechny residence, které hráč vlastní (krome skrytych).
          - Pro zobrazení residenci všech hráčů použij /res listall.
        version:
          Description: Verze residence
          Info:
          - '&ePoužití: &6/res version'
        tool:
          Description: Zvýraznit residenci a informaci jména nástroje
          Info:
          - '&ePoužití: &6/res tool'
        pdel:
          Description: Odebrat hráče z residence.
          Info:
          - '&ePoužití: &6/res pdel <název residence> [jméno hráče]'
          - Odebere práva hráče residence
        market:
          Description: Koupit, prodat nebo pronajmout residenci.
          Info:
          - '&ePoužití: &6/res market ? pro více informací'
          SubCommands:
            Info:
              Description: Zobrazit informace o ekonomii residence
              Info:
              - '&ePoužití: &6/res market Info [název residence]'
              - Zobrazit, jestli je residence na prodej nebo k pronájmu, popř. její
                cenu
            list:
              Description: Zobrazit seznam residencí na prodej a k pronájmu
              Info:
              - '&ePoužití: &6/res market list'
              SubCommands:
                rent:
                  Description: Seznam residencí k pronájmu.
                  Info:
                  - '&ePoužití: &6/res market list rent'
                sell:
                  Description: Seznam residencí k prodeji.
                  Info:
                  - '&ePoužití: &6/res market list sell'
            sell:
              Description: Prodat residenci
              Info:
              - '&ePoužití: &6/res market sell [název residence] [částka]'
              - Nastaví residenci k prodeji za [částka] peněz.
              - Jiný hráč může koupit residenci pomocí /res market buy [název residence]
            sign:
              Description: Nastavit obchodní ceduli
              Info:
              - '&ePoužití: &6/res market sign [název residence]'
              - Nastaví ceduli na kterou koukáš, jako obchodní
            buy:
              Description: Koupit residenci
              Info:
              - '&ePoužití: &6/res market buy [název residence]'
              - Koupí residenci, pokud je na prodej
            unsell:
              Description: Stáhnout residenci z prodeje
              Info:
              - '&ePoužití: &6/res market unsell [název residence]'
            rent:
              Description: Pronajmout si cizí residenci
              Info:
              - '&ePoužití: &6/res market rent [název residence] <autoProdloužení>'
              - Pronajmout si residenci. autoProdloužení může být true(ano)/false(ne).
                Pokud true, bude docházet k automatickému prodlužování nájmu pokud
                to vlastník schválil a to do doby ukončení.
            rentable:
              Description: Pronajmout vlastní residenci
              Info:
              - '&ePoužití: &6/res market rentable [název residence] [cena] [dní]
                <autoProdloužení>'
              - Pronajme vlastní residenci za [cena] na [dní]. Pokud je <autoProdloužení>
                true, pronájem bude automaticky prodlužován.
            autopay:
              Description: Nastavit residenci Autoplatbu
              Info:
              - '&ePoužití: &6/res market autopay <název residence> [true/false]'
            payrent:
              Description: Zaplatit nájem za residenci
              Info:
              - '&ePoužití: &6/res market payrent <název residence>'
            confirm:
              Description: Potvrdit ukončení nájmu
              Info:
              - '&ePoužití: &6/res market confirm'
            unrent:
              Description: Ukončit nájem či pronájem residence.
              Info:
              - '&ePoužití: &6/res market unrent [název residence]'
              - Pokud jsi nájemce, tento příkaz zruší tvůj nájem.
              - Pokud jsi vlastník, tento příkaz zruší pronájem nájemci.
        rc:
          Description: Připojit se do residenčního chatu.
          Info:
          - '&ePoužití: &6/res rc (název residence)'
          SubCommands:
            leave:
              Description: Opustit stávající residenční chat
              Info:
              - '&ePoužití: &6/res rc leave'
              - Pokud jsi v residenčním chatu, tímto příkazem ho opustíš.
            setcolor:
              Description: Nastavit barvu residenčního chatu
              Info:
              - '&ePoužití: &6/res rc setcolor [barevný kód]'
              - Nastaví barvu residenčního chatu
            setprefix:
              Description: Nastavit prefix residenčního chatu
              Info:
              - '&ePoužití: &6/res rc setprefix [novýPrefix]'
              - Nastaví prefix residenčního chatu
            kick:
              Description: Vyhodit hráče z residenčního chatu
              Info:
              - '&ePoužití: &6/res rc kick [jméno hráče]'
              - Vyhodí hráče z residenčního chatu
        expand:
          Description: Rozšířit residenci ve směru, kam koukáš
          Info:
          - '&ePoužití: &6/res expand (název residence) [počet]'
          - Rozšířit residenci ve směru, kam se koukáš.
          - Zadání jména residence je volitelné
        compass:
          Description: Nastavit kompas ukazující směrem k residenci
          Info:
          - '&ePoužití: &6/res compass <název residence>'
        lists:
          Description: Přednastavena práva
          Info:
          - Přednastavena práva residence
          SubCommands:
            add:
              Description: Přidat seznam
              Info:
              - '&ePoužití: &6/res lists add <název seznamu>'
            remove:
              Description: Odstranit seznam
              Info:
              - '&ePoužití: &6/res lists remove <název seznamu>'
            apply:
              Description: Použít seznam na residenci
              Info:
              - '&ePoužití: &6/res lists apply <název seznamu> <název residence>'
            set:
              Description: Nastavit vlajku
              Info:
              - '&ePoužití: &6/res lists set <název seznamu> <vlajka> <hodnota>'
            pset:
              Description: Nastavit hráčskou vlajku
              Info:
              - '&ePoužití: &6/res lists pset <název seznamu> <jméno hráče> <vlajka>
                <hodnota>'
            gset:
              Description: Nastavit skupinovou vlajku
              Info:
              - '&ePoužití: &6/res lists view <název seznamu>'
            view:
              Description: Zobrazit seznam.
              Info:
              - '&ePoužití: &6/res lists view <název seznamu>'
        reset:
          Description: Resetovat residenci
          Info:
          - '&ePoužití: &6/res reset <název residence>'
          - Resetuje vlajky residence do továrního nastavení.
        listhidden:
          Description: Zobrazit seznam skrytých residencí
          Info:
          - '&ePoužití: &6/res listhidden <jméno hráče> <strana>'
          - Zobrazit seznam skrytých residencí určitého hráče.
        setmain:
          Description: Nastavit definovanou rezidenci jako hlavní, aby se v chatu
            zobrazovala jako předpona
          Info:
          - '&ePoužití: &6/res setmain (název residence)'
          - Nastaví definovanou rezidenci jako hlavní.
        server:
          Description: Předat residenci serveru
          Info:
          - '&ePoužití: &6/resadmin server [název residence]'
          - Nastaví server jako vlastníka residence.
        rt:
          Description: Teleportovat se na náhodnou lokaci ve světě.
          Info:
          - '&ePoužití: &6/res rt'
          - Teleportuje tě na náhodnou lokaci.
        mirror:
          Description: Zrcadlit vlajky
          Info:
          - '&ePoužití: &6/res mirror ?'
        shop:
          Description: Spravovat obchod s residencemi
          Info:
          - Spravovat funkce obchodu
          SubCommands:
            list:
              Description: Zobrazit seznam obchodovatelných residencí
              Info:
              - '&ePoužití: &6/res shop list'
              - Zobrazit celý seznam obchodovatelných residencí
            vote:
              Description: Hlasovat v residenčním obchodu
              Info:
              - '&ePoužití: &6/res shop vote <název residence> [hodnota]'
              - Hlasování pro aktuální nebo definovanou residenci
            like:
              Description: Lajknout obchod residence
              Info:
              - '&ePoužití: &6/res shop like <název residence>'
              - Lajkne obchod residence
            votes:
              Description: Zobrazit seznam hlasů obchodu
              Info:
              - '&ePoužití: &6/res shop votes <název residence> <strana>'
            likes:
              Description: Zobrazit počet lajků
              Info:
              - '&ePoužití: &6/res shop likes <název residence> <strana>'
              - Zobrazit plný seznam lajků
            setdesc:
              Description: Nastavit popis residenčního shopu
              Info:
              - '&ePoužití: &6/res shop setdesc [text]'
              - Nastaví popis residenčního shopu. Barvy jsou podporovány. Pro nový
                řádek použij /n
            createboard:
              Description: Vytvořit novou obchodní ceduli
              Info:
              - '&ePoužití: &6/res shop createboard [místo]'
              - Vytvoří novou obchodní ceduli. Místo - pozice, od které se začne plnit
                cedule
            deleteboard:
              Description: Smazat obchodní ceduli
              Info:
              - '&ePoužití: &6/res shop deleteboard'
              - Smaže ceduli kliknutím pravým tlačítkem na jednu z cedulek
        lset:
          Description: Měnit možnosti černé listiny a seznamu ignorovaných
          Info:
          - '&ePoužití: &6/res lset <název residence> [blacklist/ignorelist] [materiál]'
          - '&ePoužití: &6/res lset <název residence> Info'
          - Zakázání materiálu na černé listině brání jeho umístění v rezidenci.
          - Seznam ignorovaných materiálů způsobí, že konkrétní materiál nebude v
            residenci chráněn.
        pset:
          Description: Nastavení vlajek residence na určitého hráče.
          Info:
          - '&ePoužití: &6/res pset <název residence> [jméno hráče] [vlajka] [true/false/remove]'
          - '&ePoužití: &6/res pset <název residence> [jméno hráče] removeall'
          - Pro zobrazení všech vlajek, napiš /res flags ?
        show:
          Description: Zobrazit hranice residence
          Info:
          - '&ePoužití: &6/res show <název residence>'
        flags:
          Description: Seznam všech vlajek
          Info:
          - True vetšinou povoluje, False zakazuje
          SubCommands:
            anvil:
              Translated: anvil
              Description: Povolit/zakázat používání kovadliny v residenci
              Info:
              - '&ePoužití: &6/res set/pset <název residence> anvil true/false/remove'
            admin:
              Translated: admin
              Description: Povolit/zakázat hráči měnit vlajky v residenci
              Info:
              - '&ePoužití: &6/res pset <název residence> [vlajka] true/false/remove'
            animalkilling:
              Translated: animalkilling
              Description: Povolit/zakázat zabíjení zvířat v residenci
              Info:
              - '&ePoužití: &6/res set/pset <název residence> [vlajka] true/false/remove'
            animals:
              Translated: animals
              Description: Povolit/zakázat spawn zvířat v residenci
              Info:
              - '&ePoužití: &6/res set <název residence> [vlajka] true/false/remove'
            anvilbreak:
              Translated: anvilbreak
              Description: Povolit/zakázat ničení kovadliny v residenci
              Info:
              - '&ePoužití: &6/res set <název residence> anvilbreak true/false/remove'
            backup:
              Translated: backup
              Description: Pokud je true, obnoví prvotní vzhled plochy (nutný WordEdit)
              Info:
              - '&ePoužití: &6/res set <název residence> backup true/false/remove'
            bank:
              Translated: bank
              Description: Povolit/zakázat použití banky residence
              Info:
              - '&ePoužití: &6/res set <název residence> [vlajka] true/false/remove'
            bed:
              Translated: bed
              Description: Povolit/zakázat použití postelí v residenci
              Info:
              - '&ePoužití: &6/res set/pset <název residence> [vlajka] true/false/remove'
            beacon:
              Translated: beacon
              Description: Povolit/zakázat použití majáku v residenci
              Info:
              - '&ePoužití: &6/res set/pset <název residence> beacon true/false/remove'
            brew:
              Translated: brew
              Description: Povolit/zakázat použití stojanu na vaření lektvarů v residenci
              Info:
              - '&ePoužití: &6/res set/pset <název residence> [vlajka] true/false/remove'
            build:
              Translated: build
              Description: Povolit/zakázat stavění v residenci
              Info:
              - '&ePoužití: &6/res set/pset <název residence> [vlajka] true/false/remove'
            burn:
              Translated: burn
              Description: Povolit/zakázat hoření mobů v residenci
              Info:
              - '&ePoužití: &6/res set <název residence> [vlajka] true/false/remove'
            button:
              Translated: button
              Description: Povolit/zakázat použití tlačítek v residenci
              Info:
              - '&ePoužití: &6/res set/pset <název residence> [vlajka] true/false/remove'
            cake:
              Translated: cake
              Description: Povolit/zakázat hráčům jíst dort v residenci
              Info:
              - '&ePoužití: &6/res set/pset <název residence> [vlajka] true/false/remove'
            canimals:
              Translated: canimals
              Description: Povolit/zakázat spawn vlastních zvířat v residenci
              Info:
              - '&ePoužití: &6/res set <název residence> [vlajka] true/false/remove'
            chorustp:
              Translated: chorustp
              Description: Povolit/zakázat teleportaci s plodem chorusu v residenci
                fruit
              Info:
              - '&ePoužití: &6/res set/pset <název residence> chorustp true/false/remove'
            chat:
              Translated: chat
              Description: Povolit připojení do chatu residence
              Info:
              - '&ePoužití: &6/res set <název residence> [vlajka] true/false/remove'
            cmonsters:
              Translated: cmonsters
              Description: Povolit/zakázat náhodný spawn monster v residenci
              Info:
              - '&ePoužití: &6/res set <název residence> [vlajka] true/false/remove'
            commandblock:
              Translated: commandblock
              Description: Povolit/zakázat používání command bloku v residenci
              Info:
              - '&ePoužití: &6/res set/pset <název residence> commandblock true/false/remove'
            command:
              Translated: command
              Description: Povolit/zakázat použití příkazu v residenci
              Info:
              - '&ePoužití: &6/res set/pset <název residence> [vlajka] true/false/remove'
            container:
              Translated: container
              Description: Povolit/zakázat přístup k truhlám, pecím, dispenserům,
                atd ... v residenci
              Info:
              - '&ePoužití: &6/res set/pset  <název residence> [vlajka] true/false/remove'
            coords:
              Translated: coords
              Description: Ukázat/skrýt koordináty residence
              Info:
              - '&ePoužití: &6/res set <název residence> coords true/false/remove'
            craft:
              Translated: craft
              Description: 'Dát vlajky: table, enchant, brew'
              Info:
              - '&ePoužití: &6/res set/pset <název residence> [vlajka] true/false/remove'
            creeper:
              Translated: creeper
              Description: Povolit/zakázat výbuch creeperu v residenci
              Info:
              - '&ePoužití: &6/res set <název residence> [vlajka] true/false/remove'
            dragongrief:
              Translated: dragongrief
              Description: Zabránit ničení bloků ender drakem v residenci
              Info:
              - '&ePoužití: &6/res set <název residence> [vlajka] true/false/remove'
            day:
              Translated: day
              Description: Nastavit den v residenci
              Info:
              - '&ePoužití: &6/res set <název residence> [vlajka] true/false/remove'
            dye:
              Translated: dye
              Description: Povolit/zakázat barvení ovcí v residenci
              Info:
              - '&ePoužití: &6/res set/pset <název residence> [vlajka] true/false/remove'
            damage:
              Translated: damage
              Description: Všem povolit/zakázat poškození útokem v residenci
              Info:
              - '&ePoužití: &6/res set <název residence> [vlajka] true/false/remove'
            decay:
              Translated: decay
              Description: Povolit/zakázat rozpad listí v residenci
              Info:
              - '&ePoužití: &6/res set <název residence> decay true/false/remove'
            destroy:
              Translated: destroy
              Description: Povolit/zakázat ničení bloků v residenci, ignoruje vlajku
                build
              Info:
              - '&ePoužití: &6/res set/pset <název residence> [vlajka] true/false/remove'
            dryup:
              Translated: dryup
              Description: Povolit/zakázat vysychání např houbou v residenci
              Info:
              - '&ePoužití: &6/res set <název residence> dryup true/false/remove'
            diode:
              Translated: diode
              Description: Povolit/zakázat použití ruditového zesilovače v residenci
              Info:
              - '&ePoužití: &6/res set/pset <název residence> [vlajka] true/false/remove'
            door:
              Translated: door
              Description: Povolit/zakázat použití dveří v residenci
              Info:
              - '&ePoužití: &6/res set/pset <název residence> [vlajka] true/false/remove'
            egg:
              Translated: egg
              Description: Povolit/zakázat použití dračího vejce v residenci
              Info:
              - '&ePoužití: &6/res set/pset <název residence> egg true/false/remove'
            enchant:
              Translated: enchant
              Description: Povolit/zakázat použití čarovného stolu v residenci
              Info:
              - '&ePoužití: &6/res set/pset <název residence> [vlajka] true/false/remove'
            explode:
              Translated: explode
              Description: Povolit/zakázat výbuchy v residenci
              Info:
              - '&ePoužití: &6/res set <název residence> [vlajka] true/false/remove'
            enderpearl:
              Translated: enderpearl
              Description: Povolit/zakázat použití enderperly v residenci
              Info:
              - '&ePoužití: &6/res set/pset <název residence> [vlajka] true/false/remove'
            fallinprotection:
              Translated: fallinprotection
              Description: Povolit/zakázat ochranu před padajícím blokem v residenci
              Info:
              - '&ePoužití: &6/res set <název residence> fallinprotection true/false/remove'
            falldamage:
              Translated: falldamage
              Description: Povolit/zakázat ochranu před pádem v residenci
              Info:
              - '&ePoužití: &6/res set <název residence> falldamage true/false/remove'
            feed:
              Translated: feed
              Description: Povolit/zakázat ochranu před hladem v residenci
              Info:
              - '&ePoužití: &6/res set <název residence> [vlajka] true/false/remove'
            friendlyfire:
              Translated: friendlyfire
              Description: Povolit/zakázat střelbu do kamarádů v residenci
              Info:
              - '&ePoužití: &6/res pset <název residence> friendlyfire true/false/remove'
            fireball:
              Translated: fireball
              Description: Povolit/zakázat ohnivou kouli v residenci
              Info:
              - '&ePoužití: &6/res set <název residence> [vlajka] true/false/remove'
            firespread:
              Translated: firespread
              Description: Povolit/zakázat šíření ohně v residenci
              Info:
              - '&ePoužití: &6/res set <název residence> [vlajka] true/false/remove'
            flowinprotection:
              Translated: flowinprotection
              Description: Povolit/zakázat tok kapalin z venčí do residence
              Info:
              - '&ePoužití: &6/res set <název residence> flowinprotection true/false/remove'
            flow:
              Translated: flow
              Description: Povolit/zakázat tok kapalin v residenci
              Info:
              - '&ePoužití: &6/res set <název residence> [vlajka] true/false/remove'
            flowerpot:
              Translated: flowerpot
              Description: Povolit/zakázat použití květináče v residenci
              Info:
              - '&ePoužití: &6/res set/pset <název residence> flowerpot true/false/remove'
            grow:
              Translated: grow
              Description: Povolit/zakázat sázení v residenci
              Info:
              - '&ePoužití: &6/res set <název residence> grow true/false/remove'
            glow:
              Translated: glow
              Description: Zapnout/vypnout záři kolem hráčů v residenci
              Info:
              - '&ePoužití: &6/res set <název residence> glow true/false/remove'
            hotfloor:
              Translated: hotfloor
              Description: Povolit/zakázat poškození od magma bloků v residenci
              Info:
              - '&ePoužití: &6/res set <název residence> hotfloor true/false/remove'
            hidden:
              Translated: hidden
              Description: Povolit/zakázat zobrazení residence při použití příkazů
                list a listall
              Info:
              - '&ePoužití: &6/res set <název residence> [vlajka] true/false/remove'
            hook:
              Translated: hook
              Description: Povolit/zakázat chycení prutem všech entit v residenci
              Info:
              - '&ePoužití: &6/res set/pset <název residence> hook true/false/remove'
            healing:
              Translated: healing
              Description: Zapne/vypne regeneraci života v residenci
              Info:
              - '&ePoužití: &6/res set <název residence> [vlajka] true/false/remove'
            iceform:
              Translated: iceform
              Description: Povolit/zakázat tvorbu ledu v residenci
              Info:
              - '&ePoužití: &6/res set <název residence> iceform true/false/remove'
            icemelt:
              Translated: icemelt
              Description: Povolit/zakázat tání ledu v residenci
              Info:
              - '&ePoužití: &6/res set <název residence> icemelt true/false/remove'
            ignite:
              Translated: ignite
              Description: Povolit/zakázat vzplanutí v residenci
              Info:
              - '&ePoužití: &6/res set/pset <název residence> [vlajka] true/false/remove'
            itemdrop:
              Translated: itemdrop
              Description: Povolit/zakázat odhození předmětů v residenci
              Info:
              - '&ePoužití: &6/res set/pset <název residence> itemdrop true/false/remove'
            itempickup:
              Translated: itempickup
              Description: Povolit/zakázat sbírání předmětů ze země v residenci
              Info:
              - '&ePoužití: &6/res set/pset <název residence> itempickup true/false/remove'
            jump2:
              Translated: jump2
              Description: Povolit/zakázat skákání nad 2 bloky v residenci
              Info:
              - '&ePoužití: &6/res set <název residence> jump2 true/false/remove'
            jump3:
              Translated: jump3
              Description: Povolit/zakázat skákání nad 3 bloky v residenci
              Info:
              - '&ePoužití: &6/res set <název residence> jump3 true/false/remove'
            keepinv:
              Translated: keepinv
              Description: Povolit/zakázat ponechání inventáře hráčům po smrti v residenci
              Info:
              - '&ePoužití: &6/res set/pset <název residence> [vlajka] true/false/remove'
            keepexp:
              Translated: keepexp
              Description: Povolit/zakázat ponechání XP hráčům po smrti v residenci
              Info:
              - '&ePoužití: &6/res set/pset <název residence> [vlajka] true/false/remove'
            lavaflow:
              Translated: lavaflow
              Description: Povolit/zakázat tok lávy v residenci, ignoruje flow vlajku
              Info:
              - '&ePoužití: &6/res set <název residence> [vlajka] true/false/remove'
            leash:
              Translated: leash
              Description: Povolit/zakázat použití vodítka v residenci
              Info:
              - '&ePoužití: &6/res set/pset <název residence> [vlajka] true/false/remove'
            lever:
              Translated: lever
              Description: Povolit/zakázat použití páčky v residenci
              Info:
              - '&ePoužití: &6/res set/pset <název residence> [vlajka] true/false/remove'
            mobexpdrop:
              Translated: mobexpdrop
              Description: Povolit/zakázat XP z mobů v residenci
              Info:
              - '&ePoužití: &6/res set <název residence> [vlajka] true/false/remove'
            mobitemdrop:
              Translated: mobitemdrop
              Description: Povolit/zakázat předměty z mobů v residenci
              Info:
              - '&ePoužití: &6/res set <název residence> [vlajka] true/false/remove'
            mobkilling:
              Translated: mobkilling
              Description: Povolit/zakázat zabíjení monster v residenci
              Info:
              - '&ePoužití: &6/res set/pset <název residence> [vlajka] true/false/remove'
            monsters:
              Translated: monsters
              Description: Povolit/zakázat spawn monster v residenci
              Info:
              - '&ePoužití: &6/res set <název residence> [vlajka] true/false/remove'
            move:
              Translated: move
              Description: Povolit/zakázat pohyb v residenci
              Info:
              - '&ePoužití: &6/res set/pset <název residence> [vlajka] true/false/remove'
            nanimals:
              Translated: nanimals
              Description: Povolit/zakázat přírodní spawnování zvířat v residenci
              Info:
              - '&ePoužití: &6/res set <název residence> [vlajka] true/false/remove'
            nmonsters:
              Translated: nmonsters
              Description: Povolit/zakázat přírodní spawnování monster v residenci
              Info:
              - '&ePoužití: &6/res set <název residence> [vlajka] true/false/remove'
            night:
              Translated: night
              Description: Nastaví noc v residenci
              Info:
              - '&ePoužití: &6/res set <název residence> [vlajka] true/false/remove'
            nofly:
              Translated: nofly
              Description: Povolit/zakázat létání při vstupu do residence
              Info:
              - '&ePoužití: &6/res set/pset <název residence> [vlajka] true/false/remove'
            fly:
              Translated: fly
              Description: Zapne/vypne létání pro hráče v residenci
              Info:
              - '&ePoužití: &6/res set/pset <název residence> fly true/false/remove'
            nomobs:
              Translated: nomobs
              Description: Povolit/zakázat monstrům v proniknutí do residence
              Info:
              - '&ePoužití: &6/res set <název residence> [vlajka] true/false/remove'
            note:
              Translated: note
              Description: Povolit/zakázat použití note bloků v residenci
              Info:
              - '&ePoužití: &6/res set/pset <název residence> [vlajka] true/false/remove'
            nodurability:
              Translated: nodurability
              Description: Povolit/zakázat opotřebování nástrojů v residenci
              Info:
              - '&ePoužití: &6/res set <název residence> [vlajka] true/false/remove'
            overridepvp:
              Translated: overridepvp
              Description: Povolit/zakázat PVP v residenci absolutně nad ostatními
                pluginy
              Info:
              - '&ePoužití: &6/res set <název residence> [vlajka] true/false/remove'
            pressure:
              Translated: pressure
              Description: Povolit/zakázat použití nášlapné desky v residenci
              Info:
              - '&ePoužití: &6/res set/pset <název residence> [vlajka] true/false/remove'
            piston:
              Translated: piston
              Description: Povolit/zakázat funkci pistonu v residenci
              Info:
              - '&ePoužití: &6/res set <název residence> [vlajka] true/false/remove'
            pistonprotection:
              Translated: pistonprotection
              Description: Povolit/zakázat funkci pistonu do nebo z residence
              Info:
              - '&ePoužití: &6/res set <název residence> pistonprotection true/false/remove'
            place:
              Translated: place
              Description: Povolit/zakázat pouze pokládání bloků v residenci
              Info:
              - '&ePoužití: &6/res set/pset <název residence> [vlajka] true/false/remove'
            pvp:
              Translated: pvp
              Description: Povolit/zakázat pvp v residenci
              Info:
              - '&ePoužití: &6/res set/pset <název residence> [vlajka] true/false/remove'
            rain:
              Translated: rain
              Description: Povolit/zakázat déšť v residenci
              Info:
              - '&ePoužití: &6/res set <název residence> rain true/false/remove'
            redstone:
              Translated: redstone
              Description: 'Dát vlajky: lever, diode, button, pressure, note'
              Info:
              - '&ePoužití: &6/res set/pset <název residence> [vlajka] true/false/remove'
            respawn:
              Translated: respawn
              Description: Povolit/zakázat automatický respawn hráče v residenci
              Info:
              - '&ePoužití: &6/res set <název residence> [vlajka] true/false/remove'
            riding:
              Translated: riding
              Description: Povolit/zakázat jezdit na koni
              Info:
              - '&ePoužití: &6/res set/pset <název residence> riding true/false/remove'
            shoot:
              Translated: shoot
              Description: Povolit/zakázat střelbu projektilů v residenci
              Info:
              - '&ePoužití: &6/res set <název residence> shoot true/false/remove'
            sun:
              Translated: sun
              Description: Zapnout/vypnout slunečné počasí v residenci
              Info:
              - '&ePoužití: &6/res set <název residence> sun true/false/remove'
            shop:
              Translated: shop
              Description: Přidat residenci do obchodního seznamu
              Info:
              - '&ePoužití: &6/res set <název residence> [vlajka] true/false/remove'
            snowtrail:
              Translated: snowtrail
              Description: Povolit/zakázat sněhulákům sněhovou stopu v residenci
              Info:
              - '&ePoužití: &6/res set <název residence> [vlajka] true/false/remove'
            spread:
              Translated: spread
              Description: Povolit/zakázat růstu bloků v residenci
              Info:
              - '&ePoužití: &6/res set <název residence> spread true/false/remove'
            snowball:
              Translated: snowball
              Description: Povolit/zakázat odhození při zásahu sněhovou koulí v residenci
              Info:
              - '&ePoužití: &6/res set <název residence> snowball true/false/remove'
            sanimals:
              Translated: sanimals
              Description: Povolit/zakázat zvířecí spawnery a spawn vejce v residenci
              Info:
              - '&ePoužití: &6/res set <název residence> [vlajka] true/false/remove'
            shear:
              Translated: shear
              Description: Povolit/zakázat stříhání ovcí v residenci
              Info:
              - '&ePoužití: &6/res set/pset <název residence> [vlajka] true/false/remove'
            smonsters:
              Translated: smonsters
              Description: Povolit/zakázat monster spawnery a spawn vejce v residenci
              Info:
              - '&ePoužití: &6/res set <název residence> [vlajka] true/false/remove'
            subzone:
              Translated: subzone
              Description: Povolit/zakázat hráči dělat subzóny v residenci
              Info:
              - '&ePoužití: &6/res pset <název residence> [vlajka] true/false/remove'
            title:
              Translated: title
              Description: Zapnout/vypnout uvítací hlášku při vstupu do/výstupu z
                residence
              Info:
              - '&ePoužití: &6/res set <název residence> title true/false/remove'
            table:
              Translated: table
              Description: Povolit/zakázat použití pracovního stolu v residenci
              Info:
              - '&ePoužití: &6/res set/pset <název residence> [vlajka] true/false/remove'
            tnt:
              Translated: tnt
              Description: Povolit/zakázat tnt v residenci
              Info:
              - '&ePoužití: &6/res set <název residence> [vlajka] true/false/remove'
            tp:
              Translated: tp
              Description: Povolit/zakázat teleport na residenci
              Info:
              - '&ePoužití: &6/res set/pset <název residence> [vlajka] true/false/remove'
            trade:
              Translated: trade
              Description: Povolit/zakázat obchodování s vesničany v residenci
              Info:
              - '&ePoužití: &6/res set/pset <název residence> [vlajka] true/false/remove'
            trample:
              Translated: trample
              Description: Povolit/zakázat pošlapání obilí v residenci
              Info:
              - '&ePoužití: &6/res set/pset <název residence> [vlajka] true/false/remove'
            trusted:
              Translated: trusted
              Description: 'Nastavit vlajky: build, use, move, container a tp'
              Info:
              - '&ePoužití: &6/res pset <název residence> [vlajka] true/false/remove'
            use:
              Translated: use
              Description: Povolit/zakázat použití dveří, tlačítek, páček, atd. v
                residenci
              Info:
              - '&ePoužití: &6/res set/pset <název residence> [vlajka] true/false/remove'
            vehicledestroy:
              Translated: vehicledestroy
              Description: Povolit/zakázat ničeni dopravních prostředků v residenci
              Info:
              - '&ePoužití: &6/res set/pset <název residence> [vlajka] true/false/remove'
            witherspawn:
              Translated: witherspawn
              Description: Povolit/zakázat spawnování withera v residenci
              Info:
              - '&ePoužití: &6/res set <název residence> witherspawn true/false/remove'
            phantomspawn:
              Translated: phantomspawn
              Description: Povolit/zakázat spawnování fantoma v residenci
              Info:
              - '&ePoužití: &6/res set <název residence> phantomspawn true/false/remove'
            witherdamage:
              Translated: witherdamage
              Description: Povolit/zakázat poškození witherem v residenci
              Info:
              - '&ePoužití: &6/res set <název residence> [vlajka] true/false/remove'
            witherdestruction:
              Translated: witherdestruction
              Description: Povolit/zakázat ničení bloků witherem v residenci
              Info:
              - '&ePoužití: &6/res set <název residence> witherdestruction true/false/remove'
            waterflow:
              Translated: waterflow
              Description: Povolit/zakázat tok vody v residenci, ignoruje flow vlajku
              Info:
              - '&ePoužití: &6/res set <název residence> [vlajka] true/false/remove'
            wspeed1:
              Translated: wspeed1
              Description: Povolit/zakázat hráči rychlé běhání %1 v residenci
              Info:
              - '&ePoužití: &6/res set <název residence> wspeed1 true/false/remove'
            wspeed2:
              Translated: wspeed2
              Description: Povolit/zakázat hráči rychlé běhání %1 v residenci
              Info:
              - '&ePoužití: &6/res set <název residence> wspeed2 true/false/remove'
            bucket:
              Description: Povolit/zakázat použití kýble v residenci
              Info:
              - '&ePoužití: &6/res set/pset <název residence> [vlajka] true/false/remove'
            bucketfill:
              Description: Povolit/zakázat naplnění kýble v residenci
              Info:
              - '&ePoužití: &6/res set/pset <název residence> [vlajka] true/false/remove'
            bucketempty:
              Description: Povolit/zakázat vyprázdnění kýble v residenci
              Info:
              - '&ePoužití: &6/res set/pset <název residence> [vlajka] true/false/remove'
        remove:
          Description: Odstranit residenci.
          Info:
          - '&ePoužití: &6/res remove <název residence>'
        signupdate:
          Description: Aktualizovat cedulky residence.
          Info:
          - '&ePoužití: &6/res signupdate'
        current:
          Description: Zobrazit residenci, ve které se nacházíš.
          Info:
          - '&ePoužití: &6/res current'
        reload:
          Description: Aktualizovat soubory config,lang,groups,flags
          Info:
          - '&ePoužití: &6/res reload [config/lang/groups/flags]'
        setowner:
          Description: Změnit vlastníka residence.
          Info:
          - '&ePoužití: &6/resadmin setowner [název residence] [jméno hráče]'
        unstuck:
          Description: Teleportovat z residence
          Info:
          - '&ePoužití: &6/res unstuck'
        subzone:
          Description: Vytvořit subzóny v residenci.
          Info:
          - '&ePoužití: &6/res subzone <jméno residence> [jméno subzony]'
          - Pokud jméno residence není zadané, použije se jméno residence, ve které
            se nacházíš.
        removeworld:
          Description: Odstranit všechny residence světa.
          Info:
          - '&ePoužití: &6/res removeworld [svet]'
          - Musí být spuštěno z konzole.
        limits:
          Description: Zobrazit mé limity.
          Info:
          - '&ePoužití: &6/res limits'
          - Zobrazí tvá omezení týkající se rezidencí.
        set:
          Description: Nastavení vlajek residence.
          Info:
          - '&ePoužití: &6/res set <název residence> [vlajka] [true/false/remove]'
          - Použij &6/res flags ? &cpro zobrazení všech dostupných vlajek
          - Tyto vlajky se vztahují na všechny hráče. (pokud chceš jenom na jednoho,
            zkus &6/res pset ?&c)
        clearflags:
          Description: Odstranění všech vlajek z residence
          Info:
          - '&ePoužití: &6/res clearflags <název residence>'
        message:
          Description: Spravovat uvítací text při příchodu/odchodu z residence
          Info:
          - '&ePoužití: &6/res message <název residence> [enter/leave] [zpráva]'
          - Nastaví zprávu pro příchod (enter) nebo odchod (leave).
          - '&ePoužití: &6/res message <název residence> remove [enter/leave]'
          - Odstraní zprávu pro příchod (enter)/odchod (leave)
        command:
          Description: Povolit/zakázat příkazy v residenci
          Info:
          - '&ePoužití: &6/res command <název residence> <allow/block/list> <command>'
          - Zobrazí seznam, přidá nebo odebere povolení k užití příkazů v residenci
          - Použij podtržítko _ při přidání příkazu s více/mezi proměnnými
        confirm:
          Description: Potvrdit odstranění residence.
          Info:
          - '&ePoužití: &6/res confirm'
          - Potvrdí odstranění residence.
        resadmin:
          Description: Povolit/zakázat residenčního admina
          Info:
          - '&ePoužití: &6/res resadmin [on/off]'
        tpset:
          Description: Nastavit pozici teleportace v residenci
          Info:
          - '&ePoužití: &6/res tpset'
          - Nastaví teleportační pozici residence tam, kde stojíš.
          - Musíš být vlastník residence, nebo mít vlajku +admin.
        tpconfirm:
          Description: Ignorovat nebezpečný teleport.
          Info:
          - '&ePoužití: &6/res tpconfirm'
          - Vynutí teleportaci i přes to, že může být nebezpečná.
        removeall:
          Description: Odstranit všechny residence hráče.
          Info:
          - '&ePoužití: &6/res removeall [jméno vlastníka]'
          - Odstraní všechny residence vlastněné určitým hráčem.'
          - Vyžaduje /resadmin pokud to používáš na kohokoliv jiného, než sebe.
        material:
          Description: Zkontrolovat jestli existuje materiál pomocí id
          Info:
          - '&ePoužití: &6/res material [materiál]'
        kick:
          Description: Vyhodit hráče z residence
          Info:
          - '&ePoužití: &6/res kick <jméno hráče>'
          - Musíš být vlastník residence.
          - Hráč by měl být online.
        sublist:
          Description: Zobrazit subzóny
          Info:
          - '&ePoužití: &6/res sublist <název residence> <strana>'
          - Zobrazí subzóny v residenci
        rename:
          Description: Přejmenovat residenci.
          Info:
          - '&ePoužití: &6/res rename [StarýNázev] [NovýNázev]'
          - Musíš být vlastník residence.
          - Jméno nesmí být už použité.
        setallfor:
          Description: Nastavit vlajku určitému vlastníkovi pro všechny jeho residence
          Info:
          - '&ePoužití: &6/res setallfor [jméno hráče] [vlajka] [true/false/remove]'
        lease:
          Description: Spravovat nájmy v residenci
          Info:
          - '&ePoužití: &6/res lease [renew/cost] [název residence]'
          - /res lease cost ukáže náklady na obnovení rezidenčního pronájmu.
          - /res lease renew obnoví residenci za předpokladu, že máš dost peněz.
          SubCommands:
            set:
              Description: Nastavit dobu pronájmu
              Info:
              - '&ePoužití: &6/resadmin lease set [název residence] [#days/infinite]'
              - Nastaví dobu pronájmu na určitý počet dní nebo nekonečně dlouho.
            renew:
              Description: Obnovit dobu pronájmu
              Info:
              - '&ePoužití: &6/resadmin lease renew <název residence>'
              - Obnoví dobu pronájmu zadané residence.
            list:
              Description: Vypsat seznam pronájmu residence
              Info:
              - '&ePoužití: &6/resadmin lease list <název residence> <page>'
              - Vypíše všechny doby pronájmu subzón
            expires:
              Description: Zobrazit koncové datum pronájmu
              Info:
              - '&ePoužití: &6/resadmin lease expires <název residence>'
              - Zobrazí kdy končí pronájem residence.
            cost:
              Description: Zobrazit cenu za obnovení pronájmu
              Info:
              - '&ePoužití: &6/resadmin lease cost <název residence>'
              - Zobrazit kolik peněz potřebuješ k obnovení nájmu residence.
        tp:
          Description: Teleportovat se do residence.
          Info:
          - '&ePoužití: &6/res tp [název residence]'
          - Teleportuje tě do residence. Ta musí mít nastavenou vlajku +tp, popr musíš
            být vlastník.
          - Také na to musíš mít práva.
        setall:
          Description: Nastavit vlajku pro všechny residence
          Info:
          - '&ePoužití: &6/res setall [flag] [true/false/remove]'
        resreload:
          Description: Obnovit residenci.
          Info:
          - '&ePoužití: &6/resreload'
        resload:
          Description: Načíst residenci z uloženého souboru.
          Info:
          - '&ePoužití: &6/resload'
          - Pozor příkaz před použitím neukládá residenci.
          - Načti residenci ze souboru po provedených změnách.
