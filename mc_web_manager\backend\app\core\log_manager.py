"""
MC Web Manager - 日志管理模块
实时日志读取、WebSocket推送和日志搜索功能
"""

import asyncio
import os
import time
from pathlib import Path
from typing import List, Dict, Any, Optional, AsyncGenerator
from datetime import datetime
import logging
import re
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
import json

# 导入配置
import sys
backend_dir = Path(__file__).parent.parent.parent
if str(backend_dir) not in sys.path:
    sys.path.insert(0, str(backend_dir))

from config.settings import mc_config

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class LogFileHandler(FileSystemEventHandler):
    """日志文件变化监听器"""
    
    def __init__(self, log_manager):
        self.log_manager = log_manager
        
    def on_modified(self, event):
        if not event.is_directory and event.src_path.endswith('latest.log'):
            # 日志文件被修改，通知管理器
            asyncio.create_task(self.log_manager.handle_log_update())

class LogManager:
    """日志管理器 - 处理服务器日志的读取、监控和推送"""
    
    def __init__(self):
        self.server_dir = Path(mc_config.SERVER_DIR)
        self.log_file = self.server_dir / "logs" / "latest.log"
        self.observers = []
        self.websocket_connections = set()
        self.last_position = 0
        self.log_buffer = []  # 缓存最近的日志行
        self.max_buffer_size = 1000  # 最大缓存行数
        
        # 启动文件监控
        self._start_file_watcher()
        
    def _start_file_watcher(self):
        """启动文件监控"""
        try:
            if self.log_file.parent.exists():
                observer = Observer()
                handler = LogFileHandler(self)
                observer.schedule(handler, str(self.log_file.parent), recursive=False)
                observer.start()
                self.observers.append(observer)
                logger.info(f"开始监控日志文件: {self.log_file}")
            else:
                logger.warning(f"日志目录不存在: {self.log_file.parent}")
        except Exception as e:
            logger.error(f"启动文件监控失败: {e}")
    
    def add_websocket_connection(self, websocket):
        """添加WebSocket连接"""
        self.websocket_connections.add(websocket)
        logger.info(f"新的WebSocket连接，当前连接数: {len(self.websocket_connections)}")
    
    def remove_websocket_connection(self, websocket):
        """移除WebSocket连接"""
        self.websocket_connections.discard(websocket)
        logger.info(f"WebSocket连接断开，当前连接数: {len(self.websocket_connections)}")
    
    async def handle_log_update(self):
        """处理日志文件更新"""
        try:
            new_lines = await self.read_new_log_lines()
            if new_lines:
                # 添加到缓存
                self.log_buffer.extend(new_lines)
                # 保持缓存大小
                if len(self.log_buffer) > self.max_buffer_size:
                    self.log_buffer = self.log_buffer[-self.max_buffer_size:]
                
                # 推送到所有WebSocket连接
                await self.broadcast_log_lines(new_lines)
        except Exception as e:
            logger.error(f"处理日志更新失败: {e}")
    
    async def read_new_log_lines(self) -> List[Dict[str, Any]]:
        """读取新的日志行"""
        try:
            if not self.log_file.exists():
                return []
            
            with open(self.log_file, 'r', encoding='utf-8', errors='ignore') as f:
                f.seek(self.last_position)
                new_content = f.read()
                self.last_position = f.tell()
            
            if not new_content.strip():
                return []
            
            lines = new_content.strip().split('\n')
            parsed_lines = []
            
            for line in lines:
                if line.strip():
                    parsed_line = self.parse_log_line(line)
                    parsed_lines.append(parsed_line)
            
            return parsed_lines
            
        except Exception as e:
            logger.error(f"读取日志文件失败: {e}")
            return []
    
    def parse_log_line(self, line: str) -> Dict[str, Any]:
        """解析日志行"""
        # Minecraft日志格式: [时间] [线程/级别]: 消息
        pattern = r'\[(\d{2}:\d{2}:\d{2})\] \[([^/]+)/([^\]]+)\]: (.+)'
        match = re.match(pattern, line)
        
        if match:
            time_str, thread, level, message = match.groups()
            return {
                'timestamp': datetime.now().strftime('%Y-%m-%d ') + time_str,
                'thread': thread,
                'level': level,
                'message': message,
                'raw': line,
                'parsed': True
            }
        else:
            # 无法解析的行，作为普通文本处理
            return {
                'timestamp': datetime.now().isoformat(),
                'thread': 'Unknown',
                'level': 'INFO',
                'message': line,
                'raw': line,
                'parsed': False
            }
    
    async def broadcast_log_lines(self, lines: List[Dict[str, Any]]):
        """向所有WebSocket连接广播日志行"""
        if not self.websocket_connections:
            return
        
        message = {
            'type': 'log_update',
            'data': lines
        }
        
        # 移除已断开的连接
        disconnected = set()
        
        for websocket in self.websocket_connections.copy():
            try:
                await websocket.send_text(json.dumps(message))
            except Exception as e:
                logger.warning(f"WebSocket发送失败: {e}")
                disconnected.add(websocket)
        
        # 清理断开的连接
        for websocket in disconnected:
            self.websocket_connections.discard(websocket)
    
    async def get_recent_logs(self, lines: int = 100) -> List[Dict[str, Any]]:
        """获取最近的日志行"""
        try:
            if not self.log_file.exists():
                return []
            
            # 如果缓存中有足够的日志，直接返回
            if len(self.log_buffer) >= lines:
                return self.log_buffer[-lines:]
            
            # 否则从文件读取
            with open(self.log_file, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
                self.last_position = f.tell()
            
            if not content.strip():
                return []
            
            all_lines = content.strip().split('\n')
            recent_lines = all_lines[-lines:] if len(all_lines) > lines else all_lines
            
            parsed_lines = []
            for line in recent_lines:
                if line.strip():
                    parsed_line = self.parse_log_line(line)
                    parsed_lines.append(parsed_line)
            
            # 更新缓存
            self.log_buffer = parsed_lines[-self.max_buffer_size:]
            
            return parsed_lines
            
        except Exception as e:
            logger.error(f"获取最近日志失败: {e}")
            return []
    
    async def search_logs(self, query: str, max_results: int = 100) -> List[Dict[str, Any]]:
        """搜索日志"""
        try:
            if not self.log_file.exists():
                return []
            
            with open(self.log_file, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            lines = content.strip().split('\n')
            matching_lines = []
            
            # 简单的文本搜索
            query_lower = query.lower()
            
            for line in lines:
                if query_lower in line.lower():
                    parsed_line = self.parse_log_line(line)
                    matching_lines.append(parsed_line)
                    
                    if len(matching_lines) >= max_results:
                        break
            
            return matching_lines
            
        except Exception as e:
            logger.error(f"搜索日志失败: {e}")
            return []
    
    def cleanup(self):
        """清理资源"""
        for observer in self.observers:
            observer.stop()
            observer.join()
        self.observers.clear()
        self.websocket_connections.clear()

# 创建全局日志管理器实例
log_manager = LogManager()
