
# 这些短语支持完整的颜色(十六进制)代码和一些变量.

# 请记住,某些变量对于某些行可能不起作用.

# 只需将它们保留在当前位置,一切都会没问题 :)

# 有些行可以设置全局变量.对将受影响的玩家.例如,对于执行命令 /heal Zrips 的玩家,将使用 Zrips 的数据作为变量

# [serverName] 显示服务器名称

# [playerName] 显示目标玩家名称

# [playerDisplayName] 显示目标玩家显示名称

# [lvl] 显示目标玩家等级

# [exp] 显示目标玩家总经验

# [hp] 显示目标玩家生命值

# [maxHp] 显示目标玩家最大生命值

# [hunger] 显示目标玩家饥饿水平

# [gameMode] 显示目标玩家游戏模式

# [prefix] 显示目标玩家前缀(如果可能)

# [suffix] 显示目标玩家后缀(如果可能)

# 发送者是执行命令的控制台或玩家.例如,Zrips 执行 /heal Zhax,然后将使用 Zrips 的数据

# [senderName] 显示发送者玩家名称

# [senderDisplayName] 显示发送者玩家显示名称

# [senderLvl] 显示发送者玩家等级

# [senderExp] 显示发送者玩家总经验

# [senderHp] 显示发送者玩家生命值

# [senderMaxHp] 显示发送者玩家最大生命值

# [senderHunger] 显示发送者玩家饥饿水平

# [senderGameMode] 显示发送者玩家游戏模式

# [senderPrefix] 显示发送者玩家前缀(如果可能)

# [senderSuffix] 显示发送者玩家后缀(如果可能)

# 来源是用于额外信息的玩家.例如,Zrips 执行 /tp Zhax Zrips,然后将使用 Zhax 的数据,因为其位置被用于新玩家的位置

# [sourceName] 显示来源玩家名称

# [sourceDisplayName] 显示来源玩家显示名称

# [sourceLvl] 显示来源玩家等级

# [sourceExp] 显示来源玩家总经验

# [sourceHp] 显示来源玩家生命值

# [sourceMaxHp] 显示来源玩家最大生命值

# [sourceHunger] 显示来源玩家饥饿水平

# [sourceGameMode] 显示来源玩家游戏模式

# [sourcePrefix] 显示来源玩家前缀(如果可能)

# [sourceSuffix] 显示来源玩家后缀(如果可能)

# ***********************************************

# 一些行支持将它们发送到自定义位置,比如动作栏、标题、副标题,甚至创建 JSON/可点击消息

# 如果行以 !toast! 开头,玩家将收到弹出消息(进度弹出窗口,仅适用于 1.12 及更高版本).一些额外变量可以用于定义类型和图标.例如:!toast! -t:goal -icon:paper Hello world!

# 如果行以 !actionbar! 开头,玩家将收到此变量之后定义的动作栏消息

# 如果行以 !actionbar:[seconds]! 开头,玩家将在定义的时间内收到动作栏消息

# 如果行以 !broadcast! 开头,则所有人都会收到消息.您可以添加额外的 !toast! !actionbar! 或 !title! 来将消息发送到特定位置,例如 !broadcast!!title!

# 如果一行以!customtext:[cTextName]!开头,则将按提供的名称获取自定义文本并显示给玩家.如果在!broadcast!之后使用,则在线的所有人都将收到此自定义文本消息

# 如果一行以!title!开头,则玩家将收到在此变量之后定义的标题消息,此外它还可以包含!subtitle!,这将添加副标题消息

# 如果一行以!bossbar:[name]-[timer]!开头,则玩家将收到在此变量之后定义的bossbar消息,此外,您可以定义此消息将可见的时间长度.您需要定义bossbar名称,它可以是任何您想要的名称,但具有相同名称的行将互相覆盖,以防止堆叠

# 要包含可点击的消息: <T>文本</T><H>悬停文本</H><C>命令</C><SC>建议文本</SC>

# <T>和</T>是必需的,其他是可选的

# 使用/n进行换行

# 要有多个JSON消息,请使用<Next>

# <C>以点击的玩家身份执行命令

# <CC>一次从控制台执行命令

# <CCI>每次玩家点击文本时从控制台执行命令

# <URL>包含URL

info:
  # Use !prefix! in any locale line to automatically include this prefix
  prefix: '&e[&aCMI&e] '
  NoPermission: '&c你没有权限!'
  CantHavePermission: '&c你不能拥有这个权限!'
  WrongGroup: '&c你所在的组别不对!'
  NoPlayerPermission: '&c[playerName] 没有 [permission] 权限'
  Ingame: '&c只能在游戏中使用!'
  NoInformation: '&c未找到相关信息!'
  Console: '&6服务器'
  FromConsole: '&c只能在控制台中使用!'
  NotOnline: '&c玩家不在线!'
  NobodyOnline: '&c当前没有玩家在线!'
  NoPlayer: '&c找不到名为[name]的玩家!'
  NoCommand: '&c没有这个命令!'
  cantFindCommand: '&5找不到 &7[%1]&5 命令,你是不是想找 &7[%2]&5?'
  nolocation: '&4找不到合适的位置'
  FeatureNotEnabled: '&c此功能未启用!'
  ModuleNotEnabled: '&c此模块未启用!'
  versionNotSupported: '&c服务器版本不支持此功能'
  spigotNotSupported: '&c你需要使用PaperSpigot分支类型服务器才能使用此功能'
  bungeeNoGo: '&c此功能在Bungee网络服务器上无法使用'
  clickToTeleport: '&e点击传送'
  UseMaterial: '&4请使用正确的材料名称!'
  IncorrectMaterial: '&4错误的材料名称!'
  UseInteger: '&4请输入数字!'
  UseBoolean: '&4请输入True或False!'
  NoLessThan: '&4数字不能小于[amount]!'
  NoMoreThan: '&4数值不能大于[amount]'
  NoWorld: '&4找不到名为[name]的世界!'
  IncorrectLocation: '&4位置定义错误!'
  Show: '&e展示'
  Remove: '&c移除'
  Back: '&e返回'
  Forward: '&e前进'
  Update: '&e更新'
  Save: '&e保存'
  Delete: '&c删除'
  Click: '&e点击'
  Preview: '&e预览'
  PasteOld: '&e粘贴旧的'
  ClickToPaste: '&e点击以粘贴到聊天框'
  CantTeleportWorld: '&e你无法传送到这个世界'
  CantTeleportNoWorld: '&c目标世界不存在.传送已取消'
  ClickToConfirmDelete: '&e点击确认移除 &6[name]'
  teleported: '&e你已被传送'
  PlayerSpliter: '&e----- &6[playerDisplayName] &e-----'
  Spliter: '&e--------------------------------------------------'
  SpliterValue: '&e------------------ &6[value] &e------------------'
  singleSpliter: '&e-'
  SpliterMiddle: ' &6[value] '
  ListSpliter: ', '
  ProgressBarFill: '&2▏'
  ProgressBarEmpty: '&e▏'
  nothingInHand: '!actionbar!&e你需要手持物品'
  nothingInHandLeather: '&e你需要手持皮革制品'
  nothingToShow: '&e无内容可展示'
  noItem: '&c找不到物品'
  dontHaveItem: '&c你的背包中没有 &6[amount]x [itemName]'
  wrongWorld: '&c无法在此世界中执行此操作'
  differentWorld: '&c不同的世界'
  HaveItem: '&c你的背包中有 &6[amount]x [itemName]'
  cantDoInGamemode: '&e你无法在当前游戏模式中执行此操作'
  cantDoForPlayer: '&e你无法对 &6[playerDisplayName] &e执行此操作'
  cantDoForYourSelf: '&e你无法对自己执行此操作'
  cantDetermineMobType: '&c无法从 &e[type] &c变量确定生物类型'
  cantRename: '!actionbar!&e你无法使用此名称'
  confirmRedefine: '&e点击确认重新定义'
  cantEdit: '&e你无法编辑此内容'
  wrongName: '&c错误的名称'
  unknown: 未知
  invalidName: '&c无效的名称'
  alreadyexist: '&4此名称已被使用'
  dontexist: '&4未找到此名称'
  worldDontExist: '&c目标世界无法访问.无法传送到该世界!'
  notSet: 未设置
  lookAtSign: '&e看着标识'
  lookAtBlock: '&e看着方块'
  lookAtEntity: '&e看着实体'
  noSpace: '&e没有足够的空间'
  notOnGround: '&e你不能在飞行时执行此操作'
  bungee:
    Online: '&6在线'
    Offline: '&c离线'
    not: '&c服务器不属于Bungee网络'
    noserver: '&c找不到该名称的服务器!'
    server: '&e服务器: &7[name]'
  variables:
    am: '&e上午'
    pm: '&e下午'
    Online: '&6在线'
    Offline: '&c离线'
    TrueColor: '&2'
    FalseColor: '&4'
    'True': '&6是'
    'False': '&c否'
    Enabled: '&6已启用'
    Disabled: '&c已禁用'
    survival: '&6生存模式'
    creative: '&6创造模式'
    adventure: '&6冒险模式'
    spectator: '&6旁观模式'
    flying: '&6飞行中'
    notflying: '&6未飞行'
  Inventory:
    Full: '&5你的背包已满,无法添加物品'
    FullDrop: '&5你的背包已满,无法添加所有物品.它们已被丢弃在地上'
  TimeNotRecorded: '&e-未记录-'
  months: '&e[months] &6个月 '
  oneMonth: '&e[months] &6个月 '
  weeks: '&e[weeks] &6周 '
  oneWeek: '&e[weeks] &6周 '
  years: '&e[years] &6年 '
  oneYear: '&e[years] &6年 '
  day: '&e[days] &6天 '
  oneDay: '&e[days] &6天 '
  hour: '&e[hours] &6小时 '
  oneHour: '&e[hours] &6小时 '
  min: '&e[mins] &6分钟 '
  sec: '&e[secs] &6秒 '
  nextPageConsole: '&f执行 &5[command] &f以获取下一页'
  prevPage: '&2----<< &6上一页 '
  prevCustomPage: '&2----<< &6[value] '
  prevPageGui: '&6上一页 '
  prevPageClean: '&6上一页 '
  prevPageOff: '&2----<< &7上一页 '
  prevCustomPageOff: '&2----<< &7[value] '
  prevPageHover: '&7<<<'
  firstPageHover: '&7|<'
  nextPage: '&6 下一页 &2>>----'
  nextCustomPage: '&6 [value] &2>>----'
  nextPageGui: '&6下一页'
  nextPageClean: '&6下一页'
  nextPageOff: '&7 下一页 &2>>----'
  nextCustomPageOff: '&7 [value] &2>>----'
  nextPageHover: '&7>>>'
  lastPageHover: '&7>|'
  pageCount: '&2[current]&7/&2[total]'
  pageCountHover: '&e共有[totalEntries]条目'
  skullOwner: '!actionbar!&7所有者:&r [playerName]'
  playerHeadName: '&7[playerName]&e的头'
  mobHeadName: '&f[mobName]&e的头'
  circle: '&3圆形'
  square: '&5方形'
  clear: '&7清除'
  protectedArea: '&c受保护区域.无法执行此操作.'
  valueToLong: '&e数值过高.最大值: [max]'
  valueToShort: '&e数值过低.最小值: [min]'
  pickIcon: '&8选择图标'
  Spawner: '&r[type] 刷怪笼'
  DamageCause:
    block_explosion: 方块爆炸
    campfire: 营火
    contact: 方块伤害
    cramming: 挤压
    custom: 未知
    dragon_breath: 龙息
    drowning: 溺水
    dryout: 脱水
    entity_attack: 近战攻击
    entity_explosion: 实体爆炸
    entity_sweep_attack: 近战横扫攻击
    fall: 摔落
    falling_block: 被下落的方块砸中
    fire: 接触火或营火
    fire_tick: 着火
    fly_into_wall: 使用鞘翅滑翔时高速撞击方块侧面
    freeze: 冰冻
    hot_floor: 站在岩浆块上
    kill: 指令击杀
    lava: 接触熔岩
    lightning: 被闪电雷击
    magic: 魔法伤害
    melting: 融化
    poison: 中毒
    projectile: 弹射物
    sonic_boom: 监守者的远程攻击
    starvation: 饥饿
    suffocation: 窒息
    suicide: 自杀
    thorns: 荆棘
    void: 虚空
    wither: 凋零
    world_border: 世界边界
  Biomes:
    DARK_FOREST: 黑森林
    SNOWY_TAIGA: 积雪针叶林
    COLD_OCEAN: 冷水海洋
    FOREST: 森林
    GROVE: 雪林
    JAGGED_PEAKS: 尖峭山峰
    SOUL_SAND_VALLEY: 灵魂沙峡谷
    CHERRY_GROVE: 樱花树林
    OLD_GROWTH_PINE_TAIGA: 原始松木针叶林
    THE_VOID: 虚空
    END_HIGHLANDS: 末地高地
    WINDSWEPT_HILLS: 风袭丘陵
    MUSHROOM_FIELDS: 蘑菇岛
    FLOWER_FOREST: 繁花森林
    DEEP_FROZEN_OCEAN: 冰冻深海
    STONY_PEAKS: 裸岩山峰
    DEEP_LUKEWARM_OCEAN: 温水深海
    PLAINS: 平原
    WARPED_FOREST: 诡异森林
    BADLANDS: 恶地
    CRIMSON_FOREST: 绯红森林
    LUSH_CAVES: 繁茂洞穴
    END_MIDLANDS: 末地内陆
    WINDSWEPT_GRAVELLY_HILLS: 风袭沙砾丘陵
    DEEP_OCEAN: 深海
    STONY_SHORE: 石岸
    DEEP_COLD_OCEAN: 冷水深海
    JUNGLE: 丛林
    TAIGA: 针叶林
    FROZEN_PEAKS: 冰封山峰
    WARM_OCEAN: 暖水海洋
    END_BARRENS: 末地荒地
    MANGROVE_SWAMP: 红树林沼泽
    FROZEN_OCEAN: 冻洋
    NETHER_WASTES: 下界荒地
    WINDSWEPT_SAVANNA: 风袭热带草原
    OLD_GROWTH_SPRUCE_TAIGA: 原始云杉针叶林
    DEEP_DARK: 深暗之域
    SUNFLOWER_PLAINS: 向日葵平原
    DESERT: 沙漠
    BEACH: 沙滩
    BIRCH_FOREST: 桦木森林
    WOODED_BADLANDS: 疏林恶地
    FROZEN_RIVER: 冻河
    WINDSWEPT_FOREST: 风袭森林
    SNOWY_BEACH: 积雪沙滩
    SNOWY_SLOPES: 积雪山坡
    SAVANNA: 热带草原
    ICE_SPIKES: 冰刺之地
    LUKEWARM_OCEAN: 温水海洋
    SWAMP: 沼泽
    BASALT_DELTAS: 玄武岩三角洲
    SPARSE_JUNGLE: 稀疏丛林
    OCEAN: 海洋
    MEADOW: 草甸
    RIVER: 河流
    ERODED_BADLANDS: 风蚀恶地
    PALE_GARDEN: 苍白之园
    SAVANNA_PLATEAU: 热带高原
    DRIPSTONE_CAVES: 溶洞
    OLD_GROWTH_BIRCH_FOREST: 原始桦木森林
    SNOWY_PLAINS: 雪原
    BAMBOO_JUNGLE: 竹林
    SMALL_END_ISLANDS: 末地小型岛屿
    THE_END: 末地
  EntityType:
    acacia_boat: 金合欢木船
    acacia_chest_boat: 金合欢木运输船
    allay: 悦灵
    area_effect_cloud: 区域效果云
    armadillo: 犰狳
    armor_stand: 盔甲架
    arrow: 箭
    axolotl: 美西螈
    bamboo_chest_raft: 运输竹筏
    bamboo_raft: 竹筏
    bat: 蝙蝠
    bee: 蜜蜂
    birch_boat: 白桦木船
    birch_chest_boat: 白桦木运输船
    blaze: 烈焰人
    block_display: 方块展示实体
    bogged: 沼骸
    breeze: 旋风人
    breeze_wind_charge: 旋风人风弹
    camel: 骆驼
    cat: 猫
    cave_spider: 洞穴蜘蛛
    cherry_boat: 樱花木船
    cherry_chest_boat: 樱花木运输船
    chest_minecart: 运输矿车
    chicken: 鸡
    cod: 鳕鱼
    command_block_minecart: 命令方块矿车
    cow: 牛
    creaking: 嘎枝
    creeper: 苦力怕
    dark_oak_boat: 深色橡木船
    dark_oak_chest_boat: 深色橡木运输船
    dolphin: 海豚
    donkey: 驴
    dragon_fireball: 末影龙火球
    drowned: 溺尸
    egg: 鸡蛋
    elder_guardian: 远古守卫者
    enderman: 末影人
    endermite: 末影螨
    ender_dragon: 末影龙
    ender_pearl: 末影珍珠
    end_crystal: 末影水晶
    evoker: 唤魔者
    evoker_fangs: 唤魔者尖牙
    experience_bottle: 经验瓶
    experience_orb: 经验球
    eye_of_ender: 末影之眼
    falling_block: 下落的方块
    fireball: 火球
    firework_rocket: 烟花火箭
    fishing_bobber: 浮漂
    fox: 狐狸
    frog: 青蛙
    furnace_minecart: 动力矿车
    ghast: 恶魂
    giant: 巨人
    glow_item_frame: 荧光物品展示框
    glow_squid: 发光鱿鱼
    goat: 山羊
    guardian: 守卫者
    hoglin: 疣猪兽
    hopper_minecart: 漏斗矿车
    horse: 马
    husk: 尸壳
    illusioner: 幻术师
    interaction: 交互实体
    iron_golem: 铁傀儡
    item: 物品
    item_display: 物品展示实体
    item_frame: 物品展示框
    jungle_boat: 丛林木船
    jungle_chest_boat: 丛林木运输船
    leash_knot: 拴绳结
    lightning_bolt: 闪电束
    llama: 羊驼
    llama_spit: 羊驼唾沫
    magma_cube: 岩浆怪
    mangrove_boat: 红树木船
    mangrove_chest_boat: 红树木运输船
    marker: 标记
    minecart: 矿车
    mooshroom: 哞菇
    mule: 骡
    oak_boat: 橡木船
    oak_chest_boat: 橡木运输船
    ocelot: 豹猫
    ominous_item_spawner: 不祥之物生成器
    painting: 画
    pale_oak_boat: 苍白橡木船
    pale_oak_chest_boat: 苍白橡木运输船
    panda: 熊猫
    parrot: 鹦鹉
    phantom: 幻翼
    pig: 猪
    piglin: 猪灵
    piglin_brute: 猪灵蛮兵
    pillager: 掠夺者
    player: 玩家
    polar_bear: 北极熊
    potion: 药水
    pufferfish: 河豚
    rabbit: 兔子
    ravager: 劫掠兽
    salmon: 鲑鱼
    sheep: 绵羊
    shulker: 潜影贝
    shulker_bullet: 潜影弹
    silverfish: 蠹虫
    skeleton: 骷髅
    skeleton_horse: 骷髅马
    slime: 史莱姆
    small_fireball: 小火球
    sniffer: 嗅探兽
    snowball: 雪球
    snow_golem: 雪傀儡
    spawner_minecart: 刷怪笼矿车
    spectral_arrow: 光灵箭
    spider: 蜘蛛
    spruce_boat: 云杉木船
    spruce_chest_boat: 云杉木运输船
    squid: 鱿鱼
    stray: 流浪者
    strider: 炽足兽
    tadpole: 蝌蚪
    text_display: 文本展示实体
    tnt: TNT
    tnt_minecart: TNT矿车
    trader_llama: 行商羊驼
    trident: 三叉戟
    tropical_fish: 热带鱼
    turtle: 海龟
    unknown: 未知
    vex: 恼鬼
    villager: 村民
    vindicator: 卫道士
    wandering_trader: 流浪商人
    warden: 监守者
    wind_charge: 风弹
    witch: 女巫
    wither: 凋灵
    wither_skeleton: 凋灵骷髅
    wither_skull: 凋灵之首
    wolf: 狼
    zoglin: 僵尸疣猪兽
    zombie: 僵尸
    zombie_horse: 僵尸马
    zombie_villager: 僵尸村民
    zombified_piglin: 僵尸猪灵
  # Avoid adding color codes to the enchant name
  EnchantNames:
    wind_burst: 风爆
    channeling: 引雷
    silk_touch: 精准采集
    oxygen: 水下呼吸
    fire_aspect: 火焰附加
    loot_bonus_mobs: 抢夺
    density: 致密
    lure: 饵钓
    swift_sneak: 迅捷潜行
    breach: Breach
    depth_strider: 深海探索者
    protection_projectile: 弹射物保护
    impaling: 穿刺
    arrow_infinite: 无限
    loot_bonus_blocks: 时运
    knockback: 击退
    soul_speed: 灵魂疾行
    multishot: 多重射击
    water_worker: 水下速掘
    arrow_fire: 火矢
    thorns: 荆棘
    protection_fire: 火焰保护
    protection_environmental: 保护
    protection_explosions: 爆炸保护
    luck: 幸运
    sweeping_edge: 横扫之刃
    protection_fall: 摔落缓冲
    riptide: 激流
    frost_walker: 冰霜行者
    vanishing_curse: 消失诅咒
    binding_curse: 绑定诅咒
    durability: 耐久
    damage_all: 锋利
    dig_speed: 效率
    arrow_damage: 力量
    damage_undead: 亡灵杀手
    quick_charge: 快速装填
    damage_arthropods: 节肢杀手
    loyalty: 忠诚
    arrow_knockback: 冲击
    mending: 经验修补
    piercing: 穿透
  PotionEffectAliases:
    instanthealth:
    - 瞬间治疗
    invisibility:
    - 隐身
    waterbreathing:
    - 水下呼吸
    resistance:
    - 抗性提升
    unluck:
    - 霉运
    blindness:
    - 失明
    haste:
    - 急迫
    poison:
    - 中毒
    slowness:
    - 缓慢
    hunger:
    - 饥饿
    slowfalling:
    - 缓降
    weaving:
    - 盘丝
    fireresistance:
    - 防火
    saturation:
    - 饱和
    raidomen:
    - 袭击之兆
    jumpboost:
    - 跳跃提升
    dolphinsgrace:
    - 海豚的恩惠
    miningfatigue:
    - 挖掘疲劳
    healthboost:
    - 生命提升
    regeneration:
    - 生命恢复
    conduitpower:
    - 潮涌能量
    badomen:
    - 不祥之兆
    luck:
    - 幸运
    speed:
    - 迅捷
    trialomen:
    - 试炼之兆
    strength:
    - 力量
    darkness:
    - 黑暗
    heroofthevillage:
    - 村庄英雄
    levitation:
    - 飘浮
    instantdamage:
    - 瞬间伤害
    oozing:
    - 渗浆
    weakness:
    - 虚弱
    nausea:
    - 反胃
    windcharged:
    - 蓄风
    absorption:
    - 伤害吸收
    wither:
    - 凋灵
    glowing:
    - 发光
    infested:
    - 寄生
    nightvision:
    - 夜视
direction:
  n: 北
  ne: 东北
  e: 东
  se: 东南
  s: 南
  sw: 西南
  w: 西
  nw: 西北
modify:
  middlemouse: '&2中键点击进行编辑'
  qButtonEdit: '&2点击Q进行编辑'
  newItem: '&7放置新物品在这里'
  newLine: '&2<新行>'
  newLineHover: '&2添加新行'
  newPage: '&2<新页面>'
  newPageHover: '&2创建新页面'
  removePage: '&c<移除页面>'
  removePageHover: '&c移除页面'
  deleteSymbol: ' &c[X]'
  deleteSymbolHover: '&c删除 &e[文本]'
  extraEditSymbol: ' &6!'
  addSymbol: '&2[+]'
  addSymbolHover: '&2添加新'
  cancelSymbol: ' &7&l[X]'
  cancelSymbolHover: '&a取消'
  acceptSymbol: ' &2&l[✔]'
  acceptSymbolHover: '&a接受'
  denySymbol: ' &4&l[X]'
  denySymbolHover: '&c拒绝'
  enabledSymbol: '&2[+]'
  disabledSymbol: '&c[-]'
  enabled: '&2已启用'
  disabled: '&c已禁用'
  running: '&2运行中'
  paused: '&c已暂停'
  editSymbol: '&e✎'
  editSymbolHover: '&e编辑 &6[文本]'
  editLineColor: '&f'
  listUpSymbol: '&6⇑'
  listUpSymbolHover: '&e上移'
  listDownSymbol: '&6⇓'
  listDownSymbolHover: '&e下移'
  listNumbering: '&e[编号]. '
  listAlign: '&80'
  ChangeHover: '&e点击进行修改'
  ChangeCommands: '&e命令'
  enabledColor: '&6'
  disabledColor: '&7'
  commandTitle: ' &e--- &6[name] &e---'
  commandList: ' &e[command]  '
  emptyLine: '&7[空行]'
  commandEdit: '&e编辑列表'
  nameAddInfo: '&e输入新名称.输入 &6cancel &e取消'
  lineAddInfo: '&e输入新行.输入 &6cancel &e取消'
  commandAddInfo: '&e输入新命令.输入 &6cancel &e取消'
  commandAddInformationHover: "&e[playerName] 可用于获取玩家名称 \n&e要包含命令延迟: \n&edelay! 5\
    \ \n&e支持专用命令.更多信息请访问 \n&ehttps://www.zrips.net/cmi/commands/specialized/"
  commandEditInfo: '&e点击粘贴旧文本.输入 &6cancel &e取消操作'
  listLimit: '&e列表不能大于 &6[amount] &e条目'
  commandEditInfoHover: '&e点击粘贴旧文本'
teleportation:
  relocation: '!actionbar!&4你的传送位置被阻挡了.你已被传送到安全位置.'
econ:
  noMoney: '&c你没有足够的金钱'
  charged: '!actionbar!&f扣除: &6[amount]'
  notEnoughMoney: '&c你的金钱不够.需要(&6[amount]&c)'
  tooMuchMoney: '&c你的金钱太多了'
Selection:
  SelectPoints: '&c使用选择工具选择2个点!命令:&6[tool]'
  PrimaryPoint: '&e放置 &6主要 &e选择点 [point]'
  SecondaryPoint: '&e放置 &6次要 &e选择点 [point]'
  CoordsTop: '&eX坐标:&6[x] &eY坐标:&6[y] &eZ坐标:&6[z]'
  CoordsBottom: '&eX坐标:&6[x] &eY坐标:&6[y] &eZ坐标:&6[z]'
  Area: '&7[world] &f(&6[x1]:[y1]:[z1] &e[x2]:[y2]:[z2]&f)'
Location:
  Title: '&8玩家位置'
  Killer: '&e杀手: &6[killer]'
  OneLiner: '&e位置: &6[location]'
  DeathReason: '&e死亡原因: &6[reason]'
  Full: '&7[world] &f[x]&7:&f[y]&7:&f[z]'
  World: '&e世界: &6[world]'
  X: '&eX坐标: &6[x]'
  Y: '&eY坐标: &6[y]'
  Z: '&eZ坐标: &6[z]'
  Pitch: '&e俯视角: &6[pitch]'
  Yaw: '&e偏航角: &6[yaw]'
  WorldNames:
  - world-&2世界
  - world_nether-&2地狱世界
  - world_the_end-&2末地世界
Locations: '&7位置: '
command:
  help:
    output:
      usage: '&e用法: &7%usage%'
      cmdInfoFormat: '[command] &f- &e[description]'
      cmdFormat: '&6/[command]&f[arguments]'
      helpPageDescription: '&e* [description]'
      explanation: '&e * [explanation]'
      title: '&e------ ======= &6帮助&e &e======= ------'
  nocmd:
    help:
      info: '&e显示所有可用命令'
      args: ''
  clearcache:
    help:
      info: '&e清除缓存'
      args: ''
  reload:
    help:
      info: '&e重新加载插件配置和语言环境文件'
      args: ''
    info:
      feedback: '&6CMIL配置和语言环境文件已重新加载!花费了[ms]毫秒'
      failedConfig: '&4无法加载配置文件!请检查拼写!'
      failedLocale: '&4无法加载语言环境文件!请检查拼写!'
