luckperms.logs.actionlog-prefix=LOGG
luckperms.logs.verbose-prefix=VB
luckperms.logs.export-prefix=EKSPORTER
luckperms.commandsystem.available-commands=Bruk {0} for å se tilgjengelige kommandoer
luckperms.commandsystem.command-not-recognised=Kommando ikke gjenkjent
luckperms.commandsystem.no-permission=Du har ikke tillatelse til å bruke denne kommandoen\!
luckperms.commandsystem.no-permission-subcommands=Du har ikke tillatelse til å bruke noen underkommandoer
luckperms.commandsystem.already-executing-command=En annen kommando blir kjørt, venter på at den skal fullføres...
luckperms.commandsystem.usage.sub-commands-header=Underkommandoer
luckperms.commandsystem.usage.usage-header=Kommando bruk
luckperms.commandsystem.usage.arguments-header=Argumenter
luckperms.first-time.no-permissions-setup=Det ser ut til at ingen tillatelser er satt opp ennå\!
luckperms.first-time.use-console-to-give-access=Før du kan bruke noen av kommandoene i LuckPerms, må du bruke konsollen for å gi deg selv tilgang
luckperms.first-time.console-command-prompt=Åpne konsollen og kjør
luckperms.first-time.next-step=Etter at du har gjort dette, kan du begynne å definere tillatelser og grupper
luckperms.first-time.wiki-prompt=Vet du ikke hvor du skal starte? Se her\: {0}
luckperms.login.try-again=Vennligst prøv igjen senere
luckperms.login.loading-database-error=En databasefeil oppsto under lasting av data
luckperms.login.server-admin-check-console-errors=Hvis du er en server admin, vennligst sjekk konsollen for feil
luckperms.login.server-admin-check-console-info=Vennligst se konsollen for mer informasjon
luckperms.login.data-not-loaded-at-pre=Data for din bruker ble ikke lastet i perioden før innlogging
luckperms.login.unable-to-continue=kunne ikke fortsette
luckperms.login.craftbukkit-offline-mode-error=dette skyldes sannsynligvis en konflikt mellom CraftBukkit og innstillingene for online-modus
luckperms.login.unexpected-error=En uventet feil oppsto når dine tillatelser ble satt opp
luckperms.opsystem.disabled=Vanilla OP systemet er deaktivert på denne serveren
luckperms.opsystem.sponge-warning=Vær oppmerksom på at Server Operatør status ikke har noen effekt på Sponge når en tillatelsesplugin er installert, du må redigere brukerdata direkte
luckperms.duration.unit.years.plural={0} år
luckperms.duration.unit.years.singular={0} år
luckperms.duration.unit.years.short={0}å
luckperms.duration.unit.months.plural={0} måneder
luckperms.duration.unit.months.singular={0} måned
luckperms.duration.unit.months.short={0}mnd
luckperms.duration.unit.weeks.plural={0} uker
luckperms.duration.unit.weeks.singular={0} uke
luckperms.duration.unit.weeks.short={0}u
luckperms.duration.unit.days.plural={0} dager
luckperms.duration.unit.days.singular={0} dag
luckperms.duration.unit.days.short={0}d
luckperms.duration.unit.hours.plural={0} timer
luckperms.duration.unit.hours.singular={0} time
luckperms.duration.unit.hours.short={0}t
luckperms.duration.unit.minutes.plural={0} minutter
luckperms.duration.unit.minutes.singular={0} minutt
luckperms.duration.unit.minutes.short={0}m
luckperms.duration.unit.seconds.plural={0} sekunder
luckperms.duration.unit.seconds.singular={0} sekund
luckperms.duration.unit.seconds.short={0}s
luckperms.duration.since={0} siden
luckperms.command.misc.invalid-code=Ugyldig kode
luckperms.command.misc.response-code-key=responskode
luckperms.command.misc.error-message-key=melding
luckperms.command.misc.bytebin-unable-to-communicate=Kan ikke kommunisere med bytebin
luckperms.command.misc.webapp-unable-to-communicate=Klarer ikke å kommunisere med applikasjonen
luckperms.command.misc.check-console-for-errors=Sjekk konsollen for feil
luckperms.command.misc.file-must-be-in-data=Filen {0} må være en direkte undergruppe av datamappen
luckperms.command.misc.wait-to-finish=Vennligst vent til den er ferdig og prøv på nytt
luckperms.command.misc.invalid-priority=Ugyldig prioritet {0}
luckperms.command.misc.expected-number=Forventet et tall
luckperms.command.misc.date-parse-error=Kunne ikke analysere datoen {0}
luckperms.command.misc.date-in-past-error=Du kan ikke angi en dato i fortiden\!
luckperms.command.misc.page=side {0} av {1}
luckperms.command.misc.page-entries={0} innføringer
luckperms.command.misc.none=Ingen
luckperms.command.misc.loading.error.unexpected=En uventet feil har oppstått
luckperms.command.misc.loading.error.user=Bruker ble ikke lastet
luckperms.command.misc.loading.error.user-specific=Kunne ikke laste målbrukeren {0}
luckperms.command.misc.loading.error.user-not-found=En bruker for {0} ble ikke funnet
luckperms.command.misc.loading.error.user-save-error=Det oppstod en feil under lagring av brukerdata for {0}
luckperms.command.misc.loading.error.user-not-online=Brukeren {0} er ikke pålogget
luckperms.command.misc.loading.error.user-invalid={0} er ikke et gyldig brukernavn/uuid
luckperms.command.misc.loading.error.user-not-uuid=Målbruker {0} er ikke en gyldig uuid
luckperms.command.misc.loading.error.group=Gruppen er ikke lastet
luckperms.command.misc.loading.error.all-groups=Kan ikke laste alle grupper
luckperms.command.misc.loading.error.group-not-found=En gruppe kalt {0} ble ikke funnet
luckperms.command.misc.loading.error.group-save-error=Det oppstod en feil under lagring av gruppedata for {0}
luckperms.command.misc.loading.error.group-invalid={0} er ikke et gyldig gruppenavn
luckperms.command.misc.loading.error.track=Sporet ikke lastet
luckperms.command.misc.loading.error.all-tracks=Kunne ikke laste alle sporene
luckperms.command.misc.loading.error.track-not-found=Et spor kalt {0} ble ikke funnet
luckperms.command.misc.loading.error.track-save-error=Det oppstod en feil under lagring av spordata for {0}
luckperms.command.misc.loading.error.track-invalid={0} er ikke et gyldig spor navn
luckperms.command.editor.no-match=Kan ikke åpne editor, ingen objekter samsvarte med ønsket type
luckperms.command.editor.start=Forbereder en ny redigeringsøkt, vennligst vent...
luckperms.command.editor.url=Klikk på lenken under for å åpne redigeringsprogrammet
luckperms.command.editor.unable-to-communicate=Kan ikke kommunisere med redigeringsprogrammet
luckperms.command.editor.apply-edits.success=Webredigeringsdataen ble vellykket påført {0}en {1}
luckperms.command.editor.apply-edits.success-summary={0} {1} og {2} {3}
luckperms.command.editor.apply-edits.success.additions=tillegg
luckperms.command.editor.apply-edits.success.additions-singular=tillegg
luckperms.command.editor.apply-edits.success.deletions=slettinger
luckperms.command.editor.apply-edits.success.deletions-singular=sletting
luckperms.command.editor.apply-edits.no-changes=Ingen endringer ble påført fra webredigeringsøkten, ettersom dataen innført ikke hadde noen endringer
luckperms.command.editor.apply-edits.unknown-type=Kan ikke påføre endringen til den oppgivne objekttypen
luckperms.command.editor.apply-edits.unable-to-read=Kan ikke lese dataen med den oppgivne koden
luckperms.command.search.searching.permission=Søker etter brukere og grupper med {0}
luckperms.command.search.searching.inherit=Søker etter brukere og grupper som arver fra {0}
luckperms.command.search.result=Fant {0} oppføringer fra {1} brukere og {2} grupper
luckperms.command.search.result.default-notice=N.B.\: Når du søker etter medlemmer i standardgruppen vil ikke frakoblede spillere uten noen andre tillatelser bli vist\!
luckperms.command.search.showing-users=Viser brukeroppføringer
luckperms.command.search.showing-groups=Viser gruppeoppføringer
luckperms.command.tree.start=Genererer trestruktur, vennligst vent...
luckperms.command.tree.empty=Kunne ikke generere tre, ingen resultater ble funnet
luckperms.command.tree.url=URL til tillatelsestreet
luckperms.command.verbose.invalid-filter={0} er ikke et gyldig verbose filter
luckperms.command.verbose.enabled=Detaljert logging {0} for kontroll etter matchende {1}
luckperms.command.verbose.command-exec=Tvinger {0} til å utføre kommandoen {1} og rapporter alle endringer som er gjort...
luckperms.command.verbose.off=Detaljert loggføring {0}
luckperms.command.verbose.command-exec-complete=Kommandoen ble utført
luckperms.command.verbose.command.no-checks=Utførelsen av kommando fullført, men ingen tilgangskontroller ble gjort
luckperms.command.verbose.command.possibly-async=Dette kan være fordi utvidelsen kjører kommandoer i bakgrunnen (async)
luckperms.command.verbose.command.try-again-manually=Du kan fremdeles bruke verbose manuelt for å oppdage kontroller som er gjort som dette
luckperms.command.verbose.enabled-recording=Verbose opptak {0} sjekker matchende {1}
luckperms.command.verbose.uploading=Detaljert logging {0}, laster opp resultater...
luckperms.command.verbose.url=Detaljert resultat URL
luckperms.command.verbose.enabled-term=aktivert
luckperms.command.verbose.disabled-term=deaktivert
luckperms.command.verbose.query-any=ALLE
luckperms.command.info.running-plugin=Kjører
luckperms.command.info.platform-key=Plattform
luckperms.command.info.server-brand-key=Servermerke
luckperms.command.info.server-version-key=Serverversjon
luckperms.command.info.storage-key=Lager
luckperms.command.info.storage-type-key=Type
luckperms.command.info.storage.meta.split-types-key=Typer
luckperms.command.info.storage.meta.ping-key=Ping
luckperms.command.info.storage.meta.connected-key=Tilkoblet
luckperms.command.info.storage.meta.file-size-key=Filstørrelse
luckperms.command.info.extensions-key=Utvidelser
luckperms.command.info.messaging-key=Meldinger
luckperms.command.info.instance-key=Instans
luckperms.command.info.static-contexts-key=Statiske kontekster
luckperms.command.info.online-players-key=Pålogget Spillere
luckperms.command.info.online-players-unique={0} unik
luckperms.command.info.uptime-key=Oppetid
luckperms.command.info.local-data-key=Lokale data
luckperms.command.info.local-data={0} brukere, {1} grupper, {2} spor
luckperms.command.generic.create.success={0} ble opprettet
luckperms.command.generic.create.error=Det oppstod en feil under oppretting av {0}
luckperms.command.generic.create.error-already-exists={0} finnes allerede\!
luckperms.command.generic.delete.success={0} ble slettet
luckperms.command.generic.delete.error=Det oppstod en feil ved sletting av {0}
luckperms.command.generic.delete.error-doesnt-exist={0} finnes ikke\!
luckperms.command.generic.rename.success={0} endret navn til {1}
luckperms.command.generic.clone.success={0} ble klonet på {1}
luckperms.command.generic.info.parent.title=Overordnede Grupper
luckperms.command.generic.info.parent.temporary-title=Midlertidige Overordnede Grupper
luckperms.command.generic.info.expires-in=utløper om
luckperms.command.generic.info.inherited-from=arvet fra
luckperms.command.generic.info.inherited-from-self=selv
luckperms.command.generic.show-tracks.title={0} sitt Spor
luckperms.command.generic.show-tracks.empty={0} er ikke på noen spor
luckperms.command.generic.clear.node-removed={0} noder ble fjernet
luckperms.command.generic.clear.node-removed-singular={0} tillatelse ble fjernet
luckperms.command.generic.clear={0}''s noder ble fjernet i kontekst {1}
luckperms.command.generic.permission.info.title={0} s Tillatelser
luckperms.command.generic.permission.info.empty={0} har ingen tillatelser satt
luckperms.command.generic.permission.info.click-to-remove=Klikk for å fjerne denne noden fra {0}
luckperms.command.generic.permission.check.info.title=Informasjon for {0}
luckperms.command.generic.permission.check.info.directly={0} har {1} satt til {2} i kontekst {3}
luckperms.command.generic.permission.check.info.inherited={0} arver {1} satt til {2} fra {3} i kontekst {4}
luckperms.command.generic.permission.check.info.not-directly={0} har ikke {1} satt
luckperms.command.generic.permission.check.info.not-inherited={0} arver ikke {1}
luckperms.command.generic.permission.check.result.title=Tillatelsessjekk for {0}
luckperms.command.generic.permission.check.result.result-key=Resultat
luckperms.command.generic.permission.check.result.processor-key=Prosessor
luckperms.command.generic.permission.check.result.cause-key=Årsak
luckperms.command.generic.permission.check.result.context-key=Kontekst
luckperms.command.generic.permission.set=Satt {0} til {1} for {2} i kontekst {3}
luckperms.command.generic.permission.already-has={0} har allerede {1} i kontekst {2}
luckperms.command.generic.permission.set-temp=Satt {0} til {1} for {2} i en periode på {3} i kontekst {4}
luckperms.command.generic.permission.already-has-temp={0} har allerede {1} satt midlertidig i kontekst {2}
luckperms.command.generic.permission.unset=Fjern {0} for {1} i kontekst {2}
luckperms.command.generic.permission.doesnt-have={0} har ikke {1} i kontekst {2}
luckperms.command.generic.permission.unset-temp=Fjern midlertidig tillatelse {0} for {1} i kontekst av {2}
luckperms.command.generic.permission.subtract=Satt {0} til {1} for {2} i en periode på {3} i kontekst {4}, {5} mindre enn tidligere
luckperms.command.generic.permission.doesnt-have-temp={0} har ikke {1} satt midlertidig i kontekst {2}
luckperms.command.generic.permission.clear={0} sine tillatelser ble fjernet i kontekst {1}
luckperms.command.generic.parent.info.title={0}''s overordnede
luckperms.command.generic.parent.info.empty={0} har ikke noen overordnede definert
luckperms.command.generic.parent.info.click-to-remove=Klikk for å fjerne denne overordnede fra {0}
luckperms.command.generic.parent.add={0} arver nå tillatelser fra {1} i kontekst {2}
luckperms.command.generic.parent.add-temp={0} arver nå tillatelser fra {1} for en varighet på {2} i kontekst med {3}
luckperms.command.generic.parent.set={0} hadde deres eksisterende overordnede grupper tømt, og har nå {1} i kontekst {2}
luckperms.command.generic.parent.set-track={0} hadde eksisterende overordnede grupper på spor {1} slettet, og arver nå bare {2} i kontekst {3}
luckperms.command.generic.parent.remove={0} arver ikke lenger tillatelser fra {1} i kontekst {2}
luckperms.command.generic.parent.remove-temp={0} arver ikke lenger midlertidig tillatelser fra {1} i kontekst {2}
luckperms.command.generic.parent.subtract={0} vil arve tillatelser fra {1} for en varighet på {2} i kontekst {3}, {4} mindre enn tidligere
luckperms.command.generic.parent.clear={0} sine foreldre ble fjernet i kontekst {1}
luckperms.command.generic.parent.clear-track={0} sine foreldre på spor {1} ble fjernet i kontekst {2}
luckperms.command.generic.parent.already-inherits={0} arver allerede fra {1} i kontekst {2}
luckperms.command.generic.parent.doesnt-inherit={0} arver ikke fra {1} i kontekst {2}
luckperms.command.generic.parent.already-temp-inherits={0} arver allerede midlertidig fra {1} i kontekst {2}
luckperms.command.generic.parent.doesnt-temp-inherit={0} arver ikke midlertidig fra {1} i kontekst {2}
luckperms.command.generic.chat-meta.info.title-prefix={0} sine prefikser
luckperms.command.generic.chat-meta.info.title-suffix={0} sine suffiks
luckperms.command.generic.chat-meta.info.none-prefix={0} har ingen prefikser
luckperms.command.generic.chat-meta.info.none-suffix={0} har ingen suffikser
luckperms.command.generic.chat-meta.info.click-to-remove=Klikk for å fjerne {0} fra {1}
luckperms.command.generic.chat-meta.already-has={0} har allerede {1} {2} satt til prioritet {3} i kontekst {4}
luckperms.command.generic.chat-meta.already-has-temp={0} har allerede {1} {2} satt midlertidig prioritet til {3} i kontekst {4}
luckperms.command.generic.chat-meta.doesnt-have={0} har ikke {1} {2} med prioritet {3} i kontekst {4}
luckperms.command.generic.chat-meta.doesnt-have-temp={0} har ikke {1} {2} med midlertidig prioritet {3} i kontekst {4}
luckperms.command.generic.chat-meta.add={1} {2} ble satt til prioritet {3} for {0} i kontekst {4}
luckperms.command.generic.chat-meta.add-temp={1} {2} ble satt til prioritet {3} i en periode på {4} for {0} i kontekst {5}
luckperms.command.generic.chat-meta.remove={0} fikk fjernet {1} {2} med prioritet {3} i kontekst {4}
luckperms.command.generic.chat-meta.remove-bulk={0} fikk fjernet alle {1} med prioritet {2} i kontekst {3}
luckperms.command.generic.chat-meta.remove-temp={0} fikk midlertidig {1} {2} med prioritet {3} fjernet i kontekst {4}
luckperms.command.generic.chat-meta.remove-temp-bulk={0} fikk alle midlertidige {1} med prioritet {2} fjernet fra kontekst {3}
luckperms.command.generic.meta.info.title={0} sin Meta
luckperms.command.generic.meta.info.none={0} har ingen meta
luckperms.command.generic.meta.info.click-to-remove=Klikk for å fjerne denne meta noden fra {0}
luckperms.command.generic.meta.already-has={0} har allerede metanøkkel {1} satt til {2} i kontekst {3}
luckperms.command.generic.meta.already-has-temp={0} har allerede metanøkkel {1} midlertidig satt til {2} i kontekst {3}
luckperms.command.generic.meta.doesnt-have={0} har ikke metanøkkel {1} i kontekst {2}
luckperms.command.generic.meta.doesnt-have-temp={0} har ikke metanøkkel {1} satt midlertidig i kontekst {2}
luckperms.command.generic.meta.set=Satt metanøkkel {0} til {1} for {2} i kontekst {3}
luckperms.command.generic.meta.set-temp=Satt metanøkkel {0} til {1} for {2} i en periode på {3} i kontekst {4}
luckperms.command.generic.meta.unset=Fjernet metanøkkel {0} for {1} i kontekst {2}
luckperms.command.generic.meta.unset-temp=Fjernet midlertidig metanøkkel {0} for {1} i kontekst {2}
luckperms.command.generic.meta.clear=Meta som samsvarer med {1} ble fjernet fra {0} i kontekst {2}
luckperms.command.generic.contextual-data.title=Kontekstuell informasjon
luckperms.command.generic.contextual-data.mode.key=modus
luckperms.command.generic.contextual-data.mode.server=server
luckperms.command.generic.contextual-data.mode.active-player=aktiv spiller
luckperms.command.generic.contextual-data.contexts-key=Kontekster
luckperms.command.generic.contextual-data.prefix-key=Prefiks
luckperms.command.generic.contextual-data.suffix-key=Suffiks
luckperms.command.generic.contextual-data.primary-group-key=Primær Gruppe
luckperms.command.generic.contextual-data.meta-key=Meta
luckperms.command.generic.contextual-data.null-result=Ingen
luckperms.command.user.info.title=Brukerinfo
luckperms.command.user.info.uuid-key=UUID
luckperms.command.user.info.uuid-type-key=type
luckperms.command.user.info.uuid-type.mojang=mojang
luckperms.command.user.info.uuid-type.not-mojang=frakoblet
luckperms.command.user.info.status-key=Status
luckperms.command.user.info.status.online=Pålogget
luckperms.command.user.info.status.offline=Frakoblet
luckperms.command.user.removegroup.error-primary=Du kan ikke fjerne en bruker fra sin primære gruppe
luckperms.command.user.primarygroup.not-member={0} var ikke medlem av {1}, legger de til nå
luckperms.command.user.primarygroup.already-has={0} har allerede {1} som sin primære gruppe
luckperms.command.user.primarygroup.warn-option=Advarsel\: Beregningsmetoden for primære grupper som blir benyttet på denne serveren ({0}) vil kanskje ikke gjenspeile denne endringen
luckperms.command.user.primarygroup.set={0} sin primære gruppe ble satt til {1}
luckperms.command.user.track.error-not-contain-group={0} er ikke i noen grupper på {1}
luckperms.command.user.track.unsure-which-track=Usikker på hvilket spor som skal brukes, vennligst angi sporet som argument
luckperms.command.user.track.missing-group-advice=Enten opprett gruppen, eller fjern den fra sporet og prøv på nytt
luckperms.command.user.promote.added-to-first={0} er ikke i noen grupper på {1}, så de ble lagt til i den første gruppen {2} i kontekst {3}
luckperms.command.user.promote.not-on-track={0} er ikke i noen grupper på {1}, ble derfor ikke forfremmet
luckperms.command.user.promote.success=Forfremmer {0} langs spor {1} fra {2} til {3} i kontekst {4}
luckperms.command.user.promote.end-of-track=Slutten på sporet {0} ble nådd, ugyldig å forfremme {1}
luckperms.command.user.promote.next-group-deleted=Den neste gruppen på sporet, {0}, eksisterer ikke lenger
luckperms.command.user.promote.unable-to-promote=Kan ikke forfremme bruker
luckperms.command.user.demote.success=Degrader {0} langs spor {1} fra {2} til {3} i kontekst {4}
luckperms.command.user.demote.end-of-track=Slutten på sporet {0} ble nådd, {1} ble dermed fjernet fra {2}
luckperms.command.user.demote.end-of-track-not-removed=Slutten av spor {0} ble nådd, men {1} ble ikke fjernet fra første gruppe
luckperms.command.user.demote.previous-group-deleted=Forrige gruppe på sporet, {0}, eksisterer ikke lenger
luckperms.command.user.demote.unable-to-demote=Kan ikke degradere bruker
luckperms.command.group.list.title=Grupper
luckperms.command.group.delete.not-default=Du kan ikke slette standardgruppen
luckperms.command.group.info.title=Gruppe Informasjon
luckperms.command.group.info.display-name-key=Visningsnavn
luckperms.command.group.info.weight-key=Vekt
luckperms.command.group.setweight.set=Satte vekt til {0} for gruppen {1}
luckperms.command.group.setdisplayname.doesnt-have={0} har ikke satt et visningsnavn
luckperms.command.group.setdisplayname.already-has={0} har allerede visningsnavn {1}
luckperms.command.group.setdisplayname.already-in-use=Visningsnavnet {0} er allerede i bruk av {1}
luckperms.command.group.setdisplayname.set=Satt visningsnavn til {0} for gruppen {1} i kontekst {2}
luckperms.command.group.setdisplayname.removed=Fjernet visningsnavn for gruppen {0} i kontekst {1}
luckperms.command.track.list.title=Spor
luckperms.command.track.path.empty=Ingen
luckperms.command.track.info.showing-track=Viser spor
luckperms.command.track.info.path-property=Sti
luckperms.command.track.clear={0} sitt gruppespor ble fjernet
luckperms.command.track.append.success=Gruppe {0} ble lagt til i spor {1}
luckperms.command.track.insert.success=Gruppe {0} ble satt inn i spor {1} ved posisjon {2}
luckperms.command.track.insert.error-number=Forventet tall, men mottok istedet\: {0}
luckperms.command.track.insert.error-invalid-pos=Kunne ikke sette inn på posisjon {0}
luckperms.command.track.insert.error-invalid-pos-reason=ugyldig posisjon
luckperms.command.track.remove.success=Gruppe {0} ble fjernet fra spor {1}
luckperms.command.track.error-empty={0} kan ikke brukes fordi det er tomt eller inneholder kun en gruppe
luckperms.command.track.error-multiple-groups={0} er medlem av flere grupper på dette sporet
luckperms.command.track.error-ambiguous=Kan ikke finne deres posisjon
luckperms.command.track.already-contains={0} inneholder allerede {1}
luckperms.command.track.doesnt-contain={0} inneholder ikke {1}
luckperms.command.log.load-error=Kan ikke laste loggen
luckperms.command.log.invalid-page=Ugyldig sidenummer
luckperms.command.log.invalid-page-range=Angi en verdi mellom {0} og {1}
luckperms.command.log.empty=Ingen loggoppføringer å vise
luckperms.command.log.notify.error-console=Kan ikke aktivere/deaktivere varslinger for konsollen
luckperms.command.log.notify.enabled-term=Aktivert
luckperms.command.log.notify.disabled-term=Deaktivert
luckperms.command.log.notify.changed-state={0} loggfører utgang
luckperms.command.log.notify.already-on=Du mottar allerede varslinger
luckperms.command.log.notify.already-off=Du mottar ikke varslinger
luckperms.command.log.notify.invalid-state=Tilstand ukjent. Forventer {0} eller {1}
luckperms.command.log.show.search=Viser nylige handlinger for spørringen {0}
luckperms.command.log.show.recent=Viser nylige handlinger
luckperms.command.log.show.by=Viser nylige handlinger av {0}
luckperms.command.log.show.history=Viser historikk for {0} {1}
luckperms.command.export.error-term=Feil
luckperms.command.export.already-running=En annen eksportprosess kjører allerede
luckperms.command.export.file.already-exists=Filen {0} finnes allerede
luckperms.command.export.file.not-writable=Filen {0} er ikke skrivbar
luckperms.command.export.file.success=Vellykket eksportert til {0}
luckperms.command.export.file-unexpected-error-writing=Det oppstod en uventet feil under skriving til filen
luckperms.command.export.web.export-code=Eksporter kode
luckperms.command.export.web.import-command-description=Bruk følgende kommando for å importere
luckperms.command.import.term=Importer
luckperms.command.import.error-term=Feil
luckperms.command.import.already-running=En annen importprosess kjører allerede
luckperms.command.import.file.doesnt-exist=Filen {0} eksisterer ikke
luckperms.command.import.file.not-readable=Filen {0} er ikke lesbar
luckperms.command.import.file.unexpected-error-reading=En uventet feil oppsto ved lesing fra den importerte filen
luckperms.command.import.file.correct-format=er dette det riktige formatet?
luckperms.command.import.web.unable-to-read=Kan ikke lese data ved å bruke angitt kode
luckperms.command.import.progress.percent={0}% ferdig
luckperms.command.import.progress.operations={0}/{1} operasjoner fullført
luckperms.command.import.starting=Starter importprosess
luckperms.command.import.completed=FULLFØRT
luckperms.command.import.duration=tok {0} sekunder
luckperms.command.bulkupdate.must-use-console=Kommando for masseoppdatering kan kun brukes fra konsollen
luckperms.command.bulkupdate.invalid-data-type=Ugyldig type, forventet {0}
luckperms.command.bulkupdate.invalid-constraint=Ugyldig begrensning {0}
luckperms.command.bulkupdate.invalid-constraint-format=Begrensninger bør være i formatet {0}
luckperms.command.bulkupdate.invalid-comparison=Ugyldig sammenligningsoperatør {0}
luckperms.command.bulkupdate.invalid-comparison-format=Forventet en av følgende\: {0}
luckperms.command.bulkupdate.queued=Masseoppdateringen ble satt i kø
luckperms.command.bulkupdate.confirm=Kjør {0} for å utføre oppdateringen
luckperms.command.bulkupdate.unknown-id=Handlingen med id {0} eksisterer ikke eller den er utløpt
luckperms.command.bulkupdate.starting=Kjører masseoppdatering
luckperms.command.bulkupdate.success=Masseoppdatering fullført
luckperms.command.bulkupdate.success.statistics.nodes=Totalt berørte noder
luckperms.command.bulkupdate.success.statistics.users=Totalt berørte brukere
luckperms.command.bulkupdate.success.statistics.groups=Totalt berørte grupper
luckperms.command.bulkupdate.failure=Masseoppdatering feilet, sjekk konsollen for feilmeldinger
luckperms.command.update-task.request=Oppdatering er etterspurt, vennligst vent
luckperms.command.update-task.complete=Oppdatering fullført
luckperms.command.update-task.push.attempting=Forsøker å sende til andre servere
luckperms.command.update-task.push.complete=Andre servere ble vellykket varslet via {0}
luckperms.command.update-task.push.error=Feil under sending av endringer til andre servere
luckperms.command.update-task.push.error-not-setup=Kan ikke sende endringer til andre servere fordi meldingstjenesten er ikke konfigurert
luckperms.command.reload-config.success=Konfigurasjonsfilen ble lastet på nytt
luckperms.command.reload-config.restart-note=noen alternativer vil kun gjelde etter at serveren har startet på nytt
luckperms.command.translations.searching=Søker etter tilgjengelige oversettelser, vennligst vent...
luckperms.command.translations.searching-error=Kan ikke finne en liste over tilgjengelige oversettelser
luckperms.command.translations.installed-translations=Installerte oversettelser
luckperms.command.translations.available-translations=Tilgjengelige oversettelser
luckperms.command.translations.percent-translated={0}% er oversatt
luckperms.command.translations.translations-by=av
luckperms.command.translations.installing=Installerer oversettelser, vennligst vent...
luckperms.command.translations.download-error=Kunne ikke laste ned oversettelse for {0}
luckperms.command.translations.installing-specific=Installerer språk {0}...
luckperms.command.translations.install-complete=Installasjon fullført
luckperms.command.translations.download-prompt=Bruk {0} for å laste ned og installere oppdaterte versjoner av oversettelsene som er laget av fellesskapet
luckperms.command.translations.download-override-warning=Vær oppmerksom på at dette vil overskrive endringer du har gjort på disse språkene
luckperms.usage.user.description=Et sett med kommandoer for å behandle brukere i LuckPerms. (En ''bruker'' i LuckPerms er bare en spiller, og kan refereres til en UUID eller brukernavn)
luckperms.usage.group.description=Et sett med kommandoer for å behandle grupper i LuckPerms. Grupper er bare en samling av tillatelsesnoder som kan gis til brukerne. Nye grupper opprettes med kommandoen ''creategroup''.
luckperms.usage.track.description=Et sett med kommandoer for å behandle spor i LuckPerms. Spor er en stige med en samling av grupper som kan brukes til å definere graderinger og degraderinger.
luckperms.usage.log.description=Et sett med kommandoer for å behandle logg funksjonaliteten i LuckPerms.
luckperms.usage.sync.description=Laster inn all data fra lagring til minne, og tar i bruk alle endringer som er oppdaget.
luckperms.usage.info.description=Viser generell informasjon om den aktive instansen.
luckperms.usage.editor.description=Oppretter en ny web økt for redigering
luckperms.usage.editor.argument.type=typer som skal lastes inn for redigering. (''all'', ''users'' eller ''groups'')
luckperms.usage.editor.argument.filter=tillatelse til å filtrere brukeroppføringer av
luckperms.usage.verbose.description=Kontrollerer informasjonsflyten på overvåkingssystemet for tillatelser.
luckperms.usage.verbose.argument.action=om du vil aktivere/deaktivere logging, eller å laste opp resultatet av loggingen
luckperms.usage.verbose.argument.filter=filter for å samsvare oppføringer mot
luckperms.usage.verbose.argument.commandas=spilleren/kommandoen som skal kjøres
luckperms.usage.tree.description=Genererer en trevisning (ordnet listehierarki) av alle tillatelser som er kjente for LuckPerms.
luckperms.usage.tree.argument.scope=roten av treet. Angi "." for å inkludere alle tillatelser
luckperms.usage.tree.argument.player=navnet på en pålogget spiller som kan sjekkes
luckperms.usage.search.description=Søker etter alle brukere/grupper med en bestemt tillatelse
luckperms.usage.search.argument.permission=tillatelsen å søke etter
luckperms.usage.search.argument.page=siden å vise
luckperms.usage.network-sync.description=Synkroniser endringer med lagringsenheten og be om at alle andre servere på nettverket gjør det samme
luckperms.usage.import.description=Importerer data fra en (tidligere opprettet) eksportfil
luckperms.usage.import.argument.file=filen å importere fra
luckperms.usage.import.argument.replace=erstatte eksisterende data i stedet for sammenslåing
luckperms.usage.import.argument.upload=laste opp data fra en tidligere eksport
luckperms.usage.export.description=Eksporterer alle tillatelsesdata til en ''eksport'' fil. Kan importeres på et senere tidspunkt.
luckperms.usage.export.argument.file=filen som skal eksporteres til
luckperms.usage.export.argument.without-users=ekskluder brukere fra eksporten
luckperms.usage.export.argument.without-groups=ekskluder grupper fra eksporten
luckperms.usage.export.argument.upload=Laste opp alle tillatelsesdata til web redigering. Kan importeres på nytt senere.
luckperms.usage.reload-config.description=Last inn noen av konfigurasjonsalternativene på nytt
luckperms.usage.bulk-update.description=Kjør masseendring av spørringer på alle data
luckperms.usage.bulk-update.argument.data-type=typen data som endres. (''all'', ''users'' eller ''groups'')
luckperms.usage.bulk-update.argument.action=handlinger som kan utføres på dataene. (''update'' eller ''delete'')
luckperms.usage.bulk-update.argument.action-field=feltet du vil endre på. bare nødvendig ved ''update''. (''permission'', ''server'' eller ''world'')
luckperms.usage.bulk-update.argument.action-value=verdien som skal erstattes med. Kun påkrevd ved ''update''.
luckperms.usage.bulk-update.argument.constraint=begrensningene som kreves for oppdateringen
luckperms.usage.translations.description=Behandle oversettelser
luckperms.usage.translations.argument.install=underkommando for å installere oversettelser
luckperms.usage.apply-edits.description=Gjelder tillatelsesendringer fra web redigeringen
luckperms.usage.apply-edits.argument.code=den unike koden for dataene
luckperms.usage.apply-edits.argument.target=hvem som blir påvirket av dataene
luckperms.usage.create-group.description=Opprett ny gruppe
luckperms.usage.create-group.argument.name=navnet på gruppen
luckperms.usage.create-group.argument.weight=vekten på gruppen
luckperms.usage.create-group.argument.display-name=visningsnavnet til gruppen
luckperms.usage.delete-group.description=Slett en gruppe
luckperms.usage.delete-group.argument.name=navnet på gruppen
luckperms.usage.list-groups.description=Vis alle grupper
luckperms.usage.create-track.description=Opprett et nytt spor
luckperms.usage.create-track.argument.name=navnet på sporet
luckperms.usage.delete-track.description=Slett et spor
luckperms.usage.delete-track.argument.name=navnet på sporet
luckperms.usage.list-tracks.description=Vis alle spor
luckperms.usage.user-info.description=Viser informasjon om brukeren
luckperms.usage.user-switchprimarygroup.description=Bytter brukerens primære gruppe
luckperms.usage.user-switchprimarygroup.argument.group=gruppen som skal byttes til
luckperms.usage.user-promote.description=Grader en bruker opp et spor
luckperms.usage.user-promote.argument.track=sporet for å forfremme brukeren
luckperms.usage.user-promote.argument.context=kontekstene for å forfremme brukeren i
luckperms.usage.user-promote.argument.dont-add-to-first=bare forfremme brukeren hvis de allerede er på sporet
luckperms.usage.user-demote.description=Degrader brukeren ned et spor
luckperms.usage.user-demote.argument.track=sporet for å degradere brukeren
luckperms.usage.user-demote.argument.context=konteksten for å degradere brukeren i
luckperms.usage.user-demote.argument.dont-remove-from-first=forhindrer at brukeren fjernes fra den første gruppen
luckperms.usage.user-clone.description=Klon brukeren
luckperms.usage.user-clone.argument.user=navnet/uuid til brukeren som skal motta klonedata
luckperms.usage.group-info.description=Gir informasjon om gruppen
luckperms.usage.group-listmembers.description=Vis brukere/gruppene som arver fra denne gruppen
luckperms.usage.group-listmembers.argument.page=siden å vise
luckperms.usage.group-setweight.description=Angi gruppens vekt
luckperms.usage.group-setweight.argument.weight=vekten som skal angis
luckperms.usage.group-set-display-name.description=Angi gruppens visningsnavn
luckperms.usage.group-set-display-name.argument.name=navnet som skal angis
luckperms.usage.group-set-display-name.argument.context=kontekstene som skal inneholde navnet
luckperms.usage.group-rename.description=Endre navnet på gruppen
luckperms.usage.group-rename.argument.name=det nye navnet
luckperms.usage.group-clone.description=Klon gruppen
luckperms.usage.group-clone.argument.name=navnet på gruppen som mottar klonedata
luckperms.usage.holder-editor.description=Åpne web redigering for tillatelser
luckperms.usage.holder-showtracks.description=Viser sporene som objektet er på
luckperms.usage.holder-clear.description=Fjerner alle tillatelser, foreldre og meta
luckperms.usage.holder-clear.argument.context=kontekstene å filtrere etter
luckperms.usage.permission.description=Redigere tillatelser
luckperms.usage.parent.description=Redigere arv
luckperms.usage.meta.description=Redigere metaverdier
luckperms.usage.permission-info.description=Viser tillatelser (noder) objektet har
luckperms.usage.permission-info.argument.page=siden å vise
luckperms.usage.permission-info.argument.sort-mode=hvordan sortere oppføringer
luckperms.usage.permission-set.description=Angir en tillatelse for objektet
luckperms.usage.permission-set.argument.node=tillatelsen som skal angis
luckperms.usage.permission-set.argument.value=verdien til noden
luckperms.usage.permission-set.argument.context=hvilken kontekst tillatelsen skal legges i
luckperms.usage.permission-unset.description=Fjern tillatelse fra et objekt
luckperms.usage.permission-unset.argument.node=tillatelsen som skal fjernes
luckperms.usage.permission-unset.argument.context=hvilken kontekst tillatelsen skal fjernes fra
luckperms.usage.permission-settemp.description=Angir en midlertidig tillatelse for objektet
luckperms.usage.permission-settemp.argument.node=tillatelsen som skal angis
luckperms.usage.permission-settemp.argument.value=verdien til noden
luckperms.usage.permission-settemp.argument.duration=varigheten før tillatelsen utløper
luckperms.usage.permission-settemp.argument.temporary-modifier=hvordan de midlertidige tillatelsene skal angis
luckperms.usage.permission-settemp.argument.context=hvilken kontekst tillatelsen skal legges i
luckperms.usage.permission-unsettemp.description=Fjern midlertidig tillatelse fra et objekt
luckperms.usage.permission-unsettemp.argument.node=tillatelsen som skal fjernes
luckperms.usage.permission-unsettemp.argument.duration=subtrahere varigheten
luckperms.usage.permission-unsettemp.argument.context=hvilken kontekst tillatelsen skal fjernes fra
luckperms.usage.permission-check.description=Kontrollerer om objektet har en bestemt tillatelse
luckperms.usage.permission-check.argument.node=tillatelsen å se etter
luckperms.usage.permission-clear.description=Fjern alle tillatelser
luckperms.usage.permission-clear.argument.context=kontekstene å filtrere etter
luckperms.usage.parent-info.description=Viser hvilke grupper dette objektet arver fra
luckperms.usage.parent-info.argument.page=siden å vise
luckperms.usage.parent-info.argument.sort-mode=hvordan sortere oppføringer
luckperms.usage.parent-set.description=Fjerner alle andre grupper objektet arver allerede og legger det til det som er angitt
luckperms.usage.parent-set.argument.group=gruppen som skal settes
luckperms.usage.parent-set.argument.context=konteksten gruppen skal settes i
luckperms.usage.parent-add.description=Angir en annen gruppe som objektet skal arve tillatelser fra
luckperms.usage.parent-add.argument.group=gruppen som skal arve fra
luckperms.usage.parent-add.argument.context=konteksten gruppen skal arves i
luckperms.usage.parent-remove.description=Fjerner en tidligere satt regel for arv
luckperms.usage.parent-remove.argument.group=gruppen som skal fjernes
luckperms.usage.parent-remove.argument.context=konteksten gruppen skal fjernes i
luckperms.usage.parent-set-track.description=Fjerner alle andre grupper objektet arver allerede i det angitte sporet, og legger det til i den angitte gruppen
luckperms.usage.parent-set-track.argument.track=sporet som skal settes på
luckperms.usage.parent-set-track.argument.group=gruppen som skal settes, eller et tall knyttet til gruppens posisjon på det angitte sporet
luckperms.usage.parent-set-track.argument.context=konteksten gruppen skal settes i
luckperms.usage.parent-add-temp.description=Angir en annen gruppe som objektet skal arve tillatelser fra midlertidig
luckperms.usage.parent-add-temp.argument.group=gruppen som skal arve fra
luckperms.usage.parent-add-temp.argument.duration=varigheten av gruppemedlemskapet
luckperms.usage.parent-add-temp.argument.temporary-modifier=hvordan de midlertidige tillatelsene skal settes
luckperms.usage.parent-add-temp.argument.context=konteksten gruppen skal arves i
luckperms.usage.parent-remove-temp.description=Fjerner en tidligere satt regel for midlertidig arv
luckperms.usage.parent-remove-temp.argument.group=gruppen som skal fjernes
luckperms.usage.parent-remove-temp.argument.duration=subtrahere varigheten
luckperms.usage.parent-remove-temp.argument.context=konteksten gruppen skal fjernes i
luckperms.usage.parent-clear.description=Fjerner alle foreldre
luckperms.usage.parent-clear.argument.context=kontekstene å filtrere etter
luckperms.usage.parent-clear-track.description=Fjerner alle foreldre på et gitt spor
luckperms.usage.parent-clear-track.argument.track=sporet som skal fjernes på
luckperms.usage.parent-clear-track.argument.context=kontekstene å filtrere etter
luckperms.usage.meta-info.description=Viser all chat meta
luckperms.usage.meta-set.description=Angir en meta verdi
luckperms.usage.meta-set.argument.key=nøkkelen som skal settes
luckperms.usage.meta-set.argument.value=verdien som skal settes
luckperms.usage.meta-set.argument.context=kontekstene for å legge metaparet i
luckperms.usage.meta-unset.description=Fjern en metaverdi
luckperms.usage.meta-unset.argument.key=nøkkelen som skal fjernes
luckperms.usage.meta-unset.argument.context=kontekstene for å fjerne metaparet i
luckperms.usage.meta-settemp.description=Angir en midlertidig metaverdi
luckperms.usage.meta-settemp.argument.key=nøkkelen som skal settes
luckperms.usage.meta-settemp.argument.value=verdien som skal settes
luckperms.usage.meta-settemp.argument.duration=varigheten før metaverdien utløper
luckperms.usage.meta-settemp.argument.context=kontekstene for å legge metaparet i
luckperms.usage.meta-unsettemp.description=Fjerner en midlertidig metaverdi
luckperms.usage.meta-unsettemp.argument.key=nøkkelen som skal fjernes
luckperms.usage.meta-unsettemp.argument.context=kontekstene for å fjerne metaparet i
luckperms.usage.meta-addprefix.description=Legg til prefiks
luckperms.usage.meta-addprefix.argument.priority=sett prioritet på prefiksen
luckperms.usage.meta-addprefix.argument.prefix=prefiks tekst
luckperms.usage.meta-addprefix.argument.context=konteksten for å legge prefiksen i
luckperms.usage.meta-addsuffix.description=Legg til suffiks
luckperms.usage.meta-addsuffix.argument.priority=sett prioritet på suffiksen
luckperms.usage.meta-addsuffix.argument.suffix=suffiks tekst
luckperms.usage.meta-addsuffix.argument.context=konteksten for å legge suffiksen i
luckperms.usage.meta-setprefix.description=Angi et prefiks
luckperms.usage.meta-setprefix.argument.priority=sett prioritet på prefiksen
luckperms.usage.meta-setprefix.argument.prefix=prefiks tekst
luckperms.usage.meta-setprefix.argument.context=konteksten for å legge prefiksen i
luckperms.usage.meta-setsuffix.description=Angi et suffiks
luckperms.usage.meta-setsuffix.argument.priority=sett prioritet på suffiksen
luckperms.usage.meta-setsuffix.argument.suffix=suffiks tekst
luckperms.usage.meta-setsuffix.argument.context=konteksten for å legge suffiksen i
luckperms.usage.meta-removeprefix.description=Fjerner et prefiks
luckperms.usage.meta-removeprefix.argument.priority=fjern prefiks med bestemt prioritetsverdi
luckperms.usage.meta-removeprefix.argument.prefix=prefiks tekst
luckperms.usage.meta-removeprefix.argument.context=konteksten prefiksen skal fjernes i
luckperms.usage.meta-removesuffix.description=Fjerner et suffiks
luckperms.usage.meta-removesuffix.argument.priority=fjern suffiks med bestemt prioritetsverdi
luckperms.usage.meta-removesuffix.argument.suffix=suffiks tekst
luckperms.usage.meta-removesuffix.argument.context=konteksten suffiksen skal fjernes i
luckperms.usage.meta-addtemp-prefix.description=Legger til et midlertidig prefiks
luckperms.usage.meta-addtemp-prefix.argument.priority=sett prioritet på prefiksen
luckperms.usage.meta-addtemp-prefix.argument.prefix=prefiks tekst
luckperms.usage.meta-addtemp-prefix.argument.duration=varigheten før prefiksen utløper
luckperms.usage.meta-addtemp-prefix.argument.context=konteksten for å legge prefiksen i
luckperms.usage.meta-addtemp-suffix.description=Legger til et midlertidig suffiks
luckperms.usage.meta-addtemp-suffix.argument.priority=sett prioritet på suffiksen
luckperms.usage.meta-addtemp-suffix.argument.suffix=suffiks tekst
luckperms.usage.meta-addtemp-suffix.argument.duration=varigheten før suffiksen utløper
luckperms.usage.meta-addtemp-suffix.argument.context=konteksten for å legge suffiksen i
luckperms.usage.meta-settemp-prefix.description=Legger til et midlertidig prefiks
luckperms.usage.meta-settemp-prefix.argument.priority=sett prioritet på prefiksen
luckperms.usage.meta-settemp-prefix.argument.prefix=prefiks tekst
luckperms.usage.meta-settemp-prefix.argument.duration=varigheten før prefiksen utløper
luckperms.usage.meta-settemp-prefix.argument.context=konteksten for å legge prefiksen i
luckperms.usage.meta-settemp-suffix.description=Legger til et midlertidig suffiks
luckperms.usage.meta-settemp-suffix.argument.priority=sett prioritet på suffiksen
luckperms.usage.meta-settemp-suffix.argument.suffix=suffiks tekst
luckperms.usage.meta-settemp-suffix.argument.duration=varigheten før suffiksen utløper
luckperms.usage.meta-settemp-suffix.argument.context=konteksten for å legge suffiksen i
luckperms.usage.meta-removetemp-prefix.description=Fjerner et midlertidig prefiks
luckperms.usage.meta-removetemp-prefix.argument.priority=fjern prefiks med bestemt prioritetsverdi
luckperms.usage.meta-removetemp-prefix.argument.prefix=prefiks tekst
luckperms.usage.meta-removetemp-prefix.argument.context=konteksten prefiksen skal fjernes i
luckperms.usage.meta-removetemp-suffix.description=Fjerner et midlertidig suffiks
luckperms.usage.meta-removetemp-suffix.argument.priority=fjern suffiks med bestemt prioritetsverdi
luckperms.usage.meta-removetemp-suffix.argument.suffix=suffiks tekst
luckperms.usage.meta-removetemp-suffix.argument.context=konteksten suffiksen skal fjernes i
luckperms.usage.meta-clear.description=Fjerner alle meta
luckperms.usage.meta-clear.argument.type=hvilken type meta som skal fjernes
luckperms.usage.meta-clear.argument.context=kontekstene å filtrere etter
luckperms.usage.track-info.description=Gir informasjon om sporet
luckperms.usage.track-editor.description=Åpne web redigering for tillatelser
luckperms.usage.track-append.description=Tilføy en gruppe på slutten av sporet
luckperms.usage.track-append.argument.group=gruppen som skal legges til
luckperms.usage.track-insert.description=Setter inn en gruppe på en angitt posisjon langs sporet
luckperms.usage.track-insert.argument.group=gruppen som skal legges til
luckperms.usage.track-insert.argument.position=angi posisjonen gruppen skal plasseres ved (første posisjon på sporet er 1)
luckperms.usage.track-remove.description=Fjerner en gruppe fra sporet
luckperms.usage.track-remove.argument.group=gruppen som skal fjernes
luckperms.usage.track-clear.description=Fjerner gruppene på sporet
luckperms.usage.track-rename.description=Endre navn på sporet
luckperms.usage.track-rename.argument.name=det nye navnet
luckperms.usage.track-clone.description=Klon sporet
luckperms.usage.track-clone.argument.name=navnet på sporet som mottar klonedata
luckperms.usage.log-recent.description=Viser nylige handlinger
luckperms.usage.log-recent.argument.user=filtrer på bruker ved å angi navn/uuid
luckperms.usage.log-recent.argument.page=nummer på siden du vil lese
luckperms.usage.log-search.description=Søk i loggen etter en oppføring
luckperms.usage.log-search.argument.query=spørring å søke etter
luckperms.usage.log-search.argument.page=nummer på siden du vil lese
luckperms.usage.log-notify.description=Vis/skjul loggvarsler
luckperms.usage.log-notify.argument.toggle=om du skal slå av eller på
luckperms.usage.log-user-history.description=Vis brukerens historikk
luckperms.usage.log-user-history.argument.user=navn/uuid for brukeren
luckperms.usage.log-user-history.argument.page=nummer på siden du vil lese
luckperms.usage.log-group-history.description=Vis gruppehistorikk
luckperms.usage.log-group-history.argument.group=navnet på gruppen
luckperms.usage.log-group-history.argument.page=nummer på siden du vil lese
luckperms.usage.log-track-history.description=Vis sporets historikk
luckperms.usage.log-track-history.argument.track=navnet på sporet
luckperms.usage.log-track-history.argument.page=nummer på siden du vil lese
luckperms.usage.sponge.description=Rediger ekstra Sponge data
luckperms.usage.sponge.argument.collection=samling som skal spørres
luckperms.usage.sponge.argument.subject=subjektet som skal endres
luckperms.usage.sponge-permission-info.description=Viser informasjon om subjektets tillatelser
luckperms.usage.sponge-permission-info.argument.contexts=kontekstene å filtrere etter
luckperms.usage.sponge-permission-set.description=Angir en tillatelse for objektet
luckperms.usage.sponge-permission-set.argument.node=tillatelsen som skal angis
luckperms.usage.sponge-permission-set.argument.tristate=angi verdi på tillatelsen
luckperms.usage.sponge-permission-set.argument.contexts=hvilken kontekst tillatelsen skal legges i
luckperms.usage.sponge-permission-clear.description=Fjern subjektets tillatelser
luckperms.usage.sponge-permission-clear.argument.contexts=hvilken kontekst tillatelsen skal fjernes fra
luckperms.usage.sponge-parent-info.description=Viser informasjon om subjektets foreldre
luckperms.usage.sponge-parent-info.argument.contexts=kontekstene å filtrere etter
luckperms.usage.sponge-parent-add.description=Legger til en forelder til subjektet
luckperms.usage.sponge-parent-add.argument.collection=samlingen med subjekter der foreldre subjektet er
luckperms.usage.sponge-parent-add.argument.subject=navnet på foreldre subjektet
luckperms.usage.sponge-parent-add.argument.contexts=konteksten for å legge forelder i
luckperms.usage.sponge-parent-remove.description=Fjerner en forelder fra et subjekt
luckperms.usage.sponge-parent-remove.argument.collection=samlingen med subjekter der foreldre subjektet er
luckperms.usage.sponge-parent-remove.argument.subject=navnet på foreldre subjektet
luckperms.usage.sponge-parent-remove.argument.contexts=konteksten for å fjerne forelder i
luckperms.usage.sponge-parent-clear.description=Fjern subjektets foreldre
luckperms.usage.sponge-parent-clear.argument.contexts=hvilken kontekst foreldre skal fjernes fra
luckperms.usage.sponge-option-info.description=Viser informasjon om subjektets alternativer
luckperms.usage.sponge-option-info.argument.contexts=kontekstene å filtrere etter
luckperms.usage.sponge-option-set.description=Angir et alternativ for subjektet
luckperms.usage.sponge-option-set.argument.key=nøkkelen som skal settes
luckperms.usage.sponge-option-set.argument.value=angi verdi på nøkkelen
luckperms.usage.sponge-option-set.argument.contexts=konteksten for å legge alternativer i
luckperms.usage.sponge-option-unset.description=Fjern et alternativ fra subjektet
luckperms.usage.sponge-option-unset.argument.key=nøkkelen som skal fjernes
luckperms.usage.sponge-option-unset.argument.contexts=konteksten der nøkkelen skal fjernes
luckperms.usage.sponge-option-clear.description=Fjern subjektets alternativer
luckperms.usage.sponge-option-clear.argument.contexts=konteksten for å fjerne alternativer i
