luckperms.logs.actionlog-prefix=LOG
luckperms.logs.verbose-prefix=VB
luckperms.logs.export-prefix=EKSPORTER
luckperms.commandsystem.available-commands=Brug {0} for at se tilgængelige kommandoer
luckperms.commandsystem.command-not-recognised=Kommando ikke genkendt
luckperms.commandsystem.no-permission=Du har ikke tilladelse til at bruge denne kommando\!
luckperms.commandsystem.no-permission-subcommands=Du har ikke tilladelse til at bruge nogen underkommandoer
luckperms.commandsystem.already-executing-command=En anden kommando udføres; afventer dens færdiggørelse...
luckperms.commandsystem.usage.sub-commands-header=Underkommandoer
luckperms.commandsystem.usage.usage-header=Kommando brug
luckperms.commandsystem.usage.arguments-header=Argumenter
luckperms.first-time.no-permissions-setup=Det lader til, at ingen tilladelser er blevet opsat endnu\!
luckperms.first-time.use-console-to-give-access=Før du kan bruge nogen af LuckPerms kommandoerne i spillet, skal du bruge konsollen til at give dig selv adgang
luckperms.first-time.console-command-prompt=Åbn din konsol og kør
luckperms.first-time.next-step=Når du har gjort dette, kan du begynde at definere dine tilladelser og grupper
luckperms.first-time.wiki-prompt=Ved ikke, hvor du skal starte? Tjek her\: {0}
luckperms.login.try-again=Prøv igen senere
luckperms.login.loading-database-error=En database fejl opstod under indlæsning af tilladelses data
luckperms.login.server-admin-check-console-errors=Hvis du er en serveradministrator, så tjek venligst konsollen for eventuelle fejl
luckperms.login.server-admin-check-console-info=Tjek venligst serverkonsollen for mere information
luckperms.login.data-not-loaded-at-pre=Tilladelses data for din bruger blev ikke indlæst i præ-login fasen
luckperms.login.unable-to-continue=kunne ikke fortsætte
luckperms.login.craftbukkit-offline-mode-error=dette skyldes sandsynligvis en konflikt mellem CraftBukkit og online-mode indstillingen
luckperms.login.unexpected-error=En uventet fejl opstod under opsætning af dine tilladelses data
luckperms.opsystem.disabled=Vanilla OP-systemet er deaktiveret på denne server
luckperms.opsystem.sponge-warning=Bemærk, at Server Operator status ikke har nogen effekt på Sponge tilladelse kontrol, når et tilladelse plugin er installeret, skal du redigere brugerdata direkte
luckperms.duration.unit.years.plural={0} år
luckperms.duration.unit.years.singular={0} år
luckperms.duration.unit.years.short={0}år
luckperms.duration.unit.months.plural={0} måneder
luckperms.duration.unit.months.singular={0} måned
luckperms.duration.unit.months.short={0}måned
luckperms.duration.unit.weeks.plural={0} uger
luckperms.duration.unit.weeks.singular={0} uge
luckperms.duration.unit.weeks.short={0}u
luckperms.duration.unit.days.plural={0} dage
luckperms.duration.unit.days.singular={0} dag
luckperms.duration.unit.days.short={0}d
luckperms.duration.unit.hours.plural={0} timer
luckperms.duration.unit.hours.singular={0} time
luckperms.duration.unit.hours.short={0}t
luckperms.duration.unit.minutes.plural={0} minutter
luckperms.duration.unit.minutes.singular={0} minut
luckperms.duration.unit.minutes.short={0}min
luckperms.duration.unit.seconds.plural={0} sekunder
luckperms.duration.unit.seconds.singular={0} sekund
luckperms.duration.unit.seconds.short={0}s
luckperms.duration.since={0} siden
luckperms.command.misc.invalid-code=Ugyldig kode
luckperms.command.misc.response-code-key=svarkode
luckperms.command.misc.error-message-key=besked
luckperms.command.misc.bytebin-unable-to-communicate=Kunne ikke kommunikere med bytebin
luckperms.command.misc.webapp-unable-to-communicate=Kunne ikke kommunikere med web-appen
luckperms.command.misc.check-console-for-errors=Tjek konsollen for fejl
luckperms.command.misc.file-must-be-in-data=Filen {0} skal være et direkte barn af datamappen
luckperms.command.misc.wait-to-finish=Vent venligst på at den er færdig og prøv igen
luckperms.command.misc.invalid-priority=Ugyldig prioritet {0}
luckperms.command.misc.expected-number=Forventede et tal
luckperms.command.misc.date-parse-error=Kunne ikke parse datoen {0}
luckperms.command.misc.date-in-past-error=Du kan ikke sætte en dato fra fortiden\!
luckperms.command.misc.page=side {0} af {1}
luckperms.command.misc.page-entries={0} poster
luckperms.command.misc.none=Ingen
luckperms.command.misc.loading.error.unexpected=Der opstod en uventet fejl
luckperms.command.misc.loading.error.user=Bruger ikke indlæst
luckperms.command.misc.loading.error.user-specific=Kunne ikke indlæse målbrugeren {0}
luckperms.command.misc.loading.error.user-not-found=En bruger for {0} kunne ikke findes
luckperms.command.misc.loading.error.user-save-error=Der var en fejl med at gemme brugerdata for {0}
luckperms.command.misc.loading.error.user-not-online=Brugeren {0} er ikke online
luckperms.command.misc.loading.error.user-invalid={0} er ikke et gyldigt brugernavn eller UUID
luckperms.command.misc.loading.error.user-not-uuid=Målbrugeren {0} er ikke et gyldigt uuid
luckperms.command.misc.loading.error.group=Gruppen blev ikke indlæst
luckperms.command.misc.loading.error.all-groups=Kunne ikke indlæse alle grupper
luckperms.command.misc.loading.error.group-not-found=En gruppe navngivet {0} kunne ikke blive fundet
luckperms.command.misc.loading.error.group-save-error=Der var en fejl med at gemme gruppedata for {0}
luckperms.command.misc.loading.error.group-invalid={0} er ikke et gyldigt gruppenavn
luckperms.command.misc.loading.error.track=Sporet er ikke indlæst
luckperms.command.misc.loading.error.all-tracks=Kunne ikke indlæse alle spor
luckperms.command.misc.loading.error.track-not-found=Et spor navngivet {0} blev ikke fundet
luckperms.command.misc.loading.error.track-save-error=Der var en fejl med at gemme spordataen {0}
luckperms.command.misc.loading.error.track-invalid={0} er ikke et gyldigt spornavn
luckperms.command.editor.no-match=Kan ikke åbne editor, ingen objekter matchede den ønskede type
luckperms.command.editor.start=Forbereder en ny redigeringssession, vent venligst...
luckperms.command.editor.url=Klik på linket nedenfor for at åbne editoren
luckperms.command.editor.unable-to-communicate=Kunne ikke kommunikere med redigeringsværktøjet
luckperms.command.editor.apply-edits.success=Data fra redigeringsværktøjet blev anvendt til {0} {1} med succes
luckperms.command.editor.apply-edits.success-summary={0} {1} og {2} {3}
luckperms.command.editor.apply-edits.success.additions=tillægninger
luckperms.command.editor.apply-edits.success.additions-singular=tillægning
luckperms.command.editor.apply-edits.success.deletions=sletninger
luckperms.command.editor.apply-edits.success.deletions-singular=sletning
luckperms.command.editor.apply-edits.no-changes=Ingen ændringer blev anvendt fra webeditoren, de returnerede data indeholdt ingen redigeringer
luckperms.command.editor.apply-edits.unknown-type=Kan ikke anvende redigering på den angivne objekttype
luckperms.command.editor.apply-edits.unable-to-read=Kan ikke læse data med den givne kode
luckperms.command.search.searching.permission=Søger efter brugere og grupper med {0}
luckperms.command.search.searching.inherit=Søger efter brugere og grupper som arver fra {0}
luckperms.command.search.result=Fandt {0} poster fra {1} brugere og {2} grupper
luckperms.command.search.result.default-notice=Bemærk\: når der søges efter medlemmer af standardgruppen, vil offline spillere uden andre tilladelser ikke blive vist\!
luckperms.command.search.showing-users=Viser brugerposter
luckperms.command.search.showing-groups=Viser gruppeposter
luckperms.command.tree.start=Genererer tilladelsestræ. Vent venligst...
luckperms.command.tree.empty=Ikke i stand til at generere træ, ingen resultater blev fundet
luckperms.command.tree.url=Tilladelsestræ URL
luckperms.command.verbose.invalid-filter={0} er ikke et gyldigt verbose filter
luckperms.command.verbose.enabled=Detaljeret logning {0} for tjek der matcher {1}
luckperms.command.verbose.command-exec=Tvinger {0} til at udføre kommando {1} og rapportere alle ændringer foretaget...
luckperms.command.verbose.off=Detaljeret logføring {0}
luckperms.command.verbose.command-exec-complete=Kommandoen blev udført
luckperms.command.verbose.command.no-checks=Kommandoen udført, men ingen tilladelse tjek blev foretaget
luckperms.command.verbose.command.possibly-async=Dette kan skyldes, at pluginet kører kommandoer i baggrunden (async)
luckperms.command.verbose.command.try-again-manually=Du kan stadig bruge detaljeret manuelt til at opdage kontrol foretaget på denne måde
luckperms.command.verbose.enabled-recording=Detaljeret optagelse {0} for tjek der matcher {1}
luckperms.command.verbose.uploading=Detaljeret logning {0}, uploader resultater...
luckperms.command.verbose.url=Detaljeret resultaters URL
luckperms.command.verbose.enabled-term=aktiveret
luckperms.command.verbose.disabled-term=slået fra
luckperms.command.verbose.query-any=ALLE
luckperms.command.info.running-plugin=Kører
luckperms.command.info.platform-key=Platform
luckperms.command.info.server-brand-key=Servermærke
luckperms.command.info.server-version-key=Server Version
luckperms.command.info.storage-key=Lager
luckperms.command.info.storage-type-key=Type
luckperms.command.info.storage.meta.split-types-key=Typer
luckperms.command.info.storage.meta.ping-key=Ping
luckperms.command.info.storage.meta.connected-key=Tilsluttet
luckperms.command.info.storage.meta.file-size-key=Filstørrelse
luckperms.command.info.extensions-key=Udvidelser
luckperms.command.info.messaging-key=Beskeder
luckperms.command.info.instance-key=Instans
luckperms.command.info.static-contexts-key=Statiske kontekster
luckperms.command.info.online-players-key=Online Spillere
luckperms.command.info.online-players-unique={0} unikke
luckperms.command.info.uptime-key=Oppetid
luckperms.command.info.local-data-key=Lokal Data
luckperms.command.info.local-data={0} brugere, {1} grupper, {2} spor
luckperms.command.generic.create.success={0} blev oprettet uden problemer
luckperms.command.generic.create.error=Der opstod en fejl under oprettelsen af {0}
luckperms.command.generic.create.error-already-exists={0} eksisterer allerede\!
luckperms.command.generic.delete.success={0} er blevet slettet uden problemer
luckperms.command.generic.delete.error=Der opstod en fejl under sletningen af {0}
luckperms.command.generic.delete.error-doesnt-exist={0} eksisterer ikke\!
luckperms.command.generic.rename.success={0} blev omdøbt til {1}
luckperms.command.generic.clone.success={0} blev klonet på {1}
luckperms.command.generic.info.parent.title=Overordnet Gruppe
luckperms.command.generic.info.parent.temporary-title=Midlertidige Overordnede Grupper
luckperms.command.generic.info.expires-in=udløber om
luckperms.command.generic.info.inherited-from=nedarvet fra
luckperms.command.generic.info.inherited-from-self=selv
luckperms.command.generic.show-tracks.title={0}''s Spor
luckperms.command.generic.show-tracks.empty={0} er ikke på nogen spor
luckperms.command.generic.clear.node-removed={0} indholdselementer blev fjernet
luckperms.command.generic.clear.node-removed-singular={0} indholdselement blev fjernet
luckperms.command.generic.clear={0}''s indholdselementer blev ryddet i kontekst {1}
luckperms.command.generic.permission.info.title={0}''s Tilladelser
luckperms.command.generic.permission.info.empty={0} har ingen rettigheder angivet
luckperms.command.generic.permission.info.click-to-remove=Klik for at fjerne dette indholdselement fra {0}
luckperms.command.generic.permission.check.info.title=Tilladelsesoplysninger for {0}
luckperms.command.generic.permission.check.info.directly={0} har {1} indstillet til {2} i kontekst {3}
luckperms.command.generic.permission.check.info.inherited={0} arver {1} indstillet til {2} fra {3} i kontekst {4}
luckperms.command.generic.permission.check.info.not-directly={0} har ikke {1} indstillet
luckperms.command.generic.permission.check.info.not-inherited={0} arver ikke {1}
luckperms.command.generic.permission.check.result.title=Tilladelsestjek for {0}
luckperms.command.generic.permission.check.result.result-key=Resultat
luckperms.command.generic.permission.check.result.processor-key=Processor
luckperms.command.generic.permission.check.result.cause-key=Årsag
luckperms.command.generic.permission.check.result.context-key=Kontekst
luckperms.command.generic.permission.set=Sæt {0} til {1} for {2} i kontekst {3}
luckperms.command.generic.permission.already-has={0} har allerede {1} sat i kontekst {2}
luckperms.command.generic.permission.set-temp=Sæt {0} til {1} for {2} for en varighed af {3} i kontekst {4}
luckperms.command.generic.permission.already-has-temp={0} har allerede {1} midlertidigt indstillet i kontekst {2}
luckperms.command.generic.permission.unset=Fjern {0} for {1} i kontekst {2}
luckperms.command.generic.permission.doesnt-have={0} har ikke {1} sat i kontekst {2}
luckperms.command.generic.permission.unset-temp=Fjern midlertidig tilladelse {0} for {1} i kontekst {2}
luckperms.command.generic.permission.subtract=Sæt {0} til {1} for {2} for en varighed af {3} i kontekst {4}, {5} mindre end før
luckperms.command.generic.permission.doesnt-have-temp={0} har ikke {1} indstillet midlertidigt i kontekst {2}
luckperms.command.generic.permission.clear={0}''s rettigheder blev ryddet i kontekst {1}
luckperms.command.generic.parent.info.title={0}''s Forældre
luckperms.command.generic.parent.info.empty={0} har ingen forældre defineret
luckperms.command.generic.parent.info.click-to-remove=Klik for at fjerne denne forælder fra {0}
luckperms.command.generic.parent.add={0} arver nu tilladelser fra {1} i kontekst {2}
luckperms.command.generic.parent.add-temp={0} arver nu tilladelser fra {1} for en varighed af {2} i kontekst {3}
luckperms.command.generic.parent.set={0} fik deres eksisterende forældre gruppe fjernet, og arver kun {1} i kontekst {2}
luckperms.command.generic.parent.set-track={0} fik deres eksisterende forældre gruppe fjernet, på track {1}, og arver kun {2} i kontekst {3}
luckperms.command.generic.parent.remove={0} arver ikke længere tilladelser fra {1} i kontekst {2}
luckperms.command.generic.parent.remove-temp={0} arver ikke længere midlertidigt tilladelser fra {1} i kontekst {2}
luckperms.command.generic.parent.subtract={0} vil arve tilladelser fra {1} for en varighed af {2} i kontekst {3}, {4} mindre end før
luckperms.command.generic.parent.clear={0}''s forældre blev ryddet i kontekst {1}
luckperms.command.generic.parent.clear-track={0}''s forældre på sporet {1} blev ryddet i kontekst {2}
luckperms.command.generic.parent.already-inherits={0} har allerede {1} sat i kontekst {2}
luckperms.command.generic.parent.doesnt-inherit={0} arver ikke fra {1} i kontekst {2}
luckperms.command.generic.parent.already-temp-inherits={0} arver allerede midlertidigt fra {1} i kontekst {2}
luckperms.command.generic.parent.doesnt-temp-inherit={0} arver ikke midlertidigt fra {1} i kontekst {2}
luckperms.command.generic.chat-meta.info.title-prefix={0}''s Præfikses
luckperms.command.generic.chat-meta.info.title-suffix={0}''s Suffikses
luckperms.command.generic.chat-meta.info.none-prefix={0} har ingen præfikser
luckperms.command.generic.chat-meta.info.none-suffix={0} har ingen suffikser
luckperms.command.generic.chat-meta.info.click-to-remove=Klik for at fjerne denne {0} fra {1}
luckperms.command.generic.chat-meta.already-has={0} har allerede {1} {2} sat til en prioritet på {3} i kontekst {4}
luckperms.command.generic.chat-meta.already-has-temp={0} har allerede {1} {2} midlertidigt sat til en prioritet på {3} i kontekst {4}
luckperms.command.generic.chat-meta.doesnt-have={0} har ikke {1} {2} sat til en prioritet på {3} i kontekst {4}
luckperms.command.generic.chat-meta.doesnt-have-temp={0} har ikke {1} {2} sat til en midlertidigt prioritet på {3} i kontekst {4}
luckperms.command.generic.chat-meta.add={0} havde {1} {2} sat til en prioritet på {3} i kontekst {4}
luckperms.command.generic.chat-meta.add-temp={0} havde {1} {2} sat til en prioritet på {3} for en varighed af {4} i kontekst {5}
luckperms.command.generic.chat-meta.remove={0} havde {1} {2} i prioritet {3} fjernet i kontekst {4}
luckperms.command.generic.chat-meta.remove-bulk={0} havde alle {1} med prioritet {2} fjernet i kontekst {3}
luckperms.command.generic.chat-meta.remove-temp={0} havde midlertidig {1} {2} med prioritet {3} fjernet i kontekst {4}
luckperms.command.generic.chat-meta.remove-temp-bulk={0} havde alle midlertidige {1} med prioritet {2} fjernet i kontekst {3}
luckperms.command.generic.meta.info.title={0}''s Meta
luckperms.command.generic.meta.info.none={0} har ingen meta
luckperms.command.generic.meta.info.click-to-remove=Klik for at fjerne denne meta node fra {0}
luckperms.command.generic.meta.already-has={0} har allerede metanøgle {1} sat til {2} i kontekst {3}
luckperms.command.generic.meta.already-has-temp={0} har allerede metanøgle {1} midlertidigt indstillet til {2} i kontekst {3}
luckperms.command.generic.meta.doesnt-have={0} har ikke metanøgle {1} sat i kontekst {2}
luckperms.command.generic.meta.doesnt-have-temp={0} har ikke metanøgle {1} sat midlertidigt i kontekst {2}
luckperms.command.generic.meta.set=Sæt metanøgle {0} til {1} for {2} i kontekst {3}
luckperms.command.generic.meta.set-temp=Sæt metanøgle {0} til {1} for {2} for en varighed af {3} i kontekst {4}
luckperms.command.generic.meta.unset=Fjern metanøgle {0} for {1} i kontekst {2}
luckperms.command.generic.meta.unset-temp=Fjern midlertidig metanøgle {0} for {1} i kontekst {2}
luckperms.command.generic.meta.clear={0}''s meta matchende type {1} blev ryddet i kontekst {2}
luckperms.command.generic.contextual-data.title=Kontekstuel Data
luckperms.command.generic.contextual-data.mode.key=tilstand
luckperms.command.generic.contextual-data.mode.server=server
luckperms.command.generic.contextual-data.mode.active-player=aktiv spiller
luckperms.command.generic.contextual-data.contexts-key=Kontekster
luckperms.command.generic.contextual-data.prefix-key=Præfiks
luckperms.command.generic.contextual-data.suffix-key=Suffiks
luckperms.command.generic.contextual-data.primary-group-key=Primær Gruppe
luckperms.command.generic.contextual-data.meta-key=Meta
luckperms.command.generic.contextual-data.null-result=Ingen
luckperms.command.user.info.title=Brugeroplysninger
luckperms.command.user.info.uuid-key=UUID
luckperms.command.user.info.uuid-type-key=type
luckperms.command.user.info.uuid-type.mojang=mojang
luckperms.command.user.info.uuid-type.not-mojang=offline
luckperms.command.user.info.status-key=Status
luckperms.command.user.info.status.online=Online
luckperms.command.user.info.status.offline=Offline
luckperms.command.user.removegroup.error-primary=Du kan ikke fjerne en bruger fra deres primære gruppe
luckperms.command.user.primarygroup.not-member={0} Var ikke et medlem af {1}, tilføjer dem nu
luckperms.command.user.primarygroup.already-has={0} har allerede {1} sat som deres primære gruppe
luckperms.command.user.primarygroup.warn-option=Advarsel\: Den primære gruppeberegningsmetode, der anvendes af denne server ({0}) afspejler muligvis ikke denne ændring
luckperms.command.user.primarygroup.set={0}''s primære gruppe blev sat til {1}
luckperms.command.user.track.error-not-contain-group={0} er ikke i nogen grupper på {1}
luckperms.command.user.track.unsure-which-track=Usikker på, hvilken spor der skal bruges, angiv venligst det som et argument
luckperms.command.user.track.missing-group-advice=Opret enten gruppen eller fjern den fra sporet og prøv igen
luckperms.command.user.promote.added-to-first={0} er ikke i nogen grupper på {1}, så de blev føjet til den første gruppe, {2} i kontekst {3}
luckperms.command.user.promote.not-on-track={0} er ikke i nogen grupper på {1}, så brugeren blev ikke forfremmet
luckperms.command.user.promote.success=Forfremmer {0} langs sporet {1} fra {2} til {3} i forbindelse {4}
luckperms.command.user.promote.end-of-track=Slutningen af sporet {0} blev nået, kunne ikke forfremme {1}
luckperms.command.user.promote.next-group-deleted=Den næste gruppe på sporet, {0}, findes ikke længere
luckperms.command.user.promote.unable-to-promote=Kan ikke forfremme brugeren
luckperms.command.user.demote.success=Nedrykker {0} langs sporet {1} fra {2} til {3} i kontekst {4}
luckperms.command.user.demote.end-of-track=Slutningen af sporet {0} blev nået, så {1} blev fjernet fra {2}
luckperms.command.user.demote.end-of-track-not-removed=Slutningen af sporet {0} blev nået, men {1} blev ikke fjernet fra den første gruppe
luckperms.command.user.demote.previous-group-deleted=Den forrige gruppe på sporet, {0}, findes ikke længere
luckperms.command.user.demote.unable-to-demote=Kan ikke degradere brugeren
luckperms.command.group.list.title=Grupper
luckperms.command.group.delete.not-default=Du kan ikke slette standardgruppen
luckperms.command.group.info.title=Gruppeinfo
luckperms.command.group.info.display-name-key=Visningsnavn
luckperms.command.group.info.weight-key=Vægt
luckperms.command.group.setweight.set=Sæt vægt til {0} for gruppe {1}
luckperms.command.group.setdisplayname.doesnt-have={0} har ikke et visningsnavn sat
luckperms.command.group.setdisplayname.already-has={0} har allerede et visningsnavn på {1}
luckperms.command.group.setdisplayname.already-in-use=Visningsnavnet {0} bruges allerede af {1}
luckperms.command.group.setdisplayname.set=Sæt visningsnavn til {0} for gruppe {1} i kontekst {2}
luckperms.command.group.setdisplayname.removed=Fjernede visningsnavn for gruppe {0} i kontekst {1}
luckperms.command.track.list.title=Spor
luckperms.command.track.path.empty=Ingen
luckperms.command.track.info.showing-track=Viser Spor
luckperms.command.track.info.path-property=Sti
luckperms.command.track.clear={0}''s gruppespor er blevet ryddet
luckperms.command.track.append.success=Gruppe {0} blev tilføjet til spor {1}
luckperms.command.track.insert.success=Gruppe {0} blev indsat i sporet {1} på position {2}
luckperms.command.track.insert.error-number=Forventede nummer, men modtog\: {0}
luckperms.command.track.insert.error-invalid-pos=Kan ikke indsætte på position {0}
luckperms.command.track.insert.error-invalid-pos-reason=ugyldig position
luckperms.command.track.remove.success=Gruppen {0} blev fjernet fra sporet {1}
luckperms.command.track.error-empty={0} kan ikke bruges, da det er tomt eller kun indeholder en gruppe
luckperms.command.track.error-multiple-groups={0} er medlem af flere grupper på dette spor
luckperms.command.track.error-ambiguous=Kan ikke afgøre deres placering
luckperms.command.track.already-contains={0} indeholder allerede {1}
luckperms.command.track.doesnt-contain={0} indeholder ikke {1}
luckperms.command.log.load-error=Loggen kunne ikke indlæses
luckperms.command.log.invalid-page=Ugyldigt sidenummer
luckperms.command.log.invalid-page-range=Angiv en værdi mellem {0} og {1}
luckperms.command.log.empty=Ingen log poster at vise
luckperms.command.log.notify.error-console=Kan ikke slå notifikationer til for konsollen
luckperms.command.log.notify.enabled-term=Slået til
luckperms.command.log.notify.disabled-term=Slået fra
luckperms.command.log.notify.changed-state={0} logger udput
luckperms.command.log.notify.already-on=Du modtager allerede notifikationer
luckperms.command.log.notify.already-off=Du modtager ikke notifikationer i øjeblikket
luckperms.command.log.notify.invalid-state=Ukendt tilstand. Forventer {0} eller {1}
luckperms.command.log.show.search=Viser seneste handlinger for forespørgsel {0}
luckperms.command.log.show.recent=Viser seneste handlinger
luckperms.command.log.show.by=Viser seneste handlinger af {0}
luckperms.command.log.show.history=Viser historik for {0} {1}
luckperms.command.export.error-term=Fejl
luckperms.command.export.already-running=En anden eksportproces kører allerede
luckperms.command.export.file.already-exists=Filen {0} findes allerede
luckperms.command.export.file.not-writable=Filen {0} er ikke skrivbar
luckperms.command.export.file.success=Eksporteret succesfuldt til {0}
luckperms.command.export.file-unexpected-error-writing=Der opstod en uventet fejl under skrivning til filen
luckperms.command.export.web.export-code=Eksportér kode
luckperms.command.export.web.import-command-description=Brug følgende kommando til at importere
luckperms.command.import.term=Importer
luckperms.command.import.error-term=Fejl
luckperms.command.import.already-running=En anden importproces kører allerede
luckperms.command.import.file.doesnt-exist=Filen {0} eksisterer ikke
luckperms.command.import.file.not-readable=Filen {0} er ikke læsbar
luckperms.command.import.file.unexpected-error-reading=En uventet fejl opstod under læsning fra importfilen
luckperms.command.import.file.correct-format=er det, det korrekte format?
luckperms.command.import.web.unable-to-read=Kan ikke læse data med den givne kode
luckperms.command.import.progress.percent={0}% færdig
luckperms.command.import.progress.operations={0}/{1} handlinger fuldført
luckperms.command.import.starting=Starter importeringsprocessen
luckperms.command.import.completed=FULDFØRT
luckperms.command.import.duration=tog {0} sekunder
luckperms.command.bulkupdate.must-use-console=Masseopdaterings kommandoen kan kun bruges fra konsollen
luckperms.command.bulkupdate.invalid-data-type=Ugyldig type, forventede {0}
luckperms.command.bulkupdate.invalid-constraint=Ugyldig begrænsning {0}
luckperms.command.bulkupdate.invalid-constraint-format=Restriktioner skal være i formatet {0}
luckperms.command.bulkupdate.invalid-comparison=Ugyldig sammenligningsudbyder {0}
luckperms.command.bulkupdate.invalid-comparison-format=Forventede en af følgende\: {0}
luckperms.command.bulkupdate.queued=Masseopdateringshandlingen blev sat i kø
luckperms.command.bulkupdate.confirm=Kør {0} for at køre opdateringen
luckperms.command.bulkupdate.unknown-id=Handlingen med id''et {0} eksisterer ikke eller er udløbet
luckperms.command.bulkupdate.starting=Kører masseopdatering
luckperms.command.bulkupdate.success=Masseopdatering gennemført
luckperms.command.bulkupdate.success.statistics.nodes=I alt påvirkede knudepunkter
luckperms.command.bulkupdate.success.statistics.users=Påvirkede brugere i alt
luckperms.command.bulkupdate.success.statistics.groups=I alt påvirkede grupper
luckperms.command.bulkupdate.failure=Masseopdatering fejlede, tjek konsollen for fejl
luckperms.command.update-task.request=Der er anmodet om en opdateringsopgave, vent venligst
luckperms.command.update-task.complete=Opdateringsopgave fuldført
luckperms.command.update-task.push.attempting=Forsøger nu at skubbe ud til andre servere
luckperms.command.update-task.push.complete=Andre servere blev underrettet via {0}
luckperms.command.update-task.push.error=Fejl under trykning af ændringer til andre servere
luckperms.command.update-task.push.error-not-setup=Kan ikke sende ændringer til andre servere, da en besked tjeneste ikke er konfigureret
luckperms.command.reload-config.success=Konfigurationsfilen blev genindlæst
luckperms.command.reload-config.restart-note=nogle indstillinger vil kun gælde efter serveren er genstartet
luckperms.command.translations.searching=Søger efter tilgængelige oversættelser, vent venligst...
luckperms.command.translations.searching-error=Kan ikke hente en liste over tilgængelige oversættelser
luckperms.command.translations.installed-translations=Installerede Oversættelser
luckperms.command.translations.available-translations=Tilgængelige Oversættelser
luckperms.command.translations.percent-translated={0}% oversat
luckperms.command.translations.translations-by=af
luckperms.command.translations.installing=Installerer oversættelser, vent venligst...
luckperms.command.translations.download-error=Kunne ikke downloade oversættelse til {0}
luckperms.command.translations.installing-specific=Installerer sprog {0}...
luckperms.command.translations.install-complete=Installation gennemført
luckperms.command.translations.download-prompt=Brug {0} til at downloade og installere opdaterede versioner af disse oversættelser fra fællesskabet
luckperms.command.translations.download-override-warning=Bemærk, at dette vil overskrive de ændringer, du har foretaget for disse sprog
luckperms.usage.user.description=Et sæt kommandoer til håndtering af brugere i LuckPerms. (En ''bruger'' i LuckPerms er blot en spiller, og kan henvise til et UUID eller brugernavn)
luckperms.usage.group.description=Et sæt kommandoer til håndtering af grupper i LuckPerms. Grupper er er blot samlinger af tilladelser, der kan gives til brugere. Nye grupper laves ved hjælp af ''creategroup''-kommandoen.
luckperms.usage.track.description=Et sæt kommandoer til styring af spor i LuckPerms. Spor er en ordnet samling af grupper, som kan bruges til at definere forfremmelser og demoteringer.
luckperms.usage.log.description=Et sæt kommandoer til styring af logning funktionalitet i LuckPerms.
luckperms.usage.sync.description=Genindlæser alle data fra plugins lagring i hukommelsen, og anvender eventuelle ændringer, der registreres.
luckperms.usage.info.description=Udskriver generel information om den aktive plugin instans.
luckperms.usage.editor.description=Opretter en ny webredigeringssession
luckperms.usage.editor.argument.type=typer der skal indlæses i editoren. (''alle'', ''brugere'' eller ''grupper'')
luckperms.usage.editor.argument.filter=tilladelse til at filtrere brugerindgange efter
luckperms.usage.verbose.description=Kontrollerer plugins verbose tilladelse kontrol overvågningssystem.
luckperms.usage.verbose.argument.action=om logning skal aktiveres/deaktivere eller uploade logget output
luckperms.usage.verbose.argument.filter=filteret der skal matches poster mod
luckperms.usage.verbose.argument.commandas=spilleren/kommandoen der skal køres
luckperms.usage.tree.description=Genererer en trævisning (sorteret liste hierarki) af alle tilladelser kendt af LuckPerms.
luckperms.usage.tree.argument.scope=roden af træet. Angiv "." for at inkludere alle tilladelser
luckperms.usage.tree.argument.player=navnet på en online-spiller, der skal tjekkes mod
luckperms.usage.search.description=Søger efter alle bruger/grupper med en bestemt tilladelse
luckperms.usage.search.argument.permission=tilladelsen at søge efter
luckperms.usage.search.argument.page=siden der skal vises
luckperms.usage.network-sync.description=Synkroniser ændringer med lageret og anmoder om, at alle andre servere på netværket gør det samme
luckperms.usage.import.description=Importdata fra en (tidligere oprettet) eksportfil
luckperms.usage.import.argument.file=filen der skal importeres fra
luckperms.usage.import.argument.replace=erstat eksisterende data i stedet for at fusionere
luckperms.usage.import.argument.upload=upload data fra en tidligere eksport
luckperms.usage.export.description=Eksporterer alle tilladelser data til en ''eksport''-fil. Kan genimporteres på et senere tidspunkt.
luckperms.usage.export.argument.file=filen der skal eksporteres til
luckperms.usage.export.argument.without-users=udelukke brugere fra eksporten
luckperms.usage.export.argument.without-groups=ekskludere grupper fra eksport
luckperms.usage.export.argument.upload=Upload alle tilladelsesdata til webeditoren. Kan genimporteres på et senere tidspunkt.
luckperms.usage.reload-config.description=Genindlæs nogle af konfigurationsindstillingerne
luckperms.usage.bulk-update.description=Kør masseændringsforespørgsler på alle data
luckperms.usage.bulk-update.argument.data-type=den type data, der skal ændres. (''alle'', ''brugere'' eller ''grupper'')
luckperms.usage.bulk-update.argument.action=den handling, der skal udføres på dataene. (''update'' eller ''slette'')
luckperms.usage.bulk-update.argument.action-field=feltet der skal ageres på, kræves kun ved ''opdatering''. (''tilladelse'', ''server'' eller ''verden'')
luckperms.usage.bulk-update.argument.action-value=den værdi der skal erstattes med. kræves kun for ''opdatering''.
luckperms.usage.bulk-update.argument.constraint=de nødvendige der kræves for opdateringen
luckperms.usage.translations.description=Administrer oversættelser
luckperms.usage.translations.argument.install=underkommando til at installere oversættelser
luckperms.usage.apply-edits.description=Anvender tilladelses ændringer foretaget fra webeditoren
luckperms.usage.apply-edits.argument.code=den unikke kode til dataen
luckperms.usage.apply-edits.argument.target=hvem dataen påvirker
luckperms.usage.create-group.description=Opret en ny gruppe
luckperms.usage.create-group.argument.name=navn på gruppe
luckperms.usage.create-group.argument.weight=vægten af gruppen
luckperms.usage.create-group.argument.display-name=visningsnavnet for gruppen
luckperms.usage.delete-group.description=Slet gruppe
luckperms.usage.delete-group.argument.name=navnet på gruppen
luckperms.usage.list-groups.description=Vis alle grupper på platformen
luckperms.usage.create-track.description=Opret et nyt spor
luckperms.usage.create-track.argument.name=navnet på sporet
luckperms.usage.delete-track.description=Slet et spor
luckperms.usage.delete-track.argument.name=navnet på sporet
luckperms.usage.list-tracks.description=Liste over alle spor på platformen
luckperms.usage.user-info.description=Viser information om brugeren
luckperms.usage.user-switchprimarygroup.description=Skifter brugerens primære gruppe
luckperms.usage.user-switchprimarygroup.argument.group=gruppen der skal skiftes til
luckperms.usage.user-promote.description=Forfremmer brugeren op ad sporet
luckperms.usage.user-promote.argument.track=sporet til at promovere brugeren op
luckperms.usage.user-promote.argument.context=konteksten brugeren skal forfremmes i
luckperms.usage.user-promote.argument.dont-add-to-first=kun promover brugeren, hvis den allerede er på sporet
luckperms.usage.user-demote.description=Nedgraderer brugeren et spor ned
luckperms.usage.user-demote.argument.track=sporet til at degraderer brugeren ned
luckperms.usage.user-demote.argument.context=konteksten brugeren skal degraderes i
luckperms.usage.user-demote.argument.dont-remove-from-first=forhindre brugeren i at blive fjernet fra den første gruppe
luckperms.usage.user-clone.description=Klon brugeren
luckperms.usage.user-clone.argument.user=navn/uuid på brugeren til at klone på
luckperms.usage.group-info.description=Giver info om gruppen
luckperms.usage.group-listmembers.description=Vis brugere/grupper som arver fra denne gruppe
luckperms.usage.group-listmembers.argument.page=siden der skal vises
luckperms.usage.group-setweight.description=Indstil gruppernes vægt
luckperms.usage.group-setweight.argument.weight=den vægt, der skal angives
luckperms.usage.group-set-display-name.description=Angiv gruppernes visningsnavn
luckperms.usage.group-set-display-name.argument.name=navnet der skal angives
luckperms.usage.group-set-display-name.argument.context=den kontekst navnet skal indsættes i
luckperms.usage.group-rename.description=Omdøb gruppen
luckperms.usage.group-rename.argument.name=det nye navn
luckperms.usage.group-clone.description=Klon gruppen
luckperms.usage.group-clone.argument.name=navnet på den gruppe, der skal klones på
luckperms.usage.holder-editor.description=Åbner webtilladelseseditoren
luckperms.usage.holder-showtracks.description=Viser en liste over spor som objektet er på
luckperms.usage.holder-clear.description=Fjerner alle tilladelser, forældre og meta
luckperms.usage.holder-clear.argument.context=de kontekster, der skal filtreres efter
luckperms.usage.permission.description=Rediger rettigheder
luckperms.usage.parent.description=Rediger arv
luckperms.usage.meta.description=Rediger metadata værdier
luckperms.usage.permission-info.description=Viser en liste over tilladelser objektet har
luckperms.usage.permission-info.argument.page=siden der skal vises
luckperms.usage.permission-info.argument.sort-mode=hvordan man sorterer posterne
luckperms.usage.permission-set.description=Indstiller en tilladelse for objektet
luckperms.usage.permission-set.argument.node=den tilladelsesnøgle der skal indstilles
luckperms.usage.permission-set.argument.value=værdien af noden
luckperms.usage.permission-set.argument.context=konteksten tilladelsen skal indstilles i
luckperms.usage.permission-unset.description=Fjerner en tilladelse fra objektet
luckperms.usage.permission-unset.argument.node=den tilladelsesnøgle der skal fjernes
luckperms.usage.permission-unset.argument.context=konteksterne til at fjerne tilladelsen i
luckperms.usage.permission-settemp.description=Indstiller en tilladelse for objektet midlertidigt
luckperms.usage.permission-settemp.argument.node=den tilladelsesnøgle der skal indstilles
luckperms.usage.permission-settemp.argument.value=værdien af noden
luckperms.usage.permission-settemp.argument.duration=varigheden indtil tilladelsesnoden udløber
luckperms.usage.permission-settemp.argument.temporary-modifier=hvordan den midlertidige tilladelse bør anvendes
luckperms.usage.permission-settemp.argument.context=konteksten tilladelsen skal indstilles i
luckperms.usage.permission-unsettemp.description=Fjern midlertidig tilladelse fra objektet
luckperms.usage.permission-unsettemp.argument.node=den tilladelsesnøgle der skal fjernes
luckperms.usage.permission-unsettemp.argument.duration=varigheden der skal trækkes fra
luckperms.usage.permission-unsettemp.argument.context=konteksten tilladelsen skal fjernes fra
luckperms.usage.permission-check.description=Kontrollerer om objektet har en bestemt tilladelse
luckperms.usage.permission-check.argument.node=tilladelsen der skal tjekkes efter
luckperms.usage.permission-clear.description=Ryd alle tilladelser
luckperms.usage.permission-clear.argument.context=de kontekster, der skal filtreres efter
luckperms.usage.parent-info.description=Viser en liste over grupper som dette objekt arver fra
luckperms.usage.parent-info.argument.page=siden der skal vises
luckperms.usage.parent-info.argument.sort-mode=hvordan man sorterer posterne
luckperms.usage.parent-set.description=Fjerner alle andre grupper objektet der allerede arves og tilføjer dem til den givne
luckperms.usage.parent-set.argument.group=gruppen der skal sættes
luckperms.usage.parent-set.argument.context=konteksten gruppen skal sættes i
luckperms.usage.parent-add.description=Sætter en anden gruppe for objektet til at arve tilladelser fra
luckperms.usage.parent-add.argument.group=den gruppe, der skal arves fra
luckperms.usage.parent-add.argument.context=konteksten gruppen skal arves i
luckperms.usage.parent-remove.description=Fjerner en tidligere indstillet arve regel
luckperms.usage.parent-remove.argument.group=gruppen der skal fjernes
luckperms.usage.parent-remove.argument.context=konteksten gruppen skal fjernes fra
luckperms.usage.parent-set-track.description=Fjerner alle andre grupper objektet allerede arver fra og tilføjer dem til den givne
luckperms.usage.parent-set-track.argument.track=sporet der skal indsættes på
luckperms.usage.parent-set-track.argument.group=den gruppe, der skal angives, eller et nummer, der vedrører gruppens position på det givne spor
luckperms.usage.parent-set-track.argument.context=konteksten gruppen skal sættes i
luckperms.usage.parent-add-temp.description=Sætter en anden gruppe for objektet til at arve tilladelser fra midlertidigt
luckperms.usage.parent-add-temp.argument.group=den gruppe, der skal arves fra
luckperms.usage.parent-add-temp.argument.duration=varigheden af gruppemedlemskabet
luckperms.usage.parent-add-temp.argument.temporary-modifier=hvordan den midlertidige tilladelse bør anvendes
luckperms.usage.parent-add-temp.argument.context=konteksten gruppen skal sættes i
luckperms.usage.parent-remove-temp.description=Fjerner en tidligere indstillet midlertidig arv regel
luckperms.usage.parent-remove-temp.argument.group=gruppen der skal fjernes
luckperms.usage.parent-remove-temp.argument.duration=varigheden der skal trækkes fra
luckperms.usage.parent-remove-temp.argument.context=konteksten gruppen skal fjernes fra
luckperms.usage.parent-clear.description=Rydder alle forældre
luckperms.usage.parent-clear.argument.context=de kontekster, der skal filtreres efter
luckperms.usage.parent-clear-track.description=Rydder alle forældre på et givet spor
luckperms.usage.parent-clear-track.argument.track=sporet der skal fjernes på
luckperms.usage.parent-clear-track.argument.context=de kontekster, der skal filtreres efter
luckperms.usage.meta-info.description=Viser alle chat meta
luckperms.usage.meta-set.description=Indstiller en metaværdi
luckperms.usage.meta-set.argument.key=nøglen der skal angives
luckperms.usage.meta-set.argument.value=værdien der skal angives
luckperms.usage.meta-set.argument.context=de kontekster, der skal tilføjes metapar i
luckperms.usage.meta-unset.description=Fjern en metaværdi
luckperms.usage.meta-unset.argument.key=nøglen der skal fravælges
luckperms.usage.meta-unset.argument.context=de kontekster, der skal fjernes metapar i
luckperms.usage.meta-settemp.description=Indstiller en metaværdi midlertidigt
luckperms.usage.meta-settemp.argument.key=nøglen der skal angives
luckperms.usage.meta-settemp.argument.value=værdien der skal angives
luckperms.usage.meta-settemp.argument.duration=varigheden indtil metaværdien udløber
luckperms.usage.meta-settemp.argument.context=de kontekster, der skal tilføjes metapar i
luckperms.usage.meta-unsettemp.description=Fjern en midlertidig metaværdi
luckperms.usage.meta-unsettemp.argument.key=nøglen der skal fravælges
luckperms.usage.meta-unsettemp.argument.context=de kontekster, der skal fjernes metapar i
luckperms.usage.meta-addprefix.description=Tilføjer et præfiks
luckperms.usage.meta-addprefix.argument.priority=den prioritet præfikset skal sættes ved
luckperms.usage.meta-addprefix.argument.prefix=præfiks strengen
luckperms.usage.meta-addprefix.argument.context=den prioritet præfikset skal sættes ved
luckperms.usage.meta-addsuffix.description=Tilføjer et suffiks
luckperms.usage.meta-addsuffix.argument.priority=den prioritet præfikset skal sættes ved
luckperms.usage.meta-addsuffix.argument.suffix=suffiks strengen
luckperms.usage.meta-addsuffix.argument.context=konteksten suffixet skal sættes i
luckperms.usage.meta-setprefix.description=Sætter et præfiks
luckperms.usage.meta-setprefix.argument.priority=den prioritet præfikset skal sættes ved
luckperms.usage.meta-setprefix.argument.prefix=præfiks strengen
luckperms.usage.meta-setprefix.argument.context=konteksten præfikset skal sættes i
luckperms.usage.meta-setsuffix.description=Sætter et suffiks
luckperms.usage.meta-setsuffix.argument.priority=den prioritet præfikset skal sættes ved
luckperms.usage.meta-setsuffix.argument.suffix=suffiks strengen
luckperms.usage.meta-setsuffix.argument.context=konteksten suffixet skal sættes i
luckperms.usage.meta-removeprefix.description=Fjerner et præfiks
luckperms.usage.meta-removeprefix.argument.priority=den prioritet præfikset skal fjernes ved
luckperms.usage.meta-removeprefix.argument.prefix=præfiks strengen
luckperms.usage.meta-removeprefix.argument.context=konteksten præfikset skal fjernes ved
luckperms.usage.meta-removesuffix.description=Fjerner et suffiks
luckperms.usage.meta-removesuffix.argument.priority=den prioritet præfikset skal fjernes ved
luckperms.usage.meta-removesuffix.argument.suffix=suffiks strengen
luckperms.usage.meta-removesuffix.argument.context=konteksterne at fjerne suffikset i
luckperms.usage.meta-addtemp-prefix.description=Tilføjer et præfiks midlertidigt
luckperms.usage.meta-addtemp-prefix.argument.priority=den prioritet præfikset skal sættes ved
luckperms.usage.meta-addtemp-prefix.argument.prefix=præfiks strengen
luckperms.usage.meta-addtemp-prefix.argument.duration=varigheden indtil præfikset udløber
luckperms.usage.meta-addtemp-prefix.argument.context=konteksten præfikset skal sættes i
luckperms.usage.meta-addtemp-suffix.description=Tilføjer et suffiks midlertidigt
luckperms.usage.meta-addtemp-suffix.argument.priority=den prioritet suffiks skal sættes ved
luckperms.usage.meta-addtemp-suffix.argument.suffix=suffiks strengen
luckperms.usage.meta-addtemp-suffix.argument.duration=varigheden indtil suffiks udløber
luckperms.usage.meta-addtemp-suffix.argument.context=konteksten suffiks skal sættes i
luckperms.usage.meta-settemp-prefix.description=Tilføjer et præfiks midlertidigt
luckperms.usage.meta-settemp-prefix.argument.priority=den prioritet præfikset skal sættes ved
luckperms.usage.meta-settemp-prefix.argument.prefix=præfiks strengen
luckperms.usage.meta-settemp-prefix.argument.duration=varigheden indtil præfikset udløber
luckperms.usage.meta-settemp-prefix.argument.context=konteksten præfikset skal sættes i
luckperms.usage.meta-settemp-suffix.description=Tilføjer et suffiks midlertidigt
luckperms.usage.meta-settemp-suffix.argument.priority=den prioritet suffikset skal sættes ved
luckperms.usage.meta-settemp-suffix.argument.suffix=suffiks strengen
luckperms.usage.meta-settemp-suffix.argument.duration=varigheden indtil suffiket udløber
luckperms.usage.meta-settemp-suffix.argument.context=konteksten suffikset skal sættes i
luckperms.usage.meta-removetemp-prefix.description=Fjerner et midlertidigt præfiks
luckperms.usage.meta-removetemp-prefix.argument.priority=den prioritet præfikset skal fjernes ved
luckperms.usage.meta-removetemp-prefix.argument.prefix=præfiks strengen
luckperms.usage.meta-removetemp-prefix.argument.context=konteksten præfikset skal fjernes ved
luckperms.usage.meta-removetemp-suffix.description=Fjerner et midlertidigt suffiks
luckperms.usage.meta-removetemp-suffix.argument.priority=den prioritet suffikset skal fjernes ved
luckperms.usage.meta-removetemp-suffix.argument.suffix=suffiks strengen
luckperms.usage.meta-removetemp-suffix.argument.context=konteksterne suffikset skal fjernes ved
luckperms.usage.meta-clear.description=Rydder alle meta data
luckperms.usage.meta-clear.argument.type=den metatype der skal fjernes
luckperms.usage.meta-clear.argument.context=de kontekster, der skal filtreres efter
luckperms.usage.track-info.description=Giver info om sporet
luckperms.usage.track-editor.description=Åbner web-editoren for tilladelser
luckperms.usage.track-append.description=Tilføjer en gruppe til slutningen af sporet
luckperms.usage.track-append.argument.group=gruppen som skal tilføjes
luckperms.usage.track-insert.description=Indsætter en gruppe på en given position langs sporet
luckperms.usage.track-insert.argument.group=den gruppe der skal indsættes
luckperms.usage.track-insert.argument.position=positionen til at indsætte gruppen i (den første position på sporet er 1)
luckperms.usage.track-remove.description=Fjerner en gruppe fra sporet
luckperms.usage.track-remove.argument.group=gruppen der skal fjernes
luckperms.usage.track-clear.description=Fjerner grupperne på sporet
luckperms.usage.track-rename.description=Omdøb sporet
luckperms.usage.track-rename.argument.name=det nye navn
luckperms.usage.track-clone.description=Klon sporet
luckperms.usage.track-clone.argument.name=navnet på det spor, der skal klones på
luckperms.usage.log-recent.description=Vis seneste handlinger
luckperms.usage.log-recent.argument.user=navn/uuid på brugeren der skal søges efter
luckperms.usage.log-recent.argument.page=sidenummeret der skal vises
luckperms.usage.log-search.description=Søg i loggen efter en post
luckperms.usage.log-search.argument.query=forespørgslen der skal søges efter
luckperms.usage.log-search.argument.page=sidenummeret der skal vises
luckperms.usage.log-notify.description=Slå log notifikationer til/fra
luckperms.usage.log-notify.argument.toggle=om den skal slås til eller fra
luckperms.usage.log-user-history.description=Se en brugers historik
luckperms.usage.log-user-history.argument.user=navn/uuid på brugeren
luckperms.usage.log-user-history.argument.page=sidenummeret der skal vises
luckperms.usage.log-group-history.description=Vis en gruppes historik
luckperms.usage.log-group-history.argument.group=navnet på gruppen
luckperms.usage.log-group-history.argument.page=sidenummeret der skal vises
luckperms.usage.log-track-history.description=Se et spors historik
luckperms.usage.log-track-history.argument.track=navnet på sporet
luckperms.usage.log-track-history.argument.page=sidenummeret der skal vises
luckperms.usage.sponge.description=Rediger ekstra Sponge data
luckperms.usage.sponge.argument.collection=den samling der skal søges på
luckperms.usage.sponge.argument.subject=det emne der skal ændres
luckperms.usage.sponge-permission-info.description=Viser info om emnets tilladelser
luckperms.usage.sponge-permission-info.argument.contexts=de kontekster, der skal filtreres efter
luckperms.usage.sponge-permission-set.description=Indstiller en tilladelse for objektet
luckperms.usage.sponge-permission-set.argument.node=den tilladelsesnøgle der skal indstilles
luckperms.usage.sponge-permission-set.argument.tristate=værdien som tilladelsen skal sættes til
luckperms.usage.sponge-permission-set.argument.contexts=konteksten til at indstille indstillingen i
luckperms.usage.sponge-permission-clear.description=Rydder emneindstillingerne
luckperms.usage.sponge-permission-clear.argument.contexts=konteksten der skal ryddes for rettigheder
luckperms.usage.sponge-parent-info.description=Viser info om emnets forældre
luckperms.usage.sponge-parent-info.argument.contexts=de kontekster, der skal filtreres efter
luckperms.usage.sponge-parent-add.description=Tilføjer en forælder til emnet
luckperms.usage.sponge-parent-add.argument.collection=samlingen hvor forældre emnerne er
luckperms.usage.sponge-parent-add.argument.subject=navnet på det overordnede emne
luckperms.usage.sponge-parent-add.argument.contexts=de kontekster, der skal tilføjes forælder i
luckperms.usage.sponge-parent-remove.description=Fjerner en forælder fra emnet
luckperms.usage.sponge-parent-remove.argument.collection=samlingen hvor forældre emnerne er
luckperms.usage.sponge-parent-remove.argument.subject=navnet på det overordnede emne
luckperms.usage.sponge-parent-remove.argument.contexts=de kontekster, der skal fjernes forælder fra
luckperms.usage.sponge-parent-clear.description=Rydder emneforældre
luckperms.usage.sponge-parent-clear.argument.contexts=de kontekster der skal ryddes forældre i
luckperms.usage.sponge-option-info.description=Viser info om emnets indstillinger
luckperms.usage.sponge-option-info.argument.contexts=de kontekster der skal filtreres efter
luckperms.usage.sponge-option-set.description=Indstiller en mulighed for emnet
luckperms.usage.sponge-option-set.argument.key=nøglen der skal angives
luckperms.usage.sponge-option-set.argument.value=værdien som nøglen skal sættes til
luckperms.usage.sponge-option-set.argument.contexts=konteksterne til at indstille indstillingen i
luckperms.usage.sponge-option-unset.description=Fjerner end instilling for emnet
luckperms.usage.sponge-option-unset.argument.key=nøglen der skal fravælges
luckperms.usage.sponge-option-unset.argument.contexts=den kontekst nøglen skal fjernes fra
luckperms.usage.sponge-option-clear.description=Rydder emneindstillingerne
luckperms.usage.sponge-option-clear.argument.contexts=konteksten som valgmuligheden skal fjernes fra
