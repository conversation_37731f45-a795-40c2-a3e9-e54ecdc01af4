<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.google.guava</groupId>
    <artifactId>guava-parent</artifactId>
    <version>26.0-android</version>
  </parent>
  <artifactId>failureaccess</artifactId>
  <version>1.0.2</version>
  <packaging>bundle</packaging>
  <name>Guava InternalFutureFailureAccess and InternalFutures</name>
  <description>
    Contains
    com.google.common.util.concurrent.internal.InternalFutureFailureAccess and
    InternalFutures. Most users will never need to use this artifact. Its
    classes are conceptually a part of Guava, but they're in this separate
    artifact so that Android libraries can use them without pulling in all of
    Guava (just as they can use ListenableFuture by depending on the
    listenablefuture artifact).
  </description>
  <build>
    <plugins>
      <plugin>
        <artifactId>maven-jar-plugin</artifactId>
        <configuration>
          <archive>
            <manifestEntries>
              <Automatic-Module-Name>com.google.common.util.concurrent.internal</Automatic-Module-Name>
            </manifestEntries>
          </archive>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-source-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>animal-sniffer-maven-plugin</artifactId>
      </plugin>
      <plugin>
        <extensions>true</extensions>
        <groupId>org.apache.felix</groupId>
        <artifactId>maven-bundle-plugin</artifactId>
        <version>5.1.8</version>
        <executions>
          <execution>
            <id>bundle-manifest</id>
            <phase>process-classes</phase>
            <goals>
              <goal>manifest</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <instructions>
            <Export-Package>com.google.common.util.concurrent.internal</Export-Package>
            <Bundle-DocURL>https://github.com/google/guava/</Bundle-DocURL>
          </instructions>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-javadoc-plugin</artifactId>
        <executions>
          <execution>
            <id>attach-docs</id>
          </execution>
          <execution>
            <id>generate-javadoc-site-report</id>
            <phase>site</phase>
            <goals><goal>javadoc</goal></goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
  <profiles>
    <profile>
        <id>sonatype-oss-release</id>
        <build>
          <plugins>
            <plugin>
              <artifactId>maven-gpg-plugin</artifactId>
              <version>3.0.1</version>
              <executions>
                <execution>
                  <id>sign-artifacts</id>
                  <phase>verify</phase>
                  <goals>
                    <goal>sign</goal>
                  </goals>
                </execution>
              </executions>
            </plugin>
          </plugins>
      </build>
    </profile>
  </profiles>
</project>
