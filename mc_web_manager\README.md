# MC Web Manager

基于 FastAPI + 模块化前端的 Minecraft 服务器 Web 管理系统

## 功能特性

- 🚀 服务器启动/停止/重启控制
- 👥 玩家管理（在线状态、踢出、封禁）
- 🔌 插件管理（上传、删除、启用/禁用）
- 📋 实时日志查看和搜索
- ⚙️ 配置文件在线编辑
- 🔐 管理员认证系统
- 📊 服务器状态监控
- 🌐 响应式Web界面

## 技术栈

**后端:**
- FastAPI (Python Web框架)
- SQLAlchemy (ORM)
- JWT (认证)
- WebSocket (实时通信)
- RCON (MC服务器通信)

**前端:**
- 模块化 JavaScript
- Bootstrap 5 (UI框架)
- WebSocket (实时更新)

## 快速开始

1. 克隆项目并安装依赖
```bash
cd mc_web_manager
pip install -r requirements.txt
```

2. 配置环境变量
```bash
cp .env.example .env
# 编辑 .env 文件，设置您的配置
```

3. 启动应用
```bash
cd backend
uvicorn app.main:app --reload
```

4. 访问 http://localhost:8000

## 项目结构

```
mc_web_manager/
├── backend/          # 后端代码
├── frontend/         # 前端代码
├── docs/            # 文档
└── requirements.txt # 依赖列表
```