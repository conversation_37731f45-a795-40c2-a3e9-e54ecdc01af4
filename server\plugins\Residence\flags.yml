# These are Global Settings for Residence.
Global:
  # These are world flags that are applied when the player is NOT within a residence.
  Flags:
    # these are default for all worlds unless specified below, they can be overridden per group
    # Using command: false flag will allow you to disable and allow predefined commands. Command list can be difined under CommandLimits section
    Global:
      use: true
      build: true
      ignite: true
      firespread: true
      damage: true
      creeper: true
      tnt: true
      pvp: true
  # This gives permission to change certain flags to all groups, unless specifically denied to the group.
  FlagPermission:
    # Applies to: Player
    admin: true
    # Applies to: Both
    anchor: true
    # Applies to: Both
    animalkilling: false
    # Applies to: Residence
    animals: true
    # Applies to: Both
    anvil: true
    # Applies to: Residence
    anvilbreak: true
    # Applies to: Residence
    backup: false
    # Applies to: Both
    bank: true
    # Applies to: Both
    beacon: true
    # Applies to: Both
    bed: true
    # Applies to: Both
    brew: true
    # Applies to: Both
    build: false
    # Applies to: Residence
    burn: true
    # Applies to: Both
    button: true
    # Applies to: Both
    cake: true
    # Applies to: Residence
    canimals: true
    # Applies to: Both
    chat: true
    # Applies to: Both
    chorustp: true
    # Applies to: Residence
    cmonsters: true
    # Applies to: Both
    command: false
    # Applies to: Both
    commandblock: false
    # Applies to: Both
    container: false
    # Applies to: Residence
    coords: true
    # Applies to: Residence
    craft: true
    # Applies to: Residence
    creeper: false
    # Applies to: Residence
    damage: false
    # Applies to: Residence
    day: true
    # Applies to: Residence
    decay: true
    # Applies to: Both
    destroy: false
    # Applies to: Both
    diode: true
    # Applies to: Both
    door: true
    # Applies to: Residence
    dragongrief: true
    # Applies to: Residence
    dryup: true
    # Applies to: Both
    dye: true
    # Applies to: Both
    egg: true
    # Applies to: Both
    enchant: true
    # Applies to: Both
    enderpearl: true
    # Applies to: Residence
    explode: false
    # Applies to: Residence
    falldamage: true
    # Applies to: Residence
    fallinprotection: true
    # Applies to: Residence
    feed: true
    # Applies to: Residence
    fireball: true
    # Applies to: Residence
    firespread: false
    # Applies to: Residence
    flow: true
    # Applies to: Both
    flowerpot: true
    # Applies to: Residence
    flowinprotection: true
    # Applies to: Both
    fly: false
    # Applies to: Player
    friendlyfire: false
    # Applies to: Residence
    glow: true
    # Applies to: Residence
    grow: true
    # Applies to: Residence
    healing: true
    # Applies to: Residence
    hidden: false
    # Applies to: Both
    honey: true
    # Applies to: Both
    honeycomb: true
    # Applies to: Both
    hook: false
    # Applies to: Residence
    hotfloor: true
    # Applies to: Residence
    iceform: true
    # Applies to: Residence
    icemelt: true
    # Applies to: Both
    ignite: false
    # Applies to: Both
    itemdrop: true
    # Applies to: Both
    itempickup: true
    # Applies to: Residence
    jump2: false
    # Applies to: Residence
    jump3: false
    # Applies to: Residence
    keepexp: false
    # Applies to: Residence
    keepinv: false
    # Applies to: Residence
    lavaflow: true
    # Applies to: Both
    leash: false
    # Applies to: Both
    lever: true
    # Applies to: Residence
    mobexpdrop: true
    # Applies to: Residence
    mobitemdrop: true
    # Applies to: Both
    mobkilling: true
    # Applies to: Residence
    monsters: true
    # Applies to: Both
    move: true
    # Applies to: Both
    nametag: true
    # Applies to: Residence
    nanimals: true
    # Applies to: Residence
    night: true
    # Applies to: Residence
    nmonsters: true
    # Applies to: Residence
    nodurability: false
    # Applies to: Both
    nofly: false
    # Applies to: Residence
    nomobs: true
    # Applies to: Both
    note: true
    # Applies to: Residence
    overridepvp: false
    # Applies to: Residence
    phantomspawn: true
    # Applies to: Residence
    piston: true
    # Applies to: Residence
    pistonprotection: true
    # Applies to: Both
    place: true
    # Applies to: Both
    pressure: true
    # Applies to: Residence
    pvp: false
    # Applies to: Residence
    rain: true
    # Applies to: Residence
    respawn: false
    # Applies to: Both
    riding: true
    # Applies to: Residence
    sanimals: true
    # Applies to: Both
    shear: false
    # Applies to: Residence
    shoot: true
    # Applies to: Residence
    shop: true
    # Applies to: Residence
    smonsters: true
    # Applies to: Residence
    snowball: true
    # Applies to: Residence
    snowtrail: true
    # Applies to: Residence
    spread: true
    # Applies to: Both
    subzone: true
    # Applies to: Residence
    sun: true
    # Applies to: Both
    table: true
    # Applies to: Residence
    title: true
    # Applies to: Residence
    tnt: false
    # Applies to: Both
    tp: true
    # Applies to: Both
    trade: true
    # Applies to: Residence
    trample: true
    # Applies to: Both
    use: false
    # Applies to: Both
    vehicledestroy: false
    # Applies to: Residence
    waterflow: true
    # Applies to: Residence
    witherdamage: true
    # Applies to: Residence
    witherdestruction: true
    # Applies to: Residence
    witherspawn: true
    # Applies to: Residence
    wspeed1: true
    # Applies to: Residence
    wspeed2: true
    # Applies to: Both
    brush: true
    # Applies to: Both
    copper: true
    # Applies to: Both
    elytra: false
    # Applies to: Both
    goathorn: true
    # Applies to: Both
    harvest: false
    # Applies to: Residence
    safezone: false
  # This sets GUI items to represent each flag, if not given, then gray wool will be used
  FlagGui:
    admin: BEDROCK
    anchor: RESPAWN_ANCHOR
    animalkilling: CHICKEN
    animals: PIG_SPAWN_EGG
    anvil: ANVIL
    anvilbreak: ANVIL
    backup: BOOKSHELF
    bank: ENDER_CHEST
    beacon: BEACON
    bed: WHITE_BED
    brew: BREWING_STAND
    build: BRICKS
    burn: TORCH
    button: OAK_BUTTON
    cake: CAKE
    canimals: SHEEP_SPAWN_EGG
    chat: WRITABLE_BOOK
    chorustp: CHORUS_FRUIT
    cmonsters: CREEPER_SPAWN_EGG
    command: COMMAND_BLOCK
    commandblock: COMMAND_BLOCK
    container: CHEST_MINECART
    coords: COMPASS
    craft: STONE
    creeper: CREEPER_SPAWN_EGG
    damage: GOLDEN_SWORD
    day: DANDELION
    decay: OAK_LEAVES
    destroy: END_STONE
    diode: REPEATER
    door: OAK_DOOR
    dragongrief: DIRT
    dryup: BLUE_STAINED_GLASS_PANE
    dye: ORANGE_DYE
    egg: EGG
    enchant: ENCHANTING_TABLE
    enderpearl: ENDER_PEARL
    explode: TNT_MINECART
    falldamage: LEATHER_BOOTS
    fallinprotection: SAND
    feed: COOKED_BEEF
    fireball: FIRE_CHARGE
    firespread: BLAZE_POWDER
    flow: LILY_PAD
    flowerpot: FLOWER_POT
    flowinprotection: OAK_BOAT
    fly: ORANGE_CARPET
    friendlyfire: SUNFLOWER
    glow: SEA_LANTERN
    grow: WHEAT_SEEDS
    healing: POTION
    hidden: GLASS_PANE
    honey: BEEHIVE
    honeycomb: BEE_NEST
    hook: FISHING_ROD
    hotfloor: MAGMA_BLOCK
    iceform: ICE
    icemelt: ICE
    ignite: FLINT_AND_STEEL
    itemdrop: FEATHER
    itempickup: GUNPOWDER
    jump2: SLIME_BLOCK
    jump3: SLIME_BLOCK
    keepexp: GOLDEN_APPLE
    keepinv: LEATHER_HELMET
    lavaflow: LAVA_BUCKET
    leash: LEAD
    lever: LEVER
    mobexpdrop: MELON_SEEDS
    mobitemdrop: COCOA_BEANS
    mobkilling: ROTTEN_FLESH
    monsters: SPAWNER
    move: LEATHER_BOOTS
    nametag: NAME_TAG
    nanimals: COW_SPAWN_EGG
    night: BLACK_WOOL
    nmonsters: SKELETON_SPAWN_EGG
    nodurability: ANVIL
    nofly: ORANGE_CARPET
    nomobs: BARRIER
    note: NOTE_BLOCK
    overridepvp: IRON_SWORD
    phantomspawn: BROWN_WOOL
    piston: PISTON
    pistonprotection: STICKY_PISTON
    place: SEA_LANTERN
    pressure: LIGHT_WEIGHTED_PRESSURE_PLATE
    pvp: WOODEN_SWORD
    rain: BLUE_ORCHID
    respawn: SUNFLOWER
    riding: SADDLE
    sanimals: RABBIT_SPAWN_EGG
    shear: SHEARS
    shoot: ARROW
    shop: ITEM_FRAME
    smonsters: ZOMBIE_SPAWN_EGG
    snowball: SNOWBALL
    snowtrail: SNOW
    spread: SNOWBALL
    subzone: GRAY_STAINED_GLASS_PANE
    sun: SUNFLOWER
    table: CRAFTING_TABLE
    title: PAPER
    tnt: TNT
    tp: END_PORTAL_FRAME
    trade: EMERALD
    trample: DIRT
    use: STONE_PRESSURE_PLATE
    vehicledestroy: MINECART
    waterflow: WATER_BUCKET
    witherdamage: WITHER_SKELETON_SKULL
    witherdestruction: WITHER_SKELETON_SKULL
    witherspawn: WITHER_SKELETON_SKULL
    wspeed1: POTION
    wspeed2: POTION
    brush: BRUSH
    copper: IRON_BLOCK
    elytra: ELYTRA
    goathorn: GOAT_HORN
    harvest: SWEET_BERRIES
    safezone: APPLE
  # These are default flags applied to all residences from any user group.
  ResidenceDefault:
    build: false
    destroy: false
    use: false
    container: false
    pvp: false
    tnt: false
    creeper: false
    ignite: false
    firespread: false
    vehicledestroy: false
    animalkilling: false
    hook: false
    shear: false
    leash: false
    pistonprotection: true
    tp: false
    explode: false
    harvest: false
  # These are default flags applied to the residence creator of any group.
  CreatorDefault:
    build: true
    destroy: true
    move: true
    use: true
    ignite: true
    container: true
    animalkilling: true
    mobkilling: true
    vehicledestroy: true
    trade: true
    shear: true
    leash: true
    harvest: true
  # These are default flags applied to the residence renter of any group.
  RentedDefault:
    container: true
    ignite: true
    move: true
    trade: true
    mobkilling: true
    shear: true
    build: true
    use: true
    destroy: true
    vehicledestroy: true
    leash: true
    animalkilling: true
    admin: true
    harvest: true
  # These are grouped flags, so when using /res pset nickname redstone true, player will get all flags in list, same when setting to false or removing them.
  GroupedFlags:
    redstone:
    - note
    - pressure
    - lever
    - button
    - diode
    craft:
    - brew
    - table
    - enchant
    # This group of flags will be used for padd sub command
    trusted:
    - use
    - tp
    - build
    - destroy
    - container
    - move
    - leash
    - animalkilling
    - mobkilling
    - shear
    - chat
    - beacon
    - harvest
    fire:
    - ignite
    - firespread
  # Completely disables defined flag which will no longer be accesable even with resadmin command
  # Can save some of the server processing resources if you don't want to utilize specific checks for specific flags
  TotalFlagDisabling:
  - Completely
  - Disable
  - Particular
  - Flags
  # Provide list of commands you want to allow or block
  # This is when using 'command: false' flag for global/world flags
  # For example 'res create' under allow section and '*' would block everything except 'res create' command
  # Can be defined per world just like world flags can be
  # This will NOT apply inside residences. Inside residence command limits are based on residence command flag and its set commands limits
  # Residence itself will need to have 'command: false' to override global command limits with specific to that residence ones
  CommandLimits:
    Global:
      # When enabled allowed and blocked commands inside residence will be inherited from global list and combined with residence command limits
      Inherit: false
      WhiteList:
      - some allowed command
      BlackList:
      - some blocked command
# this is where you can create blacklists / whitelists
ItemList:
  # list name is not important, as long as it is unique. Its good to use a descripive name.
  DefaultList:
    # type of the list, can be blacklist, whitelist, or ignorelist
    Type: blacklist
    # If you want, you can have this list only apply to one world, otherwise it applies to all worlds
    # World: world
    # You can also have the list apply only to one group, otherwise it applies for all groups
    # Group: default
    # this is the actual list of material names that this list allows or disallows
    # You can look up the material name by item ID in game by typing /res material <id>
    # Alternativly, you can simply use the item ID in the list, but its less descriptive and harder to see what the list allows or dissallows at a glance
    Items:
    - LAVA
    - WATER
    - STATIONARY_LAVA
    - STATIONARY_WATER
