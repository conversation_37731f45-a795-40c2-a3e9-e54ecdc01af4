{% extends "base.html" %}

{% block title %}仪表板 - MC Web Manager{% endblock %}

{% block extra_css %}
<style>
body {
    background-color: #f8f9fa;
}

.navbar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.navbar-brand {
    font-weight: bold;
    color: white !important;
}

.nav-link {
    color: rgba(255,255,255,0.9) !important;
}

.nav-link:hover {
    color: white !important;
}

.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    transition: transform 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
}

.card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px 15px 0 0 !important;
    border: none;
}

.btn-success {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
    border: none;
}

.btn-danger {
    background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
    border: none;
}

.btn-warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    border: none;
}

.status-badge {
    font-size: 0.9rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
}

.status-running {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
    color: white;
}

.status-stopped {
    background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
    color: white;
}

.loading {
    opacity: 0.6;
    pointer-events: none;
}

/* 日志控制台样式 */
.log-console {
    height: 400px;
    overflow-y: auto;
    background-color: #1e1e1e;
    color: #d4d4d4;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.4;
}

.log-content {
    padding: 10px;
}

.log-line {
    margin: 0;
    padding: 2px 0;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.log-line:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.log-timestamp {
    color: #569cd6;
    margin-right: 8px;
}

.log-level-INFO {
    color: #4ec9b0;
}

.log-level-WARN {
    color: #dcdcaa;
}

.log-level-ERROR {
    color: #f44747;
}

.log-level-DEBUG {
    color: #9cdcfe;
}

.log-thread {
    color: #ce9178;
    margin-right: 8px;
}

.log-message {
    color: #d4d4d4;
}

/* 滚动条样式 */
.log-console::-webkit-scrollbar {
    width: 8px;
}

.log-console::-webkit-scrollbar-track {
    background: #2d2d30;
}

.log-console::-webkit-scrollbar-thumb {
    background: #464647;
    border-radius: 4px;
}

.log-console::-webkit-scrollbar-thumb:hover {
    background: #5a5a5c;
}
</style>
{% endblock %}

{% block content %}
<!-- 导航栏 -->
<nav class="navbar navbar-expand-lg">
    <div class="container">
        <a class="navbar-brand" href="#">
            <i class="fas fa-cube"></i> MC Web Manager
        </a>
        <div class="navbar-nav ms-auto">
            <span class="nav-link">欢迎, <span id="username">管理员</span></span>
            <a class="nav-link" href="#" id="logoutBtn">
                <i class="fas fa-sign-out-alt"></i> 登出
            </a>
        </div>
    </div>
</nav>

<div class="container mt-4">
    <!-- 服务器状态卡片 -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-server"></i> 服务器状态</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>状态:</strong> <span id="serverStatus" class="status-badge">检查中...</span></p>
                            <p><strong>进程ID:</strong> <span id="serverPid">-</span></p>
                            <p><strong>运行时间:</strong> <span id="serverUptime">-</span></p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>CPU使用率:</strong> <span id="serverCpu">-</span></p>
                            <p><strong>内存使用:</strong> <span id="serverMemory">-</span></p>
                            <p><strong>启动时间:</strong> <span id="serverStartTime">-</span></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-cogs"></i> 服务器控制</h5>
                </div>
                <div class="card-body text-center">
                    <button id="startBtn" class="btn btn-success mb-2 w-100">
                        <i class="fas fa-play"></i> 启动服务器
                    </button>
                    <button id="stopBtn" class="btn btn-danger mb-2 w-100">
                        <i class="fas fa-stop"></i> 停止服务器
                    </button>
                    <button id="restartBtn" class="btn btn-warning w-100">
                        <i class="fas fa-redo"></i> 重启服务器
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 系统信息卡片 -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-chart-line"></i> 系统信息</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <h6>系统</h6>
                            <p id="systemPlatform">-</p>
                            <p id="systemHostname">-</p>
                        </div>
                        <div class="col-md-3">
                            <h6>CPU</h6>
                            <p>核心数: <span id="cpuCount">-</span></p>
                            <p>使用率: <span id="cpuPercent">-</span></p>
                        </div>
                        <div class="col-md-3">
                            <h6>内存</h6>
                            <p>总计: <span id="memoryTotal">-</span></p>
                            <p>使用率: <span id="memoryPercent">-</span></p>
                        </div>
                        <div class="col-md-3">
                            <h6>磁盘</h6>
                            <p>总计: <span id="diskTotal">-</span></p>
                            <p>使用率: <span id="diskPercent">-</span></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 服务器日志控制台 -->
    <div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-terminal me-2"></i>服务器控制台
                </h5>
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-secondary btn-sm" id="clearLogsBtn">
                        <i class="fas fa-trash me-1"></i>清空
                    </button>
                    <button type="button" class="btn btn-outline-secondary btn-sm" id="scrollToBottomBtn">
                        <i class="fas fa-arrow-down me-1"></i>滚动到底部
                    </button>
                    <button type="button" class="btn btn-outline-secondary btn-sm" id="toggleAutoScrollBtn">
                        <i class="fas fa-sync me-1"></i>自动滚动
                    </button>
                </div>
            </div>
            <div class="card-body p-0">
                <!-- 日志搜索栏 -->
                <div class="p-3 border-bottom bg-light">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-search"></i>
                                </span>
                                <input type="text" class="form-control" id="logSearchInput" placeholder="搜索日志内容...">
                                <button class="btn btn-outline-secondary" type="button" id="searchLogsBtn">搜索</button>
                                <button class="btn btn-outline-secondary" type="button" id="clearSearchBtn">清除</button>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <select class="form-select" id="logLevelFilter">
                                <option value="">所有级别</option>
                                <option value="INFO">INFO</option>
                                <option value="WARN">WARN</option>
                                <option value="ERROR">ERROR</option>
                                <option value="DEBUG">DEBUG</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- 日志显示区域 -->
                <div id="logConsole" class="log-console">
                    <div id="logContent" class="log-content">
                        <div class="log-line text-muted text-center p-3">
                            <i class="fas fa-spinner fa-spin me-2"></i>正在连接服务器日志流...
                        </div>
                    </div>
                </div>

                <!-- 连接状态指示器 -->
                <div class="p-2 border-top bg-light d-flex justify-content-between align-items-center">
                    <div>
                        <span id="connectionStatus" class="badge bg-secondary">
                            <i class="fas fa-circle me-1"></i>连接中...
                        </span>
                        <span id="logCount" class="text-muted ms-2">日志行数: 0</span>
                    </div>
                    <div>
                        <small class="text-muted">实时日志流 | 最多显示1000行</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    </div>
</div>

<!-- 消息提示容器 -->
<div id="alertContainer" style="position: fixed; top: 20px; right: 20px; z-index: 1050;"></div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('仪表板页面加载');

    // 等待authManager初始化
    setTimeout(() => {
        if (!authManager || !authManager.isLoggedIn()) {
            console.log('未登录，跳转到登录页面');
            window.location.href = '/';
            return;
        }

        console.log('已登录，初始化仪表板');

        // 显示用户名
        const user = authManager.getCurrentUser();
        if (user) {
            document.getElementById('username').textContent = user.username;
        }

        // 登出按钮
        document.getElementById('logoutBtn').addEventListener('click', function(e) {
            e.preventDefault();
            authManager.logout();
            window.location.href = '/';
        });

        // 服务器控制按钮 - 完整功能
        document.getElementById('startBtn').addEventListener('click', () => controlServer('start'));
        document.getElementById('stopBtn').addEventListener('click', () => controlServer('stop'));
        document.getElementById('restartBtn').addEventListener('click', () => controlServer('restart'));

        // 初始化数据加载
        loadServerStatus();
        loadSystemInfo();

        // 定时刷新服务器状态（每5秒）
        setInterval(loadServerStatus, 5000);

        // 服务器控制函数
        async function controlServer(action) {
            const buttons = ['startBtn', 'stopBtn', 'restartBtn'];

            // 禁用所有按钮并显示加载状态
            buttons.forEach(id => {
                const btn = document.getElementById(id);
                btn.disabled = true;
                btn.classList.add('loading');
            });

            try {
                let response;
                switch(action) {
                    case 'start':
                        showToast('正在启动服务器...', 'info');
                        response = await apiClient.startServer();
                        break;
                    case 'stop':
                        showToast('正在停止服务器...', 'info');
                        response = await apiClient.stopServer();
                        break;
                    case 'restart':
                        showToast('正在重启服务器...', 'info');
                        response = await apiClient.restartServer();
                        break;
                }

                if (response.success) {
                    showToast(response.message, 'success');
                    // 2秒后刷新服务器状态
                    setTimeout(loadServerStatus, 2000);
                } else {
                    showToast(response.message, 'danger');
                }
            } catch (error) {
                console.error('服务器控制操作失败:', error);
                showToast('操作失败: ' + error.message, 'danger');

                // 如果是认证错误，跳转到登录页面
                if (error.status === 401) {
                    authManager.logout();
                    window.location.href = '/';
                }
            } finally {
                // 恢复按钮状态
                buttons.forEach(id => {
                    const btn = document.getElementById(id);
                    btn.disabled = false;
                    btn.classList.remove('loading');
                });
            }
        }

        // 加载服务器状态
        async function loadServerStatus() {
            try {
                const response = await apiClient.getServerStatus();
                if (response.success) {
                    updateServerStatus(response.data);
                }
            } catch (error) {
                console.error('获取服务器状态失败:', error);
                if (error.status === 401) {
                    authManager.logout();
                    window.location.href = '/';
                }
            }
        }

        // 加载系统信息
        async function loadSystemInfo() {
            try {
                const response = await apiClient.getSystemInfo();
                if (response.success) {
                    updateSystemInfo(response.data);
                }
            } catch (error) {
                console.error('获取系统信息失败:', error);
                if (error.status === 401) {
                    authManager.logout();
                    window.location.href = '/';
                }
            }
        }

        // 更新服务器状态显示
        function updateServerStatus(data) {
            const statusElement = document.getElementById('serverStatus');
            const isRunning = data.status === 'running';

            statusElement.textContent = isRunning ? '运行中' : '已停止';
            statusElement.className = `status-badge ${isRunning ? 'status-running' : 'status-stopped'}`;

            document.getElementById('serverPid').textContent = data.pid || '-';
            document.getElementById('serverCpu').textContent = data.cpu_percent ? data.cpu_percent.toFixed(1) + '%' : '-';
            document.getElementById('serverMemory').textContent = data.memory_info.rss ? formatBytes(data.memory_info.rss) : '-';
            document.getElementById('serverUptime').textContent = data.uptime_seconds ? formatUptime(data.uptime_seconds) : '-';
            document.getElementById('serverStartTime').textContent = data.create_time ? new Date(data.create_time).toLocaleString() : '-';
        }

        // 更新系统信息显示
        function updateSystemInfo(data) {
            document.getElementById('systemPlatform').textContent = `${data.system.platform} ${data.system.platform_release}`;
            document.getElementById('systemHostname').textContent = data.system.hostname;
            document.getElementById('cpuCount').textContent = data.cpu.cpu_count;
            document.getElementById('cpuPercent').textContent = data.cpu.cpu_percent.toFixed(1) + '%';
            document.getElementById('memoryTotal').textContent = formatBytes(data.memory.total);
            document.getElementById('memoryPercent').textContent = data.memory.percent.toFixed(1) + '%';
            document.getElementById('diskTotal').textContent = formatBytes(data.disk.total);
            document.getElementById('diskPercent').textContent = data.disk.percent.toFixed(1) + '%';
        }

        // 格式化字节数
        function formatBytes(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 格式化运行时间
        function formatUptime(seconds) {
            const days = Math.floor(seconds / 86400);
            const hours = Math.floor((seconds % 86400) / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);

            if (days > 0) return `${days}天 ${hours}小时 ${minutes}分钟`;
            if (hours > 0) return `${hours}小时 ${minutes}分钟`;
            return `${minutes}分钟`;
        }

        // Toast通知函数
        function showToast(message, type = 'info') {
            const toastContainer = document.getElementById('alertContainer');
            const toastId = 'toast-' + Date.now();

            const toastHTML = `
                <div id="${toastId}" class="toast align-items-center text-white bg-${type} border-0" role="alert" aria-live="assertive" aria-atomic="true">
                    <div class="d-flex">
                        <div class="toast-body">
                            ${message}
                        </div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                    </div>
                </div>
            `;

            toastContainer.insertAdjacentHTML('beforeend', toastHTML);

            // 初始化并显示Toast
            const toastElement = document.getElementById(toastId);
            const toast = new bootstrap.Toast(toastElement, {
                autohide: true,
                delay: 3000
            });
            toast.show();

            // Toast隐藏后移除元素
            toastElement.addEventListener('hidden.bs.toast', function() {
                toastElement.remove();
            });
        }

        // 初始化日志控制台
        initLogConsole();

        console.log('仪表板初始化完成');

    }, 100);

    // 日志控制台相关功能
    let logWebSocket = null;
    let autoScroll = true;
    let logLines = [];
    const maxLogLines = 1000;

    function initLogConsole() {
        console.log('初始化日志控制台');

        // 绑定按钮事件
        document.getElementById('clearLogsBtn').addEventListener('click', clearLogs);
        document.getElementById('scrollToBottomBtn').addEventListener('click', scrollToBottom);
        document.getElementById('toggleAutoScrollBtn').addEventListener('click', toggleAutoScroll);
        document.getElementById('searchLogsBtn').addEventListener('click', searchLogs);
        document.getElementById('clearSearchBtn').addEventListener('click', clearSearch);
        document.getElementById('logLevelFilter').addEventListener('change', filterLogs);

        // 搜索框回车事件
        document.getElementById('logSearchInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchLogs();
            }
        });

        // 连接WebSocket
        connectLogWebSocket();
    }

    function connectLogWebSocket() {
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}/api/server/logs/stream`;

        console.log('连接日志WebSocket:', wsUrl);
        updateConnectionStatus('connecting', '连接中...');

        try {
            logWebSocket = new WebSocket(wsUrl);

            logWebSocket.onopen = function(event) {
                console.log('日志WebSocket连接成功');
                updateConnectionStatus('connected', '已连接');
            };

            logWebSocket.onmessage = function(event) {
                try {
                    const data = JSON.parse(event.data);
                    handleLogMessage(data);
                } catch (error) {
                    console.error('解析日志消息失败:', error);
                }
            };

            logWebSocket.onclose = function(event) {
                console.log('日志WebSocket连接关闭');
                updateConnectionStatus('disconnected', '连接断开');

                // 5秒后尝试重连
                setTimeout(() => {
                    if (!logWebSocket || logWebSocket.readyState === WebSocket.CLOSED) {
                        connectLogWebSocket();
                    }
                }, 5000);
            };

            logWebSocket.onerror = function(error) {
                console.error('日志WebSocket错误:', error);
                updateConnectionStatus('error', '连接错误');
            };

            // 定期发送心跳
            setInterval(() => {
                if (logWebSocket && logWebSocket.readyState === WebSocket.OPEN) {
                    logWebSocket.send(JSON.stringify({type: 'ping'}));
                }
            }, 30000);

        } catch (error) {
            console.error('创建WebSocket连接失败:', error);
            updateConnectionStatus('error', '连接失败');
        }
    }

    function handleLogMessage(data) {
        if (data.type === 'initial_logs') {
            // 初始日志数据
            logLines = data.data || [];
            renderLogs();
        } else if (data.type === 'log_update') {
            // 新的日志行
            const newLines = data.data || [];
            logLines.push(...newLines);

            // 保持最大行数限制
            if (logLines.length > maxLogLines) {
                logLines = logLines.slice(-maxLogLines);
            }

            renderLogs();

            if (autoScroll) {
                scrollToBottom();
            }
        } else if (data.type === 'pong') {
            // 心跳响应
            console.log('收到心跳响应');
        }
    }

    function renderLogs() {
        const logContent = document.getElementById('logContent');
        const searchQuery = document.getElementById('logSearchInput').value.toLowerCase();
        const levelFilter = document.getElementById('logLevelFilter').value;

        let filteredLogs = logLines;

        // 应用搜索过滤
        if (searchQuery) {
            filteredLogs = filteredLogs.filter(log =>
                log.message.toLowerCase().includes(searchQuery) ||
                log.raw.toLowerCase().includes(searchQuery)
            );
        }

        // 应用级别过滤
        if (levelFilter) {
            filteredLogs = filteredLogs.filter(log => log.level === levelFilter);
        }

        if (filteredLogs.length === 0) {
            logContent.innerHTML = '<div class="log-line text-muted text-center p-3">暂无日志数据</div>';
        } else {
            const logHtml = filteredLogs.map(log => formatLogLine(log)).join('');
            logContent.innerHTML = logHtml;
        }

        // 更新日志计数
        document.getElementById('logCount').textContent = `日志行数: ${filteredLogs.length}`;
    }

    function formatLogLine(log) {
        const timestamp = log.timestamp ? log.timestamp.split(' ')[1] || log.timestamp : '';
        const level = log.level || 'INFO';
        const thread = log.thread || '';
        const message = log.message || log.raw || '';

        return `
            <div class="log-line">
                <span class="log-timestamp">[${timestamp}]</span>
                <span class="log-thread">[${thread}]</span>
                <span class="log-level-${level}">[${level}]</span>
                <span class="log-message">${escapeHtml(message)}</span>
            </div>
        `;
    }

    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    function updateConnectionStatus(status, text) {
        const statusElement = document.getElementById('connectionStatus');
        statusElement.innerHTML = `<i class="fas fa-circle me-1"></i>${text}`;

        // 移除所有状态类
        statusElement.classList.remove('bg-success', 'bg-danger', 'bg-warning', 'bg-secondary');

        // 添加对应状态类
        switch (status) {
            case 'connected':
                statusElement.classList.add('bg-success');
                break;
            case 'disconnected':
            case 'error':
                statusElement.classList.add('bg-danger');
                break;
            case 'connecting':
                statusElement.classList.add('bg-warning');
                break;
            default:
                statusElement.classList.add('bg-secondary');
        }
    }

    function clearLogs() {
        logLines = [];
        renderLogs();
        showToast('日志已清空', 'info');
    }

    function scrollToBottom() {
        const logConsole = document.getElementById('logConsole');
        logConsole.scrollTop = logConsole.scrollHeight;
    }

    function toggleAutoScroll() {
        autoScroll = !autoScroll;
        const btn = document.getElementById('toggleAutoScrollBtn');

        if (autoScroll) {
            btn.innerHTML = '<i class="fas fa-sync me-1"></i>自动滚动';
            btn.classList.remove('btn-outline-secondary');
            btn.classList.add('btn-success');
            scrollToBottom();
        } else {
            btn.innerHTML = '<i class="fas fa-pause me-1"></i>手动滚动';
            btn.classList.remove('btn-success');
            btn.classList.add('btn-outline-secondary');
        }
    }

    function searchLogs() {
        renderLogs();
    }

    function clearSearch() {
        document.getElementById('logSearchInput').value = '';
        document.getElementById('logLevelFilter').value = '';
        renderLogs();
    }

    function filterLogs() {
        renderLogs();
    }
});
</script>
{% endblock %}
