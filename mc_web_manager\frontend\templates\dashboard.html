{% extends "base.html" %}

{% block title %}仪表板 - MC Web Manager{% endblock %}

{% block extra_css %}
<style>
body {
    background-color: #f8f9fa;
}

.navbar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.navbar-brand {
    font-weight: bold;
    color: white !important;
}

.nav-link {
    color: rgba(255,255,255,0.9) !important;
}

.nav-link:hover {
    color: white !important;
}

.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    transition: transform 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
}

.card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px 15px 0 0 !important;
    border: none;
}

.btn-success {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
    border: none;
}

.btn-danger {
    background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
    border: none;
}

.btn-warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    border: none;
}

.status-badge {
    font-size: 0.9rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
}

.status-running {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
    color: white;
}

.status-stopped {
    background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
    color: white;
}

.loading {
    opacity: 0.6;
    pointer-events: none;
}
</style>
{% endblock %}

{% block content %}
<!-- 导航栏 -->
<nav class="navbar navbar-expand-lg">
    <div class="container">
        <a class="navbar-brand" href="#">
            <i class="fas fa-cube"></i> MC Web Manager
        </a>
        <div class="navbar-nav ms-auto">
            <span class="nav-link">欢迎, <span id="username">管理员</span></span>
            <a class="nav-link" href="#" id="logoutBtn">
                <i class="fas fa-sign-out-alt"></i> 登出
            </a>
        </div>
    </div>
</nav>

<div class="container mt-4">
    <!-- 服务器状态卡片 -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-server"></i> 服务器状态</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>状态:</strong> <span id="serverStatus" class="status-badge">检查中...</span></p>
                            <p><strong>进程ID:</strong> <span id="serverPid">-</span></p>
                            <p><strong>运行时间:</strong> <span id="serverUptime">-</span></p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>CPU使用率:</strong> <span id="serverCpu">-</span></p>
                            <p><strong>内存使用:</strong> <span id="serverMemory">-</span></p>
                            <p><strong>启动时间:</strong> <span id="serverStartTime">-</span></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-cogs"></i> 服务器控制</h5>
                </div>
                <div class="card-body text-center">
                    <button id="startBtn" class="btn btn-success mb-2 w-100">
                        <i class="fas fa-play"></i> 启动服务器
                    </button>
                    <button id="stopBtn" class="btn btn-danger mb-2 w-100">
                        <i class="fas fa-stop"></i> 停止服务器
                    </button>
                    <button id="restartBtn" class="btn btn-warning w-100">
                        <i class="fas fa-redo"></i> 重启服务器
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 系统信息卡片 -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-chart-line"></i> 系统信息</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <h6>系统</h6>
                            <p id="systemPlatform">-</p>
                            <p id="systemHostname">-</p>
                        </div>
                        <div class="col-md-3">
                            <h6>CPU</h6>
                            <p>核心数: <span id="cpuCount">-</span></p>
                            <p>使用率: <span id="cpuPercent">-</span></p>
                        </div>
                        <div class="col-md-3">
                            <h6>内存</h6>
                            <p>总计: <span id="memoryTotal">-</span></p>
                            <p>使用率: <span id="memoryPercent">-</span></p>
                        </div>
                        <div class="col-md-3">
                            <h6>磁盘</h6>
                            <p>总计: <span id="diskTotal">-</span></p>
                            <p>使用率: <span id="diskPercent">-</span></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 消息提示容器 -->
<div id="alertContainer" style="position: fixed; top: 20px; right: 20px; z-index: 1050;"></div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 等待authManager初始化完成后再检查登录状态
    setTimeout(() => {
        console.log('仪表板认证检查:', {
            authManager: !!authManager,
            isLoggedIn: authManager ? authManager.isLoggedIn() : false,
            token: !!localStorage.getItem('access_token')
        });

        if (!authManager || !authManager.isLoggedIn()) {
            console.log('认证失败，跳转到登录页面');
            window.location.href = '/';
            return;
        }

        console.log('认证成功，初始化仪表板');
        // 初始化仪表板
        initializeDashboard();
    }, 100);
});

function initializeDashboard() {
    console.log('开始初始化仪表板功能');

    // 显示用户名
    const user = authManager.getCurrentUser();
    console.log('当前用户:', user);
    if (user) {
        document.getElementById('username').textContent = user.username;
    }

    // 登出按钮
    document.getElementById('logoutBtn').addEventListener('click', function(e) {
        e.preventDefault();
        authManager.logout();
        window.location.href = '/';
    });

    // 服务器控制按钮
    document.getElementById('startBtn').addEventListener('click', () => controlServer('start'));
    document.getElementById('stopBtn').addEventListener('click', () => controlServer('stop'));
    document.getElementById('restartBtn').addEventListener('click', () => controlServer('restart'));

    // 初始化数据
    loadServerStatus();
    loadSystemInfo();

    // 定时刷新服务器状态
    setInterval(loadServerStatus, 5000);

    async function loadServerStatus() {
        try {
            const response = await apiClient.getServerStatus();
            if (response.success) {
                updateServerStatus(response.data);
            }
        } catch (error) {
            console.error('获取服务器状态失败:', error);
            if (error.status === 401) {
                authManager.logout();
                window.location.href = '/';
            }
        }
    }

    async function loadSystemInfo() {
        try {
            const response = await apiClient.getSystemInfo();
            if (response.success) {
                updateSystemInfo(response.data);
            }
        } catch (error) {
            console.error('获取系统信息失败:', error);
            if (error.status === 401) {
                authManager.logout();
                window.location.href = '/';
            }
        }
    }

    async function controlServer(action) {
        const buttons = ['startBtn', 'stopBtn', 'restartBtn'];
        buttons.forEach(id => {
            const btn = document.getElementById(id);
            btn.disabled = true;
            btn.classList.add('loading');
        });

        try {
            let response;
            switch(action) {
                case 'start':
                    response = await apiClient.startServer();
                    break;
                case 'stop':
                    response = await apiClient.stopServer();
                    break;
                case 'restart':
                    response = await apiClient.restartServer();
                    break;
            }

            if (response.success) {
                showAlert(response.message, 'success');
                setTimeout(loadServerStatus, 2000);
            } else {
                showAlert(response.message, 'danger');
            }
        } catch (error) {
            showAlert('操作失败: ' + error.message, 'danger');
            if (error.status === 401) {
                authManager.logout();
                window.location.href = '/';
            }
        } finally {
            buttons.forEach(id => {
                const btn = document.getElementById(id);
                btn.disabled = false;
                btn.classList.remove('loading');
            });
        }
    }

    function updateServerStatus(data) {
        const statusElement = document.getElementById('serverStatus');
        const isRunning = data.status === 'running';

        statusElement.textContent = isRunning ? '运行中' : '已停止';
        statusElement.className = `status-badge ${isRunning ? 'status-running' : 'status-stopped'}`;

        document.getElementById('serverPid').textContent = data.pid || '-';
        document.getElementById('serverCpu').textContent = data.cpu_percent ? data.cpu_percent.toFixed(1) + '%' : '-';
        document.getElementById('serverMemory').textContent = data.memory_info.rss ? formatBytes(data.memory_info.rss) : '-';
        document.getElementById('serverUptime').textContent = data.uptime_seconds ? formatUptime(data.uptime_seconds) : '-';
        document.getElementById('serverStartTime').textContent = data.create_time ? new Date(data.create_time).toLocaleString() : '-';
    }

    function updateSystemInfo(data) {
        document.getElementById('systemPlatform').textContent = `${data.system.platform} ${data.system.platform_release}`;
        document.getElementById('systemHostname').textContent = data.system.hostname;
        document.getElementById('cpuCount').textContent = data.cpu.cpu_count;
        document.getElementById('cpuPercent').textContent = data.cpu.cpu_percent.toFixed(1) + '%';
        document.getElementById('memoryTotal').textContent = formatBytes(data.memory.total);
        document.getElementById('memoryPercent').textContent = data.memory.percent.toFixed(1) + '%';
        document.getElementById('diskTotal').textContent = formatBytes(data.disk.total);
        document.getElementById('diskPercent').textContent = data.disk.percent.toFixed(1) + '%';
    }

    function formatBytes(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    function formatUptime(seconds) {
        const days = Math.floor(seconds / 86400);
        const hours = Math.floor((seconds % 86400) / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);

        if (days > 0) return `${days}天 ${hours}小时 ${minutes}分钟`;
        if (hours > 0) return `${hours}小时 ${minutes}分钟`;
        return `${minutes}分钟`;
    }

    function showAlert(message, type) {
        const alertContainer = document.getElementById('alertContainer');
        const alertId = 'alert-' + Date.now();

        const alertHTML = `
            <div id="${alertId}" class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;

        alertContainer.insertAdjacentHTML('beforeend', alertHTML);

        // 3秒后自动消失
        setTimeout(() => {
            const alert = document.getElementById(alertId);
            if (alert) {
                alert.remove();
            }
        }, 3000);
    }
}
});
</script>
{% endblock %}
