# If you need help, you can join our Discord - https://viaversion.com/discord
#
# Always shows a mapped mob's original name, and not only when hovering over it with the cursor.
always-show-original-mob-name: true
#
# Writes name and level of custom enchantments into the item's lore.
# Set this to false if you see the entries doubled/if the custom-enchant plugin already writes these into the lore manually.
add-custom-enchants-into-lore: true
#
# Adds the color of a scoreboard team after its prefix for 1.12 clients on 1.13+ servers.
add-teamcolor-to-prefix: true
#
# Converts the 1.13 face look-at packet for 1.12- players. Requires a bit of extra caching.
fix-1_13-face-player: false
#
# Fixes 1.13 clients and lower not seeing color or formatting in inventory titles by converting them to legacy text.
# If you have issues with translatable text being displayed wrongly, disable this.
fix-formatted-inventory-titles: true
#
# Sends inventory acknowledgement packets to act as a replacement for ping packets for sub 1.17 clients.
# This only takes effect for ids in the short range. Useful for anticheat compatibility.
handle-pings-as-inv-acknowledgements: false
#
# Adds bedrock blocks at y=0 for sub 1.17 clients. This may allow for weird interactions due to sending fake blocks.
bedrock-at-y-0: false
#
# Shows sculk shriekers as crying obsidian for 1.18.2 clients on 1.19+ servers. This fixes collision and block breaking issues.
# If disabled, the client will see them as end portal frames.
sculk-shriekers-to-crying-obsidian: true
#
# Shows scaffolding as water for 1.13.2 clients on 1.14+ servers. This fixes collision issues.
# If disabled, the client will see them as hay blocks.
scaffolding-to-water: false
#
# Maps the darkness effect to blindness for 1.18.2 clients on 1.19+ servers.
map-darkness-effect: true
#
# If enabled, 1.21.3 clients will receive the first float of 1.21.4+ custom model data as int. Disable if you handle this change yourself.
map-custom-model-data: true
#
# If enabled, 1.19.3 clients will receive display entities as armor stands with custom entity data on 1.19.4+ servers. Note that
# this does not support all features display entities offer.
map-display-entities: true
#
# Suppresses warnings of missing emulations for certain features that are not supported (e.g. world height in 1.17+).
suppress-emulation-warnings: false
