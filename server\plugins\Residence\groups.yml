# These groups correspond to the Permissions groups defined in your '<worldname>.yml'
Groups:
    Default: #group name, DONT remove this group
       # Information about the Residence zone that the player can define.
       #Uncomment the lines below to mirror this config to other groups.
       #Mirror:
         #- 'group1'
         #- 'group2'
       Residence:
           # Determins if this group can create residences or not.  This option can be overriden with the permissions node 'residence.create'
           CanCreate: true
           # The maximum number of Residences a player can have.
           MaxResidences: 3
           # The maximum number of physical areas a residence can have.
           MaxAreasPerResidence: 2
           # The maximum number of blocks a Residence can be, East to West (X).
           MaxEastWest: 16
           # (Optional) The minimum number of blocks a Residence can be, East to West (X).
           MinEastWest: 3
           # The maximum number of blocks a Residence can be, North to South (Z).
           MaxNorthSouth: 16
           # (Optional) The minimum number of blocks a Residence can be, North to South (Z).
           MinNorthSouth: 3
           # The maximum number of blocks a Residence can be, High to Low (Y).
           MaxUpDown: 320
           # (Optional) The minimum number of blocks a Residence can be, High to Low (Y).
           MinUpDown: 3
           # The lowest altitude this group is allowed to protect.
           MinHeight: -64
           # The highest altitude this group is allowed to protect.
           MaxHeight: 320
            # The maximum amount of subzones can current area have.
           MaxSubzonesInArea: 3
            # The maximum recursive depth that subzones can go.
            # A subzone within a subzone within the Residence zone would be a depth of 2.
            # Set to 0 to disable subzones.
           SubzoneDepth: 3
           # (Optional) The maximum number of blocks a Residence Subzone can be, East to West (X).
           SubzoneMaxEastWest: 16
           # (Optional) The minimum number of blocks a Residence Subzone can be, East to West (X).
           SubzoneMinEastWest: 3
           # (Optional) The maximum number of blocks a Residence Subzone can be, North to South (Z).
           SubzoneMaxNorthSouth: 16
           # (Optional) The minimum number of blocks a Residence Subzone can be, North to South (Z).
           SubzoneMinNorthSouth: 3
           # (Optional) The maximum number of blocks a Residence Subzone can be, High to Low (Y).
           SubzoneMaxUpDown: 320
           # (Optional) The minimum number of blocks a Residence Subzone can be, High to Low (Y).
           SubzoneMinUpDown: 3
           # Whether or not to allow teleporting to Residences.
           CanTeleport: true
           # Allow or Disallow the use of /res unstuck, to get yourself out of a residence if stuck in one somehow
           Unstuck: true
           # Allow or Disallow the use of /res kick, to kick player from residence.
           Kick: False
           # Allow or Disallow the use of the /res select command, if disabled they can only use the selection tool.
           SelectCommandAccess: true
           # Allow or Disallow the group to access the Blacklist/Ignorelist for residences they own.
           ItemListAccess: true
       # Options relating to enter and leave messages on the residence.
       Messaging:
           # Whether or not the player can change the Residence enter and leave messages.
           CanChange: true
           # The default enter message to apply to new Residences created by players in this group.
           # Use %zone if you want to include only current residence name without including parent zones into name
           # Leaving the message blank will disable it.
           DefaultEnter: "Welcome %player to %residence, owned by %owner."
           # The default leave message to apply to new Residences created by players in this group.
           # %player if you want to include players name
           # %playerDisplay if you want to include players display name
           # %owner if you want to include residence owner name
           # %residence if you want to include residence name
           # %zone if you want to include only current residence name without including parent zones into name
           # 
           # Leaving the message blank will disable it.
           DefaultLeave: "Now leaving %residence."
       # Options relating to the Lease system.
       Lease:
          # The maximum number of days to allow leases.
          MaxDays: 16
          # The number of days to add to the lease following a '/res lease renew' command.
          RenewIncrement: 14
       #Options related to the rent system
       Rent:
           #determines how many residences can be rented by players of this group at once
           MaxRents: 3
           #determines how many residences can be set for rent by players of this group at once
           MaxRentables: 3
           # determines max days player can rent residence at once
           MaxRentDays: 31
       # Options relating to the Residence Economy.
       Economy:
          # Whether or not players in this group can buy other Residences that are for sale.
          CanBuy: true
          # Whether or not players in this group can sell their Residences.
          CanSell: false
          # Whether or not to ignore the Residence size/count limits when buying a residence.
          IgnoreLimits: false
          # The cost, per block, of making a new Residence or adding a area to it.
          BuyCost: 0.5
          # The cost, per block, player will be getting back when removing residence. 
          # ATTENTION ResMoneyBack needs to be enabled in config file for this to work
          SellCost: 0.25
          # The cost, per block, of renewing a Residence lease (if the lease system is enabled).
          RenewCost: 0.2
       # Flags are checked in the order:
       # 1: Player
       # 2: Group
       # 3: Owner
       # 4: If all others are undefined, reverts to default value (usually true).
       # Flag permissions default to false.
       # These flags settings override the globals.
       Flags:
          # Specifically allow or deny this group from changing certain flags.
          Permission:
               #build: true
          # Specifys the flags that are applied at residence creation for players of this group.
          Default:
              #build: true
          # Specifys the flags that are applied at to the creator at residence creation.
          CreatorDefault:
              #build: true
          # Specifys the flags that are applied to other groups for this residence, at creation.
          GroupDefault:
               #default: #group name
                   #build: false
          #these flags are applied specifically to this group whenever they are outside a residence
          #these flags will override the flags at the top, under the Global section.
          World:
                Global: #these flags will apply to all worlds
                     #build: false
                WorldNameHere: #or you can apply them by each individual world
                     #build: false
    NextGroup: #group name, player should have residence.group.nextgroup permission node to have access to this residence group
       Residence:
           CanCreate: true
           MaxResidences: 4
           MaxAreasPerResidence: 2
           MaxEastWest: 20
           MaxNorthSouth: 20
           MaxUpDown: 20
           MinHeight: -64
           MaxHeight: 320
           SubzoneDepth: 3
           CanTeleport: true
           Unstuck: true
           Kick: true
           SelectCommandAccess: true
           ItemListAccess: true
       Messaging:
           CanChange: true
           DefaultEnter: "Welcome %player to %residence, owned by %owner."
           DefaultLeave: "Now leaving %residence."
       Lease:
          MaxDays: 16
          RenewIncrement: 14
       Rent:
           MaxRents: 3
           MaxRentables: 3
           MaxRentDays: 31
       Economy:
          CanBuy: true
          CanSell: false
          IgnoreLimits: false
          BuyCost: 0.7
          SellCost: 0.3
          RenewCost: 0.3

# You can manually specify the group a player is in below, this overrides their permissions group.
GroupAssignments:
     bekvon: default
     player: default