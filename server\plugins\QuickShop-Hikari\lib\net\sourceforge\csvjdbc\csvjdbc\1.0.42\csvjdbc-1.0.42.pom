<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>net.sourceforge.csvjdbc</groupId>
  <artifactId>csvjdbc</artifactId>
  <version>1.0.42</version>
  <packaging>jar</packaging>
  <name>CsvJdbc</name>
  <description>a Java JDBC driver for reading comma-separated-value files</description>
  <url>http://csvjdbc.sourceforge.net</url>

  <licenses>
    <license>
      <name>LGPL2.1</name>
      <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html</url>
    </license>
  </licenses>

  <scm>
    <connection>scm:git:ssh://git.code.sf.net/p/csvjdbc/code</connection>
    <tag>csvjdbc-1.0.42</tag>
    <url>http://sourceforge.net/p/csvjdbc/_list/git</url>
  </scm>

  <developers>
    <developer>
      <id>mfrasca</id>
      <name>Mario Frasca</name>
      <email><EMAIL></email>
      <url>http://sourceforge.net/users/mfrasca</url>
      <organization>Sourceforge</organization>
      <organizationUrl>http://www.sourceforge.net</organizationUrl>
      <roles>
        <role>developer</role>
      </roles>
    </developer>
    <developer>
      <id>simoc</id>
      <name>Simon Chenery</name>
      <email><EMAIL></email>
      <url>http://sourceforge.net/users/simoc</url>
      <organization>Sourceforge</organization>
      <organizationUrl>http://www.sourceforge.net</organizationUrl>
      <roles>
        <role>developer</role>
      </roles>
    </developer>
  </developers>

  <repositories>
    <repository>
      <id>SourceForge</id>
      <url>http://csvjdbc.sourceforge.net/maven2</url>
    </repository>
  </repositories>

  <properties>
    <sample.files.location>${basedir}/src/testdata</sample.files.location>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
  </properties>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <version>3.2.1</version>
        <configuration>
          <workingDirectory>build</workingDirectory>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-javadoc-plugin</artifactId>
        <version>3.1.1</version>
        <configuration>
          <additionalOptions>
            <additionalOption>-Xdoclint:none</additionalOption>
          </additionalOptions>
        </configuration>
        <executions>
          <execution>
            <id>attach-javadocs</id>
            <goals>
              <goal>jar</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-source-plugin</artifactId>
        <version>2.2.1</version>
        <executions>
          <execution>
            <id>attach-sources</id>
            <goals>
              <goal>jar-no-fork</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>2.3.2</version>
        <configuration>
          <source>1.8</source>
          <target>1.8</target>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jar-plugin</artifactId>
        <version>2.3.2</version>
        <configuration>
          <archive>
            <manifest>
              <mainClass>org.relique.jdbc.csv.CsvMain</mainClass>
            </manifest>
          </archive>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>javacc-maven-plugin</artifactId>
        <version>2.6</version>
        <executions>
          <execution>
            <id>javacc</id>
            <goals>
              <goal>javacc</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-release-plugin</artifactId>
        <version>2.5.3</version>
        <configuration>
          <autoVersionSubmodules>true</autoVersionSubmodules>
          <useReleaseProfile>false</useReleaseProfile>
          <!-- references the profile defined just below -->
          <releaseProfiles>release</releaseProfiles>
          <goals>deploy</goals>
          <pushChanges>true</pushChanges>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-gpg-plugin</artifactId>
        <version>1.5</version>
        <executions>
          <execution>
            <id>sign-artifacts</id>
            <phase>verify</phase>
            <goals>
              <goal>sign</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
    <extensions>
      <extension>
        <groupId>org.apache.maven.wagon</groupId>
         <artifactId>wagon-ssh</artifactId>
         <version>2.5</version>
      </extension>
    </extensions>
  </build>
  <dependencies>
    <dependency>
      <groupId>io.github.classgraph</groupId>
      <artifactId>classgraph</artifactId>
      <version>4.8.106</version>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter</artifactId>
      <version>5.10.0</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.junit.platform</groupId>
      <artifactId>junit-platform-suite</artifactId>
      <version>1.10.0</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.junit.platform</groupId>
      <artifactId>junit-platform-suite-engine</artifactId>
      <version>1.10.0</version>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <distributionManagement>
    <snapshotRepository>
      <id>ossrh</id>
      <url>https://oss.sonatype.org/content/repositories/snapshots</url>
    </snapshotRepository>
    <repository>
      <id>ossrh</id>
      <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
    </repository>
  </distributionManagement>
</project>
