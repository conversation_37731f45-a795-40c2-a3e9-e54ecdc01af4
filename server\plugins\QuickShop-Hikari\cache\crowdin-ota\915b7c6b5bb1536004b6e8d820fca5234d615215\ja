break-shop-use-supertool: <yellow>SuperToolを使ってショップを破壊することができます。
fee-charged-for-price-change: <green>価格変更のため <red>{0}<green> を支払いました
not-allowed-to-create: <red>あなたはここでショップを作る事ができません
disabled-in-this-world: <red>このワールドではクイックショップが無効になっています
how-much-to-trade-for: <green>チャットで<yellow>{1}個の {0}<green>をいくらで取引したいか入力してください。
client-language-changed: <green>QuickShop はクライアントの言語設定が変更されていることを検出しました。現在 {0} ロケールを使用しています。
shops-backingup: データベースからショップのバックアップを作成しています...
_comment: 翻訳者さんこんにちは！Githubから、もしくはソースコードから直接編集している場合は、代わりにhttps://crowdin.com/project/quickshop-hikariにアクセスしてください。
unlimited-shop-owner-changed: <yellow>この無制限の店主は {0} に変更されました。
bad-command-usage-detailed: '<red>コマンド引数が不正です! 次のパラメータを受け付けます: <gray>{0}'
thats-not-a-number: <red>無効な番号です
shop-name-disallowed: <red>The shop name <yellow>{0} <red>is disallowed. Pick another one!
console-only-danger: <red>これは危険なコマンドなので、コンソールのみが実行できます。
not-a-number: <red>数字しか入力出来ません。あなたの入力は {0} でした。
not-looking-at-valid-shop-block: <red>ショップを作成するためのブロックが見つかりませんでした。確認する必要があります。
shop-removed-cause-ongoing-fee: <red>あなたの {0} のショップは十分な資金がなかったため削除されました！
tabcomplete:
  amount: '[個数]'
  item: '[アイテム]'
  price: '[価格]'
  name: '[名称]'
  range: '[範囲]'
  currency: '[通貨名]'
  percentage: '[百分率%]'
taxaccount-unset: <green>このショップの税金口座はサーバーのグローバル設定に従っています。
blacklisted-item: <red>ブラックリストに載っているので、この商品を販売することはできません
command-type-mismatch: <red>このコマンドは<aqua>{0} のみ実行できます。
server-crash-warning: '<red>サーバーの実行中にQuickShopプラグインのJarファイルを交換/削除した場合、/qsreloadコマンドを実行するとサーバーがクラッシュすることがあります。'
you-cant-afford-to-change-price: <red>ショップの価格を変更するには {0} かかります。
safe-mode: <red>クイックショップはセーフモードです。このショップのコンテナを開くことができません。エラーを修正するにはサーバー管理者に問い合わせてください。
forbidden-vanilla-behavior: この操作はバニラの動作に反したため、禁止されています
shop-out-of-space-name: <dark_purple>あなたのショップ {0} がいっぱいです！
paste-disabled: |-
  <red>Paste functionality has been disabled! You cannot request technical support. <newline>Reason: {0}
quick-fill:
  entry-own: ' <yellow>- <red>[店主] <aqua>{0} <light_purple>{1}'
  hover:
    - '<yellow>名称: <aqua>{0}'
    - '<yellow>店主: <aqua>{0}'
    - '<yellow>タイプ: <aqua>{0}'
    - '<yellow>価格: <aqua>{0}'
    - '<yellow>アイテム: <aqua>{0}'
    - '<yellow>座標: <aqua>{0}'
  hover-arg-filled:
    - '<yellow>名称: <aqua>{name}'
    - '<yellow>店主: <aqua>{owner}'
    - '<yellow>タイプ: <aqua>{type}'
    - '<yellow>価格: <aqua>{price}'
    - '<yellow>アイテム: <aqua>{item}'
    - '<yellow>座標: <aqua>{location}'
  header: '<yellow>You have multiple shops with name "<green>{0}<yellow>", choose one to continue:'
  entry-normal: ' <yellow>- <aqua>{0} <light_purple>{1}'
  entry-cooperation: ' <yellow>- <red>[co] <aqua>{0} <light_purple>{1}'
3rd-plugin-build-check-failed-admin: '<gray>(AdminOnly) <light_purple>{0}<dark_gray> denied the permission checks. If this not expected, try adding <light_purple>{1} <gray>to the listener blacklist. Configuration Guide:https://ghost-chu.github.io/QuickShop-Hikari-Documents/docs/modules/shops/protection-checker'
average-price-nearby: '<green>近所の平均価格: <yellow>{0}'
inventory-check-global-alert: "<red>[Inventory Check] <gray>Warning! Found a QuickShop display item <gold>{2}</gold> in inventory at <aqua>{0}</aqua> <dark_gray>[{1}]</dark_gray>, which it shouldn't happened, This usually means that someone is maliciously exploiting the exploit to duplicating the display item."
digits-reach-the-limit: 価格が小数点以下の制限に達しました。
currency-unset: <green>ショップの通貨は正常に削除されました。現在、デフォルトの設定を使用しています。
you-cant-create-shop-in-there: <red>この場所にショップを作成する権限がありません。
no-pending-action: <red>保留中のアクションはありません
refill-success: <green>補充成功
failed-to-paste: <red>Pastebinへのデータのアップロードに失敗しました。インターネット接続を確認して、もう一度お試しください。(詳細についてはコンソールをご覧ください)
shop-out-of-stock-name: <dark_purple>あなたのショップ {0} の {1} の在庫がなくなりました！!
shop-name-invalid: <red>The shop name <yellow>{0} <red>is invalid. Pick another one!
how-many-buy-stack: <yellow>チャットで<aqua>購入<green>したい商品の数を入力して下さい。1つの商品に <yellow>{0} 個<green> のアイテムが含まれており、あなたは<yellow>{1} つ<green> の商品を購入することができます。すべて購入するにはチャットで <aqua>{2}<green> と入力して下さい。
exceeded-maximum: この値はJavaの最大値を越えました
unlimited-shop-owner-keeped: '<yellow>注意: 店主はまだ無制限の店主です。自分で新しいショップオーナーを再設定する必要があります。'
no-enough-money-to-keep-shops: <red>ショップを維持するのに十分な資金がありませんので全てのショップが削除されました...
3rd-plugin-build-check-failed: <red>サードパーティプラグイン <bold>{0}<reset><red> が権限チェックを拒否しました。そこに権限を設定していませんか？
not-a-integer: 数字しか入力できません。あなたが入力したのは {0} でした
translation-country: '&c&l言語圏: &b&l日本語 (ja_JP)'
buying-more-than-selling: '<red>警告: あなたは販売価格以上の値段で買取価格を設定しています！'
purchase-failed: '<red>購入失敗: 内部エラーが発生しました。サーバー管理者に連絡してください。'
denied-put-in-item: <red>ショップにこのアイテムを入れることはできません！
shop-has-changed: <red>クリックしてから利用しようとしたショップが変わっています！
flush-finished: <green>未読メッセージの取り出しに成功しました
no-price-given: <red>有効な価格を設定してください
shop-already-owned: <red>これはすでにショップです。
backup-success: <green>バックアップに成功しました。
not-looking-at-shop: <red>QuickShopが見つかりませんでした。確認する必要があります。
you-cant-afford-a-new-shop: <red>新しいショップを作成するには {0} かかります。
success-created-shop: <red>ショップが作成されました。
shop-creation-cancelled: <red>ショップの作成がキャンセルされました。
shop-owner-self-trade: <yellow>自分のショップと取引することで、残高を獲得できない可能性があります。
purchase-out-of-space: <red>このショップは空き容量がありません。店主またはショップスタッフにお問い合わせください。
reloading-status:
  success: <green>エラーなしでリロードが完了しました。
  scheduled: <green>リロードが完了しました。<gray>(一部の変更は反映に時間が掛かる場合があります)
  require-restart: <green>リロードが完了しました。<yellow>(一部の変更については、サーバーの再起動が必要です)
  failed: <red>リロードに失敗しました。サーバーコンソールを確認してください
player-bought-from-your-store-tax: <green>{0} が {1} 個の {2} をあなたのショップから購入したので {3} を獲得しました。(うち消費税 {4})
not-enough-space: <red>あなたには {0} 個分のスペースしかありません！
shop-name-success: <green>ショップ名を <yellow>{0}<green> に設定することに成功しました。
shop-staff-added: <green>あなたのショップスタッフに {0} を追加しました。
shop-staff-empty: <yellow>このショップにはスタッフがいません。
shops-recovering: バックアップからショップを復元しています...
virtual-player-component-hover: "<gray>This is a virtual player.\n<gray>Refers to a system account of this name with the same name.</gray>\n<green>UUID: <yellow>{0}</yellow></green>\n<green>Username: <yellow>{1}</yellow></green>\n<green>Display as: <yellow>{2}</yellow></green>"
real-player-component-hover: "<gray>This is a real exists player.\n<green>UUID: <yellow>{0}</yellow></green>\n<green>Username: <yellow>{1}</yellow></green>\n<green>Display as: <yellow>{2}</yellow></green>\n<gray>If you wish to use a virtual system account with the same name, add <dark_gray>\"[]\"</dark_gray> to the both side of the username: <dark_gray>[{1}]</dark_gray>."
menu:
  sell-tax: <green>消費税として <yellow>{0} <green>を支払いました
  owner: '<green>店主: {0}'
  preview: <aqua>[アイテムプレビュー]
  enchants: <dark_purple>エンチャント
  sell-tax-self: <green>このショップの店主なので、消費税を支払っていません。
  shop-information: '<green>ショップ情報 :'
  item: '<green>アイテム : <yellow>{0}'
  price-per: '<green>単価: <yellow>{0} <green>- <yellow>{1}'
  item-name-and-price: <yellow>{0} {1} <green>を <yellow>{2}
  item-name-and-price-tax: <yellow>{0} {1} <green>for</green> {2} <gray>(<green>{3}</green> in taxes)</gray>
  successful-purchase: '<green>購入成功:'
  price-per-stack: '<green>単価: <yellow>{2}x {0} - {1}'
  stored-enchants: <dark_purple>エンチャント保管
  item-holochat-error: <red>[Error]
  this-shop-is-selling: <green>このショップは <aqua>販売<green> です
  shop-stack: '<green>1商品の内容量: <yellow>{0}'
  space: '<green>空き: <yellow>{0}'
  effects: <green>効果
  damage-percent-remaining: <green>残り耐久値 <yellow>{0}%
  item-holochat-data-too-large: <red>[Error] Item NBT is too large for showing
  stock: '<green>在庫: <yellow>{0}'
  this-shop-is-buying: <green>このショップは <light_purple>買取<green> です
  successfully-sold: '<green>売却成功:'
  total-value-of-chest: '<green>チェストの合計額: <yellow>{0}'
currency-not-exists: <red>設定したい通貨が見当たりません。スペルが間違っているか、その通貨がこの世界では利用できないかもしれません。
no-nearby-shop: <red> {0} に一致するショップは近所にありません。
translation-author: 'Ghost_chu, Andre_601'
integrations-check-failed-trade: <red>{0} 統合がショップの取引を拒否しました
shop-transaction-failed: <red>Sorry, but an internal error occurred while processing your purchase. The purchase has been cancelled and any operation has been rollback. Please contact the server administrators if it keeping occurred.
success-change-owner-to-server: <green>ショップの店主をサーバーに設定しました。
shop-name-not-found: <red>The shop named <yellow>{0} <red>not exists.
shop-name-too-long: <red>このショップ名は長すぎます(最大長 {0})。別な名前を設定して下さい！
metric:
  header-player: '<yellow>{0} の {1} {2} トランザクション:'
  action-hover: <yellow>{0}
  price-hover: <yellow>合計 {0}, {1} 税を含む。
  unknown: <gray>(不明)
  undefined: <gray>(名前なし)
  no-results: <red>取引が見つかりませんでした
  action-description:
    DELETE: <yellow>Player deleted a shop. And refunded the shop creation fee to owner if possible.
    ONGOING_FEE: <yellow>Player paid the ongoing fee because the payment period.
    PURCHASE_BUYING_SHOP: <yellow>プレイヤーはショップにアイテムを売りました
    CREATE: <yellow>プレイヤーがショップを作成しました
    PURCHASE_SELLING_SHOP: <yellow>プレイヤーがショップからアイテムを購入しました
    PURCHASE: <yellow>ショップで購入した商品
  query-argument: 'クエリ引数: {0}'
  amount-hover: <yellow>{0}x
  header-shop: '<yellow>The shop {0}''s {1} {2} transactions:'
  player-hover: |-
    <yellow>{0}<newline><gold>UUID: <gray>{1}
  looking-up: <yellow>メトリック検索を実行しています。お待ちください...
  tax-hover: <yellow>{0} 税
  header-global: '<yellow>The server {0} {1} transactions:'
  na: <gray>該当なし
  transaction-count: <yellow>{0} 合計
  shop-hover: |-
    <yellow>{0}<newline><gold>Pos: <gray>{1} {2} {3}, World: {4}<newline><gold>Owner: <gray>{5}<newline><gold>Shop Type: <gray>{6}<newline><gold>Item: <gray>{7}<newline><gold>Price: <gray>{8}
  time-hover: '<yellow>時間: {0}'
  amount-stack-hover: <yellow>{0}x スタック
permission-denied-3rd-party: '<red>アクセス拒否: サードパーティのプラグイン [{0}]'
you-dont-have-that-many-items: <red>{1} を {0} 個 しか持っていません。
complete: <green>完了!
translate-not-completed-yet-url: '{1} では言語 {0} の翻訳が完了していません。翻訳の改善に協力しませんか？参照: {2}'
success-removed-shop: <green>ショップを削除しました。
currency-set: <green>ショップの通貨は {0} に設定されました。
shop-purged-start: <green>ショップパージを開始しました。コンソールを確認してください。
economy-transaction-failed: <red>Sorry, but an internal error occurred while processing your transaction. The transaction has been cancelled and any economy operation has been rollback. Please contact the server administrators if it keeping occurred.
nothing-to-flush: <green>新しいショップメッセージはありませんでした
no-price-change: <red>これでは価格変更になりません！
edition-confilct: QuicShop-Hikari with QuickShop-Reremake installed may conflict each other, uninstall one of them.
inventory-unavailable: |-
  <red>This shop InventoryWrapper not exists or invalid. Did you using addon to re-binding the shop Inventory? Information: InventoryWrapper={0}, WrapperProvider={1}, SymbolLink={2}. Please contact the server administrators.
file-test: これはテスト用のファイルです。私たちはこれを使って、messages.jsonは壊れていないかをチェックします。ここに好きなイースターエッグを挿入してもいいですよ:)
unknown-player: <red>指定したプレーヤーが存在しません。入力したユーザー名を確認してください。
player-offline: <red>The target player currently is offline.
player-profile-format: <aqua>{0}</aqua><gold>(<yellow>{1}</yellow>)</gold>
shop-type:
  selling: 売却
  buying: 買取
language:
  qa-issues: '<yellow>品質保証の問題: <aqua>{0}%'
  code: '<yellow>コード: <gold>{0}'
  approval-progress: '<yellow>承認の進行状況: <aqua>{0}%'
  translate-progress: '<yellow>翻訳の進行状況: <aqua>{0}%'
  name: '<yellow>名前: <gold>{0}'
  help-us: <green>[翻訳の品質向上にご協力ください]
warn-to-paste: |-
  <yellow>データを収集してPastebinにアップロードするには、しばらく時間がかかる場合があります。<red><bold>警告:<red> データは1週間公開されます。サーバーの設定やその他の機密情報が漏洩する可能性があります。<bold>信頼できるスタッフや開発者にのみ送信するようにしてください。
how-many-sell-stack: <yellow>チャットで<aqua>売却<green>したい商品の数を入力して下さい。1つの商品に <yellow>{0} 個<green> のアイテムが含まれており、あなたは<yellow>{1} つ<green> の商品を売却することができます。すべて売却するにはチャットで <aqua>{2}<green> と入力して下さい。
updatenotify:
  buttontitle: '[更新する]'
  onekeybuttontitle: '[ワンキーアップデート]'
  label:
    github: '[GitHub]'
    ore: '[鉱石]'
    lts: '[LTS]'
    nbtapi: '[+NBTAPI]'
    qualityverifyed: '[品質]'
    master: '[Master]'
    unstable: '[不安定]'
    paper: '[+Paper Optimized]'
    stable: '[安定]'
    spigotmc: '[SpigotMC]'
    papermc: '[PaperMC]'
    bukkitdev: '[BukkitDev]'
    polymart: '[Polymart]'
    modrinth: '[Modrinth]'
    basic: '[Basic]'
  list:
    - '{0} がリリースされました。まだ {1} を使用しています！'
    - どかーん！新しいアップデート {0} がやってきました。更新しよう！
    - サプライズです！ {0} がやってきました！あなたは {1} です
    - アップデートが必要です。{0} がリリースされました！
    - おっと！ {0} がリリースされました。今は {1} です！
    - マジでQSが {0} にアップデートされてるんだって！なんでまだ更新してないんだ？
    - 修正と再... 申し訳ありませんが、{0} がリリースされました！
    - エラー！間違えました。これはエラーではありません。{0} がリリースされました！
    - ああ、神様！ {0} が出ました！ まだ {1} を使用しているのはなぜですか？
    - '今日のニュース：QuickShop が {0} に更新されました！'
    - プラグインは戦死しました。{0} にアップデートする必要があります！
    - '{0} を更新しました。アップデートしましょう！'
    - 司令官、アップデートがあります。{0} が出ました！
    - 私のスタイルを見てください---{0} が更新されました。まだ {1} を使用しています
    - ああ！ 新しい更新 {0}！ 更新を！
    - どう思う？{0} がリリースされました！更新しよう！
    - ドクター！QuickShopに新しいアップデート {0} がやってきました！アップデートした方がいいですよ～
    - こ～こ～だ～よ～！QuickShopに新しいアップデート {0} があるよ～
    - Paimonはクイックショップに新しいアップデート {0} があると言っています！
  remote-disable-warning: '<red>このバージョンのQuickShopは、リモートサーバーによって無効とマークされており、このバージョンには深刻な問題がある可能性があります。{0}. この警告は安定したバージョンに切り替えるまで表示され続けますが、サーバーのパフォーマンスに影響を与えることはありません。'
purchase-out-of-stock: <red>このショップは在庫がなくなりました。店主またはショップスタッフにお問い合わせください。
nearby-shop-entry: '<green>- 情報:{0} <green>価格:<aqua>{1} <green>x:<aqua>{2} <green>y:<aqua>{3} <green>z:<aqua>{4} <green>距離: <aqua>{5} <green>ブロック'
chest-title: QuickShopストア
console-only: <red>このコマンドはコンソールでのみ実行できます。
failed-to-put-sign: <red>情報看板を設置する十分なスペースがショップの周囲にありません。
shop-name-unset: <red>このショップの名前が削除されました
shop-nolonger-freezed: <green>ショップを凍結解除しました。通常の状態に戻ります！
no-permission-build: <red>あなたはここではショップを作成できません。
tableformat:
  left_half_line: <dark_purple>+--------------------
  right_half_line: <dark_purple>--------------------+
  full_line: <dark_purple>+---------------------------------------------------+
  left_begin: '<dark_purple>| '
quickshop-gui-preview: QuickShop GUI アイテムプレビュー
translate-not-completed-yet-click: '{1} では言語 {0} の翻訳が完了していません。翻訳の改善に協力しませんか？ここをクリックしてください！'
taxaccount-invalid: <red>対象アカウントが無効です。有効なプレイヤー名またはuuid(ダッシュ付き)を入力してください。
player-bought-from-your-store: <red>{0} が {1} 個の {2} をあなたのショップから購入したので {3} を獲得しました。
reached-maximum-can-create: <red>あなたはすでに最大値を超えて {0} / {1} のショップを作成しています！
reached-maximum-create-limit: <red>作成可能なショップ数の上限に達しました
translation-version: 'サポートバージョン: Hikari'
no-double-chests: <red>ラージチェストのショップを作成することはできません。
price-too-cheap: <red>価格は<yellow>${0}<red>以上である必要があります。
shop-not-exist: <red>そこにショップはありませんでした。
bad-command-usage: <red>コマンド引数が間違っています!
cleanghost-warning: <yellow>This command will purge <red>all</red> shops if the shop is corrupted, was created in not allowed worlds, is selling/buying not allowed items or <red><bold>EXISTS IN A UNLOADED WORLD</bold></red>. Make sure to create a full backup of your shop data first and use <aqua>/qs cleanghost confirm</aqua> to continue.
cleanghost-starting: <green>Starting to check for ghost shops (missing container blocks). All non-existing shops will be removed...
cleanghost-deleting: <yellow>破損したショップ <aqua>{0}</aqua> が見つかりました。なぜなら {1}, マークして削除...
cleanghost-deleted: <green>Total <yellow>{0}</yellow> shops have been deleted.
shop-purchase-cancelled: <red>ショップでの購入がキャンセルされました。
bypassing-lock: <red>QuickShopロックをバイパスします！
bungee-cross-server-msg: '<yellow>QuickShop CSM: <green>{0}'
saved-to-path: バックアップファイルは {0} に保存されました。
shop-now-freezed: <green>ショップを凍結しました。誰もこのショップと取引できません！
price-is-now: <green>ショップの新しい価格は <yellow>{0} です
shops-arent-locked: <red>ショップは保護されていません！保護が必要な場合は、LWCやLocketteなどでロックしてください！
that-is-locked: <red>このショップはロックされています。
shop-has-no-space: <red>ショップには {0} 個以上 {1} 個分のスペースしかありません
safe-mode-admin: '<red><bold>WARNING: <red>このサーバーのクイックショップは現在安全モードで動作していますが、機能は動作しません。エラーをチェックするには<yellow>/qs <red>コマンドを入力してください。'
shop-stock-too-low: <red>ショップには {1} は {0} 個 しか残っていません！
world-not-exists: <red>ワールド <yellow>{0}<red> が存在しません
how-many-sell: <green>チャットで<light_purple>売却<green>したい個数を入力してください。<yellow>{0}個 <green>所持しています。<aqua>{1} <green>で全て売却
shop-freezed-at-location: <yellow> {0} のショップ {1} が凍結されました！
translation-contributors: '貢献者: Timtower, Netherfoam, KaiNoMood, Mgazul, JackTheChicken, Andre_601 and Namiu'
empty-success: <green>ショップを空っぽにしました
taxaccount-set: <green>このショップの税金口座は <yellow>{0} に設定されました
support-disable-reason:
  hot-reload: <yellow>Unsupported hot reloaded, restart the server and check again.
  outdated: <yellow>This version of QuickShop already outdated, up-to-date before requesting support!
  bad-hosts: |-
    <yellow>This server HOSTS has been modified and QuickShop some functions require connect to Mojang API correctly. Fix the HOSTS before asking support, Windows at C:\windows\system32\drivers\etc\hosts, Linux at /etc/hosts.
  privacy: <yellow>This server running under cracked (offline) mode. If you are running bukkit server under proxy and online-mode=true on proxy, please configure the proxy related settings correctly.
  modified: <yellow>File integrity check failed, this QuickShop build has been modified by others.
  consolespamfix-installed: <yellow>ConsoleSpamFix installed, it will hidden exception details, temporarily disable it during asking support.
  authlib-injector-detected: <yellow>This server running under 3rd-party authlib provider like authlib-injector.
  unsupported-server-software: <yellow>Unsupported server software, Any modded hybrid server software is unsupported including MCPC, Cauldron, CatServer, Mohist, Magma, Fukkit, Cardboard etc.
supertool-is-disabled: <red>スーパーツールが無効になっています。どのショップも破壊することはできません。
unknown-owner: 不明
restricted-prices: '<red>{0} の価格制限: 最低 {1}, 最高 {2}'
nearby-shop-this-way: <green>ショップは {0} ブロック先にあります。
owner-bypass-check: <yellow>全てのチェックをバイパスしました。取引成功！ (あなたがショップの店主になりました！)
april-rick-and-roll-easter-egg: "<hover:show_text:'Click to open in browser for time limited rewards!'><click:open_url:'https://www.youtube.com/watch?v=dQw4w9WgXcQ'><green>Click here to acquire your time limited rewards that provided by QuickShop-Hikari developer!</green></click></hover>"
signs:
  item-right: ''
  out-of-space: 容量不足
  unlimited: 無制限
  stack-selling: 販売 {0}
  stack-price: '{0} / {1}個 {2}'
  status-unavailable: <red>
  out-of-stock: 在庫切れ
  stack-buying: 買取 {0}
  freeze: 取引が無効化されました。
  price: '{0} / 1個'
  buying: 買取 {0}
  header: '{1}{0}'
  header-available: <green>{0}
  header-unavailable: <red>{0}
  selling: 販売 {0}
  status-available: <green>
  item-left: ''
negative-amount: <red>マイナス量を取引できません
display-turn-on: <green>ショップの表示をオンにしました。
shop-staff-deleted: <green>あなたのショップスタッフの {0} を削除しました。
nearby-shop-header: '<aqua>{0}<green> に一致する近くのショップ:'
backup-failed: データベースをバックアップできません。詳細についてはコンソールを確認してください。
shop-staff-cleared: <green>ショップのスタッフを全員削除しました。
price-too-high: <red>ショップの価格が高すぎます！ {0} より高い価格のショップは作成できません。
plugin-cancelled: '<red>Operation cancelled, Reason: {0}'
player-sold-to-your-store: <green>{0} があなたのショップに {2} を {1} 個 売却しました。
shop-out-of-stock: <dark_purple>{0}、{1}、{2} のショップで {3} の在庫がなくなりました！
how-many-buy: <green>チャットで<aqua>購入<green>したい個数を入力してください。<yellow>{0}個 <green> 購入できます。<aqua>{1} <green>で全て購入
language-info-panel:
  help: 'ヘルプ: '
  code: 'コード: '
  name: '言語: '
  progress: '進捗: '
  translate-on-crowdin: '[Crowdinで翻訳]'
item-not-exist: <red>アイテム<yellow>{0} <red>は存在しません。スペルを確認してください。
shop-creation-failed: <red>ショップの作成に失敗しました。サーバー管理者に連絡してください。
inventory-space-full: <red>Your inventory space remains can only put <green>{1}x <red>items into it, try empty the your inventory!
no-creative-break: <red>クリエイティブモードでは他のプレイヤーのショップを破壊することはできません。サバイバルモードに切り替えるか、代わりにスーパーツール {0} を使用してください。
booleanformat:
  success: <green>✔
  failed: <red>✘
controlpanel:
  stack: '<green>Per bulk amount: <aqua>{0} <yellow>[<light_purple><bold>Change<yellow>]'
  price-hover: <yellow>クリックしてアイテムの新しい価格を設定します。
  remove: <red><bold>[ショップ削除]
  mode-buying-hover: <yellow>クリックしてショップを販売モードに変更します。
  empty: '<green>Empty: Remove all items <yellow>[<light_purple><bold>OK<yellow>]'
  stack-hover: <yellow>クリックして1商品の内容量を設定します。通常動作の場合は1に設定します。
  alwayscounting-hover: <yellow>Click to toggle if the shop is always counting container.
  alwayscounting: '<green>Always counting: {0} <yellow>[<light_purple><bold>Change<yellow>]'
  setowner: '<green>店主: <aqua>{0} <yellow>[<light_purple><bold>変更<yellow>]'
  freeze: '<yellow>Freeze mode: <aqua>{0} <yellow>[<light_purple><bold>Toggle<yellow>]'
  price: '<green>Price: <aqua>{0} <yellow>[<light_purple><bold>Change<yellow>]'
  currency-hover: <yellow>このショップが使用している通貨を設定または削除する場合はクリックしてください
  lock: '<yellow>Shop lock: <aqua>{0} <yellow>[<light_purple><bold>Toggle<yellow>]'
  mode-selling: '<green>ショップモード: <aqua>販売 <yellow>[<light_purple><bold>変更<yellow>]'
  currency: '<green>Currency: <aqua>{0} <yellow>[<light_purple><bold>Set<yellow>]'
  setowner-hover: <yellow>クリックで店主を変更します
  mode-buying: '<green>Shop mode: <aqua>Buying <yellow>[<light_purple><bold>Change<yellow>]'
  item: '<green>Shop Item: {0} <yellow>[<light_purple><bold>Change<yellow>]'
  unlimited: '<green>Unlimited: {0} <yellow>[<light_purple><bold>Change<yellow>]'
  unlimited-hover: <yellow>クリックで制限/無制限を切り替えます
  refill-hover: <yellow>クリックでアイテム追加
  remove-hover: <yellow>クリックしてショップを削除します。
  toggledisplay-hover: <yellow>ショップのディスプレイアイテムの状態を切り替えます
  refill: '<green>Refill: Refill the items <yellow>[<light_purple><bold>OK<yellow>]'
  freeze-hover: <yellow>ショップのフリーズ状態を切り替えます。
  lock-hover: <yellow>ショップのロック保護を有効/無効にします。
  item-hover: <yellow>ショップアイテムを変更するにはクリックしてください
  infomation: '<green>ショップコントロールパネル:'
  mode-selling-hover: <yellow>クリックしてショップを買取モードに変更します。
  empty-hover: <yellow>クリックしてショップの在庫を削除します。
  toggledisplay: '<green>Display-Item: <aqua>{0} <yellow>[<light_purple><bold>Toggle</bold></light_purple>]'
  history: '<green>History: <yellow>[<bold><light_purple>View</light_purple></bold>]</yellow>'
  history-hover: <yellow>Click to view shop history logs
timeunit:
  behind: behind
  week: "{0} 週間"
  weeks: "{0} 週間"
  year: "{0} 年"
  before: 以前
  scheduled: スケジュール済み
  years: "{0} 年"
  scheduled-in: '{0}でスケジュール'
  second: "{0} 秒"
  std-past-format: '{5}{4}{3}{2}{1}{0}前'
  std-time-format: HH:mm:ss
  seconds: "{0} 秒"
  hour: "{0} 時間"
  scheduled-at: scheduled at {0}
  after: 後
  day: "{0} 日"
  recent: 最近
  between: 期間
  hours: "{0} 時間"
  months: "{0} ヶ月"
  longtimeago: ずっと昔
  between-format: '{0} と {1} の間'
  minutes: "{0} 分"
  justnow: たった今
  minute: "{0} 分"
  std-format: yyyy/MM/dd HH:mm:ss
  future-plain-text: 以後
  month: "{0} ヶ月"
  future: in {0}
  days: "{0} 日"
command:
  reloading: '<green>Configuration reloaded. <yellow>Some changes may require reboot to affect. <newline><gray>(Notice: Reloading behavior has been changed after ********, we now only reload configuration but not whole plugin to ensure the server won''t crashed.)'
  description:
    buy: <yellow>ショップを<light_purple>買取<yellow>モードに変更します
    about: <yellow>QuickShopの情報を表示します
    language: <yellow>現在使用されている言語を変更します
    purge: <yellow>バックグラウンドでショップのパージタスクを開始する
    paste: <yellow>サーバーデータを Pastebin に自動アップロードします
    title: <green>QuickShop ヘルプ
    remove: <yellow>あなたが見ているショップを削除します
    ban: <yellow>ショップからプレイヤーをBANする
    empty: <yellow>ショップから全ての在庫を削除します
    alwayscounting: <yellow>常時コンテナをカウントするかどうかを設定する
    setowner: <yellow>ショップの所有権を変更します
    reload: <yellow>config.yml をリロードします
    freeze: <yellow>ショップ取引を無効または有効にする
    price: <yellow>ショップの買取/販売価格を変更します
    find: <yellow>指定アイテムの最寄りショップを検索します
    create: <yellow>指定したチェストに新しいショップを作成します
    lock: <yellow>ショップのロック状態を切り替える
    currency: <yellow>ショップの通貨設定を設定または削除する
    removeworld: <yellow>指定したワールド内のすべてのショップを削除します
    info: <yellow>QuickShop 統計を表示します
    owner: <yellow>ショップの所有権を変更します
    amount: <yellow>アイテムの個数を設定する (チャットに問題がある場合に便利)
    item: <yellow>ショップの取引アイテムを変更します
    debug: 開発者モードの有効化
    unlimited: <yellow>ショップに無制限の在庫を与えます
    sell: <yellow>ショップを<aqua>販売<yellow>モードに変更します
    fetchmessage: <yellow>未読ショップメッセージを取得
    staff: <yellow>ショップスタッフを管理します
    clean: <yellow>在庫のないすべての(読み込まれた) ショップを削除します
    refill: <yellow>特定数のアイテムをショップに追加します
    help: <yellow>QuickShopのヘルプを表示します
    removeall: <yellow>指定したプレイヤーのすべてのショップを削除します
    unban: <yellow>ショップからプレイヤーのBANを解除する
    transfer: <yellow>自分のすべてのショップを指定したプレイヤーに譲渡します
    transferall: <yellow>自分のすべてのショップを指定したプレイヤーに譲渡します
    transferownership: <yellow>Transfer the shop you're looking at to someone else
    size: <yellow>ショップの1商品の内容量を変更します
    supercreate: <yellow>全ての保護チェックを回避してショップを作成します
    taxaccount: <yellow>ショップで使用する税のアカウントを設定する
    name: <yellow>Naming a shop to specific name
    toggledisplay: <yellow>ショップのアイテムの状態を切り替える
    permission: <yellow>Shop permission management
    lookup: <yellow>Manage lookup-able items table
    database: <yellow>View and maintain QuickShop database
    benefit: <yellow>Settings of divide benefits between shop owner and other players
    tag: <yellow>Add, remove or query tags of a shop
    suggestprice: <yellow>Suggest a recommend price for looking shop, based on other shops
    history: <yellow>View the history transactions of a shop
    sign: <yellow>Change the sign material of a shop
  bulk-size-not-set: '<red>使用法: /qs size <amount>'
  no-type-given: '<red>使用法: /qs find <アイテム名>'
  feature-not-enabled: この機能は設定ファイルで有効になっていません。
  now-debuging: <green>開発者モードに切り替え成功、QuickShopをリロードしています。
  no-amount-given: <red>個数が入力されていません。<green>/qs refill <amount><red> を使用してください
  no-owner-given: <red>店主が指定されていません
  disabled: '<red>このコマンドは無効です: <yellow>{0}'
  bulk-size-now: <green>現在は <yellow>{1} <green>を <yellow>{0} 個 <green> 取引しています
  toggle-always-counting:
    counting: <green>Shop is now always counting container even is unlimited
    not-counting: <green>ショップが無制限の場合、ショップはリスペクトされます
  cleaning: <green>在庫のないショップを削除しています...
  now-nolonger-debuging: <green>開発者モードを無効化しました。QuickShopをリロードしています。
  toggle-unlimited:
    limited: <green>ショップは制限されています
    unlimited: <green>ショップは無制限になりました
  transfer-success-other: <green>{0} {1}<green>のショップを<yellow>{2} に転送しました
  no-trade-item: <green>変更対象のアイテムをメインハンドで持って下さい
  wrong-args: <red>無効な引数です。コマンドを<bold>/qs help <reset>で確認して下さい。
  some-shops-removed: <yellow>{0} つのショップを削除しました
  new-owner: '<green>新しい店主: <yellow>{0}'
  format: <green>/{0} {1} <yellow>- {2}
  transfer-success: <yellow>{0} のショップを <yellow>{1} に転送しました
  now-buying: <green>現在 <light_purple>買取 <yellow>{0}
  now-selling: <green>現在 <aqua>販売 <yellow>{0}
  cleaned: <yellow>{0} つ<green> のショップを削除しました。
  trade-item-now: <green>現在は <yellow>{1} <green>を <yellow>{0} 個 <green> 取引しています
  no-world-given: <red>ワールド名を指定してください
  format-disabled: <red>/{0} {1} <gray>- {2}
  invalid-bulk-amount: <red>その値 {0} は最大値よりも大きいか、若しくは1よりも小さい値です
currency-not-support: <red>エコノミープラグインはマルチエコノミー機能をサポートしていません。
trading-in-creative-mode-is-disabled: <red>クリエイティブモードではこのショップと取引することはできません。
the-owner-cant-afford-to-buy-from-you: <red>この商品の買取価格は {0} ですが、店主は {1} しか所持していません。
you-cant-afford-shop-naming: <red>You can't afford shop naming, it costs {0} to naming.
inventory-error: |-
  <red>Failed to processing the InventoryWrapper. Did you using addon to re-binding the shop Inventory? Information: Exception={0}, InventoryWrapper={1}, WrapperType={2}, WrapperProvider={3}, SymbolLink={4}. Please contact the server administrators.
integrations-check-failed-create: <red>{0} 統合がショップの作成を拒否しました
shop-out-of-space: <dark_purple>あなたの {0}, {1}, {2} のショップが一杯です。
admin-shop: アドミンショップ
no-anythings-in-your-hand: <red>あなたは手に何も持っていません。
no-permission: <red>あなたは権限を持っていないため実行できません。
chest-was-removed: <red>チェストが削除されました。
you-cant-afford-to-buy: <red>価格は {0} ですが {1} しか所持していません。
shops-removed-in-world: <yellow>ワールド <aqua>{1}<yellow> で合計 <aqua>{0} つ<yellow> のショップを削除しました。
display-turn-off: <green>ショップの表示をオフにしました。
client-language-unsupported: <yellow>QuickShopはクライアント言語をサポートしていません。 {0} ロケールにフォールバックします。
language-version: '63'
not-managed-shop: あなたはショップの店主でもモデレーターでもありません。
shop-cannot-trade-when-freezing: <red>凍結されているため、このショップと取引できません。
invalid-container: <red>Invalid container, You can only create the shop on block who have inventory.
permission:
  header: <green>ショップ権限の詳細
  header-player: <green> {0} のショップ権限の詳細
  header-group: <green>グループ {0}のショップ権限の詳細
  table: <gold><b>{0}</b>:</gold> <gray>{1}</gray>
  item:
    purchase: <yellow>Permission to allow users who have this to purchase the shop. (including buying and selling)
    show-information: <yellow>Permission to allow users who have this to see the shop information. (open shop info panel)
    preview-shop: <yellow>ショップをプレビューできる権限です。(アイテムのプレビュー)
    search: <yellow>Permission to allow users who have this to search the target shop. (remove permission will hide shop from search result)
    delete: <yellow>ショップを削除する権限。
    receive-alert: <yellow>Permission to allow users who have this to receive alert message (e.g out-of-stock or new trade).
    access-inventory: <yellow>ショップ在庫にアクセスできる権限。
    ownership-transfer: <yellow>Permission to allow users who have this to transfer the shop ownership.
    management-permission: <yellow>Permission to allow users who have this to manage the groups permissions and edit user's group.
    toggle-display: <yellow>Permission to allow users who have this to toggle the shop display item.
    set-shoptype: <yellow>Permission to allow users who have this to set the shop type (switch buying or selling).
    set-price: <yellow>Permission to allow users who have this to set the shop price.
    set-item: <yellow>Permission to allow users who have this to set the shop item.
    set-stack-amount: <yellow>Permission to allow users who have this to set the shop stack amount.
    set-currency: <yellow>Permission to allow users who have this to set the shop currency.
    set-name: <yellow>Permission to allow users who have this to set the shop name.
    set-sign-type: <yellow>Change the sign material that attached on the shop.
    view-purchase-logs: <yellow>Permission to view shop purchase logs.
  group:
    everyone: <yellow>Default group for all users excepted shop owner.
    staff: <yellow>System group for shop staffs.
    administrator: <red>System group for administrators, the users in this group will have permissions almost same with shop owner.
invalid-group: <red>Invalid group name.
invalid-permission: <red>Invalid permission.
invalid-operation: <red>Invalid operation, only {0} are allowed.
player-no-group: <yellow>Player {0} not in any group in this shop.
player-in-group: <green>Player {0} is in group <aqua>{1}</aqua> in this shop.
permission-required: <red>You don't have permission {0} in this shop to do this.
no-permission-detailed: <red>You don't have permission <yellow>{0}</yellow> to do this.
paste-notice: "<yellow>Note: If you are creating a Paste for troubleshooting purposes, be sure to reproduce the error before creating the Paste as quickly as possible; we need the logs retained briefly in the buffer to troubleshoot. If you create a Paste too slowly or without first reproducing the error or restarted server, the Paste will record nothing and useless."
paste-uploading: <aqua>Please wait... Uploading the paste to pastebin......
paste-created: '<green>Paste created, click to open in browser: <yellow>{0}</yellow><newline><red>Warning: <gray>Never send paste to who you don''t trust.'
paste-created-local: |-
  <green>Paste created and saved to your local disk at: {0}<newline><red>Warning: <gray>Never send paste to who you don't trust.
paste-created-local-failed: <red>Failed to save paste to your local disk, please check your disk.
paste-451: |-
  <gray><b>TIPS: </b> Your current country or region seems to have blocked the CloudFlare Workers service and QuickShop Paste may not be loaded properly.<newline><gray>If the subsequent operation fails, try adding the --file additional parameter to generate a local Paste: <dark_gray>/qs paste --file</dark_gray>
paste-upload-failed: <red>Failed to upload paste to {0}, trying other pastebin provider...
paste-upload-failed-local: <red>Failed to upload paste, trying generate local paste...
command-incorrect: '<red>Command usage incorrect, type /qs help to check help. Usage: {0}.'
successfully-set-player-group: <green>Successfully set player {0} group to <aqua>{1}</aqua>.
successfully-unset-player-group: <green>Successfully remove player group in this shop.
successfully-set-player-permission: <green>Successfully set player {0} permission <aqua>{1}</aqua> in shop <aqua>{2}</aqua>.
lookup-item-created: <green>An item named <aqua>{0}</aqua> has been created in the lookup table. You can refer this item in configurations now.
lookup-item-exists: <red>An item named <yellow>{0}</yellow> already exists in the lookup table, delete it or pick another name.
lookup-item-not-found: <red>An item named <yellow>{0}</yellow> not exists.
lookup-item-name-illegal: <red>Item name illegal. Only alphanumeric characters and underscores are allowed.
lookup-item-name-regex: '<red>The name must match this regex: <aqua>{0}</aqua>.'
lookup-item-test-not-found: '<gold>Test: <yellow>The item in your hand not registered in lookup table.'
lookup-item-test-found: '<gold>Test: <green>The item in your hand was registered as name <aqua>{0}</aqua> in lookup table.'
lookup-item-removed: <green>The specified item <aqua>{0}</aqua> has been successfully removed from lookup table.
internal-error: <red>内部エラーが発生しました。サーバー管理者に連絡してください。
argument-cannot-be: <red>The argument <aqua>{0}</aqua> cannot be <yellow>{1}</yellow>.
argument-must-between: <red>The argument <aqua>{0}</aqua> value must between <yellow>{1}</yellow> and <yellow>{2}</yellow>
invalid-percentage: <red>Percentage invalid, you must add '%' after the number of percentage.
display-fallback: |-
  <red>Due to an internal failure, a fallback message is being displayed.
  The value of this item may not be localized or processed correctly.
  Please contact the server administrator.
not-a-valid-time: |-
  <red>The string <yellow>{0}</yellow> not a valid timestamp, please enter a <aqua>Zulu Time (ISO 8601)</aqua> or <aqua>Unix Epoch Time in seconds</aqua>.
  <gold>Valid timestamp example: (for Sat, 17 Dec 2022 10:31:37 GMT)</gold>
  <aqua>-<aqua> <yellow>2022-12-17T10:31:37Z</yellow> <grey>(Zulu Time)</grey>
  <aqua>-<aqua> <yellow>1671273097</yellow> <grey>(Unix Epoch Time in seconds)</grey>
invalid-past-time: <red>You can't specify a time in the past.
debug:
  arguments-invalid: <red>Provided arguments <yellow>{0}</yellow> invalid.
  sign-located: '<green>Sign valid: <yellow>{0}</yellow>.'
  operation-missing: <red>You must specific an operation.
  operation-invalid: <red>You must specific an valid operation.
  invalid-base64-encoded-sql: <red>The SQL provided must be base64 encoded.
  warning-sql: |-
    <red><bold>Warning:</bold></red><yellow>You're executing a SQL statement, this may corrupt your database or destroy any data in database even it belongs to other plugins.
    <red>Don't confirm if you don't trust who send you this command.
  warning-sql-confirm: <yellow>To confirm this dangerous operation, please type <aqua>/qs debug database sql confirm {0}</aqua> in 60 seconds.
  warning-sql-confirm-hover: <yellow>Click to confirm this dangerous operation.
  sql-confirm-not-found: <yellow>Didn't found the operation that you given, it may invalid or expired.
  sql-executing: '<yellow>Executing SQL statement: <aqua>{0}</aqua>'
  sql-completed: <green>Completed, {0} rows affected.
  sql-exception: <red>An error occurred while executing the SQL query, check the Console for details!
  sql-disabled: '<red>For security reason, SQL queries are disabled in this server, If you really need this feature, you can add the following flag to startup arguments: <aqua>{0}</aqua> with `true` value to enable it.'
  force-shop-reload: <yellow>Forcing reload all loaded shops...
  force-shop-reload-complete: <green>Forced <aqua>{0}</aqua> shops to be reloaded.
  force-shop-loader-reload: <yellow>Forcing shop-loader reloading...
  force-shop-loader-reload-unloading-shops: <yellow>Unloading <aqua>{0}</aqua> loaded shops...
  force-shop-loader-reload-unloading-shops-from-memory: <yellow>Removing <aqua>{0}</aqua> shops from memory...
  force-shop-loader-reload-reloading-shop-loader: <yellow>Re-calling shop-loader for reloading all shops from database...
  force-shop-loader-reload-complete: <green>Shop-loader has been reloaded all shops!
  toggle-shop-loaded-status: <aqua>Toggle this shop loaded status to <gold>{0}</gold>
  shop-internal-data: '<yellow>The internal data of this shop: <reset>{0}</reset>'
  handler-list-not-valid-bukkit-event-class: <red>The provided class <yellow>{0}</yellow> is not a valid Bukkit event class.
  update-player-shops-signs-no-username-given: <red>You must provide a valid player username.
  update-player-shops-signs-create-async-task: <yellow>Creating async tasks for force updating signs...
  update-player-shops-player-selected: '<yellow>Player selected: <gold>{0}</gold>'
  update-player-shops-player-shops: <yellow>Total <gold>{0}</gold> shops waiting for updates.
  update-player-shops-per-tick-threshold: '<yellow>Max shops can be update per tick: <gold>{0}</gold>'
  update-player-shops-complete: <green>Task completed, Used <yellow>{0}ms</yellow> for updating.
  update-player-shops-task-started: <gold>The tasks has been started, please wait for it to complete.
  item-info-store-as-string: "<green>The store you looking at: <gold>{0}</gold> Hash: <white>{1}</white>"
  item-info-hand-as-string: "<green>The item in your hand: <gold>{0}</gold> Hash: <white>{1}</white>"
  item-matching-result: "<green>Hand2Store: <aqua>{0}</aqua>, Store2Hand: <aqua>{1}</aqua>"
  hikari-cp-size-tweak: "<green>HikariCP MaximumPoolSize and MinimumIdle was set to <white>{0}</white>"
  hikari-cp-testing: "<green>Please wait, testing HikariCP connection..."
  hikari-cp-working: "<green>Pass! HikariCP working well!"
  hikari-cp-not-working: "<red>Failed! The connection that returned by HikariCP is dead! (Not passed test in 1 second)"
  hikari-cp-timeout: "<red>HikariCP is timed out while getting a valid connection, please clean up all active queries to release connection resources."
  queries-stopped: "<green>Stopped <white>{0}</white> active queries."
  queries-dumping: "<yellow>Dumping active queries..."
  restart-database-manager: "<yellow>Restarting SQLManager..."
  restart-database-manager-clear-executors: "<yellow>Clearing executors..."
  restart-database-manager-unfinished-task: "<yellow>Unfinished task: <white>{0}</white>"
  restart-database-manager-unfinished-task-history-query: "<yellow>Unfinished task (History Query): <white>{0}</white>"
  restart-database-manager-reconnect: "<yellow>Re-launching SQLManager via initial sequence (via async executor)"
  restart-database-manager-done: "<green>Done!"
  property-incorrect: "<yellow>You must enter a (and only one) property key=value set. E.g   aaa=bbb"
  property-security-block: "<red>Request was rejected, for security reason, you can only change the property that startsWith <aqua>com.ghostchu.quickshop</aqua> or <aqua>quickshop</aqua>."
  property-removed: "<green>Removed property key <white>{0}</white>"
  property-changed: "<green>Property key <white>{0}</white> was changed from <white>{1}</white> to <white>{2}</white>"
  marked-as-dirty: "<green>Marked all stores as dirty status, they will be force save in next auto-save task. (Restart the server to force execute store saving task)"
  display-removed: "<green>Successfully removed <yellow>{0}</yellow> QuickShop display items/entities from the worlds."
database:
  scanning: <green>Scanning the isolated data in QuickShop databases, the database load may increase in scan progress, this might need a while...
  scanning-async: <yellow>Scanning the isolated data in QuickShop databases on async task thread, the database load may increase in scan progress, this might need a while, try again later.
  already-scanning: <red>An scan task has been started, please wait until it finished.
  trim-warning: '<yellow><red><bold>Warning: </red>Backup your database before continue database trim to avoid the data loss. Once you get ready, execute <aqua>/qs database trim confirm</aqua> to continue.'
  status: '<yellow>Status: {0}'
  status-good: <green>Good
  status-bad: <yellow>Maintenance Required
  isolated: '<yellow>Isolated Data:'
  isolated-data-ids: '<yellow><aqua>└</aqua> Data Records: <gold>{0}</gold>'
  isolated-shop-ids: '<yellow><aqua>└</aqua> Shop Indexes: <gold>{0}</gold>'
  isolated-logs: '<yellow><aqua>└</aqua> Logs: <gold>{0}</gold>'
  isolated-external-caches: '<yellow><aqua>└</aqua> External Caches: <gold>{0}</gold>'
  last-purge-time: <yellow>Last time of trim at {0}
  report-time: <yellow>Last time of scan at {0}
  auto-scan-alert: <yellow>The QuickShop database on this server require a maintenance. Found <gold>{0}</gold> isolated data wait for trimming.
  auto-trim: <green>Auto trim has been enabled on this server, no manually trim required.
  trim-complete: <green>Database trim completed, <yellow>{0}</yellow> isolated trimmed.
  auto-trim-started: <green>Auto trim has been started, please wait...
  trim-start: <green>Database trim started, please wait...
  trim-exception: <red>Database trim failed, an exception raised during database trimming, check the server console.
  generated-at: '<yellow>Generated at: <gold>{0}</gold>'
  purge-date: <red>You must given a date.
  purge-warning: <yellow>This operation will purge the history that stored in your database including shop creates/changes/deletes, purchases, transactions and system logs. Delete those data can free the disk space but all history metric will lost, and other plugins that depend on metrics will stop working. To continue execute this operation, execute `/qs database purgelogs <before-days> confirm`
  purge-task-created: <green>Task created! Database are silent purging history records in background.
  purge-done-with-line: <green>Purge task completed, total <gold>{0}</gold> lines purged from database.
  purge-done-with-error: <red>Purge task failed, check the server console for details.
  purge-players-cache: <yellow>Please wait, purging players caches...
  purge-players-completed: |-
    <green>Successfully purged {0} players caches from both memory and database.<newline><aqua>Note: You server performance may impact by this operation.
  purge-players-error: <red>Failed to purge players caches, please check the server console.
  suggestion:
    trim: <yellow>This database require a isolated data trim, execute command <aqua>/qs database trim</aqua> to trim the database.
always-counting-removal-early-warning: <red>Always Counting feature is scheduled for removal, you shouldn't use it anymore and will stop work in future.
exporting-database: <green>Exporting database, please wait...
exporting-failed: <red>Failed to export database, please check the server console.
exported-database: <green>Database exported to <yellow>{0}</yellow>.
importing-not-found: <red>The <yellow>{0}</yellow> file not found, please check the file path.
importing-early-warning: |-
  <yellow><red><bold>Warning: </bold></red>The backup will be imported to the current database, any exists data will be purged and permanently lost if you don't have a backup.<newline><red>Are you sure to continue the import progress?</red><yellow>Type <aqua>/qs recovery confirm</aqua> to continue.
importing-database: <green>Importing database from backup, please wait...
importing-failed: <red>Failed to import database, please check the server console.
imported-database: <green>Database imported from <yellow>{0}</yellow>.
transfer-sent: <green>Shops transfer request sent to <yellow>{0}</yellow>.
transfer-request: <yellow>Player <aqua>{0}</aqua> requested to transfer their shops to you. Did you want accept the transfer?
transfer-single-request: <yellow>Player <aqua>{0}</aqua> would like to transfer a shop to you. Do you want to accept the request?
transfer-ask: |-
  <gold>Type <red>/qs transfer accept</red> to accept.</gold><newline><gold>Type <red>/qs transfer deny</red> to deny.</gold><newline><gold>Request will expire after <red>{0}</red> seconds.
transferall-ask: |-
  <gold>Type <red>/quickshop transferall accept</red> to accept or <red>/quickshop transferall deny</red> to deny.
  The request will expire after <red>{0}</red> seconds.
transfer-single-ask: |-
  <gold>Type <red>/quickshop transferownership accept</red> to accept or <red>/quickshop transferownership deny</red> to deny.
  The request will expire after <red>{0}</red> seconds.
transfer-accepted-fromside: <green>Player <aqua>{0}</aqua> accepted your shop transfer request.
transfer-accepted-toside: <green>You accepted <aqua>{0}</aqua>'s transfer request.
transfer-rejected-fromside: <red>Player <aqua>{0}</aqua> rejected your shop transfer request.
transfer-rejected-toside: <red>You rejected <aqua>{0}</aqua>'s shop transfer request.
transfer-no-pending-operation: <red>You don't have pending transfer request yet.
transfer-no-self: <red>You can't transfer your shops to yourself.
benefit-overflow: <red>The sum of all benefits cannot be greater than or equal to 100%.
benefit-exists: <red>Target player already in benefits list on this shop.
benefit-removed: <red>Target player has been removed from shop benefits.
benefit-added: <green>Player <aqua>{0}</aqua> has been added into shop benefits!
benefit-updated: <green>Player <aqua>{0}</aqua>'s benefits has been updated!
benefit-query: <green>This shop have <yellow>{0}</yellow> players in benefits list!
benefit-query-list: <yellow> - </yellow><white>Player <gold>{0}</gold>, Benefit <gold>{1}%</gold></white>
tag-added: '<green>Successfully added <aqua>#{0}</aqua> to this shop!'
tag-add-duplicate: '<red>The tag <aqua>#{0}</aqua> already exists at this shop!'
tag-removed: '<green>Successfully removed <aqua>#{0}</aqua> from this shop!'
tag-remove-not-exists: 'The tag <aqua>#{0}</aqua> not exists at this shop!'
tag-cleared: <green>Successfully cleared all tags from this shop!
tag-shops-cleared: '<green>Successfully cleared <aqua>#{0}</aqua> from all your tagged shops!'
tag-query: '<green>This shop have <yellow>{0}</yellow> tags:'
tag-query-listing: '<yellow> - <aqua>#{0}</aqua>'
tag-query-no-tag: <red>This shop have no tags.
tag-query-shops: '<green>This tag contains <yellow>{0}</yellow> shops:'
tag-query-shops-listing: <yellow> - <aqua>{0}</aqua>
batch-operations-based-on-tags-no-failure: <green>Successfully batch processed <yellow>{0}</yellow> shops.
batch-operations-based-on-tags-have-failure: <yellow>Total <yellow>{0}</yellow> shops in batch processing successfully completed, but <red>{1}</red> requests not completed.
batch-operations-based-on-tags-have-failure-with-reason: '<yellow>Total <yellow>{0}</yellow> shops in batch processing successfully completed, but <red>{1}</red> requests not completed. Reason: <gold>{2}</gold>'
batch-operations-based-on-tags-have-failure-error-messages-may-appears-above: <gray>The error messages will send to you above this message in the chat.
addon:
  towny:
    commands:
      town: <yellow>Set or unset a shop to a Town shop
      nation: <yellow>Set or unset a shop to a Nation shop
    make-shop-owned-by-town: <green>You have made the shop owned by town <yellow>{0}</yellow>.
    make-shop-no-longer-owned-by-town: <green>You have reset shop ownership, it now transfer back to original shop owner.
    make-shop-owned-by-nation: <green>You have made the shop owned by nation <yellow>{0}</yellow>.
    make-shop-no-longer-owned-by-nation: <green>You have reset shop ownership, it now transfer back to original shop owner.
    shop-owning-changing-notice: <grey>This shop now owned by a town/nation, original shop owner has been automatically added into administrator list, modify or add any new owner / staff by using command /qs permission
    target-shop-already-is-town-shop: <red>The target shop is already owned by a town.
    target-shop-already-is-nation-shop: <red>The target shop is already owned by a nation.
    target-shop-not-in-town-region: <red>The target shop is not in the town.
    target-shop-not-in-nation-region: <red>The target shop is not in the nation.
    item-not-allowed: <red>This shop's item are not allowed used by town/nation shop, pick another one!
    operation-disabled-due-shop-status: <red>This shop operation has been disabled due this already is a town/nation shop.
    plot-type-disallowed: <red>You can't create town/nation shop on this type of plot.
    flags:
      own: <red>You can only create shops at your owned shop type towny plot.
      modify: <red>You didn't have build permission on this towny plot.
      shop-type: <red>You must create shop on a shop type towny plot.
  residence:
    creation-flag-denied: <red>You don't have permission to create shops in this residence.
    trade-flag-denied: <red>You don't have permission to purchase in this residence.
    you-cannot-create-shop-in-wildness: <red>You can't create shop in wildness.
  griefprevention:
    creation-denied: <red>You don't have permission to create shops in this claim.
    trade-denied: <red>You don't have permission to purchase shops in this claim.
  lands:
    world-not-enabled: <red>You are not allowed to create or purchase in this world.
    creation-denied: <red>You don't have permission to create shops in this Land area.
  plotsquared:
    no-plot-whitelist-creation: <red>You cannot create shop outside of Plot.
    no-plot-whitelist-trade: <red>You cannot purchase shop outside of Plot.
    creation-denied: <red>You don't have permission to create shops in this Plot.
    trade-denied: <red>You don't have permission to purchase shops in this Plot.
    flag:
      create: Create QuickShop-Hikari shops
      trade: Purchase QuickShop-Hikari shops
  superiorskyblock:
    owner-create-only: <red>Only island owner can create shop there.
    owner-member-create-only: <red>Only island owner or member can create shop there.
  worldguard:
    creation-flag-test-failed: <red>You don't have permission create shop in this WorldGuard area.
    trade-flag-test-failed: <red>You don't have permission trade shop in this WorldGuard area.
    reached-per-region-amount-limit: "<red>You have reached the maximum amount of shops in this region."
  discord:
    __to_translators: 'To translators: `addon.discord.discord-messages` use a special variable in strings, %%LANG_KEY%% instead of QuickShop''s style {LANG_INDEX}, do not translate anything inside double ''%'' section. BUT for any other section texts, it respect the normal QuickShop Text System syntax ({0},{1} etc.) This may be difficult to understand because two different variable forms are used in the same program at the same time, but trust me, it is reasonable. (you don''t need translate this string, it''s a comment)'
    __to_users: Like the QuickShop Text System, Discord addon will also auto-detect user language and use user's language to send Discord message, it following the QuickShop-Hikari's language system settings.
    __to_message_designer: 'GUIでDiscordメッセージをデザインしよう: https://glitchii.github.io/embedbuilder/ でJSONのコードをコピーして翻訳に貼り付ければ、はい完成！'
    discord-enabled: <aqua>Successfully <green>enabled</green> your QuickShop discord messages, now you can receive the shop messages from Discord.
    discord-disabled: <aqua>Successfully <red>disabled</red> your QuickShop discord messages, now you won't receive the shop messages from Discord anymore.
    discord-not-integrated: <red>You hadn't link your Discord yet! Please link your Discord account first!
    feature-enabled-for-user: <aqua>You have <green>enabled</green> <gold>{0}</gold> notification.
    feature-disabled-for-user: <aqua>You have <red>disabled</red> <gold>{0}</gold> notification.
    link-help: <yellow>This server using <gold>{0}</gold> for Discord driver, please use <green>{0}</green> to link your Discord account.
    save-notifaction-exception: <red>An error occurred while saving your Discord notifaction settings, please contact the server administrator.
    feature-status-changed: <green>Successfully set the notifaction <aqua>{0}</aqua> status to <gold>{1}</gold>
    commands:
      discord:
        description: <yellow>Managing your QuickShop Discord settings
    discord-messages:
      sold-to-your-shop: |
        {
         "embed":
           {
             "title": ":inbox_tray: 誰かがあなたのショップで商品を売却しました",
             "description": "プレイヤー %%purchase.name%% があなたのショップで %%shop.item.name%% x%%purchase.amount%%  を売却しました。",
             "color": 52084,
             "author": {
               "name": "QuickShop-Hikari",
               "icon_url": ""
             },
             "thumbnail": {
               "url": ""
             },
             "image": {
               "url": ""
             },
             "footer": {
               "text": "QuickShop-Hikari Discord通知",
               "icon_url": ""
             },
             "fields": [
               {
                 "name": "ショップ",
                 "value": "%%shop.display-name%%",
                 "inline": false
               },
               {
                 "name": "売却者",
                 "value": "%%purchase.name%%",
                 "inline": true
               },
               {
                 "name": "アイテム",
                 "value": "%%shop.item.name%%",
                 "inline": true
               },
               {
                 "name": "個数",
                 "value": "%%purchase.amount%% (x%%shop.stacking-amount%%)",
                 "inline": true
               },
               {
                 "name": "支払った金額",
                 "value": "%%purchase.balance-formatted%%",
                 "inline": true
               },
               {
                 "name": "消費税",
                 "value": "%%purchase.taxes-formatted%%",
                 "inline": true
               }
             ]
           }
        }
      bought-from-your-shop: |
        {
           "embed":
             {
               "title": ":outbox_tray: 誰かがあなたのショップで商品を購入しました",
               "description": "プレイヤー %%purchase.name%% があなたのショップで %%shop.item.name%% x%%purchase.amount%%  を購入しました。",
               "color": 52084,
               "author": {
                   "name": "QuickShop-Hikari",
                   "icon_url": ""
               },
               "thumbnail": {
                   "url": ""
               },
               "image": {
                   "url": ""
               },
               "footer": {
                   "text": "QuickShop-Hikari Discord通知",
                   "icon_url": ""
               },
               "fields": [
                   {
                       "name": "ショップ",
                       "value": "%%shop.display-name%%",
                       "inline": false
                   },
                   {
                       "name": "購入者",
                       "value": "%%purchase.name%%",
                       "inline": true
                   },
                   {
                       "name": "アイテム",
                       "value": "%%shop.item.name%%",
                       "inline": true
                   },
                   {
                       "name": "個数",
                       "value": "%%purchase.amount%%(x%%shop.stacking-amount%%)",
                       "inline": true
                   },
                   {
                       "name": "獲得した金額",
                       "value": "%%purchase.balance-formatted%%",
                       "inline": true
                   },
                   {
                       "name": "消費税",
                       "value": "%%purchase.taxes-formatted%%",
                       "inline": true
                   }
               ]
           }
         }
      mod-shop-purchase: |
        {
        "embed":
          {
              "title": ":dollar: Somebody purchased in a shop",
              "description": "The player %%purchase.name%% purchase x%%purchase.amount%% %%shop.item.name%% from the shop.",
              "color": 6061450,
              "author": {
                  "name": "QuickShop-Hikari",
                  "icon_url": ""
              },
              "thumbnail": {
                  "url": ""
              },
              "image": {
                  "url": ""
              },
              "footer": {
                  "text": "QuickShop-Hikari Discord Notification",
                  "icon_url": ""
              },
              "fields": [
                  {
                      "name": "Shop",
                      "value": "%%shop.display-name%%",
                      "inline": false
                  },
                  {
                      "name": "Purchaser",
                      "value": "%%purchase.name%%",
                      "inline": true
                  },
                  {
                      "name": "Item",
                      "value": "%%shop.item.name%%",
                      "inline": true
                  },
                  {
                      "name": "Amount",
                      "value": "%%purchase.amount%% (x%%shop.stacking-amount%%)",
                      "inline": true
                  },
                  {
                      "name": "Balance",
                      "value": "%%purchase.balance-formatted%%",
                      "inline": true
                  },
                  {
                      "name": "Taxes",
                      "value": "%%purchase.taxes-formatted%%",
                      "inline": true
                  }
              ]
          }
        }
      out-of-space: |
        {
        "embed":
          {
              "title": ":mailbox_with_mail: Your shop has run out of space",
              "description": "Your shop inventory is full of items!\nYou need empty your shop container to free the space then you can continue to accept new items.",
              "color": 16065893,
              "author": {
                  "name": "QuickShop-Hikari",
                  "icon_url": ""
              },
              "thumbnail": {
                  "url": ""
              },
              "image": {
                  "url": ""
              },
              "footer": {
                  "text": "QuickShop-Hikari Discord Notification",
                  "icon_url": ""
              },
              "fields": [
                  {
                      "name": "Shop",
                      "value": "%%shop.display-name%%",
                      "inline": false
                  }
              ]
          }
        }
      out-of-stock: |
        {
            "embed":
                {
                    "title": ":mailbox_with_no_mail: Your shop has run out of stock",
                    "description": "Your shop inventory is empty now!\nYou need to refill the shop container with items to continue selling items!",
                    "color": 16065893,
                    "author": {
                        "name": "QuickShop-Hikari",
                        "icon_url": ""
                    },
                    "thumbnail": {
                        "url": ""
                    },
                    "image": {
                        "url": ""
                    },
                    "footer": {
                        "text": "QuickShop-Hikari Discord Notification",
                        "icon_url": ""
                    },
                    "fields": [
                        {
                            "name": "Shop",
                            "value": "%%shop.display-name%%",
                            "inline": false
                        }
                    ]
                }
        }
      mod-shop-created: |
        {
          "embed": {
            "title": ":classical_building: A new shop has been created",
            "description": "Player created a new shop on your server!",
            "color": 6061450,
            "author": {
              "name": "QuickShop-Hikari",
              "icon_url": ""
            },
            "thumbnail": {
              "url": ""
            },
            "image": {
              "url": ""
            },
            "footer": {
              "text": "QuickShop-Hikari Discord Notification",
              "icon_url": ""
            },
            "fields": [
              {
                "name": "Shop",
                "value": "%%shop.display-name%%",
                "inline": false
              },
              {
                "name": "Owner",
                "value": "%%shop.owner.name%%",
                "inline": true
              },
              {
                "name": "Item",
                "value": "%%shop.item.name%%",
                "inline": true
              },
              {
                "name": "Amount",
                "value": "%%shop.stacking-amount%%",
                "inline": true
              },
              {
                "name": "Type",
                "value": "%%shop.type%%",
                "inline": true
              }
            ]
          }
        }
      mod-remove-shop: |
        {
            "embed": {
                "title": ":recycle: A shop was removed from this server",
                "description": "A shop was removed from this server.",
                "color": 6061450,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "Reason",
                        "value": "%%delete.reason%%",
                        "inline": false
                    }
                ]
            }
        }
      shop-transfer-to-you: |
        {
            "embed": {
                "title": ":placard: A shop was transfer to you",
                "description": "A shop transfer to you from another player.",
                "color": 16172079,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "From",
                        "value": "%%transfer.from%%",
                        "inline": false
                    }
                ]
            }
        }
      mod-shop-transfer: |
        {
            "embed": {
                "title": ":placard: A shop was transferred",
                "description": "A shop transfer to a player from another player.",
                "color": 6061450,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "From",
                        "value": "%%transfer.from%%",
                        "inline": true
                    },
                    {
                        "name": "To",
                        "value": "%%transfer.to%%",
                        "inline": true
                    }
                ]
            }
        }
      shop-price-changed: |
        {
            "embed": {
                "title": ":money_with_wings: Your shop price was changed",
                "description": "You or your shop staff changed your shop price.",
                "color": 16172079,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "From",
                        "value": "%%change-price.from%%",
                        "inline": true
                    },
                    {
                        "name": "To",
                        "value": "%%change-price.to%%",
                        "inline": true
                    }
                ]
            }
        }
      mod-shop-price-changed: |
        {
            "embed": {
                "title": ":money_with_wings: A shop price has been changed",
                "description": "A shop changed its price settings.",
                "color": 6061450,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": true
                    },
                    {
                        "name": "Owner",
                        "value": "%%shop.owner.name%%",
                        "inline": true
                    },
                    {
                        "name": "",
                        "value": "",
                        "inline": true
                    },
                    {
                        "name": "From",
                        "value": "%%change-price.from%%",
                        "inline": true
                    },
                    {
                        "name": "To",
                        "value": "%%change-price.to%%",
                        "inline": true
                    }
                ]
            }
        }
      shop-permission-changed: |
        {
            "embed": {
                "title": ":closed_lock_with_key: Your shop permission settings has been changed",
                "description": "One of your shop's permission setting has been changed.",
                "color": 15879747,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "Player",
                        "value": "%%change-permission.player%%",
                        "inline": true
                    },
                    {
                        "name": "Assign to group",
                        "value": "%%change-permission.to-group%%",
                        "inline": true
                    },
                    {
                        "name": "",
                        "value": "",
                        "inline": true
                    },
                    {
                        "name": "Permission granted (Inherit from group)",
                        "value": "```\n%%change-permission.perms-list%%\n```",
                        "inline": false
                    }
                ]
            }
        }
    field:
      player: Player
      item: Item
      amount: Amount
      balance: Balance
      balance-after-tax: Balance (after tax)
      account: Your account balance
      taxes: Taxes
      cost: Cost
  discount:
    commands:
      discount:
        description: <yellow>Apply a discount code or manage your discount codes.
    tab-complete:
      discount:
        general:
          code: <code>
        create:
          rate: |
            Command Hint:
            Argument: <rate>
            Description: The actual percentage or money you will earn
            Input `30%` = price * 0.3
            Input `50` = price - 50
          max-usage: |
            Command Hint:
            Argument: [max-usage]
            Description: The times that code can be used
            `-1` for unlimited.
          threshold: |
            Command Hint:
            Argument: [threshold]
            Description: The minimum price that code can be applied
            `-1` for unlimited
          expired: |
            Command Hint
            Argument: [expired]
            Description: The code expired time.
            `-1` for unlimited duration.
            Accept both Zulu time and UNIX timestamp in seconds.
            Zulu Example: 2022-12-17T10:31:37Z
            UNIX Example: 1671273097
    discount-code-already-exists: <red>Sorry, the name of your discount code already in use.
    invalid-discount-code-regex: '<red>The discount code must matches the regex: <yellow>{0}</yellow>'
    invalid-discount-code: <red>This discount code are invalid.
    discount-code-added: <green>Your discount code <yellow>{0}</yellow> has been added into shop <aqua>{1}</aqua>
    discount-code-removed: <green>Your discount code <yellow>{0}</yellow> has been removed from shop <aqua>{1}</aqua>
    invalid-code-type: <red>The code type <yellow>{0}</yellow> are invalid.
    invalid-usage-restriction: <red>The usage restriction <yellow>{0}</yellow> are invalid.
    invalid-threshold-restriction: <red>The threshold restriction <yellow>{0}</yellow> are invalid.
    invalid-effect-scope: <red>The scope <yellow>{0}</yellow> are invalid.
    invalid-expire-time: <red>You can't specify a time in the past.
    invalid-discount-rate: <red>The discount rate <yellow>{0}</yellow> are invalid, it can be a fixed number or percentage.
    discount-code-expired: <red>ああっと!! 割引コード <yellow>{0}</yellow> の有効期限が切れました!
    discount-code-installed: <green>You have installed discount code <gold>{0}</gold>, the discount code will be automatically applied to the available purchase in all the following purchases during this session. To uninstall discount code, execute <aqua>/qs discount uninstall {0}</aqua>.
    discount-code-uninstalled: <green>You have uninstalled your discount code.
    discount-code-query-nothing: <red>You hadn't install a discount code yet!
    discount-code-query: '<green>You are using discount code: <yellow>{0}</yellow>.'
    discount-code-applicable: '<color:#bcef26>Your discount code <yellow><bold>{0}</bold></yellow> are <bold>applicable</bold> in this shop!'
    discount-code-applicable-with-threshold: '<color:#bcef26>Your discount code <yellow><bold>{0}</bold></yellow> are <bold>applicable</bold> in this shop, but only your purchase cost over <yellow>{1}</yellow> in single transaction.'
    discount-code-not-applicable: <red>Your discount code <yellow><bold>{0}</bold></yellow> are <bold>not applicable</bold> in this shop!
    discount-code-reach-the-limit: <red>You have reach the usage limit of discount code <yellow><bold>{0}</bold></yellow>, discount are not applied.
    discount-code-no-permission: <red>You didn't have permission to use any discount code in this shop!
    discount-code-has-been-expired: <red>Your discount has been expired!
    discount-code-config-shop-added: <green>Successfully to add this shop into your discount code allow list.
    discount-code-config-shop-add-failure: <red>This shop already in your discount code allow list.
    discount-code-config-shop-removed: <green>Successfully to remove this shop from your discount code allow list.
    discount-code-config-shop-remove-failure: <red>This shop not exists in your discount code allow list.
    discount-code-config-expire: <green>Successfully changed your code expire time.
    discount-code-config-applied: <green>Config the discount code successfully!
    discount-code-created-successfully: |
      <green>Your discount code <yellow>{0}</yellow> has been successfully created!
      <gold>Scope: <aqua>{1}</aqua></gold>.
      <gold>Share with others: <color:#bcef26>{2}</color:#bcef26></gold>.
      <yellow>For <gray>Specific Shops</gray> scope only: To add shops to your discount code allow list, please looking an quickshop and execute <aqua>{3}</aqua> command.
      <yellow>You can use <aqua>/qs discount config {0}</aqua> to edit your discount code anytime.
    discount-code-under-threshold: <red>The discount not applied to your purchase because the total value under the discount code threshold <yellow>{0}</yellow>.
    percentage-off: '<bold><color:#bcef26>-{0}%</color:#bcef26></bold>'
    fixed-off: '<bold><color:#bcef26>-{0}</color:#bcef26></bold>'
    discount-code-list: '<gold>Listing your discount codes:</gold>'
    discount-code-applied-in-purchase: '<color:#bcef26>Your discount code <yellow>{0}</yellow> has been applied to this purchase and you saved <gold>{1}</gold>!'
    scope:
      this-shop: '{0}'
      your-shops-owned: all your shops (owned)
      your-shops-managed: all your shops (managed)
      server: サーバー全体
    code-type:
      SERVER_ALL_SHOPS: このサーバー上のすべてのショップ
      PLAYER_ALL_SHOPS: コードオーナーが作成したすべてのショップ
      SPECIFIC_SHOPS: 特定のショップ
    discount-code-details: |-
      <gold>Code: <yellow>{0}</yellow>
      <gold>Creator: <yellow>{1}</yellow>
      <gold>Applied to: <yellow>{2}</yellow>
      <gold>Remaining usage: <yellow>{3}</yellow>
      <gold>Expired on: <yellow>{4}</yellow>
      <gold>Threshold: <yellow>{5}</yellow>
      <gold>Discount: <yellow>{6}</yellow>
  list:
    commands:
      list: <yellow>Listing all shops owned by your or specific player
    table-prefix: '<yellow><green>{0}</green>''s shops <gray>(Total {1})</gray>: '
    table-prefix-pageable: '<yellow><green>{0}</green>''s shops <gray>(Page {1}/{2})</gray>: '
    entry: "<hover:show_text:'<yellow>{1}</yellow>\n<gray>{2} {3},{4},{5}</gray>\n<aqua>Price <yellow>{6}</yellow> - <light_purple>x{7}</light_purple> <yellow>{8}</yellow>\n<green>{9}'><yellow>{0}. <aqua>{1}</aqua></hover>"
  shopitemonly:
    message: <red>You can't put non-shop items into shop container, all non-shop items will dropped at your location.
  limited:
    commands:
      limit: <yellow>プレイヤーが期間内に購入できる制限を設定します
    titles:
      title: <green>Purchase Success
      subtitle: <aqua>You can also purchase <gold>{0}</gold> more in this shop
    reach-the-limit: <red>You have reached the limit in this shop. You can also purchase <green>{0}</green> items, but you trying purchase <yellow>{1}</yellow> items.
    success-reset: <green>このショップの制限をリセットしました
    success-remove: <green>Successfully remove the all restriction of this shop
    success-setup: <green>このショップの制限設定は正常に保存されました
    trade-limit-reached-cancel-reason: <red>Reached the restriction of this shop
    remains-limits: '<gold>Your remaining purchase quota in this shop: <yellow>{0}</yellow>'
    remains-limits-before-reset-period-note: '<gold>This shop will reset their purchase quota every: <yellow>{0}</yellow>'
  dynmap:
    markerset-title: QuickShop-Hikari Shops
    marker-name: '{0} @ {2} @ {3} for x{4} @ {5}'
    marker-description: |
      Name: {0}
      Owner: {1}
      Item: {2}
      Price: {3} for x{4} item(s)
      Type: {5}
      Unlimited: {6}
      Location: {7}
  bluemap:
    markerset-title: QuickShop-Hikari Shops
    marker-name: '{0} @ {2} @ {3} for x{4} @ {5}'
    marker-description: |
      Name: {0}
      Owner: {1}
      Item: {2}
      Price: {3} for x{4} item(s)
      Type: {5}
      Unlimited: {6}
      Location: {7}
  chestprotect:
    protection-exists: <red>This region already protected by ChestProtect and you don't have permission to create shops at here.
    shops-exsts: <red>The region that you want to protect contains other player's shops and you don't have permission to access them.
  displaycontrol:
    toggle: |-
      <green>Successfully toggle your QuickShop display showcasing to <aqua>{0}</aqua>.
      <yellow>You may need re-join to take effect.
    toggle-exception: <red>Failed to toggle your display showcase settings due an internal error, please contact server administrator.
    command:
      displaycontrol: <yellow>Toggle your QuickShop display showcasing
  reremake-migrator:
    commands:
      migratefromreremake: QuickShop-ReelemakeのデータをQuickShop-Hikariに移行
    server-not-empty: "<red>No players are allowed to be online on the server during the conversion progress, please enable server whitelist or maintenance mode."
    starting-convert-progress: "<gold>Starting the convert progress, <red>DO NOT SHUTDOWN YOUR SERVER!</red>"
    executing: "<gold>Executing migrate component <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
    completed: "<green>Done! Migrate completed, please restart your server and remove QuickShop-Reremake from plugins directory."
    join_blocking_converting: "<red>[QuickShop-Hikari Migrator] This server is performing a data conversion and you cannot join the server at this time, try again later!"
    join_blocking_finished: "<red>[QuickShop-Hikari Migrator] This server has just completed a data conversion and is waiting for a reboot to apply the changes, try again later!"
    failed: "<red>Migration progress exited with error, please check the server console."
    modules:
      config:
        copy-values: "<yellow>Copying values (total {0} entries)..."
        copying-value: "<gray> - Copying <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        migrate-price-restriction: "<yellow>Migrating price-restriction related settings..."
        migrate-price-restriction-entry: "<gray> - Migrating <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
      shop:
        start-migrate: "<yellow>Migrating shops (total {0} entries)..."
        migrate-entry: "<gray> - Migrating <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        unloading-reremake: "<gold>Unloading Reremake to avoid data overwrite..."
        register-entry: "<gray> - Registering <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        save-entry: "<gray> - Saving <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        saving-shops: "<yellow>Saving <gold>{0}</gold> shops, this may need a while™ (the time based on shops amounts)..."
        conflict: "<gray> - CONFLICT > Detects a conflict between a Reremake store and an existing Hikari store location, taking a predefined behavior: <dark_gray>{0}</dark_gray>"
      translation:
        start-migrate: "<yellow>Migrating translation files...</yellow>"
        copy-values: "<yellow>Copying values in locale <gold>{0}</gold> (total {1} entries)..."
        migrate-entry: "<gray> - Migrating <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        copying-value: "<gray> - Copying <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
      shop-logs:
        start-migrate: "<yellow>Migrating shop logs (details in console), please wait...</yellow>"
        extract-history-files: "<gray>Please wait for us to unzip and append the history logs..."
        filter-history-files: "<gray>Please wait for us to filter the history log..."
        filtered-history-files: "<gray>Filtered {0} lines out of queue."
        import-entry: "<gray> - Migrating <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
compat:
  advancedchests:
    created: <green>You're successfully created a shop on AdvancedChests container!
    permission-denied: <red>Sorry! You don't have the permission to create a shop on a AdvancedChests container!
suite:
  dynmap:
    title: '{0} - {1} - {2}'
    description: |-
      Item: {0}
      Owner: {1}
      Type: {2} {3}
      Price: {4}
      Location: {5} {6}, {7}, {8}
      Space: {9}
      Stock: {10}
  limited:
    command-description: <yellow>プレイヤーが期間内に購入できる制限を設定します
    reach-the-quota-limit: <red>You have reach the purchase limit in this shop ({0}/{1}).
    quota-reset-countdown: <yellow>The quota in this shop will reset in {0}.
    quota-reset-player-successfully: <green>The quota for player {0} in this shop has been reset successfully.
    quota-reset-everybody-successfully: <green>The quota for everyone in this shop has been reset successfully.
    quota-setup: <green>このショップでの購入制限が適用されます!
    quota-remove: <green>The purchase restriction now removed from this shop!
    subtitles:
      title: <green>Purchase Success
      subtitle: <aqua>You can purchase <yellow>{0}</yellow> more in this shop
  list:
    command-description: <yellow>List all shops owned by yourself or another player.
    table-prefix: <yellow>You owned total <aqua>{0}</aqua> shops on this server.
    entry-syntax: <aqua>- <yellow>{0}
    entry-syntax-fallback-name: '<yellow>Item:{0} X:{1}, Y:{2}, Z:{3}, World: {4}'
    hover-text: |-
      <yellow>{0}
      <gray>{1} {2}, {3}, {4}
      <aqua>{5} - <yellow>{6}
      <yellow>{7} items each stack.
  shopitemonly:
    message: <red>You can't put non-shop items into shop container, all non-shop items will dropped at your location.
compatibility:
  elitemobs:
    soulbound-disallowed: You can't trade item which have EliteMobs Soulbound enchantment.
internet-paste-forbidden-privacy-reason: "<red>Failed! According your privacy setting, QuickShop-Hikari are not allowed upload your paste to Internet, turn on DIAGNOSTIC permission in privacy setting in config.yml or use <aqua>/quickshop paste --file</aqua> instead."
no-sign-type-given: "<red>You need give a sign material, the sign materials that available on this server: {0}"
sign-type-invalid: "<red>The type <yellow>{0}</yellow> not a valid sign material."
delete-controlpanel-button-confirm: "<red>Do you really want remove this shop? Click the <bold>[Remove Shop]</bold> button again in {0} seconds to confirm."
cannot-suggest-price: "<red>Sorry, For the time being, no more people are trading the same item as you and there is not enough data to generate a suggested price."
price-suggest: "<green>Based on data from <aqua>{0}</aqua> stores, the highest priced store is priced at <light_purple>{1}</light_purple>, the lowest priced store is priced at <light_purple>{2}</light_purple>, the average price is <light_purple>{3}</light_purple>, and the median price is <light_purple>{4}</light_purple>. <newline><yellow>It's recommended that you set your price around <gold>{5}</gold>.</yellow>"
suggest-wait: "<green>Please wait... Calculating recommend price."
history:
  shop:
    gui-title: "View purchase history"
    header-icon-multiple-shop: "<white>The query result across {0} shops</white>"
    header-icon-description:
      - "<white>Type: <yellow>{0}</yellow></white>"
      - "<white>Owner: <yellow>{1}</yellow></white>"
      - "<white>Item: <yellow>{2}</yellow></white>"
      - "<white>Price: <yellow>{3} <aqua>x{4}</aqua></yellow></white>"
      - "<white>Location: <yellow>{5}</yellow></white>"
    log-icon-title: "<reset><green>Time: {0}</green>"
    log-icon-description:
      - "<white>Purchaser: <yellow>{0}</yellow></white>"
      - "<white>Item: <yellow>{1} <aqua>x{2}</aqua></yellow></white>"
      - "<white>Balance: <yellow>{3}</yellow></white>"
      - "<white>Tax: <yellow>{4}</yellow></white>"
    log-icon-description-with-store-name:
      - "<white>Store: <yellow>{0}</yellow></white>"
      - "<white>Purchaser: <yellow>{1}</yellow></white>"
      - "<white>Item: <yellow>{2} <aqua>x{3}</aqua></yellow></white>"
      - "<white>Balance: <yellow>{4}</yellow></white>"
      - "<white>Tax: <yellow>{5}</yellow></white>"
    query-icon: "<gray>Please wait, querying...</gray>"
    previous-page: "<white><< Previous Page</white>"
    next-page: "<white>Next Page >></white>"
    current-page: "<white>Page {0}</white>"
    summary-icon-title: "<green>Shop Summary"
    recent-purchases: "<white>Recent <aqua>{0}</aqua> purchases: <yellow>{1}</yellow></white>"
    recent-purchase-balance: "<white>Recent <aqua>{0}</aqua> turnover: <yellow>{1}</yellow></white>"
    total-purchases: "<white>Total purchases: <yellow>{0}</yellow></white>"
    total-balances: "<white>Total turnover: <yellow>{0}</yellow></white>"
    total-unique-purchasers: "<white>Total unique purchasers: <yellow>{0}</yellow></white>"
    top-n-valuable-customers-title: "<gold>Top {0} valuable customers</gold>"
    top-n-valuable-customers-entry: "<white>- <yellow>{0}</yellow> <aqua>{1}</aqua></white>"
    no-result: "<white>結果なし</white>"
about:
  text:
    - "<b><aqua>QuickShop <yellow>{0}</yellow></aqua></b>"
    - "<aqua>Version <yellow>>></yellow> <green>{1}</green></aqua>"
    - "<aqua>Release <yellow>>></yellow> <green>{2}</green></aqua>"
    - "<aqua>Developers <yellow>>></yellow> <color:#218bff><hover:show_text:'<yellow>Click to check contributors'><click:open_url:'https://github.com/Ghost-chu/QuickShop-Hikari/graphs/contributors'>[View contributors on GitHub]</click></hover></color></aqua>"
    - "<aqua>Localized members <gold>({4})</gold>: <yellow>>></yellow> <green>{5}</green></aqua>"
    - "  <yellow>+ <color:#43A047><hover:show_text:'<yellow>Click to open Crowdin translate page'><click:open_url:'https://crowdin.com/project/quickshop-hikari'>[Help translate on Crowdin]</click></hover></color></yellow>"
    - "<aqua>寄付キー <yellow>>></yellow> <green>{6}</green></aqua>"
    - "<gold>コミュニティによって支えられています</gold> <red>❤</red>"
  valid-donation-key: "<color:#00AFF1>Bound to <color:#F9C23C>{0}</color></color>"
  invalid-donation-key: "<gray>寄付キーが無効です</gra>"
  kofi-thanks: "<gold>Ko-fi :)でQuickShopをサポートしてくださる方々に感謝します</gold>"
history-command-leave-blank: "<Leave blank for seeing store>"
shop-information-not-shown-due-an-internal-error: "<red>内部エラーが発生しました。間違ったショップ情報が表示される場合があります。サーバ管理者に連絡してください。"
