{"skinsrestorer.prefix_format": "<yellow>[<dark_green>SkinsRestorer</dark_green>] <message>", "skinsrestorer.help_skin": "ცვლის თქვენს სკინს.", "skinsrestorer.help_skins": "ხსის სკინების ინტერფეისს.", "skinsrestorer.help_sr": "ადმინის ბრძანებები SkinsRestorer-ისთვის.", "skinsrestorer.help_skin_help": "აჩვენებს ამ დამხმარე ბრძანებას.", "skinsrestorer.help_skin_set": "ცვლის თქვენს სკინს.", "skinsrestorer.help_skin_set_other": "უყენებს სასურველ მოთამაშეს სკინს.", "skinsrestorer.help_skin_set_url": "ცვლის სკინს URL-ით.", "skinsrestorer.help_skin_clear": "სკინს ანულებს.", "skinsrestorer.help_skin_clear_other": "სასურველი მოთამაშის სკინს ანულება.", "skinsrestorer.help_skin_random": "აყენებს შემთხვევით სკინს.", "skinsrestorer.help_skin_random_other": "უყენებს სასურველ მოთამაშეს შემთხვევით სკინს.", "skinsrestorer.help_skin_search": "მოიძიეთ სასურველი სკინი.", "skinsrestorer.help_skin_edit": "გადააკეთეთ თქვენი სკინი ქსელში.", "skinsrestorer.help_skin_update": "სკინს ანახლებს.", "skinsrestorer.help_skin_update_other": "სასურველი მოთამაშის სკინს ანახლებს.", "skinsrestorer.help_skin_undo": "წინარე სკინს აბრუნებს.", "skinsrestorer.help_skin_undo_other": "სასურველ მოთამაშეს უბრუნებს წინარე სკინს.", "skinsrestorer.help_skin_favourite": "ინახავს სკინს სასურველებში.", "skinsrestorer.help_skin_favourite_other": "სასურველი მოთამაშის სკინს ინახავს სასურველებში.", "skinsrestorer.help_skull": "თავის ქალას გაძლევს.", "skinsrestorer.help_skull_help": "ბრძანებები თავის ქალებისთვის.", "skinsrestorer.help_skull_get": "თავის ქალას გაძლევს.", "skinsrestorer.help_skull_get_other": "მოთამაშისთვის თავის ქალის მიცემა.", "skinsrestorer.help_skull_get_url": "თავის ქალას გაძლევს სკინის URL-იდან.", "skinsrestorer.help_skull_random": "გაძლევს შემთხვევით თავის ქალას.", "skinsrestorer.help_skull_random_other": "მოთამაშისთვის შემთხვევითი თავის ქალის მიცემა.", "skinsrestorer.help_sr_reload": "კონფიგურაციის ფაილის გადატვირთვა.", "skinsrestorer.help_sr_status": "ამოწმებს პლაგინებისთვის საჭირო API სერვისებს.", "skinsrestorer.help_sr_drop": "შლის მოთამაშის სკინს მონაცემთა ბაზიდან.", "skinsrestorer.help_sr_info": "ასახავს ინფორმაციას მოთამაშის სკინზე.", "skinsrestorer.help_sr_apply_skin": "სასურველი მოთამაშისთვის სკინს ხელახლა აყენებს.", "skinsrestorer.help_sr_create_custom": "ქმინს მთელი სერვერისთვის საერთო სკინს.", "skinsrestorer.success_generic": "<dark_green><message>"}