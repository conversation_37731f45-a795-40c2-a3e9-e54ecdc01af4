# Full color code support and some variables
# Keep in mind that variables wont work for some lines, when it will for anothers :)
# Just keep them where there are now and everything will be ok :)
# Some lines can have global variables set. For player who will be effected. In example /heal Zrips then Zrips data will be used
# [serverName] to show server name
# [playerName] to show target player name
# [playerDisplayName] to show target player display name
# [lvl] to show target player level
# [exp] to show target player total exp
# [hp] to show target player health
# [maxHp] to show target player max health
# [hunger] to show target player hunger level
# [gameMode] to show target player gamemode
# [prefix] to show target player prefix if possible
# [suffix] to show target player suffix if possible
# Sender is console or player who performs command. In example Zrips performs /heal Zhax then Zrips data will be used
# [senderName] to show Sender player name
# [senderDisplayName] to show Sender player display name
# [senderLvl] to show Sender player level
# [senderExp] to show Sender player total exp
# [senderHp] to show Sender player health
# [senderMaxHp] to show Sender player max health
# [senderHunger] to show Sender player hunger level
# [senderGameMode] to show Sender player gamemode
# [senderPrefix] to show Sender player prefix if possible
# [senderSuffix] to show Sender player suffix if possible
# Source is player which is being used for extra info. In example Zrips performs /tp Zhax Zrips then Zhax data will be used as its location is being taken for new player location
# [sourceName] to show source player name
# [sourceDisplayName] to show source player display name
# [sourceLvl] to show source player level
# [sourceExp] to show source player total exp
# [sourceHp] to show source player health
# [sourceMaxHp] to show source player max health
# [sourceHunger] to show source player hunger level
# [sourceGameMode] to show source player gamemode
# [sourcePrefix] to show source player prefix if possible
# [sourceSuffix] to show source player suffix if possible
# ***********************************************
# Some lines supports option to send them to custom places, like action bar, title, sub title or even create JSON/clickable messages
# If line starts with !toast! then player will get toast message (advancement popup, only 1.12 and up). Some extra variables can be used to define type and icon. example: !toast! -t:goal -icon:paper Hello world!
# If line starts with !actionbar! then player will get action bar message defined after this variable
# If line starts with !actionbar:[seconds]! then player will get action bar message for defined amount of time
# If line starts with !broadcast! then everyone will receive message. You can add extra !toast! !actionbar! or !title! to send message for everyone to specific place, in example !broadcast!!title!
# If line starts with !customtext:[cTextName]! then custom text will be taken by name provided and shown for player. In case its used after !broadcast! then everyone who is online will get this custom text message
# If line starts with !title! then player will get title message defined after this variable, in addition it can contain !subtitle! which will add subtitle message
# If line starts with !bosbar:[name]-[timer]! then player will get bossbar message defined after this variable, in addition you can define how long this message will be visible. You need to define bossbar name which can be anything you want, but lines with same name will override each other to prevent stacking
# To include clickable messages: <T>Text</T><H>Hover text</H><C>command</C><SC>Suggested text</SC>
# <T> and </T> required, other is optional
# Use /n to break line
# To have more than one JSON message use <Next>
# <C> performs command as a player who clicked
# <CC> performs command from console once
# <CCI> performs command from console every time player clicks text
# <URL> includes url
info:
  # Use !prefix! in any locale line to automatically include this prefix
  prefix: '&e[&aCMI&e] '
  NoPermission: '&cYetkin yok!'
  CantHavePermission: '&cBu yetkiye sahip olamazsın!'
  WrongGroup: '&cBunun için yanlış gruptasın!'
  NoPlayerPermission: '&c[playerName] adlı kullanıcının bu yetkiye izni yok: [permission]'
  Ingame: '&cBunu sadece oyunda kullanabilirsin!'
  NoInformation: '&cHiçbir bilgi bulunamadı!'
  Console: '&6Sunucu'
  FromConsole: '&cBunu sadece konsolda kullanabilirsin!'
  NotOnline: '&cOyuncu çevrimiçi değil!'
  NobodyOnline: '&cÇevrimiçi olan birisi yok!'
  Same: '&cKendi envanterini düzenlemek için açamazsın!'
  cantLoginWithDifCap: '&cFarklı isim, büyük isimle giriş yapılamıyor! Eski isim:
    &e[oldName]&c. Şuanki isim: &e[currentName]'
  Searching: '&eOyuncunun verisi araştırılıyor, lütfen bekle, bunun bitmesi biraz
    zaman alıcak!'
  NoPlayer: '&cBu isimde bir oyuncu bulunamadı!'
  NoCommand: '&cBu isimle bir komut yok!'
  NoCommandWhileSleeping: '&cUyurken komut çalıştıramazsın!'
  cantFindCommand: '&5&7[%1]&5 adlı komut bulunamadı, bunu mu demek istedin: &7[%2]&5?'
  nolocation: '&4Uygun konum bulunamadı'
  PurgeNotEnabled: '&cConfig dosyasında temizleme işlevi etkin değil!'
  FeatureNotEnabled: '&cBu özellik etkin değil!'
  TeamManagementDisabled: '&7DisableTeamManagement True olarak ayarlandığı zaman bu
    özellik sınırlı olacak'
  ModuleNotEnabled: '&cBu modül etkin değil!'
  versionNotSupported: '&cSunucu versiyonu bu özelliği desteklemiyor'
  bungeeNoGo: '&cBu özellik bunge ağ tabanlı sunucularda çalışmayacaktır'
  clickToTeleport: '&eIşınlanmak için tıkla'
  UseMaterial: '&4Lütfen materyal ismi kullanın!'
  IncorrectMaterial: '&4Incorrect material name!'
  UseInteger: '&4Lütfen sayı kullanın!'
  UseBoolean: '&4Lütfen Doğru veya Yanlış komutlarından birini kullanın!'
  NoLessThan: '&4Numara sayısı belirtilen miktardan az olamaz: [amount]!'
  NoMoreThan: '&4Değer belirtilen miktardan fazla olamaz: [amount]'
  NoGameMode: '&cLütfen bunlardan birini kullanın 0/1/2/3 veya Survival/Creative/Adventure/Spectator
    veya s/c/a/sp!'
  NoWorld: '&4Bu isimde dünya bulunamadı!'
  IncorrectLocation: '&4Konum yanlış tanımlandı!'
  NameChange: '&6[playerDisplayName] &eGriş yaptı, aynı zamanda olarak biliniyor:
    &6[namelist]'
  Cooldowns: '&eBu komutu bir daha kullanabilmek için beklemen gereken süre: &6[time]'
  specializedCooldowns: '&eBu komut için hareket halinde bekleme süresi, lütfen bekleyin
    &6[time]'
  specializedRunning: '&eKomut hala çalışıyor, lütfen bekleyin &6[time]'
  CooldownOneTime: '&eBu komut yalnızca bir kez kullanılabilir!'
  WarmUp:
    canceled: '&eHareketinle beraber komut iptal edildi'
    counter: '!actionbar!&6-->&e[time] &6süre bekleyiniz <--'
    DontMove: '!title!&6Hareket etme!!subtitle!&7 &c[time] &7süre bekleyiniz'
    Boss:
      DontMove: '&4 &7[autoTimeLeft] &4kadar süre hareket etme!'
      WaitFor: '&4&7[autoTimeLeft] &4kadar süre bekle!'
  Spawner: '&r[type] Spawner'
  FailedSpawnerMine: '!actionbar!&cSpawner kazısı başarısız. &7[percent]% &cdüşme
    şansı'
  ClickSpawner: '!actionbar!&7[percent]% &eDüşme şansı'
  Elevator:
    created: '&eAsansör işareti oluşturuldu'
  CantPlaceSpawner: '&eSpawnerı başka bir spawnerın yakınına yerleştiremezsiniz (&6[range]&e)'
  ChunksLoading: '&eDünya chunk verisi hala yükleniyor. Lütfen biraz bekle ve tekrar
    dene.'
  ShulkerBox: Shulker Box
  CantUseNonEncrypted: '!actionbar!&cBu öğeyle ilgili komutlar şifrelenmemiş. Onları
    kullanamazsın!'
  CantDecode: '!actionbar!&cMesaj/komut deşifre edemezsin. Bu görev için anahtar dosyası
    yanlış anahtarı içeriyor. Sunucu yönetimini bu konuda bilgilendirin'
  Show: '&eGöstermek'
  Remove: '&cKaldır'
  Back: '&eGeri'
  Forward: '&eİleri'
  Update: '&eYükle'
  Save: '&eKaydet'
  Delete: '&cSil'
  Click: '&eTıkla'
  Preview: '&eGörüntülemek'
  PasteOld: '&eEskiyi yapıştır'
  ClickToPaste: '&eSohbete yapıştırmak için tıklayın'
  CantTeleportWorld: '&eBu dünyaya ışınlanamazsın'
  CantTeleportNoWorld: '&cHedef dünya mevcut değil. Işınlanma iptal edildi'
  CantTeleport: '&eIşınlanamazsın çünkü çok fazla sınırlı eşyan var. İzin verilen
    maksimum öge miktarını görmek için bu satırı kaydırın.'
  ClickToConfirmDelete: '&eClick to confirm removal of &6[name]'
  teleported: '&eIşınlandırıldınız.'
  BlackList: '&e[material] [amount] &6Max: [max]'
  PlayerSpliter: '&e----- &6[playerDisplayName] &e-----'
  Spliter: '&e--------------------------------------------------'
  SpliterValue: '&e------------------ &6[value] &e------------------'
  singleSpliter: '&e-'
  SpliterMiddle: ' &6[value] '
  ListSpliter: ', '
  ProgressBarFill: '&2▏'
  ProgressBarEmpty: '&e▏'
  nothingInHand: '!actionbar!&eElinde eşya tutmalısın'
  nothingInHandLeather: '&eDeri eşyayı elinizde tutmanız gerekiyor'
  nothingToShow: '&eGösterilecek bir şey yok'
  noItem: '&cİtem bulunamadı'
  dontHaveItem: '&cEnvanterinde bu itemden belirtilen miktarda&6[amount]x [itemName]
    &citem yok'
  wrongWorld: '&cBu dünyada bunu yapamazsın'
  wrongPortal: '&cYanlış etki alanındasın'
  differentWorld: '&cFarklı dünyalar'
  HaveItem: '&cEnvanterinde bu itemden belirtilen miktarda&6[amount]x [itemName] &citem
    yok'
  ItemWillBreak: '!actionbar!&e (&6[itemName]&e) adlı item yakında kırılacak! &e[current]&6/&e[max]'
  ArmorWillBreak: '!actionbar!&e[itemName] adlı zırh yakında kırılacak! &e[current]&6/&e[max]'
  cantDoInGamemode: '&eBu oyun modunda bunu yapamazsın'
  cantDoForPlayer: '&e &6[playerDisplayName] adlı oyuncuya bunu yapamazsın'
  cantDoForYourSelf: '&eBunu kendine yapamazsın'
  cantDetermineMobType: '&c&e[type] &cDeğişkeninden mob türü belirlenemiyor'
  cantRename: '!actionbar!&eBu ismi kullanamazsın'
  confirmRedefine: '&eClick to confirm redefining'
  cantEdit: '&eYou can''t edit this'
  wrongName: '&cYanlış isim'
  unknown: unknown
  invalidName: '&cGeçersiz isim'
  alreadyexist: '&4Bu isim alınmış'
  dontexist: '&4Bu isimde bir şey bulunamadı'
  worldDontExist: '&cHedef dünyaya artık erişilemez. Seni oraya ışınlayamam!'
  flyingToHigh: '&cBu kadar yükseğe uçamazsın, maksimum yükseklik: &6[max]&c!'
  specializedItemFail: '&cÖzel item gereksinimini değer göre belirleyemiyorum: &7[value]'
  sunSpeeding: Sleeping [count] of [total] [hour] hour [speed]X speed
  sleepersRequired: '!actionbar!&f[sleeping] &7of &f[required] &7sleeping from required
    for night time speedup'
  sunSpeedingTitle: '&7[hour]'
  skippingNight: '!title!&7Bütün geceleri atlamak'
  sunSpeedingSubTitle: '&f[count]&7/&f[total] &7(&f[speed]X&7)'
  repairConfirm: '&eeşyaları onaylamak içn tıkla &7[items] &eEşya tamir maliyeti:
    &7[cost]'
  bookDate: '&7&f[date] &7 tarihinde yazıldı'
  maintenance: '&7Bakım modu'
  notSet: not set
  mapLimit: '&c30 000 000 blokun ilerisine gidemezsin'
  startedEditingPainting: '&eResmi düzenlemeye başladın. İptal etmek için başla bloka
    tıkla.'
  canceledEditingPainting: '&eBoyama düzenleme modunu iptal ettiniz'
  changedPainting: '!actionbar!&e&6[id] bu ID ile boyama ismi &6[name] &eolmak üzere
    değiştirildi'
  noSpam: '!title!&cSpam yapmayınız!'
  noCmdSpam: '!title!&cKomut spamı yapmayınız!'
  spamConsoleInform: '&cOyuncu (&7[playerName]&c),bu mesajı ile (&7[rules]&c)  adlı
    kurala uymadı:&r [message]'
  lookAtSign: '&eİşarete bak'
  lookAtBlock: '&eBloğa bak'
  lookAtEntity: '&eVarlığa bak'
  noSpace: '&eNot enough free space'
  notOnGround: '&eUçarken bunu uygulayamazsın'
  # This line can have extra variables: [totalUsers] [onlinePlayers]
  FirstJoin: '&e&6[playerDisplayName] &eSunucumuza hoşgeldin!'
  LogoutCustom: ' &6[playerDisplayName] &eSunucudan çıktı'
  LoginCustom: ' &6[playerDisplayName] &eSunucuya katıldı'
  deathlocation: '&eÖldüğün yer: x:&6[x]&e, y:&6[y]&e, z:&6[z]&e in &6[world]'
  book:
    exploit: '&cYou cant create book with more than [amount] pages'
  combat:
    CantUseShulkerBox: '&cBir başka oyuncu ile savaştayken shulker kutusunu kullanamazsın.
      Beklemelisin: [time]'
    CantUseCommand: '!actionbar!&csavaştayken bu komutu kullanmazsın. Bekle: [time]'
    bossBarPvp: '&cSavaş modu [autoTimeLeft]'
    bossBarPve: '&2Savaş modu [autoTimeLeft]'
  bungee:
    Online: '&6Çevrimiçi'
    Offline: '&cÇevrimdışı'
    not: '&cSunucu bungee ağına ait değil'
    noserver: '&cBu isimde sunucu bulunamadı!'
    server: '&eSunucu: &7[name]'
  variables:
    am: '&eAM'
    pm: '&ePM'
    Online: '&6Çevrimiçi'
    Offline: '&cÇevrimdışı'
    TrueColor: '&2'
    FalseColor: '&4'
    'True': '&6Açık'
    'False': '&cKapalı'
    Enabled: '&6Etkin'
    Disabled: '&cDevre dışı'
    survival: '&6Survival'
    creative: '&6Yaratıcı'
    adventure: '&6Macera'
    spectator: '&6İzleyici'
    flying: '&6Uçuş'
    notflying: '&6Uçmuyorsun'
  noSchedule: '&cBu isme göre program bulunamadı'
  totem:
    cooldown: '&eTotem bekleme süresi: [time]'
    warmup: '&eTotem etkisi: [time]'
    cantConsume: '&eTotem kullanma bekleme süresine bağlı reddedildi'
  Inventory:
    FullDrop: '&5Envanterinize tüm öğeler sığmıyor. Yere düşürüldüler'
  InventorySave:
    info: '&8Bilgi: &8[playerDisplayName]'
    saved: '&e[time] &ebu id ile envanter kaydedildi: &e[id]'
    NoSavedInv: '&eBu oyuncunun kaydedilmiş envanteri yok'
    NoEntries: '&4Dosya mevcut, Ama envanterler bulunamadı!'
    CantFind: '&eBu id ile envanter bulunamadı'
    TopLine: '&e----------- &6[playerDisplayName] Kayıtlı envanter &e-----------'
    List: '&eid: &6[id]&e. &6[time]'
    KillerSymbol: '&c ☠'
    Click: '&eKayıtlı envanteri ([id]) kontrol etmek için tıkla'
    IdDontExist: '&4Bu kayıt IDsi mevcut değil!'
    Deleted: '&eKayıtlı envanter başarılı şekilde silindi!'
    Restored: '&e[targetname] adlı kullanıcının envanterini &egeri yükledin.&e[sourcename]'
    GotRestored: '&eEnvanterin &e[sourcename] &etarafından geri yüklendi. &eEnvanter
      kaydedildi&e[time]'
    LoadForSelf: '&eBu envanteri kendiniz yükleyiniz'
    LoadForOwner: '&eSahibi için bu envanteri yükleyiniz'
    NextInventory: '&eSonraki envanter'
    PreviousInventory: '&eÖnceki envanter'
    Editable: '&eDüzenleme modu etkin'
    NonEditable: '&eDüzenleme modu devre dışı'
  TimeNotRecorded: '&e-Kayıt yok-'
  years: '&e[years] &6yıl '
  oneYear: '&e[years] &6yıl '
  day: '&e[days] &6gün '
  oneDay: '&e[days] &6gün '
  hour: '&e[hours] &6saat '
  oneHour: '&e[hours] &6saat '
  min: '&e[mins] &6dakika '
  sec: '&e[secs] &6saniye '
  vanishSymbolOn: '&8[&7H&8]&r'
  vanishSymbolOff: ''
  afkSymbolOn: '&8[&7Afk&8]&r'
  afkSymbolOff: ''
  nextPageConsole: '&fSonraki sayfa için &5[command]'
  prevPage: '&2----<< &6Prev '
  prevPageGui: '&6Önceki sayfa'
  prevPageClean: '&6Önceki '
  prevPageOff: '&2----<< &7Önceki '
  prevPageHover: '&7<<<'
  firstPageHover: '&7|<'
  nextPage: '&6 Sonraki &2>>----'
  nextPageGui: '&6Sonraki sayfa'
  nextPageClean: '&6 Sonraki'
  nextPageOff: '&7 Sonraki &2>>----'
  nextPageHover: '&7>>>'
  lastPageHover: '&7>|'
  pageCount: '&2[current]&7/&2[total]'
  pageCountHover: '&e[totalEntries] sayfa'
  skullOwner: '!actionbar!&7Owner:&r [playerName]'
  beeinfo: '!actionbar!&7Bal seviyesi: &e[level]&7/&e[maxlevel] &7İçerideki arılar:
    &e[count]&7/&e[maxcount]'
  circle: '&3Daire'
  square: '&5Kare'
  clear: '&7Clear'
  protectedArea: '&cKorumalı alan. Bunu burada yapamam.'
  valueToLong: '&eDeğer çok yüksek. Maksimum: [max]'
  valueToShort: '&eDeğer çok düşük. Minimum: [min]'
  pvp:
    noGodDamage: '!actionbar!&cÖlümsüzken oyunculara zarar veremezsin'
  InvEmpty:
    armor: '&eZırh slotlarının boş olması lazım!'
    hand: '&eElin boş olmak zorunda!'
    maininv: '&eAna envanterişn boş olmak zorunda!'
    maininvslots: '&eAna envanterinde en az&6[count] &eboş slot olmalıdır!'
    inv: '&eEnvantein boş olmalı!'
    offhand: '&eHazırlık boş olmalı!'
    quickbar: '&equick bar boş olmalı!'
    quickbarslots: '&eQuick barında en az&6[count] &eboş slot olmalıdır'
    subinv: '&eAlt envanteriniz boş olmalıdır!'
    subinvslots: '&eAlt envanterinizde en az&6[count] &eboş slot olmalıdır!'
  pickIcon: '&8İkon seç'
  DamageCause:
    block_explosion: Explosion
    contact: Block Damage
    cramming: cramming
    custom: Unknown
    dragon_breath: Dragon breath
    drowning: Drowning
    dryout: dryout
    entity_attack: Entity attack
    entity_explosion: Explosion
    entity_sweep_attack: entity sweep attack
    fall: Fall
    falling_block: Falling block
    fire: Fire
    fire_tick: Fire
    fly_into_wall: Fly into wall
    hot_floor: Magma block
    lava: Lava
    lightning: Lightning
    magic: Magic
    melting: Melting
    poison: Poison
    projectile: Projectile
    starvation: Starvation
    suffocation: Suffocation
    suicide: Suicide
    thorns: Thorns
    void: Void
    wither: Wither
  Biomes:
    BADLANDS: Badlands
    BADLANDS_PLATEAU: Badlands plateau
    BAMBOO_JUNGLE: Bamboo jungle
    BAMBOO_JUNGLE_HILLS: Bamboo jungle hills
    BASALT_DELTAS: Basalt deltas
    BEACH: Beach
    BIRCH_FOREST: Birch forest
    BIRCH_FOREST_HILLS: Birch forest hills
    COLD_OCEAN: Cold ocean
    CRIMSON_FOREST: Crimson forest
    DARK_FOREST: Dark forest
    DARK_FOREST_HILLS: Dark forest hills
    DEEP_COLD_OCEAN: Deep cold ocean
    DEEP_FROZEN_OCEAN: Deep frozen ocean
    DEEP_LUKEWARM_OCEAN: Deep lukewarm ocean
    DEEP_OCEAN: Deep ocean
    DEEP_WARM_OCEAN: Deep warm ocean
    DESERT: Desert
    DESERT_HILLS: Desert hills
    DESERT_LAKES: Desert lakes
    END_BARRENS: End barrens
    END_HIGHLANDS: End highlands
    END_MIDLANDS: End midlands
    ERODED_BADLANDS: Eroded badlands
    FLOWER_FOREST: Flower forest
    FOREST: Forest
    FROZEN_OCEAN: Frozen ocean
    FROZEN_RIVER: Frozen river
    GIANT_SPRUCE_TAIGA: Giant spruce taiga
    GIANT_SPRUCE_TAIGA_HILLS: Giant spruce taiga hills
    GIANT_TREE_TAIGA: Giant tree taiga
    GIANT_TREE_TAIGA_HILLS: Giant tree taiga hills
    GRAVELLY_MOUNTAINS: Gravelly mountains
    ICE_SPIKES: Ice spikes
    JUNGLE: Jungle
    JUNGLE_EDGE: Jungle edge
    JUNGLE_HILLS: Jungle hills
    LUKEWARM_OCEAN: Lukewarm ocean
    MODIFIED_BADLANDS_PLATEAU: Modified badlands plateau
    MODIFIED_GRAVELLY_MOUNTAINS: Modified gravelly mountains
    MODIFIED_JUNGLE: Modified jungle
    MODIFIED_JUNGLE_EDGE: Modified jungle edge
    MODIFIED_WOODED_BADLANDS_PLATEAU: Modified wooded badlands plateau
    MOUNTAINS: Mountains
    MOUNTAIN_EDGE: Mountain edge
    MUSHROOM_FIELDS: Mushroom fields
    MUSHROOM_FIELD_SHORE: Mushroom field shore
    NETHER_WASTES: Nether wastes
    OCEAN: Ocean
    PLAINS: Plains
    RIVER: River
    SAVANNA: Savanna
    SAVANNA_PLATEAU: Savanna plateau
    SHATTERED_SAVANNA: Shattered savanna
    SHATTERED_SAVANNA_PLATEAU: Shattered savanna plateau
    SMALL_END_ISLANDS: Small end islands
    SNOWY_BEACH: Snowy beach
    SNOWY_MOUNTAINS: Snowy mountains
    SNOWY_TAIGA: Snowy taiga
    SNOWY_TAIGA_HILLS: Snowy taiga hills
    SNOWY_TAIGA_MOUNTAINS: Snowy taiga mountains
    SNOWY_TUNDRA: Snowy tundra
    SOUL_SAND_VALLEY: Soul sand valley
    STONE_SHORE: Stone shore
    SUNFLOWER_PLAINS: Sunflower plains
    SWAMP: Swamp
    SWAMP_HILLS: Swamp hills
    TAIGA: Taiga
    TAIGA_HILLS: Taiga hills
    TAIGA_MOUNTAINS: Taiga mountains
    TALL_BIRCH_FOREST: Tall birch forest
    TALL_BIRCH_HILLS: Tall birch hills
    THE_END: The end
    THE_VOID: The void
    WARM_OCEAN: Warm ocean
    WARPED_FOREST: Warped forest
    WOODED_BADLANDS_PLATEAU: Wooded badlands plateau
    WOODED_HILLS: Wooded hills
    WOODED_MOUNTAINS: Wooded mountains
  EntityType:
    area_effect_cloud: Area effect cloud
    armor_stand: Armor stand
    arrow: Arrow
    bat: Bat
    bee: Bee
    blaze: Blaze
    boat: Boat
    cat: Cat
    cave_spider: Cave spider
    chicken: Chicken
    cod: Cod
    cow: Cow
    creeper: Creeper
    dolphin: Dolphin
    donkey: Donkey
    dragon_fireball: Dragon fireball
    dropped_item: Dropped item
    drowned: Drowned
    egg: Egg
    elder_guardian: Elder guardian
    enderman: Enderman
    endermite: Endermite
    ender_crystal: Ender crystal
    ender_dragon: Ender dragon
    ender_pearl: Ender pearl
    ender_signal: Ender signal
    evoker: Evoker
    evoker_fangs: Evoker fangs
    experience_orb: Experience orb
    falling_block: Falling block
    fireball: Fireball
    firework: Firework
    fishing_hook: Fishing hook
    fox: Fox
    ghast: Ghast
    giant: Giant
    guardian: Guardian
    hoglin: Hoglin
    horse: Horse
    husk: Husk
    illusioner: Illusioner
    iron_golem: Iron golem
    item_frame: Item frame
    leash_hitch: Leash hitch
    lightning: Lightning
    llama: Llama
    llama_spit: Llama spit
    magma_cube: Magma cube
    minecart: Minecart
    minecart_chest: Minecart chest
    minecart_command: Minecart command
    minecart_furnace: Minecart furnace
    minecart_hopper: Minecart hopper
    minecart_mob_spawner: Minecart mob spawner
    minecart_tnt: Minecart tnt
    mule: Mule
    mushroom_cow: Mushroom cow
    ocelot: Ocelot
    painting: Painting
    panda: Panda
    parrot: Parrot
    phantom: Phantom
    pig: Pig
    piglin: Piglin
    piglin_brute: Piglin brute
    pillager: Pillager
    player: Player
    polar_bear: Polar bear
    primed_tnt: Primed tnt
    pufferfish: Pufferfish
    rabbit: Rabbit
    ravager: Ravager
    salmon: Salmon
    sheep: Sheep
    shulker: Shulker
    shulker_bullet: Shulker bullet
    silverfish: Silverfish
    skeleton: Skeleton
    skeleton_horse: Skeleton horse
    slime: Slime
    small_fireball: Small fireball
    snowball: Snowball
    snowman: Snowman
    spectral_arrow: Spectral arrow
    spider: Spider
    splash_potion: Splash potion
    squid: Squid
    stray: Stray
    strider: Strider
    thrown_exp_bottle: Thrown exp bottle
    trader_llama: Trader llama
    trident: Trident
    tropical_fish: Tropical fish
    turtle: Turtle
    unknown: Unknown
    vex: Vex
    villager: Villager
    vindicator: Vindicator
    wandering_trader: Wandering trader
    witch: Witch
    wither: Wither
    wither_skeleton: Wither skeleton
    wither_skull: Wither skull
    wolf: Wolf
    zoglin: Zoglin
    zombie: Zombie
    zombie_horse: Zombie horse
    zombie_villager: Zombie villager
    zombified_piglin: Zombified piglin
  EnchantAliases:
    protection_fire:
    - FireProtection
    damage_all:
    - Sharpness
    arrow_fire:
    - Flame
    soul_speed:
    - SOULSPEED
    water_worker:
    - AquaAffinity
    arrow_knockback:
    - Punch
    loyalty:
    - Loyalty
    depth_strider:
    - DepthStrider
    vanishing_curse:
    - VanishingCurse
    durability:
    - Unbreaking
    knockback:
    - Knockback
    luck:
    - Luck
    binding_curse:
    - BindingCurse
    loot_bonus_blocks:
    - Fortune
    protection_environmental:
    - Protection
    dig_speed:
    - Efficiency
    mending:
    - Mending
    lure:
    - Lure
    loot_bonus_mobs:
    - Looting
    piercing:
    - Piercing
    damage_undead:
    - Smite
    multishot:
    - Multishot
    fire_aspect:
    - FireAspect
    channeling:
    - Channeling
    sweeping_edge:
    - SweepingEdge
    thorns:
    - Thorns
    damage_arthropods:
    - BaneOfArthropods
    oxygen:
    - Respiration
    riptide:
    - Riptide
    silk_touch:
    - SilkTouch
    quick_charge:
    - QUICKCHARGE
    protection_projectile:
    - ProjectileProtection
    impaling:
    - Impaling
    protection_fall:
    - FallProtection
    - FeatherFalling
    arrow_damage:
    - Power
    arrow_infinite:
    - Infinity
  PotionEffectAliases:
    speed:
    - Speed
    slow:
    - Slow
    fast_digging:
    - Fast digging
    slow_digging:
    - Slow digging
    increase_damage:
    - Increase damage
    heal:
    - Heal
    harm:
    - Harm
    jump:
    - Jump
    confusion:
    - Confusion
    regeneration:
    - Regeneration
    damage_resistance:
    - Damage resistance
    fire_resistance:
    - Fire resistance
    water_breathing:
    - Water breathing
    invisibility:
    - Invisibility
    blindness:
    - Blindness
    night_vision:
    - Night vision
    hunger:
    - Hunger
    weakness:
    - Weakness
    poison:
    - Poison
    wither:
    - Wither
    health_boost:
    - Health boost
    absorption:
    - Absorption
    saturation:
    - Saturation
    glowing:
    - Glowing
    levitation:
    - Levitation
    luck:
    - Luck
    unluck:
    - Unluck
    slow_falling:
    - Slow falling
    conduit_power:
    - Conduit power
    dolphins_grace:
    - Dolphins grace
    bad_omen:
    - Bad omen
    hero_of_the_village:
    - Hero of the village
direction:
  n: North
  ne: North East
  e: East
  se: South East
  s: South
  sw: South West
  w: West
  nw: North West
modify:
  middlemouse: '&2Düzenlemek için farenin orta tuşuna tıklayın'
  newItem: '&7Buraya yeni eşya yerleştirin'
  newLine: '&2<NewLine>'
  newLineHover: '&2Yeni hat ekle'
  newPage: '&2<NewPage>'
  newPageHover: '&2Yeni sayfa oluştur'
  removePage: '&c<RemovePage>'
  removePageHover: '&cSayfayı kaldır'
  deleteSymbol: ' &c[X]'
  deleteSymbolHover: '&cSil &e[text]'
  extraEditSymbol: ' &6!'
  addSymbol: '&2[+]'
  addSymbolHover: '&2Yeni ekle'
  cancelSymbol: ' &7&l[X]'
  cancelSymbolHover: '&aİptal'
  acceptSymbol: ' &2&l[✔]'
  acceptSymbolHover: '&aKabul'
  denySymbol: ' &4&l[X]'
  denySymbolHover: '&cReddedildi'
  enabledSymbol: '&2[+]'
  disabledSymbol: '&c[-]'
  enabled: '&2Etkin'
  disabled: '&cDevre dışı'
  running: '&2İşleniyor'
  paused: '&cDurduruldu'
  editSymbol: '&e✎'
  editSymbolHover: '&eDüzenle &6[text]'
  editLineColor: '&f'
  listUpSymbol: '&6⇑'
  listUpSymbolHover: '&eYukarı'
  listDownSymbol: '&6⇓'
  listDownSymbolHover: '&eAşağı'
  listNumbering: '&e[number]. '
  listAlign: '&80'
  ChangeHover: '&eDeğiştirmek için tıkla'
  ChangeCommands: '&eCommands'
  enabledColor: '&6'
  disabledColor: '&7'
  commandTitle: ' &e--- &6[name] &e---'
  commandList: ' &e[command]  '
  emptyLine: '&7[Empty line]'
  commandEdit: '&eListeyi düzenle'
  lineAddInfo: '&eYeni hat ekle. %6İptal etmek için iptal yazın'
  commandAddInfo: '&eYeni komut ekle. &6İptal etmek için cancel yazın'
  commandAddInformationHover: "&e[playerName] Oyuncu adını almak için kullanılabilir\
    \ \n&eTo Komutlarda gecikme dahil: \n&edelay! 5 \n&eÖzel komutlar desteklenir.\
    \ Daha fazla bilgi \n&ehttps://www.zrips.net/cmi/commands/specialized/"
  commandEditInfo: '&eEski metni yapıştırmak için tıklayın. &6İptal etmek için cancel
    yazın'
  listLimit: '&eList can''t be bigger than &6[amount] &eentries'
  commandEditInfoHover: '&eEski metni yapıştırmak için tıkla'
warp:
  list: '&e[pos]. &6[warpName] &f- &7[worldName] ([x]:[y]:[z])'
teleportation:
  relocation: '!actionbar!&4Işınlanma konumunuz engellendi. Güvenli bir lokasyona
    ışınlandın.'
afk:
  'on': '&6AFK'
  'off': '&7Oynuyor'
  left: '&6[playerDisplayName] &eartık AFK değil'
  MayNotRespond: '&eOyuncu AFK ve yanıt vermeyebilir'
  MayNotRespondStaff: '&eYetkili üyesi AFK ve yanıt vermeyebilir. Bizim ile discorddan
    iletişime geçin'
BossBar:
  hpBar: '&f[victim] &e[current]&f/&e[max] &f(&c-[damage]&f)'
Potion:
  Effects: '&8İksir etkileri'
  List: '&e[PotionName] [PotionAmplifier] &eSüre: &e[LeftDuration] &esaniye'
  NoPotions: '&eNone'
Information:
  Title: '&8Oyuncuların bilgileri'
  Health: '&eKalp: &6[Health]/[maxHealth]'
  Hunger: '&eAçlık: &6[Hunger]'
  Saturation: '&eDoyma: &6[Saturation]'
  Exp: '&eTecrübe: &6[Exp]'
  NotEnoughExp: '&e tecrübe yeterli değil: &6[Exp]'
  NotEnoughExpNeed: '&e tecrübe yeterli değil: &6[Exp]/[need]'
  tooMuchExp: '&eÇok fazla xp: &6[Exp]/[need]'
  NotEnoughVotes: '&eoy yeterli değil: &6[votes]'
  TooMuchVotes: '&eÇok fazla oy: &6[votes]'
  BadGameMode: '&cŞuanki oyun modunda bunu yapamazsın'
  BadArea: '&cBu eylemi bu bölgede yapamazsın'
  GameMode: '&eOyun modu: &6[GameMode]'
  GodMode: '&eGodMode: &6[GodMode]'
  Flying: '&eFlying: &6[Flying]'
  CanFly: '&eCan Fly: &6[CanFly]'
  Uuid: '&6[uuid]'
  ip: '&eIp address: &6[address]'
  FirstConnection: '&eİlk bağlantı: &6[time]'
  Lastseen: '&eSon görülme: &6[time]'
  Onlinesince: '&eZamandan beri çevrimiçi: &6[time]'
  Money: '&ePara: &6[money]'
  Group: '&eGrup: &6[group]'
econ:
  disabled: '&cEkonomi desteği devre dışı bırakıldığında bu komutu kullanamazsınız'
  noMoney: '&cParanız yok'
  charged: '!actionbar!&fYenilendi: &6[amount]'
  notEnoughMoney: '&cYeterli miktarda paran yok. Gerekli olan: (&6[amount]&c)'
  tooMuchMoney: '&cÇok fazla paran var'
  commandCost: '&7Bu komutun bedeli: &6[cost] &7komutu tekrarla veya tıklayarak onayla'
Elytra:
  Speed: '&eHız: &6[speed]&ekm/h'
  SpeedBoost: ' &a+ '
  SpeedSuperBoost: ' &2+ '
  CanUse: '&cYetki olmadan elytrayı kullanamazsın!'
  CantGlide: '&cCan''t use elytra here!'
  Charging: '&eYenileniliyor &f[percentage]&e%'
Selection:
  SelectPoints: '&cSeçme aleti ile 2 nokta seç! AKA: &6[tool]'
  PrimaryPoint: '&6İlk seçim noktası yerleştirildi [point]'
  SecondaryPoint: '&6İkinci seçim noktası yerleştirildi [point]'
  CoordsTop: '&eX:&6[x] &eY:&6[y] &eZ:&6[z]'
  CoordsBottom: '&eX:&6[x] &eY:&6[y] &eZ:&6[z]'
  Area: '&7[world] &f(&6[x1]:[y1]:[z1] &e[x2]:[y2]:[z2]&f)'
NetherPortal:
  ToHigh: '&cPortal çok büyük, maksimum yükseklik: &6[max]&c!'
  ToWide: '&cPortal çok geniş, maksimum genişlik &6[max]&c!'
  Creation: '!actionbar!&7[height]x[width] Boyutunda cehennem portalı oluşturuldu!'
  Disabled: '&cPortal oluşumu devre dışı bırakıldı!'
Location:
  Title: '&8Oyuncu konumu'
  Killer: '&eKatil: &6[killer]'
  OneLiner: '&eKonum: &6[location]'
  DeathReason: '&eÖlüm sebebi: &6[reason]'
  Full: '&7[world] &f[x]&7:&f[y]&7:&f[z]'
  World: '&eDünya: &6[world]'
  X: '&eX: &6[x]'
  Y: '&eY: &6[y]'
  Z: '&eZ: &6[z]'
  Pitch: '&eYer: &6[pitch]'
  Yaw: '&eYaw: &6[yaw]'
Locations: '&7Locations: '
Ender:
  Title: '&7Ender sandığı açmak'
Chat:
  localPrefix: ''
  shoutPrefix: '&c[S]&r'
  LocalNoOne: '!actionbar!&cNobody hear you, write ! before message for global chat'
  shoutDeduction: '!actionbar!&cbağırmak için &e[amount] &cçıkarıldı'
  # Use \n to add new line
  publicHover: '&eGönderilme süresi: &6%server_time_hh:mm:ss%'
  privateHover: '&eGönderilme süresi: &6%server_time_hh:mm:ss%'
  staffHover: '&eGönderilme süresi: &6%server_time_hh:mm:ss%'
  helpopHover: '&eGönderilme süresi: &6%server_time_hh:mm:ss%'
  link: '&l&4[&7LINK&4]'
  item: '&7[%cmi_iteminhand_displayname%[amount]&7]'
  itemAmount: ' x[amount]'
  itemEmpty: '&7[Mighty fist]'
