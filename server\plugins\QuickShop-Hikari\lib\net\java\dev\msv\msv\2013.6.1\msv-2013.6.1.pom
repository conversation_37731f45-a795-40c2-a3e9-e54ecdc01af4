<?xml version="1.0"?>
<!--

    Copyright (c) 2001-2013 Oracle and/or its affiliates. All rights reserved.

    Redistribution and use in source and binary forms, with or without
    modification, are permitted provided that the following conditions
    are met:

      - Redistributions of source code must retain the above copyright
        notice, this list of conditions and the following disclaimer.

      - Redistributions in binary form must reproduce the above copyright
        notice, this list of conditions and the following disclaimer in the
        documentation and/or other materials provided with the distribution.

      - Neither the name of Oracle nor the names of its
        contributors may be used to endorse or promote products derived
        from this software without specific prior written permission.

    THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS
    IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
    THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
    PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE COPYRIGHT OWNER OR
    CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
    EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
    PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
    PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
    LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
    NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
    SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>net.java</groupId>
        <artifactId>jvnet-parent</artifactId>
        <version>3</version>
    </parent>
    
    <groupId>net.java.dev.msv</groupId>
    <artifactId>msv</artifactId>
    <version>2013.6.1</version>
    <packaging>pom</packaging>
    
    <name>msv</name>
    <description>multi schema validator</description>
    
    <url>http://msv.java.net</url>

    <organization>
        <name>Oracle Corporation</name>
        <url>http://www.oracle.com/</url>
    </organization>

    <developers>
        <developer>
            <name>Martin Grebac</name>
            <email><EMAIL></email>
            <organization>Oracle Corporation</organization>
        </developer>
        <developer>
            <name>Iaroslav Savytskyi</name>
            <email><EMAIL></email>
            <organization>Oracle Corporation</organization>
        </developer>
        <developer>
            <id>kohsuke</id>
            <name>Kohsuke Kawaguchi</name>
            <email><EMAIL></email>
        </developer>
    </developers>

    <licenses>
        <license>
            <name>BSD</name>
            <url>http://msv.java.net/License.txt</url>
            <distribution>repo</distribution>
        </license>
    </licenses>
  
    <modules>
        <module>xsdlib</module>
        <module>testharness</module>
        <module>msv</module>
        <module>generator</module>
        <module>rngconverter</module>
    </modules>
  
    <scm>
        <developerConnection>scm:svn:https://svn.java.net/svn/msv~svn/tags/msv-2013.6.1</developerConnection>
        <connection>scm:svn:https://svn.java.net/svn/msv~svn/tags/msv-2013.6.1</connection>
        <url>http://java.net/projects/msv/sources/svn/show/tags/msv-2013.6.1</url>
    </scm>

    <prerequisites>
        <maven>3.0</maven>
    </prerequisites>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>2.4</version>
                    <configuration>
                        <source>1.5</source>
                        <target>1.5</target>
                        <compilerArgument>-XDignore.symbol.file</compilerArgument>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>cobertura-maven-plugin</artifactId>
                    <version>2.5.1</version>
                    <configuration>
                        <formats>
                            <format>xml</format>
                        </formats>
                        <check>
                            <totalLineRate>45</totalLineRate>
                            <packageLineRate>45</packageLineRate>
                            <haltOnFailure>true</haltOnFailure>
                        </check>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.glassfish.copyright</groupId>
                    <artifactId>glassfish-copyright-maven-plugin</artifactId>
                    <version>1.29</version>
                    <configuration>
                        <templateFile>copyright.txt</templateFile>
                        <excludeFile>copyright-exclude</excludeFile>
                        <!-- skip files not under SCM-->
                        <scmOnly>true</scmOnly>
                        <!-- turn off warnings -->
                        <warn>true</warn>
                        <!-- for use with repair -->
                        <update>false</update>
                        <!-- check that year is correct -->
                        <ignoreYear>false</ignoreYear>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>findbugs-maven-plugin</artifactId>
                    <version>2.5.2</version>
                    <configuration>
                        <skip>${findbugs.skip}</skip>
                        <threshold>${findbugs.threshold}</threshold>
                        <findbugsXmlWithMessages>true</findbugsXmlWithMessages>
                        <excludeFilterFile>
                            exclude-common.xml,${findbugs.exclude}
                        </excludeFilterFile>
                        <fork>true</fork>
                        <jvmArgs>-Xms64m -Xmx256m</jvmArgs>    
                    </configuration>
                    <dependencies>
                        <dependency>
                            <groupId>org.glassfish.findbugs</groupId>
                            <artifactId>findbugs</artifactId>
                            <version>1.0</version>
                        </dependency>
                    </dependencies>
                </plugin>
                <plugin>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>2.4</version>
                </plugin>  
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-deploy-plugin</artifactId>
                    <version>2.7</version>
                    <configuration>
                        <retryFailedDeploymentCount>10</retryFailedDeploymentCount>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.felix</groupId>
                    <artifactId>maven-bundle-plugin</artifactId>
                    <version>2.0.0</version>
                    <executions>
                        <execution>
                            <id>bundle-manifest</id>
                            <phase>process-classes</phase>
                            <goals>
                                <goal>manifest</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>2.2.1</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar-no-fork</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <version>2.9</version>
                <executions>
                    <execution>
                        <id>attach-javadocs</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>        
            <plugin>
                <artifactId>maven-enforcer-plugin</artifactId>
                <version>1.0.1</version>
                <executions>
                    <execution>
                        <id>enforce-versions</id>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <rules>
                        <requireJavaVersion>
                            <version>[1.6,)</version>
                        </requireJavaVersion>
                        <requireMavenVersion>
                            <version>[3.0.3,)</version>
                        </requireMavenVersion>
                        <DependencyConvergence />
                    </rules>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>buildnumber-maven-plugin</artifactId>
                <version>1.1</version>
                <configuration>
                    <getRevisionOnlyOnce>true</getRevisionOnlyOnce>
                    <timestampFormat>{0,date,yyyy-MM-dd'T'HH:mm:ssZ}</timestampFormat>
                    <providerImplementations>
                        <svn>javasvn</svn>
                    </providerImplementations>
                    <getRevisionOnlyOnce>true</getRevisionOnlyOnce>
                    <revisionOnScmFailure>false</revisionOnScmFailure>
                </configuration>
                <executions>
                    <execution>
                        <phase>validate</phase>
                        <goals>
                            <goal>create</goal>
                        </goals>
                    </execution>
                </executions>
                <dependencies>
                    <dependency>
                        <groupId>org.tmatesoft.svnkit</groupId>
                        <artifactId>svnkit</artifactId>
                        <version>1.7.4-v1</version>
                    </dependency>
                </dependencies>
            </plugin>
            <plugin>
                <groupId>org.apache.felix</groupId>
                <artifactId>maven-bundle-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
  
    <dependencyManagement>
        <dependencies>

            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>4.4</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>xerces</groupId>
                <artifactId>xercesImpl</artifactId>
                <version>2.9.1</version>
            </dependency>

            <dependency>
                <groupId>com.sun.xml.bind.jaxb</groupId>
                <artifactId>isorelax</artifactId>
                <version>20090621</version>
            </dependency>
            
            <dependency>
                <groupId>relaxngDatatype</groupId>
                <artifactId>relaxngDatatype</artifactId>
                <version>20020414</version>
            </dependency>

            <dependency>
                <groupId>org.apache.ant</groupId>
                <artifactId>ant</artifactId>
                <version>1.7.1</version>
            </dependency>

            <dependency>
                <groupId>jdom</groupId>
                <artifactId>jdom</artifactId>
                <version>1.0</version>
                <scope>test</scope>
            </dependency>

        </dependencies>
    </dependencyManagement>
  
    <dependencies>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <properties>
        <findbugs.skip>false</findbugs.skip>
        <findbugs.threshold>Low</findbugs.threshold>
        <findbugs.exclude>exclude.xml</findbugs.exclude>
    </properties>

    <distributionManagement>
        <snapshotRepository>
            <id>sonatype-nexus-snapshots</id>
            <name>Sonatype Nexus Snapshots</name>
            <url>https://oss.sonatype.org/content/repositories/snapshots/</url>
        </snapshotRepository>
        <repository>
            <id>sonatype-nexus-staging</id>
            <name>Nexus Release Repository</name>
            <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
        </repository>
    </distributionManagement>
    
</project>
