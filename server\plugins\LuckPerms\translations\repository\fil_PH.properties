luckperms.logs.actionlog-prefix=Susi\: 
luckperms.logs.verbose-prefix=VB
luckperms.logs.export-prefix=I-EXPORT
luckperms.commandsystem.available-commands=G<PERSON>tin ang {0} upang tingnan ang mga pwedeng utos
luckperms.commandsystem.command-not-recognised=Hindi ma-recognize ang utos
luckperms.commandsystem.no-permission=Wala kang pahintulot na gamitin ang utos na ito\!
luckperms.commandsystem.no-permission-subcommands=Wala kang pahintulot na gamitin ang kahit anong mga sub na utos
luckperms.commandsystem.already-executing-command=Susi\: {0} ang kasalukuyang ginagawang utos
luckperms.commandsystem.usage.sub-commands-header=Mga Sub na Utos
luckperms.commandsystem.usage.usage-header=Susi\: {0}
luckperms.commandsystem.usage.arguments-header=Mga Argumento
luckperms.first-time.no-permissions-setup=Susi\: Wala pang itinakdang mga pahintulot
luckperms.first-time.use-console-to-give-access=<PERSON>go ka makapagamit ng anumang utos sa LuckPerms sa laro, kailangan mong gamitin ang konsol para bigyan ang iyong sarili ng pahintulot
luckperms.first-time.console-command-prompt=Buksan ang iyong konsol at i-takbo ang
luckperms.first-time.next-step=Pagkatapos mong gawin ito, maaari ka nang mag-umpisa na magtakda ng iyong mga assignment sa pahintulot at mga grupo
luckperms.first-time.wiki-prompt=Hindi alam kung saan magsisimula? Tingnan dito\: {0}
luckperms.login.try-again=Pakiusap subukang muli mamaya
luckperms.login.loading-database-error=May naganap na error sa database habang naglo-load ng data ng pahintulot
luckperms.login.server-admin-check-console-errors=Kung ikaw ay isang admin ng server, mangyaring suriin ang konsol para sa anumang mga error
luckperms.login.server-admin-check-console-info=Pakitingnan ang konsol ng server para sa karagdagang impormasyon
luckperms.login.data-not-loaded-at-pre=Ang data ng pahintulot para sa iyong user ay hindi na-load sa pre-login stage
luckperms.login.unable-to-continue=Hindi makapagpatuloy
luckperms.login.craftbukkit-offline-mode-error=Ang pagkakaroon nito ay malamang na dahil sa isang conflict sa pagitan ng CraftBukkit at ng setting ng online-mode
luckperms.login.unexpected-error=May hindi inaasahang error na naganap habang itinatakda ang iyong mga pahintulot na data
luckperms.opsystem.disabled=Ang vanilla OP system ay hindi pinagana sa server na ito
luckperms.opsystem.sponge-warning=Pakipansin na ang Server Operator status ay walang epekto sa mga Sponge permission check kapag may permission plugin na naka-install, kailangan mong i-edit ang data ng user diretso
luckperms.duration.unit.years.plural={0} taon
luckperms.duration.unit.years.singular={0} taon
luckperms.duration.unit.years.short={0}t
luckperms.duration.unit.months.plural={0} buwan
luckperms.duration.unit.months.singular={0} buwan
luckperms.duration.unit.months.short={0}bu
luckperms.duration.unit.weeks.plural={0} linggo
luckperms.duration.unit.weeks.singular={0} linggo
luckperms.duration.unit.weeks.short={0}w
luckperms.duration.unit.days.plural={0} araw
luckperms.duration.unit.days.singular={0} araw
luckperms.duration.unit.days.short={0}a
luckperms.duration.unit.hours.plural={0} oras
luckperms.duration.unit.hours.singular={0} oras
luckperms.duration.unit.hours.short={0}h
luckperms.duration.unit.minutes.plural={0} minuto
luckperms.duration.unit.minutes.singular={0} minuto
luckperms.duration.unit.minutes.short={0}m
luckperms.duration.unit.seconds.plural={0} segundo
luckperms.duration.unit.seconds.singular={0} segundo
luckperms.duration.unit.seconds.short={0}s
luckperms.duration.since={0} na ang nakalipas
luckperms.command.misc.invalid-code=Hindi balido ang code
luckperms.command.misc.response-code-key=code ng tugon
luckperms.command.misc.error-message-key=menasahe
luckperms.command.misc.bytebin-unable-to-communicate=Hindi makapagkomunikasyon sa bytebin
luckperms.command.misc.webapp-unable-to-communicate=Hindi makapagkomunikasyon sa web app
luckperms.command.misc.check-console-for-errors=Suriiin ang konsol para sa mga error
luckperms.command.misc.file-must-be-in-data=Ang File {0} ay dapat na direkta sa data directory
luckperms.command.misc.wait-to-finish=Pakihintay na matapos at subukang muli
luckperms.command.misc.invalid-priority=Hindi balidong priority na {0}
luckperms.command.misc.expected-number=Inaasahang numero
luckperms.command.misc.date-parse-error=Hindi ma-parse ang petsa {0}
luckperms.command.misc.date-in-past-error=Hindi maaaring itakda ang isang petsa sa nakaraan\!
luckperms.command.misc.page=pahina {0} ng {1}
luckperms.command.misc.page-entries={0} entries
luckperms.command.misc.none=Wala
luckperms.command.misc.loading.error.unexpected=May di-inaasahang mali na naganap
luckperms.command.misc.loading.error.user=Hindi naka-load ang User
luckperms.command.misc.loading.error.user-specific=Hindi ma-load ang target user na {0}
luckperms.command.misc.loading.error.user-not-found=Ang user para sa {0} ay hindi mahanap
luckperms.command.misc.loading.error.user-save-error=May error habang nagse-save ng user data para sa {0}
luckperms.command.misc.loading.error.user-not-online=Ang User {0} ay hindi online
luckperms.command.misc.loading.error.user-invalid={0} ay hindi balidong username/uuid
luckperms.command.misc.loading.error.user-not-uuid=Ang target user na {0} ay hindi balidong uuid
luckperms.command.misc.loading.error.group=Ang Group ay hindi naka-load
luckperms.command.misc.loading.error.all-groups=Hindi maaaring mag-load lahat ng mga groups
luckperms.command.misc.loading.error.group-not-found=Ang group na pinangalanang {0} ay hindi mahanap
luckperms.command.misc.loading.error.group-save-error=May error habang nagse-save ng group data para sa {0}
luckperms.command.misc.loading.error.group-invalid={0} ay hindi balidong pangalan ng group
luckperms.command.misc.loading.error.track=Ang Track ay hindi naka-load
luckperms.command.misc.loading.error.all-tracks=Hindi maaaring mag-load lahat ng mga tracks
luckperms.command.misc.loading.error.track-not-found=Ang track na pinangalanang {0} ay hindi mahanap
luckperms.command.misc.loading.error.track-save-error=May error habang nagse-save ng track data para sa {0}
luckperms.command.misc.loading.error.track-invalid={0} ay hindi balidong pangalan ng track
luckperms.command.editor.no-match=Hindi mabuksan ang editor, walang mga object na tumugma sa nais na uri
luckperms.command.editor.start=Nagpe-prepare ng bagong sesyon ng editor, pakihintay...
luckperms.command.editor.url=I-click ang link sa ibaba para buksan ang editor
luckperms.command.editor.unable-to-communicate=Hindi makapagkomunikasyon sa editor
luckperms.command.editor.apply-edits.success=Ang data ng web editor ay na-apply sa {0} {1} nang matagumpay
luckperms.command.editor.apply-edits.success-summary={0} {1} at {2} {3}
luckperms.command.editor.apply-edits.success.additions=karagdagang bagay
luckperms.command.editor.apply-edits.success.additions-singular=karagdagang bagay
luckperms.command.editor.apply-edits.success.deletions=pinatanggal
luckperms.command.editor.apply-edits.success.deletions-singular=pinatanggal
luckperms.command.editor.apply-edits.no-changes=Walang mga pagbabago na na-apply mula sa web editor, walang edits na laman ang ibinigay na data
luckperms.command.editor.apply-edits.unknown-type=Hindi makapag-apply ng edit sa tinukoy na uri ng object
luckperms.command.editor.apply-edits.unable-to-read=Hindi makapagbasa ng data gamit ang ibinigay na code
luckperms.command.search.searching.permission=Naghahanap para sa mga users at groups na may {0}
luckperms.command.search.searching.inherit=Naghahanap para sa mga users at groups na nagmana mula kay {0}
luckperms.command.search.result=Natagpuan ang {0} entries mula sa {1} users at {2} groups
luckperms.command.search.result.default-notice=Tandaan\: kapag naghahanap para sa mga miyembro ng default group, ang mga offline players na walang ibang pahintulot ay hindi ipapakita\!
luckperms.command.search.showing-users=Nagpapakita ng user entries
luckperms.command.search.showing-groups=Nagpapakita ng group entries
luckperms.command.tree.start=Kumakalap ng permission tree, pakihintay...
luckperms.command.tree.empty=Hindi makagawa ng tree, walang mga resulta ang natagpuan
luckperms.command.tree.url=Permission tree URL
luckperms.command.verbose.invalid-filter={0} ay hindi balidong verbose filter
luckperms.command.verbose.enabled=Verbose logging {0} para sa mga checks na tumutugma sa {1}
luckperms.command.verbose.command-exec=Pinapilit {0} na magexecute ng command {1} at ini-uulat ang lahat ng checks na ginawa...
luckperms.command.verbose.off=Verbose logging {0}
luckperms.command.verbose.command-exec-complete=Kumpletong pag-execute ng command
luckperms.command.verbose.command.no-checks=Ang pag-execute ng command ay natapos, ngunit walang mga permission checks na ginawa
luckperms.command.verbose.command.possibly-async=Maaaring dahil ang plugin ay nagpapatakbo ng mga command sa background (async)
luckperms.command.verbose.command.try-again-manually=Maaari ka pa ring gumamit ng verbose manually para makadetect ng mga checks na ginawa tulad nito
luckperms.command.verbose.enabled-recording=Verbose recording {0} para sa mga checks na tumutugma sa {1}
luckperms.command.verbose.uploading=Verbose logging {0}, nag-uupload ng mga resulta...
luckperms.command.verbose.url=Verbose results URL
luckperms.command.verbose.enabled-term=enabled
luckperms.command.verbose.disabled-term=disabled
luckperms.command.verbose.query-any=ANY
luckperms.command.info.running-plugin=Kumakarera
luckperms.command.info.platform-key=Plataforma
luckperms.command.info.server-brand-key=Brand ng Server
luckperms.command.info.server-version-key=Bersyon ng Server
luckperms.command.info.storage-key=Storage
luckperms.command.info.storage-type-key=Uri
luckperms.command.info.storage.meta.split-types-key=Mga Uri
luckperms.command.info.storage.meta.ping-key=Ping
luckperms.command.info.storage.meta.connected-key=Konektado
luckperms.command.info.storage.meta.file-size-key=Laki ng File
luckperms.command.info.extensions-key=Extensions
luckperms.command.info.messaging-key=Messaging
luckperms.command.info.instance-key=Instance
luckperms.command.info.static-contexts-key=Mga Nakapirmang Konteksto
luckperms.command.info.online-players-key=Online na mga Manlalaro
luckperms.command.info.online-players-unique={0} na natatangi
luckperms.command.info.uptime-key=Oras ng Pagpapatuloy
luckperms.command.info.local-data-key=Lokal na mga Datos
luckperms.command.info.local-data={0} mga user, {1} mga grupo, {2} mga track
luckperms.command.generic.create.success={0} ay matagumpay na nilikha
luckperms.command.generic.create.error=Mayroong error habang nililikha si {0}
luckperms.command.generic.create.error-already-exists={0} ay mayroon na\!
luckperms.command.generic.delete.success={0} ay matagumpay na binura
luckperms.command.generic.delete.error=Mayroong error habang binubura si {0}
luckperms.command.generic.delete.error-doesnt-exist={0} ay hindi umiiral\!
luckperms.command.generic.rename.success={0} ay matagumpay na bininyagan bilang {1}
luckperms.command.generic.clone.success={0} ay matagumpay na kinopya sa {1}
luckperms.command.generic.info.parent.title=Mga Parent na Grupo
luckperms.command.generic.info.parent.temporary-title=Temporary Parent Groups
luckperms.command.generic.info.expires-in=nagtatapos sa
luckperms.command.generic.info.inherited-from=namana mula sa
luckperms.command.generic.info.inherited-from-self=sarili
luckperms.command.generic.show-tracks.title=Mga Track ni {0}
luckperms.command.generic.show-tracks.empty={0} ay wala sa anumang track
luckperms.command.generic.clear.node-removed={0} na mga node ay tinanggal
luckperms.command.generic.clear.node-removed-singular={0} na node ay tinanggal
luckperms.command.generic.clear={0} na mga node ay tinanggal sa konteksto {1}
luckperms.command.generic.permission.info.title=Mga Pahintulot ni {0}
luckperms.command.generic.permission.info.empty={0} ay walang itinatakang pahintulot
luckperms.command.generic.permission.info.click-to-remove=Mag-click para alisin ang node na ito mula sa {0}
luckperms.command.generic.permission.check.info.title=Impormasyon sa Pahintulot para kay {0}
luckperms.command.generic.permission.check.info.directly={0} ay may {1} itinakda sa {2} sa konteksto {3}
luckperms.command.generic.permission.check.info.inherited={0} ay namana ang {1} na itinakda sa {2} mula sa {3} sa konteksto {4}
luckperms.command.generic.permission.check.info.not-directly={0} ay walang itinatakang {1}
luckperms.command.generic.permission.check.info.not-inherited={0} ay hindi namamana ang {1}
luckperms.command.generic.permission.check.result.title=Pagsubok ng Pahintulot para kay {0}
luckperms.command.generic.permission.check.result.result-key=Resulta
luckperms.command.generic.permission.check.result.processor-key=Processor
luckperms.command.generic.permission.check.result.cause-key=Dahilan
luckperms.command.generic.permission.check.result.context-key=Konteksto
luckperms.command.generic.permission.set=Itakda ang {0} sa {1} para kay {2} sa konteksto {3}
luckperms.command.generic.permission.already-has={0} ay mayroon nang {1} na itinakda sa konteksto {2}
luckperms.command.generic.permission.set-temp=Itakda ang {0} sa {1} para kay {2} para sa tagal na {3} sa konteksto {4}
luckperms.command.generic.permission.already-has-temp={0} ay mayroon nang {1} na itinakda pansamantala sa konteksto {2}
luckperms.command.generic.permission.unset=Alisin ang {0} para kay {1} sa konteksto {2}
luckperms.command.generic.permission.doesnt-have={0} ay walang itinatakang {1} sa konteksto {2}
luckperms.command.generic.permission.unset-temp=Alisin ang pansamantalang pahintulot {0} para kay {1} sa konteksto {2}
luckperms.command.generic.permission.subtract=Itakda ang {0} sa {1} para kay {2} para sa tagal na {3} sa konteksto {4}, {5} mas mababa kaysa dati
luckperms.command.generic.permission.doesnt-have-temp={0} ay walang itinatakang pansamantalang {1} sa konteksto {2}
luckperms.command.generic.permission.clear={0} na mga pahintulot ay nilinaw sa konteksto {1}
luckperms.command.generic.parent.info.title=Mga Magulang ni {0}
luckperms.command.generic.parent.info.empty={0} ay walang itinakdang magulang
luckperms.command.generic.parent.info.click-to-remove=Mag-click para alisin ang magulang na ito mula kay {0}
luckperms.command.generic.parent.add={0} ay ngayon nagmamana ng mga pahintulot mula sa {1} sa konteksto {2}
luckperms.command.generic.parent.add-temp={0} ay ngayon pansamantalang nagmamana ng mga pahintulot mula sa {1} para sa tagal na {2} sa konteksto {3}
luckperms.command.generic.parent.set={0} ay pinalaya mula sa dati nitong mga magulang, at ngayon ay nagmamana na lamang mula sa {1} sa konteksto {2}
luckperms.command.generic.parent.set-track={0} ay pinalaya mula sa dati nitong mga magulang sa track {1}, at ngayon ay nagmamana na lamang mula sa {2} sa konteksto {3}
luckperms.command.generic.parent.remove={0} ay hindi na nagmamana ng mga pahintulot mula sa {1} sa konteksto {2}
luckperms.command.generic.parent.remove-temp={0} ay hindi na pansamantalang nagmamana ng mga pahintulot mula sa {1} sa konteksto {2}
luckperms.command.generic.parent.subtract={0} ay magmamana ng mga pahintulot mula sa {1} para sa tagal na {2} sa konteksto {3}, {4} mas mababa kaysa dati
luckperms.command.generic.parent.clear={0} na mga magulang ay nilinaw sa konteksto {1}
luckperms.command.generic.parent.clear-track={0} na mga magulang sa track {1} ay nilinaw sa konteksto {2}
luckperms.command.generic.parent.already-inherits={0} ay mayroon nang nagmamana mula sa {1} sa konteksto {2}
luckperms.command.generic.parent.doesnt-inherit={0} ay hindi nagmamana mula sa {1} sa konteksto {2}
luckperms.command.generic.parent.already-temp-inherits={0} ay mayroon nang pansamantalang nagmamana mula sa {1} sa konteksto {2}
luckperms.command.generic.parent.doesnt-temp-inherit={0} ay hindi pansamantalang nagmamana mula sa {1} sa konteksto {2}
luckperms.command.generic.chat-meta.info.title-prefix={0}''s Prefixes
luckperms.command.generic.chat-meta.info.title-suffix=Mga Suffix ni {0}
luckperms.command.generic.chat-meta.info.none-prefix={0} ay walang mga prefix
luckperms.command.generic.chat-meta.info.none-suffix={0} ay walang mga suffix
luckperms.command.generic.chat-meta.info.click-to-remove=Mag-click para alisin ang {0} na ito mula kay {1}
luckperms.command.generic.chat-meta.already-has={0} ay mayroon nang {1} {2} itinakda sa isang priyoridad ng {3} sa konteksto {4}
luckperms.command.generic.chat-meta.already-has-temp={0} ay mayroon nang pansamantalang {1} {2} itinakda sa isang priyoridad ng {3} sa konteksto {4}
luckperms.command.generic.chat-meta.doesnt-have={0} ay walang {1} {2} itinakda sa isang priyoridad ng {3} sa konteksto {4}
luckperms.command.generic.chat-meta.doesnt-have-temp={0} ay walang pansamantalang {1} {2} itinakda sa isang priyoridad ng {3} sa konteksto {4}
luckperms.command.generic.chat-meta.add={0} ay mayroon nang {1} {2} itinakda sa isang priyoridad ng {3} sa konteksto {4}
luckperms.command.generic.chat-meta.add-temp={0} ay mayroon nang {1} {2} itinakda sa isang priyoridad ng {3} para sa tagal na {4} sa konteksto {5}
luckperms.command.generic.chat-meta.remove={0} ay mayroon nang {1} {2} sa priyoridad {3} na tinanggal sa konteksto {4}
luckperms.command.generic.chat-meta.remove-bulk={0} ay mayroon nang lahat ng {1} sa priyoridad {2} na tinanggal sa konteksto {3}
luckperms.command.generic.chat-meta.remove-temp={0} ay mayroon nang pansamantalang {1} {2} sa priyoridad {3} na tinanggal sa konteksto {4}
luckperms.command.generic.chat-meta.remove-temp-bulk={0} ay mayroon nang lahat ng pansamantalang {1} sa priyoridad {2} na tinanggal sa konteksto {3}
luckperms.command.generic.meta.info.title=Mga Meta ni {0}
luckperms.command.generic.meta.info.none={0} ay walang mga meta
luckperms.command.generic.meta.info.click-to-remove=Mag-click para alisin ang meta na ito mula kay {0}
luckperms.command.generic.meta.already-has={0} ay mayroon nang meta key na {1} itinakda sa {2} sa konteksto {3}
luckperms.command.generic.meta.already-has-temp={0} ay mayroon nang meta key na {1} pansamantalang itinakda sa {2} sa konteksto {3}
luckperms.command.generic.meta.doesnt-have={0} ay walang meta key na {1} itinakda sa konteksto {2}
luckperms.command.generic.meta.doesnt-have-temp={0} ay walang meta key na {1} pansamantalang itinakda sa konteksto {2}
luckperms.command.generic.meta.set=Itakda ang meta key na {0} sa {1} para kay {2} sa konteksto {3}
luckperms.command.generic.meta.set-temp=Itakda ang meta key na {0} sa {1} para kay {2} para sa tagal na {3} sa konteksto {4}
luckperms.command.generic.meta.unset=Alisin ang meta key na {0} para kay {1} sa konteksto {2}
luckperms.command.generic.meta.unset-temp=Alisin ang pansamantalang meta key na {0} para kay {1} sa konteksto {2}
luckperms.command.generic.meta.clear=Ang meta na tumutugma sa uri ng {0} ay nilinaw sa konteksto {1}
luckperms.command.generic.contextual-data.title=Kontekstuwal na Datos
luckperms.command.generic.contextual-data.mode.key=mode
luckperms.command.generic.contextual-data.mode.server=server
luckperms.command.generic.contextual-data.mode.active-player=aktibong manlalaro
luckperms.command.generic.contextual-data.contexts-key=Konteksto
luckperms.command.generic.contextual-data.prefix-key=Prefix
luckperms.command.generic.contextual-data.suffix-key=Suffix
luckperms.command.generic.contextual-data.primary-group-key=Priyaryong Grupo
luckperms.command.generic.contextual-data.meta-key=Meta
luckperms.command.generic.contextual-data.null-result=Wala
luckperms.command.user.info.title=Impormasyon ng User
luckperms.command.user.info.uuid-key=UUID
luckperms.command.user.info.uuid-type-key=uri
luckperms.command.user.info.uuid-type.mojang=mojang
luckperms.command.user.info.uuid-type.not-mojang=offline
luckperms.command.user.info.status-key=Katayuan
luckperms.command.user.info.status.online=Online
luckperms.command.user.info.status.offline=Offline
luckperms.command.user.removegroup.error-primary=Hindi mo maaaring alisin ang isang user mula sa kanilang priyaryong grupo
luckperms.command.user.primarygroup.not-member={0} ay hindi pa miyembro ng {1}, idinaragdag sila ngayon
luckperms.command.user.primarygroup.already-has={0} ay mayroon nang itinatakang {1} bilang kanilang priyaryong grupo
luckperms.command.user.primarygroup.warn-option=Babala\: Ang paraan ng pagkuha ng priyaryong grupo na ginagamit ng server na ito ({0}) ay hindi sinusuportahan
luckperms.command.user.primarygroup.set={0}''s primary group was set to {1}
luckperms.command.user.track.error-not-contain-group={0} isn''t already in any groups on {1}
luckperms.command.user.track.unsure-which-track=Unsure which track to use, please specify it as an argument
luckperms.command.user.track.missing-group-advice=Either create the group, or remove it from the track and try again
luckperms.command.user.promote.added-to-first={0} isn''t in any groups on {1}, so they were added to the first group, {2} in context {3}
luckperms.command.user.promote.not-on-track={0} isn''t in any groups on {1}, so was not promoted
luckperms.command.user.promote.success=Promoting {0} along track {1} from {2} to {3} in context {4}
luckperms.command.user.promote.end-of-track=The end of track {0} was reached, unable to promote {1}
luckperms.command.user.promote.next-group-deleted=The next group on the track, {0}, no longer exists
luckperms.command.user.promote.unable-to-promote=Unable to promote user
luckperms.command.user.demote.success=Demoting {0} along track {1} from {2} to {3} in context {4}
luckperms.command.user.demote.end-of-track=The end of track {0} was reached, so {1} was removed from {2}
luckperms.command.user.demote.end-of-track-not-removed=The end of track {0} was reached, but {1} was not removed from the first group
luckperms.command.user.demote.previous-group-deleted=The previous group on the track, {0}, no longer exists
luckperms.command.user.demote.unable-to-demote=Unable to demote user
luckperms.command.group.list.title=Groups
luckperms.command.group.delete.not-default=You cannot delete the default group
luckperms.command.group.info.title=Impormasyon ng Grupo
luckperms.command.group.info.display-name-key=Display Name
luckperms.command.group.info.weight-key=Bigat
luckperms.command.group.setweight.set=Set weight to {0} for group {1}
luckperms.command.group.setdisplayname.doesnt-have={0} doesn''t have a display name set
luckperms.command.group.setdisplayname.already-has={0} already has a display name of {1}
luckperms.command.group.setdisplayname.already-in-use=The display name {0} is already being used by {1}
luckperms.command.group.setdisplayname.set=Set display name to {0} for group {1} in context {2}
luckperms.command.group.setdisplayname.removed=Removed display name for group {0} in context {1}
luckperms.command.track.list.title=Tracks
luckperms.command.track.path.empty=None
luckperms.command.track.info.showing-track=Showing Track
luckperms.command.track.info.path-property=Path
luckperms.command.track.clear={0}''s groups track was cleared
luckperms.command.track.append.success={0} ay matagumpay na inilalagay sa hulihan ng {1}
luckperms.command.track.insert.success={0} ay matagumpay na inilalagay sa index {1} ng {2}
luckperms.command.track.insert.error-number=Expected number but instead received\: {0}
luckperms.command.track.insert.error-invalid-pos=Unable to insert at position {0}
luckperms.command.track.insert.error-invalid-pos-reason=invalid position
luckperms.command.track.remove.success={0} ay matagumpay na tinanggal sa track
luckperms.command.track.error-empty={0} cannot be used as it is empty or contains only one group
luckperms.command.track.error-multiple-groups={0} is a member of multiple groups on this track
luckperms.command.track.error-ambiguous=Unable to determine their location
luckperms.command.track.already-contains={0} already contains {1}
luckperms.command.track.doesnt-contain={0} doesn''t contain {1}
luckperms.command.log.load-error=The log could not be loaded
luckperms.command.log.invalid-page=Invalid page number
luckperms.command.log.invalid-page-range=Please enter a value between {0} and {1}
luckperms.command.log.empty=No log entries to show
luckperms.command.log.notify.error-console=Cannot toggle notifications for console
luckperms.command.log.notify.enabled-term=Enabled
luckperms.command.log.notify.disabled-term=Disabled
luckperms.command.log.notify.changed-state={0} logging output
luckperms.command.log.notify.already-on=You are already receiving notifications
luckperms.command.log.notify.already-off=You aren''t currently receiving notifications
luckperms.command.log.notify.invalid-state=State unknown. Expecting {0} or {1}
luckperms.command.log.show.search=Showing recent actions for query {0}
luckperms.command.log.show.recent=Showing recent actions
luckperms.command.log.show.by=Showing recent actions by {0}
luckperms.command.log.show.history=Showing history for {0} {1}
luckperms.command.export.error-term=Error
luckperms.command.export.already-running=Another export process is already running
luckperms.command.export.file.already-exists=File {0} already exists
luckperms.command.export.file.not-writable=File {0} is not writable
luckperms.command.export.file.success=Successfully exported to {0}
luckperms.command.export.file-unexpected-error-writing=An unexpected error occurred whilst writing to the file
luckperms.command.export.web.export-code=Export code
luckperms.command.export.web.import-command-description=Use the following command to import
luckperms.command.import.term=Import
luckperms.command.import.error-term=Error
luckperms.command.import.already-running=Another import process is already running
luckperms.command.import.file.doesnt-exist=File {0} does not exist
luckperms.command.import.file.not-readable=File {0} is not readable
luckperms.command.import.file.unexpected-error-reading=An unexpected error occurred whilst reading from the import file
luckperms.command.import.file.correct-format=is it the correct format?
luckperms.command.import.web.unable-to-read=Unable to read data using the given code
luckperms.command.import.progress.percent={0}% complete
luckperms.command.import.progress.operations={0}/{1} operations complete
luckperms.command.import.starting=Starting import process
luckperms.command.import.completed=COMPLETED
luckperms.command.import.duration=took {0} seconds
luckperms.command.bulkupdate.must-use-console=The bulk update command can only be used from the console
luckperms.command.bulkupdate.invalid-data-type=Invalid type, was expecting {0}
luckperms.command.bulkupdate.invalid-constraint=Invalid constraint {0}
luckperms.command.bulkupdate.invalid-constraint-format=Constraints should be in the format {0}
luckperms.command.bulkupdate.invalid-comparison=Invalid comparison operator {0}
luckperms.command.bulkupdate.invalid-comparison-format=Expected one of the following\: {0}
luckperms.command.bulkupdate.queued=Bulk update operation was queued
luckperms.command.bulkupdate.confirm=Run {0} to execute the update
luckperms.command.bulkupdate.unknown-id=Operation with id {0} does not exist or has expired
luckperms.command.bulkupdate.starting=Running bulk update
luckperms.command.bulkupdate.success=Bulk update completed successfully
luckperms.command.bulkupdate.success.statistics.nodes=Total affected nodes
luckperms.command.bulkupdate.success.statistics.users=Total affected users
luckperms.command.bulkupdate.success.statistics.groups=Total affected groups
luckperms.command.bulkupdate.failure=Bulk update failed, check the console for errors
luckperms.command.update-task.request=An update task has been requested, please wait
luckperms.command.update-task.complete=Update task complete
luckperms.command.update-task.push.attempting=Now attempting to push to other servers
luckperms.command.update-task.push.complete=Other servers were notified via {0} successfully
luckperms.command.update-task.push.error=Error whilst pushing changes to other servers
luckperms.command.update-task.push.error-not-setup=Cannot push changes to other servers as a messaging service has not been configured
luckperms.command.reload-config.success=The configuration file was reloaded
luckperms.command.reload-config.restart-note=some options will only apply after the server has restarted
luckperms.command.translations.searching=Searching for available translations, please wait...
luckperms.command.translations.searching-error=Unable to obtain a list of available translations
luckperms.command.translations.installed-translations=Installed Translations
luckperms.command.translations.available-translations=Available Translations
luckperms.command.translations.percent-translated={0}% translated
luckperms.command.translations.translations-by=by
luckperms.command.translations.installing=Installing translations, please wait...
luckperms.command.translations.download-error=Unable download translation for {0}
luckperms.command.translations.installing-specific=Installing language {0}...
luckperms.command.translations.install-complete=Installation complete
luckperms.command.translations.download-prompt=Use {0} to download and install up-to-date versions of these translations provided by the community
luckperms.command.translations.download-override-warning=Please note that this will override any changes you''ve made for these languages
luckperms.usage.user.description=A set of commands for managing users within LuckPerms. (A ''user'' in LuckPerms is just a player, and can refer to a UUID or username)
luckperms.usage.group.description=A set of commands for managing groups within LuckPerms. Groups are just collections of permission assignments that can be given to users. New groups are made using the ''creategroup'' command.
luckperms.usage.track.description=A set of commands for managing tracks within LuckPerms. Tracks are a ordered collection of groups which can be used for defining promotions and demotions.
luckperms.usage.log.description=A set of commands for managing the logging functionality within LuckPerms.
luckperms.usage.sync.description=Reloads all data from the plugins storage into memory, and applies any changes that are detected.
luckperms.usage.info.description=Prints general information about the active plugin instance.
luckperms.usage.editor.description=Creates a new web editor session
luckperms.usage.editor.argument.type=the types to load into the editor. (''all'', ''users'' or ''groups'')
luckperms.usage.editor.argument.filter=permission to filter user entries by
luckperms.usage.verbose.description=Controls the plugins verbose permission check monitoring system.
luckperms.usage.verbose.argument.action=whether to enable/disable logging, or to upload the logged output
luckperms.usage.verbose.argument.filter=the filter to match entries against
luckperms.usage.verbose.argument.commandas=the player/command to run
luckperms.usage.tree.description=Generates a tree view (ordered list hierarchy) of all permissions known to LuckPerms.
luckperms.usage.tree.argument.scope=the root of the tree. specify "." to include all permissions
luckperms.usage.tree.argument.player=the name of an online player to check against
luckperms.usage.search.description=Searches for all of the users/groups with a specific permission
luckperms.usage.search.argument.permission=the permission to search for
luckperms.usage.search.argument.page=the page to view
luckperms.usage.network-sync.description=Sync changes with the storage and request that all other servers on the network do the same
luckperms.usage.import.description=Imports data from a (previously created) export file
luckperms.usage.import.argument.file=the file to import from
luckperms.usage.import.argument.replace=replace existing data instead of merging
luckperms.usage.import.argument.upload=upload the data from a previous export
luckperms.usage.export.description=Exports all permissions data to an ''export'' file. Can be re-imported at a later time.
luckperms.usage.export.argument.file=the file to export to
luckperms.usage.export.argument.without-users=exclude users from the export
luckperms.usage.export.argument.without-groups=exclude groups from the export
luckperms.usage.export.argument.upload=Upload all permission data to the webeditor. Can be re-imported at a later time.
luckperms.usage.reload-config.description=Reload some of the config options
luckperms.usage.bulk-update.description=Execute bulk change queries on all data
luckperms.usage.bulk-update.argument.data-type=the type of data being changed. (''all'', ''users'' or ''groups'')
luckperms.usage.bulk-update.argument.action=the action to perform on the data. (''update'' or ''delete'')
luckperms.usage.bulk-update.argument.action-field=the field to act upon. only required for ''update''. (''permission'', ''server'' or ''world'')
luckperms.usage.bulk-update.argument.action-value=the value to replace with. only required for ''update''.
luckperms.usage.bulk-update.argument.constraint=the constraints required for the update
luckperms.usage.translations.description=Manage translations
luckperms.usage.translations.argument.install=subcommand to install translations
luckperms.usage.apply-edits.description=Applies permission changes made from the web editor
luckperms.usage.apply-edits.argument.code=the unique code for the data
luckperms.usage.apply-edits.argument.target=who to apply the data to
luckperms.usage.create-group.description=Create a new group
luckperms.usage.create-group.argument.name=the name of the group
luckperms.usage.create-group.argument.weight=the weight of the group
luckperms.usage.create-group.argument.display-name=the display name of the group
luckperms.usage.delete-group.description=I-delete ang isang grupo
luckperms.usage.delete-group.argument.name=ang pangalan ng grupo
luckperms.usage.list-groups.description=I-lista ang lahat ng mga grupo sa platform
luckperms.usage.create-track.description=Gumawa ng isang bagong track
luckperms.usage.create-track.argument.name=ang pangalan ng track
luckperms.usage.delete-track.description=I-delete ang isang track
luckperms.usage.delete-track.argument.name=ang pangalan ng track
luckperms.usage.list-tracks.description=I-lista ang lahat ng mga track sa platform
luckperms.usage.user-info.description=Nagpapakita ng impormasyon tungkol sa user
luckperms.usage.user-switchprimarygroup.description=Nagpapalit ng pangunahing grupo ng user
luckperms.usage.user-switchprimarygroup.argument.group=ang grupo na papalitan
luckperms.usage.user-promote.description=Pinapataas ang ranggo ng user sa isang track
luckperms.usage.user-promote.argument.track=ang track na papataasan ng ranggo ang user
luckperms.usage.user-promote.argument.context=ang mga konteksto kung saan papataasan ang user
luckperms.usage.user-promote.argument.dont-add-to-first=tanging patataasin ang user kung nasa track na sila
luckperms.usage.user-demote.description=Pinapababa ang ranggo ng user sa isang track
luckperms.usage.user-demote.argument.track=ang track na pababain ang ranggo ang user
luckperms.usage.user-demote.argument.context=ang mga konteksto kung saan pababain ang user
luckperms.usage.user-demote.argument.dont-remove-from-first=pinipigilan ang user na alisin mula sa unang grupo
luckperms.usage.user-clone.description=Nagkoklona ng user
luckperms.usage.user-clone.argument.user=ang pangalan/uuid ng user na pagkoklunahan
luckperms.usage.group-info.description=Nagbibigay ng impormasyon tungkol sa grupo
luckperms.usage.group-listmembers.description=Nagpapakita ng mga user/grupo na nakakatanggap mula sa grupo na ito
luckperms.usage.group-listmembers.argument.page=ang pahina na gustong tingnan
luckperms.usage.group-setweight.description=Itinatakda ang timbang ng mga grupo
luckperms.usage.group-setweight.argument.weight=ang timbang na itatatakda
luckperms.usage.group-set-display-name.description=Itinatakda ang display name ng grupo
luckperms.usage.group-set-display-name.argument.name=ang pangalan na itatatag sa display
luckperms.usage.group-set-display-name.argument.context=ang mga konteksto kung saan itatatakda ang pangalan
luckperms.usage.group-rename.description=Inirerenome ang grupo
luckperms.usage.group-rename.argument.name=ang bagong pangalan
luckperms.usage.group-clone.description=Nagkoklona ng grupo
luckperms.usage.group-clone.argument.name=ang pangalan ng grupo na pagkoklunahan
luckperms.usage.holder-editor.description=Nagbubukas ng editor ng web permission
luckperms.usage.holder-showtracks.description=I-lista ang mga track na kinabibilangan ng bagay
luckperms.usage.holder-clear.description=Tinatanggal ang lahat ng mga pahintulot, magulang, at meta
luckperms.usage.holder-clear.argument.context=ang mga konteksto kung saan gusto mag-filter
luckperms.usage.permission.description=I-edit ang mga pahintulot
luckperms.usage.parent.description=I-edit ang mga pinapamana
luckperms.usage.meta.description=I-edit ang mga halaga ng metadata
luckperms.usage.permission-info.description=I-lista ang mga node ng pahintulot na kinabibilangan ng bagay
luckperms.usage.permission-info.argument.page=ang pahinang gustong tingnan
luckperms.usage.permission-info.argument.sort-mode=kung paano ayusin ang mga entry
luckperms.usage.permission-set.description=Nagtatatak ng isang pahintulot para sa bagay
luckperms.usage.permission-set.argument.node=ang node ng pahintulot na itatatag
luckperms.usage.permission-set.argument.value=ang halaga ng node
luckperms.usage.permission-set.argument.context=ang mga konteksto kung saan idaragdag ang pahintulot
luckperms.usage.permission-unset.description=Tinatanggal ang isang pahintulot para sa bagay
luckperms.usage.permission-unset.argument.node=ang node ng pahintulot na itatanggal
luckperms.usage.permission-unset.argument.context=ang mga konteksto kung saan aalisin ang pahintulot
luckperms.usage.permission-settemp.description=Nagtatatak ng isang pahintulot para sa bagay nang pansamantala
luckperms.usage.permission-settemp.argument.node=ang node ng pahintulot na itatatag
luckperms.usage.permission-settemp.argument.value=ang halaga ng node
luckperms.usage.permission-settemp.argument.duration=ang tagal bago mag-expire ang node ng pahintulot
luckperms.usage.permission-settemp.argument.temporary-modifier=kung paano ipapatupad ang pansamantalang pahintulot
luckperms.usage.permission-settemp.argument.context=ang mga konteksto kung saan idaragdag ang pahintulot
luckperms.usage.permission-unsettemp.description=Tinatanggal ang isang pansamantalang pahintulot para sa bagay
luckperms.usage.permission-unsettemp.argument.node=ang node ng pahintulot na itatanggal
luckperms.usage.permission-unsettemp.argument.duration=ang tagal na iaalis
luckperms.usage.permission-unsettemp.argument.context=ang mga konteksto kung saan aalisin ang pahintulot
luckperms.usage.permission-check.description=Sinusuri kung mayroon bang isang partikular na node ng pahintulot ang bagay
luckperms.usage.permission-check.argument.node=ang node ng pahintulot na sinusuri
luckperms.usage.permission-clear.description=Naglilinis ng lahat ng mga pahintulot
luckperms.usage.permission-clear.argument.context=ang mga konteksto kung saan gusto mag-filter
luckperms.usage.parent-info.description=I-lista ang mga grupo na ito ay nagmamana mula rito
luckperms.usage.parent-info.argument.page=ang pahinang gustong tingnan
luckperms.usage.parent-info.argument.sort-mode=kung paano ayusin ang mga entry
luckperms.usage.parent-set.description=Tinatanggal ang lahat ng iba pang mga grupo na ito ay nagmamana na at idinadagdag sila sa ibinigay na
luckperms.usage.parent-set.argument.group=ang grupo na itatapat
luckperms.usage.parent-set.argument.context=ang mga konteksto kung saan itatapat ang grupo
luckperms.usage.parent-add.description=Nagdadagdag ng isa pang grupo para ang bagay ay magmana ng pahintulot mula rito
luckperms.usage.parent-add.argument.group=ang grupo na magmamana mula rito
luckperms.usage.parent-add.argument.context=ang mga konteksto kung saan magmamana ng grupo
luckperms.usage.parent-remove.description=Tinatanggal ang isang nakaraang itinalagang patakaran ng pamamana
luckperms.usage.parent-remove.argument.group=ang grupo na tatanggalin
luckperms.usage.parent-remove.argument.context=ang mga konteksto kung saan tatanggalin ang grupo
luckperms.usage.parent-set-track.description=Tinatanggal ang lahat ng iba pang mga grupo na ito ay nagmamana na sa ibinigay na track at idinadagdag sila sa ibinigay na
luckperms.usage.parent-set-track.argument.track=ang track na itatapat sa
luckperms.usage.parent-set-track.argument.group=ang grupo na itatapat, o isang numero na may kinalaman sa posisyon ng grupo sa ibinigay na track
luckperms.usage.parent-set-track.argument.context=ang mga konteksto kung saan itatapat ang grupo
luckperms.usage.parent-add-temp.description=Nagdadagdag ng isa pang grupo para pansamantalang magmana ng pahintulot mula rito
luckperms.usage.parent-add-temp.argument.group=ang grupo na magmamana mula rito
luckperms.usage.parent-add-temp.argument.duration=ang tagal ng pagiging pansamantal na miyembro ng grupo
luckperms.usage.parent-add-temp.argument.temporary-modifier=kung paano ipapatupad ang pansamantalang pahintulot
luckperms.usage.parent-add-temp.argument.context=ang mga konteksto kung saan magmamana ng grupo
luckperms.usage.parent-remove-temp.description=Tinatanggal ang isang dating itinalagang pansamantalang patakaran ng pamamana
luckperms.usage.parent-remove-temp.argument.group=ang grupo na tatanggalin
luckperms.usage.parent-remove-temp.argument.duration=ang tagal na iaalis
luckperms.usage.parent-remove-temp.argument.context=ang mga konteksto kung saan tatanggalin ang grupo
luckperms.usage.parent-clear.description=Naglilinis ng lahat ng mga magulang
luckperms.usage.parent-clear.argument.context=ang mga konteksto kung saan gusto mag-filter
luckperms.usage.parent-clear-track.description=Naglilinis ng lahat ng mga magulang sa ibinigay na track
luckperms.usage.parent-clear-track.argument.track=ang track na tatanggalin
luckperms.usage.parent-clear-track.argument.context=ang mga konteksto kung saan gusto mag-filter
luckperms.usage.meta-info.description=Nagpapakita ng lahat ng chat meta
luckperms.usage.meta-set.description=Itinatakda ang isang halaga ng meta
luckperms.usage.meta-set.argument.key=ang key na itatatag
luckperms.usage.meta-set.argument.value=ang halaga na itatatag
luckperms.usage.meta-set.argument.context=ang mga konteksto kung saan idadagdag ang pares ng meta
luckperms.usage.meta-unset.description=Tinatanggal ang isang halaga ng meta
luckperms.usage.meta-unset.argument.key=ang key na iaalis
luckperms.usage.meta-unset.argument.context=ang mga konteksto kung saan iaalis ang pares ng meta
luckperms.usage.meta-settemp.description=Itinatakda ang isang halaga ng meta nang pansamantala
luckperms.usage.meta-settemp.argument.key=ang key na itatatag
luckperms.usage.meta-settemp.argument.value=ang halaga na itatatag
luckperms.usage.meta-settemp.argument.duration=ang tagal bago mag-expire ang halaga ng meta
luckperms.usage.meta-settemp.argument.context=ang mga konteksto kung saan idadagdag ang pares ng meta
luckperms.usage.meta-unsettemp.description=Tinatanggal ang isang pansamantalang halaga ng meta
luckperms.usage.meta-unsettemp.argument.key=ang key na iaalis
luckperms.usage.meta-unsettemp.argument.context=ang mga konteksto kung saan iaalis ang pares ng meta
luckperms.usage.meta-addprefix.description=Nagdaragdag ng isang prefix
luckperms.usage.meta-addprefix.argument.priority=ang prayoridad na idadagdag ang prefix
luckperms.usage.meta-addprefix.argument.prefix=ang string ng prefix
luckperms.usage.meta-addprefix.argument.context=ang mga konteksto kung saan idadagdag ang prefix
luckperms.usage.meta-addsuffix.description=Nagdaragdag ng isang suffix
luckperms.usage.meta-addsuffix.argument.priority=ang prayoridad na idadagdag ang suffix
luckperms.usage.meta-addsuffix.argument.suffix=ang string ng suffix
luckperms.usage.meta-addsuffix.argument.context=ang mga konteksto kung saan idadagdag ang suffix
luckperms.usage.meta-setprefix.description=Itinatakda ang isang prefix
luckperms.usage.meta-setprefix.argument.priority=ang prayoridad na itatatag ang prefix
luckperms.usage.meta-setprefix.argument.prefix=ang string ng prefix
luckperms.usage.meta-setprefix.argument.context=ang mga konteksto kung saan itatatag ang prefix
luckperms.usage.meta-setsuffix.description=Itinatakda ang isang suffix
luckperms.usage.meta-setsuffix.argument.priority=ang prayoridad na itatatag ang suffix
luckperms.usage.meta-setsuffix.argument.suffix=ang string ng suffix
luckperms.usage.meta-setsuffix.argument.context=ang mga konteksto kung saan itatatag ang suffix
luckperms.usage.meta-removeprefix.description=Tinatanggal ang isang prefix
luckperms.usage.meta-removeprefix.argument.priority=ang prayoridad na iaalis ang prefix
luckperms.usage.meta-removeprefix.argument.prefix=ang string ng prefix
luckperms.usage.meta-removeprefix.argument.context=ang mga konteksto kung saan iaalis ang prefix
luckperms.usage.meta-removesuffix.description=Tinatanggal ang isang suffix
luckperms.usage.meta-removesuffix.argument.priority=ang prayoridad na iaalis ang suffix
luckperms.usage.meta-removesuffix.argument.suffix=ang string ng suffix
luckperms.usage.meta-removesuffix.argument.context=ang mga konteksto kung saan iaalis ang suffix
luckperms.usage.meta-addtemp-prefix.description=Nagdaragdag ng isang prefix nang pansamantala
luckperms.usage.meta-addtemp-prefix.argument.priority=ang prayoridad na idadagdag ang prefix
luckperms.usage.meta-addtemp-prefix.argument.prefix=ang string ng prefix
luckperms.usage.meta-addtemp-prefix.argument.duration=ang tagal bago mag-expire ang prefix
luckperms.usage.meta-addtemp-prefix.argument.context=ang mga konteksto kung saan idadagdag ang prefix
luckperms.usage.meta-addtemp-suffix.description=Nagdaragdag ng isang suffix nang pansamantala
luckperms.usage.meta-addtemp-suffix.argument.priority=ang prayoridad na idadagdag ang suffix
luckperms.usage.meta-addtemp-suffix.argument.suffix=ang string ng suffix
luckperms.usage.meta-addtemp-suffix.argument.duration=ang tagal bago mag-expire ang suffix
luckperms.usage.meta-addtemp-suffix.argument.context=ang mga konteksto kung saan idadagdag ang suffix
luckperms.usage.meta-settemp-prefix.description=Itinatakda ang isang prefix nang pansamantala
luckperms.usage.meta-settemp-prefix.argument.priority=ang prayoridad na itatatag ang prefix
luckperms.usage.meta-settemp-prefix.argument.prefix=ang string ng prefix
luckperms.usage.meta-settemp-prefix.argument.duration=ang tagal bago mag-expire ang prefix
luckperms.usage.meta-settemp-prefix.argument.context=ang mga konteksto kung saan itatatag ang prefix
luckperms.usage.meta-settemp-suffix.description=Itinatakda ang isang suffix nang pansamantala
luckperms.usage.meta-settemp-suffix.argument.priority=ang prayoridad na itatatag ang suffix
luckperms.usage.meta-settemp-suffix.argument.suffix=ang string ng suffix
luckperms.usage.meta-settemp-suffix.argument.duration=ang tagal bago mag-expire ang suffix
luckperms.usage.meta-settemp-suffix.argument.context=ang mga konteksto kung saan itatatag ang suffix
luckperms.usage.meta-removetemp-prefix.description=Tinatanggal ang isang pansamantalang prefix
luckperms.usage.meta-removetemp-prefix.argument.priority=ang prayoridad na iaalis ang prefix
luckperms.usage.meta-removetemp-prefix.argument.prefix=ang string ng prefix
luckperms.usage.meta-removetemp-prefix.argument.context=ang mga konteksto kung saan iaalis ang prefix
luckperms.usage.meta-removetemp-suffix.description=Tinatanggal ang isang pansamantalang suffix
luckperms.usage.meta-removetemp-suffix.argument.priority=ang prayoridad na iaalis ang suffix
luckperms.usage.meta-removetemp-suffix.argument.suffix=ang string ng suffix
luckperms.usage.meta-removetemp-suffix.argument.context=ang mga konteksto kung saan iaalis ang suffix
luckperms.usage.meta-clear.description=Naglilinis ng lahat ng meta
luckperms.usage.meta-clear.argument.type=ang uri ng meta na iaalis
luckperms.usage.meta-clear.argument.context=ang mga konteksto kung saan gusto mag-filter
luckperms.usage.track-info.description=Nagbibigay ng impormasyon tungkol sa track
luckperms.usage.track-editor.description=Nagbubukas ng editor ng web permission
luckperms.usage.track-append.description=Nagdadagdag ng isang grupo sa dulo ng track
luckperms.usage.track-append.argument.group=ang grupo na idadagdag
luckperms.usage.track-insert.description=Nag-iinsert ng isang grupo sa isang ibinigay na posisyon sa track
luckperms.usage.track-insert.argument.group=ang grupo na iiinsert
luckperms.usage.track-insert.argument.position=ang posisyon kung saan iiinsert ang grupo (ang unang posisyon sa track ay 1)
luckperms.usage.track-remove.description=Tinatanggal ang isang grupo mula sa track
luckperms.usage.track-remove.argument.group=ang grupo na tatanggalin
luckperms.usage.track-clear.description=Naglilinis ng mga grupo sa track
luckperms.usage.track-rename.description=Inirerenome ang track
luckperms.usage.track-rename.argument.name=ang bagong pangalan
luckperms.usage.track-clone.description=Nagkoklona ng track
luckperms.usage.track-clone.argument.name=ang pangalan ng track na pagkoklunahan
luckperms.usage.log-recent.description=Nakikita ang mga kamakailang aksyon
luckperms.usage.log-recent.argument.user=ang pangalan/uuid ng user na gusto i-filter
luckperms.usage.log-recent.argument.page=ang bilang ng pahina na gustong tingnan
luckperms.usage.log-search.description=Naghahanap sa log para sa isang entry
luckperms.usage.log-search.argument.query=ang query na gusto hanapin
luckperms.usage.log-search.argument.page=ang bilang ng pahina na gustong tingnan
luckperms.usage.log-notify.description=Nagto-toggle ng log notifications
luckperms.usage.log-notify.argument.toggle=kung gagawing on o off
luckperms.usage.log-user-history.description=Nakikita ang kasaysayan ng isang user
luckperms.usage.log-user-history.argument.user=ang pangalan/uuid ng user
luckperms.usage.log-user-history.argument.page=ang bilang ng pahina na gustong tingnan
luckperms.usage.log-group-history.description=Nakikita ang kasaysayan ng isang grupo
luckperms.usage.log-group-history.argument.group=ang pangalan ng grupo
luckperms.usage.log-group-history.argument.page=ang bilang ng pahina na gustong tingnan
luckperms.usage.log-track-history.description=Nakikita ang kasaysayan ng isang track
luckperms.usage.log-track-history.argument.track=ang pangalan ng track
luckperms.usage.log-track-history.argument.page=ang bilang ng pahina na gustong tingnan
luckperms.usage.sponge.description=I-edit ang karagdagang data ng Sponge
luckperms.usage.sponge.argument.collection=ang koleksyon na gustong kuhanin
luckperms.usage.sponge.argument.subject=ang subject na gusto baguhin
luckperms.usage.sponge-permission-info.description=Nagpapakita ng impormasyon tungkol sa mga pahintulot ng subject
luckperms.usage.sponge-permission-info.argument.contexts=the contexts to filter by
luckperms.usage.sponge-permission-set.description=Itinatakda ang isang pahintulot para sa subject
luckperms.usage.sponge-permission-set.argument.node=ang node ng pahintulot na itatatag
luckperms.usage.sponge-permission-set.argument.tristate=the value to set the permission to
luckperms.usage.sponge-permission-set.argument.contexts=the contexts to set the permission in
luckperms.usage.sponge-permission-clear.description=Naglilinis ng lahat ng mga pahintulot
luckperms.usage.sponge-permission-clear.argument.contexts=the contexts to clear permissions in
luckperms.usage.sponge-parent-info.description=I-lista ang mga magulang ng subject
luckperms.usage.sponge-parent-info.argument.contexts=the contexts to filter by
luckperms.usage.sponge-parent-add.description=Nagdadagdag ng isa pang grupo para ang subject ay magmana ng pahintulot mula rito
luckperms.usage.sponge-parent-add.argument.collection=ang koleksyon ng subject
luckperms.usage.sponge-parent-add.argument.subject=ang pangalan ng parent Subject
luckperms.usage.sponge-parent-add.argument.contexts=ang mga konteksto upang idagdag ang magulang sa
luckperms.usage.sponge-parent-remove.description=Tinatanggal ang isang nakaraang itinalagang patakaran ng pamamana
luckperms.usage.sponge-parent-remove.argument.collection=ang koleksyon ng subject
luckperms.usage.sponge-parent-remove.argument.subject=ang pangalan ng parent Subject
luckperms.usage.sponge-parent-remove.argument.contexts=ang mga konteksto upang alisin ang magulang sa
luckperms.usage.sponge-parent-clear.description=Naglilinis ng lahat ng mga magulang
luckperms.usage.sponge-parent-clear.argument.contexts=ang mga konteksto upang linisin ang mga magulang sa
luckperms.usage.sponge-option-info.description=Nagpapakita ng impormasyon tungkol sa mga opsyon ng subject
luckperms.usage.sponge-option-info.argument.contexts=ang mga konteksto upang i-filter
luckperms.usage.sponge-option-set.description=Nagtatakda ng isang opsyon para sa Subject
luckperms.usage.sponge-option-set.argument.key=ang key na itatakda
luckperms.usage.sponge-option-set.argument.value=ang halaga na itatakda ang key
luckperms.usage.sponge-option-set.argument.contexts=ang mga konteksto upang itakda ang opsyon sa
luckperms.usage.sponge-option-unset.description=Hindi nagtatakda ng isang opsyon para sa Subject
luckperms.usage.sponge-option-unset.argument.key=ang key na hindi itatakda
luckperms.usage.sponge-option-unset.argument.contexts=ang mga konteksto upang hindi itakda ang key sa
luckperms.usage.sponge-option-clear.description=Naglilinis ng mga opsyon ng Subject
luckperms.usage.sponge-option-clear.argument.contexts=ang mga konteksto upang linisin ang mga opsyon sa
