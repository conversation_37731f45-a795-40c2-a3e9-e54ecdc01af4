break-shop-use-supertool: <yellow>SuperTool aletini kullanarak marketleri kırabilirsin.
fee-charged-for-price-change: <green>Fiyatı değiştirmek için <red>{0}<green> ödediniz.
not-allowed-to-create: <red>Burada bir market oluşturamazsın.
disabled-in-this-world: <red>Bu komut bu dünyada devre dışı
how-much-to-trade-for: <yellow>{1} <green>tane <yellow>{0} <green>için ne kadar ticaret yapmak istediğini sohbete yaz.
client-language-changed: <green>QuickShop, istemci dil ayarınızın değiştirildiğini algıladı, şimdi sizin için {0} yerel ayarını kullanıyoruz.
shops-backingup: Veri tabanından market yedeği oluşturuluyor...
_comment: Merhaba çevirmen! Eğer bunu Github'tan ya da kaynak kodundan çeviriyorsan, https://crowdin.com/project/quickshop-hikari adresine gitmelisin
unlimited-shop-owner-changed: <yellow>Bu sınırsız marketin sahibi {0} olarak değiştirildi.
bad-command-usage-detailed: '<red>Bad command arguments! Accepts the following parameters: <gray>{0}'
thats-not-a-number: <red>Geçersiz sayı
shop-name-disallowed: <red>The shop name <yellow>{0} <red>is disallowed. Pick another one!
console-only-danger: <red>Bu tehlikeli bir komut, yani sadece konsol tarafından kullanılabilir.
not-a-number: <red>Girdi sadece sayı olabilir, senin girdin {0} idi.
not-looking-at-valid-shop-block: <red>Hiç Mağza bulunamadı. Birine bakman lazım.
shop-removed-cause-ongoing-fee: <red>{0}'deki marketiniz açık tutmaya yetecek kadar paranız olmadığı için kaldırıldı!
tabcomplete:
  amount: '[miktar]'
  item: '[item]'
  price: '[fiyat]'
  name: '[name]'
  range: '[menzil]'
  currency: '[para biriminin adı]'
  percentage: '[percentage%]'
taxaccount-unset: <green>Bu dükkanın vergi hesabı artık sunucu genel ayarını takip ediyor.
blacklisted-item: <red>Bu öğeyi satamazsın çünkü kara listede
command-type-mismatch: <red>Bu komut sadece <aqua>{0} <red>tarafından kullanılabilir.
server-crash-warning: '<red>Eğer QuickShop Jar dosyasını sunucu çalışırken değiştirdiyseniz/sildiyseniz, /qs reload komutunu çalıştırdığınızda sunucu çökebilir.'
you-cant-afford-to-change-price: <red>Marketinizin fiyatını değiştirmek ${0} tutarındadır.
safe-mode: <red>QuickShop şimdi güvenli modda, bu mağaza kapsayıcısını açamazsınız, hataları düzeltmek için lütfen sunucu yöneticisi ile iletişime geçin.
forbidden-vanilla-behavior: <red>Eylem, vanilla'ya uygun olmadığı için yasaklandı
shop-out-of-space-name: <dark_purple>Marketin {0} dolu!
paste-disabled: |-
  <red>Paste functionality has been disabled! You cannot request technical support. <newline>Reason: {0}
quick-fill:
  entry-own: ' <yellow>- <red>[kendi] <aqua>{0} <light_purple>{1}'
  hover:
    - '<yellow>İsim: <aqua>{0}'
    - '<yellow>Sahibi: <aqua>{0}'
    - '<yellow>Türü: <aqua>{0}'
    - '<yellow>Fiyat: <aqua>{0}'
    - '<yellow>Öğe: <aqua>{0}'
    - '<yellow>Konum: <aqua>{0}'
  hover-arg-filled:
    - '<yellow>İsim: <aqua>{name}'
    - '<yellow>Sahibi: <aqua>{owner}'
    - '<yellow>Türü: <aqua>{type}'
    - '<yellow>Fiyat: <aqua>{price}'
    - '<yellow>Öğe: <aqua>{item}'
    - '<yellow>Konum: <aqua>{location}'
  header: '<yellow>You have multiple shops with name "<green>{0}<yellow>", choose one to continue:'
  entry-normal: ' <yellow>- <aqua>{0} <light_purple>{1}'
  entry-cooperation: ' <yellow>- <red>[co] <aqua>{0} <light_purple>{1}'
3rd-plugin-build-check-failed-admin: '<gray>(AdminOnly) <light_purple>{0} <dark_gray>denied the permission checks, If this not excepted, try add <light_purple>{1} <gray>to listener blacklist. <gray>Configure Guide: https://github.com/Ghost-chu/QuickShop-Hikari/wiki/Use-protection-listener-filter'
average-price-nearby: '<green>Yakınlardaki ortalama fiyat: <yellow>{0}'
inventory-check-global-alert: "<red>[Envanter Kontrolü] <gray>Uyarı! QuickShop görüntülü öğe bulundu <gold>{2}</gold> Envanterde <aqua>{0}</aqua> <dark_gray>[{1}]</dark_gray>, ki bu olmamalı, Bu genellikle birisinin görüntüleme öğesini çoğaltmak için istismardan kötü niyetli olarak yararlandığı anlamına gelir."
digits-reach-the-limit: <red>Fiyatta maksimum basamak sınırına ulaştın.
currency-unset: <green>Marketin para birimi başarıyla kaldırıldı. Artık varsayılan ayarlar kullanılıyor.
you-cant-create-shop-in-there: <red>Burada bir market oluşturma izniniz yok.
no-pending-action: <red>Bekleyen aksiyonunuz yok
refill-success: <green>Yeniden doldurma başarılı
failed-to-paste: <red>Veriler Pastebin'e yüklenemedi. İnternetinizi kontrol edip tekrar deneyiniz. (Ayrıntılar için konsola bakın)
shop-out-of-stock-name: <dark_purple>{0}, {1}, {2} koordinatlarındaki marketinde {3} tükendi!
shop-name-invalid: <red>The shop name <yellow>{0} <red>is invalid. Pick another one!
how-many-buy-stack: <yellow>Sohbete kaç tane toplu satın almak istediğinizi girin. Her toplu üründe <yellow>{0}&öğeleri vardır ve <yellow>{1}&ye toplu olarak satın alabilirsiniz. Tümünü satın almak için sohbete <aqua>{2}<green> yazın.
exceeded-maximum: <red>Değer, Java'nın maksimum değerini aştı.
unlimited-shop-owner-keeped: '<yellow>Dikkat: Marketin sahibi hala sınırsız market sahibidir, yeni dükkan sahibini yeniden ayarlamanız gerekmektedir.'
no-enough-money-to-keep-shops: <red>Marketlerini açık tutacak kadar paran yoktu! Bütün marketlerin kaldırıldı...
3rd-plugin-build-check-failed: <red>3. parti <bold>{0}<reset><red> eklentisi yetki kontrollerini reddetti, orada yetkiniz var mıydı?
not-a-integer: <red>Bir sayı yazmalısın, yazdığın {0} idi.
translation-country: 'Dil Bölgesi: Türkçe (tr_TR)'
buying-more-than-selling: '<red>UYARI: Sattığınızdan fazla öğe satın alıyorsunuz!'
purchase-failed: '<green>Satın alma başarısız oldu: İç Hata. Lütfen Sunucu Yöneticisine başvurunuz.'
denied-put-in-item: <red>Bu öğeyi marketine koyamazsın!
shop-has-changed: <red>Kullanmaya çalıştığınız market, tıkladığınızdan beri değişti!
flush-finished: <green>Mesajlar başarıyla temizlendi.
no-price-given: <red>Lütfen geçerli bir fiyat verin.
shop-already-owned: <red>Bu zaten bir market.
backup-success: <green>Yedekleme başarılı.
not-looking-at-shop: '&sHiç Mağza bulunamadı. Birine bakman lazım.'
you-cant-afford-a-new-shop: '<red>Yeni bir market oluşturma maliyeti: ${0}'
success-created-shop: <red>Market oluşturuldu.
shop-creation-cancelled: <red>Market oluşumu iptal edildi.
shop-owner-self-trade: <yellow>Kendi marketinde alışveriş yapıyorsun, bu nedenle herhangi bir kazan elde edemeyebilirsiniz.
purchase-out-of-space: <red>Bu markette yer kalmadı, marketi boşaltmaları için mağaza sahibi ya da personelleri ile iletişime geç.
reloading-status:
  success: <green>Yeniden yükleme hata olmadan tamamlandı.
  scheduled: <green>Yeniden yükleme tamamlandı. <gray>(Bazı değişikliklerin etki etmesi biraz zaman aldı)
  require-restart: <green>Yeniden yükleme tamamlandı. <yellow>(Bazı değişikliklerin etkilenmesi için sunucuyu yeniden başlatmak gerekebilir)
  failed: <red>Yeniden yükleme başarısız, sunucu konsolunu kontrol edin
player-bought-from-your-store-tax: <red>{0} marketinden {1} tane {2} aldı ve {3} ({4} vergi) kazandın.
not-enough-space: <red>Sadece {0} için daha fazla yerin var!
shop-name-success: <green>Successfully set the shop name to <yellow>{0}<green>.
shop-staff-added: <green>{0} başarıyla market personellerinize eklendi.
shop-staff-empty: <yellow>This shop have no staff members.
shops-recovering: Marketler yedekten kurtarılıyor...
virtual-player-component-hover: "<gray>Bu sanal bir oynatıcıdır.\n<gray>Aynı ada sahip bu ada sahip bir sistem hesabını ifade eder.</gray>\n<green>UUID: <yellow>{0}</yellow></green>\n<green>Kullanıcı adı: <yellow>{1}</yellow></green>\n<green>Olarak görüntüle: <yellow>{2}</yellow></green>"
real-player-component-hover: "<gray>Bu gerçek bir oyuncu.\n<green>UUID: <yellow>{0}</yellow></green>\n<green>Kullanıcı adı: <yellow>{1}</yellow></green>\n<green>Olarak görüntüle: <yellow>{2}</yellow></green>\n<gray>Aynı ada sahip bir sanal sistem hesabı kullanmak istiyorsanız, <dark_gray>\"[]\"</dark_gray> Kullanıcı adının her iki tarafına: <dark_gray>[{1}]</dark_gray>."
menu:
  sell-tax: <green>Vergilerde <yellow>{0} <green>ödediniz.
  owner: '<green>Sahibi: {0}'
  preview: <aqua>[Öğe Önizlemesi]
  enchants: <dark_purple>Büyüler
  sell-tax-self: <green>Bu marketin sahibi olduğunuz için vergi ödemediniz.
  shop-information: '<green>Market Bilgisi:'
  item: '<green>Öğe: <yellow>{0}'
  price-per: <yellow>{0} <green>başına fiyat - <yellow>{1}
  item-name-and-price: <yellow>{2} <green>için <yellow>{0} <green>tane {1}
  item-name-and-price-tax: <yellow>{0} {1} <green>for</green> {2} <gray>(<green>{3}</green> in taxes)</gray>
  successful-purchase: '<green>Başarıyla satın alındı:'
  price-per-stack: <yellow>{2} <green>tane <yellow>{0} <green>başına fiyat <yellow>{1}
  stored-enchants: <dark_purple>Depolanmış Büyüler
  item-holochat-error: <red>[Hata]
  this-shop-is-selling: <green>Bu market eşyaları<aqua>SATIYOR</aqua>.
  shop-stack: '<green>Yığının miktarı: <yellow>{0}'
  space: '<green>Yer: <yellow>{0}'
  effects: <green>Efektler
  damage-percent-remaining: <green>Kalan <yellow>{0}%.
  item-holochat-data-too-large: <red>[Error] Öğe NBT'si gösterilemeyecek kadar büyük
  stock: '<green>Stok <yellow>{0}'
  this-shop-is-buying: <green>Bu market öğe <light_purple>SATIN ALIYOR<green>.
  successfully-sold: '<green>Başarıyla satıldı:'
  total-value-of-chest: '<green>Sandığın toplam değeri: <yellow>{0}'
currency-not-exists: <red>Ayarlamak istediğiniz para birimi bulunamadı. Belki imlâ hatası yaptığınız ya da o para birimi dünyada mevcut değil.
no-nearby-shop: <red>Yakınlarda {0} ile uyuşan market yok.
translation-author: 'Ghost_chu, Andre_601'
integrations-check-failed-trade: <red>{0} entegrasyonu market ticaretini reddetti
shop-transaction-failed: <red>Sorry, but an internal error occurred while processing your purchase. The purchase has been cancelled and any operation has been rollback. Please contact the server administrators if it keeping occurred.
success-change-owner-to-server: <green>Market sahibi başarıyla Sunucu yapıldı.
shop-name-not-found: <red>The shop named <yellow>{0} <red>not exists.
shop-name-too-long: <red>Bu isim çok uzun (maks. {0} uzunluğunda olmalı), başka bir tane seçin!
metric:
  header-player: '<yellow>{0}''ın {1} {2} işlemleri:'
  action-hover: <yellow>{0}
  price-hover: <yellow>Toplam {0}, {1} vergiler dahil.
  unknown: <gray>(bilinmiyor)
  undefined: <gray>(isimsiz)
  no-results: <red>İşlem bulunamadı.
  action-description:
    DELETE: <yellow>Player deleted a shop. And refunded the shop creation fee to owner if possible.
    ONGOING_FEE: <yellow>Player paid the ongoing fee because the payment period.
    PURCHASE_BUYING_SHOP: <yellow>Player bazı eşyaları bir satın alma dükkanına sattı.
    CREATE: <yellow>Player bir mağaza oluşturdu.
    PURCHASE_SELLING_SHOP: <yellow>Oyuncu bir satış mağazasından bazı eşyalar satın aldı
    PURCHASE: <yellow>Bir mağazadan satın alınan ürün
  query-argument: 'Sorgu bağımsız değişkeni: {0}'
  amount-hover: <yellow>{0}x
  header-shop: '<yellow>Mağaza {0}''ın {1} {2} işlemleri:'
  player-hover: |-
    <yellow>{0}<newline><gold>UUID: <gray>{1}
  looking-up: <yellow>Metrik araması gerçekleştiriliyor, Lütfen bekleyin...
  tax-hover: <yellow>{0} vergiler
  header-global: '<yellow>Sunucu işlemleri {0} {1} :'
  na: <gray>N/A
  transaction-count: <yellow>{0} toplam
  shop-hover: |-
    <yellow>{0}<newline><gold>Pos: <gray>{1} {2} {3}, Dünya: {4}<newline><gold>Sahibi: <gray>{5}<newline><gold>Mağaza Türü: <gray>{6}<newline><gold>Ürün: <gray>{7}<newline><gold>Fiyat: <gray>{8}
  time-hover: '<yellow>Saat: {0}'
  amount-stack-hover: <yellow>{0}x yığını
permission-denied-3rd-party: '<red>İzin reddedildi: 3. Parti Eklenti [{0}].'
you-dont-have-that-many-items: <red>Sende sadece {0} tane {1} var.
complete: <green>Tamamlandı!
translate-not-completed-yet-url: '{0} dilinin çevirisi {1} tamamlanmadı. Çevirmemize yardım etmek mi istiyorsun? Araştır: {2}'
success-removed-shop: <green>Market kaldırıldı.
currency-set: <green>Marketin para birimi başarıyla {0} olarak ayarlandı.
shop-purged-start: <green>Shop tasfiyesi başladı, ayrıntılar için konsolu kontrol edin.
economy-transaction-failed: <red>Sorry, but an internal error occurred while processing your transaction. The transaction has been cancelled and any economy operation has been rollback. Please contact the server administrators if it keeping occurred.
nothing-to-flush: <green>Yeni market mesajınız yok.
no-price-change: <red>Bu fiyat değişikliğine sebep olmaz!
edition-confilct: QuicShop-Hikari with QuickShop-Reremake installed may conflict each other, uninstall one of them.
inventory-unavailable: |-
  <red>This shop InventoryWrapper not exists or invalid. Did you using addon to re-binding the shop Inventory? Information: InventoryWrapper={0}, WrapperProvider={1}, SymbolLink={2}. Please contact the server administrators.
file-test: Bu bir deneme metin dosyası. Bu dosyası messages.json dosyasının bozuk mu diye kontrol etmek için kullanıyoruz. Bu dosyaya istediğin gibi sevdiğin easter egg'leri koyabilirsin :)
unknown-player: <red>Hedef oyuncu mevcut değil, lütfen yazdığınız kullanıcı adını kontrol ediniz.
player-offline: <red>The target player currently is offline.
player-profile-format: <aqua>{0}</aqua><gold>(<yellow>{1}</yellow>)</gold>
shop-type:
  selling: Satış
  buying: Satın alım
language:
  qa-issues: '<yellow>Kalite güven sorunları: <aqua>{0}%'
  code: '<yellow>Kod: <gold>{0}'
  approval-progress: '<yellow>Onaylanan İlerleme: <aqua>{0}%'
  translate-progress: '<yellow>Tercüme İleriemesi: <aqua>{0}%'
  name: '<yellow>İsim: <gold>{0}'
  help-us: <green>[Tercüme kalitesini arttırmamıza yardımcı olun]
warn-to-paste: |-
  <yellow>Veriler toplanıyor ve Pastebin'e yükleniyor, bu biraz zaman alabilir. <red><bold>Uyarı<red>, Veriler bir hafta boyunca herkese açık tutulur, sunucu konfigürasyonunuzu ve diğer hassas bilgilerinizi sızdırabilir, sadece <bold>güvendiğiniz personele/geliştiriciye <red> göderdiğinizden emin olun.
how-many-sell-stack: <green>Sohbete Kaç tane toplu satış yapmak istediğinizi yazın. Toplu olarak <yellow>{0}'ın öğesi vardır ve <yellow>{1}'ın toplu olarak satabilirsiniz. Tümünü satmak için sohbete <aqua>{2}<green> yazın.
updatenotify:
  buttontitle: '[Şimdi Güncelle]'
  onekeybuttontitle: '[OneKey Update]'
  label:
    github: '[GitHub]'
    ore: '[Ore]'
    lts: '[LTS]'
    nbtapi: '[+NBTAPI]'
    qualityverifyed: '[Kalite]'
    master: '[Usta]'
    unstable: '[Kararsız]'
    paper: '[+Paper için Optimize Edilmiş]'
    stable: '[Kararlı]'
    spigotmc: '[SpigotMC]'
    papermc: '[PaperMC]'
    bukkitdev: '[BukkitDev]'
    polymart: '[Polymart]'
    modrinth: '[Modrinth]'
    basic: '[Basic]'
  list:
    - '{0} yayınlandı. Sen hala {1} kullanıyorsun!'
    - Boom! Yeni güncelleme {0} geliyor. Güncelle!
    - Sürpriz! {0} çıktı. Sen {1} kullanıyorsun
    - Görünüşe göre güncellemen gerekiyor. {0} yayınlandı!
    - Eyvah! {0} yayınlanmış. Sen {1} kullanıyorsun!
    - Yemin ederim QS {0} sürümüne güncellendi. Neden hala güncellemedin?
    - Tamir ediliyor ve ona... Üzgünüm, ama {0} yayınlanmış!
    - Hata! Yok yok. Bu bir hata değil. {0} yayınlandı!
    - OMG! {0} Çıktı! Neden hala {1} versiyonunu kullanıyorsun?
    - 'Bügünün Haberleri: QuickShop {0} versiyonuna güncellendi!'
    - Eklenti k.i.a. {0} sürümüne güncellemelisin!
    - '{0} güncellemesi ateşlendi. Güncelleyi kaydet!'
    - Komutanım! Bir güncelleme mevcut. {0} sürümü daha yeni çıktı!
    - Stilime bak---{0} güncellendi. Sen hala {1} kullanıyorsun
    - Ahhhhhhh! Yeni güncelleme {0}! Güncelle!
    - Ne düşünüyürsun? {0} yayınlandı! Güncelle!
    - Doktor QuickShop'a yeni {0} güncellemesi geldi! Güncellemelisin~
    - Ko~ko~da~yo~ QuickShop'a yeni {0} güncellemesi geldi ~
    - Paimon, QuickShop'un yeni güncellemesi {0} olduğunu size söylemek istiyor!
  remote-disable-warning: '<red>QuickShop''un bu sürümü uzak sunucu tarafından devre dışı bırakıldı, bunun anlamı bu versiyonda büyük problemler oluşabilir. Detayları SpigotMC sayfamızdan öğrenebilirsiniz: {0}. Bu uyarı siz versiyonu stabil başka bir sürümle değiştirmedikçe konsolda belirmeye devam edecek. Ama bu sunucu hızınızı etkilemez.'
purchase-out-of-stock: <red>Bu marketin stoğu tükendi, stoğu yeniden doldurmaları için mağaza sahibi ya da personelleri ile iletişime geç.
nearby-shop-entry: '<green>Bilgi:-:{0} <green>Fiyat:<aqua>{1} <green>x:<aqua>{2} <green>y:<aqua>{3} <green>z:<aqua>{4} <green>Uzaklık: <aqua>{5} <green>Blok(lar)'
chest-title: QuickShop Marketi
console-only: <red>Bu komut sadece konsolda kullanabilir.
failed-to-put-sign: <red>Market çevresinde bilgi tabelasını yerleştirmek için yeterli yer yok.
shop-name-unset: <red>This shop's name now removed
shop-nolonger-freezed: <green>Marketi çözdünüz. Şimdi normale döndü!
no-permission-build: <red>Buraya bir market kuramazsın.
tableformat:
  left_half_line: <dark_purple>+--------------------
  right_half_line: <dark_purple>--------------------+
  full_line: <dark_purple>+---------------------------------------------------+
  left_begin: '<dark_purple>| '
quickshop-gui-preview: QuickShop GUI Öğe Önizlemesi
translate-not-completed-yet-click: '{0} dilinin çevirisi {1} tamamlanmadı. Çevirmemize yardım etmek mi istiyorsun? Buraya tıkla!'
taxaccount-invalid: <red>Target hesabı geçersiz değil, lütfen geçerli bir oyuncu adı veya uuid(çizgili) girin.
player-bought-from-your-store: <red>{0} marketinden {1} tane {2} aldı ve {3} kazandın.
reached-maximum-can-create: <red>Zaten maksimum {0}/{1} market oluşturdun!
reached-maximum-create-limit: <red>Oluşturabileceğiniz maksimum mağaza sayısına ulaştınız
translation-version: 'Destek Sürümü: Hikari'
no-double-chests: <red>Çifte sandık ile market oluşturamazsınız.
price-too-cheap: <red>Fiyat <yellow>${0}<red>'dan büyük olmalıdır.
shop-not-exist: <red>Orada market yok.
bad-command-usage: <red>Kötü komut argümanları!
cleanghost-warning: <yellow>This command will purge <red>all</red> shops if the shop is corrupted, was created in not allowed worlds, is selling/buying not allowed items or <red><bold>EXISTS IN A UNLOADED WORLD</bold></red>. Make sure to create a full backup of your shop data first and use <aqua>/qs cleanghost confirm</aqua> to continue.
cleanghost-starting: <green>Hayalet dükkanları kontrol etmeye başlıyor (eksik konteyner blokları). Mevcut olmayan tüm dükkanlar kaldırılacak...
cleanghost-deleting: <yellow>Bozuk bir dükkan buldum <aqua>{0}</aqua> çünkü {1}, silmek için işaretleyin...
cleanghost-deleted: <green>Toplam <yellow>{0}</yellow> Mağazalar silindi.
shop-purchase-cancelled: <red>Market alımı iptal edildi.
bypassing-lock: <red>Bir QuickShop kilidi atlanıyor!
bungee-cross-server-msg: '<yellow>QuickShop CSM: <green>{0}'
saved-to-path: Yedek {0} dosyasına kaydedildi.
shop-now-freezed: <green>Marketi dondurdunuz. Artık hiç kimse bu marketle ticaret yapamaz!
price-is-now: <green>Marketin yeni fiyatı <yellow>{0}
shops-arent-locked: <red>Unutmayın! Marketler hırsızlara karşı koruma altında DEĞİLDİR. Eğer hırsızları durdurmak istiyorsanız LWC, Lockette, vb. eklentiler ile kilitleyiniz!
that-is-locked: <red>Bu market kilitli.
shop-has-no-space: <red>Markette sadece {1} için daha fazla {0} yer var.
safe-mode-admin: '<red><bold>UYARI: <red>Bu sunucudaki QuickShop artık güvenli modda çalışıyor, hiçbir özellik çalışmayacak, hataları kontrol etmek için lütfen <yellow>/qs <red> komutunu yazın.'
shop-stock-too-low: <red>Markette sadece {0} tane {1} kalmış!
world-not-exists: <yellow>{0}<red> isimli dünya mevcut değil
how-many-sell: <green>Sohbete kaç tane <aqua>SATMAK<green> istediğinizi yazın. <yellow>{0}<green> tane satabilirsiniz. Hepsini satmak için <aqua>{1}<green> yazın.
shop-freezed-at-location: '{1} yerindeki {0} marketiniz donduruldu!'
translation-contributors: 'Katkıda Bulunanlar: Timtower, Netherfoam, KaiNoMood, Mgazul, JackTheChicken Andre_601 and ZOYTRK#4529'
empty-success: <green>Market boşaltma başarılı
taxaccount-set: <green>Bu dükkanın vergi hesabı <yellow>{0} olarak ayarlandı
support-disable-reason:
  hot-reload: <yellow>Unsupported hot reloaded, restart the server and check again.
  outdated: <yellow>This version of QuickShop already outdated, up-to-date before requesting support!
  bad-hosts: |-
    <yellow>This server HOSTS has been modified and QuickShop some functions require connect to Mojang API correctly. Fix the HOSTS before asking support, Windows at C:\windows\system32\drivers\etc\hosts, Linux at /etc/hosts.
  privacy: <yellow>This server running under cracked (offline) mode. If you are running bukkit server under proxy and online-mode=true on proxy, please configure the proxy related settings correctly.
  modified: <yellow>File integrity check failed, this QuickShop build has been modified by others.
  consolespamfix-installed: <yellow>ConsoleSpamFix installed, it will hidden exception details, temporarily disable it during asking support.
  authlib-injector-detected: <yellow>This server running under 3rd-party authlib provider like authlib-injector.
  unsupported-server-software: <yellow>Unsupported server software, Any modded hybrid server software is unsupported including MCPC, Cauldron, CatServer, Mohist, Magma, Fukkit, Cardboard etc.
supertool-is-disabled: '&sSupertool devre dışı. Hiçbir marketi kıramazsın.'
unknown-owner: Bilinmiyor
restricted-prices: '<red>{0} için sınırlandırılmış fiyat: Min {1}, Maks {2}'
nearby-shop-this-way: <green>Market sizden {0} blok uzakta.
owner-bypass-check: <yellow>Bütün kontroller atlandı. Ticaret başarılı! (Market sahibisin!)
april-rick-and-roll-easter-egg: "<green>---------------------------------------------------</green>\n<rainbow><bold>LIMITED TIME EVENT -- QuickShop-Hikari</bold></rainbow>\n<yellow>Get your <gold>PREMIUM</gold> particle effect on all QuickShop server!</yellow>\n<aqua>Claim it by watch the video tutorial below!</aqua>\n<hover:show_text:'<gold>Click to open rewards claim page in your browser.</gold><click:open_url:'https://youtu.be/dQw4w9WgXcQ'>https://youtu.be/dQw4w9WgXcQ</click></hover>\n<green>---------------------------------------------------</green>"
signs:
  item-right: ''
  out-of-space: Yeterli alan yok
  unlimited: Sınırsız
  stack-selling: '{0} Satıyor'
  stack-price: '{1} tane {2} başına {0}'
  status-unavailable: <red>
  out-of-stock: Stok tükendi
  stack-buying: '{0} Alıyor'
  freeze: Takas kapatıldı
  price: 'Her biri {0}'
  buying: 'Alıyor: {0}'
  header: '{1}{0}'
  header-available: <green>{0}
  header-unavailable: <red>{0}
  selling: 'Satıyor: {0}'
  status-available: <green>
  item-left: ''
negative-amount: <red>Negatif miktarlarda işlem yapamazsın
display-turn-on: <green>Mağaza ekranını başarıyla açın.
shop-staff-deleted: <green>{0} başarıyla market personellerinizden kaldırıldı.
nearby-shop-header: '<green>En yakın Dükkan eşleşmesi <aqua>{0}<green>:'
backup-failed: Veri tabanı yedeklenemedi. Detaylar için konsolu kontrol edin.
shop-staff-cleared: <green>Başarıyla marketinizin bütün personelleri kadırıldı.
price-too-high: <red>Market fiyatı çok yüksek! {0} fiyatından yüksek bir tane oluşturamazsınız.
plugin-cancelled: '<red>Operation cancelled, Reason: {0}'
player-sold-to-your-store: <green>{0} marketine {1} tane {2} sattı.
shop-out-of-stock: <dark_purple>{0}, {1}, {2} koordinatlarındaki marketinde {3} tükendi!
how-many-buy: <green>Sohbete kaç tane <aqua>Satın almak<green> istediğinizi yazın. <yellow>{0}<green> tane alabilirsiniz. Hepsini satın almak için <aqua>{1}<green> yazın.
language-info-panel:
  help: 'Bize yardım edin: '
  code: 'Kod: '
  name: 'Dil: '
  progress: 'İlerleme: '
  translate-on-crowdin: '[Crowdin''de Çevirin]]'
item-not-exist: <yellow>{0} <red>eşyası mevcut değil, lütfen yazımınızı kontrol ediniz.
shop-creation-failed: <red>Market oluşturulurken hata meydana geldi, lütfen sunucu yöneticisi ile iletişime geçiniz.
inventory-space-full: <red>Your inventory space remains can only put <green>{1}x <red>items into it, try empty the your inventory!
no-creative-break: '&sYaratıcı modda diğer oyuncuların marketlerini kıramazsın, hayatta kalma moduna geçin veya yerine {0} supertool aletini kullanın.'
booleanformat:
  success: <green>✔
  failed: <red>✘
controlpanel:
  stack: '<green>Toplu miktar başına: <aqua>{0} <yellow>[<bold><light_purple>Değiştir</light_purple>]'
  price-hover: <yellow>Öğeye yeni bir fiyat belirlemek için tıkla.
  remove: <red><bold>[Marketi Kaldır]
  mode-buying-hover: <yellow>Marketi SATMA moduna değiştirmek için tıkla.
  empty: '<green>Boşalt: Tüm eşyaları boşalt <yellow>[<light_purple><bold>BOŞALT<yellow>]'
  stack-hover: <yellow>Yığın başına öğe miktarını ayarlamak için tıklayın. Normal davranış için 1 yapın.
  alwayscounting-hover: Dükkan her zaman konteyner sayıyorsa geçiş yapmak için <yellow>tıklayın.
  alwayscounting: '<green>Her zaman sayıyor: {0} <yellow>[<bold><light_purple>Değiştir</light_purple>]'
  setowner: '<green>Fiyat: <aqua>{0} <yellow>[<bold><light_purple>Değiştir</light_purple>]'
  freeze: '<yellow>Freeze mode: <aqua>{0} <yellow>[<light_purple><bold>Toggle<yellow>]'
  price: '<green>Fiyat: <aqua>{0} <yellow>[<bold><light_purple>Değiştir</light_purple>]'
  currency-hover: <yellow>Bu mağazanın kullandığı para birimini ayarlamak veya kaldırmak için tıklayınız
  lock: '<yellow>Shop lock: <aqua>{0} <yellow>[<light_purple><bold>Toggle<yellow>]'
  mode-selling: 'Market modu: <aqua>Satış <yellow>[<light_purple><bold>Değiştir<yellow>]'
  currency: '<green>Currency: <aqua>{0} <yellow>[<light_purple><bold>Set<yellow>]'
  setowner-hover: <yellow>Sahibi değiştirmek için tıkla.
  mode-buying: '<green>Market modu: <aqua>Alma <yellow>[<bold><light_purple>Değiştir</light_purple>]'
  item: '<green>Market Öğesi: {0} <yellow>[<bold><light_purple>Değiştir</light_purple>]'
  unlimited: '<green>Sınırsız: {0} <yellow>[<bold><light_purple>Değiştir</light_purple>]'
  unlimited-hover: <yellow>Mağaza sınırsızsa geçiş yapmak için tıkla.
  refill-hover: <yellow>Marketi doldurmak için tıkla.
  remove-hover: <yellow>Bu marketi kaldırmak için tıkla.
  toggledisplay-hover: <yellow>Mağazanın hologramının durumunu değiştir
  refill: '<green>Doldur: Eşyaları doldur <yellow>[<light_purple><bold>DOLDUR<yellow>]'
  freeze-hover: <yellow>Marketin dondurulma durumunu değiştir.
  lock-hover: <yellow>Marketin kilit korumasını aktif/deaktif et.
  item-hover: <yellow>Market öğesini değiştirmek için tıkla
  infomation: '<green>Market Kontrol paneli:'
  mode-selling-hover: <yellow>Marketi ALMA moduna değiştirmek için tıkla.
  empty-hover: '%eMarketin envanterini temizlemek için tıkla.'
  toggledisplay: '<green>Display-Item: <aqua>{0} <yellow>[<light_purple><bold>Toggle</bold></light_purple>]'
  history: '<green>Tarih: <yellow>[<bold><light_purple>Görünüm</light_purple></bold>]</yellow>'
  history-hover: <yellow>Mağaza geçmişi günlüklerini görüntülemek için tıklayın
timeunit:
  behind: arkasında
  week: "{0} hafta"
  weeks: "{0} haftalar"
  year: "{0} yıl"
  before: önce
  scheduled: Planlanan
  years: "{0} Yıllar"
  scheduled-in: '{0}''da planlandı'
  second: "{0} saniye"
  std-past-format: '{5}{4}{3}{2}{1}{0}önce'
  std-time-format: HH:mm:ss
  seconds: "{0} saniye"
  hour: "{0} saat"
  scheduled-at: '{0}''da planlandı'
  after: sonra
  day: "{0} gün"
  recent: son
  between: arasında
  hours: "{0} saat"
  months: "{0} ay"
  longtimeago: uzun zaman önce
  between-format: '{0} ile {1}arasında'
  minutes: "{0} dakika"
  justnow: az önce
  minute: "{0} dakika"
  std-format: 'AA / gg / yyyy HH: aa: ss'
  future-plain-text: gelecek
  month: "{0} ay"
  future: '{0} içinde'
  days: "{0} günler"
command:
  reloading: <green>Yapılandırma yeniden yüklendi. <yellow>Bazı değişikliklerin etkilenmesi için yeniden başlatma gerekebilir.
  description:
    buy: <yellow>Bir marketi <light_purple>ALMA<yellow> moduna geçirir
    about: <yellow>QuickShop bilgilerini gösterir
    language: <yellow>Geçerli dili değiştir
    purge: <yellow>Market temizleme görevini arka planda başlatır
    paste: <yellow>Sunucu verilerini Pastebin'e yükler
    title: <green>QuickShop yardım
    remove: <yellow>Baktığınız marketi kaldırır
    ban: <red>Bir oyuncuyu bu marketten yasaklar
    empty: <yellow>Bir marketteki bütün öğeleri kaldırır
    alwayscounting: <yellow>Set, eğer dükkan her zaman konteyner sayıyorsa bile sınırsızdır
    setowner: <yellow>Bir marketin sahibini değiştirir.
    reload: <yellow>QuickShop'un config.yml dosyasını yeniden yükler
    freeze: <yellow>Market takasını devre dışı bırak ya da aktifleştir
    price: <yellow>Bir marketin alım/satım fiyatını değiştirir
    find: <yellow>Belirli bir türde en yakındaki marketi bulur.
    create: <yellow>Hedef sandıkta yeni bir market oluşturur
    lock: <yellow>Marketin kilit statüsünü değiştir
    currency: <yellow>Marketin para birimi ayarlayın ya da kaldırın
    removeworld: <yellow>Belirtilen dünya içindeki TÜM marketleri kaldırır
    info: <yellow>QuickShop istatistiklerini göster
    owner: <yellow>Bir marketin sahibini değiştirir.
    amount: <yellow>Öğe miktarını belirlemek için (Sohbet sorunları varken kullanışlı)
    item: <yellow>Bir marketin market öğesini değiştir
    debug: <yellow>Geliştirici modunu aktive eder
    unlimited: <yellow>Bir markete sınırsız stok sağlar.
    sell: <yellow>Bir marketi <light_purple>SATMA<yellow> moduna geçirir
    fetchmessage: <yellow>Okunmamış market mesajlarını göster
    staff: <yellow>Market personellerini yönet
    clean: <yellow>Stoksuz bütün (yüklü) marketleri kaldırır
    refill: <yellow>Bir mağazaya belirli sayıda öğe ekler
    help: <yellow>QuickShop yardımını gösterir
    removeall: <yellow>Belirli bir oyuncunun BÜTÜN marketlerini kaldır
    unban: <yellow>Bir oyuncunun bu marketteki yasaklamasını kaldırır
    transfer: <yellow>Birinin TÜM marketlerini bir başkasına devret
    transferall: <yellow>Birinin TÜM marketlerini bir başkasına devret
    transferownership: <yellow>Baktığınız mağazayı başka birine devredin
    size: <yellow>Bir marketin her yığınının miktarını değiştir
    supercreate: <yellow>Bütün koruma kontrollerini atlayarak bir market oluştur
    taxaccount: <yellow>Alışveriş yapılırken verginin aktarılacağı hesabı ayarlar
    name: <yellow>Naming a shop to specific name
    toggledisplay: <yellow>Mağazadaki öğe durumunu değiştir
    permission: <yellow>Mağaza izin yönetimi
    lookup: <yellow>Aranabilir öğeler tablosunu yönetme
    database: <yellow>View and maintain QuickShop database
    benefit: <yellow>Avantajları mağaza sahibi ve diğer oyuncular arasında bölme ayarları
    tag: <yellow>Bir mağazanın etiketlerini ekleme, kaldırma veya sorgulama
    suggestprice: <yellow>Diğer mağazalara göre dükkan aramak için tavsiye edilen bir fiyat önerin
    history: <yellow>View the history transactions of a shop
    sign: <yellow>Change the sign material of a shop
  bulk-size-not-set: '<red>Kullanım: /qs size <miktar>'
  no-type-given: '<red>Kullanım: /qs find <item>'
  feature-not-enabled: Bu özellik yapılandırma dosyasında aktif değil.
  now-debuging: <green>Geliştirici modu başarıyla aktive edildi. QuickShop yeniden yükleniyor...
  no-amount-given: <red>Miktar sağlanmadı. <green>/qs refill <amount><red> komudunu kullan.
  no-owner-given: <red>Sahip sağlanmadı
  disabled: '<red>Bu komut aktif değil: <yellow>{0}'
  bulk-size-now: <green>Şimdi <yellow>{0} tane {1}<green> ticaret ediliyor
  toggle-always-counting:
    counting: <green>Shop artık her zaman konteyner sayıyor, hatta sınırsız
    not-counting: <green>Shop artık mağaza sınırsızsa saygı görüyor
  cleaning: <green>Stoksuz marketler kaldırılıyor...
  now-nolonger-debuging: <green>Geliştirici modu başarıyla devre dışı bırakıldı. QuickShop yeniden yükleniyor...
  toggle-unlimited:
    limited: <green>Market şimdi sınırlı
    unlimited: <green>Market şimdi sınırsız
  transfer-success-other: <yellow>{1} <green>oyuncusunun <yellow>{0} <green>marketi <yellow>{2} <green>oyuncusuna devredildi
  no-trade-item: <green>Lütfen değiştirmek için elinizde ticaret edilecek öğeyi tutunuz
  wrong-args: <red>Geçersiz argüman. Komutların listesini görüntülemek için <bold>/qs help <red>komudunu kullan.
  some-shops-removed: <yellow>{0} <green>market kaldırıldı
  new-owner: '<green>Yeni sahip: <yellow>{0}'
  format: <green>/{0} {1} <yellow>- {2}
  transfer-success: <yellow>{0} <green>market <yellow>{1} <green>oyuncusuna devredildi
  now-buying: <green>Şimdi <yellow>{0} <light_purple>ALIYOR
  now-selling: <green>Şimdi <yellow>{0} <aqua>SATILIYOR
  cleaned: <yellow>{0}<green> market kaldırıldı.
  trade-item-now: <green>Şimdi <yellow>{0} tane {1}<green> ticaret ediliyor
  no-world-given: <red>Lütfen bir dünya adı belirtin
  format-disabled: <red>/{0} {1} <gray>- {2}
  invalid-bulk-amount: <red>Verilen değer {0} maks yığın miktarından fazla veya birinden az
currency-not-support: <red>Ekonomi eklentisi çoklu-ekonomi özelliğini desteklemiyor.
trading-in-creative-mode-is-disabled: <red>Yaratıcı modda iken bu marketle ticaret yapamazsın.
the-owner-cant-afford-to-buy-from-you: <red>Bu {0} ediyor ama market sahibinde sadece {1} var
you-cant-afford-shop-naming: <red>You can't afford shop naming, it costs {0} to naming.
inventory-error: |-
  <red>Failed to processing the InventoryWrapper. Did you using addon to re-binding the shop Inventory? Information: Exception={0}, InventoryWrapper={1}, WrapperType={2}, WrapperProvider={3}, SymbolLink={4}. Please contact the server administrators.
integrations-check-failed-create: <red>{0} entegrasyonu market oluşumunu reddetti
shop-out-of-space: <dark_purple>{0}, {1}, {2} koordinatlarındaki marketiniz doldu.
admin-shop: AdminMarketi
no-anythings-in-your-hand: <red>Elinde hiçbir şey yok.
no-permission: <red>Bunu yapmaya iznin yok.
chest-was-removed: <red>Sandık kaldırıldı.
you-cant-afford-to-buy: <red>Bu {0} ediyor, ama sende sadece {1} var
shops-removed-in-world: <aqua>{1}<yellow> dünyasından toplam <aqua>{0}<yellow> market silindi.
display-turn-off: <green>Mağaza ekranını başarıyla kapatın.
client-language-unsupported: <yellow>QuickShop, istemci dilinizi desteklemiyor, artık {0} yerel ayarına geri döndük.
language-version: '63'
not-managed-shop: <red>Bu marketin sahibi veya moderatörü değilsin
shop-cannot-trade-when-freezing: <red>Bu markette ticaret yapamazsın çünkü donmuş.
invalid-container: <red>Invalid container, You can only create the shop on block who have inventory.
permission:
  header: <green>Mağaza İzin Detayları
  header-player: '<green>Şunun için Mağaza İzni Ayrıntıları: {0}'
  header-group: '<green>Grup için Mağaza İzni Ayrıntıları: {0}'
  table: <gold><b>{0}</b>:</gold> <gray>{1}</gray>
  item:
    purchase: <yellow>Permission to allow users who have this to purchase the shop. (including buying and selling)
    show-information: <yellow>Buna sahip kullanıcıların mağaza bilgilerini görmesine izin vermek için İzin'i. (mağaza bilgi panelini açın)
    preview-shop: <yellow>Buna sahip kullanıcıların mağazayı önizlemesine izin vermek için İzin'i. (önizleme öğesi)
    search: <yellow>Permission to allow users who have this to search the target shop. (remove permission will hide shop from search result)
    delete: <yellow>Buna sahip kullanıcıların mağazayı önizlemesine izin vermek için İzin'i. (önizleme öğesi).
    receive-alert: <yellow>Permission to allow users who have this to receive alert message (e.g out-of-stock or new trade).
    access-inventory: <yellow>Buna sahip kullanıcıların mağaza envanterine erişmesine izin vermek için İzin'i.
    ownership-transfer: <yellow>Buna sahip kullanıcıların mağaza sahipliğini aktarmasına izin verme izni.
    management-permission: <yellow>Permission to allow users who have this to manage the groups permissions and edit user's group.
    toggle-display: <yellow>Buna sahip kullanıcıların mağaza vitrini öğesini değiştirmesine izin vermek için İzin'i tıklayın.
    set-shoptype: <yellow>Permission to allow users who have this to set the shop type (switch buying or selling).
    set-price: <yellow>Buna sahip kullanıcıların mağaza fiyatını ayarlamasına izin vermek için İzin'i tıklayın.
    set-item: <yellow>Buna sahip kullanıcıların mağaza öğesini ayarlamasına izin vermek için İzin'i.
    set-stack-amount: <yellow>Buna sahip kullanıcıların mağaza yığını tutarını ayarlamasına izin vermek için izin.
    set-currency: <yellow>Buna sahip kullanıcıların mağaza para birimini ayarlamasına izin vermek için İzin'i.
    set-name: <yellow>Buna sahip kullanıcıların mağaza adını ayarlamasına izin vermek için İzin'i.
    set-sign-type: <yellow>Mağazaya yapıştırılan işaret malzemesini değiştirin.
    view-purchase-logs: <yellow>Mağaza satın alma günlüklerini görüntüleme izni.
  group:
    everyone: <yellow>Default group for all users excepted shop owner.
    staff: <yellow>System group for shop staffs.
    administrator: <red>System group for administrators, the users in this group will have permissions almost same with shop owner.
invalid-group: <red>Geçersiz grup adı.
invalid-permission: <red>Geçersiz izin.
invalid-operation: <red>Geçersiz işlem, yalnızca {0} izin verilir.
player-no-group: <yellow>Oyuncu {0} bu dükkandaki herhangi bir grupta yer almıyor.
player-in-group: <green>Player {0} grupta <aqua>{1}</aqua> bu dükkanda.
permission-required: <red>Bunu yapmak için bu dükkanda {0} izniniz yok.
no-permission-detailed: <red>Bunu yapmak için izniniz <yellow>{0}</yellow> yok.
paste-notice: "<yellow>Note: If you are creating a Paste for troubleshooting purposes, be sure to reproduce the error before creating the Paste as quickly as possible; we need the logs retained briefly in the buffer to troubleshoot. If you create a Paste too slowly or without first reproducing the error or restarted server, the Paste will record nothing and useless."
paste-uploading: <aqua>Lütfen bekleyiniz... Yapıştırmayı pastebin'e yükleniyor......
paste-created: '<green>Paste created, click to open in browser: <yellow>{0}</yellow><newline><red>Warning: <gray>Never send paste to who you don''t trust.'
paste-created-local: |-
  <green>Paste created and saved to your local disk at: {0}<newline><red>Warning: <gray>Never send paste to who you don't trust.
paste-created-local-failed: <red>Yerel diskinize yapıştırma kaydedilemedi, lütfen diskinizi kontrol edin.
paste-451: |-
  <gray><b>TIPS: </b> Your current country or region seems to have blocked the CloudFlare Workers service and QuickShop Paste may not be loaded properly.<newline><gray>If the subsequent operation fails, try adding the --file additional parameter to generate a local Paste: <dark_gray>/qs paste --file</dark_gray>
paste-upload-failed: <red>Failed to upload paste to {0}, trying other pastebin provider...
paste-upload-failed-local: <red>Failed to upload paste, trying generate local paste...
command-incorrect: '<red>Komut kullanımı yanlış, yardımı denetlemek için /qs help yazın. Kullanım: {0}.'
successfully-set-player-group: <green>Oyuncu {0} grubu başarıyla <aqua>{1}</aqua>olarak ayarlayın.
successfully-unset-player-group: <green>Successfully remove player group in this shop.
successfully-set-player-permission: <green>Mağaza {0} oyuncu <aqua>{1}</aqua> izin <aqua>{2}</aqua>başarıyla ayarlayın.
lookup-item-created: <green>An item named <aqua>{0}</aqua> has been created in the lookup table. You can refer this item in configurations now.
lookup-item-exists: <red>Adlı bir öğe <yellow>{0}</yellow> adlı bir öğe zaten var, silin veya başka bir ad seçin.
lookup-item-not-found: <red>An item named <yellow>{0}</yellow> not exists.
lookup-item-name-illegal: <red>Item name illegal. Only alphanumeric characters and underscores are allowed.
lookup-item-name-regex: '<red>Ad şu regex''le eşleşmelidir: <aqua>{0}</aqua>.'
lookup-item-test-not-found: '<gold>Test: <yellow>The item in your hand not registered in lookup table.'
lookup-item-test-found: '<gold>Test: <green>The item in your hand was registered as name <aqua>{0}</aqua> in lookup table.'
lookup-item-removed: <green>The specified item <aqua>{0}</aqua> has been successfully removed from lookup table.
internal-error: <red>Bir iç hata oluştu, lütfen sunucu yöneticisine başvurun.
argument-cannot-be: <red>The argument <aqua>{0}</aqua> cannot be <yellow>{1}</yellow>.
argument-must-between: <red>The argument <aqua>{0}</aqua> value must between <yellow>{1}</yellow> and <yellow>{2}</yellow>
invalid-percentage: <red>Percentage invalid, you must add '%' after the number of percentage.
display-fallback: |-
  <red>Due to an internal failure, a fallback message is being displayed.
  The value of this item may not be localized or processed correctly.
  Please contact the server administrator.
not-a-valid-time: |-
  <red>The string <yellow>{0}</yellow> not a valid timestamp, please enter a <aqua>Zulu Time (ISO 8601)</aqua> or <aqua>Unix Epoch Time in seconds</aqua>.
  <gold>Valid timestamp example: (for Sat, 17 Dec 2022 10:31:37 GMT)</gold>
  <aqua>-<aqua> <yellow>2022-12-17T10:31:37Z</yellow> <grey>(Zulu Time)</grey>
  <aqua>-<aqua> <yellow>1671273097</yellow> <grey>(Unix Epoch Time in seconds)</grey>
invalid-past-time: <red>You can't specify a time in the past.
debug:
  arguments-invalid: <red>Provided arguments <yellow>{0}</yellow> invalid.
  sign-located: '<green>Sign valid: <yellow>{0}</yellow>.'
  operation-missing: <red>You must specific an operation.
  operation-invalid: <red>You must specific an valid operation.
  invalid-base64-encoded-sql: <red>The SQL provided must be base64 encoded.
  warning-sql: |-
    <red><bold>Warning:</bold></red><yellow>You're executing a SQL statement, this may corrupt your database or destroy any data in database even it belongs to other plugins.
    <red>Don't confirm if you don't trust who send you this command.
  warning-sql-confirm: <yellow>To confirm this dangerous operation, please type <aqua>/qs debug database sql confirm {0}</aqua> in 60 seconds.
  warning-sql-confirm-hover: <yellow>Click to confirm this dangerous operation.
  sql-confirm-not-found: <yellow>Didn't found the operation that you given, it may invalid or expired.
  sql-executing: '<yellow>SQL deyimini yürütme: <aqua>{0}</aqua>'
  sql-completed: <green>Completed, {0} rows affected.
  sql-exception: <red>An error occurred while executing the SQL query, check the Console for details!
  sql-disabled: '<red>For security reason, SQL queries are disabled in this server, If you really need this feature, you can add the following flag to startup arguments: <aqua>{0}</aqua> with `true` value to enable it.'
  force-shop-reload: <yellow>Forcing reload all loaded shops...
  force-shop-reload-complete: <green>Forced <aqua>{0}</aqua> shops to be reloaded.
  force-shop-loader-reload: <yellow>Forcing shop-loader reloading...
  force-shop-loader-reload-unloading-shops: <yellow>Unloading <aqua>{0}</aqua> loaded shops...
  force-shop-loader-reload-unloading-shops-from-memory: <yellow>Removing <aqua>{0}</aqua> shops from memory...
  force-shop-loader-reload-reloading-shop-loader: <yellow>Re-calling shop-loader for reloading all shops from database...
  force-shop-loader-reload-complete: <green>Shop-loader has been reloaded all shops!
  toggle-shop-loaded-status: <aqua>Toggle this shop loaded status to <gold>{0}</gold>
  shop-internal-data: '<yellow>The internal data of this shop: <reset>{0}</reset>'
  handler-list-not-valid-bukkit-event-class: <red>The provided class <yellow>{0}</yellow> is not a valid Bukkit event class.
  update-player-shops-signs-no-username-given: <red>You must provide a valid player username.
  update-player-shops-signs-create-async-task: <yellow>Creating async tasks for force updating signs...
  update-player-shops-player-selected: '<yellow>Player selected: <gold>{0}</gold>'
  update-player-shops-player-shops: <yellow>Total <gold>{0}</gold> shops waiting for updates.
  update-player-shops-per-tick-threshold: '<yellow>Max shops can be update per tick: <gold>{0}</gold>'
  update-player-shops-complete: <green>Task completed, Used <yellow>{0}ms</yellow> for updating.
  update-player-shops-task-started: <gold>The tasks has been started, please wait for it to complete.
  item-info-store-as-string: "<green>The store you looking at: <gold>{0}</gold> Hash: <white>{1}</white>"
  item-info-hand-as-string: "<green>The item in your hand: <gold>{0}</gold> Hash: <white>{1}</white>"
  item-matching-result: "<green>Hand2Store: <aqua>{0}</aqua>, Store2Hand: <aqua>{1}</aqua>"
  hikari-cp-size-tweak: "<green>HikariCP MaximumPoolSize and MinimumIdle was set to <white>{0}</white>"
  hikari-cp-testing: "<green>Please wait, testing HikariCP connection..."
  hikari-cp-working: "<green>Pass! HikariCP working well!"
  hikari-cp-not-working: "<red>Failed! The connection that returned by HikariCP is dead! (Not passed test in 1 second)"
  hikari-cp-timeout: "<red>HikariCP is timed out while getting a valid connection, please clean up all active queries to release connection resources."
  queries-stopped: "<green>Stopped <white>{0}</white> active queries."
  queries-dumping: "<yellow>Dumping active queries..."
  restart-database-manager: "<yellow>Restarting SQLManager..."
  restart-database-manager-clear-executors: "<yellow>Clearing executors..."
  restart-database-manager-unfinished-task: "<yellow>Unfinished task: <white>{0}</white>"
  restart-database-manager-unfinished-task-history-query: "<yellow>Unfinished task (History Query): <white>{0}</white>"
  restart-database-manager-reconnect: "<yellow>Re-launching SQLManager via initial sequence (via async executor)"
  restart-database-manager-done: "<green>Done!"
  property-incorrect: "<yellow>You must enter a (and only one) property key=value set. E.g   aaa=bbb"
  property-security-block: "<red>Request was rejected, for security reason, you can only change the property that startsWith <aqua>com.ghostchu.quickshop</aqua> or <aqua>quickshop</aqua>."
  property-removed: "<green>Removed property key <white>{0}</white>"
  property-changed: "<green>Property key <white>{0}</white> was changed from <white>{1}</white> to <white>{2}</white>"
  marked-as-dirty: "<green>Marked all stores as dirty status, they will be force save in next auto-save task. (Restart the server to force execute store saving task)"
  display-removed: "<green>Successfully removed <yellow>{0}</yellow> QuickShop display items/entities from the worlds."
database:
  scanning: <green>Scanning the isolated data in QuickShop databases, the database load may increase in scan progress, this might need a while...
  scanning-async: <yellow>Scanning the isolated data in QuickShop databases on async task thread, the database load may increase in scan progress, this might need a while, try again later.
  already-scanning: <red>An scan task has been started, please wait until it finished.
  trim-warning: '<yellow><red><bold>Warning: </red>Backup your database before continue database trim to avoid the data loss. Once you get ready, execute <aqua>/qs database trim confirm</aqua> to continue.'
  status: '<yellow>Durumu: {0}'
  status-good: <green>İyi
  status-bad: <yellow>Maintenance Required
  isolated: '<yellow>Yalıtılmış Veriler:'
  isolated-data-ids: '<yellow><aqua>└</aqua> Data Records: <gold>{0}</gold>'
  isolated-shop-ids: '<yellow><aqua>└</aqua> Shop Indexes: <gold>{0}</gold>'
  isolated-logs: '<yellow><aqua>└</aqua> Logs: <gold>{0}</gold>'
  isolated-external-caches: '<yellow><aqua>└</aqua> External Caches: <gold>{0}</gold>'
  last-purge-time: <yellow> {0}'daki son tarama zamanı
  report-time: <yellow> {0}'daki son tarama zamanı
  auto-scan-alert: <yellow>The QuickShop database on this server require a maintenance. Found <gold>{0}</gold> isolated data wait for trimming.
  auto-trim: <green>Auto trim has been enabled on this server, no manually trim required.
  trim-complete: <green>Database trim completed, <yellow>{0}</yellow> isolated trimmed.
  auto-trim-started: <green>Auto trim has been started, please wait...
  trim-start: <green>Database trim started, please wait...
  trim-exception: <red>Database trim failed, an exception raised during database trimming, check the server console.
  generated-at: '<yellow>Generated at: <gold>{0}</gold>'
  purge-date: <red>You must given a date.
  purge-warning: <yellow>Bu işlem, mağaza oluşturma/değiştirme/silme, satın alma, işlem ve sistem günlükleri dahil olmak üzere veritabanınızda depolanan geçmişi temizleyecektir. Bu verileri silmek disk alanını boşaltabilir, ancak tüm geçmiş ölçümleri kaybolur ve ölçümlere bağlı diğer eklentiler çalışmayı durdurur. Bu işlemi yürütmeye devam etmek için '/quickshop database purgelogs \<before-days> Onaylamak'
  purge-task-created: <green>Task created! Database are silent purging history records in background.
  purge-done-with-line: <green>Purge task completed, total <gold>{0}</gold> lines purged from database.
  purge-done-with-error: <red>Purge task failed, check the server console for details.
  purge-players-cache: <yellow>Please wait, purging players caches...
  purge-players-completed: |-
    <green>Successfully purged {0} players caches from both memory and database.<newline><aqua>Note: You server performance may impact by this operation.
  purge-players-error: <red>Failed to purge players caches, please check the server console.
  suggestion:
    trim: <yellow>This database require a isolated data trim, execute command <aqua>/qs database trim</aqua> to trim the database.
always-counting-removal-early-warning: <red>Always Counting feature is scheduled for removal, you shouldn't use it anymore and will stop work in future.
exporting-database: <green>Veritabanı dışa aktarılıyor, lütfen bekleyin...
exporting-failed: <red>Dışa aktarılamadı, lütfen sunucu konsolunu kontrol edin.
exported-database: <green>Veritabanı <yellow>{0}</yellow>'a verildi.
importing-not-found: <red>The <yellow>{0}</yellow> file not found, please check the file path.
importing-early-warning: |-
  <yellow><red><bold>Warning: </bold></red>The backup will be imported to the current database, any exists data will be purged and permanently lost if you don't have a backup.<newline><red>Are you sure to continue the import progress?</red><yellow>Type <aqua>/qs recovery confirm</aqua> to continue.
importing-database: <green>Veritabanı dışa aktarılıyor, lütfen bekleyin...
importing-failed: <red>Dışa aktarılamadı, lütfen sunucu konsolunu kontrol edin.
imported-database: <green>Veritabanı <yellow>{0}</yellow>'a verildi.
transfer-sent: <green>Shops transfer request sent to <yellow>{0}</yellow>.
transfer-request: <yellow>Player <aqua>{0}</aqua> requested to transfer their shops to you. Did you want accept the transfer?
transfer-single-request: <yellow>Player <aqua>{0}</aqua> would like to transfer a shop to you. Do you want to accept the request?
transfer-ask: |-
  <gold>Type <red>/qs transfer accept</red> to accept.</gold><newline><gold>Type <red>/qs transfer deny</red> to deny.</gold><newline><gold>Request will expire after <red>{0}</red> seconds.
transferall-ask: |-
  <gold>Type <red>/quickshop transferall accept</red> to accept or <red>/quickshop transferall deny</red> to deny.
  The request will expire after <red>{0}</red> seconds.
transfer-single-ask: |-
  <gold>Type <red>/qs transfer accept</red> to accept.</gold><newline><gold>Type <red>/qs transfer deny</red> to deny.</gold><newline><gold>Request will expire after <red>{0}</red> seconds.
transfer-accepted-fromside: <green>Player <aqua>{0}</aqua> accepted your shop transfer request.
transfer-accepted-toside: <green>You accepted <aqua>{0}</aqua>'s transfer request.
transfer-rejected-fromside: <red>Player <aqua>{0}</aqua> rejected your shop transfer request.
transfer-rejected-toside: <red>You rejected <aqua>{0}</aqua>'s shop transfer request.
transfer-no-pending-operation: <red>You don't have pending transfer request yet.
transfer-no-self: <red>You can't transfer your shops to yourself.
benefit-overflow: <red>The sum of all benefits cannot be greater than or equal to 100%.
benefit-exists: <red>Target player already in benefits list on this shop.
benefit-removed: <red>Target player has been removed from shop benefits.
benefit-added: <green>Player <aqua>{0}</aqua> has been added into shop benefits!
benefit-updated: <green>Player <aqua>{0}</aqua>'s benefits has been updated!
benefit-query: <green>This shop have <yellow>{0}</yellow> players in benefits list!
benefit-query-list: <yellow> - </yellow><white>Player <gold>{0}</gold>, Benefit <gold>{1}%</gold></white>
tag-added: '<green>Successfully added <aqua>#{0}</aqua> to this shop!'
tag-add-duplicate: '<red>The tag <aqua>#{0}</aqua> already exists at this shop!'
tag-removed: '<green>Successfully removed <aqua>#{0}</aqua> from this shop!'
tag-remove-not-exists: 'The tag <aqua>#{0}</aqua> not exists at this shop!'
tag-cleared: <green>Successfully cleared all tags from this shop!
tag-shops-cleared: '<green>Successfully cleared <aqua>#{0}</aqua> from all your tagged shops!'
tag-query: '<green>This shop have <yellow>{0}</yellow> tags:'
tag-query-listing: '<yellow> - <aqua>#{0}</aqua>'
tag-query-no-tag: <red>This shop have no tags.
tag-query-shops: '<green>This tag contains <yellow>{0}</yellow> shops:'
tag-query-shops-listing: <yellow> - <aqua>{0}</aqua>
batch-operations-based-on-tags-no-failure: <green>Successfully batch processed <yellow>{0}</yellow> shops.
batch-operations-based-on-tags-have-failure: <yellow>Total <yellow>{0}</yellow> shops in batch processing successfully completed, but <red>{1}</red> requests not completed.
batch-operations-based-on-tags-have-failure-with-reason: '<yellow>Total <yellow>{0}</yellow> shops in batch processing successfully completed, but <red>{1}</red> requests not completed. Reason: <gold>{2}</gold>'
batch-operations-based-on-tags-have-failure-error-messages-may-appears-above: <gray>The error messages will send to you above this message in the chat.
addon:
  towny:
    commands:
      town: <yellow>Set or unset a shop to a Town shop
      nation: <yellow>Set or unset a shop to a Nation shop
    make-shop-owned-by-town: <green>You have made the shop owned by town <yellow>{0}</yellow>.
    make-shop-no-longer-owned-by-town: <green>You have reset shop ownership, it now transfer back to original shop owner.
    make-shop-owned-by-nation: <green>You have made the shop owned by nation <yellow>{0}</yellow>.
    make-shop-no-longer-owned-by-nation: <green>You have reset shop ownership, it now transfer back to original shop owner.
    shop-owning-changing-notice: <grey>This shop now owned by a town/nation, original shop owner has been automatically added into administrator list, modify or add any new owner / staff by using command /qs permission
    target-shop-already-is-town-shop: <red>Hedef dükkan zaten bir kasabaya ait.
    target-shop-already-is-nation-shop: <red>Hedef dükkan zaten bir kasabaya ait.
    target-shop-not-in-town-region: <red>Hedef dükkan kasabada değil.
    target-shop-not-in-nation-region: <red>Hedef dükkan kasabada değil.
    item-not-allowed: <red>This shop's item are not allowed used by town/nation shop, pick another one!
    operation-disabled-due-shop-status: <red>This shop operation has been disabled due this already is a town/nation shop.
    plot-type-disallowed: <red>You can't create town/nation shop on this type of plot.
    flags:
      own: <red>You can only create shops at your owned shop type towny plot.
      modify: <red>You didn't have build permission on this towny plot.
      shop-type: <red>You must create shop on a shop type towny plot.
  residence:
    creation-flag-denied: <red>You don't have permission to create shops in this residence.
    trade-flag-denied: <red>You don't have permission to purchase in this residence.
    you-cannot-create-shop-in-wildness: <red>Issız bölgelerde mağaza yaratamazsınız.
  griefprevention:
    creation-denied: <red>Burada bir market oluşturma izniniz yok.
    trade-denied: <red>You don't have permission to purchase shops in this claim.
  lands:
    world-not-enabled: <red>You are not allowed to create or purchase in this world.
    creation-denied: <red>You don't have permission to create shops in this Land area.
  plotsquared:
    no-plot-whitelist-creation: <red>You cannot create shop outside of Plot.
    no-plot-whitelist-trade: <red>You cannot purchase shop outside of Plot.
    creation-denied: <red>Bu arsada market oluşturma izniniz yok.
    trade-denied: <red>You don't have permission to purchase shops in this Plot.
    flag:
      create: Create QuickShop-Hikari shops
      trade: Purchase QuickShop-Hikari shops
  superiorskyblock:
    owner-create-only: <red>Sadece ada sahibi orada dükkan açabilir.
    owner-member-create-only: <red>Sadece ada sahibi veya üyesi orada dükkan oluşturabilir.
  worldguard:
    creation-flag-test-failed: <red>Bu WorldGuard bölgesinde market oluşturma izniniz yok.
    trade-flag-test-failed: <red>Bu WorldGuard bölgesinde takas izniniz yok.
    reached-per-region-amount-limit: "<red>You have reached the maximum amount of shops in this region."
  discord:
    __to_translators: 'To translators: `addon.discord.discord-messages` use a special variable in strings, %%LANG_KEY%% instead of QuickShop''s style {LANG_INDEX}, do not translate anything inside double ''%'' section. BUT for any other section texts, it respect the normal QuickShop Text System syntax ({0},{1} etc.) This may be difficult to understand because two different variable forms are used in the same program at the same time, but trust me, it is reasonable. (you don''t need translate this string, it''s a comment)'
    __to_users: Like the QuickShop Text System, Discord addon will also auto-detect user language and use user's language to send Discord message, it following the QuickShop-Hikari's language system settings.
    __to_message_designer: 'Design your Discord message with GUI: https://glitchii.github.io/embedbuilder/, then copy the JSON code and paste it into the translation and here we go!'
    discord-enabled: <aqua>Successfully <green>enabled</green> your QuickShop discord messages, now you can receive the shop messages from Discord.
    discord-disabled: <aqua>Successfully <red>disabled</red> your QuickShop discord messages, now you won't receive the shop messages from Discord anymore.
    discord-not-integrated: <red>You hadn't link your Discord yet! Please link your Discord account first!
    feature-enabled-for-user: <aqua>You have <green>enabled</green> <gold>{0}</gold> notification.
    feature-disabled-for-user: <aqua>You have <red>disabled</red> <gold>{0}</gold> notification.
    link-help: <yellow>This server using <gold>{0}</gold> for Discord driver, please use <green>{0}</green> to link your Discord account.
    save-notifaction-exception: <red>An error occurred while saving your Discord notifaction settings, please contact the server administrator.
    feature-status-changed: <green>Successfully set the notifaction <aqua>{0}</aqua> status to <gold>{1}</gold>
    commands:
      discord:
        description: <yellow>Managing your QuickShop Discord settings
    discord-messages:
      sold-to-your-shop: |
        {
         "embed":
           {
             "title": ":inbox_tray: Somebody sold items to your shop",
             "description": "The player %%purchase.name%% sold x%%purchase.amount%% %%shop.item.name%% to your shop.",
             "color": 52084,
             "author": {
               "name": "QuickShop-Hikari",
               "icon_url": ""
             },
             "thumbnail": {
               "url": ""
             },
             "image": {
               "url": ""
             },
             "footer": {
               "text": "QuickShop-Hikari Discord Notification",
               "icon_url": ""
             },
             "fields": [
               {
                 "name": "Shop",
                 "value": "%%shop.display-name%%",
                 "inline": false
               },
               {
                 "name": "Purchaser",
                 "value": "%%purchase.name%%",
                 "inline": true
               },
               {
                 "name": "Item",
                 "value": "%%shop.item.name%%",
                 "inline": true
               },
               {
                 "name": "Amount",
                 "value": "%%purchase.amount%% (x%%shop.stacking-amount%%)",
                 "inline": true
               },
               {
                 "name": "Your paid",
                 "value": "%%purchase.balance-formatted%%",
                 "inline": true
               },
               {
                 "name": "Taxes",
                 "value": "%%purchase.taxes-formatted%%",
                 "inline": true
               }
             ]
           }
        }
      bought-from-your-shop: |
        {
           "embed":
             {
               "title": ":outbox_tray: Somebody bought items from shop",
               "description": "The player %%purchase.name%% bought x%%purchase.amount%% %%shop.item.name%% from your shop.",
               "color": 52084,
               "author": {
                   "name": "QuickShop-Hikari",
                   "icon_url": ""
               },
               "thumbnail": {
                   "url": ""
               },
               "image": {
                   "url": ""
               },
               "footer": {
                   "text": "QuickShop-Hikari Discord Notification",
                   "icon_url": ""
               },
               "fields": [
                   {
                       "name": "Shop",
                       "value": "%%shop.display-name%%",
                       "inline": false
                   },
                   {
                       "name": "Purchaser",
                       "value": "%%purchase.name%%",
                       "inline": true
                   },
                   {
                       "name": "Item",
                       "value": "%%shop.item.name%%",
                       "inline": true
                   },
                   {
                       "name": "Amount",
                       "value": "%%purchase.amount%%(x%%shop.stacking-amount%%)",
                       "inline": true
                   },
                   {
                       "name": "Your earn",
                       "value": "%%purchase.balance-formatted%%",
                       "inline": true
                   },
                   {
                       "name": "Taxes",
                       "value": "%%purchase.taxes-formatted%%",
                       "inline": true
                   }
               ]
           }
         }
      mod-shop-purchase: |
        {
        "embed":
          {
              "title": ":dollar: Somebody purchased in a shop",
              "description": "The player %%purchase.name%% purchase x%%purchase.amount%% %%shop.item.name%% from the shop.",
              "color": 6061450,
              "author": {
                  "name": "QuickShop-Hikari",
                  "icon_url": ""
              },
              "thumbnail": {
                  "url": ""
              },
              "image": {
                  "url": ""
              },
              "footer": {
                  "text": "QuickShop-Hikari Discord Notification",
                  "icon_url": ""
              },
              "fields": [
                  {
                      "name": "Shop",
                      "value": "%%shop.display-name%%",
                      "inline": false
                  },
                  {
                      "name": "Purchaser",
                      "value": "%%purchase.name%%",
                      "inline": true
                  },
                  {
                      "name": "Item",
                      "value": "%%shop.item.name%%",
                      "inline": true
                  },
                  {
                      "name": "Amount",
                      "value": "%%purchase.amount%% (x%%shop.stacking-amount%%)",
                      "inline": true
                  },
                  {
                      "name": "Balance",
                      "value": "%%purchase.balance-formatted%%",
                      "inline": true
                  },
                  {
                      "name": "Taxes",
                      "value": "%%purchase.taxes-formatted%%",
                      "inline": true
                  }
              ]
          }
        }
      out-of-space: |
        {
        "embed":
          {
              "title": ":mailbox_with_mail: Your shop has run out of space",
              "description": "Your shop inventory is full of items!\nYou need empty your shop container to free the space then you can continue to accept new items.",
              "color": 16065893,
              "author": {
                  "name": "QuickShop-Hikari",
                  "icon_url": ""
              },
              "thumbnail": {
                  "url": ""
              },
              "image": {
                  "url": ""
              },
              "footer": {
                  "text": "QuickShop-Hikari Discord Notification",
                  "icon_url": ""
              },
              "fields": [
                  {
                      "name": "Shop",
                      "value": "%%shop.display-name%%",
                      "inline": false
                  }
              ]
          }
        }
      out-of-stock: |
        {
            "embed":
                {
                    "title": ":mailbox_with_no_mail: Your shop has run out of stock",
                    "description": "Your shop inventory is empty now!\nYou need to refill the shop container with items to continue selling items!",
                    "color": 16065893,
                    "author": {
                        "name": "QuickShop-Hikari",
                        "icon_url": ""
                    },
                    "thumbnail": {
                        "url": ""
                    },
                    "image": {
                        "url": ""
                    },
                    "footer": {
                        "text": "QuickShop-Hikari Discord Notification",
                        "icon_url": ""
                    },
                    "fields": [
                        {
                            "name": "Shop",
                            "value": "%%shop.display-name%%",
                            "inline": false
                        }
                    ]
                }
        }
      mod-shop-created: |
        {
          "embed": {
            "title": ":classical_building: A new shop has been created",
            "description": "Player created a new shop on your server!",
            "color": 6061450,
            "author": {
              "name": "QuickShop-Hikari",
              "icon_url": ""
            },
            "thumbnail": {
              "url": ""
            },
            "image": {
              "url": ""
            },
            "footer": {
              "text": "QuickShop-Hikari Discord Notification",
              "icon_url": ""
            },
            "fields": [
              {
                "name": "Shop",
                "value": "%%shop.display-name%%",
                "inline": false
              },
              {
                "name": "Owner",
                "value": "%%shop.owner.name%%",
                "inline": true
              },
              {
                "name": "Item",
                "value": "%%shop.item.name%%",
                "inline": true
              },
              {
                "name": "Amount",
                "value": "%%shop.stacking-amount%%",
                "inline": true
              },
              {
                "name": "Type",
                "value": "%%shop.type%%",
                "inline": true
              }
            ]
          }
        }
      mod-remove-shop: |
        {
            "embed": {
                "title": ":recycle: A shop was removed from this server",
                "description": "A shop was removed from this server.",
                "color": 6061450,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "Reason",
                        "value": "%%delete.reason%%",
                        "inline": false
                    }
                ]
            }
        }
      shop-transfer-to-you: |
        {
            "embed": {
                "title": ":placard: A shop was transfer to you",
                "description": "A shop transfer to you from another player.",
                "color": 16172079,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "From",
                        "value": "%%transfer.from%%",
                        "inline": false
                    }
                ]
            }
        }
      mod-shop-transfer: |
        {
            "embed": {
                "title": ":placard: A shop was transferred",
                "description": "A shop transfer to a player from another player.",
                "color": 6061450,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "From",
                        "value": "%%transfer.from%%",
                        "inline": true
                    },
                    {
                        "name": "To",
                        "value": "%%transfer.to%%",
                        "inline": true
                    }
                ]
            }
        }
      shop-price-changed: |
        {
            "embed": {
                "title": ":money_with_wings: Your shop price was changed",
                "description": "You or your shop staff changed your shop price.",
                "color": 16172079,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "From",
                        "value": "%%change-price.from%%",
                        "inline": true
                    },
                    {
                        "name": "To",
                        "value": "%%change-price.to%%",
                        "inline": true
                    }
                ]
            }
        }
      mod-shop-price-changed: |
        {
            "embed": {
                "title": ":money_with_wings: A shop price has been changed",
                "description": "A shop changed its price settings.",
                "color": 6061450,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": true
                    },
                    {
                        "name": "Owner",
                        "value": "%%shop.owner.name%%",
                        "inline": true
                    },
                    {
                        "name": "",
                        "value": "",
                        "inline": true
                    },
                    {
                        "name": "From",
                        "value": "%%change-price.from%%",
                        "inline": true
                    },
                    {
                        "name": "To",
                        "value": "%%change-price.to%%",
                        "inline": true
                    }
                ]
            }
        }
      shop-permission-changed: |
        {
            "embed": {
                "title": ":closed_lock_with_key: Your shop permission settings has been changed",
                "description": "One of your shop's permission setting has been changed.",
                "color": 15879747,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "Player",
                        "value": "%%change-permission.player%%",
                        "inline": true
                    },
                    {
                        "name": "Assign to group",
                        "value": "%%change-permission.to-group%%",
                        "inline": true
                    },
                    {
                        "name": "",
                        "value": "",
                        "inline": true
                    },
                    {
                        "name": "Permission granted (Inherit from group)",
                        "value": "```\n%%change-permission.perms-list%%\n```",
                        "inline": false
                    }
                ]
            }
        }
    field:
      player: Oyuncu
      item: Eşya
      amount: Adet
      balance: Bakiye
      balance-after-tax: Bakiye (vergi sonrası)
      account: Hesap bakiyeniz
      taxes: Vergiler
      cost: Ücret
  discount:
    commands:
      discount:
        description: <yellow>İndirim kodu uygulayın veya indirim kodlarınızı yönetin.
    tab-complete:
      discount:
        general:
          code: <code>
        create:
          rate: |
            Komut İpucu:
            Argüman: <rate>
            Açıklama: Kazanacağınız gerçek yüzde veya para
            Giriş '%30' = fiyat * 0,3
            Giriş '50' = fiyat - 50
          max-usage: |
            Command Hint:
            Argument: [max-usage]
            Description: The times that code can be used
            `-1` for unlimited.
          threshold: |
            Command Hint:
            Argument: [threshold]
            Description: The minimum price that code can be applied
            `-1` for unlimited
          expired: |
            Command Hint
            Argument: [expired]
            Description: The code expired time.
            `-1` for unlimited duration.
            Accept both Zulu time and UNIX timestamp in seconds.
            Zulu Example: 2022-12-17T10:31:37Z
            UNIX Example: 1671273097
    discount-code-already-exists: <red>Sorry, the name of your discount code already in use.
    invalid-discount-code-regex: '<red>The discount code must matches the regex: <yellow>{0}</yellow>'
    invalid-discount-code: <red>This discount code are invalid.
    discount-code-added: <green>Your discount code <yellow>{0}</yellow> has been added into shop <aqua>{1}</aqua>
    discount-code-removed: <green>Your discount code <yellow>{0}</yellow> has been removed from shop <aqua>{1}</aqua>
    invalid-code-type: <red>The code type <yellow>{0}</yellow> are invalid.
    invalid-usage-restriction: <red>The usage restriction <yellow>{0}</yellow> are invalid.
    invalid-threshold-restriction: <red>The threshold restriction <yellow>{0}</yellow> are invalid.
    invalid-effect-scope: <red>The scope <yellow>{0}</yellow> are invalid.
    invalid-expire-time: <red>Geçmişteki bir saati belirtemezsiniz.
    invalid-discount-rate: <red>The discount rate <yellow>{0}</yellow> are invalid, it can be a fixed number or percentage.
    discount-code-expired: <red>Eyvah! <yellow>{0}</yellow> indirim kodunuzun süresi doldu!
    discount-code-installed: <green>You have installed discount code <gold>{0}</gold>, the discount code will be automatically applied to the available purchase in all the following purchases during this session. To uninstall discount code, execute <aqua>/qs discount uninstall {0}</aqua>.
    discount-code-uninstalled: <green>İndirim kodunuzu kaldırdınız.
    discount-code-query-nothing: <red>Henüz bir indirim kodu yüklemediniz!
    discount-code-query: '<green>İndirim kodu kullanıyorsunuz: <yellow>{0}</yellow>.'
    discount-code-applicable: '<color:#bcef26>Your discount code <yellow><bold>{0}</bold></yellow> are <bold>applicable</bold> in this shop!'
    discount-code-applicable-with-threshold: '<color:#bcef26>Your discount code <yellow><bold>{0}</bold></yellow> are <bold>applicable</bold> in this shop, but only your purchase cost over <yellow>{1}</yellow> in single transaction.'
    discount-code-not-applicable: <red>Your discount code <yellow><bold>{0}</bold></yellow> are <bold>not applicable</bold> in this shop!
    discount-code-reach-the-limit: <red>You have reach the usage limit of discount code <yellow><bold>{0}</bold></yellow>, discount are not applied.
    discount-code-no-permission: <red>Bu mağazada herhangi bir indirim kodu kullanma izniniz yok!
    discount-code-has-been-expired: <red>İndiriminizin süresi doldu!
    discount-code-config-shop-added: <green>Successfully to add this shop into your discount code allow list.
    discount-code-config-shop-add-failure: <red>Bu mağaza zaten indirim kodu izin verilenler listenizde yer alıyor.
    discount-code-config-shop-removed: <green>Successfully to remove this shop from your discount code allow list.
    discount-code-config-shop-remove-failure: <red>This shop not exists in your discount code allow list.
    discount-code-config-expire: <green>Kodunuzun geçerlilik süresi başarıyla değiştirildi.
    discount-code-config-applied: <green>İndirim kodunu başarıyla yapılandırdınız!
    discount-code-created-successfully: |
      <green>Your discount code <yellow>{0}</yellow> has been successfully created!
      <gold>Scope: <aqua>{1}</aqua></gold>.
      <gold>Share with others: <color:#bcef26>{2}</color:#bcef26></gold>.
      <yellow>For <gray>Specific Shops</gray> scope only: To add shops to your discount code allow list, please looking an quickshop and execute <aqua>{3}</aqua> command.
      <yellow>You can use <aqua>/qs discount config {0}</aqua> to edit your discount code anytime.
    discount-code-under-threshold: <red>The discount not applied to your purchase because the total value under the discount code threshold <yellow>{0}</yellow>.
    percentage-off: '<bold><color:#bcef26>-{0}%</color:#bcef26></bold>'
    fixed-off: '<bold><color:#bcef26>-{0}</color:#bcef26></bold>'
    discount-code-list: '<gold>İndirim kodlarınızı listeleniyor:</gold>'
    discount-code-applied-in-purchase: '<color:#bcef26>Your discount code <yellow>{0}</yellow> has been applied to this purchase and you saved <gold>{1}</gold>!'
    scope:
      this-shop: '{0}'
      your-shops-owned: tüm dükkanlarınız (sahibi olduklarınız)
      your-shops-managed: tüm mağazalarınız (yönetilen)
      server: tüm sunucu
    code-type:
      SERVER_ALL_SHOPS: Bu sunucudaki tüm mağazalar
      PLAYER_ALL_SHOPS: İndirim kodu sahibi tarafından oluşturulan tüm mağazalar
      SPECIFIC_SHOPS: Belirli mağazalar
    discount-code-details: |-
      <gold>Code: <yellow>{0}</yellow>
      <gold>Creator: <yellow>{1}</yellow>
      <gold>Applied to: <yellow>{2}</yellow>
      <gold>Remaining usage: <yellow>{3}</yellow>
      <gold>Expired on: <yellow>{4}</yellow>
      <gold>Threshold: <yellow>{5}</yellow>
      <gold>Discount: <yellow>{6}</yellow>
  list:
    commands:
      list: <yellow>Listing all shops owned by your or specific player
    table-prefix: '<yellow><green>{0}</green>''s shops <gray>(Total {1})</gray>: '
    table-prefix-pageable: '<yellow><green>{0}</green>''s shops <gray>(Page {1}/{2})</gray>: '
    entry: "<hover:show_text:'<yellow>{1}</yellow>\n<gray>{2} {3},{4},{5}</gray>\n<aqua>Price <yellow>{6}</yellow> - <light_purple>x{7}</light_purple> <yellow>{8}</yellow>\n<green>{9}'><yellow>{0}. <aqua>{1}</aqua></hover>"
  shopitemonly:
    message: <red>Mağaza dışı ürünleri mağazaya koyamazsınız, mağaza dışı tüm ürünler bulunduğunuz yere düşer.
  limited:
    commands:
      limit: <yellow>Set a limit that restrict the purchase that player can make in period
    titles:
      title: <green>Satın Alma Başarılı
      subtitle: <aqua>You can also purchase <gold>{0}</gold> more in this shop
    reach-the-limit: <red>You have reached the limit in this shop. You can also purchase <green>{0}</green> items, but you trying purchase <yellow>{1}</yellow> items.
    success-reset: <green>Reset the restriction of this shop successfully
    success-remove: <green>Successfully remove the all restriction of this shop
    success-setup: <green>Restriction settings for this shop were saved successfully
    trade-limit-reached-cancel-reason: <red>Reached the restriction of this shop
    remains-limits: '<gold>Your remaining purchase quota in this shop: <yellow>{0}</yellow>'
    remains-limits-before-reset-period-note: '<gold>This shop will reset their purchase quota every: <yellow>{0}</yellow>'
  dynmap:
    markerset-title: QuickShop-Hikari Shops
    marker-name: '{0} @ {2} @ {3} for x{4} @ {5}'
    marker-description: |
      Name: {0}
      Owner: {1}
      Item: {2}
      Price: {3} for x{4} item(s)
      Type: {5}
      Unlimited: {6}
      Location: {7}
  bluemap:
    markerset-title: QuickShop-Hikari Shops
    marker-name: '{0} @ {2} @ {3} for x{4} @ {5}'
    marker-description: |
      Name: {0}
      Owner: {1}
      Item: {2}
      Price: {3} for x{4} item(s)
      Type: {5}
      Unlimited: {6}
      Location: {7}
  chestprotect:
    protection-exists: <red>This region already protected by ChestProtect and you don't have permission to create shops at here.
    shops-exsts: <red>The region that you want to protect contains other player's shops and you don't have permission to access them.
  displaycontrol:
    toggle: |-
      <green>Successfully toggle your QuickShop display showcasing to <aqua>{0}</aqua>.
      <yellow>You may need re-join to take effect.
    toggle-exception: <red>Failed to toggle your display showcase settings due an internal error, please contact server administrator.
    command:
      displaycontrol: <yellow>Toggle your QuickShop display showcasing
  reremake-migrator:
    commands:
      migratefromreremake: Migrate QuickShop-Reremake's data to QuickShop-Hikari
    server-not-empty: "<red>No players are allowed to be online on the server during the conversion progress, please enable server whitelist or maintenance mode."
    starting-convert-progress: "<gold>Starting the convert progress, <red>DO NOT SHUTDOWN YOUR SERVER!</red>"
    executing: "<gold>Executing migrate component <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
    completed: "<green>Done! Migrate completed, please restart your server and remove QuickShop-Reremake from plugins directory."
    join_blocking_converting: "<red>[QuickShop-Hikari Migrator] This server is performing a data conversion and you cannot join the server at this time, try again later!"
    join_blocking_finished: "<red>[QuickShop-Hikari Migrator] This server has just completed a data conversion and is waiting for a reboot to apply the changes, try again later!"
    failed: "<red>Migration progress exited with error, please check the server console."
    modules:
      config:
        copy-values: "<yellow>Copying values (total {0} entries)..."
        copying-value: "<gray> - Copying <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        migrate-price-restriction: "<yellow>Migrating price-restriction related settings..."
        migrate-price-restriction-entry: "<gray> - Migrating <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
      shop:
        start-migrate: "<yellow>Migrating shops (total {0} entries)..."
        migrate-entry: "<gray> - Migrating <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        unloading-reremake: "<gold>Unloading Reremake to avoid data overwrite..."
        register-entry: "<gray> - Registering <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        save-entry: "<gray> - Saving <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        saving-shops: "<yellow>Saving <gold>{0}</gold> shops, this may need a while™ (the time based on shops amounts)..."
        conflict: "<gray> - CONFLICT > Detects a conflict between a Reremake store and an existing Hikari store location, taking a predefined behavior: <dark_gray>{0}</dark_gray>"
      translation:
        start-migrate: "<yellow>Migrating translation files...</yellow>"
        copy-values: "<yellow>Copying values in locale <gold>{0}</gold> (total {1} entries)..."
        migrate-entry: "<gray> - Migrating <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        copying-value: "<gray> - Copying <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
      shop-logs:
        start-migrate: "<yellow>Migrating shop logs (details in console), please wait...</yellow>"
        extract-history-files: "<gray>Please wait for us to unzip and append the history logs..."
        filter-history-files: "<gray>Please wait for us to filter the history log..."
        filtered-history-files: "<gray>Filtered {0} lines out of queue."
        import-entry: "<gray> - Migrating <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
compat:
  advancedchests:
    created: <green>You're successfully created a shop on AdvancedChests container!
    permission-denied: <red>Sorry! You don't have the permission to create a shop on a AdvancedChests container!
suite:
  dynmap:
    title: '{0} - {1} - {2}'
    description: |-
      Ürün: {0}
      Sahibi: {1}
      Türü: {2} {3}
      Fiyat: {4}
      Yer: {5} {6}, {7}, {8}
      Alan: {9}
      Stok: {10}
  limited:
    command-description: <yellow>Oyuncunun periyod içinde yapabileceği satın alma işlemini kısıtlayan bir sınır belirleyiniz.
    reach-the-quota-limit: <red>Bu mağazada satın alma limitine ulaştınız ({0}/{1}).
    quota-reset-countdown: <yellow>Bu dükkandaki kota {0} zamanında sıfırlanacak.
    quota-reset-player-successfully: <green>Bu dükkandaki {0} adlı oyuncunun kotası başarıyla sıfırlandı.
    quota-reset-everybody-successfully: <green>Bu mağazadaki herkesin kotası başarıyla sıfırlandı.
    quota-setup: <green>Satın alma kısıtlaması bu dükkan için artık geçerli!
    quota-remove: <green>Satın alma kısıtlaması bu dükkandan artık kaldırıldı!
    subtitles:
      title: <green>Satın Alma Başarılı
      subtitle: <aqua>You can purchase <yellow>{0}</yellow> more in this shop
  list:
    command-description: <yellow>Kendinize veya başka bir oyuncuya ait tüm dükkanları listeleyin.
    table-prefix: <yellow>Bu sunucuda toplam <aqua>{0}</aqua> mağazanız var.
    entry-syntax: <aqua>- <yellow>{0}
    entry-syntax-fallback-name: '<yellow>Eşya:{0} X:{1}, Y:{2}, Z:{3}, Dünya: {4}'
    hover-text: |-
      <yellow>{0}
      <gray>{1} {2}, {3}, {4}
      <aqua>{5} - <yellow>{6}
      <yellow>{7} items each stack.
  shopitemonly:
    message: <red>Mağaza dışı ürünleri mağazaya koyamazsınız, mağaza dışı tüm ürünler bulunduğunuz yere düşer.
compatibility:
  elitemobs:
    soulbound-disallowed: EliteMobs Soulbound büyüsüne sahip eşyaların ticaretini yapamazsınız.
internet-paste-forbidden-privacy-reason: "<red>Failed! According your privacy setting, QuickShop-Hikari are not allowed upload your paste to Internet, turn on DIAGNOSTIC permission in privacy setting in config.yml or use <aqua>/quickshop paste --file</aqua> instead."
no-sign-type-given: "<red>You need give a sign material, the sign materials that available on this server: {0}"
sign-type-invalid: "<red>The type <yellow>{0}</yellow> not a valid sign material."
delete-controlpanel-button-confirm: "<red>Bu marketi gerçekten silmek istiyor musun? Onaylamak için <bold>[Marketi Sil]</bold> butonuna {0} saniye içinde tekrar tıkla."
cannot-suggest-price: "<red>Sorry, For the time being, no more people are trading the same item as you and there is not enough data to generate a suggested price."
price-suggest: "<green>Based on data from <aqua>{0}</aqua> stores, the highest priced store is priced at <light_purple>{1}</light_purple>, the lowest priced store is priced at <light_purple>{2}</light_purple>, the average price is <light_purple>{3}</light_purple>, and the median price is <light_purple>{4}</light_purple>. <newline><yellow>It's recommended that you set your price around <gold>{5}</gold>.</yellow>"
suggest-wait: "<green>Please wait... Calculating recommend price."
history:
  shop:
    gui-title: "View purchase history"
    header-icon-multiple-shop: "<white>The query result across {0} shops</white>"
    header-icon-description:
      - "<white>Type: <yellow>{0}</yellow></white>"
      - "<white>Owner: <yellow>{1}</yellow></white>"
      - "<white>Item: <yellow>{2}</yellow></white>"
      - "<white>Price: <yellow>{3} <aqua>x{4}</aqua></yellow></white>"
      - "<white>Location: <yellow>{5}</yellow></white>"
    log-icon-title: "<reset><green>Time: {0}</green>"
    log-icon-description:
      - "<white>Purchaser: <yellow>{0}</yellow></white>"
      - "<white>Item: <yellow>{1} <aqua>x{2}</aqua></yellow></white>"
      - "<white>Balance: <yellow>{3}</yellow></white>"
      - "<white>Tax: <yellow>{4}</yellow></white>"
    log-icon-description-with-store-name:
      - "<white>Store: <yellow>{0}</yellow></white>"
      - "<white>Purchaser: <yellow>{1}</yellow></white>"
      - "<white>Item: <yellow>{2} <aqua>x{3}</aqua></yellow></white>"
      - "<white>Balance: <yellow>{4}</yellow></white>"
      - "<white>Tax: <yellow>{5}</yellow></white>"
    query-icon: "<gray>Please wait, querying...</gray>"
    previous-page: "<white><< Previous Page</white>"
    next-page: "<white>Next Page >></white>"
    current-page: "<white>Page {0}</white>"
    summary-icon-title: "<green>Shop Summary"
    recent-purchases: "<white>Recent <aqua>{0}</aqua> purchases: <yellow>{1}</yellow></white>"
    recent-purchase-balance: "<white>Recent <aqua>{0}</aqua> turnover: <yellow>{1}</yellow></white>"
    total-purchases: "<white>Total purchases: <yellow>{0}</yellow></white>"
    total-balances: "<white>Total turnover: <yellow>{0}</yellow></white>"
    total-unique-purchasers: "<white>Total unique purchasers: <yellow>{0}</yellow></white>"
    top-n-valuable-customers-title: "<gold>Top {0} valuable customers</gold>"
    top-n-valuable-customers-entry: "<white>- <yellow>{0}</yellow> <aqua>{1}</aqua></white>"
    no-result: "<white>No result</white>"
about:
  text:
    - "<b><aqua>QuickShop <yellow>{0}</yellow></aqua></b>"
    - "<aqua>Version <yellow>>></yellow> <green>{1}</green></aqua>"
    - "<aqua>Release <yellow>>></yellow> <green>{2}</green></aqua>"
    - "<aqua>Developers <yellow>>></yellow> <color:#218bff><hover:show_text:'<yellow>Click to check contributors'><click:open_url:'https://github.com/Ghost-chu/QuickShop-Hikari/graphs/contributors'>[View contributors on GitHub]</click></hover></color></aqua>"
    - "<aqua>Localized members <gold>({4})</gold>: <yellow>>></yellow> <green>{5}</green></aqua>"
    - "  <yellow>+ <color:#43A047><hover:show_text:'<yellow>Click to open Crowdin translate page'><click:open_url:'https://crowdin.com/project/quickshop-hikari'>[Help translate on Crowdin]</click></hover></color></yellow>"
    - "<aqua>Donation Key <yellow>>></yellow> <green>{6}</green></aqua>"
    - "<gold>Powered by community</gold> <red>Made with ❤</red>"
  valid-donation-key: "<color:#00AFF1>Bound to <color:#F9C23C>{0}</color></color>"
  invalid-donation-key: "<gray>Invalid donation key</gra>"
  kofi-thanks: "<gold>Special thanks to those who support QuickShop on Ko-fi :)</gold>"
history-command-leave-blank: "<Leave blank for seeing store>"
shop-information-not-shown-due-an-internal-error: "<red>An internal error occurred. The store information panel may be displayed incompletely, please contact the server administrator."
