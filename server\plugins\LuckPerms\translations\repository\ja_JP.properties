luckperms.logs.actionlog-prefix=ログ
luckperms.logs.verbose-prefix=権限詳細
luckperms.logs.export-prefix=権限出力
luckperms.commandsystem.available-commands=利用可能なコマンドを表示するには {0} を使用してください
luckperms.commandsystem.command-not-recognised=コマンドが認識されません
luckperms.commandsystem.no-permission=このコマンドを実行する権限がありません\!
luckperms.commandsystem.no-permission-subcommands=サブコマンドを実行する権限がありません
luckperms.commandsystem.already-executing-command=別のコマンドが実行されています。完了を待っています...
luckperms.commandsystem.usage.sub-commands-header=サブコマンド
luckperms.commandsystem.usage.usage-header=コマンドの使用法
luckperms.commandsystem.usage.arguments-header=引数
luckperms.first-time.no-permissions-setup=まだ権限が設定されていません\!
luckperms.first-time.use-console-to-give-access=ゲーム内で LuckPerms コマンドを使用する前に、コンソールを使用して自分自身にアクセスを許可する必要があります
luckperms.first-time.console-command-prompt=コンソールを開いて実行
luckperms.first-time.next-step=これを実行したあと、権限の割り当てやグループを定義できます
luckperms.first-time.wiki-prompt=どこから始めればいいかわかりませんか? こちらをご覧ください\: {0}
luckperms.login.try-again=時間を置いてもう一度実行してください
luckperms.login.loading-database-error=権限データの読み込み中にデータベースエラーが発生しました
luckperms.login.server-admin-check-console-errors=サーバー管理者の場合は、コンソールでエラーを確認してください
luckperms.login.server-admin-check-console-info=詳細はサーバーコンソールを確認してください
luckperms.login.data-not-loaded-at-pre=プレ-ログインの段階ではあなたの権限データが読み込まれませんでした
luckperms.login.unable-to-continue=続行できません
luckperms.login.craftbukkit-offline-mode-error=CraftBukkit と online-mode の設定で競合している可能性があります
luckperms.login.unexpected-error=権限データの設定中に予期しないエラーが発生しました
luckperms.opsystem.disabled=このサーバーではバニラの OP システムは無効化されています
luckperms.opsystem.sponge-warning=権限プラグインが導入されている場合、Server Operator の状態は Sponge の権限チェックに影響しないので注意してください。直接ユーザーデータを編集する必要があります
luckperms.duration.unit.years.plural={0} 年
luckperms.duration.unit.years.singular={0} 年
luckperms.duration.unit.years.short={0}年
luckperms.duration.unit.months.plural={0} ヶ月
luckperms.duration.unit.months.singular={0} ヶ月
luckperms.duration.unit.months.short={0}ヶ月
luckperms.duration.unit.weeks.plural={0} 週
luckperms.duration.unit.weeks.singular={0} 週
luckperms.duration.unit.weeks.short={0}週
luckperms.duration.unit.days.plural={0} 日
luckperms.duration.unit.days.singular={0} 日
luckperms.duration.unit.days.short={0}日
luckperms.duration.unit.hours.plural={0} 時間
luckperms.duration.unit.hours.singular={0} 時間
luckperms.duration.unit.hours.short={0}時間
luckperms.duration.unit.minutes.plural={0} 分
luckperms.duration.unit.minutes.singular={0} 分
luckperms.duration.unit.minutes.short={0}分
luckperms.duration.unit.seconds.plural={0} 秒
luckperms.duration.unit.seconds.singular={0} 秒
luckperms.duration.unit.seconds.short={0}秒
luckperms.duration.since={0} 前
luckperms.command.misc.invalid-code=無効なコード
luckperms.command.misc.response-code-key=レスポンス・コード
luckperms.command.misc.error-message-key=メッセージ
luckperms.command.misc.bytebin-unable-to-communicate=bytebin と通信できません
luckperms.command.misc.webapp-unable-to-communicate=ウェブアプリと通信できません
luckperms.command.misc.check-console-for-errors=コンソールでエラーを確認してください
luckperms.command.misc.file-must-be-in-data=ファイル {0} はデータディレクトリの直下にある必要があります
luckperms.command.misc.wait-to-finish=完了するまで待ってからもう一度やり直してください
luckperms.command.misc.invalid-priority=無効な優先度\: {0}
luckperms.command.misc.expected-number=期待される数値
luckperms.command.misc.date-parse-error=日時 {0} を解析できません
luckperms.command.misc.date-in-past-error=過去の日時は指定できません\!
luckperms.command.misc.page={0} / {1} ページ
luckperms.command.misc.page-entries={0} 個の項目
luckperms.command.misc.none=なし
luckperms.command.misc.loading.error.unexpected=予期しないエラーが発生しました
luckperms.command.misc.loading.error.user=ユーザーが読み込まれていません
luckperms.command.misc.loading.error.user-specific=指定したユーザー {0} を読み込めません
luckperms.command.misc.loading.error.user-not-found=ユーザー {0} は見つかりませんでした
luckperms.command.misc.loading.error.user-save-error=ユーザー {0} のデータ保存中にエラーが発生しました
luckperms.command.misc.loading.error.user-not-online=ユーザー {0} はオフラインです
luckperms.command.misc.loading.error.user-invalid={0} は有効なユーザー名または UUID ではありません
luckperms.command.misc.loading.error.user-not-uuid=指定したユーザー {0} は有効な UUID ではありません
luckperms.command.misc.loading.error.group=グループが読み込まれていません
luckperms.command.misc.loading.error.all-groups=すべてのグループを読み込むことができませんでした
luckperms.command.misc.loading.error.group-not-found={0} というグループは見つかりませんでした
luckperms.command.misc.loading.error.group-save-error=グループ {0} のデータ保存中にエラーが発生しました
luckperms.command.misc.loading.error.group-invalid={0} は不正なグループ名です
luckperms.command.misc.loading.error.track=トラックが読み込まれていません
luckperms.command.misc.loading.error.all-tracks=すべてのトラックを読み込むことができませんでした
luckperms.command.misc.loading.error.track-not-found={0} というトラックは見つかりませんでした
luckperms.command.misc.loading.error.track-save-error=トラック {0} のデータ保存中にエラーが発生しました
luckperms.command.misc.loading.error.track-invalid={0} は不正なトラック名です
luckperms.command.editor.no-match=エディターを開くことができません。目的のタイプに一致するオブジェクトがありません
luckperms.command.editor.start=新しいエディタセッションを準備しています。お待ちください...
luckperms.command.editor.url=下のリンクをクリックしてエディタを開きます
luckperms.command.editor.unable-to-communicate=エディタと通信できません
luckperms.command.editor.apply-edits.success=ウェブエディターのデータを {0} {1} に正常に適用されました
luckperms.command.editor.apply-edits.success-summary={0} {1} と {2} {3}
luckperms.command.editor.apply-edits.success.additions=追加
luckperms.command.editor.apply-edits.success.additions-singular=追加
luckperms.command.editor.apply-edits.success.deletions=削除
luckperms.command.editor.apply-edits.success.deletions-singular=削除
luckperms.command.editor.apply-edits.no-changes=ウェブエディタのデータが編集されていなかったため、何も変更されませんでした
luckperms.command.editor.apply-edits.unknown-type=指定されたオブジェクトタイプに変更を適用できません
luckperms.command.editor.apply-edits.unable-to-read=指定されたコードを使用してデータを読み取ることはできませんでした
luckperms.command.search.searching.permission={0} の権限を持つユーザーまたはグループを検索中
luckperms.command.search.searching.inherit=グループ {0} を継承するユーザーまたはグループを検索中
luckperms.command.search.result={0} 人のユーザーと {1} グループから {2} 件の項目が見つかりました
luckperms.command.search.result.default-notice=注意\: デフォルトグループのメンバーを検索する場合、他の権限を持たないオフラインプレイヤーは表示されません\!
luckperms.command.search.showing-users=ユーザーのみ表示
luckperms.command.search.showing-groups=グループのみ表示
luckperms.command.tree.start=権限のツリー構造を生成中です。しばらくお待ちください...
luckperms.command.tree.empty=権限のツリー構造の要素が存在しないため、ツリー構造を生成できませんでした
luckperms.command.tree.url=権限のツリー構造のURL
luckperms.command.verbose.invalid-filter={0} は権限の詳細を表示するための有効なフィルターではありません
luckperms.command.verbose.enabled={1} に一致する詳細なロギングを {0} しました
luckperms.command.verbose.command-exec=コマンド {1} を {0} に強制実行し、すべてのチェックをレポートします....
luckperms.command.verbose.off=詳細なロギングを {0} しました
luckperms.command.verbose.command-exec-complete=コマンドの実行が完了しました
luckperms.command.verbose.command.no-checks=コマンドの実行は完了しましたが、権限チェックは行われませんでした
luckperms.command.verbose.command.possibly-async=これはプラグインがバックグラウンド (非同期) でコマンドを実行しているためかもしれません
luckperms.command.verbose.command.try-again-manually=このように行われたチェックを手動で検出するために verbose を使用できます
luckperms.command.verbose.enabled-recording={1} に一致する詳細な記録を {0} しました
luckperms.command.verbose.uploading=詳細なロギングを {0} しました、結果をアップロードしています...
luckperms.command.verbose.url=Verbose の結果の URL
luckperms.command.verbose.enabled-term=有効化
luckperms.command.verbose.disabled-term=無効化
luckperms.command.verbose.query-any=任意
luckperms.command.info.running-plugin=実行中
luckperms.command.info.platform-key=プラットフォーム
luckperms.command.info.server-brand-key=サーバーブランド
luckperms.command.info.server-version-key=サーバーバージョン
luckperms.command.info.storage-key=ストレージ
luckperms.command.info.storage-type-key=タイプ
luckperms.command.info.storage.meta.split-types-key=タイプ
luckperms.command.info.storage.meta.ping-key=Ping
luckperms.command.info.storage.meta.connected-key=接続済み
luckperms.command.info.storage.meta.file-size-key=ファイルサイズ
luckperms.command.info.extensions-key=拡張機能
luckperms.command.info.messaging-key=メッセージング
luckperms.command.info.instance-key=インスタンス
luckperms.command.info.static-contexts-key=静的コンテキスト
luckperms.command.info.online-players-key=接続中のプレイヤー
luckperms.command.info.online-players-unique=累計接続数 {0}
luckperms.command.info.uptime-key=稼働時間
luckperms.command.info.local-data-key=ローカルデータ
luckperms.command.info.local-data={0} ユーザー, {1} グループ, {2} トラック
luckperms.command.generic.create.success={0} は正常に作成されました
luckperms.command.generic.create.error={0} の作成中にエラーが発生しました
luckperms.command.generic.create.error-already-exists={0} はすでに存在しています\!
luckperms.command.generic.delete.success={0} は正常に削除されました
luckperms.command.generic.delete.error={0} の削除中にエラーが発生しました
luckperms.command.generic.delete.error-doesnt-exist={0} は存在していません\!
luckperms.command.generic.rename.success={0} は {1} に正常に名称変更されました
luckperms.command.generic.clone.success={0} は {1} に正常に複製されました
luckperms.command.generic.info.parent.title=親グループ
luckperms.command.generic.info.parent.temporary-title=一時的な親グループ
luckperms.command.generic.info.expires-in=有効期限\:
luckperms.command.generic.info.inherited-from=継承元\:
luckperms.command.generic.info.inherited-from-self=自身
luckperms.command.generic.show-tracks.title={0} のトラック
luckperms.command.generic.show-tracks.empty={0} はトラック上にありません
luckperms.command.generic.clear.node-removed={0} 個のノードが削除されました
luckperms.command.generic.clear.node-removed-singular={0} 個のノードが削除されました
luckperms.command.generic.clear={0} のノードはコンテキスト {1} でクリアされました
luckperms.command.generic.permission.info.title={0} の権限
luckperms.command.generic.permission.info.empty={0} は権限を持っていません
luckperms.command.generic.permission.info.click-to-remove=このノードを {0} から削除するにはクリックしてください
luckperms.command.generic.permission.check.info.title={0} の権限情報
luckperms.command.generic.permission.check.info.directly={0} は {1} がコンテキスト {3} で {2} に設定されています
luckperms.command.generic.permission.check.info.inherited={0} は {3} から {1} が {2} に設定されている {4} を継承しています
luckperms.command.generic.permission.check.info.not-directly={0} は {1} が設定されていません
luckperms.command.generic.permission.check.info.not-inherited={0} は {1} を継承していません
luckperms.command.generic.permission.check.result.title={0} の権限チェック
luckperms.command.generic.permission.check.result.result-key=結果
luckperms.command.generic.permission.check.result.processor-key=プロセッサー
luckperms.command.generic.permission.check.result.cause-key=原因
luckperms.command.generic.permission.check.result.context-key=コンテキスト
luckperms.command.generic.permission.set=コンテキスト {3} で {2} の {0} を {1} に設定しました
luckperms.command.generic.permission.already-has={0} はすでにコンテキスト {2} で {1} が設定されています
luckperms.command.generic.permission.set-temp=コンテキスト {4}, 有効期限 {3} で {2} の {0} を {1} に設定しました
luckperms.command.generic.permission.already-has-temp={0} はすでにコンテキスト {2} で {1} を一時的に設定されています
luckperms.command.generic.permission.unset=コンテキスト {2} で {1} から {0} の設定を解除しました
luckperms.command.generic.permission.doesnt-have={0} はコンテキスト {2} で {1} を設定されていません
luckperms.command.generic.permission.unset-temp=コンテキスト {2} で {1} の一時的な権限 {0} を解除しました
luckperms.command.generic.permission.subtract=コンテキスト {4} で {2} に対して {3} の期間、{0} を {1} に設定しました。これは以前より {5} 少なくなります
luckperms.command.generic.permission.doesnt-have-temp={0} はコンテキスト {2} で {1} を一時的に設定されていません
luckperms.command.generic.permission.clear={0} の権限はコンテキスト {1} でクリアされました
luckperms.command.generic.parent.info.title={0} の親
luckperms.command.generic.parent.info.empty={0} には親が定義されていません
luckperms.command.generic.parent.info.click-to-remove=クリックして {0} からこの親を削除します
luckperms.command.generic.parent.add={0} はコンテキスト {2} で {1} から権限を継承しています
luckperms.command.generic.parent.add-temp={0} はコンテキスト {3}, 有効期限 {2} で {1} から権限を継承しています
luckperms.command.generic.parent.set={0} は既存の親グループをクリアし、コンテキスト {2} で {1} のみを継承します
luckperms.command.generic.parent.set-track={0} はトラック {1} 上の既存の親グループをクリアし、コンテキスト {3} で {2} のみを継承します
luckperms.command.generic.parent.remove={0} はコンテキスト {2} で {1} から権限を継承しません
luckperms.command.generic.parent.remove-temp={0} はコンテキスト {2} で {1} からの権限を一時的に継承しません
luckperms.command.generic.parent.subtract={0} はコンテキスト {3} で有効期限を以前より {4} 少ない {2} の間 {1} から権限を継承します
luckperms.command.generic.parent.clear={0} の親はコンテキスト {1} でクリアされました
luckperms.command.generic.parent.clear-track=トラック {1} 上の {0} の親はコンテキスト {2} でクリアされました
luckperms.command.generic.parent.already-inherits={0} はすでにコンテキスト {2} で {1} から継承しています
luckperms.command.generic.parent.doesnt-inherit={0} はコンテキスト {2} で {1} から継承していません
luckperms.command.generic.parent.already-temp-inherits={0} はすでにコンテキスト {2} で {1} から一時的に継承しています
luckperms.command.generic.parent.doesnt-temp-inherit={0} はコンテキスト {2} で {1} から一時的に継承していません
luckperms.command.generic.chat-meta.info.title-prefix={0} のプレフィックス
luckperms.command.generic.chat-meta.info.title-suffix={0} のサフィックス
luckperms.command.generic.chat-meta.info.none-prefix={0} はプレフィックスを持っていません
luckperms.command.generic.chat-meta.info.none-suffix={0} はサフィックスを持っていません
luckperms.command.generic.chat-meta.info.click-to-remove=クリックして {1} からこの {0} を削除します
luckperms.command.generic.chat-meta.already-has={0} はすでにコンテキスト {4} で {1} {2} が優先度 {3} に設定されています
luckperms.command.generic.chat-meta.already-has-temp={0} はすでにコンテキスト {4} で {1} {2} が一時的に優先度 {3} に設定されています
luckperms.command.generic.chat-meta.doesnt-have={0} はコンテキスト {4} で {1} {2} を優先度 {3} で設定されていません
luckperms.command.generic.chat-meta.doesnt-have-temp={0} はコンテキスト {4} で {1} {2} を一時的に優先度 {3} で設定されていません
luckperms.command.generic.chat-meta.add={0} にコンテキスト {4} で {1} {2} を優先度 {3} で設定しました
luckperms.command.generic.chat-meta.add-temp={0} にコンテキスト {5} で {1} {2} を優先度 {3}, 有効期限 {4} で設定しました
luckperms.command.generic.chat-meta.remove={0} からコンテキスト {4} で優先度 {3} の {1} {2} を削除しました
luckperms.command.generic.chat-meta.remove-bulk={0} からコンテキスト {3} で優先度 {2} のすべての {1} を削除しました
luckperms.command.generic.chat-meta.remove-temp={0} からコンテキスト {4} で優先度 {3} の一時的な {1} {2} を削除しました
luckperms.command.generic.chat-meta.remove-temp-bulk={0} からコンテキスト {3} で優先度 {2} のすべての一時的な {1} を削除しました
luckperms.command.generic.meta.info.title={0} のメタ
luckperms.command.generic.meta.info.none={0} はメタを持っていません
luckperms.command.generic.meta.info.click-to-remove=クリックして {0} からこのメタを削除します
luckperms.command.generic.meta.already-has={0} はすでにコンテキスト {3} でメタキー {1} に {2} が設定されています
luckperms.command.generic.meta.already-has-temp={0} はすでにコンテキスト {3} でメタキー {1} に {2} が一時的に設定されています
luckperms.command.generic.meta.doesnt-have={0} はコンテキスト {2} でメタキー {1} を設定されていません
luckperms.command.generic.meta.doesnt-have-temp={0} はコンテキスト {2} でメタキー {1} を一時的に設定されていません
luckperms.command.generic.meta.set=コンテキスト {3} で {2} にメタキー {0} を {1} で設定しました
luckperms.command.generic.meta.set-temp=コンテキスト {4} で {2} のメタキー {0} を期間 {3} の間 {1} に設定しました
luckperms.command.generic.meta.unset=コンテキスト {2} で {1} のメタキー {0} の設定を解除しました
luckperms.command.generic.meta.unset-temp=コンテキスト {2} で {1} の一時的なメタキー {0} を解除しました
luckperms.command.generic.meta.clear=コンテキスト {2} で {0} のタイプ {1} に一致するメタがクリアされました
luckperms.command.generic.contextual-data.title=コンテキストデータ
luckperms.command.generic.contextual-data.mode.key=モード
luckperms.command.generic.contextual-data.mode.server=サーバー
luckperms.command.generic.contextual-data.mode.active-player=アクティブプレイヤー
luckperms.command.generic.contextual-data.contexts-key=コンテキスト
luckperms.command.generic.contextual-data.prefix-key=プレフィックス
luckperms.command.generic.contextual-data.suffix-key=サフィックス
luckperms.command.generic.contextual-data.primary-group-key=プライマリーグループ
luckperms.command.generic.contextual-data.meta-key=メタ
luckperms.command.generic.contextual-data.null-result=なし
luckperms.command.user.info.title=ユーザー情報
luckperms.command.user.info.uuid-key=UUID
luckperms.command.user.info.uuid-type-key=タイプ
luckperms.command.user.info.uuid-type.mojang=公式
luckperms.command.user.info.uuid-type.not-mojang=オフライン
luckperms.command.user.info.status-key=ステータス
luckperms.command.user.info.status.online=オンライン
luckperms.command.user.info.status.offline=オフライン
luckperms.command.user.removegroup.error-primary=プライマリグループからユーザーを削除することはできません
luckperms.command.user.primarygroup.not-member={0} はまだ {1} のメンバーではありません、今すぐ追加します
luckperms.command.user.primarygroup.already-has={0} はすでに {1} をプライマリグループとして設定されています
luckperms.command.user.primarygroup.warn-option=警告\: このサーバー ({0}) で使用されているプライマリグループの計算方法は、この変更を反映していない可能性があります
luckperms.command.user.primarygroup.set={0} のプライマリグループは {1} に設定されました
luckperms.command.user.track.error-not-contain-group={0} は {1} のどのグループにも属していません
luckperms.command.user.track.unsure-which-track=どのトラックを使用するかわからない場合は、引数として指定してください。
luckperms.command.user.track.missing-group-advice=グループを作成するか、トラックから削除して再試行してください
luckperms.command.user.promote.added-to-first={0} は {1} のどのグループにも属していないため、最初のグループ {2} にコンテキスト {3} で追加されました
luckperms.command.user.promote.not-on-track={0} は {1} のどのグループにも属していないため、昇格されませんでした
luckperms.command.user.promote.success=トラック {1} に従って {0} をコンテキスト {4} で {2} から {3} に昇格しました
luckperms.command.user.promote.end-of-track=トラック {0} が終了しました、{1} を昇格できません
luckperms.command.user.promote.next-group-deleted=トラックの次のグループ {0} は存在しません
luckperms.command.user.promote.unable-to-promote=ユーザーを昇格できません
luckperms.command.user.demote.success=トラック {1} に従って {0} をコンテキスト {4} で {2} から {3} に降格しました
luckperms.command.user.demote.end-of-track=トラック {0} が終了したので、{1} は {2} から削除されました
luckperms.command.user.demote.end-of-track-not-removed=トラック {0} は終了しましたが、{1} は最初のグループから削除されませんでした
luckperms.command.user.demote.previous-group-deleted=トラックの前のグループ {0} は存在しません
luckperms.command.user.demote.unable-to-demote=ユーザーを降格できません
luckperms.command.group.list.title=グループ
luckperms.command.group.delete.not-default=デフォルトのグループは削除できません
luckperms.command.group.info.title=グループ情報
luckperms.command.group.info.display-name-key=表示名
luckperms.command.group.info.weight-key=ウェイト
luckperms.command.group.setweight.set=グループ {1} のウェイトを {0} に設定しました
luckperms.command.group.setdisplayname.doesnt-have={0} に表示名は設定されていません
luckperms.command.group.setdisplayname.already-has={0} はすでに表示名が {1} です
luckperms.command.group.setdisplayname.already-in-use=表示名 {0} はすでに {1} に使用されています
luckperms.command.group.setdisplayname.set=グループ {1} の表示名をコンテキスト {2} で {0} に設定しました
luckperms.command.group.setdisplayname.removed=グループ {0} の表示名をコンテキスト {1} で削除しました
luckperms.command.track.list.title=トラック
luckperms.command.track.path.empty=なし
luckperms.command.track.info.showing-track=トラックを表示
luckperms.command.track.info.path-property=パス
luckperms.command.track.clear={0} のグループトラックをクリアしました
luckperms.command.track.append.success=グループ {0} はトラック {1} に追加されました
luckperms.command.track.insert.success=グループ {0} はトラック {1} の {2} 番目に挿入されました
luckperms.command.track.insert.error-number=数値が必要ですが、代わりに {0} が入力されました。
luckperms.command.track.insert.error-invalid-pos={0} 番目に挿入できませんでした
luckperms.command.track.insert.error-invalid-pos-reason=無効な位置
luckperms.command.track.remove.success=グループ {0} はトラック {1} から削除されました
luckperms.command.track.error-empty={0} は空または1つのグループしか含まれていないため使用できません
luckperms.command.track.error-multiple-groups={0} はこのトラック上の複数のグループのメンバーです
luckperms.command.track.error-ambiguous=位置を特定できません
luckperms.command.track.already-contains={0} はすでに {1} を含んでいます
luckperms.command.track.doesnt-contain={0} は {1} を含んでいません
luckperms.command.log.load-error=ログを読み込めませんでした
luckperms.command.log.invalid-page=無効なページ番号です
luckperms.command.log.invalid-page-range={0} から {1} の間で値を入力してください
luckperms.command.log.empty=表示するログエントリがありません
luckperms.command.log.notify.error-console=コンソールへの通知は切り替えられません
luckperms.command.log.notify.enabled-term=有効化
luckperms.command.log.notify.disabled-term=無効化
luckperms.command.log.notify.changed-state=ログ出力を {0} しました
luckperms.command.log.notify.already-on=すでに通知を受信しています
luckperms.command.log.notify.already-off=現在通知を受信していません
luckperms.command.log.notify.invalid-state={0} か {1} を指定してください
luckperms.command.log.show.search=クエリ {0} の最近の操作を表示する
luckperms.command.log.show.recent=最近の操作を表示する
luckperms.command.log.show.by={0} による最近の操作を表示する
luckperms.command.log.show.history={0} {1} の履歴を表示しています
luckperms.command.export.error-term=エラー
luckperms.command.export.already-running=他のエクスポートプロセスが実行されています
luckperms.command.export.file.already-exists=ファイル {0} はすでに存在しています
luckperms.command.export.file.not-writable=ファイル {0} は書き込み禁止状態です
luckperms.command.export.file.success={0} へのエクスポートが正常に完了しました
luckperms.command.export.file-unexpected-error-writing=ファイルへの書き込み中に予期しないエラーが発生しました
luckperms.command.export.web.export-code=エクスポートコード
luckperms.command.export.web.import-command-description=インポートするには次のコマンドを使用してください
luckperms.command.import.term=インポート
luckperms.command.import.error-term=エラー
luckperms.command.import.already-running=他のインポートプロセスが既に実行中です
luckperms.command.import.file.doesnt-exist=ファイル {0} は存在しません
luckperms.command.import.file.not-readable=ファイル {0} を読み込めません
luckperms.command.import.file.unexpected-error-reading=ファイルの読み込み中に予期しないエラーが発生しました
luckperms.command.import.file.correct-format=正しい形式ですか?
luckperms.command.import.web.unable-to-read=指定されたコードを使用してデータを読み取れませんでした
luckperms.command.import.progress.percent={0}% 完了
luckperms.command.import.progress.operations={0}/{1} 処理完了
luckperms.command.import.starting=インポート処理を開始しています
luckperms.command.import.completed=完了しました
luckperms.command.import.duration=所要時間 {0} 秒
luckperms.command.bulkupdate.must-use-console=一括更新コマンドはコンソールからのみ使用できます
luckperms.command.bulkupdate.invalid-data-type=無効なタイプです、{0} が指定できます
luckperms.command.bulkupdate.invalid-constraint={0} は無効な制約です
luckperms.command.bulkupdate.invalid-constraint-format=制約は {0} のフォーマットである必要があります
luckperms.command.bulkupdate.invalid-comparison=無効な比較演算子 {0}
luckperms.command.bulkupdate.invalid-comparison-format=次のいずれかである必要があります\: {0}
luckperms.command.bulkupdate.queued=バルクアップデートがキューに追加されました
luckperms.command.bulkupdate.confirm={0} を実行して更新を行います
luckperms.command.bulkupdate.unknown-id=id {0} の操作は存在しないか、期限切れです
luckperms.command.bulkupdate.starting=バルクアップデートを実行中
luckperms.command.bulkupdate.success=バルクアップデートが正常に完了しました
luckperms.command.bulkupdate.success.statistics.nodes=変更されたノード数
luckperms.command.bulkupdate.success.statistics.users=変更されたユーザー数
luckperms.command.bulkupdate.success.statistics.groups=変更されたグループ数
luckperms.command.bulkupdate.failure=バルクアップデートに失敗しました。コンソールでエラーを確認してください
luckperms.command.update-task.request=アップデートが要求されました、しばらくお待ちください
luckperms.command.update-task.complete=アップデートが完了しました
luckperms.command.update-task.push.attempting=現在他のサーバーに適用しようとしています
luckperms.command.update-task.push.complete=他のサーバーは {0} 経由で正常に適用されました
luckperms.command.update-task.push.error=他のサーバーへの変更の適用中にエラーが発生しました
luckperms.command.update-task.push.error-not-setup=メッセージングサービスが設定されていないため、他のサーバーに変更を適用できません
luckperms.command.reload-config.success=設定ファイルが再読み込みされました
luckperms.command.reload-config.restart-note=いくつかの設定はサーバーの再起動後に適用されます
luckperms.command.translations.searching=利用可能な翻訳を検索しています。お待ちください...
luckperms.command.translations.searching-error=利用可能な翻訳のリストを取得できません
luckperms.command.translations.installed-translations=インストール済みの翻訳
luckperms.command.translations.available-translations=利用可能な翻訳
luckperms.command.translations.percent-translated={0}% 翻訳済み
luckperms.command.translations.translations-by=作成者
luckperms.command.translations.installing=翻訳をインストールしています。お待ちください...
luckperms.command.translations.download-error={0} の翻訳をダウンロードできません
luckperms.command.translations.installing-specific=言語 {0} をインストール中...
luckperms.command.translations.install-complete=インストール完了
luckperms.command.translations.download-prompt=コミュニティーが提供する最新の翻訳をダウンロードしてインストールするには {0} を使用してください
luckperms.command.translations.download-override-warning=これらの言語に対する変更は上書きされますので注意してください
luckperms.usage.user.description=LuckPerms 内でユーザーを管理するためのコマンドです (LuckPerms 内の `user` はプレイヤーであり、UUID やユーザー名を参照できます)
luckperms.usage.group.description=LuckPerms 内でグループを管理するためのコマンドです。グループはユーザーに与えられる権限割り当ての単なるコレクションです。新しいグループは `creategroup` コマンドを使用して作成されます。
luckperms.usage.track.description=LuckPerms 内でトラックを管理するためのコマンドです。トラックは、昇格や広角の定義に使用できるグループの順序付きコレクションです。
luckperms.usage.log.description=LuckPerms 内でロギング機能を管理するためのコマンドです。
luckperms.usage.sync.description=プラグインのストレージからすべてのデータをメモリに再読み込みし、検出された変更を適用します
luckperms.usage.info.description=アクティブなプラグインインスタンスに関する一般情報を出力します
luckperms.usage.editor.description=新しいウェブエディターを作成します
luckperms.usage.editor.argument.type=エディタに読み込むタイプ (''all'', ''users'' or ''groups'')
luckperms.usage.editor.argument.filter=ユーザーをフィルタリングする権限
luckperms.usage.verbose.description=プラグインの権限チェックを監視するシステムを制御します
luckperms.usage.verbose.argument.action=ログの有効/無効を切り替えるか、ログ出力をアップロードするかどうか
luckperms.usage.verbose.argument.filter=次に一致するエントリのフィルタ
luckperms.usage.verbose.argument.commandas=実行するプレイヤー/コマンド
luckperms.usage.tree.description=LuckPerms が把握しているすべての権限のツリービュー (順序付きリスト階層) を生成します
luckperms.usage.tree.argument.scope=ツリーのルートです、すべての権限を含めるには "." を指定してください
luckperms.usage.tree.argument.player=確認するオンラインプレイヤーの名前
luckperms.usage.search.description=指定した権限を持つすべてのユーザー/グループを検索します
luckperms.usage.search.argument.permission=検索する権限
luckperms.usage.search.argument.page=表示するページ
luckperms.usage.network-sync.description=変更をストレージに同期させ、ネットワーク上の他のすべてのサーバーにも同期を要求します
luckperms.usage.import.description=(以前作成した) エクスポートファイルからデータをインポートする
luckperms.usage.import.argument.file=インポートするファイル
luckperms.usage.import.argument.replace=マージの代わりに既存のデータを置き換える
luckperms.usage.import.argument.upload=以前のエクスポートからデータをアップロード
luckperms.usage.export.description=ファイルにすべての権限データをエクスポートします。あとから再インポートできます。
luckperms.usage.export.argument.file=エクスポートするファイル
luckperms.usage.export.argument.without-users=エクスポートからユーザーを除外
luckperms.usage.export.argument.without-groups=エクスポートからグループを除外する
luckperms.usage.export.argument.upload=ウェブエディターにすべての権限データをアップロードします。あとから再インポートできます。
luckperms.usage.reload-config.description=設定オプションを再読み込みする
luckperms.usage.bulk-update.description=すべてのデータに対して一括変更クエリを実行する
luckperms.usage.bulk-update.argument.data-type=変更されるデータのタイプ (''all'', ''users'' or ''groups'')
luckperms.usage.bulk-update.argument.action=データ上で実行するアクション (''update'' または ''delete'')
luckperms.usage.bulk-update.argument.action-field=実行する対象。''update'' 時のみ必要です。(''permission'', ''server'' または ''world'')
luckperms.usage.bulk-update.argument.action-value=置き換え後の値。''update'' にのみ必要です。
luckperms.usage.bulk-update.argument.constraint=更新に必要な制約
luckperms.usage.translations.description=翻訳を管理する
luckperms.usage.translations.argument.install=翻訳をインストールするサブコマンド
luckperms.usage.apply-edits.description=ウェブエディターで変更した権限を適用します
luckperms.usage.apply-edits.argument.code=データの一意なコード
luckperms.usage.apply-edits.argument.target=データの適用先
luckperms.usage.create-group.description=新しいグループの作成
luckperms.usage.create-group.argument.name=グループ名
luckperms.usage.create-group.argument.weight=グループの重み
luckperms.usage.create-group.argument.display-name=グループの表示名
luckperms.usage.delete-group.description=グループの削除
luckperms.usage.delete-group.argument.name=グループ名
luckperms.usage.list-groups.description=プラットフォーム上のすべてのグループの一覧
luckperms.usage.create-track.description=新しいトラックの作成
luckperms.usage.create-track.argument.name=トラック名
luckperms.usage.delete-track.description=トラックの削除
luckperms.usage.delete-track.argument.name=トラック名
luckperms.usage.list-tracks.description=プラットフォーム上のすべてのトラックの一覧
luckperms.usage.user-info.description=ユーザーに関する情報を表示する
luckperms.usage.user-switchprimarygroup.description=ユーザーのプライマリグループを切り替える
luckperms.usage.user-switchprimarygroup.argument.group=グループの切り替え先
luckperms.usage.user-promote.description=ユーザーをトラックに従って昇格させる
luckperms.usage.user-promote.argument.track=ユーザーを昇格させるためのトラック
luckperms.usage.user-promote.argument.context=ユーザーを昇格させるコンテキスト
luckperms.usage.user-promote.argument.dont-add-to-first=ユーザーがトラック上にいる場合のみ昇格させる
luckperms.usage.user-demote.description=ユーザーをトラックに従って降格させる
luckperms.usage.user-demote.argument.track=ユーザーを降格させるためのトラック
luckperms.usage.user-demote.argument.context=ユーザーを降格させるコンテキスト
luckperms.usage.user-demote.argument.dont-remove-from-first=ユーザーが最初のグループから削除されないようにする
luckperms.usage.user-clone.description=ユーザーを複製する
luckperms.usage.user-clone.argument.user=複製先のユーザーの名前または UUID
luckperms.usage.group-info.description=グループについての情報を表示する
luckperms.usage.group-listmembers.description=このグループから継承しているユーザーやグループを表示する
luckperms.usage.group-listmembers.argument.page=表示するページ
luckperms.usage.group-setweight.description=グループのウェイトを設定する
luckperms.usage.group-setweight.argument.weight=設定するウェイト
luckperms.usage.group-set-display-name.description=グループの表示名を設定する
luckperms.usage.group-set-display-name.argument.name=設定する名前
luckperms.usage.group-set-display-name.argument.context=名前を設定するコンテキスト
luckperms.usage.group-rename.description=グループ名を変更する
luckperms.usage.group-rename.argument.name=新しい名前
luckperms.usage.group-clone.description=グループの複製
luckperms.usage.group-clone.argument.name=複製先のグループの名前
luckperms.usage.holder-editor.description=Web 権限エディターを開く
luckperms.usage.holder-showtracks.description=オブジェクトが属するトラックの一覧を表示する
luckperms.usage.holder-clear.description=すべての権限、親、メタを削除する
luckperms.usage.holder-clear.argument.context=フィルタリングするコンテキスト
luckperms.usage.permission.description=権限を変更する
luckperms.usage.parent.description=継承を変更する
luckperms.usage.meta.description=メタデータの値を変更する
luckperms.usage.permission-info.description=オブジェクトが持つ権限ノードの一覧を表示する
luckperms.usage.permission-info.argument.page=表示するページ
luckperms.usage.permission-info.argument.sort-mode=エントリのソート方法
luckperms.usage.permission-set.description=オブジェクトに権限を設定する
luckperms.usage.permission-set.argument.node=設定する権限ノード
luckperms.usage.permission-set.argument.value=ノードの値
luckperms.usage.permission-set.argument.context=権限を追加するコンテキスト
luckperms.usage.permission-unset.description=オブジェクトから権限を解除する
luckperms.usage.permission-unset.argument.node=解除する権限ノード
luckperms.usage.permission-unset.argument.context=権限を解除するコンテキスト
luckperms.usage.permission-settemp.description=オブジェクトに権限を一時的に設定する
luckperms.usage.permission-settemp.argument.node=設定する権限ノード
luckperms.usage.permission-settemp.argument.value=ノードの値
luckperms.usage.permission-settemp.argument.duration=権限ノードの有効期間
luckperms.usage.permission-settemp.argument.temporary-modifier=一時的な権限の適用方法
luckperms.usage.permission-settemp.argument.context=権限を追加するコンテキスト
luckperms.usage.permission-unsettemp.description=オブジェクトから一時的な権限を解除する
luckperms.usage.permission-unsettemp.argument.node=解除する権限ノード
luckperms.usage.permission-unsettemp.argument.duration=減算する時間
luckperms.usage.permission-unsettemp.argument.context=権限を解除するコンテキスト
luckperms.usage.permission-check.description=オブジェクトに特定の権限ノードがあるか確認する
luckperms.usage.permission-check.argument.node=確認する権限ノード
luckperms.usage.permission-clear.description=すべての権限を消去する
luckperms.usage.permission-clear.argument.context=フィルタリングするコンテキスト
luckperms.usage.parent-info.description=このオブジェクトが継承するグループの一覧
luckperms.usage.parent-info.argument.page=表示するページ
luckperms.usage.parent-info.argument.sort-mode=エントリのソート方法
luckperms.usage.parent-set.description=オブジェクトが既に継承している他のすべてのグループを削除し、指定したグループに追加します
luckperms.usage.parent-set.argument.group=設定するグループ
luckperms.usage.parent-set.argument.context=グループを設定するコンテキスト
luckperms.usage.parent-add.description=オブジェクトが権限を継承する他のグループを設定する
luckperms.usage.parent-add.argument.group=継承元のグループ
luckperms.usage.parent-add.argument.context=グループを継承するコンテキスト
luckperms.usage.parent-remove.description=以前設定した継承ルールを削除する
luckperms.usage.parent-remove.argument.group=削除するグループ
luckperms.usage.parent-remove.argument.context=グループを削除するコンテキスト
luckperms.usage.parent-set-track.description=指定されたトラック上からオブジェクトが継承するすべてのグループを削除し、指定されたトラックに追加します
luckperms.usage.parent-set-track.argument.track=設定するトラック
luckperms.usage.parent-set-track.argument.group=設定するグループ、または指定されたトラック上でのグループの位置に関連する番号
luckperms.usage.parent-set-track.argument.context=グループを設定するコンテキスト
luckperms.usage.parent-add-temp.description=オブジェクトが権限を一時的に継承する他のグループを設定する
luckperms.usage.parent-add-temp.argument.group=継承元のグループ
luckperms.usage.parent-add-temp.argument.duration=グループのメンバーである期間
luckperms.usage.parent-add-temp.argument.temporary-modifier=一時的な権限の適用方法
luckperms.usage.parent-add-temp.argument.context=グループを継承するコンテキスト
luckperms.usage.parent-remove-temp.description=以前設定した一時的な継承ルールを削除する
luckperms.usage.parent-remove-temp.argument.group=削除するグループ
luckperms.usage.parent-remove-temp.argument.duration=減算する時間
luckperms.usage.parent-remove-temp.argument.context=グループを削除するコンテキスト
luckperms.usage.parent-clear.description=すべての親を消去する
luckperms.usage.parent-clear.argument.context=フィルタリングするコンテキスト
luckperms.usage.parent-clear-track.description=指定したトラック上のすべての親を消去する
luckperms.usage.parent-clear-track.argument.track=消去するトラック
luckperms.usage.parent-clear-track.argument.context=フィルタリングするコンテキスト
luckperms.usage.meta-info.description=すべてのチャットメタを表示する
luckperms.usage.meta-set.description=メタ値を設定する
luckperms.usage.meta-set.argument.key=設定するキー
luckperms.usage.meta-set.argument.value=設定する値
luckperms.usage.meta-set.argument.context=メタを追加するコンテキスト
luckperms.usage.meta-unset.description=メタ値を解除する
luckperms.usage.meta-unset.argument.key=解除するキー
luckperms.usage.meta-unset.argument.context=メタを解除するコンテキスト
luckperms.usage.meta-settemp.description=一時的なメタ値を設定する
luckperms.usage.meta-settemp.argument.key=設定するキー
luckperms.usage.meta-settemp.argument.value=設定する値
luckperms.usage.meta-settemp.argument.duration=メタ値の有効期間
luckperms.usage.meta-settemp.argument.context=メタを追加するコンテキスト
luckperms.usage.meta-unsettemp.description=一時的なメタ値を解除する
luckperms.usage.meta-unsettemp.argument.key=解除するキー
luckperms.usage.meta-unsettemp.argument.context=メタを解除するコンテキスト
luckperms.usage.meta-addprefix.description=プレフィックスを追加
luckperms.usage.meta-addprefix.argument.priority=追加するプレフィックスの優先度
luckperms.usage.meta-addprefix.argument.prefix=プレフィックスの文字列
luckperms.usage.meta-addprefix.argument.context=プレフィックスを追加するコンテキスト
luckperms.usage.meta-addsuffix.description=サフィックスを追加する
luckperms.usage.meta-addsuffix.argument.priority=追加するサフィックスの優先度
luckperms.usage.meta-addsuffix.argument.suffix=サフィックスの文字列
luckperms.usage.meta-addsuffix.argument.context=サフィックスを追加するコンテキスト
luckperms.usage.meta-setprefix.description=プレフィックスをセットする
luckperms.usage.meta-setprefix.argument.priority=セットするプレフィックスの優先度
luckperms.usage.meta-setprefix.argument.prefix=プレフィックスの文字列
luckperms.usage.meta-setprefix.argument.context=プレフィックスをセットするコンテキスト
luckperms.usage.meta-setsuffix.description=サフィックスをセットする
luckperms.usage.meta-setsuffix.argument.priority=セットするサフィックスの優先度
luckperms.usage.meta-setsuffix.argument.suffix=サフィックスの文字列
luckperms.usage.meta-setsuffix.argument.context=サフィックスをセットするコンテキスト
luckperms.usage.meta-removeprefix.description=プレフィックスを削除する
luckperms.usage.meta-removeprefix.argument.priority=削除するプレフィックスの優先度
luckperms.usage.meta-removeprefix.argument.prefix=プレフィックスの文字列
luckperms.usage.meta-removeprefix.argument.context=プレフィックスを削除するコンテキスト
luckperms.usage.meta-removesuffix.description=サフィックスを削除する
luckperms.usage.meta-removesuffix.argument.priority=削除するサフィックスの優先度
luckperms.usage.meta-removesuffix.argument.suffix=サフィックスの文字列
luckperms.usage.meta-removesuffix.argument.context=サフィックスを削除するコンテキスト
luckperms.usage.meta-addtemp-prefix.description=プレフィックスを一時的に追加する
luckperms.usage.meta-addtemp-prefix.argument.priority=追加するプレフィックスの優先度
luckperms.usage.meta-addtemp-prefix.argument.prefix=プレフィックスの文字列
luckperms.usage.meta-addtemp-prefix.argument.duration=プレフィックスの有効期間
luckperms.usage.meta-addtemp-prefix.argument.context=プレフィックスを追加するコンテキスト
luckperms.usage.meta-addtemp-suffix.description=サフィックスを一時的に追加する
luckperms.usage.meta-addtemp-suffix.argument.priority=追加するサフィックスの優先度
luckperms.usage.meta-addtemp-suffix.argument.suffix=サフィックスの文字列
luckperms.usage.meta-addtemp-suffix.argument.duration=サフィックスの有効期間
luckperms.usage.meta-addtemp-suffix.argument.context=サフィックスを追加するコンテキスト
luckperms.usage.meta-settemp-prefix.description=一時的なプレフィックスをセットする
luckperms.usage.meta-settemp-prefix.argument.priority=セットするプレフィックスの優先度
luckperms.usage.meta-settemp-prefix.argument.prefix=プレフィックスの文字列
luckperms.usage.meta-settemp-prefix.argument.duration=プレフィックスの有効期間
luckperms.usage.meta-settemp-prefix.argument.context=プレフィックスをセットするコンテキスト
luckperms.usage.meta-settemp-suffix.description=一時的なサフィックスをセットする
luckperms.usage.meta-settemp-suffix.argument.priority=セットするサフィックスの優先度
luckperms.usage.meta-settemp-suffix.argument.suffix=サフィックスの文字列
luckperms.usage.meta-settemp-suffix.argument.duration=サフィックスの有効期間
luckperms.usage.meta-settemp-suffix.argument.context=サフィックスをセットするコンテキスト
luckperms.usage.meta-removetemp-prefix.description=一時的なプレフィックスを削除する
luckperms.usage.meta-removetemp-prefix.argument.priority=削除するプレフィックスの優先度
luckperms.usage.meta-removetemp-prefix.argument.prefix=プレフィックスの文字列
luckperms.usage.meta-removetemp-prefix.argument.context=プレフィックスを削除するコンテキスト
luckperms.usage.meta-removetemp-suffix.description=一時的なサフィックスを削除する
luckperms.usage.meta-removetemp-suffix.argument.priority=削除するサフィックスの優先度
luckperms.usage.meta-removetemp-suffix.argument.suffix=サフィックスの文字列
luckperms.usage.meta-removetemp-suffix.argument.context=サフィックスを削除するコンテキスト
luckperms.usage.meta-clear.description=すべてのメタを消去する
luckperms.usage.meta-clear.argument.type=削除するメタの種類
luckperms.usage.meta-clear.argument.context=フィルタリングするコンテキスト
luckperms.usage.track-info.description=トラックについての情報を表示する
luckperms.usage.track-editor.description=Web 権限エディターを開く
luckperms.usage.track-append.description=トラックの末尾にグループを追加する
luckperms.usage.track-append.argument.group=追加するグループ
luckperms.usage.track-insert.description=トラック上の指定位置にグループを挿入します。
luckperms.usage.track-insert.argument.group=挿入するグループ
luckperms.usage.track-insert.argument.position=グループの挿入位置 (トラック上の最初の位置は 1)
luckperms.usage.track-remove.description=トラックからグループを削除する
luckperms.usage.track-remove.argument.group=削除するグループ
luckperms.usage.track-clear.description=トラックからすべてのグループを削除する
luckperms.usage.track-rename.description=トラック名を変更する
luckperms.usage.track-rename.argument.name=新しい名前
luckperms.usage.track-clone.description=トラックの複製
luckperms.usage.track-clone.argument.name=複製先のトラックの名前
luckperms.usage.log-recent.description=最近の操作を表示する
luckperms.usage.log-recent.argument.user=フィルタリングするユーザーの名前または UUID
luckperms.usage.log-recent.argument.page=表示するページの番号
luckperms.usage.log-search.description=エントリのログを検索する
luckperms.usage.log-search.argument.query=検索するクエリ
luckperms.usage.log-search.argument.page=表示するページの番号
luckperms.usage.log-notify.description=ログ通知の切り替え
luckperms.usage.log-notify.argument.toggle=on または off
luckperms.usage.log-user-history.description=ユーザーの履歴を表示する
luckperms.usage.log-user-history.argument.user=ユーザーの名前または UUID
luckperms.usage.log-user-history.argument.page=表示するページの番号
luckperms.usage.log-group-history.description=グループの履歴を表示する
luckperms.usage.log-group-history.argument.group=グループ名
luckperms.usage.log-group-history.argument.page=表示するページの番号
luckperms.usage.log-track-history.description=トラックの履歴を表示する
luckperms.usage.log-track-history.argument.track=トラック名
luckperms.usage.log-track-history.argument.page=表示するページの番号
luckperms.usage.sponge.description=追加の Sponge データを編集する
luckperms.usage.sponge.argument.collection=クエリするコレクション
luckperms.usage.sponge.argument.subject=変更する Subject
luckperms.usage.sponge-permission-info.description=Subject の権限についての情報を表示する
luckperms.usage.sponge-permission-info.argument.contexts=フィルタリングするコンテキスト
luckperms.usage.sponge-permission-set.description=Subject に権限を設定する
luckperms.usage.sponge-permission-set.argument.node=設定する権限ノード
luckperms.usage.sponge-permission-set.argument.tristate=設定する権限の値
luckperms.usage.sponge-permission-set.argument.contexts=権限を設定するコンテキスト
luckperms.usage.sponge-permission-clear.description=Subject の権限を消去する
luckperms.usage.sponge-permission-clear.argument.contexts=権限を消去するコンテキスト
luckperms.usage.sponge-parent-info.description=Subject の親についての情報を表示する
luckperms.usage.sponge-parent-info.argument.contexts=フィルタリングするコンテキスト
luckperms.usage.sponge-parent-add.description=Subject に親を追加する
luckperms.usage.sponge-parent-add.argument.collection=親 Subject が存在する Subject のコレクション
luckperms.usage.sponge-parent-add.argument.subject=親 Subject の名前
luckperms.usage.sponge-parent-add.argument.contexts=親を追加するコンテキスト
luckperms.usage.sponge-parent-remove.description=Subject から親を削除する
luckperms.usage.sponge-parent-remove.argument.collection=親 Subject が存在する Subject のコレクション
luckperms.usage.sponge-parent-remove.argument.subject=親 Subject の名前
luckperms.usage.sponge-parent-remove.argument.contexts=親を削除するコンテキスト
luckperms.usage.sponge-parent-clear.description=Subject の親を消去する
luckperms.usage.sponge-parent-clear.argument.contexts=親を消去するコンテキスト
luckperms.usage.sponge-option-info.description=Subject のオプションの情報を表示する
luckperms.usage.sponge-option-info.argument.contexts=フィルタリングするコンテキスト
luckperms.usage.sponge-option-set.description=Subject のオプションを設定する
luckperms.usage.sponge-option-set.argument.key=設定するキー
luckperms.usage.sponge-option-set.argument.value=キーに設定する値
luckperms.usage.sponge-option-set.argument.contexts=オプションを設定するコンテキスト
luckperms.usage.sponge-option-unset.description=Subject からオプションを解除する
luckperms.usage.sponge-option-unset.argument.key=解除するキー
luckperms.usage.sponge-option-unset.argument.contexts=オプションを解除するコンテキスト
luckperms.usage.sponge-option-clear.description=Subject からオプションを消去する
luckperms.usage.sponge-option-clear.argument.contexts=オプションを消去するコンテキスト
