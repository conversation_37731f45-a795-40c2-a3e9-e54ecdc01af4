break-shop-use-supertool: <yellow>Puoi rompere il negozio usando il SuperTool.
fee-charged-for-price-change: <green>Hai pagato <red>{0}<green> per cambiare il prezzo.
not-allowed-to-create: <red>Non puoi creare un negozio qui.
disabled-in-this-world: <red>QuickShop è disabilitato in questo mondo
how-much-to-trade-for: <green><PERSON><PERSON><PERSON> in chat, per quanto desideri scambiare <yellow>{1}x {0}<green>.
client-language-changed: <green>QuickShop ha rilevato che l'impostazione della lingua del tuo client è stata modificata, ora stiamo utilizzando {0} locale per te.
shops-backingup: Creazione backup negozio dal database...
_comment: Ciao Traduttore, se stai modificando da GitHub o attraverso la sorgente di codice, dovresti andare su https://crowdin.com/project/quickshop-hikari.
unlimited-shop-owner-changed: <yellow>Questo proprietario di negozio illimitato è stato cambiato in {0}.
bad-command-usage-detailed: '<red>Argomenti di comando errati! Accetta i seguenti parametri: <gray>{0}'
thats-not-a-number: <red>Numero non valido
shop-name-disallowed: <red>The shop name <yellow>{0} <red>is disallowed. Pick another one!
console-only-danger: <red>Questo è un comando pericoloso, quindi solo la Console può eseguirlo.
not-a-number: <red>Puoi inserire solo un numero, il tuo input era {0}.
not-looking-at-valid-shop-block: Impossibile trovare un blocco per la creazione del negozio. Devi guardarne uno.
shop-removed-cause-ongoing-fee: <red>Il tuo negozio in {0} è stato rimosso perché non hai abbastanza soldi per mantenerlo!
tabcomplete:
  amount: '[quantità]'
  item: '[oggetto]'
  price: '[prezzo]'
  name: '[name]'
  range: '[raggio]'
  currency: '[nome della valuta]'
  percentage: 'percentuale'
taxaccount-unset: <green>La tassa di questo negozio ora segue l'impostazione globale del server.
blacklisted-item: <red>Non puoi vendere questo oggetto perché è nella blacklist
command-type-mismatch: <red>Questo comando può essere eseguito solo da <aqua>{0}.
server-crash-warning: '<red>Il server potrebbe arrestarsi in modo anomalo dopo l''esecuzione del comando /qs reload se si sostituisce/elimina il file Jar del plug-in QuickShop mentre il server è in esecuzione.'
you-cant-afford-to-change-price: <red>Questo costa {0} per cambiare il prezzo nel tuo negozio.
safe-mode: <red>QuickShop ora in modalità provvisoria, non puoi aprire questo contenitore del negozio, contatta l'amministratore del server per correggere gli errori.
forbidden-vanilla-behavior: <red>L'operazione è vietata in quanto non coerente con il comportamento vanilla
shop-out-of-space-name: <dark_purple>Il tuo negozio {0} è pieno!
paste-disabled: |-
  <red>Paste functionality has been disabled! You cannot request technical support. <newline>Reason: {0}
quick-fill:
  entry-own: ' <yellow><red>[own]<aqua>{0}<light_purple>{1}'
  hover:
    - '<yellow>Nome: <aqua>{0}'
    - '<yellow>Proprietario: <aqua>{0}'
    - '<yellow>Tipo: <aqua>{0}'
    - '<yellow>Prezzo: <aqua>{0}'
    - '<yellow>Oggetto: <aqua>{0}'
    - '<yellow>Luogo:<aqua>{0}'
  hover-arg-filled:
    - '<yellow>Nome: <aqua>{name}'
    - '<yellow>Proprietario: <aqua>{owner}'
    - '<yellow>Tipo: <aqua>{type}'
    - '<yellow>Prezzo: <aqua>{price}'
    - '<yellow>Oggetto: <aqua>{item}'
    - '<yellow>Luogo:<aqua>{location}'
  header: '<yellow>You have multiple shops with name "<green>{0}<yellow>", choose one to continue:'
  entry-normal: ' <yellow>- <aqua>{0} <light_purple>{1}'
  entry-cooperation: ' <yellow>- <red>[co] <aqua>{0} <light_purple>{1}'
3rd-plugin-build-check-failed-admin: '<gray>(AdminOnly) <light_purple>{0}<dark_gray> ha negato il controllo dei permessi. Se questo non era previsto, prova ad aggiungere <light_purple>{1} <gray>alla lista nera degli ascoltatori. Guida alla configurazione:https://ghost-chu.github.io/QuickShop-Hikari-Documents/docs/modules/shops/protection-checker'
average-price-nearby: '<green>Prezzo medio nelle vicinanze: <yellow>{0}'
inventory-check-global-alert: "<red>[Controllo Inventario] <gray>Attenzione! Trovato un elemento di visualizzazione QuickShop <gold>{2}</gold> nell'inventario di <aqua>{0}</aqua> <dark_gray>[{1}]</dark_gray>, che non dovrebbe accadere, Questo di solito significa che qualcuno sta sfruttando maliziosamente l'exploit per duplicare il display-item."
digits-reach-the-limit: <red>Hai raggiunto i limiti dei decimali nel prezzo.
currency-unset: <green>Valuta del negozio rimossa con successo. Utilizzando le impostazioni predefinite.
you-cant-create-shop-in-there: <red>Non hai il permesso di creare un negozio in questa posizione.
no-pending-action: <red>Non hai alcuna azione in sospeso
refill-success: <green>Ricarica completata
failed-to-paste: <red>Impossibile caricare i dati a Pastebin. Controlla la tua connessione internet e riprova. (Vedi console per dettagli)
shop-out-of-stock-name: <dark_purple>Il tuo negozio {0} ha esaurito {1}!
shop-name-invalid: <red>The shop name <yellow>{0} <red>is invalid. Pick another one!
how-many-buy-stack: <yellow>Inserisci in chat, la quantità che desideri <aqua>ACQUISTARE. Ci sono <yellow>{0}<green> articoli in ogni sfuso e puoi acquistare <yellow>{1}<green> all'ingrosso. Inserisci <aqua>{2}<green> nella chat per acquistare tutto.
exceeded-maximum: <red>Il valore ha superato il valore massimo in Java.
unlimited-shop-owner-keeped: '<yellow>Attenzione: il proprietario del negozio è ancora proprietario del negozio illimitato, devi reimpostare il nuovo proprietario del negozio da solo.'
no-enough-money-to-keep-shops: <red>Non hai abbastanza soldi per tenere i tuoi negozi! Tutti i negozi sono stati rimossi...
3rd-plugin-build-check-failed: <red>Il plugin di terze parti <bold>{0}<reset><red> ha negato i controlli dei permessi, hai già i permessi impostati?
not-a-integer: <red>Devi inserire un numero intero, il tuo inserimento era {0}.
translation-country: 'Lingua zona: Italian (it_IT)'
buying-more-than-selling: '<red>ATTENZIONE: Stai acquistando oggetti più di quanto li stai vendendo!'
purchase-failed: '<red>Acquisto fallito: Errore interno. Si prega di contattare l''amministratore del server.'
denied-put-in-item: <red>Non puoi mettere questo oggetto nel tuo negozio!
shop-has-changed: <red>Il negozio che hai provato ad utilizzare è cambiato da quando l'hai cliccato!
flush-finished: <green>I messaggi sono stati svuotati con successo.
no-price-given: <red>Perfavore, inserisci un prezzo valido.
shop-already-owned: <red>Questo è già un negozio.
backup-success: <green>Backup riuscito.
not-looking-at-shop: <red>Impossibile trovare un negozio. È necessario guardarne uno.
you-cant-afford-a-new-shop: <red>Il prezzo per creare un nuovo negozio è di {0}.
success-created-shop: <red>Negozio creato.
shop-creation-cancelled: <red>Creazione del negozio annullata.
shop-owner-self-trade: <yellow>Scambi con te stesso, quindi potresti non guadagnare alcun saldo.
purchase-out-of-space: <red>Questo negozio ha esaurito lo spazio, contatta il proprietario del negozio o il personale per svuotare il negozio.
reloading-status:
  success: <green>Ricarica completata senza errori.
  scheduled: <green>Ricarica completata. <gray>(Alcune modifiche hanno richiesto del tempo per essere applicate)
  require-restart: <green>Ricarica completata. <yellow>(Alcune modifiche richiedono il riavvio del server per avere effetto)
  failed: <red>Ricarica non riuscita, controlla la console del server
player-bought-from-your-store-tax: <green>{0} ha acquistato {1} {2} dal tuo negozio ed hai guadagnato {3} ({4} in tasse).
not-enough-space: <red>Hai spazio solo per altri {0}!
shop-name-success: <green>Impostato con successo il nome del negozio a <yellow>{0}<green>.
shop-staff-added: <green>Aggiunto con successo {0} come membro dello staff per il tuo negozio.
shop-staff-empty: <yellow>Questo negozio non ha membri dello staff.
shops-recovering: Recupero negozi dal backup...
virtual-player-component-hover: "<gray>Questo è un giocatore virtuale.\n<gray>Si riferisce a un account di sistema con lo stesso nome.</gray>\n<green>UUUID: <yellow>{0}</yellow></green>\n<green>Nome utente: <yellow>{1}</yellow></green>\n<green>Mostra come: <yellow>{2}</yellow></green>"
real-player-component-hover: "<gray>Questo è un vero e proprio giocatore esistente.\n<green>UUID: <yellow>{0}</yellow></green>\n<green>Nome utente: <yellow>{1}</yellow></green>\n<green>Mostra come: <yellow>{2}</yellow></green>\n<gray>Se desideri utilizzare un account di sistema virtuale con lo stesso nome, aggiungi <dark_gray>\"[]\"</dark_gray> ai lati del nome utente: <dark_gray>[{1}]</dark_gray>."
menu:
  sell-tax: <green>Hai pagato <yellow>{0} <green>tasse incluse.
  owner: '<green>Proprietario: {0}'
  preview: <aqua>[Anteprima oggetto]
  enchants: <dark_purple>Incantesimi
  sell-tax-self: <green>Non hai pagato tasse perché possiedi questo negozio.
  shop-information: '<green>Informazioni negozio:'
  item: '<green>Oggetto: <yellow>{0}'
  price-per: <green>Prezzo per <yellow>{0} <green>- <yellow>{1}
  item-name-and-price: <yellow>{0} {1} <green>per <yellow>{2}
  item-name-and-price-tax: <yellow>{0}{1}<green>per</green>{2}<gray>(<green>{3}</green> in tasse)</gray>
  successful-purchase: '<green>Acquistato con successo:'
  price-per-stack: <green>Prezzo per <yellow>{2}x {0} - {1}
  stored-enchants: <dark_purple>Incantesimi memorizzati
  item-holochat-error: <red>[Errore]
  this-shop-is-selling: <green>Questo negozio sta <aqua>VENDENDO oggetti.
  shop-stack: '<green>Quantità sfusa: <yellow>{0}'
  space: '<green>Spazio: <yellow>{0}'
  effects: <green>Effetti
  damage-percent-remaining: <yellow>{0}% <green>rimanenti.
  item-holochat-data-too-large: <red>[Errore] L'elemento NBT è troppo grande per essere mostrato
  stock: '<green>Stock <yellow>{0}'
  this-shop-is-buying: <green>Questo negozio sta <light_purple>ACQUISTANDO oggetti.
  successfully-sold: '<green>Venduto con successo:'
  total-value-of-chest: '<green>Valore totale della cassa: <yellow>{0}'
currency-not-exists: <red>Impossibile trovare la valuta che si desidera impostare. Forse l'ortografia è sbagliata o quella valuta non è disponibile in questo mondo.
no-nearby-shop: <red>Nessun negozio vicino corrispondente a {0}.
translation-author: 'Ghost_chu, Andre_601'
integrations-check-failed-trade: <red>L'ntegrazione {0} ha negato lo scambio del negozio
shop-transaction-failed: <red>Sorry, but an internal error occurred while processing your purchase. The purchase has been cancelled and any operation has been rollback. Please contact the server administrators if it keeping occurred.
success-change-owner-to-server: <green>Impostato con successo il proprietario del negozio sul server.
shop-name-not-found: <red>The shop named <yellow>{0} <red>not exists.
shop-name-too-long: <red>Il nome del negozio è troppo lungo (lunghezza massima {0}), si prega di sceglierne un altro!
metric:
  header-player: 'le transazioni per {1}{2} di <yellow>{0}:'
  action-hover: <yellow>{0}
  price-hover: <yellow>Totale {0}, includendo {1} tasse.
  unknown: <gray>(sconosciuto)
  undefined: <gray>(nessun nome)
  no-results: <red>Nessuna transazione trovata.
  action-description:
    DELETE: <yellow>Il Giocatore ha eliminato un negozio ed ha rimborsato la tassa di creazione del negozio al proprietario se possibile.
    ONGOING_FEE: <yellow>Il Giocatore ha pagato la tassa attuale grazie al periodo di pagamento.
    PURCHASE_BUYING_SHOP: <yellow>Il Giocatore ha venduto degli oggetti ad un negozio che li acquistava.
    CREATE: <yellow>Il Giocatore ha creato un negozio.
    PURCHASE_SELLING_SHOP: <yellow>Il Giocatore ha comprato degli oggetti da un negozio che li vendeva
    PURCHASE: <yellow>Ha comprato oggetti tramite un negozio
  query-argument: 'Argomento della query: {0}'
  amount-hover: <yellow>{0}x
  header-shop: '<yellow>Le transazioni {1} {2} del {0} del negozio:'
  player-hover: |-
    <yellow>{0}<newline><gold>UUID: <gray>{1}
  looking-up: <yellow>Esecuzione della ricerca metrica in corso, per favore attendere...
  tax-hover: <yellow>{0} tasse
  header-global: '<yellow>Le transazioni {0} {1} del server:'
  na: <gray>N/A
  transaction-count: <yellow>{0} totale
  shop-hover: |-
    <yellow>{0}<newline><gold>Posizione: <gray>{1} {2} {3}, Mondo: {4}<newline><gold>Proprietario: <gray>{5}<newline><gold>Tipo di Negozio: <gray>{6}<newline><gold>Oggetto: <gray>{7}<newline><gold>Prezzo: <gray>{8}
  time-hover: '<yellow>Tempo: {0}'
  amount-stack-hover: <yellow>{0}pila x
permission-denied-3rd-party: '<red>Permesso negato: Plugin di terze parti [{0}].'
you-dont-have-that-many-items: <red>Hai solo {0}{1}.
complete: <green>Completato!
translate-not-completed-yet-url: 'La traduzione della lingua {0} non è stata ancora completata da {1}. Vuoi aiutarci a migliorare la traduzione? Sfoglia: {2}'
success-removed-shop: <red>Negozio rimosso
currency-set: <green>Valuta del negozio impostata correttamente su {0}.
shop-purged-start: <green>Eliminazione negozio avviata, controlla la console per i dettagli.
economy-transaction-failed: <red>Sorry, but an internal error occurred while processing your transaction. The transaction has been cancelled and any economy operation has been rollback. Please contact the server administrators if it keeping occurred.
nothing-to-flush: <green>Non hai nuovi messaggi nel negozio.
no-price-change: <red>Questo non comporterebbe una variazione di prezzo!
edition-confilct: QuicShop-Hikari with QuickShop-Reremake installed may conflict each other, uninstall one of them.
inventory-unavailable: |-
  <red>This shop InventoryWrapper not exists or invalid. Did you using addon to re-binding the shop Inventory? Information: InventoryWrapper={0}, WrapperProvider={1}, SymbolLink={2}. Please contact the server administrators.
file-test: Questo è un file di testo di prova. Lo usiamo per verificare se il file messages.json è rotto. Puoi riempirlo con tutti gli easter egg che ti piacciono qui :)
unknown-player: <red>Il giocatore di destinazione non esiste, per favore controlla il nome utente digitato.
player-offline: <red>Il giocatore scelto è attualmente offline.
player-profile-format: <aqua>{0}</aqua><gold>(<yellow>{1}</yellow>)</gold>
shop-type:
  selling: VENDITA
  buying: ACQUISTA
language:
  qa-issues: '<yellow>Problemi di garanzia della qualità: <aqua>{0}%'
  code: '<yellow>Codice: <gold>{0}'
  approval-progress: '<yellow>Progresso di approvazione: <aqua>{0}%'
  translate-progress: '<yellow>Progresso di traduzione: <aqua>{0}%'
  name: '<yellow>Nome: <gold>{0}'
  help-us: <green>[Aiutaci a migliorare la qualità della traduzione]
warn-to-paste: |-
  <yellow>Raccogliere i dati e caricarli su Pastebin, potrebbe richiedere un po' di tempo. <red><bold>Warning:<red> I dati sono tenuti pubblici per una settimana! Potrebbe mostrare la configurazione del server e altre informazioni sensibili. Assicurati di inviarlo solo allo <bold>staff/sviluppatori fidati.
how-many-sell-stack: <green>Inserisci nella chat, quante quantità desideri <light_purple>VENDERE. Ci sono <yellow>{0}<green> articoli per sfuso e puoi vendere <yellow>{1}<green> all'ingrosso. Inserisci <aqua>{2}<green> nella chat per vendere tutto.
updatenotify:
  buttontitle: '[Aggiorna ora]'
  onekeybuttontitle: '[OneKey Update]'
  label:
    github: '[GitHub]'
    ore: '[Ore]'
    lts: '[LTS]'
    nbtapi: '[+NBTAPI]'
    qualityverifyed: '[Quality]'
    master: '[Master]'
    unstable: '[Unstable]'
    paper: '[+Paper Optimized]'
    stable: '[Stable]'
    spigotmc: '[SpigotMC]'
    papermc: '[PaperMC]'
    bukkitdev: '[BukkitDev]'
    polymart: '[Polymart]'
    modrinth: 'Modrinth'
    basic: '[Basic]'
  list:
    - '{0} è stato rilasciato. Stai ancora usando {1}!'
    - Boom! Nuovo aggiornamento {0} in arrivo. Aggiorna!
    - Sorpresa! {0} è uscito. Stai usando {1}
    - Sembra che tu abbia bisogno di un aggiornamento. {0} è stato rilasciato!
    - Ooops! {0} è stato rilasciato. Sei il {1}!
    - Lo giuro... QS è stato aggiornato a {0}. Perché non hai ancora aggiornato?
    - Fix e... Scusa, ma {0} è stato rilasciato!
    - Err... No! Questo non è un errore. {0} è stato rilasciato!
    - Ah! {0} è uscito! Perché stai ancora usando {1}?
    - 'Notizia del giorno: QuickShop è stato aggiornato a {0}!'
    - Aggiorna a {0}. Mi sono stufato di inventarmi un messaggio per implorarti di aggiornare!
    - Aggiornamento {0} innescato. Salva l'aggiornamento!
    - C'è un aggiornamento disponibile per lei, Comandante. {0} è appena uscito!
    - Guarda il mio stile---{0} aggiornato. Stai ancora usando {1}
    - Ahhhhhhh! Nuovo aggiornamento {0}! Aggiornami!
    - Cosa ne pensi? {0} è stato rilasciato! Aggiorna!
    - Dottore, QuickShop ha un nuovo aggiornamento {0}! Dovresti aggiornare~
    - Ko~ko~da~yo~ QuickShop ha un nuovo aggiornamento {0}~
    - Paimon vuole dirti che QuickShop ha un nuovo aggiornamento {0}!
  remote-disable-warning: '<red>Questa versione di QuickShop è contrassegnata come disabilitata dal server remoto, il che significa che questa versione potrebbe avere seri problemi, ottieni i dettagli dalla nostra pagina SpigotMC: {0}. Questo avviso continuerà a essere visualizzato finché non passerai a una versione stabile, ma non influirà sulle prestazioni del tuo server.'
purchase-out-of-stock: <red>Questo negozio ha esaurito le scorte, contatta il proprietario del negozio o il personale per rifornire le scorte.
nearby-shop-entry: '<green>- Info:{0} <green>Prezzo:<aqua>{1} <green>x:<aqua>{2} <green>y:<aqua>{3} <green>z:<aqua>{4} <green>distanza: <aqua>{5} <green>blocchi'
chest-title: Negozio QuickShop
console-only: <red>Questo comando può essere eseguito solo da Console.
failed-to-put-sign: <red>Spazio insufficiente intorno al negozio per inserire le informazioni.
shop-name-unset: <red>Il nome di questo negozio è stato rimosso
shop-nolonger-freezed: <green>Hai scongelato il negozio. È tornato alla normalità!
no-permission-build: <red>Non puoi costruire un negozio qui.
tableformat:
  left_half_line: <dark_purple>+--------------------
  right_half_line: <dark_purple>--------------------+
  full_line: <dark_purple>+---------------------------------------------------+
  left_begin: '<dark_purple>| '
quickshop-gui-preview: Anteprima Articolo GUI QuickShop
translate-not-completed-yet-click: La traduzione della lingua {0} non è stata ancora completata da {1}. Vuoi aiutarci a migliorare la traduzione? Clicca qui!
taxaccount-invalid: <red>Account target non valido, inserisci un nome giocatore valido o uuid (con trattini).
player-bought-from-your-store: <red>{0} ha acquistato {1} {2} dal tuo negozio, e hai guadagnato {3}.
reached-maximum-can-create: <red>Hai già creato un massimo di {0}/{1} negozi!
reached-maximum-create-limit: <red>Hai raggiunto il numero massimo di negozi che puoi creare
translation-version: 'Versione di Supporto: Hikari'
no-double-chests: <red>Non puoi creare un negozio con doppia cassa.
price-too-cheap: <red>Il prezzo deve essere superiore di <yellow> ${0}
shop-not-exist: <red>Non c'è nessun negozio.
bad-command-usage: <red>Argomento di comando errato!
cleanghost-warning: <yellow>This command will purge <red>all</red> shops if the shop is corrupted, was created in not allowed worlds, is selling/buying not allowed items or <red><bold>EXISTS IN A UNLOADED WORLD</bold></red>. Make sure to create a full backup of your shop data first and use <aqua>/qs cleanghost confirm</aqua> to continue.
cleanghost-starting: <green>Inizio a controllare se ci sono negozi fantasmi (contenitori mancanti). Tutti i negozi non esistenti verranno rimossi...
cleanghost-deleting: <yellow>Trovato un negozio corrotto <aqua>{0}</aqua> a causa di {1}, segnalo per eliminarlo...
cleanghost-deleted: <green>In totale <yellow>{0}</yellow> negozi sono stati cancellati.
shop-purchase-cancelled: <red>Acquisto annullato.
bypassing-lock: <red>Bypassa un blocco QuickShop!
bungee-cross-server-msg: '<yellow>QuickShop CSM: <green>{0}'
saved-to-path: Il backup è stato salvato in {0}.
shop-now-freezed: <green>Hai congelato il negozio. Nessuno può interagire con questo negozio!
price-is-now: <green>Il nuovo prezzo del negozio è <yellow>{0}
shops-arent-locked: <red>Ricorda che i negozi NON sono protetti dal furto! Se vuoi fermare i ladri, bloccali con LWC, Lockette, ecc!
that-is-locked: <red>Questo negozio è bloccato.
shop-has-no-space: <red>Il negozio ha spazio solo per {0} in più {1}.
safe-mode-admin: '<red><bold>ATTENZIONE: <red>Il QuickShop su questo server ora funziona in modalità provvisoria, nessuna funzione funzionerà, per favore digita il comando <yellow>/qs <red> per controllare eventuali errori.'
shop-stock-too-low: <red>Il negozio ha solo {0} {1} rimanenti!
world-not-exists: <red>Il mondo <yellow>{0}<red> non esiste
how-many-sell: <green>Inserisci in chat, quanti desideri <aqua>VENDERE<green>. Puoi vendere <yellow>{0}<green>. Inserisci <aqua>{1}<green> in chat per venderli tutti.
shop-freezed-at-location: <yellow>Il tuo negozio {0} a {1} è stato congelato!
translation-contributors: 'Collaboratori: Timtower, Netherfoam, KaiNoMood, Mgazul, JackTheChicken and Andre_601, sandtechnology, MiniMoro'
empty-success: <green>Svuotamento negozio riuscito
taxaccount-set: <green>La tassa di questo negozio è stato impostato su <yellow>{0}
support-disable-reason:
  hot-reload: <yellow>Unsupported hot reloaded, restart the server and check again.
  outdated: <yellow>This version of QuickShop already outdated, up-to-date before requesting support!
  bad-hosts: |-
    <yellow>This server HOSTS has been modified and QuickShop some functions require connect to Mojang API correctly. Fix the HOSTS before asking support, Windows at C:\windows\system32\drivers\etc\hosts, Linux at /etc/hosts.
  privacy: <yellow>This server running under cracked (offline) mode. If you are running bukkit server under proxy and online-mode=true on proxy, please configure the proxy related settings correctly.
  modified: <yellow>File integrity check failed, this QuickShop build has been modified by others.
  consolespamfix-installed: <yellow>ConsoleSpamFix installed, it will hidden exception details, temporarily disable it during asking support.
  authlib-injector-detected: <yellow>This server running under 3rd-party authlib provider like authlib-injector.
  unsupported-server-software: <yellow>Unsupported server software, Any modded hybrid server software is unsupported including MCPC, Cauldron, CatServer, Mohist, Magma, Fukkit, Cardboard etc.
supertool-is-disabled: <red>Supertool è disabilitato. Impossibile rompere alcun negozio.
unknown-owner: '&2Sconosciuto'
restricted-prices: '<red>Prezzo ristretto per {0}: Minimo {1}, max {2}'
nearby-shop-this-way: <green>Il negozio è a {0} blocchi di distanza da te.
owner-bypass-check: <yellow>Bypassato tutti i controlli. Eseguito con successo! (Ora sei il proprietario del negozio!)
april-rick-and-roll-easter-egg: "<hover:show_text:'Click to open in browser for time limited rewards!'><click:open_url:'https://www.youtube.com/watch?v=dQw4w9WgXcQ'><green>Clicca qui per ricevere i tuoi premi a tempo limitato, messi in palio dallo sviluppatore QuickShop-Hikari!</green></click></hover>"
signs:
  item-right: ''
  out-of-space: Spazio pieno
  unlimited: Illimitato
  stack-selling: Vendi {0}
  stack-price: '{0} per {1}x {2}'
  status-unavailable: <red>
  out-of-stock: Esaurito
  stack-buying: Acquisto {0}
  freeze: Scambio disabilitato
  price: '{0} ciascuno'
  buying: Acquisto {0}
  header: '{1}{0}'
  header-available: <green>{0}
  header-unavailable: <red>{0}
  selling: Vendi {0}
  status-available: <green>
  item-left: ''
negative-amount: <red>Non puoi scambiare importi negativi
display-turn-on: <green>Accensione riuscita del display del negozio.
shop-staff-deleted: <green>Rimosso {0} come membro dello staff per il tuo negozio.
nearby-shop-header: '<green>Abbinamento negozio nelle vicinanze <aqua>{0}<green>:'
backup-failed: Impossibile eseguire il backup del database. Controlla la console per i dettagli.
shop-staff-cleared: <green>Rimossi con successo tutti i membri dello staff dal tuo negozio.
price-too-high: <red>Il prezzo del negozio è troppo alto! Non puoi crearne uno con un prezzo superiore a {0}.
plugin-cancelled: '<red>Operation cancelled, Reason: {0}'
player-sold-to-your-store: <green>{0} ha venduto {1} {2} al tuo negozio.
shop-out-of-stock: <dark_purple>Il tuo negozio in {0}, {1}, {2} è esaurito {3}!
how-many-buy: <green>Inserisci in chat, quanti desideri <aqua>ACQUISTARE<green>. Puoi comprare <yellow>{0}<green>. Inserisci <aqua>{1}<green> per comprarli tutti.
language-info-panel:
  help: 'Aiutaci: '
  code: 'Codice: '
  name: 'Lingua: '
  progress: 'Progresso: '
  translate-on-crowdin: '[Traduci su Crowdin]'
item-not-exist: <red>L'elemento <yellow>{0} <red>non esiste, controlla la tua ortografia.
shop-creation-failed: <red>Creazione del negozio non riuscita, contatta l'amministratore del server.
inventory-space-full: <red>Your inventory space remains can only put <green>{1}x <red>items into it, try empty the your inventory!
no-creative-break: <red>Non puoi rompere i negozi degli altri giocatori in modalità creativa, passa alla modalità Sopravvivenza o prova ad usare il supertool {0}.
booleanformat:
  success: <green>✔
  failed: <red>✘
controlpanel:
  stack: '<green>Per la quantità del carico: <aqua>{0}<yellow>[<light_purple><bold>Change<yellow>]'
  price-hover: <yellow>Clicca per impostare un nuovo prezzo per l'articolo.
  remove: <red><bold>[Rimuovi negozio]
  mode-buying-hover: <yellow>Clicca per cambiare la modalità del negozio in VENDITA.
  empty: '<green>Vuoto: rimuovi tutti gli oggetti <yellow>[<light_purple><bold>OK<yellow>]'
  stack-hover: <yellow>Fare clic per impostare la quantità di articolo per sfuso. Imposta a 1 per comportamento normale.
  alwayscounting-hover: <yellow>Fai clic per attivare/disattivare se il negozio conta sempre il contenitore.
  alwayscounting: '<green>Conta sempre: {0}<yellow>[<light_purple><bold>Change<yellow>]'
  setowner: '<green>Proprietario: <aqua>{0} <yellow>[<bold><light_purple>Cambia</light_purple></bold>]'
  freeze: '<yellow>Modalità congelato: <aqua>{0}<yellow>[<light_purple><bold>Toggle<yellow>]'
  price: '<green>Prezzo: <aqua>{0} <yellow>[<bold><light_purple>Cambia</light_purple></bold>]'
  currency-hover: <yellow>Clicca per impostare o rimuovere la valuta che questo negozio sta usando
  lock: '<yellow>Negozio chiuso: <aqua>{0}<yellow>[<light_purple><bold>Toggle<yellow>]'
  mode-selling: '<green>Modalità negozio: <aqua>Vendita <yellow>[<bold><light_purple>Cambia</light_purple></bold>]'
  currency: '<green>Valuta: <aqua>{0}<yellow>[<light_purple><bold>Set<yellow>]'
  setowner-hover: <yellow>Clicca per cambiare proprietario.
  mode-buying: '<green> Modalità del negozio: <aqua>Acquisto <yellow>[<bold><light_purple>Cambia</light_purple></bold>]'
  item: '<green>Oggetto del negozio: {0}<yellow>[<light_purple><bold>Change<yellow>]'
  unlimited: '<green>Illimitato: {0} <yellow>[<bold><light_purple>Cambia</light_purple></bold>]'
  unlimited-hover: <yellow>Clicca per attivare/disattivare se il negozio è illimitato.
  refill-hover: <yellow>Clicca per ricaricare il negozio.
  remove-hover: <yellow>Clicca per rimuovere questo negozio.
  toggledisplay-hover: <yellow>Attiva/disattiva lo stato dell'oggetto visualizzato nel negozio
  refill: '<green>Riempi: ricarica gli oggetti <yellow>[<light_purple><bold>OK<yellow>]'
  freeze-hover: <yellow>Attiva lo stato di blocco del negozio.
  lock-hover: <yellow>Abilita/Disabilita la protezione del blocco del negozio.
  item-hover: <yellow>Click per cambiare l'elemento del negozio
  infomation: '<green>Pannello di Controllo Negozio:'
  mode-selling-hover: <yellow>Clicca per cambiare la modalità del negozio in ACQUISTO.
  empty-hover: <yellow>Clicca per cancellare l'inventario del negozio.
  toggledisplay: '<green>Display-Item: <aqua>{0} <yellow>[<bold><light_purple>Cambia</light_purple></bold>]'
  history: '<green>Storico: <yellow>[<bold><light_purple>Visualizza</light_purple></bold>]</yellow>'
  history-hover: <yellow>Clicca per visualizzare i log della cronologia del negozio
timeunit:
  behind: dietro
  week: "{0} settimana"
  weeks: "{0} settimane"
  year: "{0} anno"
  before: prima
  scheduled: pianificato
  years: "{0} anni"
  scheduled-in: pianificata in {0}
  second: "{0} secondo"
  std-past-format: '{5}{4}{3}{2}{1}{0}fa'
  std-time-format: H:mm:ss
  seconds: "{0} secondi"
  hour: "{0} ora"
  scheduled-at: pianificata in {0}
  after: dopo
  day: "{0} giorno"
  recent: recente
  between: compreso tra
  hours: "{0} ore"
  months: "{0} mesi"
  longtimeago: molto tempo fa
  between-format: tra {0} e {1}
  minutes: "{0} minuti"
  justnow: in questo momento
  minute: "{0} minuto"
  std-format: dd/MM/yyyy HH:mm:ss
  future-plain-text: futuro
  month: "{0} mese"
  future: in {0}
  days: "{0} giorni"
command:
  reloading: '<green>Configuration reloaded. <yellow>Some changes may require reboot to affect. <newline><gray>(Notice: Reloading behavior has been changed after ********, we now only reload configuration but not whole plugin to ensure the server won''t crashed.)'
  description:
    buy: <yellow>Cambia un negozio in modalità di <light_purple>ACQUISTO<yellow>
    about: <yellow>Mostra informazioni di QuickShop
    language: <yellow>Cambia la lingua attualmente in uso
    purge: <yellow>Avvia l'attività di eliminazione del negozio in background
    paste: <yellow>Carica i dati del server a Pastebin
    title: <green>Aiuto di QuickShop
    remove: <yellow>Rimuove il negozio che stai guardando
    ban: <yellow>Bannato il giocatore dal negozio
    empty: <yellow>Rimuove tutti gli elementi da un negozio
    alwayscounting: <yellow>Set if shop always counting container even is unlimited
    setowner: <yellow>Cambia la proprietà di un negozio.
    reload: <yellow>Ricarica il file config.yml di QuickShop
    freeze: <yellow>Disabilita o abilita il commercio in negozio
    price: <yellow>Cambia il prezzo di acquisto/vendita di un negozio
    find: <yellow>Trova il negozio più vicino di un tipo specifico.
    create: <yellow>Crea un nuovo negozio dalla cassa selezionata
    lock: <yellow>Cambia lo stato di blocco del negozio
    currency: <yellow>Imposta o rimuovi l'impostazione della valuta del negozio
    removeworld: <yellow>Rimuovi tutti i negozi in uno specifico mondo
    info: <yellow>Mostra statistiche di QuickShop
    owner: <yellow>Cambia la proprietà di un negozio.
    amount: <yellow>Per impostare la quantità di elementi (utile quando si hanno problemi in chat)
    item: <yellow>Cambia l'oggetto del negozio di un negozio
    debug: <yellow>Abilita modalità sviluppatore
    unlimited: <yellow>Dà a un negozio scorte illimitate.
    sell: <yellow>Cambia un negozio in modalità di <aqua>VENDITA<yellow>
    fetchmessage: <yellow>Mostra messaggi negozio non letti
    staff: <yellow>Gestisci il personale del negozio
    clean: <yellow>Rimuove tutti i negozi (caricati) senza scorte
    refill: <yellow>Aggiunge un determinato numero di articoli a un negozio
    help: <yellow>Mostra aiuto QuickShop
    removeall: <yellow>Rimuovi TUTTI i negozi di un giocatore specificato
    unban: <yellow>Sbannato il giocatore dal negozio
    transfer: <yellow>Trasferisci tutti i negozi di qualcuno ad un altro player
    transferall: <yellow>Trasferisci tutti i negozi di qualcuno ad un altro player
    transferownership: <yellow>Trasferisci a qualcun altro il negozio che stai guardando
    size: <yellow>Modifica per importo all'ingrosso di un negozio
    supercreate: <yellow>Crea un negozio aggirando tutti i controlli di protezione
    taxaccount: <yellow>Imposta la tassa utilizzata dal negozio
    name: <yellow>Naming a shop to specific name
    toggledisplay: <yellow>Attiva/disattiva lo stato dell'articolo di visualizzazione del negozio
    permission: <yellow>Gestione autorizzazioni negozio
    lookup: <yellow>Gestisce la tabella degli elementi ricercati
    database: <yellow>View and maintain QuickShop database
    benefit: <yellow>Impostazioni per dividere i benefici tra il proprietario del negozio e gli altri giocatori
    tag: <yellow>Aggiungi, rimuovi o interroga i tag di un negozio
    suggestprice: <yellow>Suggerisci un prezzo consigliato per cercare negozio, basato su altri negozi
    history: <yellow>Visualizza la cronologia delle transazioni di un negozio
    sign: <yellow>Cambia il materiale del cartello di un negozio
  bulk-size-not-set: '<red>Utilizza: /qs size <amount>'
  no-type-given: '<red>Utilizza: /qs find <item>'
  feature-not-enabled: Questa funzione non è abilitata nel file di configurazione.
  now-debuging: <green>Modalità sviluppatore attivata con successo. Ricaricando QuickShop...
  no-amount-given: <red>Nessun importo fornito. Usa <green>/qs refill <amount><red>
  no-owner-given: <red>Nessun proprietario fornito
  disabled: '<red>Questo comando è disabilitato: <yellow>{0}'
  bulk-size-now: <green>Scambio <yellow>{0}x {1}
  toggle-always-counting:
    counting: Il negozio ora conta sempre il contenitore anche se è illimitato
    not-counting: <green>Il negozio non conta più il contenitore se è illimitato
  cleaning: <green>Rimozione negozi senza scorte...
  now-nolonger-debuging: <green>Modalità sviluppatore disabilitata con successo. Ricaricando QuickShop...
  toggle-unlimited:
    limited: <green>Il negozio è ora limitato
    unlimited: <green>Il negozio è ora illimitato
  transfer-success-other: <green>Trasferiti <yellow>{0} {1}<green> negozi a <yellow>{2}
  no-trade-item: <green>Si prega di tenere un oggetto di scambio per cambiare nella mano principale
  wrong-args: <red>Argomento non valido. Usa <bold>/qs help <red>per vedere un elenco di comandi.
  some-shops-removed: <yellow>{0} <green>shop rimosso/i
  new-owner: '<green>Nuovo proprietario: <yellow>{0}'
  format: <green>/{0} {1} <yellow>- {2}
  transfer-success: <green>Trasferiti <yellow>{0} <green>shop(s) a <yellow>{1}
  now-buying: <green>Ora stai <light_purple>ACQUISTANDO <yellow>{0}
  now-selling: <green>Ora stai <aqua>VENDENDO <yellow>{0}
  cleaned: <green>Rimosso <yellow>{0} <green>negozi.
  trade-item-now: <green>Scambio <yellow>{0}x {1}
  no-world-given: <red>Sei pregato di specificare il nome del mondo
  format-disabled: <red>/{0} {1} <gray>- {2}
  invalid-bulk-amount: <red>Il valore specificato {0} è maggiore della dimensione massima dello stack o inferiore a uno
currency-not-support: <red>Il plugin dell'economia non supporta la funzione multi-economia.
trading-in-creative-mode-is-disabled: <red>Non puoi scambiare con questo negozio mentre sei in modalità creativa.
the-owner-cant-afford-to-buy-from-you: <red>Questo costa {0}, ma il proprietario del negozio ha solo {1}
you-cant-afford-shop-naming: <red>You can't afford shop naming, it costs {0} to naming.
inventory-error: |-
  <red>Failed to processing the InventoryWrapper. Did you using addon to re-binding the shop Inventory? Information: Exception={0}, InventoryWrapper={1}, WrapperType={2}, WrapperProvider={3}, SymbolLink={4}. Please contact the server administrators.
integrations-check-failed-create: <red>L'integrazione {0} ha negato la creazione del negozio
shop-out-of-space: <dark_purple>Il tuo negozio in {0}, {1}, {2} è ora pieno.
admin-shop: '&6&LNegozio Admin'
no-anythings-in-your-hand: <red>Non hai nulla in mano.
no-permission: <red>Non hai il permesso per farlo.
chest-was-removed: <red>La cassa è stata rimossa.
you-cant-afford-to-buy: <red>Questo costa {0}, ma tu hai solo {1}
shops-removed-in-world: <aqua>{0}<yellow> negozi sono stati eliminati nel mondo <aqua>{1}<yellow>.
display-turn-off: <green>Spegnimento con successo il display del negozio.
client-language-unsupported: <yellow>QuickShop non supporta la lingua del tuo client, ora siamo utilizzando {0} locale.
language-version: '63'
not-managed-shop: <red>Non sei il proprietario o moderatore di questo negozio
shop-cannot-trade-when-freezing: <red>Non puoi interagire con questo negozio perché è congelato.
invalid-container: <red>Container non valido, puoi creare uno shop soltanto su un blocco che hai nell'inventario.
permission:
  header: Dettagli dei Permessi del Negozio
  header-player: <green>Dettagli permessi del negozio per {0}
  header-group: <green>Dettagli permessi del negozio per il gruppo {0}
  table: <gold><b>{0}</b>:</gold> <gray>{1}</gray>
  item:
    purchase: <yellow>Permission to allow users who have this to purchase the shop. (including buying and selling)
    show-information: <yellow>Permesso di consentire agli utenti che hanno questo permesso di vedere le informazioni del negozio. (aprire l'info panel del negozio)
    preview-shop: <yellow>Consenti agli utenti che hanno questo permesso di visualizzare in anteprima il negozio. (elemento di anteprima)
    search: <yellow>Permission to allow users who have this to search the target shop. (remove permission will hide shop from search result)
    delete: <yellow>Consenti agli utenti che hanno questo permesso, di eliminare il negozio.
    receive-alert: <yellow>Permission to allow users who have this to receive alert message (e.g out-of-stock or new trade).
    access-inventory: <yellow>Consenti agli utenti che hanno questo permesso, di avere accesso all'inventario del negozio.
    ownership-transfer: <yellow>Consenti agli utenti che hanno questo permesso di trasferire la proprietà del negozio.
    management-permission: <yellow>Permission to allow users who have this to manage the groups permissions and edit user's group.
    toggle-display: <yellow>Permesso per consentire agli utenti scelti di attivare gli oggetti in mostra del negozio.
    set-shoptype: <yellow>Permesso per consentire agli utenti scelti di scegliere il tipo di negozio (cambiare ad acquisto o vendita).
    set-price: <yellow>Permesso di consentire agli utenti scelti d'impostare il prezzo del negozio.
    set-item: <yellow>Permesso per consentire agli utenti scelti d'impostare l'oggetto del negozio.
    set-stack-amount: <yellow>Permesso per consentire agli utenti scelti d'impostare la quantità del carico del negozio.
    set-currency: <yellow>Permesso per consentire agli utenti scelti d'impostare la valuta del negozio.
    set-name: <yellow>Permesso per consentire agli utenti scelti di impostare il nome del negozio.
    set-sign-type: <yellow>Cambia il materiale del cartello del negozio.
    view-purchase-logs: <yellow>Permesso di visualizzare i log di acquisto del negozio.
  group:
    everyone: <yellow>Gruppo predefinito per tutti gli utenti eccetto il proprietario del negozio.
    staff: <yellow>Gruppo di sistema per il personale del negozio.
    administrator: <red>Gruppo di sistema per gli amministratori, gli utenti in questo gruppo avranno permessi quasi alla pari del proprietario del negozio.
invalid-group: <red>Nome del gruppo non valido.
invalid-permission: <red>Permesso non valido.
invalid-operation: <red>Operazione non valida, solo {0} sono permessi.
player-no-group: <yellow>Il Giocatore {0} non è in nessun gruppo in questo negozio.
player-in-group: <green>Il Giocatore {0} è nel gruppo <aqua>{1}</aqua> di questo negozio.
permission-required: <red>Non hai il permesso {0} per farlo in questo negozio.
no-permission-detailed: <red>Non hai il permesso <yellow>{0}</yellow> per farlo.
paste-notice: "<yellow>Note: If you are creating a Paste for troubleshooting purposes, be sure to reproduce the error before creating the Paste as quickly as possible; we need the logs retained briefly in the buffer to troubleshoot. If you create a Paste too slowly or without first reproducing the error or restarted server, the Paste will record nothing and useless."
paste-uploading: <aqua>Si prega di attendere... Caricamento del testo al pastebin......
paste-created: '<green>Paste creata, clicca per aprirlo nel browser: <yellow>{0}</yellow><newline><red>Attenzione: <gray>Non mandare mai la paste a persone di cui non ti fidi.'
paste-created-local: |-
  <green>Paste creata e salvata nel tuo disco rigido su: {0}<newline><red>Attenzione: <gray>Non mandare mai la paste a persone di cui non ti fidi.
paste-created-local-failed: <red>Fallimento durrante il salvataggio della paste nel tuo disco rigido.
paste-451: |-
  <gray><b>CONSIGLI: </b> La tua regione o paese attuale sembra aver bloccato il servizio di CloudFlare Workers e il QuickShop Paste potrebbe non caricare correttamente.<newline><gray>Se l'operazione successiva fallisce, prova ad aggiungere il parametro aggiuntivo --file per generare una Paste locale: <dark_gray>/qs paste --file</dark_gray>
paste-upload-failed: <red>Impossibile caricare la paste su {0}, stiamo provando un altro fornitore di pastebin...
paste-upload-failed-local: <red>Impossibile caricare la pasta, stiamo cercando di generare una paste locale...
command-incorrect: '<red>Uso del commando incorretto, scrivi /qs help per controllare la pagina d''aiuto. Uso: {0}.'
successfully-set-player-group: <green>Assegnato il gruppo giocatore {0} a <aqua>{1}</aqua> con successo.
successfully-unset-player-group: <green>Rimosso il gruppo giocatore dal negozio con successo.
successfully-set-player-permission: <green>Permesso <aqua>{1}</aqua> del giocatore {0} impostato con successo nel negozio <aqua>{2}</aqua>.
lookup-item-created: <green>Un oggetto chiamato <aqua>{0}</aqua> è stato creato nella tabella di ricerca. Ora puoi fare riferimento a questo oggetto nelle configurazioni.
lookup-item-exists: <red>Un oggetto chiamato <yellow>{0}</yellow> esiste già nella tabella di ricerca. Cancellalo o scegli un altro nome.
lookup-item-not-found: <red>Un oggetto chiamato <yellow>{0}</yellow> non esiste.
lookup-item-name-illegal: <red>Nome dell'oggetto impossibile. Solo caratteri alfanumerici e trattini bassi sono consentiti.
lookup-item-name-regex: '<red>Il nome deve corrispondere a questa espressione regolare: <aqua>{0}</aqua>.'
lookup-item-test-not-found: '<gold>Test: <yellow>L''oggetto nella tua mano non è registrato nella tabella di ricerca.'
lookup-item-test-found: '<gold>Test: <green>L''oggetto nella tua mano è stato registrato sotto il nome di <aqua>{0}</aqua> nella tabella di ricerca.'
lookup-item-removed: <green>L'oggetto specifico <aqua>{0}</aqua> è stato rimosso con successo dalla tabella di ricerca.
internal-error: <red>Si è verificato un errore interno, si prega di contattare l'amministratore del server.
argument-cannot-be: <red>L'argomento <aqua>{0}</aqua> non può essere <yellow>{1}</yellow>.
argument-must-between: <red>The argument <aqua>{0}</aqua> value must between <yellow>{1}</yellow> and <yellow>{2}</yellow>
invalid-percentage: <red>Percentuale non valida, è necessario aggiungere '%' dopo il numero percentuale.
display-fallback: |-
  <red>A causa di un errore interno, viene visualizzato un messaggio di fallback.
  Il valore di questo elemento potrebbe non essere localizzato o elaborato correttamente.
  Si prega di contattare l'amministratore del server.
not-a-valid-time: |-
  <red>The string <yellow>{0}</yellow> not a valid timestamp, please enter a <aqua>Zulu Time (ISO 8601)</aqua> or <aqua>Unix Epoch Time in seconds</aqua>.
  <gold>Valid timestamp example: (for Sat, 17 Dec 2022 10:31:37 GMT)</gold>
  <aqua>-<aqua> <yellow>2022-12-17T10:31:37Z</yellow> <grey>(Zulu Time)</grey>
  <aqua>-<aqua> <yellow>1671273097</yellow> <grey>(Unix Epoch Time in seconds)</grey>
invalid-past-time: <red>Non è possibile specificare un'ora nel passato.
debug:
  arguments-invalid: <red>Argomenti forniti <yellow>{0}</yellow> non validi.
  sign-located: '<green>Segno valido: <yellow>{0}</yellow>.'
  operation-missing: <red>È necessario specificare un'operazione.
  operation-invalid: <red>È necessario specificare un'operazione valida.
  invalid-base64-encoded-sql: <red> L'SQL fornito deve essere codificato con base64.
  warning-sql: |-
    <red><bold>Attenzione: </bold></red><yellow>Stai eseguendo uno statement SQL, ciò potrebbe corrompere la tua banca dati o distruggere qualsiasi dato dentro di essa anche se appartiene ad altri plugin.
    <red>Non confermare se non ti fidi di chi ti ha mandato questo comando.
  warning-sql-confirm: <yellow>Per confermare questa pericolosa operazione, per favore scrivi <aqua>/qs debug database sql confirm {0}</aqua> entro 60 secondi.
  warning-sql-confirm-hover: <yellow>Clicca per confermare questa operazione pericolosa.
  sql-confirm-not-found: <yellow>L'operazione che hai fornito non è stata trovata, potrebbe essere non valida o scaduta.
  sql-executing: '<yellow>Esecuzione dello statement SQL: <aqua>{0}</aqua>'
  sql-completed: <green>Completato, {0} righe affette.
  sql-exception: <red>Si è verificato un errore durante l'esecuzione della query SQL, controlla la Console per ulteriori dettagli!
  sql-disabled: '<red>Per ragioni di sicurezza, le query SQL sono disabilitate su questo server. Se hai davvero bisogno di questa funzione, puoi aggiungere il seguente flag a questi argomenti di avvio: <aqua>{0}</aqua> con il valore `true` per attivarlo.'
  force-shop-reload: <yellow>Forzando il reload di tutti i negozi caricati...
  force-shop-reload-complete: <green>Forzato<aqua>{0}</aqua> il reload di tutti i negozi.
  force-shop-loader-reload: <yellow>Forzando il reload...
  force-shop-loader-reload-unloading-shops: <yellow>Sto scaricando <aqua>{0}</aqua> negozi caricati...
  force-shop-loader-reload-unloading-shops-from-memory: <yellow>Rimozione di <aqua>{0}</aqua> negozi dalla memoria...
  force-shop-loader-reload-reloading-shop-loader: <yellow>Re-calling shop-loader for reloading all shops from database...
  force-shop-loader-reload-complete: <green>Shop-loader has been reloaded all shops!
  toggle-shop-loaded-status: <aqua>Toggle this shop loaded status to <gold>{0}</gold>
  shop-internal-data: '<yellow>The internal data of this shop: <reset>{0}</reset>'
  handler-list-not-valid-bukkit-event-class: <red>The provided class <yellow>{0}</yellow> is not a valid Bukkit event class.
  update-player-shops-signs-no-username-given: <red>Devi fornire un nome utente valido.
  update-player-shops-signs-create-async-task: <yellow>Creating async tasks for force updating signs...
  update-player-shops-player-selected: '<yellow>Player selected: <gold>{0}</gold>'
  update-player-shops-player-shops: <yellow>In totale <gold>{0}</gold> negozi in attesa di aggiornamenti.
  update-player-shops-per-tick-threshold: '<yellow>Max shops can be update per tick: <gold>{0}</gold>'
  update-player-shops-complete: <green>Operazione completata. Impiegati <yellow>{0} ms</yellow> per aggiornare.
  update-player-shops-task-started: <gold>L'operazione è stata avviata, per favore attendi il suo completamento.
  item-info-store-as-string: "<green>Il negozio che stai cercando: <gold>{0}</gold> Hash: <white>{1}</white>"
  item-info-hand-as-string: "<green>L'oggetto che hai in mano: <gold>{0}</gold> Hash: <white>{1}</white>"
  item-matching-result: "<green>Hand2Store: <aqua>{0}</aqua>, Store2Hand: <aqua>{1}</aqua>"
  hikari-cp-size-tweak: "<green>HikariCP MaximumPoolSize and MinimumIdle was set to <white>{0}</white>"
  hikari-cp-testing: "<green>Per favore attendi, sto testando la connessione di HikariCP..."
  hikari-cp-working: "<green>Andata! HikariCP funziona alla grande!"
  hikari-cp-not-working: "<red>Test fallito! La connessione rientrata da HikariCP è morta! (Non ha passato il test in 1 secondo)"
  hikari-cp-timeout: "<red>HikariCP è scaduto mentre provava a prendere una connessione valida, per favore ripulisci tutte le query attive per liberare risorse di connessione."
  queries-stopped: "<green>Interrotte <white>{0}</white> le query attive."
  queries-dumping: "<yellow>Sto scaricando le query attive..."
  restart-database-manager: "<yellow>Restarting SQLManager..."
  restart-database-manager-clear-executors: "<yellow>Sto ripulendo gli esecutori..."
  restart-database-manager-unfinished-task: "<yellow>Operazione non finita: <white>{0}</white>"
  restart-database-manager-unfinished-task-history-query: "<yellow>Unfinished task (History Query): <white>{0}</white>"
  restart-database-manager-reconnect: "<yellow>Re-launching SQLManager via initial sequence (via async executor)"
  restart-database-manager-done: "<green>Done!"
  property-incorrect: "<yellow>You must enter a (and only one) property key=value set. E.g   aaa=bbb"
  property-security-block: "<red>Request was rejected, for security reason, you can only change the property that startsWith <aqua>com.ghostchu.quickshop</aqua> or <aqua>quickshop</aqua>."
  property-removed: "<green>Removed property key <white>{0}</white>"
  property-changed: "<green>Property key <white>{0}</white> was changed from <white>{1}</white> to <white>{2}</white>"
  marked-as-dirty: "<green>Marked all stores as dirty status, they will be force save in next auto-save task. (Restart the server to force execute store saving task)"
  display-removed: "<green>Successfully removed <yellow>{0}</yellow> QuickShop display items/entities from the worlds."
database:
  scanning: <green>Scanning the isolated data in QuickShop databases, the database load may increase in scan progress, this might need a while...
  scanning-async: <yellow>Scanning the isolated data in QuickShop databases on async task thread, the database load may increase in scan progress, this might need a while, try again later.
  already-scanning: <red>An scan task has been started, please wait until it finished.
  trim-warning: '<yellow><red><bold>Warning: </red>Backup your database before continue database trim to avoid the data loss. Once you get ready, execute <aqua>/qs database trim confirm</aqua> to continue.'
  status: '<yellow>Status: {0}'
  status-good: <green>Good
  status-bad: <yellow>Maintenance Required
  isolated: '<yellow>Isolated Data:'
  isolated-data-ids: '<yellow><aqua>└</aqua> Data Records: <gold>{0}</gold>'
  isolated-shop-ids: '<yellow><aqua>└</aqua> Shop Indexes: <gold>{0}</gold>'
  isolated-logs: '<yellow><aqua>└</aqua> Logs: <gold>{0}</gold>'
  isolated-external-caches: '<yellow><aqua>└</aqua> External Caches: <gold>{0}</gold>'
  last-purge-time: <yellow>Last time of trim at {0}
  report-time: <yellow>Last time of scan at {0}
  auto-scan-alert: <yellow>The QuickShop database on this server require a maintenance. Found <gold>{0}</gold> isolated data wait for trimming.
  auto-trim: <green>Auto trim has been enabled on this server, no manually trim required.
  trim-complete: <green>Database trim completed, <yellow>{0}</yellow> isolated trimmed.
  auto-trim-started: <green>Auto trim has been started, please wait...
  trim-start: <green>Database trim started, please wait...
  trim-exception: <red>Database trim failed, an exception raised during database trimming, check the server console.
  generated-at: '<yellow>Generated at: <gold>{0}</gold>'
  purge-date: <red>You must given a date.
  purge-warning: <yellow>This operation will purge the history that stored in your database including shop creates/changes/deletes, purchases, transactions and system logs. Delete those data can free the disk space but all history metric will lost, and other plugins that depend on metrics will stop working. To continue execute this operation, execute `/qs database purgelogs <before-days> confirm`
  purge-task-created: <green>Task created! Database are silent purging history records in background.
  purge-done-with-line: <green>Purge task completed, total <gold>{0}</gold> lines purged from database.
  purge-done-with-error: <red>Purge task failed, check the server console for details.
  purge-players-cache: <yellow>Please wait, purging players caches...
  purge-players-completed: |-
    <green>Successfully purged {0} players caches from both memory and database.<newline><aqua>Note: You server performance may impact by this operation.
  purge-players-error: <red>Failed to purge players caches, please check the server console.
  suggestion:
    trim: <yellow>This database require a isolated data trim, execute command <aqua>/qs database trim</aqua> to trim the database.
always-counting-removal-early-warning: <red>Always Counting feature is scheduled for removal, you shouldn't use it anymore and will stop work in future.
exporting-database: <green>Exporting database, please wait...
exporting-failed: <red>Failed to export database, please check the server console.
exported-database: <green>Database exported to <yellow>{0}</yellow>.
importing-not-found: <red>The <yellow>{0}</yellow> file not found, please check the file path.
importing-early-warning: |-
  <yellow><red><bold>Warning: </bold></red>The backup will be imported to the current database, any exists data will be purged and permanently lost if you don't have a backup.<newline><red>Are you sure to continue the import progress?</red><yellow>Type <aqua>/qs recovery confirm</aqua> to continue.
importing-database: <green>Importing database from backup, please wait...
importing-failed: <red>Failed to import database, please check the server console.
imported-database: <green>Database imported from <yellow>{0}</yellow>.
transfer-sent: <green>Shops transfer request sent to <yellow>{0}</yellow>.
transfer-request: <yellow>Player <aqua>{0}</aqua> requested to transfer their shops to you. Did you want accept the transfer?
transfer-single-request: <yellow>Player <aqua>{0}</aqua> would like to transfer a shop to you. Do you want to accept the request?
transfer-ask: |-
  <gold>Type <red>/qs transfer accept</red> to accept.</gold><newline><gold>Type <red>/qs transfer deny</red> to deny.</gold><newline><gold>Request will expire after <red>{0}</red> seconds.
transferall-ask: |-
  <gold>Type <red>/quickshop transferall accept</red> to accept or <red>/quickshop transferall deny</red> to deny.
  The request will expire after <red>{0}</red> seconds.
transfer-single-ask: |-
  <gold>Type <red>/quickshop transferownership accept</red> to accept or <red>/quickshop transferownership deny</red> to deny.
  The request will expire after <red>{0}</red> seconds.
transfer-accepted-fromside: <green>Player <aqua>{0}</aqua> accepted your shop transfer request.
transfer-accepted-toside: <green>You accepted <aqua>{0}</aqua>'s transfer request.
transfer-rejected-fromside: <red>Player <aqua>{0}</aqua> rejected your shop transfer request.
transfer-rejected-toside: <red>You rejected <aqua>{0}</aqua>'s shop transfer request.
transfer-no-pending-operation: <red>You don't have pending transfer request yet.
transfer-no-self: <red>You can't transfer your shops to yourself.
benefit-overflow: <red>The sum of all benefits cannot be greater than or equal to 100%.
benefit-exists: <red>Target player already in benefits list on this shop.
benefit-removed: <red>Target player has been removed from shop benefits.
benefit-added: <green>Player <aqua>{0}</aqua> has been added into shop benefits!
benefit-updated: <green>Player <aqua>{0}</aqua>'s benefits has been updated!
benefit-query: <green>This shop have <yellow>{0}</yellow> players in benefits list!
benefit-query-list: <yellow> - </yellow><white>Player <gold>{0}</gold>, Benefit <gold>{1}%</gold></white>
tag-added: '<green>Successfully added <aqua>#{0}</aqua> to this shop!'
tag-add-duplicate: '<red>The tag <aqua>#{0}</aqua> already exists at this shop!'
tag-removed: '<green>Successfully removed <aqua>#{0}</aqua> from this shop!'
tag-remove-not-exists: 'The tag <aqua>#{0}</aqua> not exists at this shop!'
tag-cleared: <green>Successfully cleared all tags from this shop!
tag-shops-cleared: '<green>Successfully cleared <aqua>#{0}</aqua> from all your tagged shops!'
tag-query: '<green>This shop have <yellow>{0}</yellow> tags:'
tag-query-listing: '<yellow> - <aqua>#{0}</aqua>'
tag-query-no-tag: <red>This shop have no tags.
tag-query-shops: '<green>This tag contains <yellow>{0}</yellow> shops:'
tag-query-shops-listing: <yellow> - <aqua>{0}</aqua>
batch-operations-based-on-tags-no-failure: <green>Successfully batch processed <yellow>{0}</yellow> shops.
batch-operations-based-on-tags-have-failure: <yellow>Total <yellow>{0}</yellow> shops in batch processing successfully completed, but <red>{1}</red> requests not completed.
batch-operations-based-on-tags-have-failure-with-reason: '<yellow>Total <yellow>{0}</yellow> shops in batch processing successfully completed, but <red>{1}</red> requests not completed. Reason: <gold>{2}</gold>'
batch-operations-based-on-tags-have-failure-error-messages-may-appears-above: <gray>The error messages will send to you above this message in the chat.
addon:
  towny:
    commands:
      town: <yellow>Set or unset a shop to a Town shop
      nation: <yellow>Set or unset a shop to a Nation shop
    make-shop-owned-by-town: <green>You have made the shop owned by town <yellow>{0}</yellow>.
    make-shop-no-longer-owned-by-town: <green>You have reset shop ownership, it now transfer back to original shop owner.
    make-shop-owned-by-nation: <green>You have made the shop owned by nation <yellow>{0}</yellow>.
    make-shop-no-longer-owned-by-nation: <green>You have reset shop ownership, it now transfer back to original shop owner.
    shop-owning-changing-notice: <grey>This shop now owned by a town/nation, original shop owner has been automatically added into administrator list, modify or add any new owner / staff by using command /qs permission
    target-shop-already-is-town-shop: <red>The target shop is already owned by a town.
    target-shop-already-is-nation-shop: <red>The target shop is already owned by a nation.
    target-shop-not-in-town-region: <red>The target shop is not in the town.
    target-shop-not-in-nation-region: <red>The target shop is not in the nation.
    item-not-allowed: <red>This shop's item are not allowed used by town/nation shop, pick another one!
    operation-disabled-due-shop-status: <red>This shop operation has been disabled due this already is a town/nation shop.
    plot-type-disallowed: <red>You can't create town/nation shop on this type of plot.
    flags:
      own: <red>You can only create shops at your owned shop type towny plot.
      modify: <red>You didn't have build permission on this towny plot.
      shop-type: <red>You must create shop on a shop type towny plot.
  residence:
    creation-flag-denied: <red>You don't have permission to create shops in this residence.
    trade-flag-denied: <red>You don't have permission to purchase in this residence.
    you-cannot-create-shop-in-wildness: <red>You can't create shop in wildness.
  griefprevention:
    creation-denied: <red>You don't have permission to create shops in this claim.
    trade-denied: <red>You don't have permission to purchase shops in this claim.
  lands:
    world-not-enabled: <red>You are not allowed to create or purchase in this world.
    creation-denied: <red>You don't have permission to create shops in this Land area.
  plotsquared:
    no-plot-whitelist-creation: <red>You cannot create shop outside of Plot.
    no-plot-whitelist-trade: <red>You cannot purchase shop outside of Plot.
    creation-denied: <red>You don't have permission to create shops in this Plot.
    trade-denied: <red>You don't have permission to purchase shops in this Plot.
    flag:
      create: Create QuickShop-Hikari shops
      trade: Purchase QuickShop-Hikari shops
  superiorskyblock:
    owner-create-only: <red>Only island owner can create shop there.
    owner-member-create-only: <red>Only island owner or member can create shop there.
  worldguard:
    creation-flag-test-failed: <red>You don't have permission create shop in this WorldGuard area.
    trade-flag-test-failed: <red>You don't have permission trade shop in this WorldGuard area.
    reached-per-region-amount-limit: "<red>You have reached the maximum amount of shops in this region."
  discord:
    __to_translators: 'To translators: `addon.discord.discord-messages` use a special variable in strings, %%LANG_KEY%% instead of QuickShop''s style {LANG_INDEX}, do not translate anything inside double ''%'' section. BUT for any other section texts, it respect the normal QuickShop Text System syntax ({0},{1} etc.) This may be difficult to understand because two different variable forms are used in the same program at the same time, but trust me, it is reasonable. (you don''t need translate this string, it''s a comment)'
    __to_users: Like the QuickShop Text System, Discord addon will also auto-detect user language and use user's language to send Discord message, it following the QuickShop-Hikari's language system settings.
    __to_message_designer: 'Design your Discord message with GUI: https://glitchii.github.io/embedbuilder/, then copy the JSON code and paste it into the translation and here we go!'
    discord-enabled: <aqua>Successfully <green>enabled</green> your QuickShop discord messages, now you can receive the shop messages from Discord.
    discord-disabled: <aqua>Successfully <red>disabled</red> your QuickShop discord messages, now you won't receive the shop messages from Discord anymore.
    discord-not-integrated: <red>You hadn't link your Discord yet! Please link your Discord account first!
    feature-enabled-for-user: <aqua>You have <green>enabled</green> <gold>{0}</gold> notification.
    feature-disabled-for-user: <aqua>You have <red>disabled</red> <gold>{0}</gold> notification.
    link-help: <yellow>This server using <gold>{0}</gold> for Discord driver, please use <green>{0}</green> to link your Discord account.
    save-notifaction-exception: <red>An error occurred while saving your Discord notifaction settings, please contact the server administrator.
    feature-status-changed: <green>Successfully set the notifaction <aqua>{0}</aqua> status to <gold>{1}</gold>
    commands:
      discord:
        description: <yellow>Managing your QuickShop Discord settings
    discord-messages:
      sold-to-your-shop: |
        {
         "embed":
           {
             "title": ":inbox_tray: Somebody sold items to your shop",
             "description": "The player %%purchase.name%% sold x%%purchase.amount%% %%shop.item.name%% to your shop.",
             "color": 52084,
             "author": {
               "name": "QuickShop-Hikari",
               "icon_url": ""
             },
             "thumbnail": {
               "url": ""
             },
             "image": {
               "url": ""
             },
             "footer": {
               "text": "QuickShop-Hikari Discord Notification",
               "icon_url": ""
             },
             "fields": [
               {
                 "name": "Shop",
                 "value": "%%shop.display-name%%",
                 "inline": false
               },
               {
                 "name": "Purchaser",
                 "value": "%%purchase.name%%",
                 "inline": true
               },
               {
                 "name": "Item",
                 "value": "%%shop.item.name%%",
                 "inline": true
               },
               {
                 "name": "Amount",
                 "value": "%%purchase.amount%% (x%%shop.stacking-amount%%)",
                 "inline": true
               },
               {
                 "name": "Your paid",
                 "value": "%%purchase.balance-formatted%%",
                 "inline": true
               },
               {
                 "name": "Taxes",
                 "value": "%%purchase.taxes-formatted%%",
                 "inline": true
               }
             ]
           }
        }
      bought-from-your-shop: |
        {
           "embed":
             {
               "title": ":outbox_tray: Somebody bought items from shop",
               "description": "The player %%purchase.name%% bought x%%purchase.amount%% %%shop.item.name%% from your shop.",
               "color": 52084,
               "author": {
                   "name": "QuickShop-Hikari",
                   "icon_url": ""
               },
               "thumbnail": {
                   "url": ""
               },
               "image": {
                   "url": ""
               },
               "footer": {
                   "text": "QuickShop-Hikari Discord Notification",
                   "icon_url": ""
               },
               "fields": [
                   {
                       "name": "Shop",
                       "value": "%%shop.display-name%%",
                       "inline": false
                   },
                   {
                       "name": "Purchaser",
                       "value": "%%purchase.name%%",
                       "inline": true
                   },
                   {
                       "name": "Item",
                       "value": "%%shop.item.name%%",
                       "inline": true
                   },
                   {
                       "name": "Amount",
                       "value": "%%purchase.amount%%(x%%shop.stacking-amount%%)",
                       "inline": true
                   },
                   {
                       "name": "Your earn",
                       "value": "%%purchase.balance-formatted%%",
                       "inline": true
                   },
                   {
                       "name": "Taxes",
                       "value": "%%purchase.taxes-formatted%%",
                       "inline": true
                   }
               ]
           }
         }
      mod-shop-purchase: |
        {
        "embed":
          {
              "title": ":dollar: Somebody purchased in a shop",
              "description": "The player %%purchase.name%% purchase x%%purchase.amount%% %%shop.item.name%% from the shop.",
              "color": 6061450,
              "author": {
                  "name": "QuickShop-Hikari",
                  "icon_url": ""
              },
              "thumbnail": {
                  "url": ""
              },
              "image": {
                  "url": ""
              },
              "footer": {
                  "text": "QuickShop-Hikari Discord Notification",
                  "icon_url": ""
              },
              "fields": [
                  {
                      "name": "Shop",
                      "value": "%%shop.display-name%%",
                      "inline": false
                  },
                  {
                      "name": "Purchaser",
                      "value": "%%purchase.name%%",
                      "inline": true
                  },
                  {
                      "name": "Item",
                      "value": "%%shop.item.name%%",
                      "inline": true
                  },
                  {
                      "name": "Amount",
                      "value": "%%purchase.amount%% (x%%shop.stacking-amount%%)",
                      "inline": true
                  },
                  {
                      "name": "Balance",
                      "value": "%%purchase.balance-formatted%%",
                      "inline": true
                  },
                  {
                      "name": "Taxes",
                      "value": "%%purchase.taxes-formatted%%",
                      "inline": true
                  }
              ]
          }
        }
      out-of-space: |
        {
        "embed":
          {
              "title": ":mailbox_with_mail: Your shop has run out of space",
              "description": "Your shop inventory is full of items!\nYou need empty your shop container to free the space then you can continue to accept new items.",
              "color": 16065893,
              "author": {
                  "name": "QuickShop-Hikari",
                  "icon_url": ""
              },
              "thumbnail": {
                  "url": ""
              },
              "image": {
                  "url": ""
              },
              "footer": {
                  "text": "QuickShop-Hikari Discord Notification",
                  "icon_url": ""
              },
              "fields": [
                  {
                      "name": "Shop",
                      "value": "%%shop.display-name%%",
                      "inline": false
                  }
              ]
          }
        }
      out-of-stock: |
        {
            "embed":
                {
                    "title": ":mailbox_with_no_mail: Your shop has run out of stock",
                    "description": "Your shop inventory is empty now!\nYou need to refill the shop container with items to continue selling items!",
                    "color": 16065893,
                    "author": {
                        "name": "QuickShop-Hikari",
                        "icon_url": ""
                    },
                    "thumbnail": {
                        "url": ""
                    },
                    "image": {
                        "url": ""
                    },
                    "footer": {
                        "text": "QuickShop-Hikari Discord Notification",
                        "icon_url": ""
                    },
                    "fields": [
                        {
                            "name": "Shop",
                            "value": "%%shop.display-name%%",
                            "inline": false
                        }
                    ]
                }
        }
      mod-shop-created: |
        {
          "embed": {
            "title": ":classical_building: A new shop has been created",
            "description": "Player created a new shop on your server!",
            "color": 6061450,
            "author": {
              "name": "QuickShop-Hikari",
              "icon_url": ""
            },
            "thumbnail": {
              "url": ""
            },
            "image": {
              "url": ""
            },
            "footer": {
              "text": "QuickShop-Hikari Discord Notification",
              "icon_url": ""
            },
            "fields": [
              {
                "name": "Shop",
                "value": "%%shop.display-name%%",
                "inline": false
              },
              {
                "name": "Owner",
                "value": "%%shop.owner.name%%",
                "inline": true
              },
              {
                "name": "Item",
                "value": "%%shop.item.name%%",
                "inline": true
              },
              {
                "name": "Amount",
                "value": "%%shop.stacking-amount%%",
                "inline": true
              },
              {
                "name": "Type",
                "value": "%%shop.type%%",
                "inline": true
              }
            ]
          }
        }
      mod-remove-shop: |
        {
            "embed": {
                "title": ":recycle: A shop was removed from this server",
                "description": "A shop was removed from this server.",
                "color": 6061450,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "Reason",
                        "value": "%%delete.reason%%",
                        "inline": false
                    }
                ]
            }
        }
      shop-transfer-to-you: |
        {
            "embed": {
                "title": ":placard: A shop was transfer to you",
                "description": "A shop transfer to you from another player.",
                "color": 16172079,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "From",
                        "value": "%%transfer.from%%",
                        "inline": false
                    }
                ]
            }
        }
      mod-shop-transfer: |
        {
            "embed": {
                "title": ":placard: A shop was transferred",
                "description": "A shop transfer to a player from another player.",
                "color": 6061450,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "From",
                        "value": "%%transfer.from%%",
                        "inline": true
                    },
                    {
                        "name": "To",
                        "value": "%%transfer.to%%",
                        "inline": true
                    }
                ]
            }
        }
      shop-price-changed: |
        {
            "embed": {
                "title": ":money_with_wings: Your shop price was changed",
                "description": "You or your shop staff changed your shop price.",
                "color": 16172079,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "From",
                        "value": "%%change-price.from%%",
                        "inline": true
                    },
                    {
                        "name": "To",
                        "value": "%%change-price.to%%",
                        "inline": true
                    }
                ]
            }
        }
      mod-shop-price-changed: |
        {
            "embed": {
                "title": ":money_with_wings: A shop price has been changed",
                "description": "A shop changed its price settings.",
                "color": 6061450,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": true
                    },
                    {
                        "name": "Owner",
                        "value": "%%shop.owner.name%%",
                        "inline": true
                    },
                    {
                        "name": "",
                        "value": "",
                        "inline": true
                    },
                    {
                        "name": "From",
                        "value": "%%change-price.from%%",
                        "inline": true
                    },
                    {
                        "name": "To",
                        "value": "%%change-price.to%%",
                        "inline": true
                    }
                ]
            }
        }
      shop-permission-changed: |
        {
            "embed": {
                "title": ":closed_lock_with_key: Your shop permission settings has been changed",
                "description": "One of your shop's permission setting has been changed.",
                "color": 15879747,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "Player",
                        "value": "%%change-permission.player%%",
                        "inline": true
                    },
                    {
                        "name": "Assign to group",
                        "value": "%%change-permission.to-group%%",
                        "inline": true
                    },
                    {
                        "name": "",
                        "value": "",
                        "inline": true
                    },
                    {
                        "name": "Permission granted (Inherit from group)",
                        "value": "```\n%%change-permission.perms-list%%\n```",
                        "inline": false
                    }
                ]
            }
        }
    field:
      player: Player
      item: Item
      amount: Amount
      balance: Balance
      balance-after-tax: Balance (after tax)
      account: Your account balance
      taxes: Taxes
      cost: Cost
  discount:
    commands:
      discount:
        description: <yellow>Apply a discount code or manage your discount codes.
    tab-complete:
      discount:
        general:
          code: <code>
        create:
          rate: |
            Command Hint:
            Argument: <rate>
            Description: The actual percentage or money you will earn
            Input `30%` = price * 0.3
            Input `50` = price - 50
          max-usage: |
            Command Hint:
            Argument: [max-usage]
            Description: The times that code can be used
            `-1` for unlimited.
          threshold: |
            Command Hint:
            Argument: [threshold]
            Description: The minimum price that code can be applied
            `-1` for unlimited
          expired: |
            Command Hint
            Argument: [expired]
            Description: The code expired time.
            `-1` for unlimited duration.
            Accept both Zulu time and UNIX timestamp in seconds.
            Zulu Example: 2022-12-17T10:31:37Z
            UNIX Example: 1671273097
    discount-code-already-exists: <red>Sorry, the name of your discount code already in use.
    invalid-discount-code-regex: '<red>The discount code must matches the regex: <yellow>{0}</yellow>'
    invalid-discount-code: <red>This discount code are invalid.
    discount-code-added: <green>Your discount code <yellow>{0}</yellow> has been added into shop <aqua>{1}</aqua>
    discount-code-removed: <green>Your discount code <yellow>{0}</yellow> has been removed from shop <aqua>{1}</aqua>
    invalid-code-type: <red>The code type <yellow>{0}</yellow> are invalid.
    invalid-usage-restriction: <red>The usage restriction <yellow>{0}</yellow> are invalid.
    invalid-threshold-restriction: <red>The threshold restriction <yellow>{0}</yellow> are invalid.
    invalid-effect-scope: <red>The scope <yellow>{0}</yellow> are invalid.
    invalid-expire-time: <red>Non è possibile specificare un'ora nel passato.
    invalid-discount-rate: <red>The discount rate <yellow>{0}</yellow> are invalid, it can be a fixed number or percentage.
    discount-code-expired: <red>Ouch! Your discount code <yellow>{0}</yellow> has been expired!
    discount-code-installed: <green>You have installed discount code <gold>{0}</gold>, the discount code will be automatically applied to the available purchase in all the following purchases during this session. To uninstall discount code, execute <aqua>/qs discount uninstall {0}</aqua>.
    discount-code-uninstalled: <green>You have uninstalled your discount code.
    discount-code-query-nothing: <red>You hadn't install a discount code yet!
    discount-code-query: '<green>You are using discount code: <yellow>{0}</yellow>.'
    discount-code-applicable: '<color:#bcef26>Your discount code <yellow><bold>{0}</bold></yellow> are <bold>applicable</bold> in this shop!'
    discount-code-applicable-with-threshold: '<color:#bcef26>Your discount code <yellow><bold>{0}</bold></yellow> are <bold>applicable</bold> in this shop, but only your purchase cost over <yellow>{1}</yellow> in single transaction.'
    discount-code-not-applicable: <red>Your discount code <yellow><bold>{0}</bold></yellow> are <bold>not applicable</bold> in this shop!
    discount-code-reach-the-limit: <red>You have reach the usage limit of discount code <yellow><bold>{0}</bold></yellow>, discount are not applied.
    discount-code-no-permission: <red>You didn't have permission to use any discount code in this shop!
    discount-code-has-been-expired: <red>Your discount has been expired!
    discount-code-config-shop-added: <green>Successfully to add this shop into your discount code allow list.
    discount-code-config-shop-add-failure: <red>This shop already in your discount code allow list.
    discount-code-config-shop-removed: <green>Successfully to remove this shop from your discount code allow list.
    discount-code-config-shop-remove-failure: <red>This shop not exists in your discount code allow list.
    discount-code-config-expire: <green>Successfully changed your code expire time.
    discount-code-config-applied: <green>Config the discount code successfully!
    discount-code-created-successfully: |
      <green>Your discount code <yellow>{0}</yellow> has been successfully created!
      <gold>Scope: <aqua>{1}</aqua></gold>.
      <gold>Share with others: <color:#bcef26>{2}</color:#bcef26></gold>.
      <yellow>For <gray>Specific Shops</gray> scope only: To add shops to your discount code allow list, please looking an quickshop and execute <aqua>{3}</aqua> command.
      <yellow>You can use <aqua>/qs discount config {0}</aqua> to edit your discount code anytime.
    discount-code-under-threshold: <red>The discount not applied to your purchase because the total value under the discount code threshold <yellow>{0}</yellow>.
    percentage-off: '<bold><color:#bcef26>-{0}%</color:#bcef26></bold>'
    fixed-off: '<bold><color:#bcef26>-{0}</color:#bcef26></bold>'
    discount-code-list: '<gold>Listing your discount codes:</gold>'
    discount-code-applied-in-purchase: '<color:#bcef26>Your discount code <yellow>{0}</yellow> has been applied to this purchase and you saved <gold>{1}</gold>!'
    scope:
      this-shop: '{0}'
      your-shops-owned: all your shops (owned)
      your-shops-managed: all your shops (managed)
      server: whole server
    code-type:
      SERVER_ALL_SHOPS: All shops on this server
      PLAYER_ALL_SHOPS: All shops that created by code owner
      SPECIFIC_SHOPS: Specific shops
    discount-code-details: |-
      <gold>Code: <yellow>{0}</yellow>
      <gold>Creator: <yellow>{1}</yellow>
      <gold>Applied to: <yellow>{2}</yellow>
      <gold>Remaining usage: <yellow>{3}</yellow>
      <gold>Expired on: <yellow>{4}</yellow>
      <gold>Threshold: <yellow>{5}</yellow>
      <gold>Discount: <yellow>{6}</yellow>
  list:
    commands:
      list: <yellow>Listing all shops owned by your or specific player
    table-prefix: '<yellow><green>{0}</green>''s shops <gray>(Total {1})</gray>: '
    table-prefix-pageable: '<yellow><green>{0}</green>''s shops <gray>(Page {1}/{2})</gray>: '
    entry: "<hover:show_text:'<yellow>{1}</yellow>\n<gray>{2} {3},{4},{5}</gray>\n<aqua>Price <yellow>{6}</yellow> - <light_purple>x{7}</light_purple> <yellow>{8}</yellow>\n<green>{9}'><yellow>{0}. <aqua>{1}</aqua></hover>"
  shopitemonly:
    message: <red>You can't put non-shop items into shop container, all non-shop items will dropped at your location.
  limited:
    commands:
      limit: <yellow>Set a limit that restrict the purchase that player can make in period
    titles:
      title: <green>Purchase Success
      subtitle: <aqua>You can also purchase <gold>{0}</gold> more in this shop
    reach-the-limit: <red>You have reached the limit in this shop. You can also purchase <green>{0}</green> items, but you trying purchase <yellow>{1}</yellow> items.
    success-reset: <green>Reset the restriction of this shop successfully
    success-remove: <green>Successfully remove the all restriction of this shop
    success-setup: <green>Restriction settings for this shop were saved successfully
    trade-limit-reached-cancel-reason: <red>Reached the restriction of this shop
    remains-limits: '<gold>Your remaining purchase quota in this shop: <yellow>{0}</yellow>'
    remains-limits-before-reset-period-note: '<gold>This shop will reset their purchase quota every: <yellow>{0}</yellow>'
  dynmap:
    markerset-title: QuickShop-Hikari Shops
    marker-name: '{0} @ {2} @ {3} for x{4} @ {5}'
    marker-description: |
      Name: {0}
      Owner: {1}
      Item: {2}
      Price: {3} for x{4} item(s)
      Type: {5}
      Unlimited: {6}
      Location: {7}
  bluemap:
    markerset-title: QuickShop-Hikari Shops
    marker-name: '{0} @ {2} @ {3} for x{4} @ {5}'
    marker-description: |
      Name: {0}
      Owner: {1}
      Item: {2}
      Price: {3} for x{4} item(s)
      Type: {5}
      Unlimited: {6}
      Location: {7}
  chestprotect:
    protection-exists: <red>This region already protected by ChestProtect and you don't have permission to create shops at here.
    shops-exsts: <red>The region that you want to protect contains other player's shops and you don't have permission to access them.
  displaycontrol:
    toggle: |-
      <green>Successfully toggle your QuickShop display showcasing to <aqua>{0}</aqua>.
      <yellow>You may need re-join to take effect.
    toggle-exception: <red>Failed to toggle your display showcase settings due an internal error, please contact server administrator.
    command:
      displaycontrol: <yellow>Toggle your QuickShop display showcasing
  reremake-migrator:
    commands:
      migratefromreremake: Migrate QuickShop-Reremake's data to QuickShop-Hikari
    server-not-empty: "<red>No players are allowed to be online on the server during the conversion progress, please enable server whitelist or maintenance mode."
    starting-convert-progress: "<gold>Starting the convert progress, <red>DO NOT SHUTDOWN YOUR SERVER!</red>"
    executing: "<gold>Executing migrate component <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
    completed: "<green>Done! Migrate completed, please restart your server and remove QuickShop-Reremake from plugins directory."
    join_blocking_converting: "<red>[QuickShop-Hikari Migrator] This server is performing a data conversion and you cannot join the server at this time, try again later!"
    join_blocking_finished: "<red>[QuickShop-Hikari Migrator] This server has just completed a data conversion and is waiting for a reboot to apply the changes, try again later!"
    failed: "<red>Migration progress exited with error, please check the server console."
    modules:
      config:
        copy-values: "<yellow>Copying values (total {0} entries)..."
        copying-value: "<gray> - Copying <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        migrate-price-restriction: "<yellow>Migrating price-restriction related settings..."
        migrate-price-restriction-entry: "<gray> - Migrating <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
      shop:
        start-migrate: "<yellow>Migrating shops (total {0} entries)..."
        migrate-entry: "<gray> - Migrating <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        unloading-reremake: "<gold>Unloading Reremake to avoid data overwrite..."
        register-entry: "<gray> - Registering <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        save-entry: "<gray> - Saving <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        saving-shops: "<yellow>Saving <gold>{0}</gold> shops, this may need a while™ (the time based on shops amounts)..."
        conflict: "<gray> - CONFLICT > Detects a conflict between a Reremake store and an existing Hikari store location, taking a predefined behavior: <dark_gray>{0}</dark_gray>"
      translation:
        start-migrate: "<yellow>Migrating translation files...</yellow>"
        copy-values: "<yellow>Copying values in locale <gold>{0}</gold> (total {1} entries)..."
        migrate-entry: "<gray> - Migrating <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        copying-value: "<gray> - Copying <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
      shop-logs:
        start-migrate: "<yellow>Migrating shop logs (details in console), please wait...</yellow>"
        extract-history-files: "<gray>Please wait for us to unzip and append the history logs..."
        filter-history-files: "<gray>Please wait for us to filter the history log..."
        filtered-history-files: "<gray>Filtered {0} lines out of queue."
        import-entry: "<gray> - Migrating <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
compat:
  advancedchests:
    created: <green>You're successfully created a shop on AdvancedChests container!
    permission-denied: <red>Sorry! You don't have the permission to create a shop on a AdvancedChests container!
suite:
  dynmap:
    title: '{0} - {1} - {2}'
    description: |-
      Item: {0}
      Owner: {1}
      Type: {2} {3}
      Price: {4}
      Location: {5} {6}, {7}, {8}
      Space: {9}
      Stock: {10}
  limited:
    command-description: <yellow>Set a limit that restrict the purchase that player can make in period.
    reach-the-quota-limit: <red>You have reach the purchase limit in this shop ({0}/{1}).
    quota-reset-countdown: <yellow>The quota in this shop will reset in {0}.
    quota-reset-player-successfully: <green>The quota for player {0} in this shop has been reset successfully.
    quota-reset-everybody-successfully: <green>The quota for everyone in this shop has been reset successfully.
    quota-setup: <green>The purchase restriction now apply to this shop!
    quota-remove: <green>The purchase restriction now removed from this shop!
    subtitles:
      title: <green>Purchase Success
      subtitle: <aqua>You can purchase <yellow>{0}</yellow> more in this shop
  list:
    command-description: <yellow>List all shops owned by yourself or another player.
    table-prefix: <yellow>You owned total <aqua>{0}</aqua> shops on this server.
    entry-syntax: <aqua>- <yellow>{0}
    entry-syntax-fallback-name: '<yellow>Item:{0} X:{1}, Y:{2}, Z:{3}, World: {4}'
    hover-text: |-
      <yellow>{0}
      <gray>{1} {2}, {3}, {4}
      <aqua>{5} - <yellow>{6}
      <yellow>{7} items each stack.
  shopitemonly:
    message: <red>You can't put non-shop items into shop container, all non-shop items will dropped at your location.
compatibility:
  elitemobs:
    soulbound-disallowed: You can't trade item which have EliteMobs Soulbound enchantment.
internet-paste-forbidden-privacy-reason: "<red>Failed! According your privacy setting, QuickShop-Hikari are not allowed upload your paste to Internet, turn on DIAGNOSTIC permission in privacy setting in config.yml or use <aqua>/quickshop paste --file</aqua> instead."
no-sign-type-given: "<red>You need give a sign material, the sign materials that available on this server: {0}"
sign-type-invalid: "<red>The type <yellow>{0}</yellow> not a valid sign material."
delete-controlpanel-button-confirm: "<red>Vuoi davvero rimuovere questo negozio? Clicca di nuovo il pulsante <bold>[Rimuovi negozio]</bold> entro {0} secondi per confermare."
cannot-suggest-price: "<red>Sorry, For the time being, no more people are trading the same item as you and there is not enough data to generate a suggested price."
price-suggest: "<green>Based on data from <aqua>{0}</aqua> stores, the highest priced store is priced at <light_purple>{1}</light_purple>, the lowest priced store is priced at <light_purple>{2}</light_purple>, the average price is <light_purple>{3}</light_purple>, and the median price is <light_purple>{4}</light_purple>. <newline><yellow>It's recommended that you set your price around <gold>{5}</gold>.</yellow>"
suggest-wait: "<green>Please wait... Calculating recommend price."
history:
  shop:
    gui-title: "View purchase history"
    header-icon-multiple-shop: "<white>The query result across {0} shops</white>"
    header-icon-description:
      - "<white>Type: <yellow>{0}</yellow></white>"
      - "<white>Owner: <yellow>{1}</yellow></white>"
      - "<white>Item: <yellow>{2}</yellow></white>"
      - "<white>Price: <yellow>{3} <aqua>x{4}</aqua></yellow></white>"
      - "<white>Location: <yellow>{5}</yellow></white>"
    log-icon-title: "<reset><green>Time: {0}</green>"
    log-icon-description:
      - "<white>Purchaser: <yellow>{0}</yellow></white>"
      - "<white>Item: <yellow>{1} <aqua>x{2}</aqua></yellow></white>"
      - "<white>Balance: <yellow>{3}</yellow></white>"
      - "<white>Tax: <yellow>{4}</yellow></white>"
    log-icon-description-with-store-name:
      - "<white>Store: <yellow>{0}</yellow></white>"
      - "<white>Purchaser: <yellow>{1}</yellow></white>"
      - "<white>Item: <yellow>{2} <aqua>x{3}</aqua></yellow></white>"
      - "<white>Balance: <yellow>{4}</yellow></white>"
      - "<white>Tax: <yellow>{5}</yellow></white>"
    query-icon: "<gray>Please wait, querying...</gray>"
    previous-page: "<white><< Previous Page</white>"
    next-page: "<white>Next Page >></white>"
    current-page: "<white>Page {0}</white>"
    summary-icon-title: "<green>Shop Summary"
    recent-purchases: "<white>Recent <aqua>{0}</aqua> purchases: <yellow>{1}</yellow></white>"
    recent-purchase-balance: "<white>Recent <aqua>{0}</aqua> turnover: <yellow>{1}</yellow></white>"
    total-purchases: "<white>Total purchases: <yellow>{0}</yellow></white>"
    total-balances: "<white>Total turnover: <yellow>{0}</yellow></white>"
    total-unique-purchasers: "<white>Total unique purchasers: <yellow>{0}</yellow></white>"
    top-n-valuable-customers-title: "<gold>Top {0} valuable customers</gold>"
    top-n-valuable-customers-entry: "<white>- <yellow>{0}</yellow> <aqua>{1}</aqua></white>"
    no-result: "<white>No result</white>"
about:
  text:
    - "<b><aqua>QuickShop <yellow>{0}</yellow></aqua></b>"
    - "<aqua>Version <yellow>>></yellow> <green>{1}</green></aqua>"
    - "<aqua>Release <yellow>>></yellow> <green>{2}</green></aqua>"
    - "<aqua>Developers <yellow>>></yellow> <color:#218bff><hover:show_text:'<yellow>Click to check contributors'><click:open_url:'https://github.com/Ghost-chu/QuickShop-Hikari/graphs/contributors'>[View contributors on GitHub]</click></hover></color></aqua>"
    - "<aqua>Localized members <gold>({4})</gold>: <yellow>>></yellow> <green>{5}</green></aqua>"
    - "  <yellow>+ <color:#43A047><hover:show_text:'<yellow>Click to open Crowdin translate page'><click:open_url:'https://crowdin.com/project/quickshop-hikari'>[Help translate on Crowdin]</click></hover></color></yellow>"
    - "<aqua>Donation Key <yellow>>></yellow> <green>{6}</green></aqua>"
    - "<gold>Powered by community</gold> <red>Made with ❤</red>"
  valid-donation-key: "<color:#00AFF1>Bound to <color:#F9C23C>{0}</color></color>"
  invalid-donation-key: "<gray>Invalid donation key</gra>"
  kofi-thanks: "<gold>Special thanks to those who support QuickShop on Ko-fi :)</gold>"
history-command-leave-blank: "<Leave blank for seeing store>"
shop-information-not-shown-due-an-internal-error: "<red>An internal error occurred. The store information panel may be displayed incompletely, please contact the server administrator."
