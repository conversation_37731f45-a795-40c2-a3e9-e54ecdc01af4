break-shop-use-supertool: <yellow><PERSON><PERSON><PERSON><PERSON> rozbić sklep za pomocą SuperTool.
fee-charged-for-price-change: <green>Zapła<PERSON>ł<PERSON>ś <red>{0}<green>, aby zmie<PERSON>ć cenę.
not-allowed-to-create: <red>Tu nie zrobisz sklepu.
disabled-in-this-world: <red>QuickShop jest wyłączony w tym świecie
how-much-to-trade-for: <green>Wpisz na czacie cenę za <yellow>{1}x {0}<green>.
client-language-changed: <green>QuickShop wykrył, że ustawienia języka klienta zostały zmienione, teraz uż<PERSON>wamy dla Ciebie wersji językowej {0}.
shops-backingup: Tworzenie kopii zapasowej sklepu z bazy danych...
_comment: Witaj tłumaczu! Jeś<PERSON> edytujesz to z Github'a lub ze źró<PERSON>ł kodowych, pow<PERSON><PERSON><PERSON> prz<PERSON> do https://crowdin.com/project/quickshop-hikari
unlimited-shop-owner-changed: <yellow>Ten właściciel sklepu nielimitowanego został zmieniony na {0}.
bad-command-usage-detailed: '<red>Błędne argumenty poleceń! Akceptuje następujące parametry: <gray>{0}'
thats-not-a-number: <red>Nieprawidłowa liczba
shop-name-disallowed: <red>The shop name <yellow>{0} <red>is disallowed. Pick another one!
console-only-danger: <red>Jest to niebezpieczne polecenie, więc tylko konsola może go wykonać.
not-a-number: Możesz wpisać tylko liczbę, ty wpisałeś {0}.
not-looking-at-valid-shop-block: <red>Nie można znaleźć bloku do tworzenia sklepu. Musisz patrzeć się na jeden z nich.
shop-removed-cause-ongoing-fee: <red>Twój sklep w {0} został usunięty, ponieważ nie masz wystarczającej ilości pieniędzy, aby go utrzymać!
tabcomplete:
  amount: '[ilość]'
  item: '[item]'
  price: '[cena]'
  name: '[name]'
  range: '[zakres]'
  currency: '[nazwa waluty]'
  percentage: '[procent%]'
taxaccount-unset: <green>Konto podatkowe tego sklepu jest teraz zgodne z ustawieniami globalnymi serwera.
blacklisted-item: <red>Nie możesz sprzedać tego przedmiotu, ponieważ znajduje się na czarnej liście
command-type-mismatch: <red>Ta komenda może być wykonana tylko przez <aqua>{0}.
server-crash-warning: '<red>Serwer może się zawiesić po wykonaniu polecenia /qs reload po zastąpieniu/usunięciu pliku Jar pluginu QuickShop podczas uruchamiania serwera.'
you-cant-afford-to-change-price: <red>Musisz mieć {0} aby zmienić cenę w sklepie.
safe-mode: <red>QuickShop jest teraz w trybie bezpiecznym, nie możesz otworzyć pojemnika tego sklepu, skontaktuj się z administratorem serwera, aby naprawić błędy.
forbidden-vanilla-behavior: <red>Operacja jest zabroniona, ponieważ nie jest zgodna z zachowaniem waniliowym
shop-out-of-space-name: <dark_purple>Twój sklep {0} jest pełny!
paste-disabled: |-
  <red>Paste functionality has been disabled! You cannot request technical support. <newline>Reason: {0}
quick-fill:
  entry-own: ' <yellow>- <red>[own] <aqua>{0} <light_purple>{1}'
  hover:
    - '<yellow>Nazwa: <aqua>{0}'
    - '<yellow>Właściciel: <aqua>{0}'
    - '<yellow>Typ: <aqua>{0}'
    - '<yellow>Cena: <aqua>{0}'
    - '<yellow>Przedmiot: <aqua>{0}'
    - '<yellow>Lokalizacja: <aqua>{0}'
  hover-arg-filled:
    - '<yellow>Nazwa: <aqua>{name}'
    - '<yellow>Właściciel: <aqua>{owner}'
    - '<yellow>Typ: <aqua>{type}'
    - '<yellow>Cena: <aqua>{price}'
    - '<yellow>Przedmiot: <aqua>{item}'
    - '<yellow>Lokalizacja: <aqua>{location}'
  header: '<yellow>You have multiple shops with name "<green>{0}<yellow>", choose one to continue:'
  entry-normal: ' <yellow>- <aqua>{0} <light_purple>{1}'
  entry-cooperation: ' <yellow>- <red>[co] <aqua>{0} <light_purple>{1}'
3rd-plugin-build-check-failed-admin: '<gray>(AdminOnly) <light_purple>{0}<dark_gray> odmówił sprawdzenia uprawnień. Jeśli to nie możliwe, spróbuj dodać <light_purple>{1} <gray>do czarnej listy nasłuchujących. Przewodnik konfiguracyjny:https://ghost-chu.github.io/QuickShop-Hikari-Documents/docs/modules/shops/protection-checker'
average-price-nearby: '<green>Cena w pobliżu: <yellow>{0}'
inventory-check-global-alert: "<red>[Inventory Check] <gray>Warning! Found a QuickShop display item <gold>{2}</gold> in inventory at <aqua>{0}</aqua> <dark_gray>[{1}]</dark_gray>, which it shouldn't happened, This usually means that someone is maliciously exploiting the exploit to duplicating the display item."
digits-reach-the-limit: <red>Osiągnąłeś limit miejsc po przecinku w cenie.
currency-unset: <green>Waluta sklepu usunięta pomyślnie. Używasz teraz ustawień domyślnych.
you-cant-create-shop-in-there: <red>Nie masz uprawnień do utworzenia sklepu w tej lokalizacji.
no-pending-action: <red>Nie masz żadnych oczekujących akcji
refill-success: <green>Udało się uzupełnić sklep
failed-to-paste: <red>Nie udało się przesłać danych do Pastebina. Sprawdź swoje połączenie internetowe i spróbuj ponownie. (Szczegóły znajdują się w konsoli)
shop-out-of-stock-name: <dark_purple>W twoim sklepie {0} zabrakło {1}!
shop-name-invalid: <red>The shop name <yellow>{0} <red>is invalid. Pick another one!
how-many-buy-stack: <green>Wpisz, ile stacków chcesz <aqua>KUPIĆ<green> na czacie. W każdym stacku jest <yellow>{0}<green> przedmiotów. Możesz kupić <yellow>{1}<green> stacków. Wpisz <aqua>{2}<green> by zakupić wszystkie.
exceeded-maximum: <red>Wartość przekroczyła maksymalną wartość w Java.
unlimited-shop-owner-keeped: '<yellow>Uwaga: Właściciel sklepu nadal jest właścicielem nielimitowanego sklepu, musisz ustawić nowego właściciela sklepu samodzielnie.'
no-enough-money-to-keep-shops: <red>Nie masz wystarczająco pieniędzy, aby przechowywać sklepy! Wszystkie sklepy zostały usunięte...
3rd-plugin-build-check-failed: <red>Wtyczka firm trzecich <bold>{0}<reset><red> odmówiła sprawdzenia uprawnień, czy masz tam skonfigurowane uprawnienia?
not-a-integer: <red>Musisz wprowadzić całkowitą liczbę, ty wpisałeś {0}.
translation-country: 'Język: Polski (pl_PL)'
buying-more-than-selling: '<red>OSTRZEŻENIE: Kupujesz przedmioty za więcej niż je sprzedajesz!'
purchase-failed: '<red>Zakup nie powiódł się: Błąd wewnętrzny. Skontaktuj się z administratorem serwera.'
denied-put-in-item: <red>Nie możesz umieścić tego przedmiotu w swoim sklepie!
shop-has-changed: <red>Sklep, z którego próbowałeś skorzystać, zmienił się od momentu kliknięcia!
flush-finished: <green>Pomyślnie wyczyszczono wiadomości.
no-price-given: <red>Podaj poprawną cenę.
shop-already-owned: <red>To już jest sklep.
backup-success: <green>Kopia zapasowa powiodła się.
not-looking-at-shop: <red>Nie można znaleźć QuickShop. Musisz na niego patrzeć.
you-cant-afford-a-new-shop: <red>Musisz mieć {0} aby zrobić nowy sklep.
success-created-shop: <red>Sklep utworzony.
shop-creation-cancelled: <red>Tworzenie sklepu anulowane.
shop-owner-self-trade: <yellow>Handlujesz z własnym sklepem, więc nie będziesz mógł uzyskać żadnych korzyści.
purchase-out-of-space: <red>W tym sklepie zabrakło miejsca, skontaktuj się z właścicielem sklepu lub personelem, aby opróżnić sklep.
reloading-status:
  success: <green>Przeładowanie zakończone bez żadnych błędów.
  scheduled: <green>Przeładowanie zakończone. <gray>(Niektóre zmiany wymagały chwili, aby zadziałać)
  require-restart: <green>Przeładowanie zakończone. <yellow>(Wszystkie zmiany wymagają restartu serwera)
  failed: <red>Przeładowywanie nie powiodło się, sprawdź konsolę serwera
player-bought-from-your-store-tax: <green>{0} Kupił {1} {2} z Twojego sklepu i zarobiłeś {3} ({4} w podatkach).
not-enough-space: <red>Masz tylko miejsce na {0} więcej!
shop-name-success: <green>Pomyślnie ustawiono nazwę sklepu na <yellow>{0}<green>.
shop-staff-added: <green>Pomyślnie dodano {0} jako członka personelu twojego sklepu.
shop-staff-empty: <yellow>This shop have no staff members.
shops-recovering: Odzyskiwanie sklepów z kopii zapasowej...
virtual-player-component-hover: "<gray>This is a virtual player.\n<gray>Refers to a system account of this name with the same name.</gray>\n<green>UUID: <yellow>{0}</yellow></green>\n<green>Username: <yellow>{1}</yellow></green>\n<green>Display as: <yellow>{2}</yellow></green>"
real-player-component-hover: "<gray>This is a real exists player.\n<green>UUID: <yellow>{0}</yellow></green>\n<green>Username: <yellow>{1}</yellow></green>\n<green>Display as: <yellow>{2}</yellow></green>\n<gray>If you wish to use a virtual system account with the same name, add <dark_gray>\"[]\"</dark_gray> to the both side of the username: <dark_gray>[{1}]</dark_gray>."
menu:
  sell-tax: <green>Zapłaciłeś <yellow>{0} <green>w podatkach.
  owner: '<green>Właściciel: {0}'
  preview: <aqua>[Podgląd przedmiotu]
  enchants: <dark_purple>Enchanty
  sell-tax-self: <green>Nie płaciłeś podatków, ponieważ posiadasz ten sklep.
  shop-information: '<green>Info:'
  item: '<green>Przedmiot: <yellow>{0}'
  price-per: <green>Cena za <yellow>{0} <green>- <yellow>{1}
  item-name-and-price: <yellow>{0} {1} <green>za <yellow>{2}
  item-name-and-price-tax: <yellow>{0} {1} <green>za</green> {2} <gray>(<green>{3}</green> w podatkach)</gray>
  successful-purchase: '<green>Zakupiono:'
  price-per-stack: <green>Cena za <yellow>{2}x {0} - {1}
  stored-enchants: <dark_purple>Przechowywane Enchanty
  item-holochat-error: <red>[Error]
  this-shop-is-selling: <green>Ten sklep <aqua>SPRZEDAJE<green> itemy.
  shop-stack: '<green>Ilość stacków: <yellow>{0}'
  space: '<green>Miejsce: <yellow>{0}'
  effects: <green>Efekty
  damage-percent-remaining: <yellow>{0}% <green>Zostało.
  item-holochat-data-too-large: <red>[Error] Element NBT jest zbyt duży, aby go pokazać
  stock: '<green>Ilość <yellow>{0}'
  this-shop-is-buying: <green>Ten sklep <light_purple>SKUPUJE<green> itemy.
  successfully-sold: '<green>Sprzedano:'
  total-value-of-chest: '<green>Ilość w skrzyni: <yellow>{0}'
currency-not-exists: <red>Nie można znaleźć waluty, którą chcesz ustawić. Może pisownia jest błędna lub ta waluta nie jest dostępna w tym świecie.
no-nearby-shop: <red>Brak pobliskiego sklepu pasującego do {0}.
translation-author: 'Ghost_chu, Andre_601'
integrations-check-failed-trade: <red>Integracja {0} odmówiła handlu ze sklepem
shop-transaction-failed: <red>Sorry, but an internal error occurred while processing your purchase. The purchase has been cancelled and any operation has been rollback. Please contact the server administrators if it keeping occurred.
success-change-owner-to-server: <green>Pomyślnie ustawiono właściciela sklepu na Serwer.
shop-name-not-found: <red>The shop named <yellow>{0} <red>not exists.
shop-name-too-long: <red>Nazwa tego sklepu jest zbyt długa (maksymalna długość {0}), proszę wybrać inną!
metric:
  header-player: '<yellow>{0} transakcji {1} {2} transakcje/transakcji:'
  action-hover: <yellow>{0}
  price-hover: <yellow>Suma {0}, łącznie z {1} podatkami.
  unknown: <gray>(nieznany)
  undefined: <gray>(brak nazwy)
  no-results: <red>Nie znaleziono transakcji.
  action-description:
    DELETE: <yellow>Gracz usunął sklep. I w miarę możliwości zwrócił właścicielowi opłatę za utworzenie sklepu.
    ONGOING_FEE: <yellow>Gracz zapłacił bieżące opłaty z powodu opóźnionej płatności.
    PURCHASE_BUYING_SHOP: <yellow>Gracz sprzedał kilka przedmiotów w sklepie zakupowym.
    CREATE: <yellow>Gracz utworzył sklep.
    PURCHASE_SELLING_SHOP: <yellow>Gracz sprzedał kilka przedmiotów w sklepie do sprzedawania
    PURCHASE: <yellow>Zakupiony przedmiot ze sklepem
  query-argument: 'Argument zapytania: {0}'
  amount-hover: <yellow>{0}
  header-shop: '<yellow>{0} transakcji {1} {2} transakcje/transakcji:'
  player-hover: |-
    <yellow>{0}<newline><gold>UUUID: <gray>{1}
  looking-up: <yellow>Wykonywanie wyszukiwania metrycznego, proszę czekać...
  tax-hover: <yellow>{0}
  header-global: '<yellow>{0} transakcji {1} transakcje/transakcji:'
  na: <gray>N/A
  transaction-count: <yellow>łącznie{0}
  shop-hover: |-
    <yellow>{0}<newline><gold>Pozycja: <gray>{1} {2} {3}, Świat: {4}<newline><gold>Właściciel: <gray>{5}<newline><gold>Typ sklepu: <gray>{6}<newline><gold>Pozycja: <gray>{7}<newline><gold>Cena: <gray>{8}
  time-hover: '<yellow>Czas: {0}'
  amount-stack-hover: <yellow>{0}x stak/staków
permission-denied-3rd-party: '<red>Odmowa uprawnień: Wtyczka osób trzecich [{0}].'
you-dont-have-that-many-items: <red>Posiadasz tylko {0} {1}.
complete: <green>Zakończony!
translate-not-completed-yet-url: 'Tłumaczenie języka {0} nie zostało jeszcze ukończone przez {1}. Czy chcesz pomóc nam ulepszyć tłumaczenie? Przeglądaj: {2}'
success-removed-shop: <green>Sklep usunięty.
currency-set: <green>Waluta sklepu została ustawiona na {0}.
shop-purged-start: <green>Rozpoczęto czyszczenie sklepu, sprawdź konsolę, aby uzyskać szczegółowe informacje.
economy-transaction-failed: <red>Sorry, but an internal error occurred while processing your transaction. The transaction has been cancelled and any economy operation has been rollback. Please contact the server administrators if it keeping occurred.
nothing-to-flush: <green>Nie masz nowych wiadomości ze sklepu.
no-price-change: <red>Cena nie została zmieniona!
edition-confilct: QuicShop-Hikari with QuickShop-Reremake installed may conflict each other, uninstall one of them.
inventory-unavailable: |-
  <red>This shop InventoryWrapper not exists or invalid. Did you using addon to re-binding the shop Inventory? Information: InventoryWrapper={0}, WrapperProvider={1}, SymbolLink={2}. Please contact the server administrators.
file-test: To jest testowy plik tekstowy. Używamy go do testowania jeśli messages.json jest zepsuty. Możesz wypełnić go dowolnymi easter egg'ami, które lubisz :)
unknown-player: <red>Docelowy gracz nie istnieje, sprawdź wpisaną nazwę użytkownika.
player-offline: <red>Wybrany gracz jest obecnie offline.
player-profile-format: <aqua>{0}</aqua><gold>(<yellow>{1}</yellow>)</gold>
shop-type:
  selling: Sprzedaż
  buying: Zakup
language:
  qa-issues: '<yellow>Problemy z zapewnieniem jakości: <aqua>{0}'
  code: '<yellow>Kod: <gold>{0}'
  approval-progress: '<yellow>Postęp zatwierdzania: <aqua>{0}%'
  translate-progress: '<yellow>Postęp tłumaczenia: <aqua>{0}%'
  name: '<yellow>Nazwa: <gold>{0}'
  help-us: <green>[Pomóż nam poprawić jakość tłumaczenia]
warn-to-paste: |-
  <yellow>Gromadzenie danych i przesyłanie ich do Pastebin może trochę potrwać. <red><bold>Ostrzeżenie:<red> dane są upublicznione przez tydzień! Może to spowodować wyciek z konfiguracji serwera i innych poufnych informacji. Upewnij się, że wysyłasz je tylko do <bold>zaufanego personelu/programistów.
how-many-sell-stack: <green>Wpisz, ile stacków chcesz <light_purple>SPRZEDAĆ<green> na czacie. W każdym stacku jest <yellow>{0}<green> przedmiotów. Możesz sprzedać <yellow>{1}<green> stacków. Wpisz <aqua>{2}<green> by sprzedać wszystkie.
updatenotify:
  buttontitle: '[Aktualizuj Teraz]'
  onekeybuttontitle: '[Aktualizacja OneKey]'
  label:
    github: '[GitHub]'
    ore: '[Rudy]'
    lts: '[LTS]'
    nbtapi: '[+NBTAPI]'
    qualityverifyed: '[Jakość]'
    master: '[Mistrz]'
    unstable: '[Niestabilny]'
    paper: '[+Paper Optimized]'
    stable: '[Stabilny]'
    spigotmc: '[SpigotMC]'
    papermc: '[PaperMC]'
    bukkitdev: '[BukkitDev]'
    polymart: '[Polymart]'
    modrinth: '[Modrinth]'
    basic: '[Podstawowe]'
  list:
    - '{0} został wydany. Nadal używasz {1}!'
    - Boom! Nowa aktualizacja {0} nadchodzą. Aktualizuj!
    - Niespodzianka! {0} wyszedł. Jesteś na {1}
    - Wygląda na to, że musisz zaktualizować. {0} został wydany!
    - Ups! {0} został zwolniony. Jesteś na {1}!
    - Przysięgam, QS został zaktualizowany na {0}. Dlaczego jeszcze nie zaktualizowałeś?
    - Naprawa i re... Przepraszamy, ale {0} został wydany!
    - Err! Nie. To nie jest błąd. {0} został wydany!
    - OMG! Właśnie wyszło {0}, dlaczego ciągle siedzisz na {1}?
    - 'Dzisiaj w faktach po faktach: {0} już jest!'
    - Wtyczka k.i.a. Powinieneś zaktualizować do {0}!
    - Aktualizacja {0} zapalona. Zapisz aktualizację!
    - Dostępna jest aktualizacja dowódcy. {0} właśnie wyszedł!
    - Spójrz na mój styl---{0} zaktualizowany. Nadal używasz {1}
    - Ahhhhhhh! Nowy update {0}! Pobieraj Teraz!
    - Co myślisz? {0} został wydany! Aktualizuj!
    - Lekarz, QuickShop ma nową aktualizację {0}! Powinieneś zaktualizować~
    - Ko~ko~da~yo~ QuickShop ma nową aktualizację {0}~
    - Paimon chce Ci powiedzieć, że QuickShop ma nową aktualizację {0}!
  remote-disable-warning: '<red>Ta wersja QuickShop jest oznaczona jako wyłączona przez zdalny serwer, co oznacza, że ta wersja może mieć poważny problem, uzyskaj szczegóły z naszej strony SpigotMC: {0}. To ostrzeżenie będzie wyświetlane dopóki nie przejdziesz do stabilnej wersji, ale nie wpłynie to na wydajność Twojego serwera.'
purchase-out-of-stock: <red>W tym sklepie skończyły się zapasy, skontaktuj się z właścicielem sklepu lub personelem, aby uzupełnić zapasy.
nearby-shop-entry: '<green>- Info:{0} <green>Cena:<aqua>{1} <green>x:<aqua>{2} <green>y:<aqua>{3} <green>z:<aqua>{4} <green>dystans: <aqua>{5} <green>blok(ów)'
chest-title: Sklep QuickShop
console-only: <red>To polecenie może być wykonane tylko przez konsolę.
failed-to-put-sign: <red>Za mało miejsca wokół sklepu, aby umieścić znak informacyjny.
shop-name-unset: <red>Nazwa tego sklepu została usunięta
shop-nolonger-freezed: <green>Odblokowałeś sklep. Wróć do normalnego sklepu!
no-permission-build: <red>Nie możesz zbudować sklepu tutaj.
tableformat:
  left_half_line: <dark_purple>+--------------------
  right_half_line: <dark_purple>--------------------+
  full_line: <dark_purple>+---------------------------------------------------+
  left_begin: '<dark_purple>| '
quickshop-gui-preview: Podgląd przedmiotów ze Sklepu
translate-not-completed-yet-click: Tłumaczenie języka {0} nie zostało jeszcze ukończone przez {1}. Czy chcesz pomóc nam ulepszyć tłumaczenie? Kliknij tutaj!
taxaccount-invalid: <red>Konto docelowe jest nieprawidłowe, wprowadź prawidłową nazwę gracza lub identyfikator uuid (z myślnikami).
player-bought-from-your-store: <red>{0} zakupił {1} {2} w Twoim sklepie, i zarobiłeś {3}.
reached-maximum-can-create: <red>Już utworzyłeś maksimum {0}/{1} sklepów!
reached-maximum-create-limit: <red>Osiągnąłeś maksymalną liczbę sklepów, które możesz utworzyć
translation-version: 'Wersja wsparcia: Hikari'
no-double-chests: <red>Nie możesz stworzyć sklepu z podwójną skrzynią.
price-too-cheap: <red>Cena musi być większa niż <yellow>${0}
shop-not-exist: <red>Sklep nie istnieje.
bad-command-usage: <red>Błędne argumenty poleceń!
cleanghost-warning: <yellow>This command will purge <red>all</red> shops if the shop is corrupted, was created in not allowed worlds, is selling/buying not allowed items or <red><bold>EXISTS IN A UNLOADED WORLD</bold></red>. Make sure to create a full backup of your shop data first and use <aqua>/qs cleanghost confirm</aqua> to continue.
cleanghost-starting: <green>Starting to check for ghost shops (missing container blocks). All non-existing shops will be removed...
cleanghost-deleting: <yellow>Found a corrupted shop <aqua>{0}</aqua> because {1}, mark it to delete...
cleanghost-deleted: <green>Total <yellow>{0}</yellow> shops have been deleted.
shop-purchase-cancelled: <red>Zakup w sklepie został anulowany.
bypassing-lock: <red>Omijanie blokady QuickShop!
bungee-cross-server-msg: '<yellow>Szybki sklep CSM: <green>{0}'
saved-to-path: Kopia zapasowa została zapisana w {0}.
shop-now-freezed: <green>Zamroziłeś sklep. Nikt nie może teraz handlować z tym sklepem!
price-is-now: <green>Nowa cena sklepu to <yellow>{0}
shops-arent-locked: <red>Pamiętaj, sklepy NIE są chronione przed kradzieżą! Jeśli chcesz zatrzymać złodziei, zablokuj je za pomocą LWC, Lockette, itp!
that-is-locked: <red>Ten sklep jest zablokowany.
shop-has-no-space: <red>Sklep ma miejsce tylko na {0} więcej {1}.
safe-mode-admin: '<red><bold>UWAGA: <red>QuickShop na tym serwerze działa teraz w trybie bezpiecznym, żadne funkcje nie będą działać, wpisz <yellow>/qs <red>, aby sprawdzić błędy.'
shop-stock-too-low: <red>W sklepie pozostało tylko {0} {1}!
world-not-exists: <red>Świat <yellow>{0}<red> nie istnieje
how-many-sell: <green>Wpisz ilość jaką chcesz <light_purple>SPRZEDAĆ<green> na czacie. Możesz sprzedać <yellow>{0}<green>. Wpisz <aqua>{1}<green>, jeśli chcesz sprzedać wszystko.
shop-freezed-at-location: <yellow>Twój sklep {0} w {1} został zamrożony!
translation-contributors: 'Współtwórcy: Timtower, Netherfoam, KaiNoMood, Mgazul, JackTheChicken i Andre_601'
empty-success: <green>Udało się opróżnić sklep
taxaccount-set: <green>Konto podatkowe tego sklepu zostało ustawione na <yellow>{0}
support-disable-reason:
  hot-reload: <yellow>Unsupported hot reloaded, restart the server and check again.
  outdated: <yellow>This version of QuickShop already outdated, up-to-date before requesting support!
  bad-hosts: |-
    <yellow>This server HOSTS has been modified and QuickShop some functions require connect to Mojang API correctly. Fix the HOSTS before asking support, Windows at C:\windows\system32\drivers\etc\hosts, Linux at /etc/hosts.
  privacy: <yellow>This server running under cracked (offline) mode. If you are running bukkit server under proxy and online-mode=true on proxy, please configure the proxy related settings correctly.
  modified: <yellow>File integrity check failed, this QuickShop build has been modified by others.
  consolespamfix-installed: <yellow>ConsoleSpamFix installed, it will hidden exception details, temporarily disable it during asking support.
  authlib-injector-detected: <yellow>This server running under 3rd-party authlib provider like authlib-injector.
  unsupported-server-software: <yellow>Unsupported server software, Any modded hybrid server software is unsupported including MCPC, Cauldron, CatServer, Mohist, Magma, Fukkit, Cardboard etc.
supertool-is-disabled: <red>Supertool jest wyłączone. Nie można zniszczyć żadnych sklepów.
unknown-owner: Nieznany
restricted-prices: '<red>Ograniczona cena dla {0}: min. {1}, maks. {2}'
nearby-shop-this-way: <green>Sklep jest {0} bloków od ciebie.
owner-bypass-check: <yellow>Pominięto wszystkie sprawdzania. Handel zakończony sukcesem! (Jesteś teraz właścicielem sklepu!)
april-rick-and-roll-easter-egg: "<hover:show_text:'Click to open in browser for time limited rewards!'><click:open_url:'https://www.youtube.com/watch?v=dQw4w9WgXcQ'><green>Click here to acquire your time limited rewards that provided by QuickShop-Hikari developer!</green></click></hover>"
signs:
  item-right: ''
  out-of-space: Za mało miejsca
  unlimited: Nieograniczony
  stack-selling: Sprzedaj {0}
  stack-price: '{0} za {1}x {2}'
  status-unavailable: <red>
  out-of-stock: Wyprzedano produkt
  stack-buying: Kup {0}
  freeze: Handel wyłączony
  price: '{0} za 1 szt.'
  buying: Sprzedaj {0}
  header: '{1}{0}'
  header-available: <green>{0}
  header-unavailable: <red>{0}
  selling: Kup {0}
  status-available: <green>
  item-left: ''
negative-amount: <red>Nie możesz handlować ujemnymi kwotami
display-turn-on: <green>Pomyślnie włączono wyświetlacz sklepu.
shop-staff-deleted: <green>Pomyślnie usunięto {0} jako członka personelu twojego sklepu.
nearby-shop-header: '<green>Odpowiadające sklepy w pobliżu <aqua>{0}<green>:'
backup-failed: Nie można wykonać kopii zapasowej bazy danych. Sprawdź konsolę, aby uzyskać szczegóły.
shop-staff-cleared: <green>Pomyślnie usunięto wszystkich graczy z twojego sklepu.
price-too-high: <red>Zbyt wysoka cena sklepu! Nie można utworzyć takiego, którego cena jest wyższa niż {0}.
plugin-cancelled: '<red>Operation cancelled, Reason: {0}'
player-sold-to-your-store: <green>{0} sprzedał {1} {2} do Twojego sklepu.
shop-out-of-stock: <dark_purple>W twoim sklepie na {0}, {1}, {2}, zabrakło {3}!
how-many-buy: <green>Wpisz ilość jaką chcesz <aqua>KUPIĆ<green> na czacie. Możesz kupić <yellow>{0}<green>. Wpisz <aqua>{1}<green>, jeśli chcesz kupić wszystko.
language-info-panel:
  help: 'Pomóż nam: '
  code: 'Kod: '
  name: 'Język: '
  progress: 'Postęp: '
  translate-on-crowdin: '[Tłumacz na Crowdin]'
item-not-exist: <red>Element <yellow>{0} <red>nie istnieje, sprawdź swoją pisownię.
shop-creation-failed: <red>Tworzenie sklepu nie powiodło się, skontaktuj się z administratorem serwera.
inventory-space-full: <red>Your inventory space remains can only put <green>{1}x <red>items into it, try empty the your inventory!
no-creative-break: <red>Nie możesz zniszczyć sklepów innych graczy w trybie kreatywnym, przełącz się na tryb przetrwania lub zamiast tego spróbuj użyć SuperTool {0}.
booleanformat:
  success: <green>✔
  failed: <red>✘
controlpanel:
  stack: '<green>Ilość za stack: <aqua>{0} <yellow>[<bold><light_purple>Zmień</light_purple>]'
  price-hover: <yellow>Kliknij, aby ustawić nową cenę produktu.
  remove: <red><bold>[Usuń sklep]
  mode-buying-hover: <yellow>Kliknij, aby zmienić sklep na tryb SPRZEDAŻ.
  empty: '<green>Empty: Remove all items <yellow>[<light_purple><bold>OK<yellow>]'
  stack-hover: <yellow>Kliknij, aby ustawić ilość produktu na stack. Ustaw na 1 dla normalnego zachowania.
  alwayscounting-hover: <yellow>Kliknij, aby przełączyć, jeśli sklep zawsze liczy pojemnik.
  alwayscounting: '<green>Always counting: {0} <yellow>[<light_purple><bold>Change<yellow>]'
  setowner: '<green>Owner: <aqua>{0} <yellow>[<light_purple><bold>Change<yellow>]'
  freeze: '<yellow>Freeze mode: <aqua>{0} <yellow>[<light_purple><bold>Toggle<yellow>]'
  price: '<green>Price: <aqua>{0} <yellow>[<light_purple><bold>Change<yellow>]'
  currency-hover: <yellow>Kliknij, aby ustawić lub usunąć walutę, której używa ten sklep
  lock: '<yellow>Shop lock: <aqua>{0} <yellow>[<light_purple><bold>Toggle<yellow>]'
  mode-selling: '<green>Tryb sklepu: <aqua>Sprzedaż <yellow>[<light_purple><bold>Zmień<yellow>]'
  currency: '<green>Currency: <aqua>{0} <yellow>[<light_purple><bold>Set<yellow>]'
  setowner-hover: <yellow>Kliknij, aby zmienić właściciela.
  mode-buying: '<green>Shop mode: <aqua>Buying <yellow>[<light_purple><bold>Change<yellow>]'
  item: '<green>Shop Item: {0} <yellow>[<light_purple><bold>Change<yellow>]'
  unlimited: '<green>Unlimited: {0} <yellow>[<light_purple><bold>Change<yellow>]'
  unlimited-hover: <yellow>Kliknij, aby przełączyć.
  refill-hover: <yellow>Kliknij, aby uzupełnić sklep.
  remove-hover: <yellow>Kliknij, żeby usunąć sklep.
  toggledisplay-hover: <yellow>Przełącz status wyświetlania przedmiotu w sklepie
  refill: '<green>Refill: Refill the items <yellow>[<light_purple><bold>OK<yellow>]'
  freeze-hover: <yellow>Przełącz status zamrożenia sklepu.
  lock-hover: <yellow>Włącz/Wyłącz ochronę blokady sklepu.
  item-hover: <yellow>Kliknij, aby zmienić przedmiot sklepu
  infomation: '<green>Panel sterowania sklepu:'
  mode-selling-hover: <yellow>Kliknij, aby zmienić sklep na tryb KUPNO.
  empty-hover: <yellow>Kliknij, aby wyczyścić sklep.
  toggledisplay: '<green>Display-Item: <aqua>{0} <yellow>[<light_purple><bold>Toggle</bold></light_purple>]'
  history: '<green>Historia: <yellow>[<bold><light_purple>Zobacz</light_purple></bold>]</yellow>'
  history-hover: <yellow>Kliknij, aby wyświetlić dzienniki historii sklepu
timeunit:
  behind: za
  week: "{0} week"
  weeks: "{0} weeks"
  year: "{0} year"
  before: przed
  scheduled: zaplanowane
  years: "{0} years"
  scheduled-in: zaplanowane w {0}
  second: "{0} second"
  std-past-format: '{5}{4}{3}{2}{1}{0}temu'
  std-time-format: GG:mm:ss
  seconds: "{0} seconds"
  hour: "{0} hour"
  scheduled-at: zaplanowane na {0}
  after: po
  day: "{0} day"
  recent: najnowsze
  between: pomiędzy
  hours: "{0} hours"
  months: "{0} months"
  longtimeago: dawno temu
  between-format: pomiędzy {0} a {1}
  minutes: "{0} minut"
  justnow: w tej chwili
  minute: "{0} minuta"
  std-format: MM/dd/yyyy HH:mm:ss
  future-plain-text: przyszłość
  month: "{0} miesiąc"
  future: w {0}
  days: "{0} dni"
command:
  reloading: '<green>Configuration reloaded. <yellow>Some changes may require reboot to affect. <newline><gray>(Notice: Reloading behavior has been changed after ********, we now only reload configuration but not whole plugin to ensure the server won''t crashed.)'
  description:
    buy: <yellow>Zmienia rodzaj sklepu na tryb <light_purple>KUP
    about: <yellow>Pokazuje informacje ze Sklepu
    language: <yellow>Zmień aktualnie używany język
    purge: <yellow>Uruchom zadanie czyszczenia sklepu w tle
    paste: <yellow>Wgruje dane serwera do Pastebin
    title: <green>Pomoc Sklepu
    remove: <yellow>Usuwa sklep na który patrzysz
    ban: <yellow>Banuje gracza ze sklepu
    empty: <yellow>Usuwa wszystkie elementy ze sklepu
    alwayscounting: <yellow>Ustaw jeśli sklep zawsze liczy pojemnik nawet jeśli jest nieograniczony
    setowner: <yellow>Zmienia właściciela sklepu.
    reload: <yellow>Odświeża plik config.yml z QuickShop
    freeze: <yellow>Wyłącz lub włącz handel sklepowy
    price: <yellow>Zmienia cenę zakupu/sprzedaży sklepu
    find: <yellow>Lokalizuje najbliższy sklep określonego typu.
    create: <yellow>Tworzy nowy sklep z wybranej skrzyni
    lock: <yellow>Zmień status blokady sklepu
    currency: <yellow>Ustaw lub usuń ustawienia waluty sklepu
    removeworld: <yellow>Usuń WSZYSTKIE sklepy w określonym świecie
    info: <yellow>Pokaż statystyki
    owner: <yellow>Zmienia właściciela sklepu.
    amount: <yellow>Aby ustawić kwotę pozycji (przydatne przy zgłaszaniu problemów z czatem)
    item: <yellow>Zmień przedmiot sklepu w sklepie
    debug: <yellow>Włącza tryb programisty
    unlimited: <yellow>Daje sklepowi nieograniczone zapasy.
    sell: <yellow>Zmienia rodzaj sklepu na tryb <aqua>SPRZEDAJ
    fetchmessage: <yellow>Pokaż nieprzeczytane wiadomości sklepu
    staff: <yellow>Zarządzaj personelem sklepu
    clean: <yellow>Usuwa wszystkie (załadowane) sklepy bez żadnych zapasów
    refill: <yellow>Dodaje daną liczbę przedmiotów do sklepu
    help: <yellow>Pokazuje pomoc Sklepu
    removeall: <yellow>Usuń WSZYSTKIE sklepy określonego gracza
    unban: <yellow>Odbanuje gracza ze sklepu
    transfer: <yellow>Przenieś czyjeś WSZYSTKIE sklepy do innych
    transferall: <yellow>Przenieś czyjeś WSZYSTKIE sklepy do innych
    transferownership: <yellow>Transfer the shop you're looking at to someone else
    size: <yellow>Zmień na dużą ilość sklepu
    supercreate: <yellow>Utwórz sklep podczas omijania wszystkich kontroli ochrony
    taxaccount: <yellow>Ustaw konto podatkowe, które sklep ma używać
    name: <yellow>Nadaje sklepowi konkretną nazwę
    toggledisplay: <yellow>Przełącz stan wyświetlania przedmiotu w sklepie
    permission: <yellow>Zarządzanie uprawnieniami sklepu
    lookup: <yellow>Zarządzaj tabelą elementów do wyszukiwania
    database: <yellow>View and maintain QuickShop database
    benefit: <yellow>Settings of divide benefits between shop owner and other players
    tag: <yellow>Add, remove or query tags of a shop
    suggestprice: <yellow>Suggest a recommend price for looking shop, based on other shops
    history: <yellow>View the history transactions of a shop
    sign: <yellow>Change the sign material of a shop
  bulk-size-not-set: '<red>Użycie: /qs size <amount>'
  no-type-given: '<red>Użycie: /qs find <item>'
  feature-not-enabled: Ta funkcja nie jest włączona w pliku konfiguracyjnym.
  now-debuging: <green>Pomyślnie przełączono się do trybu developerskiego. Przeładuj QuickShop...
  no-amount-given: <red>Nie podano ilości. Użyj <green>/qs refill <amount><red>
  no-owner-given: <red>Nie podano właściciela
  disabled: '<red>Ta komenda jest wyłączona: <yellow>{0}'
  bulk-size-now: <green>Teraz handluj &{0}x {1}
  toggle-always-counting:
    counting: <green>Sklep zawsze liczy pojemnik nawet jeśli jest nieograniczony
    not-counting: <green>Sklep jest teraz respektowany, jeśli jest nieograniczony
  cleaning: <green>Usuwanie sklepów bez zapasów...
  now-nolonger-debuging: <green>Pomyślnie przełączono się do trybu developerskiego. Przeładuj QuickShop...
  toggle-unlimited:
    limited: <green>Sklep jest teraz limitowany
    unlimited: <green>Sklep jest teraz nieograniczony
  transfer-success-other: <green>Przeniesiono &{0} sklep(y/ów) {1} do <yellow>{2}
  no-trade-item: <green>Proszę trzymać przedmiot handlowy, aby zmienić go w ręce głównej
  wrong-args: <red>Nieprawidłowy argument. Użyj <bold>/qs help <red>aby zobaczyć listę poleceń.
  some-shops-removed: <yellow>{0} <green>sklep(y/ów) usunięto
  new-owner: '<green>Nowy Właściciel: <yellow>{0}'
  format: <green>/{0} {1} <yellow>- {2}
  transfer-success: <green>Przeniesiono <yellow>{0} <green>sklep(y/ów) do <yellow>{1}
  now-buying: <green>Teraz <light_purple>SKUPUJESZ<green> <yellow>{0}
  now-selling: <green>Teraz <light_purple>SPRZEDAJESZ<green> <yellow>{0}
  cleaned: <green>Usunięto <yellow>{0}<green> sklepy.
  trade-item-now: <green>Teraz handluj &{0}x {1}
  no-world-given: <red>Proszę podać nazwę świata
  format-disabled: <red>/{0} {1} <gray>- {2}
  invalid-bulk-amount: <red>Podana wartość {0} jest większa niż maksymalna wielkość stacku lub mniejsza niż jeden
currency-not-support: <red>Wtyczka ekonomiczna nie obsługuje funkcji wieloekonomicznej.
trading-in-creative-mode-is-disabled: <red>Nie możesz handlować z tym sklepem, będąc w trybie kreatywnym.
the-owner-cant-afford-to-buy-from-you: <red>To kosztuje {0}, ale właściciel ma tylko {1}
you-cant-afford-shop-naming: <red>Nie możesz sobie pozwolić na nazwę sklepu, to nazewnictwo kosztuje {0}.
inventory-error: |-
  <red>Failed to processing the InventoryWrapper. Did you using addon to re-binding the shop Inventory? Information: Exception={0}, InventoryWrapper={1}, WrapperType={2}, WrapperProvider={3}, SymbolLink={4}. Please contact the server administrators.
integrations-check-failed-create: <red>Integracja {0} odmówiła utworzenia sklepu
shop-out-of-space: <dark_purple>Twój sklep w {0}, {1}, {2} jest teraz pełny.
admin-shop: Sklep Administracyjny
no-anythings-in-your-hand: <red>W twojej ręce nic nie ma.
no-permission: <red>Nie masz uprawnień, aby to zrobić.
chest-was-removed: <red>Sklep został usunięty.
you-cant-afford-to-buy: <red>To kosztuje {0}, ale masz tylko {1}
shops-removed-in-world: <yellow>Wszystkie <aqua>{0}<yellow> sklepy zostały usunięte ze świata <aqua>{1}<yellow>.
display-turn-off: <green>Pomyślnie wyłączono wyświetlacz sklepu.
client-language-unsupported: <yellow>QuickShop nie obsługuje Twojego języka klienta, powrócono do wersji językowej {0}.
language-version: '63'
not-managed-shop: <red>Nie jesteś właścicielem ani moderatorem tego sklepu
shop-cannot-trade-when-freezing: <red>Nie możesz handlować z tym sklepem, ponieważ jest zamrożony.
invalid-container: <red>Invalid container, You can only create the shop on block who have inventory.
permission:
  header: <green>Zezwolenie sklepu
  header-player: <green>Zezwolenie sklepu dla {0}
  header-group: <green>Informacje o uprawnieniach sklepu dla grupy {0}
  table: <gold><b>{0}</b>:</gold> <gray>{1}</gray>
  item:
    purchase: <yellow>Permission to allow users who have this to purchase the shop. (including buying and selling)
    show-information: <yellow>Zezwolenie na przeglądanie informacji o sklepie przez użytkowników (otwarty panel informacyjny sklepu)
    preview-shop: <yellow>Pozwolenie na umożliwienie użytkownikom, którzy mają to do podglądu sklepu. (podgląd elementu)
    search: <yellow>Permission to allow users who have this to search the target shop. (remove permission will hide shop from search result)
    delete: <yellow>Pozwolenie na usunięcie sklepu.
    receive-alert: <yellow>Permission to allow users who have this to receive alert message (e.g out-of-stock or new trade).
    access-inventory: <yellow>Pozwolenie na dostęp do ekwipunku sklepu.
    ownership-transfer: <yellow>Pozwolenie na przeniesienie własności sklepu przez użytkowników, którzy to posiadają.
    management-permission: <yellow>Permission to allow users who have this to manage the groups permissions and edit user's group.
    toggle-display: <yellow>Pozwolenie na umożliwienie użytkownikom, którzy mają to do przełączenia elementu wyświetlanego w sklepie.
    set-shoptype: <yellow>Permission to allow users who have this to set the shop type (switch buying or selling).
    set-price: <yellow>Pozwolenie na umożliwienie użytkownikom, którzy to mają ustawiania ceny sklepu.
    set-item: <yellow>Pozwolenie na to, aby użytkownicy, którzy mają to do ustawiania elementu sklepu.
    set-stack-amount: <yellow>Pozwolenie na umożliwienie użytkownikom, którzy mają to do ustawienia ilości stosu sklepu.
    set-currency: <yellow>Pozwolenie na umożliwienie użytkownikom, którzy mają to ustawienie waluty sklepu.
    set-name: <yellow>Pozwolenie na umożliwienie użytkownikom, którzy mają to do ustawienia nazwy sklepu.
    set-sign-type: <yellow>Change the sign material that attached on the shop.
    view-purchase-logs: <yellow>Permission to view shop purchase logs.
  group:
    everyone: <yellow>Default group for all users excepted shop owner.
    staff: <yellow>System group for shop staffs.
    administrator: <red>System group for administrators, the users in this group will have permissions almost same with shop owner.
invalid-group: <red>Niepoprawna nazwa grupy.
invalid-permission: <red>Invalid permission.
invalid-operation: <red>Invalid operation, only {0} are allowed.
player-no-group: <yellow>Player {0} not in any group in this shop.
player-in-group: <green>Player {0} is in group <aqua>{1}</aqua> in this shop.
permission-required: <red>You don't have permission {0} in this shop to do this.
no-permission-detailed: <red>You don't have permission <yellow>{0}</yellow> to do this.
paste-notice: "<yellow>Note: If you are creating a Paste for troubleshooting purposes, be sure to reproduce the error before creating the Paste as quickly as possible; we need the logs retained briefly in the buffer to troubleshoot. If you create a Paste too slowly or without first reproducing the error or restarted server, the Paste will record nothing and useless."
paste-uploading: <aqua>Please wait... Uploading the paste to pastebin......
paste-created: '<green>Paste created, click to open in browser: <yellow>{0}</yellow><newline><red>Warning: <gray>Never send paste to who you don''t trust.'
paste-created-local: |-
  <green>Paste created and saved to your local disk at: {0}<newline><red>Warning: <gray>Never send paste to who you don't trust.
paste-created-local-failed: <red>Failed to save paste to your local disk, please check your disk.
paste-451: |-
  <gray><b>TIPS: </b> Your current country or region seems to have blocked the CloudFlare Workers service and QuickShop Paste may not be loaded properly.<newline><gray>If the subsequent operation fails, try adding the --file additional parameter to generate a local Paste: <dark_gray>/qs paste --file</dark_gray>
paste-upload-failed: <red>Failed to upload paste to {0}, trying other pastebin provider...
paste-upload-failed-local: <red>Failed to upload paste, trying generate local paste...
command-incorrect: '<red>Command usage incorrect, type /qs help to check help. Usage: {0}.'
successfully-set-player-group: <green>Successfully set player {0} group to <aqua>{1}</aqua>.
successfully-unset-player-group: <green>Successfully remove player group in this shop.
successfully-set-player-permission: <green>Successfully set player {0} permission <aqua>{1}</aqua> in shop <aqua>{2}</aqua>.
lookup-item-created: <green>An item named <aqua>{0}</aqua> has been created in the lookup table. You can refer this item in configurations now.
lookup-item-exists: <red>An item named <yellow>{0}</yellow> already exists in the lookup table, delete it or pick another name.
lookup-item-not-found: <red>An item named <yellow>{0}</yellow> not exists.
lookup-item-name-illegal: <red>Item name illegal. Only alphanumeric characters and underscores are allowed.
lookup-item-name-regex: '<red>The name must match this regex: <aqua>{0}</aqua>.'
lookup-item-test-not-found: '<gold>Test: <yellow>The item in your hand not registered in lookup table.'
lookup-item-test-found: '<gold>Test: <green>The item in your hand was registered as name <aqua>{0}</aqua> in lookup table.'
lookup-item-removed: <green>The specified item <aqua>{0}</aqua> has been successfully removed from lookup table.
internal-error: <red>An internal error occurred, please contact the server administrator.
argument-cannot-be: <red>The argument <aqua>{0}</aqua> cannot be <yellow>{1}</yellow>.
argument-must-between: <red>The argument <aqua>{0}</aqua> value must between <yellow>{1}</yellow> and <yellow>{2}</yellow>
invalid-percentage: <red>Percentage invalid, you must add '%' after the number of percentage.
display-fallback: |-
  <red>Due to an internal failure, a fallback message is being displayed.
  The value of this item may not be localized or processed correctly.
  Please contact the server administrator.
not-a-valid-time: |-
  <red>The string <yellow>{0}</yellow> not a valid timestamp, please enter a <aqua>Zulu Time (ISO 8601)</aqua> or <aqua>Unix Epoch Time in seconds</aqua>.
  <gold>Valid timestamp example: (for Sat, 17 Dec 2022 10:31:37 GMT)</gold>
  <aqua>-<aqua> <yellow>2022-12-17T10:31:37Z</yellow> <grey>(Zulu Time)</grey>
  <aqua>-<aqua> <yellow>1671273097</yellow> <grey>(Unix Epoch Time in seconds)</grey>
invalid-past-time: <red>You can't specify a time in the past.
debug:
  arguments-invalid: <red>Provided arguments <yellow>{0}</yellow> invalid.
  sign-located: '<green>Sign valid: <yellow>{0}</yellow>.'
  operation-missing: <red>You must specific an operation.
  operation-invalid: <red>You must specific an valid operation.
  invalid-base64-encoded-sql: <red>The SQL provided must be base64 encoded.
  warning-sql: |-
    <red><bold>Warning:</bold></red><yellow>You're executing a SQL statement, this may corrupt your database or destroy any data in database even it belongs to other plugins.
    <red>Don't confirm if you don't trust who send you this command.
  warning-sql-confirm: <yellow>To confirm this dangerous operation, please type <aqua>/qs debug database sql confirm {0}</aqua> in 60 seconds.
  warning-sql-confirm-hover: <yellow>Click to confirm this dangerous operation.
  sql-confirm-not-found: <yellow>Didn't found the operation that you given, it may invalid or expired.
  sql-executing: '<yellow>Executing SQL statement: <aqua>{0}</aqua>'
  sql-completed: <green>Completed, {0} rows affected.
  sql-exception: <red>An error occurred while executing the SQL query, check the Console for details!
  sql-disabled: '<red>For security reason, SQL queries are disabled in this server, If you really need this feature, you can add the following flag to startup arguments: <aqua>{0}</aqua> with `true` value to enable it.'
  force-shop-reload: <yellow>Forcing reload all loaded shops...
  force-shop-reload-complete: <green>Forced <aqua>{0}</aqua> shops to be reloaded.
  force-shop-loader-reload: <yellow>Forcing shop-loader reloading...
  force-shop-loader-reload-unloading-shops: <yellow>Unloading <aqua>{0}</aqua> loaded shops...
  force-shop-loader-reload-unloading-shops-from-memory: <yellow>Removing <aqua>{0}</aqua> shops from memory...
  force-shop-loader-reload-reloading-shop-loader: <yellow>Re-calling shop-loader for reloading all shops from database...
  force-shop-loader-reload-complete: <green>Shop-loader has been reloaded all shops!
  toggle-shop-loaded-status: <aqua>Toggle this shop loaded status to <gold>{0}</gold>
  shop-internal-data: '<yellow>The internal data of this shop: <reset>{0}</reset>'
  handler-list-not-valid-bukkit-event-class: <red>The provided class <yellow>{0}</yellow> is not a valid Bukkit event class.
  update-player-shops-signs-no-username-given: <red>You must provide a valid player username.
  update-player-shops-signs-create-async-task: <yellow>Creating async tasks for force updating signs...
  update-player-shops-player-selected: '<yellow>Player selected: <gold>{0}</gold>'
  update-player-shops-player-shops: <yellow>Total <gold>{0}</gold> shops waiting for updates.
  update-player-shops-per-tick-threshold: '<yellow>Max shops can be update per tick: <gold>{0}</gold>'
  update-player-shops-complete: <green>Task completed, Used <yellow>{0}ms</yellow> for updating.
  update-player-shops-task-started: <gold>The tasks has been started, please wait for it to complete.
  item-info-store-as-string: "<green>The store you looking at: <gold>{0}</gold> Hash: <white>{1}</white>"
  item-info-hand-as-string: "<green>The item in your hand: <gold>{0}</gold> Hash: <white>{1}</white>"
  item-matching-result: "<green>Hand2Store: <aqua>{0}</aqua>, Store2Hand: <aqua>{1}</aqua>"
  hikari-cp-size-tweak: "<green>HikariCP MaximumPoolSize and MinimumIdle was set to <white>{0}</white>"
  hikari-cp-testing: "<green>Please wait, testing HikariCP connection..."
  hikari-cp-working: "<green>Pass! HikariCP working well!"
  hikari-cp-not-working: "<red>Failed! The connection that returned by HikariCP is dead! (Not passed test in 1 second)"
  hikari-cp-timeout: "<red>HikariCP is timed out while getting a valid connection, please clean up all active queries to release connection resources."
  queries-stopped: "<green>Stopped <white>{0}</white> active queries."
  queries-dumping: "<yellow>Dumping active queries..."
  restart-database-manager: "<yellow>Restarting SQLManager..."
  restart-database-manager-clear-executors: "<yellow>Clearing executors..."
  restart-database-manager-unfinished-task: "<yellow>Unfinished task: <white>{0}</white>"
  restart-database-manager-unfinished-task-history-query: "<yellow>Unfinished task (History Query): <white>{0}</white>"
  restart-database-manager-reconnect: "<yellow>Re-launching SQLManager via initial sequence (via async executor)"
  restart-database-manager-done: "<green>Done!"
  property-incorrect: "<yellow>You must enter a (and only one) property key=value set. E.g   aaa=bbb"
  property-security-block: "<red>Request was rejected, for security reason, you can only change the property that startsWith <aqua>com.ghostchu.quickshop</aqua> or <aqua>quickshop</aqua>."
  property-removed: "<green>Removed property key <white>{0}</white>"
  property-changed: "<green>Property key <white>{0}</white> was changed from <white>{1}</white> to <white>{2}</white>"
  marked-as-dirty: "<green>Marked all stores as dirty status, they will be force save in next auto-save task. (Restart the server to force execute store saving task)"
  display-removed: "<green>Successfully removed <yellow>{0}</yellow> QuickShop display items/entities from the worlds."
database:
  scanning: <green>Scanning the isolated data in QuickShop databases, the database load may increase in scan progress, this might need a while...
  scanning-async: <yellow>Scanning the isolated data in QuickShop databases on async task thread, the database load may increase in scan progress, this might need a while, try again later.
  already-scanning: <red>An scan task has been started, please wait until it finished.
  trim-warning: '<yellow><red><bold>Warning: </red>Backup your database before continue database trim to avoid the data loss. Once you get ready, execute <aqua>/qs database trim confirm</aqua> to continue.'
  status: '<yellow>Status: {0}'
  status-good: <green>Good
  status-bad: <yellow>Maintenance Required
  isolated: '<yellow>Isolated Data:'
  isolated-data-ids: '<yellow><aqua>└</aqua> Data Records: <gold>{0}</gold>'
  isolated-shop-ids: '<yellow><aqua>└</aqua> Shop Indexes: <gold>{0}</gold>'
  isolated-logs: '<yellow><aqua>└</aqua> Logs: <gold>{0}</gold>'
  isolated-external-caches: '<yellow><aqua>└</aqua> External Caches: <gold>{0}</gold>'
  last-purge-time: <yellow>Ostatni czas przycinania o {0}
  report-time: <yellow>Ostatni raz skanowania w {0}
  auto-scan-alert: <yellow>The QuickShop database on this server require a maintenance. Found <gold>{0}</gold> isolated data wait for trimming.
  auto-trim: <green>Auto trim has been enabled on this server, no manually trim required.
  trim-complete: <green>Database trim completed, <yellow>{0}</yellow> isolated trimmed.
  auto-trim-started: <green>Auto trim has been started, please wait...
  trim-start: <green>Database trim started, please wait...
  trim-exception: <red>Database trim failed, an exception raised during database trimming, check the server console.
  generated-at: '<yellow>Generated at: <gold>{0}</gold>'
  purge-date: <red>Musisz podać datę.
  purge-warning: <yellow>This operation will purge the history that stored in your database including shop creates/changes/deletes, purchases, transactions and system logs. Delete those data can free the disk space but all history metric will lost, and other plugins that depend on metrics will stop working. To continue execute this operation, execute `/qs database purgelogs <before-days> confirm`
  purge-task-created: <green>Task created! Database are silent purging history records in background.
  purge-done-with-line: <green>Purge task completed, total <gold>{0}</gold> lines purged from database.
  purge-done-with-error: <red>Purge task failed, check the server console for details.
  purge-players-cache: <yellow>Please wait, purging players caches...
  purge-players-completed: |-
    <green>Successfully purged {0} players caches from both memory and database.<newline><aqua>Note: You server performance may impact by this operation.
  purge-players-error: <red>Failed to purge players caches, please check the server console.
  suggestion:
    trim: <yellow>This database require a isolated data trim, execute command <aqua>/qs database trim</aqua> to trim the database.
always-counting-removal-early-warning: <red>Always Counting feature is scheduled for removal, you shouldn't use it anymore and will stop work in future.
exporting-database: <green>Eksportowanie bazy danych, proszę czekać...
exporting-failed: <red>Nie udało się wyeksportować bazy danych, sprawdź konsolę serwera.
exported-database: <green>Baza danych wyeksportowana do <yellow>{0}</yellow>.
importing-not-found: <red>The <yellow>{0}</yellow> file not found, please check the file path.
importing-early-warning: |-
  <yellow><red><bold>Warning: </bold></red>The backup will be imported to the current database, any exists data will be purged and permanently lost if you don't have a backup.<newline><red>Are you sure to continue the import progress?</red><yellow>Type <aqua>/qs recovery confirm</aqua> to continue.
importing-database: <green>Eksportowanie bazy danych, proszę czekać...
importing-failed: <red>Nie udało się wyeksportować bazy danych, sprawdź konsolę serwera.
imported-database: <green>Baza danych wyeksportowana do <yellow>{0}</yellow>.
transfer-sent: <green>Shops transfer request sent to <yellow>{0}</yellow>.
transfer-request: <yellow>Player <aqua>{0}</aqua> requested to transfer their shops to you. Did you want accept the transfer?
transfer-single-request: <yellow>Player <aqua>{0}</aqua> would like to transfer a shop to you. Do you want to accept the request?
transfer-ask: |-
  <gold>Type <red>/qs transfer accept</red> to accept.</gold><newline><gold>Type <red>/qs transfer deny</red> to deny.</gold><newline><gold>Request will expire after <red>{0}</red> seconds.
transferall-ask: |-
  <gold>Type <red>/quickshop transferall accept</red> to accept or <red>/quickshop transferall deny</red> to deny.
  The request will expire after <red>{0}</red> seconds.
transfer-single-ask: |-
  <gold>Type <red>/quickshop transferownership accept</red> to accept or <red>/quickshop transferownership deny</red> to deny.
  The request will expire after <red>{0}</red> seconds.
transfer-accepted-fromside: <green>Player <aqua>{0}</aqua> accepted your shop transfer request.
transfer-accepted-toside: <green>You accepted <aqua>{0}</aqua>'s transfer request.
transfer-rejected-fromside: <red>Player <aqua>{0}</aqua> rejected your shop transfer request.
transfer-rejected-toside: <red>You rejected <aqua>{0}</aqua>'s shop transfer request.
transfer-no-pending-operation: <red>You don't have pending transfer request yet.
transfer-no-self: <red>You can't transfer your shops to yourself.
benefit-overflow: <red>The sum of all benefits cannot be greater than or equal to 100%.
benefit-exists: <red>Target player already in benefits list on this shop.
benefit-removed: <red>Target player has been removed from shop benefits.
benefit-added: <green>Player <aqua>{0}</aqua> has been added into shop benefits!
benefit-updated: <green>Player <aqua>{0}</aqua>'s benefits has been updated!
benefit-query: <green>This shop have <yellow>{0}</yellow> players in benefits list!
benefit-query-list: <yellow> - </yellow><white>Player <gold>{0}</gold>, Benefit <gold>{1}%</gold></white>
tag-added: '<green>Successfully added <aqua>#{0}</aqua> to this shop!'
tag-add-duplicate: '<red>The tag <aqua>#{0}</aqua> already exists at this shop!'
tag-removed: '<green>Successfully removed <aqua>#{0}</aqua> from this shop!'
tag-remove-not-exists: 'The tag <aqua>#{0}</aqua> not exists at this shop!'
tag-cleared: <green>Successfully cleared all tags from this shop!
tag-shops-cleared: '<green>Successfully cleared <aqua>#{0}</aqua> from all your tagged shops!'
tag-query: '<green>This shop have <yellow>{0}</yellow> tags:'
tag-query-listing: '<yellow> - <aqua>#{0}</aqua>'
tag-query-no-tag: <red>This shop have no tags.
tag-query-shops: '<green>This tag contains <yellow>{0}</yellow> shops:'
tag-query-shops-listing: <yellow> - <aqua>{0}</aqua>
batch-operations-based-on-tags-no-failure: <green>Successfully batch processed <yellow>{0}</yellow> shops.
batch-operations-based-on-tags-have-failure: <yellow>Total <yellow>{0}</yellow> shops in batch processing successfully completed, but <red>{1}</red> requests not completed.
batch-operations-based-on-tags-have-failure-with-reason: '<yellow>Total <yellow>{0}</yellow> shops in batch processing successfully completed, but <red>{1}</red> requests not completed. Reason: <gold>{2}</gold>'
batch-operations-based-on-tags-have-failure-error-messages-may-appears-above: <gray>The error messages will send to you above this message in the chat.
addon:
  towny:
    commands:
      town: <yellow>Set or unset a shop to a Town shop
      nation: <yellow>Set or unset a shop to a Nation shop
    make-shop-owned-by-town: <green>You have made the shop owned by town <yellow>{0}</yellow>.
    make-shop-no-longer-owned-by-town: <green>You have reset shop ownership, it now transfer back to original shop owner.
    make-shop-owned-by-nation: <green>You have made the shop owned by nation <yellow>{0}</yellow>.
    make-shop-no-longer-owned-by-nation: <green>You have reset shop ownership, it now transfer back to original shop owner.
    shop-owning-changing-notice: <grey>This shop now owned by a town/nation, original shop owner has been automatically added into administrator list, modify or add any new owner / staff by using command /qs permission
    target-shop-already-is-town-shop: <red>The target shop is already owned by a town.
    target-shop-already-is-nation-shop: <red>The target shop is already owned by a nation.
    target-shop-not-in-town-region: <red>The target shop is not in the town.
    target-shop-not-in-nation-region: <red>The target shop is not in the nation.
    item-not-allowed: <red>This shop's item are not allowed used by town/nation shop, pick another one!
    operation-disabled-due-shop-status: <red>This shop operation has been disabled due this already is a town/nation shop.
    plot-type-disallowed: <red>You can't create town/nation shop on this type of plot.
    flags:
      own: <red>You can only create shops at your owned shop type towny plot.
      modify: <red>You didn't have build permission on this towny plot.
      shop-type: <red>You must create shop on a shop type towny plot.
  residence:
    creation-flag-denied: <red>You don't have permission to create shops in this residence.
    trade-flag-denied: <red>You don't have permission to purchase in this residence.
    you-cannot-create-shop-in-wildness: <red>Nie możesz stworzyć sklepu w dzikości.
  griefprevention:
    creation-denied: <red>You don't have permission to create shops in this claim.
    trade-denied: <red>Nie masz zezwolenia na zakupy w tym miejscu.
  lands:
    world-not-enabled: <red>Nie masz pozwolenia, aby tworzyć lub kupować w tym świecie.
    creation-denied: <red>Nie masz uprawnień na tworzenie sklepów w tym miejscu.
  plotsquared:
    no-plot-whitelist-creation: <red>Nie możesz utworzyć sklepu poza działką.
    no-plot-whitelist-trade: <red>Nie możesz robić zakupów poza działka.
    creation-denied: <red>Nie masz uprawnień na tworzenie sklepów w tym miejscu.
    trade-denied: <red>Nie masz uprawnień na zakupy w tym miejscu.
    flag:
      create: Stwórz QuickShop-Hikari shops
      trade: Zakup QuickShop-Hikari shops
  superiorskyblock:
    owner-create-only: <red>Tylko właściciel wyspy może stworzyć tu sklep.
    owner-member-create-only: <red>Tylko właściciel lub członek wyspy może stworzyć tutaj sklep.
  worldguard:
    creation-flag-test-failed: <red>Nie masz uprawnień na stworzenie sklepu w tym miejscu (WorldGuard).
    trade-flag-test-failed: <red>Nie masz uprawnień, aby handlować w tym miejscu (WorldGuard).
    reached-per-region-amount-limit: "<red>You have reached the maximum amount of shops in this region."
  discord:
    __to_translators: 'To translators: `addon.discord.discord-messages` use a special variable in strings, %%LANG_KEY%% instead of QuickShop''s style {LANG_INDEX}, do not translate anything inside double ''%'' section. BUT for any other section texts, it respect the normal QuickShop Text System syntax ({0},{1} etc.) This may be difficult to understand because two different variable forms are used in the same program at the same time, but trust me, it is reasonable. (you don''t need translate this string, it''s a comment)'
    __to_users: Like the QuickShop Text System, Discord addon will also auto-detect user language and use user's language to send Discord message, it following the QuickShop-Hikari's language system settings.
    __to_message_designer: 'Zaprojektuj swoją Discordową wiadomość za pomocą interfejsu GUI: https://glitchii.github.io/embedbuilder/, następnie skopiuj kod JSON i wklej go do tłumaczenia.'
    discord-enabled: <aqua>Pomyślnie <green>włączono</green> wiadomości o QuickShop na discordzie, teraz możesz otrzymywać wiadomości za sklepu na discordzie.
    discord-disabled: <aqua>Pomyślnie <red>wyłączono</red> wiadomości o QuickShop na discordzie, nie będziesz już dostawał wiadomości ze sklepu na discordzie.
    discord-not-integrated: <red>Nie połączyłeś jeszcze swojego konta Discord! Proszę, połącz wpierw swoje konto Discord!
    feature-enabled-for-user: <green>Włączyłeś</green> powiadomienia z <gold>{0}</gold>.
    feature-disabled-for-user: <red>Wyłączyłeś</red> powiadomienia z <gold>{0}</gold>.
    link-help: <yellow>This server using <gold>{0}</gold> for Discord driver, please use <green>{0}</green> to link your Discord account.
    save-notifaction-exception: <red>Wystąpił błąd podczas zapisywania ustawień powiadomień Discordowych, proszę, skontaktuj się z administratorem serwera.
    feature-status-changed: <green>Successfully set the notifaction <aqua>{0}</aqua> status to <gold>{1}</gold>
    commands:
      discord:
        description: <yellow>Zarządzanie twoimi ustawieniami QuickShop na Discordzie
    discord-messages:
      sold-to-your-shop: |
        {
         "embed":
           {
             "title": ":inbox_tray: Somebody sold items to your shop",
             "description": "The player %%purchase.name%% sold x%%purchase.amount%% %%shop.item.name%% to your shop.",
             "color": 52084,
             "author": {
               "name": "QuickShop-Hikari",
               "icon_url": ""
             },
             "thumbnail": {
               "url": ""
             },
             "image": {
               "url": ""
             },
             "footer": {
               "text": "QuickShop-Hikari Discord Notification",
               "icon_url": ""
             },
             "fields": [
               {
                 "name": "Shop",
                 "value": "%%shop.display-name%%",
                 "inline": false
               },
               {
                 "name": "Purchaser",
                 "value": "%%purchase.name%%",
                 "inline": true
               },
               {
                 "name": "Item",
                 "value": "%%shop.item.name%%",
                 "inline": true
               },
               {
                 "name": "Amount",
                 "value": "%%purchase.amount%% (x%%shop.stacking-amount%%)",
                 "inline": true
               },
               {
                 "name": "Your paid",
                 "value": "%%purchase.balance-formatted%%",
                 "inline": true
               },
               {
                 "name": "Taxes",
                 "value": "%%purchase.taxes-formatted%%",
                 "inline": true
               }
             ]
           }
        }
      bought-from-your-shop: |
        {
           "embed":
             {
               "title": ":outbox_tray: Somebody bought items from shop",
               "description": "The player %%purchase.name%% bought x%%purchase.amount%% %%shop.item.name%% from your shop.",
               "color": 52084,
               "author": {
                   "name": "QuickShop-Hikari",
                   "icon_url": ""
               },
               "thumbnail": {
                   "url": ""
               },
               "image": {
                   "url": ""
               },
               "footer": {
                   "text": "QuickShop-Hikari Discord Notification",
                   "icon_url": ""
               },
               "fields": [
                   {
                       "name": "Shop",
                       "value": "%%shop.display-name%%",
                       "inline": false
                   },
                   {
                       "name": "Purchaser",
                       "value": "%%purchase.name%%",
                       "inline": true
                   },
                   {
                       "name": "Item",
                       "value": "%%shop.item.name%%",
                       "inline": true
                   },
                   {
                       "name": "Amount",
                       "value": "%%purchase.amount%%(x%%shop.stacking-amount%%)",
                       "inline": true
                   },
                   {
                       "name": "Your earn",
                       "value": "%%purchase.balance-formatted%%",
                       "inline": true
                   },
                   {
                       "name": "Taxes",
                       "value": "%%purchase.taxes-formatted%%",
                       "inline": true
                   }
               ]
           }
         }
      mod-shop-purchase: |
        {
        "embed":
          {
              "title": ":dollar: Somebody purchased in a shop",
              "description": "The player %%purchase.name%% purchase x%%purchase.amount%% %%shop.item.name%% from the shop.",
              "color": 6061450,
              "author": {
                  "name": "QuickShop-Hikari",
                  "icon_url": ""
              },
              "thumbnail": {
                  "url": ""
              },
              "image": {
                  "url": ""
              },
              "footer": {
                  "text": "QuickShop-Hikari Discord Notification",
                  "icon_url": ""
              },
              "fields": [
                  {
                      "name": "Shop",
                      "value": "%%shop.display-name%%",
                      "inline": false
                  },
                  {
                      "name": "Purchaser",
                      "value": "%%purchase.name%%",
                      "inline": true
                  },
                  {
                      "name": "Item",
                      "value": "%%shop.item.name%%",
                      "inline": true
                  },
                  {
                      "name": "Amount",
                      "value": "%%purchase.amount%% (x%%shop.stacking-amount%%)",
                      "inline": true
                  },
                  {
                      "name": "Balance",
                      "value": "%%purchase.balance-formatted%%",
                      "inline": true
                  },
                  {
                      "name": "Taxes",
                      "value": "%%purchase.taxes-formatted%%",
                      "inline": true
                  }
              ]
          }
        }
      out-of-space: |
        {
        "embed":
          {
              "title": ":mailbox_with_mail: Your shop has run out of space",
              "description": "Your shop inventory is full of items!\nYou need empty your shop container to free the space then you can continue to accept new items.",
              "color": 16065893,
              "author": {
                  "name": "QuickShop-Hikari",
                  "icon_url": ""
              },
              "thumbnail": {
                  "url": ""
              },
              "image": {
                  "url": ""
              },
              "footer": {
                  "text": "QuickShop-Hikari Discord Notification",
                  "icon_url": ""
              },
              "fields": [
                  {
                      "name": "Shop",
                      "value": "%%shop.display-name%%",
                      "inline": false
                  }
              ]
          }
        }
      out-of-stock: |
        {
            "embed":
                {
                    "title": ":mailbox_with_no_mail: Your shop has run out of stock",
                    "description": "Your shop inventory is empty now!\nYou need to refill the shop container with items to continue selling items!",
                    "color": 16065893,
                    "author": {
                        "name": "QuickShop-Hikari",
                        "icon_url": ""
                    },
                    "thumbnail": {
                        "url": ""
                    },
                    "image": {
                        "url": ""
                    },
                    "footer": {
                        "text": "QuickShop-Hikari Discord Notification",
                        "icon_url": ""
                    },
                    "fields": [
                        {
                            "name": "Shop",
                            "value": "%%shop.display-name%%",
                            "inline": false
                        }
                    ]
                }
        }
      mod-shop-created: |
        {
          "embed": {
            "title": ":classical_building: A new shop has been created",
            "description": "Player created a new shop on your server!",
            "color": 6061450,
            "author": {
              "name": "QuickShop-Hikari",
              "icon_url": ""
            },
            "thumbnail": {
              "url": ""
            },
            "image": {
              "url": ""
            },
            "footer": {
              "text": "QuickShop-Hikari Discord Notification",
              "icon_url": ""
            },
            "fields": [
              {
                "name": "Shop",
                "value": "%%shop.display-name%%",
                "inline": false
              },
              {
                "name": "Owner",
                "value": "%%shop.owner.name%%",
                "inline": true
              },
              {
                "name": "Item",
                "value": "%%shop.item.name%%",
                "inline": true
              },
              {
                "name": "Amount",
                "value": "%%shop.stacking-amount%%",
                "inline": true
              },
              {
                "name": "Type",
                "value": "%%shop.type%%",
                "inline": true
              }
            ]
          }
        }
      mod-remove-shop: |
        {
            "embed": {
                "title": ":recycle: A shop was removed from this server",
                "description": "A shop was removed from this server.",
                "color": 6061450,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "Reason",
                        "value": "%%delete.reason%%",
                        "inline": false
                    }
                ]
            }
        }
      shop-transfer-to-you: |
        {
            "embed": {
                "title": ":placard: A shop was transfer to you",
                "description": "A shop transfer to you from another player.",
                "color": 16172079,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "From",
                        "value": "%%transfer.from%%",
                        "inline": false
                    }
                ]
            }
        }
      mod-shop-transfer: |
        {
            "embed": {
                "title": ":placard: A shop was transferred",
                "description": "A shop transfer to a player from another player.",
                "color": 6061450,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "From",
                        "value": "%%transfer.from%%",
                        "inline": true
                    },
                    {
                        "name": "To",
                        "value": "%%transfer.to%%",
                        "inline": true
                    }
                ]
            }
        }
      shop-price-changed: |
        {
            "embed": {
                "title": ":money_with_wings: Your shop price was changed",
                "description": "You or your shop staff changed your shop price.",
                "color": 16172079,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "From",
                        "value": "%%change-price.from%%",
                        "inline": true
                    },
                    {
                        "name": "To",
                        "value": "%%change-price.to%%",
                        "inline": true
                    }
                ]
            }
        }
      mod-shop-price-changed: |
        {
            "embed": {
                "title": ":money_with_wings: A shop price has been changed",
                "description": "A shop changed its price settings.",
                "color": 6061450,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": true
                    },
                    {
                        "name": "Owner",
                        "value": "%%shop.owner.name%%",
                        "inline": true
                    },
                    {
                        "name": "",
                        "value": "",
                        "inline": true
                    },
                    {
                        "name": "From",
                        "value": "%%change-price.from%%",
                        "inline": true
                    },
                    {
                        "name": "To",
                        "value": "%%change-price.to%%",
                        "inline": true
                    }
                ]
            }
        }
      shop-permission-changed: |
        {
            "embed": {
                "title": ":closed_lock_with_key: Your shop permission settings has been changed",
                "description": "One of your shop's permission setting has been changed.",
                "color": 15879747,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "Player",
                        "value": "%%change-permission.player%%",
                        "inline": true
                    },
                    {
                        "name": "Assign to group",
                        "value": "%%change-permission.to-group%%",
                        "inline": true
                    },
                    {
                        "name": "",
                        "value": "",
                        "inline": true
                    },
                    {
                        "name": "Permission granted (Inherit from group)",
                        "value": "```\n%%change-permission.perms-list%%\n```",
                        "inline": false
                    }
                ]
            }
        }
    field:
      player: Gracz
      item: Przedmiot
      amount: Ilość
      balance: Saldo
      balance-after-tax: Saldo (po opodatkowaniu)
      account: Saldo Twojego konta
      taxes: Podatki
      cost: Koszt
  discount:
    commands:
      discount:
        description: <yellow>Zastosuj kod rabatowy lub zarządzaj kodami rabatowymi.
    tab-complete:
      discount:
        general:
          code: <code>
        create:
          rate: |
            Wskazówka polecenia:
            Argument: <rate>
            Opis: Rzeczywisty procent lub pieniądze, które zarobisz
            Wejście '30%' = cena * 0.3
            Wejście '50' = cena - 50
          max-usage: |
            Wskazówka polecenia:
            Argument: [max-usage]
            Opis: Czas, w którym kod może być użyty
            `-1` dla nieograniczonych.
          threshold: |
            Wskazówka polecenia:
            Argument: [threshold]
            Opis: Minimalna cena, dla której kod może być zastosowany
            "-1" dla nieograniczonej
          expired: |
            Wskazówka polecenia
            Argument: [expired]
            Opis: Czas wygaśnięcia kodu.
            `-1` dla nielimitowanego czasu trwania.
            Akceptuj obydwa: czas Zulu oraz znacznik czasu UNIX w sekundach.
            Przykład Zulu: 2022-12-17T10:31:37Z
            Przykład UNIX: 1671273097
    discount-code-already-exists: <red>Sorry, the name of your discount code already in use.
    invalid-discount-code-regex: '<red>The discount code must matches the regex: <yellow>{0}</yellow>'
    invalid-discount-code: <red>This discount code are invalid.
    discount-code-added: <green>Your discount code <yellow>{0}</yellow> has been added into shop <aqua>{1}</aqua>
    discount-code-removed: <green>Your discount code <yellow>{0}</yellow> has been removed from shop <aqua>{1}</aqua>
    invalid-code-type: <red>The code type <yellow>{0}</yellow> are invalid.
    invalid-usage-restriction: <red>The usage restriction <yellow>{0}</yellow> are invalid.
    invalid-threshold-restriction: <red>The threshold restriction <yellow>{0}</yellow> are invalid.
    invalid-effect-scope: <red>The scope <yellow>{0}</yellow> are invalid.
    invalid-expire-time: <red>You can't specify a time in the past.
    invalid-discount-rate: <red>The discount rate <yellow>{0}</yellow> are invalid, it can be a fixed number or percentage.
    discount-code-expired: <red>Ouch! Your discount code <yellow>{0}</yellow> has been expired!
    discount-code-installed: <green>You have installed discount code <gold>{0}</gold>, the discount code will be automatically applied to the available purchase in all the following purchases during this session. To uninstall discount code, execute <aqua>/qs discount uninstall {0}</aqua>.
    discount-code-uninstalled: <green>You have uninstalled your discount code.
    discount-code-query-nothing: <red>You hadn't install a discount code yet!
    discount-code-query: '<green>You are using discount code: <yellow>{0}</yellow>.'
    discount-code-applicable: '<color:#bcef26>Your discount code <yellow><bold>{0}</bold></yellow> are <bold>applicable</bold> in this shop!'
    discount-code-applicable-with-threshold: '<color:#bcef26>Your discount code <yellow><bold>{0}</bold></yellow> are <bold>applicable</bold> in this shop, but only your purchase cost over <yellow>{1}</yellow> in single transaction.'
    discount-code-not-applicable: <red>Your discount code <yellow><bold>{0}</bold></yellow> are <bold>not applicable</bold> in this shop!
    discount-code-reach-the-limit: <red>You have reach the usage limit of discount code <yellow><bold>{0}</bold></yellow>, discount are not applied.
    discount-code-no-permission: <red>You didn't have permission to use any discount code in this shop!
    discount-code-has-been-expired: <red>Your discount has been expired!
    discount-code-config-shop-added: <green>Successfully to add this shop into your discount code allow list.
    discount-code-config-shop-add-failure: <red>This shop already in your discount code allow list.
    discount-code-config-shop-removed: <green>Successfully to remove this shop from your discount code allow list.
    discount-code-config-shop-remove-failure: <red>This shop not exists in your discount code allow list.
    discount-code-config-expire: <green>Successfully changed your code expire time.
    discount-code-config-applied: <green>Config the discount code successfully!
    discount-code-created-successfully: |
      <green>Your discount code <yellow>{0}</yellow> has been successfully created!
      <gold>Scope: <aqua>{1}</aqua></gold>.
      <gold>Share with others: <color:#bcef26>{2}</color:#bcef26></gold>.
      <yellow>For <gray>Specific Shops</gray> scope only: To add shops to your discount code allow list, please looking an quickshop and execute <aqua>{3}</aqua> command.
      <yellow>You can use <aqua>/qs discount config {0}</aqua> to edit your discount code anytime.
    discount-code-under-threshold: <red>The discount not applied to your purchase because the total value under the discount code threshold <yellow>{0}</yellow>.
    percentage-off: '<bold><color:#bcef26>-{0}%</color:#bcef26></bold>'
    fixed-off: '<bold><color:#bcef26>-{0}</color:#bcef26></bold>'
    discount-code-list: '<gold>Listing your discount codes:</gold>'
    discount-code-applied-in-purchase: '<color:#bcef26>Your discount code <yellow>{0}</yellow> has been applied to this purchase and you saved <gold>{1}</gold>!'
    scope:
      this-shop: '{0}'
      your-shops-owned: all your shops (owned)
      your-shops-managed: wszystkie Twoje sklepy (zarządzane)
      server: cały serwer
    code-type:
      SERVER_ALL_SHOPS: Wszystkie sklepy na tym serwerze
      PLAYER_ALL_SHOPS: Wszystkie sklepy, które zostały utworzone przez właściciela kodu
      SPECIFIC_SHOPS: Konkretne sklepy
    discount-code-details: |-
      <gold>Kod: <yellow>{0}</yellow>
      <gold>Twórca: <yellow>{1}</yellow>
      <gold>Zastosowano dla: <yellow>{2}</yellow>
      <gold>Pozostałe użycia: <yellow>{3}</yellow>
      <gold>Wygaśnie: <yellow>{4}</yellow>
      <gold>Próg: <yellow>{5}</yellow>
      <gold>Rabat: <yellow>{6}</yellow>
  list:
    commands:
      list: <yellow>Listowanie wszystkich sklepów należących do Ciebie lub konkretnego gracza
    table-prefix: '<yellow><green>{0}</green>''s shops <gray>(Total {1})</gray>: '
    table-prefix-pageable: '<yellow><green>{0}</green>''s shops <gray>(Page {1}/{2})</gray>: '
    entry: "<hover:show_text:'<yellow>{1}</yellow>\n<gray>{2} {3},{4},{5}</gray>\n<aqua>Price <yellow>{6}</yellow> - <light_purple>x{7}</light_purple> <yellow>{8}</yellow>\n<green>{9}'><yellow>{0}. <aqua>{1}</aqua></hover>"
  shopitemonly:
    message: <red>You can't put non-shop items into shop container, all non-shop items will dropped at your location.
  limited:
    commands:
      limit: <yellow>Set a limit that restrict the purchase that player can make in period
    titles:
      title: <green>Zakup udany
      subtitle: <aqua>Możesz również kupić <gold>{0}</gold> więcej w tym sklepie
    reach-the-limit: <red>You have reached the limit in this shop. You can also purchase <green>{0}</green> items, but you trying purchase <yellow>{1}</yellow> items.
    success-reset: <green>Reset the restriction of this shop successfully
    success-remove: <green>Successfully remove the all restriction of this shop
    success-setup: <green>Restriction settings for this shop were saved successfully
    trade-limit-reached-cancel-reason: <red>Reached the restriction of this shop
    remains-limits: '<gold>Your remaining purchase quota in this shop: <yellow>{0}</yellow>'
    remains-limits-before-reset-period-note: '<gold>This shop will reset their purchase quota every: <yellow>{0}</yellow>'
  dynmap:
    markerset-title: QuickShop-Hikari Shops
    marker-name: '{0} @ {2} @ {3} for x{4} @ {5}'
    marker-description: |
      Name: {0}
      Owner: {1}
      Item: {2}
      Price: {3} for x{4} item(s)
      Type: {5}
      Unlimited: {6}
      Location: {7}
  bluemap:
    markerset-title: QuickShop-Hikari Shops
    marker-name: '{0} @ {2} @ {3} for x{4} @ {5}'
    marker-description: |
      Name: {0}
      Owner: {1}
      Item: {2}
      Price: {3} for x{4} item(s)
      Type: {5}
      Unlimited: {6}
      Location: {7}
  chestprotect:
    protection-exists: <red>This region already protected by ChestProtect and you don't have permission to create shops at here.
    shops-exsts: <red>The region that you want to protect contains other player's shops and you don't have permission to access them.
  displaycontrol:
    toggle: |-
      <green>Successfully toggle your QuickShop display showcasing to <aqua>{0}</aqua>.
      <yellow>You may need re-join to take effect.
    toggle-exception: <red>Failed to toggle your display showcase settings due an internal error, please contact server administrator.
    command:
      displaycontrol: <yellow>Toggle your QuickShop display showcasing
  reremake-migrator:
    commands:
      migratefromreremake: Migrate QuickShop-Reremake's data to QuickShop-Hikari
    server-not-empty: "<red>No players are allowed to be online on the server during the conversion progress, please enable server whitelist or maintenance mode."
    starting-convert-progress: "<gold>Starting the convert progress, <red>DO NOT SHUTDOWN YOUR SERVER!</red>"
    executing: "<gold>Executing migrate component <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
    completed: "<green>Done! Migrate completed, please restart your server and remove QuickShop-Reremake from plugins directory."
    join_blocking_converting: "<red>[QuickShop-Hikari Migrator] This server is performing a data conversion and you cannot join the server at this time, try again later!"
    join_blocking_finished: "<red>[QuickShop-Hikari Migrator] This server has just completed a data conversion and is waiting for a reboot to apply the changes, try again later!"
    failed: "<red>Migration progress exited with error, please check the server console."
    modules:
      config:
        copy-values: "<yellow>Copying values (total {0} entries)..."
        copying-value: "<gray> - Copying <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        migrate-price-restriction: "<yellow>Migrating price-restriction related settings..."
        migrate-price-restriction-entry: "<gray> - Migrating <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
      shop:
        start-migrate: "<yellow>Migrating shops (total {0} entries)..."
        migrate-entry: "<gray> - Migrating <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        unloading-reremake: "<gold>Unloading Reremake to avoid data overwrite..."
        register-entry: "<gray> - Registering <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        save-entry: "<gray> - Saving <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        saving-shops: "<yellow>Saving <gold>{0}</gold> shops, this may need a while™ (the time based on shops amounts)..."
        conflict: "<gray> - CONFLICT > Detects a conflict between a Reremake store and an existing Hikari store location, taking a predefined behavior: <dark_gray>{0}</dark_gray>"
      translation:
        start-migrate: "<yellow>Migrating translation files...</yellow>"
        copy-values: "<yellow>Copying values in locale <gold>{0}</gold> (total {1} entries)..."
        migrate-entry: "<gray> - Migrating <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        copying-value: "<gray> - Copying <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
      shop-logs:
        start-migrate: "<yellow>Migrating shop logs (details in console), please wait...</yellow>"
        extract-history-files: "<gray>Please wait for us to unzip and append the history logs..."
        filter-history-files: "<gray>Please wait for us to filter the history log..."
        filtered-history-files: "<gray>Filtered {0} lines out of queue."
        import-entry: "<gray> - Migrating <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
compat:
  advancedchests:
    created: <green>You're successfully created a shop on AdvancedChests container!
    permission-denied: <red>Sorry! You don't have the permission to create a shop on a AdvancedChests container!
suite:
  dynmap:
    title: '{0} - {1} - {2}'
    description: |-
      Item: {0}
      Owner: {1}
      Type: {2} {3}
      Price: {4}
      Location: {5} {6}, {7}, {8}
      Space: {9}
      Stock: {10}
  limited:
    command-description: <yellow>Set a limit that restrict the purchase that player can make in period.
    reach-the-quota-limit: <red>You have reach the purchase limit in this shop ({0}/{1}).
    quota-reset-countdown: <yellow>The quota in this shop will reset in {0}.
    quota-reset-player-successfully: <green>The quota for player {0} in this shop has been reset successfully.
    quota-reset-everybody-successfully: <green>The quota for everyone in this shop has been reset successfully.
    quota-setup: <green>The purchase restriction now apply to this shop!
    quota-remove: <green>The purchase restriction now removed from this shop!
    subtitles:
      title: <green>Purchase Success
      subtitle: <aqua>You can purchase <yellow>{0}</yellow> more in this shop
  list:
    command-description: <yellow>List all shops owned by yourself or another player.
    table-prefix: <yellow>You owned total <aqua>{0}</aqua> shops on this server.
    entry-syntax: <aqua>- <yellow>{0}
    entry-syntax-fallback-name: '<yellow>Item:{0} X:{1}, Y:{2}, Z:{3}, World: {4}'
    hover-text: |-
      <yellow>{0}
      <gray>{1} {2}, {3}, {4}
      <aqua>{5} - <yellow>{6}
      <yellow>{7} items each stack.
  shopitemonly:
    message: <red>You can't put non-shop items into shop container, all non-shop items will dropped at your location.
compatibility:
  elitemobs:
    soulbound-disallowed: You can't trade item which have EliteMobs Soulbound enchantment.
internet-paste-forbidden-privacy-reason: "<red>Failed! According your privacy setting, QuickShop-Hikari are not allowed upload your paste to Internet, turn on DIAGNOSTIC permission in privacy setting in config.yml or use <aqua>/quickshop paste --file</aqua> instead."
no-sign-type-given: "<red>You need give a sign material, the sign materials that available on this server: {0}"
sign-type-invalid: "<red>The type <yellow>{0}</yellow> not a valid sign material."
delete-controlpanel-button-confirm: "<red>Czy na pewno chcesz usunąć ten sklep? Kliknij przycisk <bold>[Usuń Sklep]</bold> ponownie w {0} sekund, aby potwierdzić."
cannot-suggest-price: "<red>Sorry, For the time being, no more people are trading the same item as you and there is not enough data to generate a suggested price."
price-suggest: "<green>Based on data from <aqua>{0}</aqua> stores, the highest priced store is priced at <light_purple>{1}</light_purple>, the lowest priced store is priced at <light_purple>{2}</light_purple>, the average price is <light_purple>{3}</light_purple>, and the median price is <light_purple>{4}</light_purple>. <newline><yellow>It's recommended that you set your price around <gold>{5}</gold>.</yellow>"
suggest-wait: "<green>Please wait... Calculating recommend price."
history:
  shop:
    gui-title: "View purchase history"
    header-icon-multiple-shop: "<white>The query result across {0} shops</white>"
    header-icon-description:
      - "<white>Type: <yellow>{0}</yellow></white>"
      - "<white>Owner: <yellow>{1}</yellow></white>"
      - "<white>Item: <yellow>{2}</yellow></white>"
      - "<white>Price: <yellow>{3} <aqua>x{4}</aqua></yellow></white>"
      - "<white>Location: <yellow>{5}</yellow></white>"
    log-icon-title: "<reset><green>Time: {0}</green>"
    log-icon-description:
      - "<white>Purchaser: <yellow>{0}</yellow></white>"
      - "<white>Item: <yellow>{1} <aqua>x{2}</aqua></yellow></white>"
      - "<white>Balance: <yellow>{3}</yellow></white>"
      - "<white>Tax: <yellow>{4}</yellow></white>"
    log-icon-description-with-store-name:
      - "<white>Store: <yellow>{0}</yellow></white>"
      - "<white>Purchaser: <yellow>{1}</yellow></white>"
      - "<white>Item: <yellow>{2} <aqua>x{3}</aqua></yellow></white>"
      - "<white>Balance: <yellow>{4}</yellow></white>"
      - "<white>Tax: <yellow>{5}</yellow></white>"
    query-icon: "<gray>Please wait, querying...</gray>"
    previous-page: "<white><< Previous Page</white>"
    next-page: "<white>Next Page >></white>"
    current-page: "<white>Page {0}</white>"
    summary-icon-title: "<green>Shop Summary"
    recent-purchases: "<white>Recent <aqua>{0}</aqua> purchases: <yellow>{1}</yellow></white>"
    recent-purchase-balance: "<white>Recent <aqua>{0}</aqua> turnover: <yellow>{1}</yellow></white>"
    total-purchases: "<white>Total purchases: <yellow>{0}</yellow></white>"
    total-balances: "<white>Total turnover: <yellow>{0}</yellow></white>"
    total-unique-purchasers: "<white>Total unique purchasers: <yellow>{0}</yellow></white>"
    top-n-valuable-customers-title: "<gold>Top {0} valuable customers</gold>"
    top-n-valuable-customers-entry: "<white>- <yellow>{0}</yellow> <aqua>{1}</aqua></white>"
    no-result: "<white>No result</white>"
about:
  text:
    - "<b><aqua>QuickShop <yellow>{0}</yellow></aqua></b>"
    - "<aqua>Version <yellow>>></yellow> <green>{1}</green></aqua>"
    - "<aqua>Release <yellow>>></yellow> <green>{2}</green></aqua>"
    - "<aqua>Developers <yellow>>></yellow> <color:#218bff><hover:show_text:'<yellow>Click to check contributors'><click:open_url:'https://github.com/Ghost-chu/QuickShop-Hikari/graphs/contributors'>[View contributors on GitHub]</click></hover></color></aqua>"
    - "<aqua>Localized members <gold>({4})</gold>: <yellow>>></yellow> <green>{5}</green></aqua>"
    - "  <yellow>+ <color:#43A047><hover:show_text:'<yellow>Click to open Crowdin translate page'><click:open_url:'https://crowdin.com/project/quickshop-hikari'>[Help translate on Crowdin]</click></hover></color></yellow>"
    - "<aqua>Donation Key <yellow>>></yellow> <green>{6}</green></aqua>"
    - "<gold>Powered by community</gold> <red>Made with ❤</red>"
  valid-donation-key: "<color:#00AFF1>Bound to <color:#F9C23C>{0}</color></color>"
  invalid-donation-key: "<gray>Invalid donation key</gra>"
  kofi-thanks: "<gold>Special thanks to those who support QuickShop on Ko-fi :)</gold>"
history-command-leave-blank: "<Leave blank for seeing store>"
shop-information-not-shown-due-an-internal-error: "<red>An internal error occurred. The store information panel may be displayed incompletely, please contact the server administrator."
