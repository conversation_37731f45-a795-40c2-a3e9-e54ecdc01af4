from pydantic_settings import BaseSettings
from typing import Optional

class Settings(BaseSettings):
    # 应用配置
    app_name: str = "MC Web Manager"
    debug: bool = False
    host: str = "127.0.0.1"
    port: int = 8000
    
    # 安全配置
    secret_key: str = "your-secret-key-change-this-in-production"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    
    # MC服务器配置
    mc_server_path: str = "./server"
    mc_server_jar: str = "paper-1.21.8-11.jar"
    mc_rcon_host: str = "localhost"
    mc_rcon_port: int = 25575
    mc_rcon_password: str = "your-rcon-password"
    
    # 数据库配置
    database_url: str = "sqlite+aiosqlite:///./mc_manager.db"
    
    class Config:
        env_file = ".env"

settings = Settings()