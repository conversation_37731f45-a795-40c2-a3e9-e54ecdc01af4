"""
MC Web Manager - 配置设置
应用程序的配置参数和环境变量管理
"""

import os
from pathlib import Path
from typing import Optional

# 项目根目录
BASE_DIR = Path(__file__).resolve().parent.parent.parent

# MC服务器相关配置
class MCServerConfig:
    """Minecraft服务器配置"""
    # 服务器目录路径（相对于项目根目录）
    SERVER_DIR = BASE_DIR.parent / "server"

    # 服务器启动脚本/JAR文件
    SERVER_JAR = "paper-1.21.8-11.jar"
    SERVER_START_SCRIPT = "点击这里开服.bat"

    # 服务器进程识别关键字
    PROCESS_KEYWORDS = ["java", "paper", "minecraft"]

    # RCON配置（暂时未使用，为后续扩展预留）
    RCON_HOST = "localhost"
    RCON_PORT = 25575
    RCON_PASSWORD = ""

# JWT认证配置
class AuthConfig:
    """认证系统配置"""
    # JWT密钥（生产环境应使用环境变量）
    SECRET_KEY = os.getenv("JWT_SECRET_KEY", "your-secret-key-change-in-production")
    ALGORITHM = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES = 30

    # 硬编码管理员账户（MVP版本）
    ADMIN_USERNAME = "admin"
    ADMIN_PASSWORD = "admin123"  # 生产环境应使用哈希密码

# 应用配置
class AppConfig:
    """应用程序配置"""
    # 应用信息
    APP_NAME = "MC Web Manager"
    VERSION = "1.0.0"
    DEBUG = os.getenv("DEBUG", "true").lower() == "true"

    # 服务器配置
    HOST = os.getenv("HOST", "0.0.0.0")
    PORT = int(os.getenv("PORT", 8000))

    # 日志配置
    LOG_LEVEL = os.getenv("LOG_LEVEL", "info")

# 导出配置实例
mc_config = MCServerConfig()
auth_config = AuthConfig()
app_config = AppConfig()