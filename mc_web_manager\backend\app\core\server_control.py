"""
MC Web Manager - 服务器控制模块
Minecraft服务器进程管理和状态监控
"""

import os
import psutil
import subprocess
import signal
from pathlib import Path
from typing import Optional, Dict, Any
from datetime import datetime
import logging

# 导入配置
import sys
from pathlib import Path

# 添加backend目录到Python路径
backend_dir = Path(__file__).parent.parent.parent
if str(backend_dir) not in sys.path:
    sys.path.insert(0, str(backend_dir))

from config.settings import mc_config

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MCServerController:
    """Minecraft服务器控制器"""
    
    def __init__(self):
        self.server_dir = Path(mc_config.SERVER_DIR)
        self.server_jar = mc_config.SERVER_JAR
        self.start_script = mc_config.SERVER_START_SCRIPT
        self.process_keywords = mc_config.PROCESS_KEYWORDS
        
    def get_server_status(self) -> Dict[str, Any]:
        """获取服务器状态信息"""
        try:
            process = self._find_server_process()
            if process:
                # 服务器正在运行
                return {
                    "status": "running",
                    "pid": process.pid,
                    "cpu_percent": process.cpu_percent(),
                    "memory_info": {
                        "rss": process.memory_info().rss,
                        "vms": process.memory_info().vms
                    },
                    "create_time": datetime.fromtimestamp(process.create_time()).isoformat(),
                    "uptime_seconds": int(datetime.now().timestamp() - process.create_time())
                }
            else:
                # 服务器未运行
                return {
                    "status": "stopped",
                    "pid": None,
                    "cpu_percent": 0,
                    "memory_info": {"rss": 0, "vms": 0},
                    "create_time": None,
                    "uptime_seconds": 0
                }
        except Exception as e:
            logger.error(f"获取服务器状态时出错: {e}")
            return {
                "status": "error",
                "error": str(e),
                "pid": None,
                "cpu_percent": 0,
                "memory_info": {"rss": 0, "vms": 0},
                "create_time": None,
                "uptime_seconds": 0
            }
    
    def _find_server_process(self) -> Optional[psutil.Process]:
        """查找Minecraft服务器进程"""
        try:
            for process in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    cmdline = process.info['cmdline']
                    if cmdline:
                        cmdline_str = ' '.join(cmdline).lower()
                        # 检查是否包含关键字
                        if any(keyword.lower() in cmdline_str for keyword in self.process_keywords):
                            # 进一步验证是否是我们的服务器
                            if self.server_jar.lower() in cmdline_str:
                                return process
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    continue
            return None
        except Exception as e:
            logger.error(f"查找服务器进程时出错: {e}")
            return None
    
    def start_server(self) -> Dict[str, Any]:
        """启动Minecraft服务器"""
        try:
            # 检查服务器是否已经在运行
            if self._find_server_process():
                return {
                    "success": False,
                    "message": "服务器已经在运行中",
                    "status": "already_running"
                }
            
            # 检查服务器目录是否存在
            if not self.server_dir.exists():
                return {
                    "success": False,
                    "message": f"服务器目录不存在: {self.server_dir}",
                    "status": "directory_not_found"
                }
            
            # 优先使用启动脚本
            start_script_path = self.server_dir / self.start_script
            jar_path = self.server_dir / self.server_jar
            
            if start_script_path.exists():
                # 使用启动脚本
                logger.info(f"使用启动脚本启动服务器: {start_script_path}")
                process = subprocess.Popen(
                    [str(start_script_path)],
                    cwd=str(self.server_dir),
                    creationflags=subprocess.CREATE_NEW_CONSOLE if os.name == 'nt' else 0
                )
            elif jar_path.exists():
                # 直接使用JAR文件启动
                logger.info(f"直接启动JAR文件: {jar_path}")
                java_cmd = [
                    "java",
                    "-Xmx2G",  # 最大内存2GB
                    "-Xms1G",  # 初始内存1GB
                    "-jar",
                    str(jar_path),
                    "nogui"
                ]
                process = subprocess.Popen(
                    java_cmd,
                    cwd=str(self.server_dir),
                    creationflags=subprocess.CREATE_NEW_CONSOLE if os.name == 'nt' else 0
                )
            else:
                return {
                    "success": False,
                    "message": f"找不到启动脚本或JAR文件",
                    "status": "files_not_found"
                }
            
            return {
                "success": True,
                "message": "服务器启动命令已执行",
                "status": "starting",
                "pid": process.pid
            }
            
        except Exception as e:
            logger.error(f"启动服务器时出错: {e}")
            return {
                "success": False,
                "message": f"启动服务器时出错: {str(e)}",
                "status": "error"
            }
    
    def stop_server(self) -> Dict[str, Any]:
        """停止Minecraft服务器"""
        try:
            process = self._find_server_process()
            if not process:
                return {
                    "success": False,
                    "message": "服务器未在运行",
                    "status": "not_running"
                }
            
            # 尝试优雅关闭
            logger.info(f"正在停止服务器进程 PID: {process.pid}")
            
            if os.name == 'nt':  # Windows
                process.terminate()
            else:  # Unix/Linux
                process.send_signal(signal.SIGTERM)
            
            # 等待进程结束
            try:
                process.wait(timeout=30)  # 等待30秒
                return {
                    "success": True,
                    "message": "服务器已成功停止",
                    "status": "stopped"
                }
            except psutil.TimeoutExpired:
                # 强制终止
                process.kill()
                return {
                    "success": True,
                    "message": "服务器已强制停止",
                    "status": "force_stopped"
                }
                
        except Exception as e:
            logger.error(f"停止服务器时出错: {e}")
            return {
                "success": False,
                "message": f"停止服务器时出错: {str(e)}",
                "status": "error"
            }
    
    def restart_server(self) -> Dict[str, Any]:
        """重启Minecraft服务器"""
        try:
            # 先停止服务器
            stop_result = self.stop_server()
            if not stop_result["success"] and stop_result["status"] != "not_running":
                return stop_result
            
            # 等待一段时间确保进程完全结束
            import time
            time.sleep(3)
            
            # 启动服务器
            start_result = self.start_server()
            
            if start_result["success"]:
                return {
                    "success": True,
                    "message": "服务器重启成功",
                    "status": "restarted"
                }
            else:
                return {
                    "success": False,
                    "message": f"重启失败: {start_result['message']}",
                    "status": "restart_failed"
                }
                
        except Exception as e:
            logger.error(f"重启服务器时出错: {e}")
            return {
                "success": False,
                "message": f"重启服务器时出错: {str(e)}",
                "status": "error"
            }

# 创建全局服务器控制器实例
server_controller = MCServerController()
