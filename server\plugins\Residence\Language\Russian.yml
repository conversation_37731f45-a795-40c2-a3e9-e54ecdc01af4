# of this file and modify that instead. This file will be updated automatically by Residence
# when a newer version is detected, and your changes will be overwritten.  Once you 
# have a copy of this file, change the Language: option under the Residence config.yml
# to whatever you named your copy.

Language:
  Invalid:
    Player: '&cНеверное имя игрока...'
    World: '&cНеверный мир...'
    Residence: '&cРезиденций не найдено...'
    Subzone: '&cНеверная подзона...'
    Direction: '&cНеверное направление...'
    Amount: '&cНедопустимая сумма...'
    Cost: '&cНеверная стоимость...'
    Days: '&cНеверное количество дней...'
    Material: '&cНеверный материал...'
    Boolean: '&cНеверное значение, должно быть &6true(t) &cили &6false(f)'
    Area: '&cНеверный район...'
    Group: '&cНеверная группа...'
    MessageType: '&cТип сообщения должен быть введен или удален.'
    Flag: '&cНеверный флаг...'
    FlagState: '&cНеверное состояние флага, должно быть &6true(t)&c, &6false(f)&c,
      или &6remove(r)'
    List: '&eНеизвестный тип списка, должен быть &6blacklist &eили &6ignorelist.'
    Page: '&eНеверная страница...'
    Help: '&cНеверная страница помощи...'
    NameCharacters: '&cИмя содержит недопустимые символы...'
    PlayerOffline: '&cИгрок не в сети'
    FlagType:
      Fail: '&cНверный флаг... Этот флаг можно использовать только над %1'
      Player: игроком
      Residence: резиденцией
    PortalDestination: '&cТочка выхода портала выходит на запретную территорию. Создание портала отменено. &7Найдите новое расположение'
    FromConsole: '&cВы можете использовать это только в консоли!'
    Ingame: '&cВы можете использовать это только в игре!'
    Location: '&cНверное расположение...'
  Area:
    Exists: '&cНазвание области уже существует.'
    Create: '&eРезиденция создана, ID &6%1'
    DiffWorld: '&cРайон находится в другом мире от резиденции'
    Collision: '&cОбласть пересекается с резиденцией &6%1'
    SubzoneCollision: '&cОбласть пересекается с подзоной &6%1'
    NonExist: '&cТакой области не существует.'
    InvalidName: '&cНеверное название области...'
    ToSmallX: '&cВаша длина выделения &6X &c(&6%1&c) слишком мала. &eРазрешается &6%2
      &eи больше.'
    ToSmallY: '&cВаша высота выделения (&6%1&c) слишком мала. &eРазрешается &6%2 &eи
      больше.'
    ToSmallZ: '&cВаша длина выделения &6Z (&6%1&c) слишком мала. &eРазрешается &6%2 &eи
      больше.'
    ToBigX: '&cВаша длина выделения &6X (&6%1&c) слишком большая. &eРазрешается &6%2
      &eи меньше.'
    ToBigY: '&cВаша высота выделения (&6%1&c) слишком большая. &eРазрешается &6%2 &eи
      меньше.'
    ToBigZ: '&cВаша длина выделения &6Z (&6%1&c) слишком большая. &eРазрешается &6%2
      &eи меньше.'
    Rename: '&eПереименована область &6%1 &eв &6%2'
    Remove: '&eУдалена область &6%1...'
    Name: '&eНазвание: &2%1'
    RemoveLast: '&cНевозможно удалить последнюю область в резиденции.'
    NotWithinParent: '&cОбласть не в пределах родительской области.'
    Update: '&eОбновление области...'
    MaxPhysical: '&eВы достигли максимально допустимых физических областей для резиденции.'
    SizeLimit: '&eРазмер области не находится в ваших допустимых пределах.'
    HighLimit: '&cВы не можете защитить так высоко, ваш предел &6%1'
    LowLimit: '&cВы не можете защитить так глубоко, ваш предел &6%1'
    TooClose: '&cСлишком близко к другой резиденции. Вам нужно отойти хотя бы на &e%1 &cблоков.'
    ListAll: '&a{&eID:&c%1 &eP1:&c(%2,%3,%4) &eP2:&c(%5,%6,%7) &e(Размер:&c%8&e)&a}'
    WeirdShape: '&3Резиденция имеет неуместную форму контуров. &6%1 &3сторона &3в &6%2 &3раз
      больше стороны &6%3'
  Select:
    Points: '&eСначала выберите две точки перед использованием этой команды!'
    Overlap: '&cВыбранные точки пересекается с регионом &6%1 &c!'
    WorldGuardOverlap: '&cВыбранные точки пересекается с WorldGuard регионом &6%1
      &c!'
    KingdomsOverlap: '&cВыбранные точки пересекается с Kingdoms землей &6%1 &c!'
    Success: '&eВыбор успешен!'
    Fail: '&cНеверная команда выделения...'
    Bedrock: '&eВыбор расширен до минимально допустимого.'
    Sky: '&eВыбор расширен до максимально допустимого предела.'
    Area: '&eВыбрана область &6%1 &eрезиденции &6%2'
    Tool: '&e- Инструмент выделения: &6%1'
    PrimaryPoint: '&eВыделена &6Первая &eточка выделения %1'
    SecondaryPoint: '&eВыделена &6Вторая &eточка выделения %1'
    Primary: '&eПервая точка: &6%1'
    Secondary: '&eВторая точка: &6%1'
    TooHigh: '&cВнимание, выбор был выше вершины карты, ограничение.'
    TooLow: '&cВнимание, выбор был ниже нижней части карты, ограничение.'
    TotalSize: '&eОбщий размер выделения: &6%1'
    AutoEnabled: '&eРежим автоматического выделения  &6ВКЛ&e. Чтобы отключить &6/res
      select auto'
    AutoDisabled: '&eРежим автоматического выделения  &6ВЫКЛ&e. Чтобы включить &6/res
      select auto'
    Disabled: '&cУ вас нет доступа к командам выделения'
  Sign:
    Updated: '&6%1 &eтабличка обновлена!'
    TopLine: '[market]'
    DateFormat: YY/MM/dd HH:mm
    ForRentTopLine: '&8Аренда'
    ForRentPriceLine: '&8%1&f/&8%2&f/&8%3'
    ForRentResName: '&8%1'
    ForRentBottomLine: '&9Доступно'
    RentedAutorenewTrue: '&2%1'
    RentedAutorenewFalse: '&c%1'
    RentedTopLine: '%1'
    RentedPriceLine: '&8%1&f/&8%2&f/&8%3'
    RentedResName: '&8%1'
    RentedBottomLine: '&1%1'
    ForSaleTopLine: '&8Продажа'
    ForSalePriceLine: '&8%1'
    ForSaleResName: '&8%1'
    ForSaleBottom: '&5%1m³'
    LookAt: '&cНаведите курсор на табличку'
    ResName: '&8%1'
    Owner: '&5%1'
    TooMany: '&cСлишком много табилчек для одной резиденции'
  Siege:
    Started: '&7Осада началась!'
    noSelf: '&cНе могу осадить свою резиденцию!'
    isOffline: '&cНе могу осадить, пока владелец не в сети!'
    cooldown: '&cСлишком быстро для очередной осады этой резиденции! Подождите %1'
  info:
    years: '&e%1 &6лет '
    oneYear: '&e%1 &6год '
    day: '&e%1 &6дней '
    oneDay: '&e%1 &6день '
    hour: '&e%1 &6часов '
    oneHour: '&e%1 &6час '
    min: '&e%1 &6мин '
    sec: '&e%1 &6сек '
    click: '&7Клик'
    listSplitter: ', '
    clickToConfirm: '&7Нажмите, чтобы подтвердить'
  server:
    land: Server_Land
  Flag:
    ownColor: '&4'
    p1Color: '&2'
    p2Color: '&a'
    haveColor: '&2'
    havePrefix: ''
    denyColor: '&4'
    denyPrefix: ''
    Set: '&eФлаг (&6%1&e) установлен для &6%2 &eна статус &6%3'
    SetFailed: '&cУ вас нет доступа к флагу &6%1'
    CheckTrue: '&eФлаг &6%1 &eотносится к игроку &6%2 &eв резиденции &6%3&e, значение
      = &6%4'
    CheckFalse: '&eФлаг &6%1 &eне относится к игроку &6%2 &eдля резиденции.'
    Cleared: '&eФлаги очищены.'
    RemovedAll: '&eВсе флаги удалены &6%1 &eв &6%2 &eрезиденции.'
    RemovedGroup: '&eВсе флаги удалены для группы &6%1 &eв резиденции &6%2 &e.'
    Default: '&eФлаги установлены по умолчанию.'
    Deny: '&cУ вас нет здесь разрешения &6%1&c.'
    SetDeny: '&cВладелец не имеет доступа к флагу &6%1'
    ChangeDeny: '&cВы не можете изменить состояние флага &6%1 &c, пока внутри находятся
      &6%2 &cигрок(ов).'
    ChangedForOne: '&eУстановлен флаг &6%1 &eдля резиденции &6%2'
    ChangedFor: '&e&eУстановлен флаг &6%1 &eдля резиденции &6%2'
    reset: '&eУдален флаг для резиденции &6%1'
    resetAll: '&eУдален флаг для &6%1 &eрезиденций'
    lackColor: '&7'
    lackPrefix: ''
    others: '&eи &2%1 &eдругих'
  Bank:
    NoAccess: '&cУ вас нет доступа к банку.'
    Name: ' &eБанк: &6%1'
    NoMoney: '&cНедостаточно денег в банке.'
    Deposit: '&eВы внесли &6%1 &eв банк резиденции.'
    Withdraw: '&eВы сняли &6%1 &eиз банка резиденции.'
    rentedWithdraw: '&cС банка арендуемой резиденции нельзя снять деньги.'
    full: '&eБанк резиденции полон!'
  Subzone:
    Rename: '&eПереименована подзона &6%1 &eв &6%2'
    Remove: '&eПодзона &6%1 &eудалена.'
    Create: '&eСоздана подзона &6%1'
    CreateFail: '&cНевозможно создать подзону &6%1'
    Exists: '&cПодзона &6%1 &cуже существует.'
    Collide: '&cПодзона пересекается с подзоной &6%1'
    MaxAmount: '&cВы достигли максимально допустимой суммы подзоны для этой резиденции.'
    MaxDepth: '&cВы достигли максимально допустимой глубины подзоны.'
    SelectInside: '&eОбе точки выделения должны быть внутри резиденции.'
    CantCreate: '&cУ вас нет разрешения на создание подзоны резиденции.'
    CantDelete: '&cУ вас нет разрешения на удаление подзоны резиденции.'
    CantDeleteNotOwnerOfParent: '&cВы не являетесь владельцем родительской резиденции
      для удаления этой подзоны.'
    CantContract: '&cВы не имеете разрешений на контракт подзон резиденции.'
    CantExpand: '&cУ вас нет разрешения на расширение подзоны резиденции.'
    DeleteConfirm: '&eВы уверены, что хотите удалить подзону &6%1&e? Пишите &6/res
      confirm &eдля подтверждения.'
    OwnerChange: '&eУ подзоны &6%1 &eвладелец изменен на &6%2'
  Residence:
    DontOwn: '&eНечего показать'
    Hidden: ' &e(&6Скрыты&e)'
    Bought: '&eВы купили резиденцию &6%1'
    Buy: '&6%1 &eкупил у вас резиденцию &6%2 &e.'
    BuyTooBig: '&cЭта резиденция имеет площадь, превышающую максимально допустимую.'
    NotForSale: '&cРезиденция не продается.'
    ForSale: '&eРезиденция &6%1 &eпродается за &6%2'
    StopSelling: '&cРезиденция больше не продается.'
    TooMany: '&cВы уже владеете максимальным количеством резиденций, которые вам разрешено.'
    MaxRent: '&cВы уже арендуете максимальное количество резиденций, которое вам разрешено.'
    AlreadyRent: '&cРезиденция уже сдана в аренду...'
    NotForRent: '&cРезиденция не сдается...'
    NotForRentOrSell: '&cРезиденция не для аренды или продажи...'
    NotRented: '&cРезиденция не сдана в аренду.'
    Unrent: '&eРезиденция &6%1 &eне сдана'
    RemoveRentable: '&eРезиденция &6%1 &eбольше не сдается в аренду.'
    ForRentSuccess: '&eРезиденция &6%1 &eсдается за &6%2 &eкаждые &6%3 &eдней.'
    RentSuccess: '&eВы арендовали Резиденцию &6%1 &eна &6%2 &eдней.'
    EndingRent: '&eВремя аренды заканчивается для &6%1 &eчерез &6%2'
    AlreadyRented: '&eРезиденция &6%1 &eсдана в аренду &6%2'
    CantAutoPay: '&eРезиденция не разрешает автоматическую оплату, она будет установлена
      на &6false'
    AlreadyExists: '&cРезиденция с именем &6%1 &cуже существует.'
    Create: '&eВы создали резиденцию &6%1&e!'
    Rename: '&eПереименована Резиденция &6%1 &eв &6%2'
    Remove: '&eРезиденция &6%1 &eудалена...'
    CantRemove: '&cРезиденция &6%1 &cне может быть удалена, так как &6%2 &cподзона
      все еще арендована игроком &6%3'
    MoveDeny: '&cУ вас нет разрешения на движение в резиденции &6%1'
    TeleportNoFlag: '&cУ вас нет доступа к телепортации для этой резиденции.'
    FlagDeny: '&cУ вас нет разрешения &6%1 &cдля резиденции &6%2'
    GiveLimits: '&cНе могу дать резиденцию к выбранному игроку, потому что он находится
      за пределами целевых игроков.'
    GiveConfirm: '&7Нажмите, чтобы подтвердить &6%1 &7передачу резиденции от &6%2
      &7для &6%3'
    Give: '&eВы дали резиденцию &6%1 &eигроку &6%2'
    Recieve: '&eВы получили резиденцию &6%1 &eот игрока &6%2'
    ResList: ' &a%1. &e%2 &e- &6%3 %4&6%5'
    List: ' &e%2 &e- &6%3'
    TeleportNear: '&eТелепортируемся в ближайшую резиденцию.'
    SetTeleportLocation: '&eРасположение Телепорта установлено...'
    PermissionsApply: '&eРазрешения применено к резиденции.'
    NotOwner: '&cВы не являетесь владельцем этой резиденции'
    RemovePlayersResidences: '&eУбраны все резиденции, принадлежащие игроку &6%1'
    NotIn: '&cВы не в резиденции.'
    PlayerNotIn: '&cИгрок стоит не в вашей резиденции.'
    Kicked: '&eВас кикнули из резиденции'
    CantKick: '&eНе могу кикнуть этого игрока'
    In: '&eВы стоите в резиденции &6%1'
    OwnerChange: '&eВ резиденции &6%1 &eизменился владелец. Им стал &6%2'
    NonAdmin: '&cВы не администратор резиденции.'
    Line: '&eРезиденция: &6%1 '
    RentedBy: '&eАрендовал: &6%1'
    MessageChange: '&eСообщение...'
    CantDeleteResidence: '&cУ вас нет разрешения на удаление резиденции.'
    CantExpandResidence: '&cУ вас нет разрешения на расширение резиденции.'
    CantContractResidence: '&cВы не имеете разрешения на контракт для резиденции.'
    NoResHere: '&cЗдесь нет резиденции.'
    OwnerNoPermission: '&cВладелец не имеет разрешения на это.'
    ParentNoPermission: '&cУ вас нет прав вносить изменения в родительскую зону.'
    ChatDisabled: '&eЧат резиденции отключен...'
    DeleteConfirm: '&eВы уверены, что хотите удалить резиденцию &6%1&e? пишите &6/res
      confirm &eдля пподверждения.'
    ChangedMain: '&eИзменена основная резиденция на &6%1'
    LwcRemoved: '&eУдалена &6%1 &eLwc защита за &6%2мс'
    CanBeRented: '&6%1&e можно взять в аренду за &6%2 &eна &6%3 &eдней. &6/res market
      rent'
    CanBeBought: '&6%1&e можно купить за &6%2&e. &6/res market buy'
    IsForRent: '&6(В аренду)'
    IsForSale: '&6(Продается)'
    IsRented: '&6(Арендована)'
    TrustedResList: ' &a%1. &f%2 &e- &6%3 %4&6%5'
    Near: '&eРезиденции поблизости: &7%1'
    BaseFlagDeny: '&cУ вас нет разрешения &6%1'
  Rent:
    Disabled: '&cАренда отключена...'
    DisableRenew: '&eРезиденция &6%1 &eбольше не будет сдаваться по истечении срока.'
    EnableRenew: '&eРезиденция &6%1 &eбудет автоматически продлена по истечении срока.'
    NotByYou: '&cРезиденция сдается не вам.'
    isForRent: '&2Резиденция доступна для сдачи в аренду.'
    MaxRentDays: '&cВы не можете арендовать на срок более &6%1 &cдней.'
    OneTime: '&cНе могу продлить срок аренды этой резиденции.'
    Extended: '&eАренда продлена на  &6%1 &eдней для резиденции &6%2'
    Expire: '&eСрок действия аренды: &6%1'
    AutoPayTurnedOn: '&eАвтооплата &2ВКЛЮЧЕНА'
    AutoPayTurnedOff: '&eАвтооплата &cВЫКЛЮЧЕНА'
    ModifyDeny: '&cНевозможно изменить аренду резиденции.'
    Days: '&eДни аренды: &6%1'
    Rented: ' &6(Арендована)'
    RentList: ' &6%1&e. &6%2 &e(&6%3&e/&6%4&e/&6%5&e) - &6%6 &6%7'
    EvictConfirm: '&eПишите &6/res market confirm &eвыселить съемщика из резиденции
      &6%1'
    UnrentConfirm: '&eПишите &6/res market confirm &eснять аренду с резиденции &6%1'
    ReleaseConfirm: '&eПишите &6/res market confirm &eудалить резиденцию &6%1 &eиз
      маркета.'
  command:
    addedAllow: '&eДобавлена новая разрешенная команда для резиденции &6%1 '
    removedAllow: '&eУдалена разрешенная команда для резиденции &6%1 '
    addedBlock: '&eДобавлена новая заблокированная команда для резиденции &6%1 '
    removedBlock: '&eУдалена заблокированная команда для резиденции &6%1 '
    Blocked: '&eЗаблокированные команды: &6%1'
    Allowed: '&eРазрешенные команды: &6%1'
    Parsed: '%1'
    PlacehlderList: '&e%1. &6%2'
    PlacehlderResult: ' &eрезультат: &6%1'
  Rentable:
    Land: '&eАрендуемая земля: &6'
    AllowRenewing: '&eМожно обновить: &6%1'
    StayInMarket: '&eАрендуемое пребывание на рынке: &6%1'
    AllowAutoPay: '&eАрендная плата позволяет автооплату: &6%1'
    DisableRenew: '&6%1 &eбольше не будет обновлять арендный статус после истечения
      срока.'
    EnableRenew: '&6%1 &eтеперь будет автоматически обновлять арендный статус по истечении
      срока.'
  Economy:
    LandForSale: '&eЗемля для продажи:'
    NotEnoughMoney: '&cВы не имеете достаточно денег.'
    MoneyCharged: '&eНачислено &6%1 &eна ваш счет &6%2 &e.'
    MoneyAdded: '&eПолучено &6%1 &eна ваш счет &6%2 &e.'
    MoneyCredit: '&eЗачислено &6%1 &eна ваш счет &6%2 &e.'
    RentReleaseInvalid: '&eРезиденция &6%1 &eне сдана и не сдается.'
    RentSellFail: '&cНевозможно продать резиденцию, если она сдается в аренду.'
    SubzoneRentSellFail: '&cНевозможно продать Резиденцию, если ее подзона сдана в
      аренду.'
    ParentRentSellFail: '&cНевозможно продать Резиденцию, если ее родительская зона
      сдана в аренду.'
    SubzoneSellFail: '&cНевозможно продать подзону.'
    SellRentFail: '&cНельзя арендовать резиденцию, если она продается.'
    ParentSellRentFail: '&cНельзя арендовать Резиденцию, если ее родительская зона
      выставлена на продажу.'
    OwnerBuyFail: '&cНе можете купить собственную землю!'
    OwnerRentFail: '&cНе можете арендовать свою землю!'
    AlreadySellFail: '&eРезиденция уже продается!'
    LeaseRenew: '&eАренда действительна до &6%1'
    LeaseRenewMax: '&eАренда продлена до максимально допустимого'
    LeaseNotExpire: '&eНет такой аренды, или срок аренды не истекает.'
    LeaseRenewalCost: '&eСтоимость обновления для области &6%1 &eсоставляет &6%2'
    LeaseInfinite: '&eВремя аренды установлено на бесконечное...'
    MarketDisabled: '&cЭкономика отключена!'
    SellAmount: '&eСумма Продажи: &2%1'
    SellList: ' &6%1&e. &6%2 &e(&6%3&e) - &6%4'
    LeaseExpire: '&eСрок действия аренды: &2%1'
    LeaseList: '&6%1. &e%2 &2%3 &e%4'
  Expanding:
    North: '&eРасширение на Север &6%1 &eблоков'
    West: '&eРасширение на Запад &6%1 &eблоков'
    South: '&eРасширение на Юг &6%1 &eблоков'
    East: '&eРасширение на Восток &6%1 &eблоков'
    Up: '&eРасширение Вверх &6%1 &eблоков'
    Down: '&eРасширение Вниз &6%1 &eблоков'
  Contracting:
    North: '&eСокращение на Север &6%1 &eблоков'
    West: '&eСокращение на Запад &6%1 &eблоков'
    South: '&eСокращение на Юг &6%1 &eблоков'
    East: '&eСокращение на Восток &6%1 &eблоков'
    Up: '&eСокращение Вверх &6%1 &eблоков'
    Down: '&eСокращение Вниз &6%1 &eблоков'
  Shifting:
    North: '&eСдвиг на Север &6%1 &eблоков'
    West: '&eСдвиг на Запад &6%1 &eблоков'
    South: '&eСдвиг на Юг &6%1 &eблоков'
    East: '&eСдвиг на Восток &6%1 &eблоков'
    Up: '&eСдвиг Вверх &6%1 &eблоков'
    Down: '&eСдвиг Вниз &6%1 &eблоков'
  Limits:
    PGroup: '&7- &eПрава группы:&3 %1'
    RGroup: '&7- &eРезиденция группы:&3 %1'
    Admin: '&7- &eРезиденция Админа:&3 %1'
    CanCreate: '&7- &eМожет создавать резиденции:&3 %1'
    MaxRes: '&7- &eМакс. Резиденций:&3 %1'
    MaxEW: '&7- &eМакс. Размер Восток/Запад:&3 %1'
    MaxNS: '&7- &eМакс. Размер Север/Юг:&3 %1'
    MaxUD: '&7- &eМакс. Размер Вверх/Вниз:&3 %1'
    MinMax: '&7- &eМин./Макс. Высота Защиты:&3 %1 to %2'
    MaxSubzones: '&7- &eМакс. Подзон:&3 %1'
    MaxSubDepth: '&7- &eМакс. глубина подзоны:&3 %1'
    MaxRents: '&7- &eМакс. Аренд:&3 %1'
    MaxRentDays: ' &eМакс. Арендных дней:&3 %1'
    EnterLeave: '&7- &eМожно установить вход/выход сообщения:&3 %1'
    NumberOwn: '&7- &eКоличество ваших резиденций:&3 %1'
    Cost: '&7- &eСтоимость проживания за блок:&3 %1'
    Sell: '&7- &eСтоимость продажи резиденции за блок:&3 %1'
    Flag: '&7- &eРазрешенные флаги:&3 %1'
    MaxDays: '&7- &eМакс. количество дней аренды:&3 %1'
    LeaseTime: '&7- &eВремя аренды, предоставляемое при продлении:&3 %1'
    RenewCost: '&7- &eСтоимость возобновления за блок:&3 %1'
  Gui:
    Set:
      Title: '&6%1 флаги'
    Pset:
      Title: '&6%1 %2 флаги'
    Actions:
    - '&2Левый клик, чтобы включить'
    - '&cПравый клик, чтобы выключить'
    - '&eШифт+Левый клик, чтобы удалить'
  InformationPage:
    TopLine: '&e---< &a %1 &e >---'
    Page: '&e-----< &6%1 &e>-----'
    NextPage: '&e-----< &6%1 &e>-----'
    NoNextPage: '&e-----------------------'
    GeneralList: '&2 %1 &6- &e%2'
    FlagsList: '&2 %1 &6- &e%2'
    SmallSeparator: '&6------'
    Top: '&e___/ &a %1 - %2 &e \___'
    TopSingle: '&e___/ &a %1 &e \___'
    NextPage2: '&e-----< &6%1 &e>-----'
  Chat:
    ChatChannelChange: '&eИзменен канал чата резиденции на &6%1!'
    ChatChannelLeave: '&eВыход из чата зезиденции'
    JoinFirst: '&4Присоединение к первому каналу чата резиденции...'
    InvalidChannel: '&4Неверный канал...'
    InvalidColor: '&4Неверный цветовой код'
    NotInChannel: '&4Игрок не находится в канале чата'
    Kicked: '&6%1 &eкикнут из канала чата &6%2 '
    InvalidPrefixLength: '&4Префикс длинный. Допустимая длина: %1'
    ChangedColor: '&eЦвет канала чата резиденции изменен на %1'
    ChangedPrefix: '&eПрефикс канала чата резиденции изменен на %1'
    ChatMessage: '%1 %2%3: %4%5'
    ChatListeningMessage: '&2[Listening %6]%1 %2%3: %4%5'
  Shop:
    ListTopLine: '&6%1 &eСписок магазинов - Страница &6%2 &eиз &6%3 %4'
    List: ' &e%1. &6%2 &e(&6%3&e) %4'
    ListVoted: '&e%1 (&6%2&e)'
    ListLiked: '&7Лайки: &7%1'
    VotesTopLine: '&6%1 &e%2 список голосов &6- &eСтраница &6%3 &eиз &6%4 %5'
    VotesList: ' &e%1. &6%2 &e%3 &7%4'
    NoDesc: '&6Без описания'
    Desc: |-
      &6Описание:
      %1
    DescChange: '&6Описание изменено на: %1'
    NewBoard: '&6Успешно добавлена новая вывеска магазина'
    BoardExist: '&cВывеска магазина уже существует в этом месте'
    DeleteBoard: '&6Правй клик по табличке, которую вы хотите удалить'
    DeletedBoard: '&6Вывеска магазина удалена'
    IncorrectBoard: '&cЭто не вывеска магазина, попробуйте выполнить команду еще раз
      и нажмите по нужной табличке'
    InvalidSelection: '&cЛевый клик с помощью инструмента выделения вверху слева,
      а затем правый клик внизу справа'
    ToBigSelection: '&cВаш выбор слишком велик, максимально допустимо 16 блоков'
    ToDeapSelection: '&cВаш выбор слишком глубокий, максимально допустимо - 16x16x1
      блок'
    VoteChanged: '&6Голосование изменено с &e%1 &6на &e%2 &6для резиденции &e%3 '
    Voted: '&6Вы проголосовали и дали &e%1 &6голос(ов) для резиденции &e%2 '
    Liked: '&6Вам нравится резиденция &e%1 '
    AlreadyLiked: '&6Вам уже нравится резиденция &e%1 '
    NoVotes: '&cДля этой резиденции нет зарегистрированных голосов'
    CantVote: '&cРезиденция не имеет флаг магазина установлен в true'
    VotedRange: '&6Диапазон голосования от &e%1 &6до &e%2'
    SignLines:
      '1': '&e--== &8%1 &e==--'
      '2': '&9%1'
      '3': '&4%1'
      '4': '&8%1&e (&8%2&e)'
      Likes4: '&9Лайки: &8%2'
    ChantChange: '&4Это нельзя менять, пока установлен флаг shop'
  RandomTeleport:
    TpLimit: '&eВы не можете телепортироваться так быстро, пожалуйста, подождите &6%1
      &eсекунд и попробуйте снова'
    TeleportSuccess: '&eТелепортация в позицию X:&6%1&e, Y:&6%2&e, Z:&6%3 '
    IncorrectLocation: '&6Не удалось найти правильное местоположение телепортации,
      подождите &e%1 &6сек и повторите попытку.'
    Disabled: '&cРандомная телепортация отключена в этом мире'
    TeleportStarted: '&eТелепортация началась, не двигайтесь &6%4 &eсекунд.'
    WorldList: '&eВозможные миры: &6%1'
  Permissions:
    variableColor: '&f'
    permissionColor: '&6'
    cmdPermissionColor: '&2'
  General:
    DisabledWorld: '&cПлагин Residence отключен в этом мире'
    UseNumbers: '&cПожалуйста, используйте челые числа...'
    # Replace all text with '' to disable this message
    CantPlaceLava: '&cВы не можете разместить лаву за пределами резиденции и выше
      &6%1 блоков'
    # Replace all text with '' to disable this message
    CantPlaceWater: '&cВы не можете разместить воду за пределами резиденции и выше
      &6%1 блоков'
    CantPlaceChest: '&cВы не можете разместить сундук на этом месте'
    NoPermission: '&cУ вас нет разрешения на это.'
    info:
      NoPlayerPermission: '&c[игрок] не имеет разрешения [permission]'
    NoCmdPermission: '&cУ вас нет разрешения на эту команду.'
    DefaultUsage: '&eПишите &6/%1 ? &eдля получения дополнительной информации'
    MaterialGet: '&eИмя материала для идентификатора &6%1 &eсоставляет &6%2'
    MarketList: '&e---- &6Список рынков &e----'
    Separator: '&e----------------------------------------------------'
    AdminOnly: '&cТолько администраторы имеют доступ к этой команде.'
    InfoTool: '&e- Инструмент информации: &6%1'
    ListMaterialAdd: '&6%1 &eдобавлен в резиденцию &6%2'
    ListMaterialRemove: '&6%1 &eудален из резиденции &6%2'
    ItemBlacklisted: '&cВы попали в черный список из за использования этого предмета'
    WorldPVPDisabled: '&cПВП в мире откл.'
    NoPVPZone: '&cВ этой зоне ПВП выключено.'
    NoFriendlyFire: '&cОгонь по своим отключен'
    InvalidHelp: '&cНеверная страница справки.'
    TeleportDeny: '&cУ вас нет доступа к телепорту.'
    TeleportSuccess: '&eТелепортация!'
    TeleportConfirm: '&cЭтот телепорт небезопасен, вы упадете на &6%1 блоки. Используйте
      &6/res tpconfirm для выполнения телепортации в любом случае'
    TeleportStarted: '&eТелепортация в &6%1 &eначалась, не двигайтесь &6%2 &eсек.'
    TeleportTitle: '&eТелепортация!'
    TeleportTitleTime: '&6%1'
    TeleportCanceled: '&eТелепортация отменена!'
    NoTeleportConfirm: '&eНет телепортов, ожидающих подтверждения!'
    HelpPageHeader: '&eСправочные страницы - &6%1 &e- Страница <&6%2 &eиз &6%3&e>'
    ListExists: '&cСписок уже существует...'
    ListRemoved: '&eСписок удален...'
    ListCreate: '&eСоздан список &6%1'
    PhysicalAreas: '&eФизические области'
    CurrentArea: '&eТекущая зона: &6%1'
    TotalResSize: '&eОбщий размер: &6%1m³ (%2m²)'
    TotalWorth: '&eОбщая стоимость резиденции: &6%1 &e(&6%2&e)'
    TotalSubzones: '&eПодзоны в резиденции: &6%1 &e(&6%2&e)'
    NotOnline: '&eЦелевой игрок должен быть онлайн.'
    NextInfoPage: '&2| &eСледующая Страница &2>>>'
    PrevInfoPage: '&2<<< &eПредыдущая Страница &2|'
    GenericPages: '&eСтраница &6%1 &eиз &6%2 &e(&6%3&e)'
    WorldEditNotFound: '&cWorldEdit не был обнаружен.'
    CoordsTop: '&eX:&6%1 &eY:&6%2 &eZ:&6%3'
    CoordsBottom: '&eX:&6%1 &eY:&6%2 &eZ:&6%3'
    AdminToggleTurnOn: '&eАвтоматический ресадмин переключен на &6ВКЛ'
    AdminToggleTurnOff: '&eАвтоматический ресадмин переключен на &6ВЫКЛ'
    NoSpawn: '&eУ вас нет разрешений на перемещение к вашей точке спавна. Перемещение'
    CompassTargetReset: '&eВаш компас сброшен'
    CompassTargetSet: '&eВаш компас теперь указывает на &6%1'
    Ignorelist: '&2Игнор список:&6'
    Blacklist: '&cЧерный список:&6'
    LandCost: '&eСтоимость земли: &6%1'
    'True': '&2ВКЛ'
    'False': '&cОТКЛ'
    Removed: '&6Удален'
    FlagState: '&eСтатус флага: %1'
    Land: '&eЗемля: &6%1'
    Cost: '&eСтоимость: &6%1 &eза &6%2 &eдней'
    Status: '&eСтатус: %1'
    Available: '&2Доступно'
    Size: ' &eРазмер: &6%1'
    ResidenceFlags: '&eФлаги резиденции: &6%1'
    PlayersFlags: '&eФлаги игроков: &6%1'
    GroupFlags: '&eГрупповые флаги: &6%1'
    OthersFlags: '&eДругие флаги: &6%1'
    Moved: '&eПеремещено...'
    Name: '&eНазвание: &6%1'
    Lists: '&eСписки: &6'
    Residences: '&eРезиденции&6'
    CreatedOn: '&eСоздана: &6%1'
    Owner: '&eВладелец: &6%1'
    World: '&eМир: &6%1'
    Subzones: '&eПодзона'
    # The below lines represent various messages residence sends to the players.
    # Note that some messages have variables such as %1 that are inserted at runtime.
    NewPlayerInfo: '&eЕсли вы хотите создать охраняемую территорию для своего дома,
      используйте деревянный топор, чтобы выбрать противоположные стороны вашего дома,
      и выполните команду /res create НазваниеВашейРезиденции'
    TeleportConfirmLava: '&cМесто прибытия не безопасно, вы упадете в &6лаву&c.
      Используйте &6/res tpconfirm&c, чтобы всё же телепортироваться.'
    TeleportConfirmVoid: '&cМесто прибытия не безопасно, вы упадете в &6бездну&c.
      Используйте &6/res tpconfirm&c, чтобы всё же телепортироваться.'
    HelpPageHeader2: '&eСтраниц справочника - &6%1 &e- Страница <&6%2 &eиз &6%3&e>'
    ResSize:
      eastWest: '&eВосток/Запад: &6%1'
      northSouth: '&eСевер/Юг: &6%1'
      upDown: '&eВерх/Низ: &6%1'
    CoordsLiner: '&7 (&3%1&7;%2&7)'
    AllowedTeleportIcon: '&2T'
    BlockedTeleportIcon: '&7T'
    AllowedMovementIcon: '&2M'
    BlockedMovementIcon: '&7M'
  Raid:
    NotEnabled: '&cФункция рейдов выключена!'
    NotIn: '&cВы не участвуете в рейде!'
    CantLeave: '&cYou cant leave (%1) your own residence raid!'
    CantKick: '&cCant kick (%1) residence owner!'
    Kicked: '&eИгрок &7%1 &eисключен из рейда &7%2&e!'
    StartsIn: '&7Рейд начнётся через: [autoTimeLeft] &2%1D &4%2A'
    EndsIn: '&cРейд окончится через: [autoTimeLeft] &2%1D &4%2A'
    Ended: '&7Рейд на резиденцию &4%1 &7окончен!'
    cantDo: '&cВы не можете делать это во время рейда!'
    left: '&7Вы покинули рейд &4%1'
    noFlagChange: '&cНельзя менять флаги во время рейда'
    noRemoval: '&cНельзя удалять резиденцию во время рейда'
    immune: '&eИммунитет на %1'
    notImmune: '&eИммунитет закончился'
    notInRaid: '&eИгрок не участвует в рейде'
    attack:
      Joined: '&7Присоединение к рейду &2%1!'
      Started: '&7Рейд начался!'
      cooldown: '&cРейд только начался! Подождите %1'
      immune: '&cЭта резиденция имеет иммунитет к рейдам! Подождите %1'
      playerImmune: '&cВладелец резиденции имеет иммунитет к рейдам! Подождите %1'
      isOffline: '&cВладелец резиденции не в сети, поэтому рейд устроить не получится!'
      noSubzones: '&cНельзя устраивать рейд на подзоны!'
      noSelf: '&cНельзя устраивать рейд на свою резиденцию!'
      alreadyInAnother: '&cВы не можете присоединиться к рейду, потому что участвуете в рейде (%1)'
    defend:
      Joined: '&7Присоединение к рейду &2%1 &7в качестве оборонительной силы!'
      Sent: '&7Запрос на присоединнение на оборонительную сторону отправлен, ожидайте подтверждения'
      Join: '&7Присоединиться к рейду &6%1 &7в качестве оборонительной силы'
      Invitation: '&7Принять &6%1 &6в оборонительные силы'
      JoinedDef: '&2%1&7 вступил в оборонительные силы!'
      IsOffline: '&cНельзя вступить в оборонительные силы, пока владелец не в сети!'
      noSelf: '&cВы уже обороняете эту резиденцию'
      notRaided: '&cРезиденция не подвергается рейду'
      alreadyInAnother: '&cВы не можете вступить в оборонительные силы, потому что участвуете в рейде (%1)'
    status:
      title: '&7----------- &f%1(%2) &7-----------'
      immune: '&eИммунитет к рейдам на: %1'
      starts: '&7Рейд начнётся через: %1'
      attackers: '&7Нападающих: &4%1'
      defenders: '&7Защищающих: &4%1'
      ends: '&7Рейд окончится через: %1'
      canraid: '&2Невозможно устроить рейд'
      raidin: '&eРейд можно устроить через: %1'
    stopped: '&eРейд на резиденцию &6%1 &eокончен'
CommandHelp:
  Description: Содержит помощь для плагина Residence
  SubCommands:
    res:
      Description: Главная команда резиденции
      Info:
      - '&2Пишите &6/res [команда] ? <страница> &2для просмотра дополнительной информации.'
      SubCommands:
        auto:
          Description: Создание максимально допустимую резиденцию вокруг вас
          Info:
          - '&eПишите: &6/res auto (НазваниеРезиденции) (радиус)'
        select:
          Description: Команды выделения
          Info:
          - Эта команда выбирает области для использования резиденции.
          - /res select [x] [y] [z] - выбирает радиус блоков, с вами в середине.
          SubCommands:
            coords:
              Description: Показать выбранные координаты
              Info:
              - '&eПишите: &6/res select coords'
            size:
              Description: Показать выбранный размер
              Info:
              - '&eПишите: &6/res select size'
            auto:
              Description: Включает инструмент автоматического выделения
              Info:
              - '&eПишите: &6/res select auto [playername]'
            cost:
              Description: Показать стоимость выделения
              Info:
              - '&eПишите: &6/res select cost'
            vert:
              Description: Расширить выделение по вертикали
              Info:
              - '&eПишите: &6/res select vert'
              - Расширит выбор настолько высоко, насколько возможно.
            sky:
              Description: Расширить выделение до неба
              Info:
              - '&eПишите: &6/res select sky'
              - Расширяется настолько высоко, насколько вам позволено.
            bedrock:
              Description: Расширить выбор до бедрока
              Info:
              - '&eПишите: &6/res select bedrock'
              - Расширяется настолько низко, насколько вам позволено.
            expand:
              Description: Расширить выделение по направлению.
              Info:
              - '&eПишите: &6/res select expand <количество>'
              - Увеличивает <количество> в направлении вашего взгляда.
            shift:
              Description: Сдвиг выделения в направлении
              Info:
              - '&eПишите: &6/res select shift <количество>'
              - Выдвигает ваш выбор на <количество> в направлении вашего взгляда.
            chunk:
              Description: Выберите чанк, в котором вы сейчас находитесь.
              Info:
              - '&eПишите: &6/res select chunk'
              - Выбирает чанк, в котором вы сейчас находитесь.
            residence:
              Description: Выберите существующий район в резиденции.
              Info:
              - '&eПишите: &6/res select residence <резиденция>'
              - Выбирает существующую площадь в резиденции.
            worldedit:
              Description: Установите выбор, используя текущий выбор WorldEdit.
              Info:
              - '&eПишите: &6/res select worldedit'
              - Установите выбор, используя текущий выбор WorldEdit.
        padd:
          Description: Добавить игрока в резиденцию.
          Info:
          - '&eПишите: &6/res padd <резиденция> [игрок]'
          - Добавляет необходимые флаги для игрока
        signconvert:
          Description: Преобразует таблички из плагина ResidenceSign
          Info:
          - '&eПишите: &6/res signconvert'
          - Постараюсь преобразовать сохраненные данные таблички из стороннего плагина
        listallhidden:
          Description: Список всех скрытых резиденций
          Info:
          - '&eПишите: &6/res listhidden <страница>'
          - Список всех скрытых резиденций на сервере.
        bank:
          Description: Управлять деньгами в резиденции
          Info:
          - '&eПишите: &6/res bank [deposit/withdraw] <резиденция> [количество]'
          - Вы должны стоять в резиденции или указать название резиденции
          - У вас должен быть флаг + bank.
        create:
          Description: Создать Резиденцию
          Info:
          - '&eПишите: &6/res create <название>'
        listall:
          Description: Список всех резиденций
          Info:
          - '&eПишите: &6/res listall <страница> <мир> <-a/-f>'
          - Списки всех резиденций
        info:
          Description: Показать информацию о резиденции.
          Info:
          - '&eПишите: &6/res info <резиденция>'
          - Оставьте <резиденция>, чтобы отобразить информацию о резиденции, в которой
            вы сейчас находитесь.
        area:
          Description: Управление физическими зонами для резиденции.
          SubCommands:
            list:
              Description: Список физических зон в резиденции
              Info:
              - '&eПишите: &6/res area list [резиденция] <страница>'
            listall:
              Description: Список координат и другая информация для областей
              Info:
              - '&eПишите: &6/res area listall [резиденция] <страница>'
            add:
              Description: Добавьте физические области к резиденции
              Info:
              - '&eПишите: &6/res area add [резиденция] [ID области]'
              - Сначала вы должны выбрать две точки.
            remove:
              Description: Удалить физические зоны из резиденции
              Info:
              - '&eПишите: &6/res area remove [резиденция] [ID области]'
            replace:
              Description: Заменить физические зоны в резиденции
              Info:
              - '&eПишите: &6/res area replace [резиденция] [ID области]'
              - Сначала вы должны выбрать две точки.
              - Замена области будет взимать разницу в размере, если новая область
                больше.
          Info:
          - ''
        give:
          Description: Дайте резиденцию игроку.
          Info:
          - '&eПишите: &6/res give <название резиденции> [игрок] <-s>'
          - Предоставляет вашу резиденцию целевому игроку
        renamearea:
          Description: Переименовать название области для резиденции
          Info:
          - '&eПишите: &6/res removeworld [резиденция] [oldAreaName] [newAreaName]'
        contract:
          Description: Контракты резиденции в направлении, котором вы ищете
          Info:
          - '&eПишите: &6/res contract (резиденция) [количество]'
          - Контракты резиденции в направлении, котором Вы ищете.
          - Название резиденции необязательно
        check:
          Description: Проверьте состояние вашего флага
          Info:
          - '&eПишите: &6/res check [резиденция] [флаг] (игрок)'
        gset:
          Description: Установите флаги для определенной группы резиденции
          Info:
          - '&eПишите: &6/res gset <резиденция> [группа] [флаг] [true/false/remove]'
          - Чтобы увидеть список флагов, используйте /res flags ?
        list:
          Description: Список резиденций
          Info:
          - '&eПишите: &6/res list <игрок> <страница> <мир>'
          - Перечисляет все резиденции, которыми владеет игрок (кроме скрытых).
          - Если это список ваших собственных резиденций, то показывает также скрытые.
          - Чтобы перечислить все резиденции, используйте /res listall.
        version:
          Description: Узнать версию плагина
          Info:
          - '&eПишите: &6/res version'
        tool:
          Description: Показывает выбор резиденции и названия информационных инструментов
          Info:
          - '&eПишите: &6/res tool'
        pdel:
          Description: Удалить игрока из резиденции.
          Info:
          - '&eПишите: &6/res pdel <резиденция> [игрок]'
          - Убирает необходимые флаги с игрока
        market:
          Description: Купить, продать или арендовать резиденцию
          Info:
          - '&eПишите: &6/res market ? для большей информации'
          SubCommands:
            Info:
              Description: Получить информацию о экономике резиденции.
              Info:
              - '&eПишите: &6/res market Info [резиденция]'
              - Показывает, если Резиденция продается или сдается, и стоимость.
            list:
              Description: Списки арендуемых и резиденций на продажу.
              Info:
              - '&eПишите: &6/res market list [rent/sell]'
              SubCommands:
                rent:
                  Description: Списки арендуемых резиденций.
                  Info:
                  - '&eПишите: &6/res market list rent'
                sell:
                  Description: Списки резиденций для продажи.
                  Info:
                  - '&eПишите: &6/res market list sell'
            sell:
              Description: Продать резиденцию
              Info:
              - '&eПишите: &6/res market sell [резиденция] [количество]'
              - Выставляет резиденцию на продажу за [количество] денег.
              - Другой игрок может купить резиденцию командой /res market buy
            sign:
              Description: Установить табличку рынка
              Info:
              - '&eПишите: &6/res market sign [резиденция]'
              - Устанавливает табличку рынка, на которую вы смотрите.
            buy:
              Description: Купить резиденцию
              Info:
              - '&eПишите: &6/res market buy [резиденция]'
              - Покупает резиденцию, если она для продажи.
            unsell:
              Description: Останавливает продажу резиденции
              Info:
              - '&eПишите: &6/res market unsell [резиденция]'
            rent:
              Description: Арендовать резиденцию
              Info:
              - '&eПишите: &6/res market rent [резиденция] <AutoPay>'
              - Арендует резиденцию. Автообновление может быть как истинным, так и
                ложным. Если это правда, место жительства будет автоматически повторно
                сдано в аренду по истечении срока, если владелец проживания разрешил
                это.
            rentable:
              Description: Сделать резиденцию арендуемой.
              Info:
              - '&eПишите: &6/res market rentable [резиденция] [стоимость] [дни] <AllowRenewing>
                <StayInMarket> <AllowAutoPay>'
              - Делает жилье арендуемым за [стоимость] денег за каждые [дни] количество
                дней.
              - Если <AllowRenewing> имеет значение true, резиденция сможет быть сдана
                в аренду еще раз до истечения срока аренды.
              - Если <StayInMarket> имеет значение true, резиденция останется на рынке
                после того, как последний арендатор будет удален.
              - Если <Allow AutoPay> имеет значение true, деньги за аренду будут автоматически
                сниматься с баланса игроков, если он выбрал эту опцию при аренде
            autopay:
              Description: Устанавливает автоплатеж резиденции на заданное значение
              Info:
              - '&eПишите: &6/res market autopay <резиденция> [true/false]'
            payrent:
              Description: Оплачивает арендную плату за определенную резиденцию
              Info:
              - '&eПишите: &6/res market payrent <резиденция>'
            confirm:
              Description: Подтверждает действие снития аренды или освобождения резиденции
              Info:
              - '&eПишите: &6/res market confirm'
            unrent:
              Description: Снять резиденцию с аренды или сдачи в аренду.
              Info:
              - '&eПишите: &6/res market unrent [резиденция]'
              - Если вы арендатор, эта команда снимает арендную плату за дом для вас.
              - Если вы владелец, эта команда больше не сдает резиденцию.
        rc:
          Description: Присоединяется к текущему или заданному чату резиденции
          Info:
          - '&eПишите: &6/res rc (резиденция)'
          - Присоединяет к общему чату резиденции
          SubCommands:
            leave:
              Description: Отсоединиться от общего чата резиденции
              Info:
              - '&eПишите: &6/res rc leave'
              - Если вы находитесь в канале резиденции, то покинете его.
            setcolor:
              Description: Устанавливить цвет текста канала чата резиденции
              Info:
              - '&eПишите: &6/res rc setcolor [код цвета]'
              - Устанавливает цвет текста канала чата резиденции
            setprefix:
              Description: Устанавливить префикс канала чата резиденции
              Info:
              - '&eПишите: &6/res rc setprefix [префикс]'
              - Устанавливает префикс канала чата резиденции
            kick:
              Description: Кикнуть игрока с канала
              Info:
              - '&eПишите: &6/res rc kick [игрок]'
              - Кикает игрока с канала
        expand:
          Description: Расширить резиденцию в направлении, в которое вы смотрите
          Info:
          - '&eПишите: &6/res expand (резиденция) [количество]'
          - Расширяет резиденцию в направлении, в которое вы смотрите
          - Название резиденции необязательно
        compass:
          Description: Установить указатель компаса на резиденцию
          Info:
          - '&eПишите: &6/res compass <резиденция>'
        lists:
          Description: Списки разрешений
          Info:
          - Предопределенные разрешения, которые могут быть применены к резиденции.
          SubCommands:
            add:
              Description: Добавить список
              Info:
              - '&eПишите: &6/res lists add <название>'
            remove:
              Description: Удалить список
              Info:
              - '&eПишите: &6/res lists remove <название>'
            apply:
              Description: Применить список к резиденции
              Info:
              - '&eПишите: &6/res lists apply <название> <резиденция>'
            set:
              Description: Установить флаг
              Info:
              - '&eПишите: &6/res lists set <название> <флаг> <значение>'
            pset:
              Description: Установить флаг игроку
              Info:
              - '&eПишите: &6/res lists pset <название> <игрок> <флаг> <значение>'
            gset:
              Description: Установить флаг группе
              Info:
              - '&eПишите: &6/res lists gset <название> <группа> <флаг> <значение>'
            view:
              Description: Просмотр списка.
              Info:
              - '&eПишите: &6/res lists view <название>'
        reset:
          Description: Сбросить флаги резиденции по умолчанию.
          Info:
          - '&eПишите: &6/res reset <резиденция/all>'
          - Сбрасывает флаги резиденции на их значения по умолчанию. Вы должны быть
            владельцем или администратором, чтобы сделать это.
        listhidden:
          Description: Список скрытых резиденций
          Info:
          - '&eПишите: &6/res listhidden <игрок> <страница>'
          - Список скрытых резиденций игрока
        setmain:
          Description: Устанавливить резиденцию в качестве основной, чтобы отображаться
            в чате в качестве префикса
          Info:
          - '&eПишите: &6/res setmain (резиденция)'
          - Устанавливает определенное резиденцию в качестве основной.
        server:
          Description: Отметить земелю сервера в собственности.
          Info:
          - '&eПишите: &6/resadmin server [резиденция]'
          - Стать владельцем резиденции сервера.
        rt:
          Description: Телепортироваться в случайное место в мире
          Info:
          - '&eПишите: &6/res rt (мир) (игрок)'
          - Телепортируйтесь в случайное место в мире
        mirror:
          Description: Копирование Флагов
          Info:
          - '&eПишите: &6/res mirror [1 резиденция] [2 резиденция]'
          - Копируйте флаги из одной резиденции в другую. Вы должны быть владельцем
            обоих или администратором, чтобы сделать это.
        shop:
          Description: Управлять магазином резиденции
          Info:
          - Управляйте магазином резиденции
          SubCommands:
            list:
              Description: Показывает список магазинов
              Info:
              - '&eПишите: &6/res shop list'
              - Показывает полный список всех резиденций с флагом магазина
            vote:
              Description: Проголосуй за магазин резиденции
              Info:
              - '&eПишите: &6/res shop vote <резиденция> [количество]'
              - Голоса за текущую или определенную резиденцию
            like:
              Description: Дать лайк для магазина резиденции
              Info:
              - '&eПишите: &6/res shop like <резиденция>'
              - Дает лайк для магазин резиденции
            votes:
              Description: Показать голоса магазина
              Info:
              - '&eПишите: &6/res shop votes <резиденция> <страница>'
              - Показывает полный список голосов магазина текущей или определенной
                резиденции
            likes:
              Description: Показать лайки магазина
              Info:
              - '&eПишите: &6/res shop likes <резиденция> <страница>'
              - Показывает полный список лайков магазина текущей или определенной
                резиденции
            setdesc:
              Description: Устанавливает описание магазина резиденции
              Info:
              - '&eПишите: &6/res shop setdesc [текст]'
              - Устанавливает описание магазина резиденции. Цветовой код поддерживается.
                Для новой строки используйте /n
            createboard:
              Description: Создать вывеску магазина
              Info:
              - '&eПишите: &6/res shop createboard [place]'
              - Создает вывеску магазина из выбранной области. place - позиция, с
                которой начинается заполнение вывески
            deleteboard:
              Description: Удалить вывеску магазина
              Info:
              - '&eПишите: &6/res shop deleteboard'
              - Удаляет вывеску магазина правым кликом по табличке
        lset:
          Description: Изменить параметры черного списка и списка игнорируемых
          Info:
          - '&eПишите: &6/res lset <резиденция> [blacklist/ignorelist] [material]'
          - '&eПишите: &6/res lset <резиденция> Info'
          - Внесение в черный список материала предотвращает его размещение в резиденции.
          - Игнорирование приводит к тому, что конкретный материал не защищен Резиденцией.
        pset:
          Description: Установите флаги на конкретного игрока в резиденции
          Info:
          - '&eПишите: &6/res pset <резиденция> [игрок] [флаг] [true/false/remove]'
          - '&eПишите: &6/res pset <резиденция> [игрок] removeall'
          - Чтобы увидеть список флагов, используйте /res flags ?
        show:
          Description: Показать границы резиденции
          Info:
          - '&eПишите: &6/res show <резиденция>'
        flags:
          Description: Список флагов
          Info:
          - Для значений флага true разрешает действие, а false запрещает действие.
          SubCommands:
            anvil:
              Translated: anvil
              Description: Разрешает или запрещает взаимодействие с наковальней
              Info:
              - '&eПишите: &6/res set/pset <резиденция> anvil true/false/remove'
            admin:
              Translated: admin
              Description: Дает игроку разрешение на смену флагов по месту жительства
              Info:
              - '&eПишите: &6/res pset <резиденция> admin true/false/remove'
            animalkilling:
              Translated: animalkilling
              Description: Разрешает или запрещает убийство животных
              Info:
              - '&eПишите: &6/res set/pset <резиденция> animalkilling true/false/remove'
            animals:
              Translated: animals
              Description: Разрешает или запрещает порождение животных
              Info:
              - '&eПишите: &6/res set <резиденция> animals true/false/remove'
            anvilbreak:
              Translated: anvilbreak
              Description: Разрешает или запрещает наковальню в резиденции
              Info:
              - '&eПишите: &6/res set <резиденция> anvilbreak true/false/remove'
            backup:
              Translated: backup
              Description: Если установлено значение true, восстанавливает предыдущий
                вид области (требуется WordEdit)
              Info:
              - '&eПишите: &6/res set <резиденция> backup true/false/remove'
            bank:
              Translated: bank
              Description: Разрешает или запрещает вводить / выводить деньги из банка
              Info:
              - '&eПишите: &6/res set/pset <резиденция> bank true/false/remove'
            bed:
              Translated: bed
              Description: Разрешает или запрещает игрокам использовать кровати
              Info:
              - '&eПишите: &6/res set/pset <резиденция> bed true/false/remove'
            beacon:
              Translated: beacon
              Description: Разрешает или запрещает взаимодействие с маяком
              Info:
              - '&eПишите: &6/res set/pset <резиденция> beacon true/false/remove'
            brew:
              Translated: brew
              Description: Позволяет или запрещает игрокам использовать варочные стойки
              Info:
              - '&eПишите: &6/res set/pset <резиденция> brew true/false/remove'
            build:
              Translated: build
              Description: Разрешает или запрещает строительство
              Info:
              - '&eПишите: &6/res set/pset <резиденция> build true/false/remove'
            burn:
              Translated: burn
              Description: Разрешает или запрещает сжигание мобов в резиденциях
              Info:
              - '&eПишите: &6/res set <резиденция> burn true/false/remove'
            button:
              Translated: button
              Description: Позволяет или запрещает игрокам использовать кнопки
              Info:
              - '&eПишите: &6/res set/pset <резиденция> button true/false/remove'
            cake:
              Translated: cake
              Description: Позволяет или запрещает игрокам есть торт
              Info:
              - '&eПишите: &6/res set/pset <резиденция> cake true/false/remove'
            canimals:
              Translated: canimals
              Description: Разрешает или запрещает нестандартное появление животных
              Info:
              - '&eПишите: &6/res set <резиденция> canimals true/false/remove'
            chorustp:
              Translated: chorustp
              Description: Разрешить или запретить телепортировку в резиденцию с фруктами
                хора
              Info:
              - '&eПишите: &6/res set/pset <резиденция> chorustp true/false/remove'
            chat:
              Translated: chat
              Description: Позволяет присоединиться к чату резиденции
              Info:
              - '&eПишите: &6/res set/pset <резиденция> chat true/false/remove'
            cmonsters:
              Translated: cmonsters
              Description: Разрешает или запрещает создание монстров
              Info:
              - '&eПишите: &6/res set <резиденция> cmonsters true/false/remove'
            commandblock:
              Translated: commandblock
              Description: Разрешает или запрещает взаимодействие с команднными блоками
              Info:
              - '&eПишите: &6/res set/pset <резиденция> commandblock true/false/remove'
            command:
              Translated: command
              Description: Разрешает или запрещает использование команды в резиденциях
              Info:
              - '&eПишите: &6/res set/pset <резиденция> command true/false/remove'
            container:
              Translated: container
              Description: Разрешает или запрещает использование печей, сундуков и
                т.д...
              Info:
              - '&eПишите: &6/res set/pset <резиденция> container true/false/remove'
            coords:
              Translated: coords
              Description: Скрывает координаты резиденции
              Info:
              - '&eПишите: &6/res set <резиденция> coords true/false/remove'
            craft:
              Translated: craft
              Description: Разрешает table, enchant, brew флаги
              Info:
              - '&eПишите: &6/res set <резиденция> craft true/false/remove'
            creeper:
              Translated: creeper
              Description: Разрешить или запретить взрывы крипера
              Info:
              - '&eПишите: &6/res set <резиденция> creeper true/false/remove'
            dragongrief:
              Translated: dragongrief
              Description: Запрещает Дракону ломать блоки
              Info:
              - '&eПишите: &6/res set <резиденция> dragongrief true/false/remove'
            day:
              Translated: day
              Description: Устанавливает дневное время в резиденции
              Info:
              - '&eПишите: &6/res set <резиденция> day true/false/remove'
            dye:
              Translated: dye
              Description: Разрешает или запрещает покраску овец
              Info:
              - '&eПишите: &6/res set/pset <резиденция> dye true/false/remove'
            damage:
              Translated: damage
              Description: Разрешает или запрещает урон сущности в резиденции
              Info:
              - '&eПишите: &6/res set <резиденция> damage true/false/remove'
            decay:
              Translated: decay
              Description: Разрешает или запрещает опадание листвы в резиденции
              Info:
              - '&eПишите: &6/res set <резиденция> decay true/false/remove'
            destroy:
              Translated: destroy
              Description: Разрешает или запрещает только уничтожение блоков, отменяет
                флаг build
              Info:
              - '&eПишите: &6/res set/pset <резиденция> destroy true/false/remove'
            dryup:
              Translated: dryup
              Description: Предотвращает землю от высыхания
              Info:
              - '&eПишите: &6/res set <резиденция> dryup true/false/remove'
            diode:
              Translated: diode
              Description: Позволяет или запрещает игрокам использовать повторители
                редстоуна
              Info:
              - '&eПишите: &6/res set/pset <резиденция> diode true/false/remove'
            door:
              Translated: door
              Description: Позволяет или запрещает игрокам использовать двери и люки
              Info:
              - '&eПишите: &6/res set/pset <резиденция> door true/false/remove'
            egg:
              Translated: egg
              Description: Разрешает или запрещает взаимодействие с яйцом дракона
              Info:
              - '&eПишите: &6/res set/pset <резиденция> egg true/false/remove'
            enchant:
              Translated: enchant
              Description: Позволяет или запрещает игрокам использовать стол зачарования
              Info:
              - '&eПишите: &6/res set/pset <резиденция> enchant true/false/remove'
            explode:
              Translated: explode
              Description: Разрешает или запрещает взрывы в резиденции
              Info:
              - '&eПишите: &6/res set <резиденция> explode true/false/remove'
            enderpearl:
              Translated: enderpearl
              Description: Разрешить или запретить телепортировку в резиденцию с помощью
                Жемчуга Края
              Info:
              - '&eПишите: &6/res set/pset <резиденция> enderpearl true/false/remove'
            fallinprotection:
              Translated: fallinprotection
              Description: Защищает от падания блоков в резиденции
              Info:
              - '&eПишите: &6/res set <резиденция> fallinprotection true/false/remove'
            falldamage:
              Translated: falldamage
              Description: Защищает игроков от повреждений при падении
              Info:
              - '&eПишите: &6/res set <резиденция> falldamage true/false/remove'
            feed:
              Translated: feed
              Description: Установка на true заставляет резиденцию кормить своих жителей
              Info:
              - '&eПишите: &6/res set <резиденция> feed true/false/remove'
            friendlyfire:
              Translated: friendlyfire
              Description: Разрешить или запретить дружеский огонь
              Info:
              - '&eПишите: &6/res pset <резиденция> friendlyfire true/false/remove'
            fireball:
              Translated: fireball
              Description: Позволяет или запрещает огненные шары в резиденциях
              Info:
              - '&eПишите: &6/res set <резиденция> fireball true/false/remove'
            firespread:
              Translated: firespread
              Description: Разрешает или запрещает распространение огня
              Info:
              - '&eПишите: &6/res set <резиденция> firespread true/false/remove'
            flowinprotection:
              Translated: flowinprotection
              Description: Позволяет или запрещает потоки жидкостей в резиденции
              Info:
              - '&eПишите: &6/res set <резиденция> flowinprotection true/false/remove'
            flow:
              Translated: flow
              Description: Позволяет или запрещает поток жидкости
              Info:
              - '&eПишите: &6/res set <резиденция> flow true/false/remove'
            flowerpot:
              Translated: flowerpot
              Description: Разрешает или запрещает взаимодействие с цветочным горшком
              Info:
              - '&eПишите: &6/res set/pset <резиденция> flowerpot true/false/remove'
            grow:
              Translated: grow
              Description: Разрешает или запрещает выращивание растений
              Info:
              - '&eПишите: &6/res set <резиденция> grow true/false/remove'
            glow:
              Translated: glow
              Description: Игроки начнут светиться при входе в резиденцию
              Info:
              - '&eПишите: &6/res set <резиденция> glow true/false/remove'
            hotfloor:
              Translated: hotfloor
              Description: Предотвратить повреждение от блоков магмы
              Info:
              - '&eПишите: &6/res set <резиденция> hotfloor true/false/remove'
            hidden:
              Translated: hidden
              Description: Скрывает резиденцию от команды list или listall
              Info:
              - '&eПишите: &6/res set <резиденция> hidden true/false/remove'
            hook:
              Translated: hook
              Description: Разрешает или запрещает зацепку удочкой
              Info:
              - '&eПишите: &6/res set/pset <резиденция> hook true/false/remove'
            healing:
              Translated: healing
              Description: Установка на true заставляет резиденцию лечить своих жителей
              Info:
              - '&eПишите: &6/res set <резиденция> healing true/false/remove'
            iceform:
              Translated: iceform
              Description: Предотвращает образование льда
              Info:
              - '&eПишите: &6/res set <резиденция> iceform true/false/remove'
            icemelt:
              Translated: icemelt
              Description: Предотвращает таяние льда
              Info:
              - '&eПишите: &6/res set <резиденция> icemelt true/false/remove'
            ignite:
              Translated: ignite
              Description: Разрешает или запрещает возгорание
              Info:
              - '&eПишите: &6/res set/pset <резиденция> ignite true/false/remove'
            itemdrop:
              Translated: itemdrop
              Description: Разрешает или запрещает выбрасывание предметов
              Info:
              - '&eПишите: &6/res set/pset <резиденция> itemdrop true/false/remove'
            itempickup:
              Translated: itempickup
              Description: Разрешает или запрещает поднятие предметов
              Info:
              - '&eПишите: &6/res set/pset <резиденция> itempickup true/false/remove'
            jump2:
              Translated: jump2
              Description: Позволяет прыгать на два блока ввысоту
              Info:
              - '&eПишите: &6/res set <резиденция> jump2 true/false/remove'
            jump3:
              Translated: jump3
              Description: Позволяет прыгать на три блока ввысоту
              Info:
              - '&eПишите: &6/res set <резиденция> jump3 true/false/remove'
            keepinv:
              Translated: keepinv
              Description: Игроки хранят инвентарь после смерти
              Info:
              - '&eПишите: &6/res set <резиденция> keepinv true/false/remove'
            keepexp:
              Translated: keepexp
              Description: Игроки сохраняют опыт после смерти
              Info:
              - '&eПишите: &6/res set <резиденция> keepexp true/false/remove'
            lavaflow:
              Translated: lavaflow
              Description: Позволяет или запрещает поток лавы, перекрывает поток
              Info:
              - '&eПишите: &6/res set <резиденция> lavaflow true/false/remove'
            leash:
              Translated: leash
              Description: Разрешает или запрещает привязи животных
              Info:
              - '&eПишите: &6/res set/pset <резиденция> leash true/false/remove'
            lever:
              Translated: lever
              Description: Позволяет или запрещает игрокам использовать рычаги
              Info:
              - '&eПишите: &6/res set/pset <резиденция> lever true/false/remove'
            mobexpdrop:
              Translated: mobexpdrop
              Description: Предотвращает мобам сбрасывание опыта после смерти
              Info:
              - '&eПишите: &6/res set <резиденция> mobexpdrop true/false/remove'
            mobitemdrop:
              Translated: mobitemdrop
              Description: Предотвращает мобам сбрасывание предметов после смерти
              Info:
              - '&eПишите: &6/res set <резиденция> mobitemdrop true/false/remove'
            mobkilling:
              Translated: mobkilling
              Description: Разрешает или запрещает убийство мобов
              Info:
              - '&eПишите: &6/res set/pset <резиденция> mobkilling true/false/remove'
            monsters:
              Translated: monsters
              Description: Разрешает или запрещает спавн мобов
              Info:
              - '&eПишите: &6/res set <резиденция> monsters true/false/remove'
            move:
              Translated: move
              Description: Разрешает или запрещает движение в резиденции
              Info:
              - '&eПишите: &6/res set/pset <резиденция> move true/false/remove'
            nanimals:
              Translated: nanimals
              Description: Разрешает или запрещает спавн животных
              Info:
              - '&eПишите: &6/res set <резиденция> nanimals true/false/remove'
            nmonsters:
              Translated: nmonsters
              Description: Разрешает или запрещает спавн мобов
              Info:
              - '&eПишите: &6/res set <резиденция> nmonsters true/false/remove'
            night:
              Translated: night
              Description: Устанавливает ночное время в резиденции
              Info:
              - '&eПишите: &6/res set <резиденция> night true/false/remove'
            nofly:
              Translated: nofly
              Description: Разрешает или запрещает летать по резиденции
              Info:
              - '&eПишите: &6/res set/pset <резиденция> nofly true/false/remove'
            fly:
              Translated: fly
              Description: Переключает полет для игроков в резиденции
              Info:
              - '&eПишите: &6/res set/pset <резиденция> fly true/false/remove'
            nomobs:
              Translated: nomobs
              Description: Предотвращает проникновение монстров в резиденцию
              Info:
              - '&eПишите: &6/res set <резиденция> nomobs true/false/remove'
            note:
              Translated: note
              Description: Позволяет или запрещает игрокам использовать музыкалные
                блоки
              Info:
              - '&eПишите: &6/res set/pset <резиденция> note true/false/remove'
            nodurability:
              Translated: nodurability
              Description: Предотвращает потерю прочности предмета
              Info:
              - '&eПишите: &6/res set <резиденция> nodurability true/false/remove'
            overridepvp:
              Translated: overridepvp
              Description: Отменяет любую защиту плагинов пвп
              Info:
              - '&eПишите: &6/res set <резиденция> overridepvp true/false/remove'
            pressure:
              Translated: pressure
              Description: Позволяет или запрещает игрокам использовать нажимные пластины
              Info:
              - '&eПишите: &6/res set/pset <резиденция> pressure true/false/remove'
            piston:
              Translated: piston
              Description: Разрешить или запретить поршням толкать или тянуть блоки
                в резиденции
              Info:
              - '&eПишите: &6/res set <резиденция> piston true/false/remove'
            pistonprotection:
              Translated: pistonprotection
              Description: Включает или отключает перемещение поршней в или из резиденции
              Info:
              - '&eПишите: &6/res set <резиденция> pistonprotection true/false/remove'
            place:
              Translated: place
              Description: Разрешает или запрещает только размещение блоков, отменяет
                флаг build
              Info:
              - '&eПишите: &6/res set/pset <резиденция> place true/false/remove'
            pvp:
              Translated: pvp
              Description: Разрешить или запретить пвп в резиденции
              Info:
              - '&eПишите: &6/res set <резиденция> pvp true/false/remove'
            rain:
              Translated: rain
              Description: Устанавливает дождливую погоду в резиденции
              Info:
              - '&eПишите: &6/res set <резиденция> rain true/false/remove'
            redstone:
              Translated: redstone
              Description: Выдает флаги lever, diode, button, pressure, note
              Info:
              - '&eПишите: &6/res pset <резиденция> redstone true/false/remove'
            respawn:
              Translated: respawn
              Description: Автоматически возрождает игрока
              Info:
              - '&eПишите: &6/res set <резиденция> respawn true/false/remove'
            riding:
              Translated: riding
              Description: Запрет верховой езды
              Info:
              - '&eПишите: &6/res set/pset <резиденция> riding true/false/remove'
            shoot:
              Translated: shoot
              Description: Разрешает или запрещает стрельбу снарядом в области
              Info:
              - '&eПишите: &6/res set <резиденция> shoot true/false/remove'
            sun:
              Translated: sun
              Description: Устанавливает солнечную погоду в резиденции
              Info:
              - '&eПишите: &6/res set <резиденция> sun true/false/remove'
            shop:
              Translated: shop
              Description: Добавляет резиденцию в специальный список магазинов
              Info:
              - '&eПишите: &6/res set <резиденция> shop true/false/remove'
            snowtrail:
              Translated: snowtrail
              Description: Предотвращает снежные тропы от снеговика
              Info:
              - '&eПишите: &6/res set <резиденция> snowtrail true/false/remove'
            spread:
              Translated: spread
              Description: Предотвращает распространение блоков
              Info:
              - '&eПишите: &6/res set <резиденция> spread true/false/remove'
            snowball:
              Translated: snowball
              Description: Препятствует отбрасывание от снежка
              Info:
              - '&eПишите: &6/res set <резиденция> snowball true/false/remove'
            sanimals:
              Translated: sanimals
              Description: Разрешает или запрещает порождать животных из яйца призыва
              Info:
              - '&eПишите: &6/res set <резиденция> sanimals true/false/remove'
            shear:
              Translated: shear
              Description: Разрешает или запрещает стрич овец
              Info:
              - '&eПишите: &6/res set/pset <резиденция> shear true/false/remove'
            smonsters:
              Translated: smonsters
              Description: Разрешает или запрещает порождать животных из спавнера
                или яйца призыва
              Info:
              - '&eПишите: &6/res set <резиденция> smonsters true/false/remove'
            subzone:
              Translated: subzone
              Description: Разрешить игроку делать подзоны в резиденции
              Info:
              - '&eПишите: &6/res set/pset <резиденция> subzone true/false/remove'
            title:
              Translated: title
              Description: Показывает или скрывает вход / выход сообщения в резиденции
              Info:
              - '&eПишите: &6/res set <резиденция> title true/false/remove'
            table:
              Translated: table
              Description: Позволяет или запрещает игрокам использовать верстак
              Info:
              - '&eПишите: &6/res set/pset <резиденция> table true/false/remove'
            tnt:
              Translated: tnt
              Description: Разрешить или запретить взрывы TNT
              Info:
              - '&eПишите: &6/res set <резиденция> tnt true/false/remove'
            tp:
              Translated: tp
              Description: Разрешить или запретить телепортацию в резиденцию
              Info:
              - '&eПишите: &6/res set/pset <резиденция> tp true/false/remove'
            trade:
              Translated: trade
              Description: Разрешает или запрещает сельским жителям торговать в резиденции
              Info:
              - '&eПишите: &6/res set/pset <резиденция> trade true/false/remove'
            trample:
              Translated: trample
              Description: Разрешает или запрещает вытаптывание урожая в резиденции
              Info:
              - '&eПишите: &6/res set <резиденция> trample true/false/remove'
            trusted:
              Translated: trusted
              Description: Выдает флаги build, use, move, container и tp
              Info:
              - '&eПишите: &6/res pset <резиденция> trusted true/false/remove'
            use:
              Translated: use
              Description: Разрешает или запрещает использование дверей, рычагов,
                кнопок и т. д....
              Info:
              - '&eПишите: &6/res set/pset <резиденция> use true/false/remove'
            vehicledestroy:
              Translated: vehicledestroy
              Description: Разрешает или запрещает уничтожение вагонеток
              Info:
              - '&eПишите: &6/res set/pset <резиденция> vehicledestroy true/false/remove'
            witherspawn:
              Translated: witherspawn
              Description: Разрешает или запрещает спавн иссушителя
              Info:
              - '&eПишите: &6/res set <резиденция> witherspawn true/false/remove'
            phantomspawn:
              Translated: phantomspawn
              Description: Разрешает или запрещает спавн фантомов
              Info:
              - '&eПишите: &6/res set <резиденция> phantomspawn true/false/remove'
            witherdamage:
              Translated: witherdamage
              Description: Позволяет или запрещает повреждение от иссушителя
              Info:
              - '&eПишите: &6/res set <резиденция> witherdamage true/false/remove'
            witherdestruction:
              Translated: witherdestruction
              Description: Позволяет или запрещает повреждение блоков от иссушителя
              Info:
              - '&eПишите: &6/res set <резиденция> witherdestruction true/false/remove'
            waterflow:
              Translated: waterflow
              Description: Позволяет или запрещает поток воды, перекрывает поток
              Info:
              - '&eПишите: &6/res set <резиденция> waterflow true/false/remove'
            wspeed1:
              Translated: wspeed1
              Description: Изменить скорость ходьбы игроков по месту жительства на
                %1
              Info:
              - '&eПишите: &6/res set <резиденция> wspeed1 true/false/remove'
            wspeed2:
              Translated: wspeed2
              Description: Изменить скорость ходьбы игроков по месту жительства на
                %1
              Info:
              - '&eПишите: &6/res set <резиденция> wspeed2 true/false/remove'
            anchor:
              Translated: anchor
              Description: Разрешает или запрещает использовать якорь возрождения
              Info:
              - '&eПишите: &6/res set/pset <резиденция> anchor true/false/remove'
            honey:
              Translated: honey
              Description: Разрешает или запрещает собирать мёд
              Info:
              - '&eПишите: &6/res set/pset <резиденция> honey true/false/remove'
            honeycomb:
              Translated: honeycomb
              Description: Allows or denys players to get honeycomb
              Info:
              - '&eПишите: &6/res set/pset <резиденция> honeycomb true/false/remove'
            copper:
              Translated: copper
              Description: Разрешает изменять медные блоки
              Info:
              - '&eПишите: &6/res set/pset <резиденция> copper true/false/remove'
            elytra:
              Translated: elytra
              Description: Разрешает или запрещает использовать элитры в резиденции
              Info:
              - '&eПишите: &6/res set/pset <резиденция> elytra true/false/remove'
            harvest:
              Translated: harvest
              Description: Разрешает собирать урожай
              Info:
              - '&eПишите: &6/res set/pset <резиденция> harvest true/false/remove'
            nametag:
              Translated: nametag
              Description: Разрешает или запрещает использовать бирки
              Info:
              - '&eПишите: &6/res set/pset <резиденция> nametag true/false/remove'
            safezone:
              Translated: safezone
              Description: Предотвращает резиденцию от наложения эффекта дурного знамения
              Info:
              - '&eПишите: &6/res set <резиденция> safezone true/false/remove'
            brush:
              Translated: brush
              Description: Позволяет или запрещает рисовать блоками
              Info:
              - '&eПишите: &6/res set/pset <резиденция> brush true/false/remove'
        remove:
          Description: Удалить резиденцию.
          Info:
          - '&eПишите: &6/res remove <резиденция>'
        signupdate:
          Description: Обновление табличек резиденции
          Info:
          - '&eПишите: &6/res signupdate'
        current:
          Description: Показывает резиденцию в вашем месте
          Info:
          - '&eПишите: &6/res current'
        reload:
          Description: перезагрузить lang или файлы конфигурации
          Info:
          - '&eПишите: &6/res reload [config/lang/groups/flags]'
        setowner:
          Description: Сменить владельца резиденции
          Info:
          - '&eПишите: &6/resadmin setowner [резиденция] [игрок]'
        unstuck:
          Description: Телепорт за пределы резиденции
          Info:
          - '&eПишите: &6/res unstuck'
        subzone:
          Description: Создайте подзоны в резиденциях.
          Info:
          - '&eПишите: &6/res subzone <резиденция> [подзона]'
          - Если название резиденции не указано, попытается использовать резиденцию,
            в котором вы находитесь.
        removeworld:
          Description: Удалить все резиденции из мира
          Info:
          - '&eПишите: &6/res removeworld [мир]'
          - Может использоваться только с консоли
        limits:
          Description: Покажит ваши лимиты
          Info:
          - '&eПишите: &6/res limits (игрок)'
          - Показывает ограничения, которые вы имеете при создании и управлении резиденциями.
        set:
          Description: Установить общие флаги на резиденции
          Info:
          - '&eПишите: &6/res set <резиденция> [флаг] [true/false/remove]'
          - Чтобы увидеть список флагов, используйте /res flags ?
          - Эти флаги применяются ко всем игрокам, у которых нет флага, специально
            примененного к ним. (смотрите /res pset ?)
        clearflags:
          Description: Убрать все флаги с резиденций
          Info:
          - '&eПишите: &6/res clearflags <резиденция>'
        message:
          Description: Управление сообщения входа/выхода из резиденции
          Info:
          - '&eПишите: &6/res message <резиденция> [enter/leave] [сообщение]'
          - Установить сообщение входа/выхода из резиденции.
          - '&eПишите: &6/res message <резиденция> remove [enter/leave]'
          - Удалить сообщение входа/выхода.
        command:
          Description: Управляет разрешенными или заблокированными командами в резиденции
          Info:
          - '&eПишите: &6/res command <резиденция> <allow/block/list> <команда>'
          - Показывает список, добавляет или удаляет разрешенные или отключенные команды
            в резиденции
          - Используйте _ для включения команды с несколькими переменными
        confirm:
          Description: Подтверждение удаления резиденции
          Info:
          - '&eПишите: &6/res confirm'
          - Подтверждает удаление резиденции
        resadmin:
          Description: Включен или отключен администратор резиденции
          Info:
          - '&eПишите: &6/res resadmin [on/off]'
        tpset:
          Description: Установите местоположение телепортации Резиденции
          Info:
          - '&eПишите: &6/res tpset'
          - Это установит местоположение телепорта для Резиденции, где вы стоите.
          - Вы должны стоять в резиденции, чтобы использовать эту команду.
          - Вы также должны быть владельцем или иметь +admin флаг для Резиденции
        tpconfirm:
          Description: Игнорировать опасное предупреждение телепортации
          Info:
          - '&eПишите: &6/res tpconfirm'
          - Телепортирует вас к месту жительства, когда телепортация небезопасна.
        removeall:
          Description: Удалить все Резиденции, принадлежащие игроку.
          Info:
          - '&eПишите: &6/res removeall [игрок]'
          - Удаляет все резиденции, принадлежащие конкретному игроку.
          - Требуется /resadmin, если вы используете команду на кого-либо, кроме себя.
        material:
          Description: Проверьте, существует ли материал по его идентификатору
          Info:
          - '&eПишите: &6/res material [материал]'
        kick:
          Description: Кикает игрока из резиденции.
          Info:
          - '&eПишите: &6/res kick <игрок>'
          - Вы должны быть владельцем или администратором, чтобы сделать это.
          - Игрок должен быть онлайн.
        sublist:
          Description: Список подзон резиденции
          Info:
          - '&eПишите: &6/res sublist <резиденция> <страница>'
          - Список подзон внутри резиденции.
        rename:
          Description: Переименовывает резиденцию
          Info:
          - '&eПишите: &6/res rename [Старое имя] [Новое имя]'
          - Вы должны быть владельцем или администратором, чтобы сделать это.
          - Имя не должно быть занято.
        setallfor:
          Description: Установите общие флаги для всех резиденций, принадлежащих конкретному
            игроку
          Info:
          - '&eПишите: &6/res setallfor [игрок] [флаг] [true/false/remove]'
        lease:
          Description: Управление условиями аренды
          Info:
          - '&eПишите: &6/res lease [renew/cost] [резиденция]'
          - /res lease cost покажет стоимость продления аренды.
          - /res lease renew продлит аренду, если у вас достаточно денег.
          SubCommands:
            set:
              Description: Установите время аренды
              Info:
              - '&eПишите: &6/resadmin lease set [резиденция] [#days/infinite]'
              - Устанавливает время аренды на указанное количество дней или бесконечно.
            renew:
              Description: Продлить срок аренды
              Info:
              - '&eПишите: &6/resadmin lease renew <резиденция>'
              - Продлевает срок аренды для текущей или указанной резиденции
            list:
              Description: Показать список аренды текущей резиденции
              Info:
              - '&eПишите: &6/resadmin lease list <резиденция> <страница>'
              - Распечатывает все сроки аренды подзон
            expires:
              Description: Дата окончания аренды
              Info:
              - '&eПишите: &6/resadmin lease expires <резиденция>'
              - Показывает, когда истекает срок аренды резиденции.
            cost:
              Description: Показывает стоимость обновления аренды
              Info:
              - '&eПишите: &6/resadmin lease cost <резиденция>'
              - Показывает, сколько денег нужно, чтобы продлить аренду резиденции.
        tp:
          Description: Телепортация в резиденцию
          Info:
          - '&eПишите: &6/res tp [резиденция]'
          - Телепортирует вас в резиденцию, вы должны иметь доступ к флагу +tp или
            быть владельцем.
          - Ваша группа разрешений также должна иметь право телепортироваться администратором
            сервера.
        setall:
          Description: Установить общие флаги на всех резиденциях
          Info:
          - '&eПишите: &6/res setall [флаг] [true/false/remove]'
        resreload:
          Description: Перезагрузить резиденцию.
          Info:
          - '&eПишите: &6/resreload'
        resload:
          Description: Загрузите файл сохранения резиденции.
          Info:
          - '&eПишите: &6/resload'
          - НЕБЕЗОПАСНАЯ команда, перед выполнением не сохраняет резиденции.
          - Загружает файл сохранения резиденции после внесения изменений.
        placeholders:
          Info:
          - '&eПишите: &6/res placeholders (parse) (плейсхолдер) (игрок)'
          Description: Список плейсхолдеров
          parse: '[result]'
        raid:
          Info:
          - '&eПишите: &6/res raid start [резиденция] (игроок)'
          - '&6/res raid stop [резиденция]'
          - '&6/res raid kick [игрок]'
          - '&6/res raid immunity [add/take/set/clear] [резиденция/currentres] [время]'
          Description: Управление рейдами на резиденцию
        raidstatus:
          Info:
          - '&eПишите: &6/res raidstatus (резиденция/игрок)'
          Description: Проверить статус рейда резиденции
        leaveraid:
          Info:
          - '&eПишите: &6/res leaveraid'
          Description: Покинуть рейд
        defend:
          Info:
          - '&eПишите: &6/res defend [резиденция] (игрок)'
          Description: Вступить в оборонительные силы резиденции
        attack:
          Description: Начать рейд на резиденцию
          Info:
          - '&eПишите: &6/res attack [резиденция]'
