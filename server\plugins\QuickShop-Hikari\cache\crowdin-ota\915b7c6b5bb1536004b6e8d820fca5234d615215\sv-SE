break-shop-use-supertool: <yellow>Du kan förstöra butiken med SuperVerktyget.
fee-charged-for-price-change: <green>Du betalade <red>{0}<green> för att ändra priset.
not-allowed-to-create: <red>Du får inte skapa en butik här.
disabled-in-this-world: <red>QuickShop är inaktiverat i denna värld
how-much-to-trade-for: <green><PERSON><PERSON> i chatten, hur mycket du vill sälja <yellow>{1}x {0}<green> för.
client-language-changed: <green>QuickShop upptäckte att ditt klientspråk har ändrats, vi använder nu {0} språk för dig.
shops-backingup: S<PERSON>par butik-backup från databasen...
_comment: Hej översättare! Om du redigerar detta från Github eller från kodkällor, besök https://crowdin.com/project/quickshop-hikari
unlimited-shop-owner-changed: <yellow><PERSON>na obegränsade butikens ägare har ändrats till {0}.
bad-command-usage-detailed: '<red>Bad command arguments! Accepts the following parameters: <gray>{0}'
thats-not-a-number: <red>Ogiltig siffra
shop-name-disallowed: <red>The shop name <yellow>{0} <red>is disallowed. Pick another one!
console-only-danger: <red>Detta är ett farligt kommando, varför endast konsolen kan exekvera det.
not-a-number: <red>Du kan bara ange ett nummer, din inmatning var {0}.
not-looking-at-valid-shop-block: <red>Kunde inte hitta ett block för att skapa butik. Du måste titta på ett.
shop-removed-cause-ongoing-fee: <red>Din butik vid {0} togs bort eftersom du inte hade tillräckligt med pengar för att behålla den!
tabcomplete:
  amount: '[antal]'
  item: '[item]'
  price: '[pris]'
  name: '[name]'
  range: '[intervall]'
  currency: '[valutans namn]'
  percentage: '[percentage%]'
taxaccount-unset: <green>Denna butiks momskonto följer nu serverns globala inställningar.
blacklisted-item: <red>Du kan inte sälja detta föremål eftersom det finns på svartlistan
command-type-mismatch: <red>Detta kommando kan endast exekveras av <aqua>{0}.
server-crash-warning: '<red>Server kan krascha efter körning av /qs reload om du ersätter/tar bort QuickShop plugin Jar-fil medan servern körs.'
you-cant-afford-to-change-price: <red>Det kostar {0} att ändra priset i din butik.
safe-mode: <red>QuickShop är nu i säkert läge, du kan inte öppna den här butiksbehållaren, kontakta serveradministratören för att åtgärda felen.
forbidden-vanilla-behavior: <red>Operationen är förbjuden på grund av att den inte är konsekvent med vanilla-beteendet
shop-out-of-space-name: <dark_purple>Din affär {0} är full!
paste-disabled: |-
  <red>Paste functionality has been disabled! You cannot request technical support. <newline>Reason: {0}
quick-fill:
  entry-own: ' <yellow>- <red>[own] <aqua>{0} <light_purple>{1}'
  hover:
    - '<yellow>Namn: <aqua>{0}'
    - '<yellow>Ägare: <aqua>{0}'
    - '<yellow>Typ: <aqua>{0}'
    - '<yellow>Pris: <aqua>{0}'
    - '<yellow>Föremål: <aqua>{0}'
    - '<yellow>Plats: <aqua>{0}'
  hover-arg-filled:
    - '<yellow>Namn: <aqua>{name}'
    - '<yellow>Ägare: <aqua>{owner}'
    - '<yellow>Typ: <aqua>{type}'
    - '<yellow>Pris: <aqua>{price}'
    - '<yellow>Föremål: <aqua>{item}'
    - '<yellow>Plats: <aqua>{location}'
  header: '<yellow>You have multiple shops with name "<green>{0}<yellow>", choose one to continue:'
  entry-normal: ' <yellow>- <aqua>{0} <light_purple>{1}'
  entry-cooperation: ' <yellow>- <red>[co] <aqua>{0} <light_purple>{1}'
3rd-plugin-build-check-failed-admin: '<gray>(AdminOnly) <light_purple>{0}<dark_gray> denied the permission checks. If this not expected, try adding <light_purple>{1} <gray>to the listener blacklist. Configuration Guide:https://ghost-chu.github.io/QuickShop-Hikari-Documents/docs/modules/shops/protection-checker'
average-price-nearby: '<green>Närliggande genomsnittspris: <yellow>{0}'
inventory-check-global-alert: "<red>[Inventory Check] <gray>Warning! Found a QuickShop display item <gold>{2}</gold> in inventory at <aqua>{0}</aqua> <dark_gray>[{1}]</dark_gray>, which it shouldn't happened, This usually means that someone is maliciously exploiting the exploit to duplicating the display item."
digits-reach-the-limit: <red>Du har nått gränsen för decimaler i priset.
currency-unset: <green>Butikens valuta har tagits bort. Använder standardinställningar nu.
you-cant-create-shop-in-there: <red>Du har inte behörighet att skapa en butik på den här platsen.
no-pending-action: <red>Du har inga väntande transaktioner
refill-success: <green>Påfyllningen lyckades
failed-to-paste: <red>Misslyckades att ladda upp data till Pastebin. Kontrollera din internetanslutning och försök igen. (Se konsolen för detaljer)
shop-out-of-stock-name: <dark_purple>Din affär {0} har fått slut på {1}!
shop-name-invalid: <red>The shop name <yellow>{0} <red>is invalid. Pick another one!
how-many-buy-stack: <yellow>Ange i chatten, hur många paket du vill <aqua>KÖPA<green>. Det finns <yellow>{0}<green> föremål i varje paket och du kan köpa <yellow>{1}<green> paket. Ange <aqua>{2}<green> i chatten för att köpa alla.
exceeded-maximum: <red>Värdet överskred det maximala värdet i Java.
unlimited-shop-owner-keeped: '<yellow>Observera: Butiksägaren är fortfarande ägare av obegränsad butik, du behöver själv byta ny butiksägare igen.'
no-enough-money-to-keep-shops: <red>Du har inte tillräckligt med pengar för att behålla dina butiker! Alla butiker har tagits bort...
3rd-plugin-build-check-failed: <red>3:e parts plugin <bold>{0}<reset><red> nekade behörighetskontroller, har du konfigurerat behörigheter där?
not-a-integer: <red>Du måste ange en siffra, din inmatning var {0}.
translation-country: 'Språkområde: Svenska (sv_SE)'
buying-more-than-selling: '<red>VARNING: Du köper föremål för mer än du säljer dem!'
purchase-failed: '<red>Köpet misslyckades: Internt fel. Kontakta serveradministratören.'
denied-put-in-item: <red>Du kan inte lägga in detta objekt i din butik!
shop-has-changed: <red>Butiken du försökte använda har ändrats sedan du klickade på den!
flush-finished: <green>Framgångsrikt rensat meddelandena.
no-price-given: <red>Var god ange ett giltigt pris.
shop-already-owned: <red>Det här är redan en butik.
backup-success: <green>Backup framgångsrik.
not-looking-at-shop: <red>Kunde inte hitta en QuickShop. Du måste titta på en.
you-cant-afford-a-new-shop: <red>Det kostar {0} att skapa en ny butik.
success-created-shop: <red>Butik skapad.
shop-creation-cancelled: <red>Butik-skapelsen avbröts!
shop-owner-self-trade: <yellow>You handlar med din egen butik, så du kanske inte kommer att få något saldo.
purchase-out-of-space: <red>Denna butik har slut på tillgängligt utrymme, Kontakta butiksägare eller personal för att tömma butiken.
reloading-status:
  success: <green>Omladdning slutförd utan några fel.
  scheduled: <green>Omladdning slutförd. &(Vissa ändringar kräver att servern startar om för att påverka)
  require-restart: <green>Omladdning slutförd. &(Vissa ändringar kräver att servern startar om för att påverka)
  failed: <red>Reload misslyckades, kontrollera serverkonsolen
player-bought-from-your-store-tax: <green>{0} köpte {1} {2} från din butik och du tjänade {3} ({4} i skatt).
not-enough-space: <red>Du har bara plats för {0} mer!
shop-name-success: <green>Successfully set the shop name to <yellow>{0}<green>.
shop-staff-added: <green>Framgångsrikt lagt till {0} som personal för din butik.
shop-staff-empty: <yellow>This shop have no staff members.
shops-recovering: Återställer butiker från backup...
virtual-player-component-hover: "<gray>This is a virtual player.\n<gray>Refers to a system account of this name with the same name.</gray>\n<green>UUID: <yellow>{0}</yellow></green>\n<green>Username: <yellow>{1}</yellow></green>\n<green>Display as: <yellow>{2}</yellow></green>"
real-player-component-hover: "<gray>This is a real exists player.\n<green>UUID: <yellow>{0}</yellow></green>\n<green>Username: <yellow>{1}</yellow></green>\n<green>Display as: <yellow>{2}</yellow></green>\n<gray>If you wish to use a virtual system account with the same name, add <dark_gray>\"[]\"</dark_gray> to the both side of the username: <dark_gray>[{1}]</dark_gray>."
menu:
  sell-tax: <green>Du betalade <yellow>{0} <green>i moms.
  owner: '<green>Ägare: {0}'
  preview: <aqua>[Förhandsgranska]
  enchants: <dark_purple>Förtrollningar
  sell-tax-self: <green>Du betalade ingen moms eftersom du äger den här butiken.
  shop-information: '<green>Butiksinformation:'
  item: '<green>Föremål: <yellow>{0}'
  price-per: <green>Pris per <yellow>{0} <green>- <yellow>{1}
  item-name-and-price: <yellow>{0} {1} <green>för <yellow>{2}
  item-name-and-price-tax: <yellow>{0} {1}<green>för</green> {2} <gray>(<green>{3}</green> i skatt)</gray>
  successful-purchase: '<green>Framgångsrikt köpt:'
  price-per-stack: <green>Pris per <yellow>{2}x {0} - {1}
  stored-enchants: <dark_purple>Lagrade förtrollningar
  item-holochat-error: <red>[Fel]
  this-shop-is-selling: <green>Denna butik <aqua>SÄLJER& föremål.
  shop-stack: '<green>Antal i paket: <yellow>{0}'
  space: '<green>Utrymme: <yellow>{0}'
  effects: <green>Effekter
  damage-percent-remaining: <yellow>{0}% <green>kvarstående.
  item-holochat-data-too-large: <red>[Error] Objektets NBT är för stort för att visa
  stock: '<green>Lagersaldo <yellow>{0}'
  this-shop-is-buying: <green>Denna butik <light_purple>KÖPER& föremål.
  successfully-sold: '<green>Framgångsrikt sålt:'
  total-value-of-chest: '<green>Totalt värde av butik: <yellow>{0}'
currency-not-exists: <red>Kan inte hitta den valuta som du vill ställa in. Stavningen är kanske fel eller att valutan inte är tillgänglig i denna värld.
no-nearby-shop: <red>Ingen butik som matchar {0} i närheten.
translation-author: 'Ghost_chu, Andre_601'
integrations-check-failed-trade: <red>Integration {0} nekade butiksbytet
shop-transaction-failed: <red>Sorry, but an internal error occurred while processing your purchase. The purchase has been cancelled and any operation has been rollback. Please contact the server administrators if it keeping occurred.
success-change-owner-to-server: <green>Framgångsrikt ändrat butiksägaren till Server.
shop-name-not-found: <red>The shop named <yellow>{0} <red>not exists.
shop-name-too-long: <red>Detta butiksnamn är för långt (max längd {0}), var snäll och välj en annan!
metric:
  header-player: '<yellow>{0}s {1} {2} transaktioner:'
  action-hover: <yellow>{0}
  price-hover: <yellow>Totalt {0}, inklusive {1} skatter.
  unknown: <gray>(okänd)
  undefined: <gray>(noname)
  no-results: <red>Inga transaktioner hittades.
  action-description:
    DELETE: <yellow>Player deleted a shop. And refunded the shop creation fee to owner if possible.
    ONGOING_FEE: <yellow>Player paid the ongoing fee because the payment period.
    PURCHASE_BUYING_SHOP: <yellow>Spelaren sålde några föremål till en köpande butik.
    CREATE: <yellow>Spelaren skapade en affär.
    PURCHASE_SELLING_SHOP: <yellow>Spelaren köpte några föremål från en säljande butik
    PURCHASE: <yellow>Köpte föremål från en butik
  query-argument: 'Query Argument: {0}'
  amount-hover: <yellow>{0}st
  header-shop: '<yellow>Affären {0}s {1} {2} transaktioner:'
  player-hover: |-
    <yellow>{0}<newline><gold>UUID: <gray>{1}
  looking-up: <yellow>Utför metrisk uppslagning, var god vänta...
  tax-hover: <yellow>{0} skatter
  header-global: '<yellow>Servern {0}s {1} transaktioner:'
  na: <gray>N/A
  transaction-count: <yellow>{0} totalt
  shop-hover: |-
    <yellow>{0}<newline><gold>Position: <gray>{1} {2} {3}, Värld: {4}<newline><gold>Ägare: <gray>{5}<newline><gold>Affärstyp: <gray>{6}<newline><gold>Föremål: <gray>{7}<newline><gold>Pris: <gray>{8}
  time-hover: '<yellow>Tid: {0}'
  amount-stack-hover: <yellow>{0}st stack
permission-denied-3rd-party: '<red>Tillstånd nekat: tredje parts plugin [{0}].'
you-dont-have-that-many-items: <red>Du har endast {0} {1}.
complete: <green>Färdig!
translate-not-completed-yet-url: 'Översättningen av språket {0} slutfördes inte av {1}. Vill du hjälpa oss att förbättra översättningen? Klicka här: {2}'
success-removed-shop: <green>Butik borttagen.
currency-set: <green>Butikens valuta har angetts till {0}.
shop-purged-start: <green>Shop rensning startade, kontrollera konsolen för detaljer.
economy-transaction-failed: <red>Sorry, but an internal error occurred while processing your transaction. The transaction has been cancelled and any economy operation has been rollback. Please contact the server administrators if it keeping occurred.
nothing-to-flush: <green>Du har inga nya butiksmeddelanden.
no-price-change: <red>Detta skulle inte resultera i en prisändring!
edition-confilct: QuicShop-Hikari med QuickShop-Reremake installerat kan strida mot varandra, avinstallera en av dem.
inventory-unavailable: |-
  <red>This shop InventoryWrapper not exists or invalid. Did you using addon to re-binding the shop Inventory? Information: InventoryWrapper={0}, WrapperProvider={1}, SymbolLink={2}. Please contact the server administrators.
file-test: Detta är en testtextfil. Vi använder den för att testa om messages.json är trasig. Du kan fylla den med valfria påskägg som du gillar här :)
unknown-player: <red>Angiven spelare finns inte, kontrollera användarnamnet du angav.
player-offline: <red>The target player currently is offline.
player-profile-format: <aqua>{0}</aqua><gold>(<yellow>{1}</yellow>)</gold>
shop-type:
  selling: SÄLJER
  buying: KÖPER
language:
  qa-issues: '<yellow>Kvalitetsförsäkringsfrågor: <aqua>{0}%'
  code: '<yellow>Kod: <gold>{0}'
  approval-progress: '<yellow>Godkända Framsteg: <aqua>{0}%'
  translate-progress: '<yellow>Översättningsframsteg: <aqua>{0}%'
  name: '<yellow>Namn: <gold>{0}'
  help-us: <green>[Hjälp oss att förbättra översättningens kvalitet]
warn-to-paste: |-
  <yellow>Samlar in data och laddar upp det till Pastebin, det kan ta ett tag. <red><bold>Varning:<red> Datan hålls offentlig i en vecka! Det kan läcka din serverkonfiguration och annan känslig information. Se till att du bara skickar den till <bold>betrodd personal/utvecklas.
how-many-sell-stack: <green>Ange i chatten, hur många föremål du vill <light_purple>SÄLJA<green> per paket. Det finns <yellow>{0}<green> föremål per paket och du har <yellow>{1}<green> paket tillgängliga. Ange <aqua>{2}<green> i chatten för att sälja alla.
updatenotify:
  buttontitle: '[Uppdatera nu]'
  onekeybuttontitle: '[OneKey-uppdatering]'
  label:
    github: '[GitHub]'
    ore: '[Ore]'
    lts: '[LTS]'
    nbtapi: '[+NBTAPI]'
    qualityverifyed: '[Kvalitet]'
    master: '[Master]'
    unstable: '[Ostabil]'
    paper: '[+Paper Optimized]'
    stable: '[Stabil]'
    spigotmc: '[SpigotMC]'
    papermc: '[PaperMC]'
    bukkitdev: '[BukkitDev]'
    polymart: '[Polymart]'
    modrinth: '[Modrinth]'
    basic: '[Basic]'
  list:
    - '{0} har släppts. Du använder {1} just nu!'
    - Boom! Ny uppdatering {0} på gång. Uppdatera!
    - Överraskning! {0} kom ut. Du är kvar på {1}
    - Ser ut som du behöver uppdatera. {0} har släppts!
    - Hoppsan! {0} släpptes. Du är på {1}!
    - Jag svär, QS har uppdaterats till {0}. Varför har du inte uppdaterat ännu?
    - Fixar och donar... Tyvärr, men {0} har släppts!
    - Fel! Nej. Detta är inget fel. {0} har släppts!
    - OMG! {0} kom ut! Varför använder du fortfarande {1}?
    - 'Dagens nyheter: QuickShop har uppdaterats till {0}!'
    - Plugin k.i.a. Du bör uppdatera till {0}!
    - Uppdatering {0} antände. Spara uppdatering!
    - Det finns en uppdatering tillgänglig Befälhavare. {0} har just kommit ut!
    - Se min stil---{0} uppdaterad. Du använder fortfarande {1}
    - Uppdatering! Ny uppdatering {0}! Uppdatera!
    - Vad tror du? {0} har släppts! Uppdatera!
    - Läkare, QuickShop har en ny uppdatering {0}! Du bör uppdatera~
    - Ko~ko~da~yo~ QuickShop har en ny uppdatering {0}~
    - Paimon vill berätta för dig att QuickShop har en ny uppdatering {0}!
  remote-disable-warning: '<red>Denna version av QuickShop har markerats som inaktiverad av fjärrservern. Det innebär att denna version kan ha allvarliga problem. Få information från vår SpigotMC-sida: {0}. Denna varning kommer att visas och spammas i din konsol tills du växlar till en icke inaktiverad version. Detta kommer dock inte att påverka din server.'
purchase-out-of-stock: <red>Denna butik har slut på lagret, Kontakta butiksägare eller personal för att fylla på lagret.
nearby-shop-entry: '<green>- Info:{0} <green>Pris:<aqua>{1} <green>x:<aqua>{2} <green>y:<aqua>{3} <green>z:<aqua>{4} <green>distans: <aqua>{5} <green>block'
chest-title: QuickShop-butik
console-only: <red>Detta kommando kan endast exekveras av Konsolen.
failed-to-put-sign: <red>Inte tillräckligt med utrymme runt butiken för att placera butiksskylten.
shop-name-unset: <red>This shop's name now removed
shop-nolonger-freezed: <green>Du tinade upp butiken. Det är tillbaka till det normala nu!
no-permission-build: <red>Du kan inte skapa en butik här.
tableformat:
  left_half_line: <dark_purple>+--------------------
  right_half_line: <dark_purple>--------------------+
  full_line: <dark_purple>+---------------------------------------------------+
  left_begin: '<dark_purple>| '
quickshop-gui-preview: QuickShop GUI Föremål Förhandsgranskning
translate-not-completed-yet-click: Översättningen av språket {0} slutfördes inte ännu av {1}. Vill du hjälpa oss att förbättra översättningen? Klicka här!
taxaccount-invalid: <red>Angivet konto är ogiltigt, ange ett giltigt spelarnamn eller uuid(med bindestreck).
player-bought-from-your-store: <red>{0} köpte {1} {2} från din butik, och du tjänade {3}.
reached-maximum-can-create: <red>Du har redan skapat {0}/{1} butiker vilket är max!
reached-maximum-create-limit: <red>Du har nått det maximala antalet butiker du kan skapa
translation-version: 'Support Version: Hikari'
no-double-chests: <red>Du kan inte skapa den dubbla kistbutiken.
price-too-cheap: <red>Priset måste vara högre än <yellow>${0}
shop-not-exist: <red>Det finns ingen butik.
bad-command-usage: <red>Bad command arguments!
cleanghost-warning: <yellow>This command will purge <red>all</red> shops if the shop is corrupted, was created in not allowed worlds, is selling/buying not allowed items or <red><bold>EXISTS IN A UNLOADED WORLD</bold></red>. Make sure to create a full backup of your shop data first and use <aqua>/qs cleanghost confirm</aqua> to continue.
cleanghost-starting: <green>Börjar söka efter spökbutiker (utan containerblock). Alla icke-existerande butiker kommer att tas bort...
cleanghost-deleting: <yellow>Found a corrupted shop <aqua>{0}</aqua> because {1}, mark it to delete...
cleanghost-deleted: <green>Total <yellow>{0}</yellow> shops have been deleted.
shop-purchase-cancelled: <red>Butiksköp avbrutet.
bypassing-lock: <red>Ignorerar ett butikslås!
bungee-cross-server-msg: '<yellow>QuickShop CSM: <green>{0}'
saved-to-path: Säkerhetskopian sparades till {0}.
shop-now-freezed: <green>Du har fryst butiken. Ingen kan handla med denna butik nu!
price-is-now: <green>Det nya priset för butiken är <yellow>{0}
shops-arent-locked: <red>Kom ihåg, butiker är INTE skyddade från stöld! Om du vill stoppa tjuvar, lås den med LWC, Lockette, m.m!
that-is-locked: <red>Denna butik är låst.
shop-has-no-space: <red>Butiken har endast plats för {0} fler {1}.
safe-mode-admin: '<red><bold>VARNING: <red>QuickShop på denna server som körs nu under safe-mode, inga funktioner kommer att fungera, skriv <yellow>/qs <red> kommandot för att kontrollera eventuella fel.'
shop-stock-too-low: <red>Butiken har endast {0} {1} kvar!
world-not-exists: <red>Världen <yellow>{0}<red> finns inte
how-many-sell: <green>Ange i chatten, hur många du vill <light_purple>SÄLJA<green>. Du har &{0}& tillgängliga. Ange &{1}<green> i chatten, för att sälja alla.
shop-freezed-at-location: <yellow>Din butik {0} vid {1} blev frusen!
translation-contributors: 'Medhjälpare: Timtower, Netherfoam, KaiNoMood, Mgazul, JackTheChicken och Andre_601'
empty-success: <green>Tömning av butik lyckades
taxaccount-set: <green>Denna butiks momskonto har satts till <yellow>{0}
support-disable-reason:
  hot-reload: <yellow>Unsupported hot reloaded, restart the server and check again.
  outdated: <yellow>This version of QuickShop already outdated, up-to-date before requesting support!
  bad-hosts: |-
    <yellow>This server HOSTS has been modified and QuickShop some functions require connect to Mojang API correctly. Fix the HOSTS before asking support, Windows at C:\windows\system32\drivers\etc\hosts, Linux at /etc/hosts.
  privacy: <yellow>This server running under cracked (offline) mode. If you are running bukkit server under proxy and online-mode=true on proxy, please configure the proxy related settings correctly.
  modified: <yellow>File integrity check failed, this QuickShop build has been modified by others.
  consolespamfix-installed: <yellow>ConsoleSpamFix installed, it will hidden exception details, temporarily disable it during asking support.
  authlib-injector-detected: <yellow>This server running under 3rd-party authlib provider like authlib-injector.
  unsupported-server-software: <yellow>Unsupported server software, Any modded hybrid server software is unsupported including MCPC, Cauldron, CatServer, Mohist, Magma, Fukkit, Cardboard etc.
supertool-is-disabled: <red>Superredskapet är inaktiverat. Du kan inte förstöra någon butik.
unknown-owner: Okänd
restricted-prices: '<red>Begränsat pris för {0}: Minst {1}, högst {2}'
nearby-shop-this-way: <green>Butiken är {0} block från dig.
owner-bypass-check: <yellow>Ignorerade alla checkar. Bytet lyckades! (Du är nu butiksägaren!)
april-rick-and-roll-easter-egg: "<hover:show_text:'Click to open in browser for time limited rewards!'><click:open_url:'https://www.youtube.com/watch?v=dQw4w9WgXcQ'><green>Click here to acquire your time limited rewards that provided by QuickShop-Hikari developer!</green></click></hover>"
signs:
  item-right: ''
  out-of-space: Slut på utrymme
  unlimited: Obegränsat
  stack-selling: Säljer {0}
  stack-price: '{0} per {1}x {2}'
  status-unavailable: <red>
  out-of-stock: Slut i Lager
  stack-buying: Köper {0}
  freeze: Handel inaktiverad
  price: '{0} st'
  buying: Köper {0}
  header: '{1}{0}'
  header-available: <green>{0}
  header-unavailable: <red>{0}
  selling: Säljer {0}
  status-available: <green>
  item-left: ''
negative-amount: <red>You kan inte handla med negativa belopp
display-turn-on: <green>Lyckades aktivera butiksdisplayen.
shop-staff-deleted: <green>Framgångsrikt tagit bort {0} som personal för din butik.
nearby-shop-header: '<green>Närliggande butik som matchar <aqua>{0}<green>:'
backup-failed: Kan inte säkerhetskopiera databasen. Kontrollera konsolen för detaljer.
shop-staff-cleared: <green>Lyckades ta bort all personal från din butik.
price-too-high: <red>Butikspriset är för högt! Du kan inte skapa ett med ett pris högre än {0}.
plugin-cancelled: '<red>Operationen avbruten, Orsak: {0}'
player-sold-to-your-store: <green>{0} sålde {1} {2} till din butik.
shop-out-of-stock: <dark_purple>Din butik vid {0}, {1}, {2} har fått slut på {3}!
how-many-buy: <green>Ange i chatten, hur många du vill <aqua>KÖPA<green>. Du kan köpa <yellow>{0}<green>. Ange <aqua>{1}<green> för att köpa alla.
language-info-panel:
  help: 'Hjälp oss: '
  code: 'Kod: '
  name: 'Språk: '
  progress: 'Förlopp: '
  translate-on-crowdin: '[Översätt på Crowdin]'
item-not-exist: <red>Objektet <yellow>{0} <red>existerar inte, kontrollera din stavning.
shop-creation-failed: <red>Skapandet av butiken misslyckades, kontakta serveradministratören.
inventory-space-full: <red>Your inventory space remains can only put <green>{1}x <red>items into it, try empty the your inventory!
no-creative-break: <red>Du kan inte förstöra andra spelarbutiker i kreativt läge, byt till överlevnadsläge eller försök använda superverktyget {0} istället.
booleanformat:
  success: <green>✔
  failed: <red>✘
controlpanel:
  stack: '<green>Per bulk amount: <aqua>{0} <yellow>[<light_purple><bold>Change<yellow>]'
  price-hover: <yellow>Klicka för att ange ett nytt pris för föremålet.
  remove: <red><bold>[Ta bort butik]
  mode-buying-hover: <yellow>Klicka för att ändra butiken till SÄLJ-läge.
  empty: '<green>Empty: Remove all items <yellow>[<light_purple><bold>OK<yellow>]'
  stack-hover: <yellow>Klicka för att ställa in mängden föremål per paket. Ange 1 för normalt beteende.
  alwayscounting-hover: <yellow>Klicka för att växla om butiken alltid räknar behållarens utrymme.
  alwayscounting: '<green>Always counting: {0} <yellow>[<light_purple><bold>Change<yellow>]'
  setowner: '<green>Owner: <aqua>{0} <yellow>[<light_purple><bold>Change<yellow>]'
  freeze: '<yellow>Freeze mode: <aqua>{0} <yellow>[<light_purple><bold>Toggle<yellow>]'
  price: '<green>Price: <aqua>{0} <yellow>[<light_purple><bold>Change<yellow>]'
  currency-hover: <yellow>Klicka för att ändra eller ta bort den valuta som denna butik använder
  lock: '<yellow>Shop lock: <aqua>{0} <yellow>[<light_purple><bold>Toggle<yellow>]'
  mode-selling: '<green>Butiksläge: <aqua>Säljer <yellow>[<light_purple><bold>Ändra<yellow>]'
  currency: '<green>Currency: <aqua>{0} <yellow>[<light_purple><bold>Set<yellow>]'
  setowner-hover: <yellow>Klicka för att byta ägare.
  mode-buying: '<green>Shop mode: <aqua>Buying <yellow>[<light_purple><bold>Change<yellow>]'
  item: '<green>Shop Item: {0} <yellow>[<light_purple><bold>Change<yellow>]'
  unlimited: '<green>Unlimited: {0} <yellow>[<light_purple><bold>Change<yellow>]'
  unlimited-hover: <yellow>Klicka för att växla huruvida butiken är obegränsad.
  refill-hover: <yellow>Klicka för att fylla på butiken.
  remove-hover: <yellow>Klicka för att ta bort denna butik.
  toggledisplay-hover: <yellow>Växla butikens status för visningsföremålet
  refill: '<green>Refill: Refill the items <yellow>[<light_purple><bold>OK<yellow>]'
  freeze-hover: <yellow>Växla butikens frysningsstatus.
  lock-hover: <yellow>Aktivera/Inaktivera butikens låsskydd.
  item-hover: <yellow>Klicka för att ändra butiksprodukten
  infomation: '<green>Butiksadministration:'
  mode-selling-hover: <yellow>Klicka för att ändra butiken till KÖP-läge.
  empty-hover: <yellow>Klicka för att rensa butikens lager.
  toggledisplay: '<green>Display-Item: <aqua>{0} <yellow>[<light_purple><bold>Toggle</bold></light_purple>]'
  history: '<green>History: <yellow>[<bold><light_purple>View</light_purple></bold>]</yellow>'
  history-hover: <yellow>Click to view shop history logs
timeunit:
  behind: behind
  week: "{0} week"
  weeks: "{0} weeks"
  year: "{0} year"
  before: before
  scheduled: scheduled
  years: "{0} years"
  scheduled-in: scheduled in {0}
  second: "{0} second"
  std-past-format: '{5}{4}{3}{2}{1}{0}ago'
  std-time-format: HH:mm:ss
  seconds: "{0} seconds"
  hour: "{0} hour"
  scheduled-at: scheduled at {0}
  after: efter
  day: "{0} day"
  recent: recent
  between: between
  hours: "{0} hours"
  months: "{0} months"
  longtimeago: long time ago
  between-format: mellan {0} och {1}
  minutes: "{0} minutes"
  justnow: just now
  minute: "{0} minute"
  std-format: MM/dd/yyyy HH:mm:ss
  future-plain-text: framtiden
  month: "{0} month"
  future: in {0}
  days: "{0} days"
command:
  reloading: '<green>Configuration reloaded. <yellow>Some changes may require reboot to affect. <newline><gray>(Notice: Reloading behavior has been changed after ********, we now only reload configuration but not whole plugin to ensure the server won''t crashed.)'
  description:
    buy: <yellow>Ändrar en butik till <light_purple>KÖP<yellow>-läge
    about: <yellow>Visar QuickShop-info
    language: <yellow>Ändra det språk som används för nuvarande
    purge: <yellow>Starta rensningsuppgifter för butiker i bakgrunden
    paste: '&Laddar upp serverdata till Pastebin'
    title: <green>QuickShop-hjälp
    remove: <yellow>Tar bort butiken du tittar på
    ban: <yellow>Bannlyser en spelare från butiken
    empty: <yellow>Tar bort alla föremål från en butik
    alwayscounting: <green>Ange om butiken alltid räknar behållarens kapacitet även om den är obegränsad
    setowner: <yellow>Ändrar ägare av en butik.
    reload: <yellow>Laddar om config.yml för QuickShop
    freeze: <yellow>Inaktivera eller aktivera butikshandel
    price: <yellow>Ändrar köp-/säljpris för en butik
    find: <yellow>Lokaliserar närmsta butik av specifik typ.
    create: <yellow>Skapar en ny butik av aktuella kistan
    lock: <yellow>Växla butikens låsstatus
    currency: <yellow>Ange eller ta bort valutainställningarna i butiken
    removeworld: <yellow>Ta bort ALLA butiker i angiven värld
    info: <yellow>Visa QuickShop-statistik
    owner: <yellow>Ändrar ägare av en butik.
    amount: <yellow>För att ställa in antal (Användbar när du har chattproblem)
    item: <yellow>Ändra föremålet i en butik
    debug: <yellow>Aktiverar utvecklarläge
    unlimited: <yellow>Ger en butik obegränsat lager.
    sell: <yellow>Ändrar en butik till <aqua>SÄLJ<yellow>-läge
    fetchmessage: <yellow>Visa olästa butiksmeddelanden
    staff: <yellow>Hantera din butikspersonal
    clean: <yellow>Tar bort alla (laddade) butiker med tomt lagersaldo
    refill: '&Lägger till ett visst antal föremål i en butik'
    help: <yellow>Visar QuickShop-hjälp
    removeall: <yellow>Ta bort alla butiker från en angiven spelare
    unban: <yellow>Unbannar en spelare från butiken
    transfer: <yellow>Överför alla butiker från en spelare till en annan
    transferall: <yellow>Överför alla butiker från en spelare till en annan
    transferownership: <yellow>Transfer the shop you're looking at to someone else
    size: <yellow>Ändra paketmängd i en butik
    supercreate: <yellow>Skapa en butik genom att kringgå alla skyddskontroller
    taxaccount: <yellow>Ange skattekontot som butiken använder
    name: <yellow>Naming a shop to specific name
    toggledisplay: <yellow>Växla butikens status för visningsobjekt
    permission: <yellow>Shop permission management
    lookup: <yellow>Manage lookup-able items table
    database: <yellow>View and maintain QuickShop database
    benefit: <yellow>Settings of divide benefits between shop owner and other players
    tag: <yellow>Add, remove or query tags of a shop
    suggestprice: <yellow>Suggest a recommend price for looking shop, based on other shops
    history: <yellow>View the history transactions of a shop
    sign: <yellow>Change the sign material of a shop
  bulk-size-not-set: '<red>Kommando: /qs size <antal>'
  no-type-given: '<red>Kommando: /qs find <föremål>'
  feature-not-enabled: Funktionen är inte aktiverad i konfigurationsfilen.
  now-debuging: '&Framgångsrikt aktiverat utvecklarläge. Laddar om QuickShop...'
  no-amount-given: <red>Inget belopp angivet. Använd <green>/qs refill <antal><red>
  no-owner-given: <red>Ingen ägare angiven
  disabled: '<red>Detta kommando är inaktiverat: <yellow>{0}'
  bulk-size-now: <green>Byter nu <yellow>{0}x {1}
  toggle-always-counting:
    counting: <green>Butiken räknar nu alltid behållarens kapacitet även om den är obegränsad
    not-counting: <green>Butiken tar nu hänsyn till huruvida den är obegränsad
  cleaning: <green>Tar bort tomma butiker...
  now-nolonger-debuging: <green>Framgångsrikt inaktiverat utvecklarläge. Laddar om QuickShop...
  toggle-unlimited:
    limited: <green>Butiken är nu begränsad
    unlimited: <green>Butiken är nu obegränsad
  transfer-success-other: <green>Överförde <yellow>{0} {1}<green>'s butik(er) till <yellow>{2}
  no-trade-item: <green>Var god håll en bytesvara i handen för att byta
  wrong-args: <red>Ogiltiga argument. Använd <bold>/qs help <reset>för att se en lista över kommandon.
  some-shops-removed: '&{0} <green>butik(er) borttagen'
  new-owner: '<green>Ny ägare: <yellow>{0}'
  format: <green>/{0} {1} <yellow>- {2}
  transfer-success: <green>Överförde <yellow>{0} <green>butik(er) till <yellow>{1}
  now-buying: <light_purple>KÖPER<green> numera <yellow>{0}
  now-selling: <aqua>SÄLJER <green>numera <yellow>{0}
  cleaned: <green>Tog bort &{0}<green> butiker.
  trade-item-now: <green>Byter nu <yellow>{0}x {1}
  no-world-given: <red>Var god ange ett världsnamn
  format-disabled: <red>/{0} {1} <gray>- {2}
  invalid-bulk-amount: <red>Det angivna värdet {0} är större än max eller lägre än ett
currency-not-support: <red>Ekonomi-pluginet stöder inte flervaluta-funktionen.
trading-in-creative-mode-is-disabled: <red>Du kan inte handla med den här butiken när du är i kreativt läge.
the-owner-cant-afford-to-buy-from-you: <red>Detta kostar {0} men butiksägaren har endast {1}
you-cant-afford-shop-naming: <red>You can't afford shop naming, it costs {0} to naming.
inventory-error: |-
  <red>Failed to processing the InventoryWrapper. Did you using addon to re-binding the shop Inventory? Information: Exception={0}, InventoryWrapper={1}, WrapperType={2}, WrapperProvider={3}, SymbolLink={4}. Please contact the server administrators.
integrations-check-failed-create: <red>Integration {0} nekade butiksskapande
shop-out-of-space: <dark_purple>Din butik vid {0}, {1}, {2} är nu full.
admin-shop: Adminbutik
no-anythings-in-your-hand: <red>Det finns inget i din hand.
no-permission: <red>Du har inte tillåtelse till att göra detta.
chest-was-removed: <red>Kistan har tagits bort.
you-cant-afford-to-buy: <red>Det kostar {0} men du har bara {1}
shops-removed-in-world: <yellow>Totalt <aqua>{0}<yellow> butiker har tagits bort i världen <aqua>{1}<yellow>.
display-turn-off: <green>Lyckades aktivera butiksdisplayen.
client-language-unsupported: <yellow>QuickShop stöder inte ditt klientspråk, vi återgår till {0} språk nu.
language-version: '63'
not-managed-shop: <red>Du är inte ägare eller moderator av denna butik
shop-cannot-trade-when-freezing: <red>Du kan inte handla med denna butik eftersom den är fryst.
invalid-container: <red>Invalid container, You can only create the shop on block who have inventory.
permission:
  header: <green>Shop Permission Details
  header-player: <green>Shop Permission Details for {0}
  header-group: <green>Shop Permission Details for group {0}
  table: <gold><b>{0}</b>:</gold> <gray>{1}</gray>
  item:
    purchase: <yellow>Permission to allow users who have this to purchase the shop. (including buying and selling)
    show-information: <yellow>Permission to allow users who have this to see the shop information. (open shop info panel)
    preview-shop: <yellow>Permission to allow users who have this to preview the shop. (preview item)
    search: <yellow>Permission to allow users who have this to search the target shop. (remove permission will hide shop from search result)
    delete: <yellow>Permission to allow users who have this to delete the shop.
    receive-alert: <yellow>Permission to allow users who have this to receive alert message (e.g out-of-stock or new trade).
    access-inventory: <yellow>Permission to allow users who have this to access the shop inventory.
    ownership-transfer: <yellow>Permission to allow users who have this to transfer the shop ownership.
    management-permission: <yellow>Permission to allow users who have this to manage the groups permissions and edit user's group.
    toggle-display: <yellow>Permission to allow users who have this to toggle the shop display item.
    set-shoptype: <yellow>Permission to allow users who have this to set the shop type (switch buying or selling).
    set-price: <yellow>Permission to allow users who have this to set the shop price.
    set-item: <yellow>Permission to allow users who have this to set the shop item.
    set-stack-amount: <yellow>Permission to allow users who have this to set the shop stack amount.
    set-currency: <yellow>Permission to allow users who have this to set the shop currency.
    set-name: <yellow>Permission to allow users who have this to set the shop name.
    set-sign-type: <yellow>Change the sign material that attached on the shop.
    view-purchase-logs: <yellow>Permission to view shop purchase logs.
  group:
    everyone: <yellow>Default group for all users excepted shop owner.
    staff: <yellow>System group for shop staffs.
    administrator: <red>System group for administrators, the users in this group will have permissions almost same with shop owner.
invalid-group: <red>Invalid group name.
invalid-permission: <red>Invalid permission.
invalid-operation: <red>Invalid operation, only {0} are allowed.
player-no-group: <yellow>Player {0} not in any group in this shop.
player-in-group: <green>Player {0} is in group <aqua>{1}</aqua> in this shop.
permission-required: <red>You don't have permission {0} in this shop to do this.
no-permission-detailed: <red>You don't have permission <yellow>{0}</yellow> to do this.
paste-notice: "<yellow>Note: If you are creating a Paste for troubleshooting purposes, be sure to reproduce the error before creating the Paste as quickly as possible; we need the logs retained briefly in the buffer to troubleshoot. If you create a Paste too slowly or without first reproducing the error or restarted server, the Paste will record nothing and useless."
paste-uploading: <aqua>Please wait... Uploading the paste to pastebin......
paste-created: '<green>Paste created, click to open in browser: <yellow>{0}</yellow><newline><red>Warning: <gray>Never send paste to who you don''t trust.'
paste-created-local: |-
  <green>Paste created and saved to your local disk at: {0}<newline><red>Warning: <gray>Never send paste to who you don't trust.
paste-created-local-failed: <red>Failed to save paste to your local disk, please check your disk.
paste-451: |-
  <gray><b>TIPS: </b> Your current country or region seems to have blocked the CloudFlare Workers service and QuickShop Paste may not be loaded properly.<newline><gray>If the subsequent operation fails, try adding the --file additional parameter to generate a local Paste: <dark_gray>/qs paste --file</dark_gray>
paste-upload-failed: <red>Failed to upload paste to {0}, trying other pastebin provider...
paste-upload-failed-local: <red>Failed to upload paste, trying generate local paste...
command-incorrect: '<red>Command usage incorrect, type /qs help to check help. Usage: {0}.'
successfully-set-player-group: <green>Successfully set player {0} group to <aqua>{1}</aqua>.
successfully-unset-player-group: <green>Successfully remove player group in this shop.
successfully-set-player-permission: <green>Successfully set player {0} permission <aqua>{1}</aqua> in shop <aqua>{2}</aqua>.
lookup-item-created: <green>An item named <aqua>{0}</aqua> has been created in the lookup table. You can refer this item in configurations now.
lookup-item-exists: <red>An item named <yellow>{0}</yellow> already exists in the lookup table, delete it or pick another name.
lookup-item-not-found: <red>An item named <yellow>{0}</yellow> not exists.
lookup-item-name-illegal: <red>Item name illegal. Only alphanumeric characters and underscores are allowed.
lookup-item-name-regex: '<red>The name must match this regex: <aqua>{0}</aqua>.'
lookup-item-test-not-found: '<gold>Test: <yellow>The item in your hand not registered in lookup table.'
lookup-item-test-found: '<gold>Test: <green>The item in your hand was registered as name <aqua>{0}</aqua> in lookup table.'
lookup-item-removed: <green>The specified item <aqua>{0}</aqua> has been successfully removed from lookup table.
internal-error: <red>An internal error occurred, please contact the server administrator.
argument-cannot-be: <red>The argument <aqua>{0}</aqua> cannot be <yellow>{1}</yellow>.
argument-must-between: <red>The argument <aqua>{0}</aqua> value must between <yellow>{1}</yellow> and <yellow>{2}</yellow>
invalid-percentage: <red>Percentage invalid, you must add '%' after the number of percentage.
display-fallback: |-
  <red>Due to an internal failure, a fallback message is being displayed.
  The value of this item may not be localized or processed correctly.
  Please contact the server administrator.
not-a-valid-time: |-
  <red>The string <yellow>{0}</yellow> not a valid timestamp, please enter a <aqua>Zulu Time (ISO 8601)</aqua> or <aqua>Unix Epoch Time in seconds</aqua>.
  <gold>Valid timestamp example: (for Sat, 17 Dec 2022 10:31:37 GMT)</gold>
  <aqua>-<aqua> <yellow>2022-12-17T10:31:37Z</yellow> <grey>(Zulu Time)</grey>
  <aqua>-<aqua> <yellow>1671273097</yellow> <grey>(Unix Epoch Time in seconds)</grey>
invalid-past-time: <red>You can't specify a time in the past.
debug:
  arguments-invalid: <red>Provided arguments <yellow>{0}</yellow> invalid.
  sign-located: '<green>Sign valid: <yellow>{0}</yellow>.'
  operation-missing: <red>You must specific an operation.
  operation-invalid: <red>You must specific an valid operation.
  invalid-base64-encoded-sql: <red>The SQL provided must be base64 encoded.
  warning-sql: |-
    <red><bold>Warning:</bold></red><yellow>You're executing a SQL statement, this may corrupt your database or destroy any data in database even it belongs to other plugins.
    <red>Don't confirm if you don't trust who send you this command.
  warning-sql-confirm: <yellow>To confirm this dangerous operation, please type <aqua>/qs debug database sql confirm {0}</aqua> in 60 seconds.
  warning-sql-confirm-hover: <yellow>Click to confirm this dangerous operation.
  sql-confirm-not-found: <yellow>Didn't found the operation that you given, it may invalid or expired.
  sql-executing: '<yellow>Executing SQL statement: <aqua>{0}</aqua>'
  sql-completed: <green>Completed, {0} rows affected.
  sql-exception: <red>An error occurred while executing the SQL query, check the Console for details!
  sql-disabled: '<red>For security reason, SQL queries are disabled in this server, If you really need this feature, you can add the following flag to startup arguments: <aqua>{0}</aqua> with `true` value to enable it.'
  force-shop-reload: <yellow>Forcing reload all loaded shops...
  force-shop-reload-complete: <green>Forced <aqua>{0}</aqua> shops to be reloaded.
  force-shop-loader-reload: <yellow>Forcing shop-loader reloading...
  force-shop-loader-reload-unloading-shops: <yellow>Unloading <aqua>{0}</aqua> loaded shops...
  force-shop-loader-reload-unloading-shops-from-memory: <yellow>Removing <aqua>{0}</aqua> shops from memory...
  force-shop-loader-reload-reloading-shop-loader: <yellow>Re-calling shop-loader for reloading all shops from database...
  force-shop-loader-reload-complete: <green>Shop-loader has been reloaded all shops!
  toggle-shop-loaded-status: <aqua>Toggle this shop loaded status to <gold>{0}</gold>
  shop-internal-data: '<yellow>The internal data of this shop: <reset>{0}</reset>'
  handler-list-not-valid-bukkit-event-class: <red>The provided class <yellow>{0}</yellow> is not a valid Bukkit event class.
  update-player-shops-signs-no-username-given: <red>You must provide a valid player username.
  update-player-shops-signs-create-async-task: <yellow>Creating async tasks for force updating signs...
  update-player-shops-player-selected: '<yellow>Player selected: <gold>{0}</gold>'
  update-player-shops-player-shops: <yellow>Total <gold>{0}</gold> shops waiting for updates.
  update-player-shops-per-tick-threshold: '<yellow>Max shops can be update per tick: <gold>{0}</gold>'
  update-player-shops-complete: <green>Task completed, Used <yellow>{0}ms</yellow> for updating.
  update-player-shops-task-started: <gold>The tasks has been started, please wait for it to complete.
  item-info-store-as-string: "<green>The store you looking at: <gold>{0}</gold> Hash: <white>{1}</white>"
  item-info-hand-as-string: "<green>The item in your hand: <gold>{0}</gold> Hash: <white>{1}</white>"
  item-matching-result: "<green>Hand2Store: <aqua>{0}</aqua>, Store2Hand: <aqua>{1}</aqua>"
  hikari-cp-size-tweak: "<green>HikariCP MaximumPoolSize and MinimumIdle was set to <white>{0}</white>"
  hikari-cp-testing: "<green>Please wait, testing HikariCP connection..."
  hikari-cp-working: "<green>Pass! HikariCP working well!"
  hikari-cp-not-working: "<red>Failed! The connection that returned by HikariCP is dead! (Not passed test in 1 second)"
  hikari-cp-timeout: "<red>HikariCP is timed out while getting a valid connection, please clean up all active queries to release connection resources."
  queries-stopped: "<green>Stopped <white>{0}</white> active queries."
  queries-dumping: "<yellow>Dumping active queries..."
  restart-database-manager: "<yellow>Restarting SQLManager..."
  restart-database-manager-clear-executors: "<yellow>Clearing executors..."
  restart-database-manager-unfinished-task: "<yellow>Unfinished task: <white>{0}</white>"
  restart-database-manager-unfinished-task-history-query: "<yellow>Unfinished task (History Query): <white>{0}</white>"
  restart-database-manager-reconnect: "<yellow>Re-launching SQLManager via initial sequence (via async executor)"
  restart-database-manager-done: "<green>Done!"
  property-incorrect: "<yellow>You must enter a (and only one) property key=value set. E.g   aaa=bbb"
  property-security-block: "<red>Request was rejected, for security reason, you can only change the property that startsWith <aqua>com.ghostchu.quickshop</aqua> or <aqua>quickshop</aqua>."
  property-removed: "<green>Removed property key <white>{0}</white>"
  property-changed: "<green>Property key <white>{0}</white> was changed from <white>{1}</white> to <white>{2}</white>"
  marked-as-dirty: "<green>Marked all stores as dirty status, they will be force save in next auto-save task. (Restart the server to force execute store saving task)"
  display-removed: "<green>Successfully removed <yellow>{0}</yellow> QuickShop display items/entities from the worlds."
database:
  scanning: <green>Scanning the isolated data in QuickShop databases, the database load may increase in scan progress, this might need a while...
  scanning-async: <yellow>Scanning the isolated data in QuickShop databases on async task thread, the database load may increase in scan progress, this might need a while, try again later.
  already-scanning: <red>An scan task has been started, please wait until it finished.
  trim-warning: '<yellow><red><bold>Warning: </red>Backup your database before continue database trim to avoid the data loss. Once you get ready, execute <aqua>/qs database trim confirm</aqua> to continue.'
  status: '<yellow>Status: {0}'
  status-good: <green>Good
  status-bad: <yellow>Maintenance Required
  isolated: '<yellow>Isolated Data:'
  isolated-data-ids: '<yellow><aqua>└</aqua> Data Records: <gold>{0}</gold>'
  isolated-shop-ids: '<yellow><aqua>└</aqua> Shop Indexes: <gold>{0}</gold>'
  isolated-logs: '<yellow><aqua>└</aqua> Logs: <gold>{0}</gold>'
  isolated-external-caches: '<yellow><aqua>└</aqua> External Caches: <gold>{0}</gold>'
  last-purge-time: <yellow>Last time of trim at {0}
  report-time: <yellow>Last time of scan at {0}
  auto-scan-alert: <yellow>The QuickShop database on this server require a maintenance. Found <gold>{0}</gold> isolated data wait for trimming.
  auto-trim: <green>Auto trim has been enabled on this server, no manually trim required.
  trim-complete: <green>Database trim completed, <yellow>{0}</yellow> isolated trimmed.
  auto-trim-started: <green>Auto trim has been started, please wait...
  trim-start: <green>Database trim started, please wait...
  trim-exception: <red>Database trim failed, an exception raised during database trimming, check the server console.
  generated-at: '<yellow>Generated at: <gold>{0}</gold>'
  purge-date: <red>You must given a date.
  purge-warning: <yellow>This operation will purge the history that stored in your database including shop creates/changes/deletes, purchases, transactions and system logs. Delete those data can free the disk space but all history metric will lost, and other plugins that depend on metrics will stop working. To continue execute this operation, execute `/qs database purgelogs <before-days> confirm`
  purge-task-created: <green>Task created! Database are silent purging history records in background.
  purge-done-with-line: <green>Purge task completed, total <gold>{0}</gold> lines purged from database.
  purge-done-with-error: <red>Purge task failed, check the server console for details.
  purge-players-cache: <yellow>Please wait, purging players caches...
  purge-players-completed: |-
    <green>Successfully purged {0} players caches from both memory and database.<newline><aqua>Note: You server performance may impact by this operation.
  purge-players-error: <red>Failed to purge players caches, please check the server console.
  suggestion:
    trim: <yellow>This database require a isolated data trim, execute command <aqua>/qs database trim</aqua> to trim the database.
always-counting-removal-early-warning: <red>Always Counting feature is scheduled for removal, you shouldn't use it anymore and will stop work in future.
exporting-database: <green>Exporting database, please wait...
exporting-failed: <red>Failed to export database, please check the server console.
exported-database: <green>Database exported to <yellow>{0}</yellow>.
importing-not-found: <red>The <yellow>{0}</yellow> file not found, please check the file path.
importing-early-warning: |-
  <yellow><red><bold>Warning: </bold></red>The backup will be imported to the current database, any exists data will be purged and permanently lost if you don't have a backup.<newline><red>Are you sure to continue the import progress?</red><yellow>Type <aqua>/qs recovery confirm</aqua> to continue.
importing-database: <green>Importing database from backup, please wait...
importing-failed: <red>Failed to import database, please check the server console.
imported-database: <green>Database imported from <yellow>{0}</yellow>.
transfer-sent: <green>Shops transfer request sent to <yellow>{0}</yellow>.
transfer-request: <yellow>Player <aqua>{0}</aqua> requested to transfer their shops to you. Did you want accept the transfer?
transfer-single-request: <yellow>Player <aqua>{0}</aqua> would like to transfer a shop to you. Do you want to accept the request?
transfer-ask: |-
  <gold>Type <red>/qs transfer accept</red> to accept.</gold><newline><gold>Type <red>/qs transfer deny</red> to deny.</gold><newline><gold>Request will expire after <red>{0}</red> seconds.
transferall-ask: |-
  <gold>Type <red>/quickshop transferall accept</red> to accept or <red>/quickshop transferall deny</red> to deny.
  The request will expire after <red>{0}</red> seconds.
transfer-single-ask: |-
  <gold>Type <red>/quickshop transferownership accept</red> to accept or <red>/quickshop transferownership deny</red> to deny.
  The request will expire after <red>{0}</red> seconds.
transfer-accepted-fromside: <green>Player <aqua>{0}</aqua> accepted your shop transfer request.
transfer-accepted-toside: <green>You accepted <aqua>{0}</aqua>'s transfer request.
transfer-rejected-fromside: <red>Player <aqua>{0}</aqua> rejected your shop transfer request.
transfer-rejected-toside: <red>You rejected <aqua>{0}</aqua>'s shop transfer request.
transfer-no-pending-operation: <red>You don't have pending transfer request yet.
transfer-no-self: <red>You can't transfer your shops to yourself.
benefit-overflow: <red>The sum of all benefits cannot be greater than or equal to 100%.
benefit-exists: <red>Target player already in benefits list on this shop.
benefit-removed: <red>Target player has been removed from shop benefits.
benefit-added: <green>Player <aqua>{0}</aqua> has been added into shop benefits!
benefit-updated: <green>Player <aqua>{0}</aqua>'s benefits has been updated!
benefit-query: <green>This shop have <yellow>{0}</yellow> players in benefits list!
benefit-query-list: <yellow> - </yellow><white>Player <gold>{0}</gold>, Benefit <gold>{1}%</gold></white>
tag-added: '<green>Successfully added <aqua>#{0}</aqua> to this shop!'
tag-add-duplicate: '<red>The tag <aqua>#{0}</aqua> already exists at this shop!'
tag-removed: '<green>Successfully removed <aqua>#{0}</aqua> from this shop!'
tag-remove-not-exists: 'The tag <aqua>#{0}</aqua> not exists at this shop!'
tag-cleared: <green>Successfully cleared all tags from this shop!
tag-shops-cleared: '<green>Successfully cleared <aqua>#{0}</aqua> from all your tagged shops!'
tag-query: '<green>This shop have <yellow>{0}</yellow> tags:'
tag-query-listing: '<yellow> - <aqua>#{0}</aqua>'
tag-query-no-tag: <red>This shop have no tags.
tag-query-shops: '<green>This tag contains <yellow>{0}</yellow> shops:'
tag-query-shops-listing: <yellow> - <aqua>{0}</aqua>
batch-operations-based-on-tags-no-failure: <green>Successfully batch processed <yellow>{0}</yellow> shops.
batch-operations-based-on-tags-have-failure: <yellow>Total <yellow>{0}</yellow> shops in batch processing successfully completed, but <red>{1}</red> requests not completed.
batch-operations-based-on-tags-have-failure-with-reason: '<yellow>Total <yellow>{0}</yellow> shops in batch processing successfully completed, but <red>{1}</red> requests not completed. Reason: <gold>{2}</gold>'
batch-operations-based-on-tags-have-failure-error-messages-may-appears-above: <gray>The error messages will send to you above this message in the chat.
addon:
  towny:
    commands:
      town: <yellow>Set or unset a shop to a Town shop
      nation: <yellow>Set or unset a shop to a Nation shop
    make-shop-owned-by-town: <green>You have made the shop owned by town <yellow>{0}</yellow>.
    make-shop-no-longer-owned-by-town: <green>You have reset shop ownership, it now transfer back to original shop owner.
    make-shop-owned-by-nation: <green>You have made the shop owned by nation <yellow>{0}</yellow>.
    make-shop-no-longer-owned-by-nation: <green>You have reset shop ownership, it now transfer back to original shop owner.
    shop-owning-changing-notice: <grey>This shop now owned by a town/nation, original shop owner has been automatically added into administrator list, modify or add any new owner / staff by using command /qs permission
    target-shop-already-is-town-shop: <red>The target shop is already owned by a town.
    target-shop-already-is-nation-shop: <red>The target shop is already owned by a nation.
    target-shop-not-in-town-region: <red>The target shop is not in the town.
    target-shop-not-in-nation-region: <red>The target shop is not in the nation.
    item-not-allowed: <red>This shop's item are not allowed used by town/nation shop, pick another one!
    operation-disabled-due-shop-status: <red>This shop operation has been disabled due this already is a town/nation shop.
    plot-type-disallowed: <red>You can't create town/nation shop on this type of plot.
    flags:
      own: <red>You can only create shops at your owned shop type towny plot.
      modify: <red>You didn't have build permission on this towny plot.
      shop-type: <red>You must create shop on a shop type towny plot.
  residence:
    creation-flag-denied: <red>You don't have permission to create shops in this residence.
    trade-flag-denied: <red>You don't have permission to purchase in this residence.
    you-cannot-create-shop-in-wildness: <red>You can't create shop in wildness.
  griefprevention:
    creation-denied: <red>You don't have permission to create shops in this claim.
    trade-denied: <red>You don't have permission to purchase shops in this claim.
  lands:
    world-not-enabled: <red>You are not allowed to create or purchase in this world.
    creation-denied: <red>You don't have permission to create shops in this Land area.
  plotsquared:
    no-plot-whitelist-creation: <red>You cannot create shop outside of Plot.
    no-plot-whitelist-trade: <red>You cannot purchase shop outside of Plot.
    creation-denied: <red>You don't have permission to create shops in this Plot.
    trade-denied: <red>You don't have permission to purchase shops in this Plot.
    flag:
      create: Create QuickShop-Hikari shops
      trade: Purchase QuickShop-Hikari shops
  superiorskyblock:
    owner-create-only: <red>Only island owner can create shop there.
    owner-member-create-only: <red>Only island owner or member can create shop there.
  worldguard:
    creation-flag-test-failed: <red>You don't have permission create shop in this WorldGuard area.
    trade-flag-test-failed: <red>You don't have permission trade shop in this WorldGuard area.
    reached-per-region-amount-limit: "<red>Du kan inte ha fler butiker i detta område."
  discord:
    __to_translators: 'To translators: `addon.discord.discord-messages` use a special variable in strings, %%LANG_KEY%% instead of QuickShop''s style {LANG_INDEX}, do not translate anything inside double ''%'' section. BUT for any other section texts, it respect the normal QuickShop Text System syntax ({0},{1} etc.) This may be difficult to understand because two different variable forms are used in the same program at the same time, but trust me, it is reasonable. (you don''t need translate this string, it''s a comment)'
    __to_users: Like the QuickShop Text System, Discord addon will also auto-detect user language and use user's language to send Discord message, it following the QuickShop-Hikari's language system settings.
    __to_message_designer: 'Design your Discord message with GUI: https://glitchii.github.io/embedbuilder/, then copy the JSON code and paste it into the translation and here we go!'
    discord-enabled: <aqua>Successfully <green>enabled</green> your QuickShop discord messages, now you can receive the shop messages from Discord.
    discord-disabled: <aqua>Successfully <red>disabled</red> your QuickShop discord messages, now you won't receive the shop messages from Discord anymore.
    discord-not-integrated: <red>You hadn't link your Discord yet! Please link your Discord account first!
    feature-enabled-for-user: <aqua>You have <green>enabled</green> <gold>{0}</gold> notification.
    feature-disabled-for-user: <aqua>You have <red>disabled</red> <gold>{0}</gold> notification.
    link-help: <yellow>This server using <gold>{0}</gold> for Discord driver, please use <green>{0}</green> to link your Discord account.
    save-notifaction-exception: <red>An error occurred while saving your Discord notifaction settings, please contact the server administrator.
    feature-status-changed: <green>Successfully set the notifaction <aqua>{0}</aqua> status to <gold>{1}</gold>
    commands:
      discord:
        description: <yellow>Managing your QuickShop Discord settings
    discord-messages:
      sold-to-your-shop: |
        {
         "embed":
           {
             "title": ":inbox_tray: Somebody sold items to your shop",
             "description": "The player %%purchase.name%% sold x%%purchase.amount%% %%shop.item.name%% to your shop.",
             "color": 52084,
             "author": {
               "name": "QuickShop-Hikari",
               "icon_url": ""
             },
             "thumbnail": {
               "url": ""
             },
             "image": {
               "url": ""
             },
             "footer": {
               "text": "QuickShop-Hikari Discord Notification",
               "icon_url": ""
             },
             "fields": [
               {
                 "name": "Shop",
                 "value": "%%shop.display-name%%",
                 "inline": false
               },
               {
                 "name": "Purchaser",
                 "value": "%%purchase.name%%",
                 "inline": true
               },
               {
                 "name": "Item",
                 "value": "%%shop.item.name%%",
                 "inline": true
               },
               {
                 "name": "Amount",
                 "value": "%%purchase.amount%% (x%%shop.stacking-amount%%)",
                 "inline": true
               },
               {
                 "name": "Your paid",
                 "value": "%%purchase.balance-formatted%%",
                 "inline": true
               },
               {
                 "name": "Taxes",
                 "value": "%%purchase.taxes-formatted%%",
                 "inline": true
               }
             ]
           }
        }
      bought-from-your-shop: |
        {
           "embed":
             {
               "title": ":outbox_tray: Somebody bought items from shop",
               "description": "The player %%purchase.name%% bought x%%purchase.amount%% %%shop.item.name%% from your shop.",
               "color": 52084,
               "author": {
                   "name": "QuickShop-Hikari",
                   "icon_url": ""
               },
               "thumbnail": {
                   "url": ""
               },
               "image": {
                   "url": ""
               },
               "footer": {
                   "text": "QuickShop-Hikari Discord Notification",
                   "icon_url": ""
               },
               "fields": [
                   {
                       "name": "Shop",
                       "value": "%%shop.display-name%%",
                       "inline": false
                   },
                   {
                       "name": "Purchaser",
                       "value": "%%purchase.name%%",
                       "inline": true
                   },
                   {
                       "name": "Item",
                       "value": "%%shop.item.name%%",
                       "inline": true
                   },
                   {
                       "name": "Amount",
                       "value": "%%purchase.amount%%(x%%shop.stacking-amount%%)",
                       "inline": true
                   },
                   {
                       "name": "Your earn",
                       "value": "%%purchase.balance-formatted%%",
                       "inline": true
                   },
                   {
                       "name": "Taxes",
                       "value": "%%purchase.taxes-formatted%%",
                       "inline": true
                   }
               ]
           }
         }
      mod-shop-purchase: |
        {
        "embed":
          {
              "title": ":dollar: Somebody purchased in a shop",
              "description": "The player %%purchase.name%% purchase x%%purchase.amount%% %%shop.item.name%% from the shop.",
              "color": 6061450,
              "author": {
                  "name": "QuickShop-Hikari",
                  "icon_url": ""
              },
              "thumbnail": {
                  "url": ""
              },
              "image": {
                  "url": ""
              },
              "footer": {
                  "text": "QuickShop-Hikari Discord Notification",
                  "icon_url": ""
              },
              "fields": [
                  {
                      "name": "Shop",
                      "value": "%%shop.display-name%%",
                      "inline": false
                  },
                  {
                      "name": "Purchaser",
                      "value": "%%purchase.name%%",
                      "inline": true
                  },
                  {
                      "name": "Item",
                      "value": "%%shop.item.name%%",
                      "inline": true
                  },
                  {
                      "name": "Amount",
                      "value": "%%purchase.amount%% (x%%shop.stacking-amount%%)",
                      "inline": true
                  },
                  {
                      "name": "Balance",
                      "value": "%%purchase.balance-formatted%%",
                      "inline": true
                  },
                  {
                      "name": "Taxes",
                      "value": "%%purchase.taxes-formatted%%",
                      "inline": true
                  }
              ]
          }
        }
      out-of-space: |
        {
        "embed":
          {
              "title": ":mailbox_with_mail: Your shop has run out of space",
              "description": "Your shop inventory is full of items!\nYou need empty your shop container to free the space then you can continue to accept new items.",
              "color": 16065893,
              "author": {
                  "name": "QuickShop-Hikari",
                  "icon_url": ""
              },
              "thumbnail": {
                  "url": ""
              },
              "image": {
                  "url": ""
              },
              "footer": {
                  "text": "QuickShop-Hikari Discord Notification",
                  "icon_url": ""
              },
              "fields": [
                  {
                      "name": "Shop",
                      "value": "%%shop.display-name%%",
                      "inline": false
                  }
              ]
          }
        }
      out-of-stock: |
        {
            "embed":
                {
                    "title": ":mailbox_with_no_mail: Your shop has run out of stock",
                    "description": "Your shop inventory is empty now!\nYou need to refill the shop container with items to continue selling items!",
                    "color": 16065893,
                    "author": {
                        "name": "QuickShop-Hikari",
                        "icon_url": ""
                    },
                    "thumbnail": {
                        "url": ""
                    },
                    "image": {
                        "url": ""
                    },
                    "footer": {
                        "text": "QuickShop-Hikari Discord Notification",
                        "icon_url": ""
                    },
                    "fields": [
                        {
                            "name": "Shop",
                            "value": "%%shop.display-name%%",
                            "inline": false
                        }
                    ]
                }
        }
      mod-shop-created: |
        {
          "embed": {
            "title": ":classical_building: A new shop has been created",
            "description": "Player created a new shop on your server!",
            "color": 6061450,
            "author": {
              "name": "QuickShop-Hikari",
              "icon_url": ""
            },
            "thumbnail": {
              "url": ""
            },
            "image": {
              "url": ""
            },
            "footer": {
              "text": "QuickShop-Hikari Discord Notification",
              "icon_url": ""
            },
            "fields": [
              {
                "name": "Shop",
                "value": "%%shop.display-name%%",
                "inline": false
              },
              {
                "name": "Owner",
                "value": "%%shop.owner.name%%",
                "inline": true
              },
              {
                "name": "Item",
                "value": "%%shop.item.name%%",
                "inline": true
              },
              {
                "name": "Amount",
                "value": "%%shop.stacking-amount%%",
                "inline": true
              },
              {
                "name": "Type",
                "value": "%%shop.type%%",
                "inline": true
              }
            ]
          }
        }
      mod-remove-shop: |
        {
            "embed": {
                "title": ":recycle: A shop was removed from this server",
                "description": "A shop was removed from this server.",
                "color": 6061450,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "Reason",
                        "value": "%%delete.reason%%",
                        "inline": false
                    }
                ]
            }
        }
      shop-transfer-to-you: |
        {
            "embed": {
                "title": ":placard: A shop was transfer to you",
                "description": "A shop transfer to you from another player.",
                "color": 16172079,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "From",
                        "value": "%%transfer.from%%",
                        "inline": false
                    }
                ]
            }
        }
      mod-shop-transfer: |
        {
            "embed": {
                "title": ":placard: A shop was transferred",
                "description": "A shop transfer to a player from another player.",
                "color": 6061450,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "From",
                        "value": "%%transfer.from%%",
                        "inline": true
                    },
                    {
                        "name": "To",
                        "value": "%%transfer.to%%",
                        "inline": true
                    }
                ]
            }
        }
      shop-price-changed: |
        {
            "embed": {
                "title": ":money_with_wings: Your shop price was changed",
                "description": "You or your shop staff changed your shop price.",
                "color": 16172079,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "From",
                        "value": "%%change-price.from%%",
                        "inline": true
                    },
                    {
                        "name": "To",
                        "value": "%%change-price.to%%",
                        "inline": true
                    }
                ]
            }
        }
      mod-shop-price-changed: |
        {
            "embed": {
                "title": ":money_with_wings: A shop price has been changed",
                "description": "A shop changed its price settings.",
                "color": 6061450,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": true
                    },
                    {
                        "name": "Owner",
                        "value": "%%shop.owner.name%%",
                        "inline": true
                    },
                    {
                        "name": "",
                        "value": "",
                        "inline": true
                    },
                    {
                        "name": "From",
                        "value": "%%change-price.from%%",
                        "inline": true
                    },
                    {
                        "name": "To",
                        "value": "%%change-price.to%%",
                        "inline": true
                    }
                ]
            }
        }
      shop-permission-changed: |
        {
            "embed": {
                "title": ":closed_lock_with_key: Your shop permission settings has been changed",
                "description": "One of your shop's permission setting has been changed.",
                "color": 15879747,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "Player",
                        "value": "%%change-permission.player%%",
                        "inline": true
                    },
                    {
                        "name": "Assign to group",
                        "value": "%%change-permission.to-group%%",
                        "inline": true
                    },
                    {
                        "name": "",
                        "value": "",
                        "inline": true
                    },
                    {
                        "name": "Permission granted (Inherit from group)",
                        "value": "```\n%%change-permission.perms-list%%\n```",
                        "inline": false
                    }
                ]
            }
        }
    field:
      player: Player
      item: Item
      amount: Amount
      balance: Balance
      balance-after-tax: Balance (after tax)
      account: Your account balance
      taxes: Taxes
      cost: Cost
  discount:
    commands:
      discount:
        description: <yellow>Apply a discount code or manage your discount codes.
    tab-complete:
      discount:
        general:
          code: <code>
        create:
          rate: |
            Command Hint:
            Argument: <rate>
            Description: The actual percentage or money you will earn
            Input `30%` = price * 0.3
            Input `50` = price - 50
          max-usage: |
            Command Hint:
            Argument: [max-usage]
            Description: The times that code can be used
            `-1` for unlimited.
          threshold: |
            Command Hint:
            Argument: [threshold]
            Description: The minimum price that code can be applied
            `-1` for unlimited
          expired: |
            Command Hint
            Argument: [expired]
            Description: The code expired time.
            `-1` for unlimited duration.
            Accept both Zulu time and UNIX timestamp in seconds.
            Zulu Example: 2022-12-17T10:31:37Z
            UNIX Example: 1671273097
    discount-code-already-exists: <red>Sorry, the name of your discount code already in use.
    invalid-discount-code-regex: '<red>The discount code must matches the regex: <yellow>{0}</yellow>'
    invalid-discount-code: <red>This discount code are invalid.
    discount-code-added: <green>Your discount code <yellow>{0}</yellow> has been added into shop <aqua>{1}</aqua>
    discount-code-removed: <green>Your discount code <yellow>{0}</yellow> has been removed from shop <aqua>{1}</aqua>
    invalid-code-type: <red>The code type <yellow>{0}</yellow> are invalid.
    invalid-usage-restriction: <red>The usage restriction <yellow>{0}</yellow> are invalid.
    invalid-threshold-restriction: <red>The threshold restriction <yellow>{0}</yellow> are invalid.
    invalid-effect-scope: <red>The scope <yellow>{0}</yellow> are invalid.
    invalid-expire-time: <red>You can't specify a time in the past.
    invalid-discount-rate: <red>The discount rate <yellow>{0}</yellow> are invalid, it can be a fixed number or percentage.
    discount-code-expired: <red>Ouch! Your discount code <yellow>{0}</yellow> has been expired!
    discount-code-installed: <green>You have installed discount code <gold>{0}</gold>, the discount code will be automatically applied to the available purchase in all the following purchases during this session. To uninstall discount code, execute <aqua>/qs discount uninstall {0}</aqua>.
    discount-code-uninstalled: <green>You have uninstalled your discount code.
    discount-code-query-nothing: <red>You hadn't install a discount code yet!
    discount-code-query: '<green>You are using discount code: <yellow>{0}</yellow>.'
    discount-code-applicable: '<color:#bcef26>Your discount code <yellow><bold>{0}</bold></yellow> are <bold>applicable</bold> in this shop!'
    discount-code-applicable-with-threshold: '<color:#bcef26>Your discount code <yellow><bold>{0}</bold></yellow> are <bold>applicable</bold> in this shop, but only your purchase cost over <yellow>{1}</yellow> in single transaction.'
    discount-code-not-applicable: <red>Your discount code <yellow><bold>{0}</bold></yellow> are <bold>not applicable</bold> in this shop!
    discount-code-reach-the-limit: <red>You have reach the usage limit of discount code <yellow><bold>{0}</bold></yellow>, discount are not applied.
    discount-code-no-permission: <red>You didn't have permission to use any discount code in this shop!
    discount-code-has-been-expired: <red>Your discount has been expired!
    discount-code-config-shop-added: <green>Successfully to add this shop into your discount code allow list.
    discount-code-config-shop-add-failure: <red>This shop already in your discount code allow list.
    discount-code-config-shop-removed: <green>Successfully to remove this shop from your discount code allow list.
    discount-code-config-shop-remove-failure: <red>This shop not exists in your discount code allow list.
    discount-code-config-expire: <green>Successfully changed your code expire time.
    discount-code-config-applied: <green>Config the discount code successfully!
    discount-code-created-successfully: |
      <green>Your discount code <yellow>{0}</yellow> has been successfully created!
      <gold>Scope: <aqua>{1}</aqua></gold>.
      <gold>Share with others: <color:#bcef26>{2}</color:#bcef26></gold>.
      <yellow>For <gray>Specific Shops</gray> scope only: To add shops to your discount code allow list, please looking an quickshop and execute <aqua>{3}</aqua> command.
      <yellow>You can use <aqua>/qs discount config {0}</aqua> to edit your discount code anytime.
    discount-code-under-threshold: <red>The discount not applied to your purchase because the total value under the discount code threshold <yellow>{0}</yellow>.
    percentage-off: '<bold><color:#bcef26>-{0}%</color:#bcef26></bold>'
    fixed-off: '<bold><color:#bcef26>-{0}</color:#bcef26></bold>'
    discount-code-list: '<gold>Listing your discount codes:</gold>'
    discount-code-applied-in-purchase: '<color:#bcef26>Your discount code <yellow>{0}</yellow> has been applied to this purchase and you saved <gold>{1}</gold>!'
    scope:
      this-shop: '{0}'
      your-shops-owned: all your shops (owned)
      your-shops-managed: all your shops (managed)
      server: whole server
    code-type:
      SERVER_ALL_SHOPS: All shops on this server
      PLAYER_ALL_SHOPS: All shops that created by code owner
      SPECIFIC_SHOPS: Specific shops
    discount-code-details: |-
      <gold>Code: <yellow>{0}</yellow>
      <gold>Creator: <yellow>{1}</yellow>
      <gold>Applied to: <yellow>{2}</yellow>
      <gold>Remaining usage: <yellow>{3}</yellow>
      <gold>Expired on: <yellow>{4}</yellow>
      <gold>Threshold: <yellow>{5}</yellow>
      <gold>Discount: <yellow>{6}</yellow>
  list:
    commands:
      list: <yellow>Listing all shops owned by your or specific player
    table-prefix: '<yellow><green>{0}</green>''s shops <gray>(Total {1})</gray>: '
    table-prefix-pageable: '<yellow><green>{0}</green>''s shops <gray>(Page {1}/{2})</gray>: '
    entry: "<hover:show_text:'<yellow>{1}</yellow>\n<gray>{2} {3},{4},{5}</gray>\n<aqua>Price <yellow>{6}</yellow> - <light_purple>x{7}</light_purple> <yellow>{8}</yellow>\n<green>{9}'><yellow>{0}. <aqua>{1}</aqua></hover>"
  shopitemonly:
    message: <red>You can't put non-shop items into shop container, all non-shop items will dropped at your location.
  limited:
    commands:
      limit: <yellow>Set a limit that restrict the purchase that player can make in period
    titles:
      title: <green>Purchase Success
      subtitle: <aqua>You can also purchase <gold>{0}</gold> more in this shop
    reach-the-limit: <red>You have reached the limit in this shop. You can also purchase <green>{0}</green> items, but you trying purchase <yellow>{1}</yellow> items.
    success-reset: <green>Reset the restriction of this shop successfully
    success-remove: <green>Successfully remove the all restriction of this shop
    success-setup: <green>Restriction settings for this shop were saved successfully
    trade-limit-reached-cancel-reason: <red>Reached the restriction of this shop
    remains-limits: '<gold>Your remaining purchase quota in this shop: <yellow>{0}</yellow>'
    remains-limits-before-reset-period-note: '<gold>This shop will reset their purchase quota every: <yellow>{0}</yellow>'
  dynmap:
    markerset-title: QuickShop-Hikari Shops
    marker-name: '{0} @ {2} @ {3} for x{4} @ {5}'
    marker-description: |
      Name: {0}
      Owner: {1}
      Item: {2}
      Price: {3} for x{4} item(s)
      Type: {5}
      Unlimited: {6}
      Location: {7}
  bluemap:
    markerset-title: QuickShop-Hikari Shops
    marker-name: '{0} @ {2} @ {3} for x{4} @ {5}'
    marker-description: |
      Name: {0}
      Owner: {1}
      Item: {2}
      Price: {3} for x{4} item(s)
      Type: {5}
      Unlimited: {6}
      Location: {7}
  chestprotect:
    protection-exists: <red>This region already protected by ChestProtect and you don't have permission to create shops at here.
    shops-exsts: <red>The region that you want to protect contains other player's shops and you don't have permission to access them.
  displaycontrol:
    toggle: |-
      <green>Successfully toggle your QuickShop display showcasing to <aqua>{0}</aqua>.
      <yellow>You may need re-join to take effect.
    toggle-exception: <red>Failed to toggle your display showcase settings due an internal error, please contact server administrator.
    command:
      displaycontrol: <yellow>Toggle your QuickShop display showcasing
  reremake-migrator:
    commands:
      migratefromreremake: Migrate QuickShop-Reremake's data to QuickShop-Hikari
    server-not-empty: "<red>No players are allowed to be online on the server during the conversion progress, please enable server whitelist or maintenance mode."
    starting-convert-progress: "<gold>Starting the convert progress, <red>DO NOT SHUTDOWN YOUR SERVER!</red>"
    executing: "<gold>Executing migrate component <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
    completed: "<green>Done! Migrate completed, please restart your server and remove QuickShop-Reremake from plugins directory."
    join_blocking_converting: "<red>[QuickShop-Hikari Migrator] This server is performing a data conversion and you cannot join the server at this time, try again later!"
    join_blocking_finished: "<red>[QuickShop-Hikari Migrator] This server has just completed a data conversion and is waiting for a reboot to apply the changes, try again later!"
    failed: "<red>Migration progress exited with error, please check the server console."
    modules:
      config:
        copy-values: "<yellow>Copying values (total {0} entries)..."
        copying-value: "<gray> - Copying <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        migrate-price-restriction: "<yellow>Migrating price-restriction related settings..."
        migrate-price-restriction-entry: "<gray> - Migrating <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
      shop:
        start-migrate: "<yellow>Migrating shops (total {0} entries)..."
        migrate-entry: "<gray> - Migrating <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        unloading-reremake: "<gold>Unloading Reremake to avoid data overwrite..."
        register-entry: "<gray> - Registering <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        save-entry: "<gray> - Sparar <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        saving-shops: "<yellow>Saving <gold>{0}</gold> shops, this may need a while™ (the time based on shops amounts)..."
        conflict: "<gray> - CONFLICT > Detects a conflict between a Reremake store and an existing Hikari store location, taking a predefined behavior: <dark_gray>{0}</dark_gray>"
      translation:
        start-migrate: "<yellow>Migrerar översättningsfiler...</yellow>"
        copy-values: "<yellow>Copying values in locale <gold>{0}</gold> (total {1} entries)..."
        migrate-entry: "<gray> - Migrating <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        copying-value: "<gray> - Copying <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
      shop-logs:
        start-migrate: "<yellow>Migrating shop logs (details in console), please wait...</yellow>"
        extract-history-files: "<gray>Please wait for us to unzip and append the history logs..."
        filter-history-files: "<gray>Please wait for us to filter the history log..."
        filtered-history-files: "<gray>Filtered {0} lines out of queue."
        import-entry: "<gray> - Migrating <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
compat:
  advancedchests:
    created: <green>You're successfully created a shop on AdvancedChests container!
    permission-denied: <red>Sorry! You don't have the permission to create a shop on a AdvancedChests container!
suite:
  dynmap:
    title: '{0} - {1} - {2}'
    description: |-
      Item: {0}
      Owner: {1}
      Type: {2} {3}
      Price: {4}
      Location: {5} {6}, {7}, {8}
      Space: {9}
      Stock: {10}
  limited:
    command-description: <yellow>Set a limit that restrict the purchase that player can make in period.
    reach-the-quota-limit: <red>You have reach the purchase limit in this shop ({0}/{1}).
    quota-reset-countdown: <yellow>The quota in this shop will reset in {0}.
    quota-reset-player-successfully: <green>The quota for player {0} in this shop has been reset successfully.
    quota-reset-everybody-successfully: <green>The quota for everyone in this shop has been reset successfully.
    quota-setup: <green>The purchase restriction now apply to this shop!
    quota-remove: <green>The purchase restriction now removed from this shop!
    subtitles:
      title: <green>Purchase Success
      subtitle: <aqua>You can purchase <yellow>{0}</yellow> more in this shop
  list:
    command-description: <yellow>List all shops owned by yourself or another player.
    table-prefix: <yellow>You owned total <aqua>{0}</aqua> shops on this server.
    entry-syntax: <aqua>- <yellow>{0}
    entry-syntax-fallback-name: '<yellow>Item:{0} X:{1}, Y:{2}, Z:{3}, World: {4}'
    hover-text: |-
      <yellow>{0}
      <gray>{1} {2}, {3}, {4}
      <aqua>{5} - <yellow>{6}
      <yellow>{7} items each stack.
  shopitemonly:
    message: <red>You can't put non-shop items into shop container, all non-shop items will dropped at your location.
compatibility:
  elitemobs:
    soulbound-disallowed: You can't trade item which have EliteMobs Soulbound enchantment.
internet-paste-forbidden-privacy-reason: "<red>Failed! According your privacy setting, QuickShop-Hikari are not allowed upload your paste to Internet, turn on DIAGNOSTIC permission in privacy setting in config.yml or use <aqua>/quickshop paste --file</aqua> instead."
no-sign-type-given: "<red>You need give a sign material, the sign materials that available on this server: {0}"
sign-type-invalid: "<red>The type <yellow>{0}</yellow> not a valid sign material."
delete-controlpanel-button-confirm: "<red>Do you really want remove this shop? Click the <bold>[Remove Shop]</bold> button again in {0} seconds to confirm."
cannot-suggest-price: "<red>Sorry, For the time being, no more people are trading the same item as you and there is not enough data to generate a suggested price."
price-suggest: "<green>Based on data from <aqua>{0}</aqua> stores, the highest priced store is priced at <light_purple>{1}</light_purple>, the lowest priced store is priced at <light_purple>{2}</light_purple>, the average price is <light_purple>{3}</light_purple>, and the median price is <light_purple>{4}</light_purple>. <newline><yellow>It's recommended that you set your price around <gold>{5}</gold>.</yellow>"
suggest-wait: "<green>Please wait... Calculating recommend price."
history:
  shop:
    gui-title: "View purchase history"
    header-icon-multiple-shop: "<white>The query result across {0} shops</white>"
    header-icon-description:
      - "<white>Type: <yellow>{0}</yellow></white>"
      - "<white>Owner: <yellow>{1}</yellow></white>"
      - "<white>Item: <yellow>{2}</yellow></white>"
      - "<white>Price: <yellow>{3} <aqua>x{4}</aqua></yellow></white>"
      - "<white>Location: <yellow>{5}</yellow></white>"
    log-icon-title: "<reset><green>Time: {0}</green>"
    log-icon-description:
      - "<white>Purchaser: <yellow>{0}</yellow></white>"
      - "<white>Item: <yellow>{1} <aqua>x{2}</aqua></yellow></white>"
      - "<white>Balance: <yellow>{3}</yellow></white>"
      - "<white>Tax: <yellow>{4}</yellow></white>"
    log-icon-description-with-store-name:
      - "<white>Store: <yellow>{0}</yellow></white>"
      - "<white>Purchaser: <yellow>{1}</yellow></white>"
      - "<white>Item: <yellow>{2} <aqua>x{3}</aqua></yellow></white>"
      - "<white>Balance: <yellow>{4}</yellow></white>"
      - "<white>Tax: <yellow>{5}</yellow></white>"
    query-icon: "<gray>Please wait, querying...</gray>"
    previous-page: "<white><< Previous Page</white>"
    next-page: "<white>Next Page >></white>"
    current-page: "<white>Page {0}</white>"
    summary-icon-title: "<green>Shop Summary"
    recent-purchases: "<white>Recent <aqua>{0}</aqua> purchases: <yellow>{1}</yellow></white>"
    recent-purchase-balance: "<white>Recent <aqua>{0}</aqua> turnover: <yellow>{1}</yellow></white>"
    total-purchases: "<white>Total purchases: <yellow>{0}</yellow></white>"
    total-balances: "<white>Total turnover: <yellow>{0}</yellow></white>"
    total-unique-purchasers: "<white>Total unique purchasers: <yellow>{0}</yellow></white>"
    top-n-valuable-customers-title: "<gold>Top {0} valuable customers</gold>"
    top-n-valuable-customers-entry: "<white>- <yellow>{0}</yellow> <aqua>{1}</aqua></white>"
    no-result: "<white>No result</white>"
about:
  text:
    - "<b><aqua>QuickShop <yellow>{0}</yellow></aqua></b>"
    - "<aqua>Version <yellow>>></yellow> <green>{1}</green></aqua>"
    - "<aqua>Release <yellow>>></yellow> <green>{2}</green></aqua>"
    - "<aqua>Developers <yellow>>></yellow> <color:#218bff><hover:show_text:'<yellow>Click to check contributors'><click:open_url:'https://github.com/Ghost-chu/QuickShop-Hikari/graphs/contributors'>[View contributors on GitHub]</click></hover></color></aqua>"
    - "<aqua>Localized members <gold>({4})</gold>: <yellow>>></yellow> <green>{5}</green></aqua>"
    - "  <yellow>+ <color:#43A047><hover:show_text:'<yellow>Click to open Crowdin translate page'><click:open_url:'https://crowdin.com/project/quickshop-hikari'>[Help translate on Crowdin]</click></hover></color></yellow>"
    - "<aqua>Donation Key <yellow>>></yellow> <green>{6}</green></aqua>"
    - "<gold>Powered by community</gold> <red>Made with ❤</red>"
  valid-donation-key: "<color:#00AFF1>Bound to <color:#F9C23C>{0}</color></color>"
  invalid-donation-key: "<gray>Invalid donation key</gra>"
  kofi-thanks: "<gold>Special thanks to those who support QuickShop on Ko-fi :)</gold>"
history-command-leave-blank: "<Leave blank for seeing store>"
shop-information-not-shown-due-an-internal-error: "<red>An internal error occurred. The store information panel may be displayed incompletely, please contact the server administrator."
