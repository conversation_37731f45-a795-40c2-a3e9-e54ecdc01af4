<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <!-- This module was also published with a richer model, Gradle metadata,  -->
  <!-- which should be used instead. Do not delete the following line which  -->
  <!-- is to indicate to <PERSON>radle or any Gradle module metadata file consumer  -->
  <!-- that they should prefer consuming it instead. -->
  <!-- do_not_remove: published-with-gradle-metadata -->
  <modelVersion>4.0.0</modelVersion>
  <groupId>org.junit</groupId>
  <artifactId>junit-bom</artifactId>
  <version>5.9.3</version>
  <packaging>pom</packaging>
  <name>JUnit 5 (Bill of Materials)</name>
  <description>This Bill of Materials POM can be used to ease dependency management when referencing multiple JUnit artifacts using Gradle or Maven.</description>
  <url>https://junit.org/junit5/</url>
  <licenses>
    <license>
      <name>Eclipse Public License v2.0</name>
      <url>https://www.eclipse.org/legal/epl-v20.html</url>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>bechte</id>
      <name>Stefan Bechtold</name>
      <email><EMAIL></email>
    </developer>
    <developer>
      <id>jlink</id>
      <name>Johannes Link</name>
      <email><EMAIL></email>
    </developer>
    <developer>
      <id>marcphilipp</id>
      <name>Marc Philipp</name>
      <email><EMAIL></email>
    </developer>
    <developer>
      <id>mmerdes</id>
      <name>Matthias Merdes</name>
      <email><EMAIL></email>
    </developer>
    <developer>
      <id>sbrannen</id>
      <name>Sam Brannen</name>
      <email><EMAIL></email>
    </developer>
    <developer>
      <id>sormuras</id>
      <name>Christian Stein</name>
      <email><EMAIL></email>
    </developer>
    <developer>
      <id>juliette-derancourt</id>
      <name>Juliette de Rancourt</name>
      <email><EMAIL></email>
    </developer>
  </developers>
  <scm>
    <connection>scm:git:git://github.com/junit-team/junit5.git</connection>
    <developerConnection>scm:git:git://github.com/junit-team/junit5.git</developerConnection>
    <url>https://github.com/junit-team/junit5</url>
  </scm>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.junit.jupiter</groupId>
        <artifactId>junit-jupiter</artifactId>
        <version>5.9.3</version>
      </dependency>
      <dependency>
        <groupId>org.junit.jupiter</groupId>
        <artifactId>junit-jupiter-api</artifactId>
        <version>5.9.3</version>
      </dependency>
      <dependency>
        <groupId>org.junit.jupiter</groupId>
        <artifactId>junit-jupiter-engine</artifactId>
        <version>5.9.3</version>
      </dependency>
      <dependency>
        <groupId>org.junit.jupiter</groupId>
        <artifactId>junit-jupiter-migrationsupport</artifactId>
        <version>5.9.3</version>
      </dependency>
      <dependency>
        <groupId>org.junit.jupiter</groupId>
        <artifactId>junit-jupiter-params</artifactId>
        <version>5.9.3</version>
      </dependency>
      <dependency>
        <groupId>org.junit.platform</groupId>
        <artifactId>junit-platform-commons</artifactId>
        <version>1.9.3</version>
      </dependency>
      <dependency>
        <groupId>org.junit.platform</groupId>
        <artifactId>junit-platform-console</artifactId>
        <version>1.9.3</version>
      </dependency>
      <dependency>
        <groupId>org.junit.platform</groupId>
        <artifactId>junit-platform-engine</artifactId>
        <version>1.9.3</version>
      </dependency>
      <dependency>
        <groupId>org.junit.platform</groupId>
        <artifactId>junit-platform-jfr</artifactId>
        <version>1.9.3</version>
      </dependency>
      <dependency>
        <groupId>org.junit.platform</groupId>
        <artifactId>junit-platform-launcher</artifactId>
        <version>1.9.3</version>
      </dependency>
      <dependency>
        <groupId>org.junit.platform</groupId>
        <artifactId>junit-platform-reporting</artifactId>
        <version>1.9.3</version>
      </dependency>
      <dependency>
        <groupId>org.junit.platform</groupId>
        <artifactId>junit-platform-runner</artifactId>
        <version>1.9.3</version>
      </dependency>
      <dependency>
        <groupId>org.junit.platform</groupId>
        <artifactId>junit-platform-suite</artifactId>
        <version>1.9.3</version>
      </dependency>
      <dependency>
        <groupId>org.junit.platform</groupId>
        <artifactId>junit-platform-suite-api</artifactId>
        <version>1.9.3</version>
      </dependency>
      <dependency>
        <groupId>org.junit.platform</groupId>
        <artifactId>junit-platform-suite-commons</artifactId>
        <version>1.9.3</version>
      </dependency>
      <dependency>
        <groupId>org.junit.platform</groupId>
        <artifactId>junit-platform-suite-engine</artifactId>
        <version>1.9.3</version>
      </dependency>
      <dependency>
        <groupId>org.junit.platform</groupId>
        <artifactId>junit-platform-testkit</artifactId>
        <version>1.9.3</version>
      </dependency>
      <dependency>
        <groupId>org.junit.vintage</groupId>
        <artifactId>junit-vintage-engine</artifactId>
        <version>5.9.3</version>
      </dependency>
    </dependencies>
  </dependencyManagement>
</project>
