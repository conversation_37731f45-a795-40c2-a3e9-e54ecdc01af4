# Full color code support and some variables
# Keep in mind that variables wont work for some lines, when it will for anothers :)
# Just keep them where there are now and everything will be ok :)
# Some lines can have global variables set. For player who will be effected. In example /heal Zrips then Zrips data will be used
# [serverName] to show server name
# [playerName] to show target player name
# [playerDisplayName] to show target player display name
# [lvl] to show target player level
# [exp] to show target player total exp
# [hp] to show target player health
# [maxHp] to show target player max health
# [hunger] to show target player hunger level
# [gameMode] to show target player gamemode
# [prefix] to show target player prefix if possible
# [suffix] to show target player suffix if possible
# Sender is console or player who performs command. In example Zrips performs /heal Zhax then Zrips data will be used
# [senderName] to show Sender player name
# [senderDisplayName] to show Sender player display name
# [senderLvl] to show Sender player level
# [senderExp] to show Sender player total exp
# [senderHp] to show Sender player health
# [senderMaxHp] to show Sender player max health
# [senderHunger] to show Sender player hunger level
# [senderGameMode] to show Sender player gamemode
# [senderPrefix] to show Sender player prefix if possible
# [senderSuffix] to show Sender player suffix if possible
# Source is player which is being used for extra info. In example Zrips performs /tp Zhax Zrips then Zhax data will be used as its location is being taken for new player location
# [sourceName] to show source player name
# [sourceDisplayName] to show source player display name
# [sourceLvl] to show source player level
# [sourceExp] to show source player total exp
# [sourceHp] to show source player health
# [sourceMaxHp] to show source player max health
# [sourceHunger] to show source player hunger level
# [sourceGameMode] to show source player gamemode
# [sourcePrefix] to show source player prefix if possible
# [sourceSuffix] to show source player suffix if possible
# ***********************************************
# Some lines supports option to send them to custom places, like action bar, title, sub title or even create JSON/clickable messages
# If line starts with !toast! then player will get toast message (advancement popup, only 1.12 and up). Some extra variables can be used to define type and icon. example: !toast! -t:goal -icon:paper Hello world!
# If line starts with !actionbar! then player will get action bar message defined after this variable
# If line starts with !actionbar:[seconds]! then player will get action bar message for defined amount of time
# If line starts with !broadcast! then everyone will receive message. You can add extra !toast! !actionbar! or !title! to send message for everyone to specific place, in example !broadcast!!title!
# If line starts with !customtext:[cTextName]! then custom text will be taken by name provided and shown for player. In case its used after !broadcast! then everyone who is online will get this custom text message
# If line starts with !title! then player will get title message defined after this variable, in addition it can contain !subtitle! which will add subtitle message
# If line starts with !bosbar:[name]-[timer]! then player will get bossbar message defined after this variable, in addition you can define how long this message will be visible. You need to define bossbar name which can be anything you want, but lines with same name will override each other to prevent stacking
# To include clickable messages: <T>Text</T><H>Hover text</H><C>command</C><SC>Suggested text</SC>
# <T> and </T> required, other is optional
# Use /n to break line
# To have more than one JSON message use <Next>
# <C> performs command as a player who clicked
# <CC> performs command from console once
# <CCI> performs command from console every time player clicks text
# <URL> includes url
info:
  # Use !prefix! in any locale line to automatically include this prefix
  prefix: '&e[&aCMI&e] '
  NoPermission: '&cDu har ikke tillatelse til dette!'
  CantHavePermission: '&cDu kan ikke ha denne tillatelsen!'
  WrongGroup: '&cYou are in wrong group for this!'
  NoPlayerPermission: '&c[playerName] har ikke denne tillatelsen: [permission]'
  Ingame: '&cDu kan bare bruke denne i spillet!'
  NoInformation: '&cKan ikke finne informasjonen!'
  Console: '&6Server'
  FromConsole: '&cDu kan bare bruke denne i konsollen!'
  NotOnline: '&cSpilleren er ikke pålogget!'
  NobodyOnline: '&cDet er ingen spillere pålogget!'
  Same: '&cDu kan ikke åpne ditt eget inventar for editering!'
  cantLoginWithDifCap: '&cDu kan ikke logge på serveren ved å endre store og små bokstaver!
    Gammelt navn: &e[oldName]&c. Nytt navn: &e[currentName]'
  Searching: '&eSøker etter spillerdata. Vennligst vent da dette kan ta noe tid!'
  NoPlayer: '&cKan ikke finne en spiller med det navnet!'
  NoCommand: '&cDet er ingen kommando med det navnet!'
  NoCommandWhileSleeping: '&cCan''t perform commands while sleeping!'
  cantFindCommand: '&5Kunne ikke finne kommandoen &7[%1]&5. Mente du egentlig &7[%2]&5?'
  nolocation: '&4Can''t find suitable location'
  PurgeNotEnabled: '&cPurge funksjonen er ikke aktivert i konfigurasjonen!'
  FeatureNotEnabled: '&cDenne funksjonen er ikke aktivert!'
  TeamManagementDisabled: '&7Denne funksjonen vil ha begrenset funksjonalitet så lenge
    DisableTeamManagement er satt til true!'
  ModuleNotEnabled: '&cDenne modulen er ikke aktivert!'
  versionNotSupported: '&cServer versjonen du bruker støtter ikke denne funksjonen'
  bungeeNoGo: '&cDenne funksjonen vil ikke fungere på servere som er basert på BungeeCord
    nettverk'
  clickToTeleport: '&eKlikk for å teleportere'
  UseMaterial: '&4Vennligst bruk navn på materialer!'
  IncorrectMaterial: '&4Incorrect material name!'
  UseInteger: '&4Vennligst bruk tall!'
  UseBoolean: '&4Vennligst bruk enten True eller False!'
  NoLessThan: '&4Tallet kan ikke være mindre enn [amount]!'
  NoMoreThan: '&4Verdien kan ikke være mer enn [amount]'
  NoGameMode: '&4Vennligst bruk 0/1/2/3 eller Survival/Creative/Adventure/Spectator!'
  NoWorld: '&4Kan ikke finne en verden med dette navnet!'
  IncorrectLocation: '&4Lokasjonen er definert feil!'
  NameChange: '&6[playerDisplayName] &elogget inn, også kjent som: &6[namelist]'
  Cooldowns: '&eCooldown er aktivert for &6[cmd] &event &6[time]'
  specializedCooldowns: '&eCooldown er aktivert for denne kommandoen, vennligst vent
    &6[time]'
  specializedRunning: '&eCommand still running, please wait &6[time]'
  CooldownOneTime: '&eDenne kommandoen kan brukes kun en gang'
  WarmUp:
    canceled: '&eKommandoen ble kansellert fordi du beveget på deg'
    counter: '!actionbar!&6--> &e[time] &6<--'
    DontMove: '!title!!subtitle!&6Stå stille!'
    Boss:
      DontMove: '&4Ikke beveg deg før om &7[autoTimeLeft] &4sekunder!'
      WaitFor: '&4Wait for &7[autoTimeLeft] &4seconds!'
  Spawner: '&r[type] Fremkaller'
  FailedSpawnerMine: '!actionbar!&cFailed to mine spawner. &7[percent]% &cdrop chance'
  ClickSpawner: '!actionbar!&7[percent]% &eDrop chance'
  Elevator:
    created: '&eOpprettet heisskilt'
  CantPlaceSpawner: '&eKan ikke plassere en spawner så nær en annen spawner (&6[range]&e)'
  ChunksLoading: '&eChunk data for verden blir fortsatt lastet inn. Vent litt og prøv
    igjen.'
  ShulkerBox: Shulker Boks
  CantUseNonEncrypted: '!actionbar!&cKommandoer på dette elementet er ikke kryptert.
    Kan ikke bruke de!'
  CantDecode: '!actionbar!&cKan ikke dekode melding/kommando. Nøkkelfil inneholder
    feil nøkkel for denne oppgaven. Informer serveradministrasjonen om dette.'
  Show: '&eVis'
  Remove: '&cFjern'
  Back: '&eTilbake'
  Forward: '&eFremover'
  Update: '&eOppdater'
  Save: '&eLagre'
  Delete: '&cSlett'
  Click: '&eKlikk'
  Preview: '&ePreview'
  PasteOld: '&eLim inn gammelt'
  ClickToPaste: '&eKlikk for å lime inn i chatten'
  CantTeleportWorld: '&eDu kan ikke teleportere til denne verdenen'
  CantTeleportNoWorld: '&cVerden eksisterer ikke. Teleportering kansellert'
  CantTeleport: '&eDu kan ikke teleportere fordi du har flere elementer enn tillatt.
    Pek over denne linjen for å se maksimalt tillatte elementer.'
  ClickToConfirmDelete: '&eClick to confirm removal of &6[name]'
  teleported: '&eDu har blitt teleportert'
  BlackList: '&e[material] [amount] &6Maks: [max]'
  PlayerSpliter: '&e----- &6[playerDisplayName] &e-----'
  Spliter: '&e--------------------------------------------------'
  SpliterValue: '&e------------------ &6[value] &e------------------'
  singleSpliter: '&e-'
  SpliterMiddle: ' &6[value] '
  ListSpliter: ', '
  ProgressBarFill: '&2▏'
  ProgressBarEmpty: '&e▏'
  nothingInHand: '&eDu må holde elementet i hånden'
  nothingInHandLeather: '&eDu må holde læret i hånden'
  nothingToShow: '&eIngenting å vise'
  noItem: '&cKan ikke finne elementet'
  dontHaveItem: '&cDu har ikke &6[itemName] x[amount] &ci ditt inventar'
  wrongWorld: '&cDu kan ikke gjøre dette i denne verdenen'
  wrongPortal: '&cYou are in incorrect area of effect'
  differentWorld: '&cUlike verdener'
  HaveItem: '&cDu har &6[amount]x [itemName] &ci ditt inventar'
  ItemWillBreak: '!actionbar!&eDin gjenstand (&6[itemName]&e) vil snart bli ødelagt!
    &e[current]&6/&e[max]'
  ArmorWillBreak: '!actionbar!&e[itemName] vil snart bli ødelagt! &e[current]&6/&e[max]'
  cantDoInGamemode: '&eYou can''t do this in this game mode'
  cantDoForPlayer: '&eDu kan ikke gjøre dette på &6[playerDisplayName]'
  cantDoForYourSelf: '&eDu kan ikke gjøre dette på deg selv'
  cantDetermineMobType: '&cKan ikke fastslå mob typen fra &e[type] &cvariabelen'
  cantRename: '!actionbar!&eDu kan ikke endre navnet på gjenstanden til dette navnet'
  confirmRedefine: '&eClick to confirm redefining'
  cantEdit: '&eYou can''t edit this'
  wrongName: '&cFeil navn'
  unknown: ukjent
  invalidName: '&cUgyldig navn'
  alreadyexist: '&4Dette navnet er opptatt'
  dontexist: '&4Ingenting ble funnet med dette navnet'
  worldDontExist: '&cVerdenen er ikke lenger tilgjengelig. Kan ikke teleportere deg
    dit!'
  flyingToHigh: '&cDu kan ikke fly så høyt. Maks høyde er &6[max]&c!'
  specializedItemFail: '&cKan ikke fastslå kravet til spesial kommandoen etter verdi:
    &7[value]'
  sunSpeeding: Det sover [count] av [total] spillere [hour] timer [speed]X hastighet
  sleepersRequired: '!actionbar!&f[sleeping] &7av &f[required] &7sovende er påkrevd
    for at natten skal gå raskere'
  sunSpeedingTitle: '&7[hour]'
  skippingNight: '!title!&7Skipping entire night'
  sunSpeedingSubTitle: '&f[count]&7/&f[total] &7(&f[speed]X&7)'
  repairConfirm: '&eKlikk for å bekrefte gjenstanden &7[items] &e blir reparert for
    &7[cost]'
  bookDate: '&7Skrevet &f[date]'
  maintenance: '&7Vedlikeholdsmodus'
  notSet: ikke satt
  mapLimit: '&cKan ikke gå lengre enn 30 000 000 blokker'
  startedEditingPainting: '&eDu startet editering av maleri. Klikk på hvilken som
    helst blokk for å avbryte.'
  canceledEditingPainting: '&eDu kansellerte redigeringsmodus for maleri'
  changedPainting: '!actionbar!&eEndret maleri til &6[name] &emed ID &6[id]'
  noSpam: '''!title!&cIkke spam!'
  noCmdSpam: '!title!&cIkke spam med kommandoer!'
  spamConsoleInform: '&cSpiller (&7[playerName]&c) utløste (&7[rules]&c) chatte filter
    med:&r [message]'
  lookAtSign: '&eSe på skiltet'
  lookAtBlock: '&eSe på blokken'
  lookAtEntity: '&eSe på enhet'
  noSpace: '&eNot enough free space'
  notOnGround: '&eDu kan ikke utføre dette mens du flyr'
  # This line can have extra variables: [totalUsers] [onlinePlayers]
  FirstJoin: '&eVelkommen &6[playerDisplayName] &etil vår server!'
  LogoutCustom: '&8[&c-&8] &e[playerDisplayName]'
  LoginCustom: '&8[&a+&8] &e[playerDisplayName]'
  deathlocation: '&eDu døde ved x:&6[x]&e, y:&6[y]&e, z:&6[z]&e i verden &6[world]'
  book:
    exploit: '&cYou cant create book with more than [amount] pages'
  combat:
    CantUseShulkerBox: '&cCan''t use shulker box while you are in combat with player.
      Wait: [time]'
    CantUseCommand: '!actionbar!&cCan''t use command while in combat mobe. Wait: [time]'
    bossBarPvp: '&cCombat mode [autoTimeLeft]'
    bossBarPve: '&2Combat mode [autoTimeLeft]'
  bungee:
    Online: '&6Oppkoblet'
    Offline: '&cFrakoblet'
    not: '&cServeren tilhører ikke et bungee nettverk'
    noserver: '&cKan ikke finne en server med dette navnet!'
    server: '&eServer: &7[name]'
  variables:
    am: '&eAM'
    pm: '&ePM'
    Online: '&6Pålogget'
    Offline: '&cFrakoblet'
    TrueColor: '&2'
    FalseColor: '&4'
    'True': '&6True'
    'False': '&cFalse'
    Enabled: '&6Aktivert'
    Disabled: '&cDeaktivert'
    survival: '&6Overlevelse'
    creative: '&6Kreativ'
    adventure: '&6Eventyr'
    spectator: '&6Tilskuer'
    flying: '&6Flyr'
    notflying: '&6Flyr ikke'
  noSchedule: '&cNavnet på planleggingen ble ikke funnet'
  totem:
    cooldown: '&eTotem cooldown: [time]'
    warmup: '&eTotem effekt: [time]'
    cantConsume: '&eBruken av totem ble stoppet på grunn av cooldown tiden'
  Inventory:
    FullDrop: '&5Ikke alle tingene fikk plass i ditt inventar. De har blitt droppet
      på bakken'
  InventorySave:
    info: '&8Info: &8[playerDisplayName]'
    saved: '&e[time] &eInventaret ble lagret med id: &e[id]'
    NoSavedInv: '&eDenne spilleren har ingen lagrede inventar'
    NoEntries: '&4Filen eksisterer, men ingen inventarer ble funnet!'
    CantFind: '&eKan ikke finne inventaret med denne id'
    TopLine: '&e*************** [playerDisplayName] sine lagrede inventar ***************'
    List: '&eid: &6[id]&e. &6[time]'
    KillerSymbol: '&c ☠'
    Click: '&eKlikk for å sjekke ([id]) lagret inventar'
    IdDontExist: '&4Denne id eksisterer ikke!'
    Deleted: '&eDet lagrede inventaret ble slettet!'
    Restored: '&eDu har gjenopprettet &e[sourcename] &esitt inventar på spilleren
      &e[targetname].'
    GotRestored: '&eDitt inventar ble gjenopprettet med &e[sourcename] &esitt lagrede
      inventar &e[time]'
    LoadForSelf: '&eGjenopprett dette inventaret på deg'
    LoadForOwner: '&eGjenopprett dette inventaret på eieren'
    NextInventory: '&eNeste inventar'
    PreviousInventory: '&eForrige inventar'
    Editable: '&eRedigeringsmodus aktivert'
    NonEditable: '&eRedigeringsmodus deaktivert'
  TimeNotRecorded: '&e-Ingen data-'
  years: '&e[years] &6år '
  oneYear: '&e[years] &6år '
  day: '&e[days] &6dager '
  oneDay: '&e[days] &6dag '
  hour: '&e[hours] &6timer '
  oneHour: '&e[hours] &6time '
  min: '&e[mins] &6min '
  sec: '&e[secs] &6sek '
  vanishSymbolOn: '&8[&7B&8]&r'
  vanishSymbolOff: ''
  afkSymbolOn: '&e&oAfk'
  afkSymbolOff: ''
  nextPageConsole: '&fFor next page perform &5[command]'
  prevPage: '&2----<< &6Forrige '
  prevPageGui: '&6Forrige side '
  prevPageClean: '&6Forrige '
  prevPageOff: '&2----<< &7Forrige '
  prevPageHover: '&7<<<'
  firstPageHover: '&7|<'
  nextPage: '&6 Neste &2>>----'
  nextPageGui: '&6Neste side'
  nextPageClean: '&6 Neste'
  nextPageOff: '&7 Neste &2>>----'
  nextPageHover: '&7>>>'
  lastPageHover: '&7>|'
  pageCount: '&2[current]&7/&2[total]'
  pageCountHover: '&2[totalEntries]'
  skullOwner: '!actionbar!&7Eier:&r [playerName]'
  beeinfo: '!actionbar!&7Honey level: &e[level]&7/&e[maxlevel] &7Bees inside: &e[count]&7/&e[maxcount]'
  circle: '&3Sirkel'
  square: '&5Firkant'
  clear: '&7Clear'
  protectedArea: '&cBeskyttet område. Kan ikke gjøre dette her.'
  valueToLong: '&eVerdien er for lang. Maks: [max]'
  valueToShort: '&eVerdien er for kort. Min: [min]'
  pvp:
    noGodDamage: '!actionbar!&cYou can''t damage players while being immortal'
  InvEmpty:
    armor: '&ePlassene for rustning må være tomme!'
    hand: '&eHånden din må være tom!'
    maininv: '&eInventaret ditt må være tomt!'
    maininvslots: '&eInventaret ditt må ha minst &6[count] &etomme plasser!'
    inv: '&eInventaret ditt bør være tomt!'
    offhand: '&eDin venstre hånd bør være tom!'
    quickbar: '&eHurtiglinjen bør være tom!'
    quickbarslots: '&eHurtiglinjen bør ha minst &6[count] &etomme plasser!'
    subinv: '&eUnderinventaret ditt bør være tomt!'
    subinvslots: '&eUnderinventaret ditt bør ha minst &6[count] &etomme plasser!'
  pickIcon: '&8Pick icon'
  DamageCause:
    block_explosion: Eksplosjon
    contact: Blokk skade
    cramming: overfylling
    custom: Ukjent
    dragon_breath: Drage ånde
    drowning: Drukner
    dryout: tørke ut
    entity_attack: Angrep
    entity_explosion: Eksplosjon
    entity_sweep_attack: entity sweep attack
    fall: Fall
    falling_block: Falling block
    fire: Brann
    fire_tick: Brann
    fly_into_wall: Fly inn i en vegg
    hot_floor: Magma block
    lava: Lava
    lightning: Lyn
    magic: Magi
    melting: Smelte
    poison: Forgiftet
    projectile: Prosjektil
    starvation: Sult
    suffocation: Kvalt
    suicide: Selvmord
    thorns: Thorns
    void: Void
    wither: Wither
  Biomes:
    BADLANDS: Badlands
    BADLANDS_PLATEAU: Badlands plateau
    BAMBOO_JUNGLE: Bamboo jungle
    BAMBOO_JUNGLE_HILLS: Bamboo jungle hills
    BASALT_DELTAS: Basalt deltas
    BEACH: Beach
    BIRCH_FOREST: Birch forest
    BIRCH_FOREST_HILLS: Birch forest hills
    COLD_OCEAN: Cold ocean
    CRIMSON_FOREST: Crimson forest
    DARK_FOREST: Dark forest
    DARK_FOREST_HILLS: Dark forest hills
    DEEP_COLD_OCEAN: Deep cold ocean
    DEEP_FROZEN_OCEAN: Deep frozen ocean
    DEEP_LUKEWARM_OCEAN: Deep lukewarm ocean
    DEEP_OCEAN: Deep ocean
    DEEP_WARM_OCEAN: Deep warm ocean
    DESERT: Desert
    DESERT_HILLS: Desert hills
    DESERT_LAKES: Desert lakes
    END_BARRENS: End barrens
    END_HIGHLANDS: End highlands
    END_MIDLANDS: End midlands
    ERODED_BADLANDS: Eroded badlands
    FLOWER_FOREST: Flower forest
    FOREST: Forest
    FROZEN_OCEAN: Frozen ocean
    FROZEN_RIVER: Frozen river
    GIANT_SPRUCE_TAIGA: Giant spruce taiga
    GIANT_SPRUCE_TAIGA_HILLS: Giant spruce taiga hills
    GIANT_TREE_TAIGA: Giant tree taiga
    GIANT_TREE_TAIGA_HILLS: Giant tree taiga hills
    GRAVELLY_MOUNTAINS: Gravelly mountains
    ICE_SPIKES: Ice spikes
    JUNGLE: Jungle
    JUNGLE_EDGE: Jungle edge
    JUNGLE_HILLS: Jungle hills
    LUKEWARM_OCEAN: Lukewarm ocean
    MODIFIED_BADLANDS_PLATEAU: Modified badlands plateau
    MODIFIED_GRAVELLY_MOUNTAINS: Modified gravelly mountains
    MODIFIED_JUNGLE: Modified jungle
    MODIFIED_JUNGLE_EDGE: Modified jungle edge
    MODIFIED_WOODED_BADLANDS_PLATEAU: Modified wooded badlands plateau
    MOUNTAINS: Mountains
    MOUNTAIN_EDGE: Mountain edge
    MUSHROOM_FIELDS: Mushroom fields
    MUSHROOM_FIELD_SHORE: Mushroom field shore
    NETHER_WASTES: Nether wastes
    OCEAN: Ocean
    PLAINS: Plains
    RIVER: River
    SAVANNA: Savanna
    SAVANNA_PLATEAU: Savanna plateau
    SHATTERED_SAVANNA: Shattered savanna
    SHATTERED_SAVANNA_PLATEAU: Shattered savanna plateau
    SMALL_END_ISLANDS: Small end islands
    SNOWY_BEACH: Snowy beach
    SNOWY_MOUNTAINS: Snowy mountains
    SNOWY_TAIGA: Snowy taiga
    SNOWY_TAIGA_HILLS: Snowy taiga hills
    SNOWY_TAIGA_MOUNTAINS: Snowy taiga mountains
    SNOWY_TUNDRA: Snowy tundra
    SOUL_SAND_VALLEY: Soul sand valley
    STONE_SHORE: Stone shore
    SUNFLOWER_PLAINS: Sunflower plains
    SWAMP: Swamp
    SWAMP_HILLS: Swamp hills
    TAIGA: Taiga
    TAIGA_HILLS: Taiga hills
    TAIGA_MOUNTAINS: Taiga mountains
    TALL_BIRCH_FOREST: Tall birch forest
    TALL_BIRCH_HILLS: Tall birch hills
    THE_END: The end
    THE_VOID: The void
    WARM_OCEAN: Warm ocean
    WARPED_FOREST: Warped forest
    WOODED_BADLANDS_PLATEAU: Wooded badlands plateau
    WOODED_HILLS: Wooded hills
    WOODED_MOUNTAINS: Wooded mountains
  EntityType:
    area_effect_cloud: Area effect cloud
    armor_stand: Armor stand
    arrow: Arrow
    bat: Bat
    bee: Bee
    blaze: Blaze
    boat: Boat
    cat: Cat
    cave_spider: Cave spider
    chicken: Chicken
    cod: Cod
    cow: Cow
    creeper: Creeper
    dolphin: Dolphin
    donkey: Donkey
    dragon_fireball: Dragon fireball
    dropped_item: Dropped item
    drowned: Drowned
    egg: Egg
    elder_guardian: Elder guardian
    enderman: Enderman
    endermite: Endermite
    ender_crystal: Ender crystal
    ender_dragon: Ender dragon
    ender_pearl: Ender pearl
    ender_signal: Ender signal
    evoker: Evoker
    evoker_fangs: Evoker fangs
    experience_orb: Experience orb
    falling_block: Falling block
    fireball: Fireball
    firework: Firework
    fishing_hook: Fishing hook
    fox: Fox
    ghast: Ghast
    giant: Giant
    guardian: Guardian
    hoglin: Hoglin
    horse: Horse
    husk: Husk
    illusioner: Illusioner
    iron_golem: Iron golem
    item_frame: Item frame
    leash_hitch: Leash hitch
    lightning: Lightning
    llama: Llama
    llama_spit: Llama spit
    magma_cube: Magma cube
    minecart: Minecart
    minecart_chest: Minecart chest
    minecart_command: Minecart command
    minecart_furnace: Minecart furnace
    minecart_hopper: Minecart hopper
    minecart_mob_spawner: Minecart mob spawner
    minecart_tnt: Minecart tnt
    mule: Mule
    mushroom_cow: Mushroom cow
    ocelot: Ocelot
    painting: Painting
    panda: Panda
    parrot: Parrot
    phantom: Phantom
    pig: Pig
    piglin: Piglin
    pillager: Pillager
    player: Player
    polar_bear: Polar bear
    primed_tnt: Primed tnt
    pufferfish: Pufferfish
    rabbit: Rabbit
    ravager: Ravager
    salmon: Salmon
    sheep: Sheep
    shulker: Shulker
    shulker_bullet: Shulker bullet
    silverfish: Silverfish
    skeleton: Skeleton
    skeleton_horse: Skeleton horse
    slime: Slime
    small_fireball: Small fireball
    snowball: Snowball
    snowman: Snowman
    spectral_arrow: Spectral arrow
    spider: Spider
    splash_potion: Splash potion
    squid: Squid
    stray: Stray
    strider: Strider
    thrown_exp_bottle: Thrown exp bottle
    trader_llama: Trader llama
    trident: Trident
    tropical_fish: Tropical fish
    turtle: Turtle
    unknown: Unknown
    vex: Vex
    villager: Villager
    vindicator: Vindicator
    wandering_trader: Wandering trader
    witch: Witch
    wither: Wither
    wither_skeleton: Wither skeleton
    wither_skull: Wither skull
    wolf: Wolf
    zoglin: Zoglin
    zombie: Zombie
    zombie_horse: Zombie horse
    zombie_villager: Zombie villager
    zombified_piglin: Zombified piglin
  EnchantAliases:
    protection_fire:
    - FireProtection
    damage_all:
    - Sharpness
    arrow_fire:
    - Flame
    soul_speed:
    - SOULSPEED
    water_worker:
    - AquaAffinity
    arrow_knockback:
    - Punch
    loyalty:
    - LOYALTY
    depth_strider:
    - DepthStrider
    vanishing_curse:
    - VanishingCurse
    durability:
    - Unbreaking
    knockback:
    - Knockback
    luck:
    - Luck
    binding_curse:
    - BindingCurse
    loot_bonus_blocks:
    - Fortune
    protection_environmental:
    - Protection
    dig_speed:
    - Efficiency
    mending:
    - Mending
    frost_walker:
    - FrostWalker
    lure:
    - Lure
    loot_bonus_mobs:
    - Looting
    piercing:
    - PIERCING
    protection_explosions:
    - BlastProtection
    damage_undead:
    - Smite
    multishot:
    - MULTISHOT
    fire_aspect:
    - FireAspect
    channeling:
    - CHANNELING
    sweeping_edge:
    - SweepingEdge
    thorns:
    - Thorns
    damage_arthropods:
    - BaneOfArthropods
    oxygen:
    - Respiration
    riptide:
    - RIPTIDE
    silk_touch:
    - SilkTouch
    quick_charge:
    - QUICKCHARGE
    protection_projectile:
    - ProjectileProtection
    impaling:
    - IMPALING
    protection_fall:
    - FallProtection
    - FeatherFalling
    arrow_damage:
    - Power
    arrow_infinite:
    - Infinity
  PotionEffectAliases:
    speed:
    - Speed
    slow:
    - Slow
    fast_digging:
    - Fast digging
    slow_digging:
    - Slow digging
    increase_damage:
    - Increase damage
    heal:
    - Heal
    harm:
    - Harm
    jump:
    - Jump
    confusion:
    - Confusion
    regeneration:
    - Regeneration
    damage_resistance:
    - Damage resistance
    fire_resistance:
    - Fire resistance
    water_breathing:
    - Water breathing
    invisibility:
    - Invisibility
    blindness:
    - Blindness
    night_vision:
    - Night vision
    hunger:
    - Hunger
    weakness:
    - Weakness
    poison:
    - Poison
    wither:
    - Wither
    health_boost:
    - Health boost
    absorption:
    - Absorption
    saturation:
    - Saturation
    glowing:
    - Glowing
    levitation:
    - Levitation
    luck:
    - Luck
    unluck:
    - Unluck
    slow_falling:
    - Slow falling
    conduit_power:
    - Conduit power
    dolphins_grace:
    - Dolphins grace
    bad_omen:
    - Bad omen
    hero_of_the_village:
    - Hero of the village
direction:
  n: Nord
  ne: Nord Øst
  e: Øst
  se: Sør Øst
  s: Sør
  sw: Sør Vest
  w: Vest
  nw: Nord Vest
modify:
  middlemouse: '&2Midtre museklikk for å redigere'
  newItem: '&7Plasser en ny gjenstand her'
  newLine: '&2<NewLine>'
  newLineHover: '&2Legg til en ny linje'
  newPage: '&2<NewPage>'
  newPageHover: '&2Lag en ny side'
  removePage: '&c<RemovePage>'
  removePageHover: '&cFjern siden'
  deleteSymbol: '&cX'
  deleteSymbolHover: '&cSlett &e[text]'
  extraEditSymbol: ' &6!'
  addSymbol: ' &2+'
  addSymbolHover: '&2Legg til ny'
  cancelSymbol: ' &7&l[X]'
  cancelSymbolHover: '&aAvbryt'
  acceptSymbol: ' &2[!] '
  acceptSymbolHover: '&2Aksepter'
  denySymbol: ' &4[X] '
  denySymbolHover: '&2Nekt'
  enabledSymbol: '&2+'
  disabledSymbol: '&c-'
  enabled: '&2Aktivert'
  disabled: '&cDeaktivert'
  running: '&2Kjører'
  paused: '&cPauset'
  editSymbol: '&e✎'
  editSymbolHover: '&eRediger &6[text]'
  editLineColor: '&f'
  listUpSymbol: '&6⇑'
  listUpSymbolHover: '&eOpp'
  listDownSymbol: '&6⇓'
  listDownSymbolHover: '&eNed'
  listNumbering: '&e[number]. '
  listAlign: '&80'
  ChangeHover: '&eKlikk for å endre'
  ChangeCommands: '&eCommands'
  enabledColor: '&6'
  disabledColor: '&7'
  commandTitle: ' &e--- &6[name] &e---'
  commandList: ' &e[command]  '
  emptyLine: '&7[Empty line]'
  commandEdit: '&eRediger liste'
  lineAddInfo: '&eSkriv inn ny linje. Skriv &6cancel &efor å kansellere'
  commandAddInfo: '&eSkriv en ny kommando. Skriv &6cancel &efor å kansellere'
  commandAddInformationHover: "&e[playerName] kan brukes til å få spillernavn \n&eFor\
    \ å inkludere forsinkelse i kommandoer: \n&edelay! 5 \n&eSpesialiserte kommandoer\
    \ støttes. Få mer info på \n&ehttps://www.zrips.net/cmi/commands/specialized/"
  commandEditInfo: '&eKlikk for å lime inn gammel tekst. Skriv &6cancel &efor å kansellere.
    Skriv &6remove &efor å fjerne linjen.'
  listLimit: '&eList can''t be bigger than &6[amount] &eentries'
  commandEditInfoHover: '&eKlikk for å lime inn gammel tekst'
warp:
  list: '&e[pos]. &6[warpName] &f- &7[worldName] ([x]:[y]:[z])'
teleportation:
  relocation: '!actionbar!&4Din teleport lokasjon ble stoppet. Du har blitt teleportet
    til et trygt sted.'
afk:
  'on': '&6AFK'
  'off': '&7Spiller'
  left: '&6[playerDisplayName] &eer ikke lenger AFK'
  MayNotRespond: '&eSpilleren svarer kanskje ikke, han er AFK'
  MayNotRespondStaff: '&eStab medlemmet er AFK og svarer kanskje ikke. Prøv å ta kontakt
    med oss på Discord.'
BossBar:
  hpBar: '&f[victim] &e[max]&f/&e[current] &f(&c-[damage]&f)'
Potion:
  Effects: '&8Brygg effekter'
  List: '&e[PotionName] [PotionAmplifier] &eVarighet: &e[LeftDuration] &esek'
  NoPotions: '&eIngen'
Information:
  Title: '&8Spiller informasjon'
  Health: '&eHelse: &6[Health]/[maxHealth]'
  Hunger: '&eSult: &6[Hunger]'
  Saturation: '&eSaturation: &6[Saturation]'
  Exp: '&eExp: &6[Exp]'
  NotEnoughExp: '&eIkke nok exp: &6[Exp]'
  NotEnoughExpNeed: '&eIkke nok exp: &6[Exp]/[need]'
  tooMuchExp: '&eFor mye exp: &6[Exp]/[need]'
  NotEnoughVotes: '&eIkke nok stemmer: &6[votes]'
  TooMuchVotes: '&eFor mange stemmer: &6[votes]'
  BadGameMode: '&cDu kan ikke gjøre dette i din nåværende spillmodus'
  BadArea: '&cDu kan ikke utføre denne handlingen på dette området'
  GameMode: '&eSpillmodus: &6[GameMode]'
  GodMode: '&eGodMode: &6[GodMode]'
  Flying: '&eFlyr: &6[Flying]'
  CanFly: '&eKanFly: &6[CanFly]'
  Uuid: '&6[uuid]'
  ip: '&eIp adresse: &6[address]'
  FirstConnection: '&eFørste tilkobling: &6[time]'
  Lastseen: '&eSist sett: &6[time]'
  Onlinesince: '&ePålogget siden: &6[time]'
  Money: '&ePenger: &6[money]'
  Group: '&eGruppe: &6[group]'
econ:
  disabled: '&cDu kan ikke bruke denne kommandoen mens økonomistøtte er deaktivert'
  noMoney: '&cIkke nok penger'
  charged: '!actionbar!&fBelastet: &6[amount]'
  notEnoughMoney: '&cDu har ikke nok penger. Kreves (&6[amount]&c)'
  tooMuchMoney: '&cDu har for mye penger'
  commandCost: '&7This command cost is &6[cost] &7repeat it or click here to confirm'
Elytra:
  Speed: '&eHastighet: &6[speed]&ekm/t'
  SpeedBoost: ' &2+ '
  SpeedSuperBoost: ' &2+ '
  CanUse: '&cDu kan ikke utstyre deg selv med elytra uten tillatelse'
  CantGlide: '&cCan''t use elytra here!'
  Charging: '&eLader &f[percentage]&e%'
Selection:
  SelectPoints: '&cVelg 2 punkter med markeringsverktøyet! AKA: &6[tool]'
  PrimaryPoint: '&6Primær &epunktet er valgt [point]'
  SecondaryPoint: '&6Sekundær &epunktet er valgt [point]'
  CoordsTop: '&eX:&6[x] &eY:&6[y] &eZ:&6[z]'
  CoordsBottom: '&eX:&6[x] &eY:&6[y] &eZ:&6[z]'
  Area: '&7[world] &f(&6[x1]:[y1]:[z1] &e[x2]:[y2]:[z2]&f)'
NetherPortal:
  ToHigh: '&cPortalen er for stor, maks høyde er &6[max]&c!'
  ToWide: '&cPortalen er for bred, maks bredde er &6[max]&c!'
  Creation: '!actionbar!&7Created [height]x[width] nether portal!'
  Disabled: '&cPortaloppretting er deaktivert'
Location:
  Title: '&8Spillerlokasjon'
  Killer: '&eDrapsmann: &6[killer]'
  OneLiner: '&eLokasjon: &6[location]'
  DeathReason: '&eDødsårsak: &6[reason]'
  Full: '&7[world] &f[x]&7:&f[y]&7:&f[z]'
  World: '&eVerden: &6[world]'
  X: '&eX: &6[x]'
  Y: '&eY: &6[y]'
  Z: '&eZ: &6[z]'
  Pitch: '&ePitch: &6[pitch]'
  Yaw: '&eYaw: &6[yaw]'
Locations: '&7Locations: '
Ender:
  Title: '&7Åpne ender kiste'
Chat:
  localPrefix: ''
  shoutPrefix: '&c[S]&r'
  LocalNoOne: '!actionbar!&cNobody hear you, write ! before message for global chat'
  shoutDeduction: '!actionbar!&cTrukket fra &e[amount] &cfor å rope'
  # Use \n to add new line
  publicHover: '&eSendt tid: &6%server_time_hh:mm:ss%'
  privateHover: '&eSendt tid: &6%server_time_hh:mm:ss%'
  staffHover: '&eSendt tid: &6%server_time_hh:mm:ss%'
  helpopHover: '&eSendt tid: &6%server_time_hh:mm:ss%'
  link: '&l&4[&7LINK&4]'
  item: '&7[%cmi_iteminhand_displayname%[amount]&7]'
  itemAmount: ' x[amount]'
  itemEmpty: '&7[Mektige knyttneve]'
