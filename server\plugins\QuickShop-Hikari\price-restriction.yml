version: 3
whole-number-only: false  # This option not control by enable option, always enabled
undefined: # This option not control by enable option, always enabled
  min: 0.01 # Can be zero if you want player create a free shop
  max: 999999999999999999999999999999.99 # Actually this can be up to 1.7976931348623157E308
enable: false
rules: # Rules set
  example: # Rules name, used for identifier and permission node (quickshop.price.restriction.bypass.<name>)
    #items section SUPPORT Item Expression!
    #https://ghost-chu.github.io/QuickShop-Hikari-Documents/docs/modules/item-expression
    items: # Items in the rule (https://hub.spigotmc.org/javadocs/spigot/org/bukkit/Material.html), or the reference the item lookup table by adding @ before the name
      - STONE_SWORD
      - STONE_PICKAXE
      - STONE_AXE
      - STONE_SHOVEL
      - STONE_HOE
    currency: # Currency name, If your plugin doesn't support multi-currency (Vault API), this section won't be used
      - '*'
    min: 1.0 # Min price (double)
    max: 50.0 # Max price (double)