{"minecraft:recipes/decorations/torch": {"criteria": {"has_stone_pickaxe": "2024-02-01 19:56:52 +0800"}, "done": true}, "minecraft:story/upgrade_tools": {"criteria": {"stone_pickaxe": "2024-02-01 19:56:52 +0800"}, "done": true}, "minecraft:recipes/decorations/crafting_table": {"criteria": {"unlock_right_away": "2024-02-01 19:56:52 +0800"}, "done": true}, "minecraft:adventure/adventuring_time": {"criteria": {"minecraft:old_growth_birch_forest": "2024-02-01 19:58:16 +0800", "minecraft:dark_forest": "2024-02-01 19:56:53 +0800", "minecraft:river": "2024-08-12 13:31:33 +0800", "minecraft:birch_forest": "2024-08-12 13:29:04 +0800"}, "done": false}, "minecraft:recipes/building_blocks/dark_oak_planks": {"criteria": {"has_log": "2024-02-01 19:57:39 +0800"}, "done": true}, "minecraft:recipes/misc/charcoal": {"criteria": {"has_log": "2024-02-01 19:57:39 +0800"}, "done": true}, "minecraft:recipes/building_blocks/dark_oak_wood": {"criteria": {"has_log": "2024-02-01 19:57:39 +0800"}, "done": true}, "minecraft:recipes/building_blocks/birch_planks": {"criteria": {"has_logs": "2024-02-01 19:58:27 +0800"}, "done": true}, "minecraft:recipes/building_blocks/birch_wood": {"criteria": {"has_log": "2024-02-01 19:58:27 +0800"}, "done": true}, "minecraft:story/root": {"criteria": {"crafting_table": "2024-02-01 19:58:37 +0800"}, "done": true}, "minecraft:recipes/redstone/birch_button": {"criteria": {"has_planks": "2024-02-01 19:58:42 +0800"}, "done": true}, "minecraft:recipes/redstone/birch_door": {"criteria": {"has_planks": "2024-02-01 19:58:42 +0800"}, "done": true}, "minecraft:recipes/decorations/birch_fence": {"criteria": {"has_planks": "2024-02-01 19:58:42 +0800"}, "done": true}, "minecraft:recipes/redstone/birch_trapdoor": {"criteria": {"has_planks": "2024-02-01 19:58:42 +0800"}, "done": true}, "minecraft:recipes/decorations/chest": {"criteria": {"has_the_recipe": "2024-02-01 19:58:42 +0800"}, "done": true}, "minecraft:recipes/misc/stick": {"criteria": {"has_planks": "2024-02-01 19:58:42 +0800"}, "done": true}, "minecraft:recipes/decorations/birch_sign": {"criteria": {"has_planks": "2024-02-01 19:58:42 +0800"}, "done": true}, "minecraft:recipes/redstone/birch_fence_gate": {"criteria": {"has_planks": "2024-02-01 19:58:42 +0800"}, "done": true}, "minecraft:recipes/decorations/barrel": {"criteria": {"has_planks": "2024-02-01 19:58:42 +0800"}, "done": true}, "minecraft:recipes/redstone/birch_pressure_plate": {"criteria": {"has_planks": "2024-02-01 19:58:42 +0800"}, "done": true}, "minecraft:recipes/building_blocks/birch_slab": {"criteria": {"has_planks": "2024-02-01 19:58:42 +0800"}, "done": true}, "minecraft:recipes/building_blocks/birch_stairs": {"criteria": {"has_planks": "2024-02-01 19:58:42 +0800"}, "done": true}, "minecraft:recipes/decorations/ladder": {"criteria": {"has_stick": "2024-02-01 19:58:45 +0800"}, "done": true}, "minecraft:recipes/decorations/campfire": {"criteria": {"has_stick": "2024-02-01 19:58:45 +0800"}, "done": true}, "minecraft:recipes/tools/wooden_hoe": {"criteria": {"has_stick": "2024-02-01 19:58:45 +0800"}, "done": true}, "minecraft:recipes/tools/wooden_pickaxe": {"criteria": {"has_stick": "2024-02-01 19:58:45 +0800"}, "done": true}, "minecraft:recipes/tools/wooden_axe": {"criteria": {"has_stick": "2024-02-01 19:58:45 +0800"}, "done": true}, "minecraft:recipes/tools/wooden_shovel": {"criteria": {"has_stick": "2024-02-01 19:58:45 +0800"}, "done": true}, "minecraft:recipes/combat/wooden_sword": {"criteria": {"has_stick": "2024-02-01 19:58:45 +0800"}, "done": true}, "minecraft:adventure/root": {"criteria": {"killed_something": "2024-02-01 19:59:28 +0800"}, "done": true}, "minecraft:recipes/food/cooked_porkchop": {"criteria": {"has_porkchop": "2024-02-01 19:59:29 +0800"}, "done": true}, "minecraft:recipes/food/cooked_porkchop_from_campfire_cooking": {"criteria": {"has_porkchop": "2024-02-01 19:59:29 +0800"}, "done": true}, "minecraft:recipes/food/cooked_porkchop_from_smoking": {"criteria": {"has_porkchop": "2024-02-01 19:59:29 +0800"}, "done": true}, "minecraft:husbandry/balanced_diet": {"criteria": {"porkchop": "2024-02-01 20:08:23 +0800"}, "done": false}, "minecraft:husbandry/root": {"criteria": {"consumed_item": "2024-02-01 20:08:23 +0800"}, "done": true}, "minecraft:recipes/redstone/lever": {"criteria": {"has_cobblestone": "2024-08-12 13:25:20 +0800"}, "done": true}, "minecraft:recipes/combat/stone_sword": {"criteria": {"has_cobblestone": "2024-08-12 13:25:20 +0800"}, "done": true}, "minecraft:recipes/decorations/cobblestone_wall": {"criteria": {"has_cobblestone": "2024-08-12 13:25:20 +0800"}, "done": true}, "minecraft:recipes/tools/stone_pickaxe": {"criteria": {"has_cobblestone": "2024-08-12 13:25:20 +0800"}, "done": true}, "minecraft:recipes/tools/stone_axe": {"criteria": {"has_cobblestone": "2024-08-12 13:25:20 +0800"}, "done": true}, "minecraft:story/mine_stone": {"criteria": {"get_stone": "2024-08-12 13:25:20 +0800"}, "done": true}, "minecraft:recipes/decorations/furnace": {"criteria": {"has_cobblestone": "2024-08-12 13:25:20 +0800"}, "done": true}, "minecraft:recipes/building_blocks/cobblestone_slab": {"criteria": {"has_cobblestone": "2024-08-12 13:25:20 +0800"}, "done": true}, "minecraft:recipes/building_blocks/cobblestone_slab_from_cobblestone_stonecutting": {"criteria": {"has_cobblestone": "2024-08-12 13:25:20 +0800"}, "done": true}, "minecraft:recipes/building_blocks/stone": {"criteria": {"has_cobblestone": "2024-08-12 13:25:20 +0800"}, "done": true}, "minecraft:recipes/decorations/cobblestone_wall_from_cobblestone_stonecutting": {"criteria": {"has_cobblestone": "2024-08-12 13:25:20 +0800"}, "done": true}, "minecraft:recipes/building_blocks/cobblestone_stairs_from_cobblestone_stonecutting": {"criteria": {"has_cobblestone": "2024-08-12 13:25:20 +0800"}, "done": true}, "minecraft:recipes/building_blocks/cobblestone_stairs": {"criteria": {"has_cobblestone": "2024-08-12 13:25:20 +0800"}, "done": true}, "minecraft:recipes/tools/stone_hoe": {"criteria": {"has_cobblestone": "2024-08-12 13:25:20 +0800"}, "done": true}, "minecraft:recipes/tools/stone_shovel": {"criteria": {"has_cobblestone": "2024-08-12 13:25:20 +0800"}, "done": true}, "minecraft:recipes/transportation/mangrove_boat": {"criteria": {"in_water": "2024-08-12 13:31:33 +0800"}, "done": true}, "minecraft:recipes/transportation/acacia_boat": {"criteria": {"in_water": "2024-08-12 13:31:33 +0800"}, "done": true}, "minecraft:recipes/transportation/cherry_boat": {"criteria": {"in_water": "2024-08-12 13:31:33 +0800"}, "done": true}, "minecraft:recipes/transportation/bamboo_raft": {"criteria": {"in_water": "2024-08-12 13:31:33 +0800"}, "done": true}, "minecraft:recipes/transportation/spruce_boat": {"criteria": {"in_water": "2024-08-12 13:31:33 +0800"}, "done": true}, "minecraft:recipes/transportation/birch_boat": {"criteria": {"in_water": "2024-08-12 13:31:33 +0800"}, "done": true}, "minecraft:recipes/transportation/oak_boat": {"criteria": {"in_water": "2024-08-12 13:31:33 +0800"}, "done": true}, "minecraft:recipes/transportation/jungle_boat": {"criteria": {"in_water": "2024-08-12 13:31:33 +0800"}, "done": true}, "minecraft:recipes/transportation/dark_oak_boat": {"criteria": {"in_water": "2024-08-12 13:31:33 +0800"}, "done": true}, "DataVersion": 4189}