// MC Web Manager - 主应用入口
class MCWebManager {
    constructor() {
        this.modules = {};
        this.components = {};
        this.isAuthenticated = false;
        this.init();
    }

    async init() {
        // 初始化核心模块
        await this.loadCoreModules();
        
        // 检查认证状态
        await this.checkAuth();
        
        // 根据认证状态加载相应界面
        if (this.isAuthenticated) {
            await this.loadDashboard();
        } else {
            this.showLogin();
        }
    }

    async loadCoreModules() {
        // 动态加载核心模块
        const coreModules = ['api', 'auth', 'websocket', 'utils'];
        for (const module of coreModules) {
            try {
                const moduleScript = document.createElement('script');
                moduleScript.src = `/static/js/core/${module}.js`;
                document.head.appendChild(moduleScript);
            } catch (error) {
                console.error(`加载核心模块 ${module} 失败:`, error);
            }
        }
    }

    async checkAuth() {
        // 检查认证状态的逻辑将在auth模块中实现
        this.isAuthenticated = localStorage.getItem('access_token') !== null;
    }

    showLogin() {
        window.location.href = '/login';
    }

    async loadDashboard() {
        window.location.href = '/dashboard';
    }
}

// 应用启动
document.addEventListener('DOMContentLoaded', () => {
    window.mcManager = new MCWebManager();
});