/**
 * MC Web Manager - 主应用脚本
 * 应用程序入口和全局功能
 */

// 应用程序配置
const AppConfig = {
    name: 'MC Web Manager',
    version: '1.0.0',
    debug: true
};

// 全局错误处理
window.addEventListener('error', function(e) {
    if (AppConfig.debug) {
        console.error('全局错误:', e.error);
    }
});

// 全局未处理的Promise拒绝
window.addEventListener('unhandledrejection', function(e) {
    if (AppConfig.debug) {
        console.error('未处理的Promise拒绝:', e.reason);
    }
});

// 应用程序初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log(`${AppConfig.name} v${AppConfig.version} 已加载`);

    // 初始化认证管理器
    if (typeof authManager !== 'undefined') {
        console.log('认证管理器已初始化');
    }

    // 初始化API客户端
    if (typeof apiClient !== 'undefined') {
        console.log('API客户端已初始化');
    }
});

// 工具函数
const Utils = {
    /**
     * 格式化字节数
     */
    formatBytes: function(bytes, decimals = 2) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const dm = decimals < 0 ? 0 : decimals;
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
    },

    /**
     * 格式化时间戳
     */
    formatTimestamp: function(timestamp) {
        return new Date(timestamp).toLocaleString('zh-CN');
    },

    /**
     * 显示加载状态
     */
    showLoading: function(element) {
        if (element) {
            element.classList.add('loading');
        }
    },

    /**
     * 隐藏加载状态
     */
    hideLoading: function(element) {
        if (element) {
            element.classList.remove('loading');
        }
    }
};

// 导出到全局
window.AppConfig = AppConfig;
window.Utils = Utils;