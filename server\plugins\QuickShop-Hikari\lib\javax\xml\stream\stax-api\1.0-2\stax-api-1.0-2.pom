<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>javax.xml.stream</groupId>
  <artifactId>stax-api</artifactId>
  <version>1.0-2</version>
  <name>Streaming API for XML</name>
  <description>
    StAX is a standard XML processing API that allows you to stream XML data from and to your application.
  </description>

  <dependencies></dependencies>

  <licenses>
    <license>
      <name>GNU General Public Library</name>
      <url>http://www.gnu.org/licenses/gpl.txt</url>
      <distribution>repo</distribution>
    </license>
    <license>
      <name>COMMON DEVELOPMENT AND DISTRIBUTION LICENSE (CDDL) Version 1.0</name>
      <url>http://www.sun.com/cddl/cddl.html</url>
      <distribution>repo</distribution>
    </license>
  </licenses>

</project>
