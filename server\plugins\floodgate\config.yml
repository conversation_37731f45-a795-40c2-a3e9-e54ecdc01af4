# In Floodgate bedrock player data is send encrypted
# The following value should point to the key <PERSON><PERSON> generated.
# The public key should be used for the Geyser(s) and the private key for the Floodgate(s)
key-file-name: key.pem

# Floodgate prepends a prefix to bedrock usernames to avoid conflicts
# However, certain conflicts can cause issues with some plugins so this prefix is configurable using the property below
# It is recommended to use a prefix that does not contain alphanumerical to avoid the possibility of duplicate usernames.
username-prefix: ""

# Should spaces be replaced with '_' in bedrock usernames?
replace-spaces: true

# The default locale for Floodgate. By default, Floodgate uses the system locale
# default-locale: en_US

disconnect:
  # The disconnect message Geyser users should get when connecting
  # to the server with an invalid key
  invalid-key: Please connect through the official Geyser
  # The disconnect message Geyser users should get when connecting
  # to the server with the correct key but not with the correct data format
  invalid-arguments-length: Expected {} arguments, got {}. Is Geyser up-to-date?

# Configuration for player linking
player-link:
  # Whether to enable the linking system. Turning this off will prevent
  # players from using the linking feature even if they are already linked.
  enabled: true

  # Whether to require a linked account in order to be able to join the server.
  require-link: false

  # Set the following option to true when you want to host your own linking database.
  # -> This can work in addition to global linking.
  # Note that you have to install a linking database implementation before enabling this, because
  # it'll cause errors otherwise.
  enable-own-linking: false

  # The following three options only apply when 'enable-own-linking' is set to 'true'

  # Whether to allow the use of /linkaccount and /unlinkaccount
  # You can also use allow specific people to use the commands using the
  # permissions floodgate.command.linkaccount and floodgate.command.unlinkaccount.
  # This is only for linking, already connected people will stay connected
  allowed: true
  # The amount of time until a link code expires in seconds.
  link-code-timeout: 300
  # The player linking database type you want to use. This option is only used when there
  # are multiple database implementations found in the configuration directory.
  type: sqlite

  # Whether to enable global linking. Global Linking is a central server where people can link their
  # accounts (Java and Bedrock) and join on servers that have Global Linking enabled. The goal of
  # Global Linking is to make linking easier by not having to link your accounts on every server.
  # -> Your server-specific linking database will have priority over global linking.
  # Global Linking should normally only be disabled when you don't have internet access or when
  # you have limited internet access.
  enable-global-linking: true

metrics:
  enabled: true
  uuid: 5b4c1ea6-8a86-435a-b7ab-223f2395e426

# Do not change this
config-version: 3
