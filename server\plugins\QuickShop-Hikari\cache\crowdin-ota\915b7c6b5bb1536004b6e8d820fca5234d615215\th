break-shop-use-supertool: <yellow>You can break the shop by using the SuperTool.
fee-charged-for-price-change: <green>You paid <red>{0}<green> to change the price.
not-allowed-to-create: <red>ไม่สามารถสร้างร้านตรงนี้ได้
disabled-in-this-world: <red>QuickShop  ถูกปิดในโลกนี้
how-much-to-trade-for: <green>Enter in chat, how much you wish to trade <yellow>{1}x {0}<green> for.
client-language-changed: <green>QuickShop detected your client language setting has been changed, we're now using {0} locale for you.
shops-backingup: การสร้าง Shop-backup จากฐานข้อมูล...
_comment: Hi translator! If you editing this from Github or from code sources, you should go https://crowdin.com/project/quickshop-hikari
unlimited-shop-owner-changed: <yellow>This unlimited shop owner has been changed to {0}.
bad-command-usage-detailed: '<red>Bad command arguments! Accepts the following parameters: <gray>{0}'
thats-not-a-number: <red>ตัวเลขไม่ถูกต้อง
shop-name-disallowed: <red>The shop name <yellow>{0}</yellow> is disallowed. Pick another one!
console-only-danger: <red>This is a dangerous command, so that only the Console can execute it.
not-a-number: <red>สามารถใส่ได้แค่ตัวเลข แต่คุณใส่ {0}
not-looking-at-valid-shop-block: ไม่พบบล็อกสำหรับสร้างร้านค้า คุณต้องมองดูที่ป้ายหีบ
shop-removed-cause-ongoing-fee: <red>ร้านของคุณที่ {0} โดนปิดเพราะคุณมีเงินไม่พอที่จะเปิดต่อได้
tabcomplete:
  amount: '[จํานวนเงิน]'
  item: '[รายการ]'
  price: ''
  name: '[name]'
  range: '[range]'
  currency: '[currency name]'
  percentage: '[percentage%]'
taxaccount-unset: <green>This shop's tax account now following server global setting.
blacklisted-item: <red>You cannot sell this item because it is on the blacklist
command-type-mismatch: <red>This command only can executed by <aqua>{0}.
server-crash-warning: '<red>WARNING: Using the /quickshop reload command to replace/delete the QuickShop Jar file may crash the server while it is running.'
you-cant-afford-to-change-price: <red>มีค่าใช้จ่ายทั้งหมด {0} ที่จะเปลี่ยนราคาในร้านของคุณ
safe-mode: <red>QuickShop now in safe-mode, you cannot open this shop container, please contact with server administrator to fix the errors.
forbidden-vanilla-behavior: <red>The operation is forbidden due to it not being consisten with vanilla behaviour
shop-out-of-space-name: <dark_purple>ร้านของคุณ {0} เต็ม
paste-disabled: |-
  <red>Paste function has been disabled! You cannot request technical support.
  Reason: {0}
quick-fill:
  entry-own: ' <yellow>- <red>[own] <aqua>{0} <light_purple>{1}'
  hover:
    - '<yellow>ชื่อ: <aqua>{0}'
    - '<yellow>Owner: <aqua>{0}'
    - '<yellow>Type: <aqua>{0}'
    - '<yellow>Price: <aqua>{0}'
    - '<yellow>ของ: <aqua>{0}'
    - '<yellow>Location: <aqua>{0}'
  hover-arg-filled:
    - '<yellow>ชื่อ: <aqua>{name}'
    - '<yellow>Owner: <aqua>{owner}'
    - '<yellow>Type: <aqua>{type}'
    - '<yellow>Price: <aqua>{price}'
    - '<yellow>ของ: <aqua>{item}'
    - '<yellow>Location: <aqua>{location}'
  header: '<yellow>You have multiple shops with name "<green>{0}</green>", choose one to continue:'
  entry-normal: ' <yellow>- <aqua>{0} <light_purple>{1}'
  entry-cooperation: ' <yellow>- <red>[co] <aqua>{0} <light_purple>{1}'
3rd-plugin-build-check-failed-admin: '<gray>(AdminOnly) <light_purple>{0}<dark_gray> denied the permission checks. If this not expected, try adding <light_purple>{1} <gray>to the listener blacklist. Configuration Guide:https://ghost-chu.github.io/QuickShop-Hikari-Documents/docs/modules/shops/protection-checker'
average-price-nearby: '<green>Nearby average price: <yellow>{0}'
inventory-check-global-alert: "<red>[Inventory Check] <gray>Warning! Found a QuickShop display item <gold>{2}</gold> in inventory at <aqua>{0}</aqua> <dark_gray>[{1}]</dark_gray>, which it shouldn't happened, This usually means that someone is maliciously exploiting the exploit to duplicating the display item."
digits-reach-the-limit: <red>You have reached the limits of decimals in the price.
currency-unset: <green>Shop currency removed successfully. Using default settings now.
you-cant-create-shop-in-there: <red>You don't have permissions to create a shop at this location.
no-pending-action: <red>You do not have any pending actions
refill-success: <green>เติมของสำเร็จ
failed-to-paste: <red>Failed to upload the data to Pastebin. Check your internet connection and try again. (See console for details)
shop-out-of-stock-name: <dark_purple>ร้าน {0} ของคุณ {1} หมด
shop-name-invalid: <red>The shop name <yellow>{0} <red>is invalid. Pick another one!
how-many-buy-stack: <yellow>Enter in chat, how many bulks you wish to <aqua>BUY<green>. There are <yellow>{0}<green> items in each bulk and you can buy <yellow>{1}<green> bulks. Enter <aqua>{2}<green> in chat to buy all.
exceeded-maximum: <red>The value exceeded the maximum value in Java.
unlimited-shop-owner-keeped: '<yellow>Attention: The shop owner still is unlimited shop owner, you need re-set new shop owner by yourself.'
no-enough-money-to-keep-shops: <red>You didn't had enough money to keep your shops! All your shops have been removed...
3rd-plugin-build-check-failed: <red>3rd party plugin <bold>{0}<reset><red> denied the permission checks, did you have permission setup in there?
not-a-integer: <red>You must input a integer, your input was {0}.
translation-country: 'โซนภาษา: ไทย (th_TH)'
buying-more-than-selling: '<red>WARNING: You are buying items for more than you are selling them!'
purchase-failed: '<red>Purchase failed: Internal Error. Please contact the Server Administrator.'
denied-put-in-item: <red>ไม่สามารถใส่ของชิ้นนี้เข้าไปในร้านของคุณได้
shop-has-changed: <red>The shop you tried to use has changed since you clicked it!
flush-finished: <green>Successfully flushed the messages.
no-price-given: <red>Please give a valid price.
shop-already-owned: <red>หีบนี้ได้สร้างร้านแล้ว
backup-success: <green>Backup successfull.
not-looking-at-shop: <red>Couldn't find a QuickShop. Make sure you look at one.
you-cant-afford-a-new-shop: <red>It costs {0} to create a new shop.
success-created-shop: <green>Shop created.
shop-creation-cancelled: <red>Shop creation cancelled.
shop-owner-self-trade: <yellow>You trade with your own shop. You may not gain any money.
purchase-out-of-space: <red>This shop has run out of space. Contact the shop owner or staff to get it emptied.
reloading-status:
  success: <green>Reload completed without any errors.
  scheduled: <green>Reload complete. <gray>(Some changes take a while to become active)
  require-restart: <green>Reload complete. <yellow>(Some changes require a server restart to take effect)
  failed: <red>Reload failed, check the server console
player-bought-from-your-store-tax: <green>{0} purchased {1} {2} from your shop and you earned {3} ({4} in taxes).
not-enough-space: <red>You only have room left for {0} more!
shop-name-success: <green>Successfully set the shop name to <yellow>{0}<green>.
shop-staff-added: <green>Successfully added {0} as a staff member to your shop.
shop-staff-empty: <yellow>This shop have no staff members.
shops-recovering: Recovering shops from backup...
virtual-player-component-hover: "<gray>This is a virtual player.\n<gray>Refers to a system account of this name with the same name.</gray>\n<green>UUID: <yellow>{0}</yellow></green>\n<green>Username: <yellow>{1}</yellow></green>\n<green>Display as: <yellow>{2}</yellow></green>"
real-player-component-hover: "<gray>This is a real exists player.\n<green>UUID: <yellow>{0}</yellow></green>\n<green>Username: <yellow>{1}</yellow></green>\n<green>Display as: <yellow>{2}</yellow></green>\n<gray>If you wish to use a virtual system account with the same name, add <dark_gray>\"[]\"</dark_gray> to the both side of the username: <dark_gray>[{1}]</dark_gray>."
menu:
  sell-tax: <green>You paid <yellow>{0} <green>in taxes.
  owner: '<green>เจ้าของ: {0}'
  preview: <aqua>[Item Preview]
  enchants: <dark_purple>Enchantments
  sell-tax-self: <green>You didn't had to pay taxes because you own this shop.
  shop-information: '<green>ข้อมูลร้าน:'
  item: '<green>ของ: <yellow>{0}'
  price-per: <green>ราคาต่อ <yellow>{0} <green>- <yellow>{1}
  item-name-and-price: <yellow>{0} {1} <green>for <yellow>{2}
  item-name-and-price-tax: <yellow>{0} {1} <green>for</green> {2} <gray>(<green>{3}</green> in taxes)
  successful-purchase: '<green>ซื้อสำเร็จ:'
  price-per-stack: <green>ราคาต่อ <yellow>{2}x {0} - {1}
  stored-enchants: <dark_purple>Stored Enchantments
  item-holochat-error: <red>[Error]
  this-shop-is-selling: <green>This shop is <aqua>SELLING<green> items.
  shop-stack: '<green>เหมารวม: <yellow>{0}'
  space: '<green>ที่ว่าง: <yellow>{0}'
  effects: <green>Effects
  damage-percent-remaining: <green>เหลือ <yellow>{0}%
  item-holochat-data-too-large: <red>[Error] Item NBT is too large for showing
  stock: '<green>Stock <yellow>{0}'
  this-shop-is-buying: <green>ร้านนี้กำลัง<light_purple>ซื้อ<green>ของ
  successfully-sold: '<green>ขายสำเร็จ:'
  total-value-of-chest: '<green>มูลค่ารวมของกล่อง: <yellow>{0}'
currency-not-exists: <red>ไม่สามารถหาสกุลเงินที่คุณจะกำหนดได้ ลองดูว่าเขียนถูกไหม ไม่ก็สกุลเงินนั้นไม่มีในโลกนี้
no-nearby-shop: <red>No nearby shop matching {0}.
translation-author: 'Ghost_chu, Andre_601'
integrations-check-failed-trade: <red>การดำเนินการ {0} ยกเลิกการสร้างร้านค้า
shop-transaction-failed: <red>Sorry, but an internal error occurred while processing your purchase. The purchase has been cancelled and any operation has been rollback. Please contact the server administrators if it keeping occurred.
success-change-owner-to-server: <green>เปลี่ยนเจ้าของร้านเป็นของเซิร์ฟเวอร์สำเร็จ
shop-name-not-found: <red>The shop named <yellow>{0}</yellow> not exists.
shop-name-too-long: <red>ชื่อของร้านยาวเกินไป (ชื่อใส่ได้แค่ {0}) กรุณาเลือกชื่อใหม่
metric:
  header-player: '<yellow>{0}''s {1} {2} transactions:'
  action-hover: <yellow>{0}
  price-hover: <yellow>Total {0}, including {1} taxes.
  unknown: <gray>(unknown)
  undefined: <gray>(noname)
  no-results: <red>ไม่พบการจ่ายหรือรับเงิน
  action-description:
    DELETE: <yellow>Player deleted a shop. And refunded the shop creation fee to owner if possible.
    ONGOING_FEE: <yellow>Player paid the ongoing fee because the payment period.
    PURCHASE_BUYING_SHOP: <yellow>ผู้เล่นขายของให้กับร้านที่กำลังรับซื้อ
    CREATE: <yellow>ผู้เล่นสร้างร้านค้า
    PURCHASE_SELLING_SHOP: <yellow>ผู้เล่นซื้อของจากร้านที่กำลังขาย
    PURCHASE: <yellow>ซื้อของกับร้าน
  query-argument: 'Query Argument: {0}'
  amount-hover: <yellow>{0}x
  header-shop: '<yellow>The shop {0}''s {1} {2} transactions:'
  player-hover: |-
    <yellow>{0}
    <gold>UUID: <gray>{1}
  looking-up: <yellow>Performing metric lookup, Please wait...
  tax-hover: <yellow>ภาษี {0}
  header-global: '<yellow>The server {0} {1} transactions:'
  na: <gray>N/A
  transaction-count: <yellow>รวม {0}
  shop-hover: |-
    <yellow>{0}
    <gold>Pos: <gray>{1} {2} {3}, World: {4}
    <gold>Owner: <gray>{5}
    <gold>Shop Type: <gray>{6}
    <gold>Item: <gray>{7}
    <gold>Price: <gray>{8}
  time-hover: '<yellow>เวลา: {0}'
  amount-stack-hover: <yellow>{0}x กอง
permission-denied-3rd-party: '<red>Permission denied: 3rd Party Plugin [{0}].'
you-dont-have-that-many-items: <red>คุณมีแค่ {0} {1}
complete: <green>สำเร็จ
translate-not-completed-yet-url: 'The translation of the language {0} was not completed yet by {1}. Do you want to help us improving the translation? Browse: {2}'
success-removed-shop: <green>ได้ทำการลบร้านค้าเรียบร้อยแล้ว
currency-set: <green>สกุลเงินของร้านได้กำหนดเป็น {0}
shop-purged-start: <green>Shop purge started, check the console for details.
economy-transaction-failed: <red>Sorry, but an internal error occurred while processing your transaction. The transaction has been cancelled and any economy operation has been rollback. Please contact the server administrators if it keeping occurred.
nothing-to-flush: <green>You have no new shop messages.
no-price-change: <red>This wouldn't result in a price change!
edition-confilct: QuicShop-Hikari with QuickShop-Reremake installed may conflict each other, uninstall one of them.
inventory-unavailable: |-
  <red>Non-existant or invalid InventoryWrapper. Do you use an Addon to re-bind the Shop Inventory?
  Information: InventoryWrapper={0}, WrapperProvider={1}, SymbolLink={2}. Please contact the Server administrators.
file-test: This is a test textfile. We use it to test if the messages.json is broken. You can fill it with any easter eggs you like here :)
unknown-player: <red>Target player doesn't exist, please check the username you typed.
player-offline: <red>The target player currently is offline.
player-profile-format: <aqua>{0}</aqua><gold>(<yellow>{1}</yellow>)
shop-type:
  selling: ขาย
  buying: ซื้อ
language:
  qa-issues: '<yellow>ปัญหาการประกันคุณภาพ: <aqua>{0}%'
  code: '<yellow>โค้ด: <gold>{0}'
  approval-progress: '<yellow>ความคืบหน้าการอนุมัติ: <aqua>{0}%'
  translate-progress: '<yellow>ความคืบหน้าของการแปลภาษา: <aqua>{0}%'
  name: '<yellow>ชื่อ: <gold>{0}'
  help-us: <green>[ช่วยพวกเราแปลภาษา]
warn-to-paste: |-
  <yellow>รวบรวมข้อมูลและอัปโหลดไปยัง Pastebin การดําเนินการนี้อาจใช้เวลาสักครู่...
  <red><bold>คำเตือน:</bold> ข้อมูลจะถูกเก็บไว้สู่สาธารณะเป็นเวลาหนึ่งสัปดาห์! อาจทําให้การกําหนดค่าเซิร์ฟเวอร์และข้อมูลที่ละเอียดอ่อนอื่น ๆ ของคุณรั่วไหล ตรวจสอบให้แน่ใจว่าคุณส่งไปที่ <bold>พนักงาน/นักพัฒนาที่เชื่อถือได้
how-many-sell-stack: <green>Enter in chat, how many bulk you wish to <light_purple>SELL</light_purple>. There are <yellow>{0}<green> items per bulk and you can sell <yellow>{1}</yellow> bulks. Enter <aqua>{2}</aqua> in chat to sell all.
updatenotify:
  buttontitle: '[Update Now]'
  onekeybuttontitle: '[OneKey Update]'
  label:
    github: '[GitHub]'
    ore: '[Ore]'
    lts: '[LTS]'
    nbtapi: '[+NBTAPI]'
    qualityverifyed: '[Quality]'
    master: '[Master]'
    unstable: '[Unstable]'
    paper: '[+ Paper Optimized]'
    stable: '[Stable]'
    spigotmc: '[SpigotMC]'
    papermc: '[PaperMC]'
    bukkitdev: '[BukkitDev]'
    polymart: '[Polymart]'
    modrinth: '[Modrinth]'
    basic: '[Basic]'
  list:
    - '{0} was released. You are still using {1}!'
    - Boom! New update {0} incoming. Update!
    - Surprise! {0} came out. You are on {1}
    - Looks like you need to update. {0} was released!
    - Ooops! {0} was now released. You are on {1}!
    - I swear, QS has been updated to {0}. Why have you not updated yet?
    - Fixing and re... Sorry, but {0} was released!
    - Err! Nope. This is not an error. {0} has been released!
    - OMG! {0} came out! Why are you still using {1}?
    - 'Todays News: QuickShop has been updated to {0}!'
    - Plugin k.i.a. You should update to {0}!
    - Update {0} ignited. Save update!
    - There is an update available Commander. {0} has just came out!
    - Look my style---{0} updated. You're still using {1}
    - Ahhhhhhh! New update {0}! Update!
    - What do you think? {0} has been released! Update!
    - Doctor, QuickShop has a new update {0}! You should update~
    - Ko~ko~da~yo~ QuickShop has a new update {0} ~
    - Paimon want told you the QuickShop have new update {0}!
  remote-disable-warning: '<red>This version of QuickShop is marked as disabled by the remote server, which means this version may have serious problem, get details from our SpigotMC page: {0}. This warning will continue to appear until you switch to a stable version, but it will not affect your server''s performance.'
purchase-out-of-stock: <red>This shop run out of the stock, Contact shop owner or staffs to refill the stock.
nearby-shop-entry: '<green>- Info: {0} Price: <aqua>{1} <green>X: <aqua>{2} <green>Y: <aqua>{3} <green>Z: <aqua>{4} <green>Distance: <aqua>{5} <green>block(s)'
chest-title: QuickShop Store
console-only: <red>This command can only be executed by Console.
failed-to-put-sign: <red>Not enough space around the shop to place the info sign.
shop-name-unset: <red>This shop's name now removed
shop-nolonger-freezed: <green>You unfroze the shop. It's back to normal now!
no-permission-build: <red>ไม่สามารถสร้างร้านตรงนี้ได้
tableformat:
  left_half_line: <dark_purple>+--------------------
  right_half_line: <dark_purple>--------------------+
  full_line: <dark_purple>+---------------------------------------------------+
  left_begin: '<dark_purple>| '
quickshop-gui-preview: QuickShop GUI Item Preview
translate-not-completed-yet-click: The translation of the language {0} was not completed yet by {1}. Do you want to help us improving the translation? Click here!
taxaccount-invalid: <red>Target account not invalid, please enter a valid player name or uuid(with dashes).
player-bought-from-your-store: <red>{0} purchased {1} {2} from your shop, and you earned {3}.
reached-maximum-can-create: <red>You already created a maximum of {0}/{1} shops!
reached-maximum-create-limit: <red>You have reached the maximum number of shops you can create
translation-version: 'เวอร์ชั่นที่รับรอง: Hikari'
no-double-chests: <red>You cannot create the double chest shop.
price-too-cheap: <red>ราคาควรสูงกว่า <yellow>${0}
shop-not-exist: <red>ไม่มีร้าน
bad-command-usage: <red>Bad command arguments!
cleanghost-warning: <yellow>This command will purge <red>all</red> shops if the shop is corrupted, was created in not allowed worlds, is selling/buying not allowed items or <bold><red>EXISTS IN A UNLOADED WORLD</red></bold>. Make sure to create a full backup of your shop data first and use <aqua>/quickshop cleanghost confirm</aqua> to continue.
cleanghost-starting: <green>Starting to check for ghost shops (missing container blocks). All non-existing shops will be removed...
cleanghost-deleting: <yellow>Found a corrupted shop <aqua>{0}</aqua> because {1}, mark it to delete...
cleanghost-deleted: <green>Total <yellow>{0}</yellow> shops have been deleted.
shop-purchase-cancelled: <red>การซื้อของในร้านค้าได้ถูกยกเลิกแล้ว
bypassing-lock: <red>Bypassing a QuickShop lock!
bungee-cross-server-msg: '<yellow>QuickShop CSM: <green>{0}'
saved-to-path: The Backup was saved to {0}.
shop-now-freezed: <green>You have frozen the shop. Nobody can trade with this shop now!
price-is-now: <green>ราคาใหม่ของร้านคือ <yellow>{0}
shops-arent-locked: <red>Remember, shops are NOT protected from theft! If you want to stop thieves, lock it with LWC, Lockette, etc!
that-is-locked: <red>ร้านนี้ล็อค
shop-has-no-space: <red>The shop only has room for {0} more {1}.
safe-mode-admin: <red><bold>WARNING:</bold> The QuickShop version on this server is currently running in safe mode. Features won't work. Please use the <yellow>/quickshop</yellow> command to check for any errors.
shop-stock-too-low: <red>The shop only has {0} {1} left!
world-not-exists: <red>The world <yellow>{0}<red> doesn't exist
how-many-sell: <green>Enter in chat, how much you wish to <light_purple>SELL<green>. You can sell <yellow>{0}<green>. Enter <aqua>{1}<green> in chat, to sell all.
shop-freezed-at-location: <yellow>Your shop {0} at {1} got frozen!
translation-contributors: 'Contributors: Timtower, Netherfoam, KaiNoMood, Mgazul, JackTheChicken and Andre_601'
empty-success: <green>Emptying shop successful
taxaccount-set: <green>This shop's tax account has been set to <yellow>{0}
support-disable-reason:
  hot-reload: <yellow>Unsupported hot reloaded, restart the server and check again.
  outdated: <yellow>This version of QuickShop is already outdated. Update before requesting support!
  bad-hosts: |-
    <yellow>The server's HOSTS have been modified and certain QuickShop-functions require a connection to the Mojang API to work. Please fix the HOSTS settings before asking for support.
    Windows: C:\\windows\\system32\\drivers\\etc\\hosts
    Linux: /etc/hosts
  privacy: <yellow>This server is running in Cracked (Offline) mode. If you're running the server under a proxy and online-mode is set to true in the proxy, configure the proxy-related settings correctly.
  modified: <yellow>File integrity check failed, this QuickShop build has been modified by others.
  consolespamfix-installed: <yellow>ConsoleSpamFix installed, it will hidden exception details, temporarily disable it during asking support.
  authlib-injector-detected: <yellow>This server running under 3rd-party authlib provider like authlib-injector.
  unsupported-server-software: <yellow>Unsupported server software, Any modded hybrid server software is unsupported including MCPC, Cauldron, CatServer, Mohist, Magma, Fukkit, Cardboard etc.
supertool-is-disabled: <red>Supertool is disabled. Cannot break any shops.
unknown-owner: Unknown
restricted-prices: '<red>Restricted price for {0}: Min {1}, max {2}'
nearby-shop-this-way: <green>Shop is {0} blocks away from you.
owner-bypass-check: <yellow>Bypassed all checks. Trade successful! (You are now the shop owner!)
april-rick-and-roll-easter-egg: "<hover:show_text:'Click to open in browser for time limited rewards!'><click:open_url:'https://www.youtube.com/watch?v=dQw4w9WgXcQ'><green>Click here to acquire your time limited rewards that provided by QuickShop-Hikari developer!</green></click></hover>"
signs:
  item-right: ''
  out-of-space: Out of Space
  unlimited: Unlimited
  stack-selling: Selling {0}
  stack-price: '{0} per {1}x {2}'
  status-unavailable: <red>
  out-of-stock: Out of Stock
  stack-buying: Buying {0}
  freeze: Trading disabled
  price: '{0} each'
  buying: Buying {0}
  header: '{1}{0}'
  header-available: <green>{0}
  header-unavailable: <red>{0}
  selling: Selling {0}
  status-available: <green>
  item-left: ''
negative-amount: <red>You can't trade negative amounts
display-turn-on: <green>Successfully turn on the shop display.
shop-staff-deleted: <green>Successfully removed {0} as a staff member for your shop.
nearby-shop-header: '<green>Nearby Shop matching <aqua>{0}<green>:'
backup-failed: Cannot backup the database. Check the console for details.
shop-staff-cleared: <green>Successfully removed all staff members from your shop.
price-too-high: <red>The shop price is too high! You cannot create one with a price higher than {0}.
plugin-cancelled: '<red>Operation cancelled, Reason: {0}'
player-sold-to-your-store: <green>{0} sold {1} {2} to your shop.
shop-out-of-stock: <dark_purple>ร้าน {0}, {1}, {2} ของคุณ {3} หมด
how-many-buy: <green>Enter in chat, how many you wish to <aqua>BUY<green>. You can buy <yellow>{0}</yellow>. Enter <aqua>{1}</aqua> to buy them all.
language-info-panel:
  help: 'Help us: '
  code: 'Code: '
  name: 'Language: '
  progress: 'Progress: '
  translate-on-crowdin: '[Translate on Crowdin]'
item-not-exist: <red>The item <yellow>{0} <red>does not exist, please check your spelling.
shop-creation-failed: <red>Shop creation failed, please contact with server administrator.
inventory-space-full: <red>Your remaining inventory space can only have <green>{1}x</green> more items added to it. Try emptying your inventory!
no-creative-break: <red>You cannot break the shops of other players while in creative mode. Switch to survival mode or try to use the supertool {0} instead.
booleanformat:
  success: <green>✔
  failed: <red>✘
controlpanel:
  stack: '<green>Per bulk amount: <aqua>{0} <yellow>[<bold><light_purple>Change</light_purple></bold>]'
  price-hover: <yellow>คลิกเพื่อตั้งราคาใหม่สำหรับของ
  remove: <bold><red>[Remove Shop]
  mode-buying-hover: <yellow>Click to change the shop to SELL mode.
  empty: '<green>Empty: Remove all items <yellow>[<bold><light_purple>OK</light_purple></bold>]'
  stack-hover: <yellow>Click to set the amount of item per bulk. Set to 1 for normal behaviour.
  alwayscounting-hover: <yellow>Click to toggle if the shop is always counting container.
  alwayscounting: '<green>Always counting: {0} <yellow>[<bold><light_purple>Change</light_purple></bold>]'
  setowner: '<green>Owner: <aqua>{0} <yellow>[<bold><light_purple>Change</light_purple></bold>]'
  freeze: '<yellow>Freeze mode: <aqua>{0} <yellow>[<bold><light_purple>Toggle</light_purple></bold>]'
  price: '<green>Price: <aqua>{0} <yellow>[<bold><light_purple>Change</light_purple></bold>]'
  currency-hover: <yellow>Click to set or remove the currency that this shop is using
  lock: '<yellow>Shop lock: <aqua>{0} <yellow>[<bold><light_purple>Toggle</light_purple></bold>]'
  mode-selling: '<green>Shop mode: <aqua>Selling <yellow>[<bold><light_purple>Change</light_purple></bold>]'
  currency: '<green>Currency: <aqua>{0} <yellow>[<bold><light_purple>Set</light_purple></bold>]'
  setowner-hover: <yellow>คลิกเพื่อเปลี่ยนเจ้าของ
  mode-buying: '<green>Shop mode: <aqua>Buying <yellow>[<bold><light_purple>Change</light_purple></bold>]'
  item: '<green>Shop Item: {0} <yellow>[<bold><light_purple>Change</light_purple></bold>]'
  unlimited: '<green>Unlimited: {0} <yellow>[<bold><light_purple>Change</light_purple></bold>]'
  unlimited-hover: <yellow>Click to toggle if the shop is unlimited.
  refill-hover: <yellow>คลิกเพื่อเติมของ
  remove-hover: <yellow>คลิกเพื่อลบร้านนี้
  toggledisplay-hover: <yellow>Toggle the shop's displayitem status
  refill: '<green>Refill: Refill the items <yellow>[<bold><light_purple>OK</light_purple></bold>]'
  freeze-hover: <yellow>Toggle the shop's freeze status.
  lock-hover: <yellow>Enable/Disable the shop's lock protection.
  item-hover: <yellow>Click to change shop Item
  infomation: '<green>แผงควบคุมร้าน'
  mode-selling-hover: <yellow>Click to change the shop to BUY mode.
  empty-hover: <yellow>Click to clear the inventory of the shop.
  toggledisplay: '<green>Display-Item: <aqua>{0} <yellow>[<bold><light_purple>Toggle</light_purple></bold>]'
  history: '<green>History: <yellow>[<bold><light_purple>View</light_purple></bold>]</yellow>'
  history-hover: <yellow>คลิกเพื่อดูบันทึกประวัติร้านค้า
timeunit:
  behind: หลัง
  week: "{0} week"
  weeks: "{0} weeks"
  year: "{0} year"
  before: ก่อน
  scheduled: scheduled
  years: "{0} years"
  scheduled-in: scheduled in {0}
  second: "{0} second"
  std-past-format: '{5}{4}{3}{2}{1}{0} ที่ผ่านมา'
  std-time-format: HH:mm:ss
  seconds: "{0} seconds"
  hour: "{0} hour"
  scheduled-at: scheduled at {0}
  after: หลังจาก
  day: "{0} day"
  recent: เร็วๆ นี้
  between: ระหว่าง
  hours: "{0} hours"
  months: "{0} months"
  longtimeago: นานมาแล้ว
  between-format: ระหว่าง {0} กับ {1}
  minutes: "{0} minutes"
  justnow: เมื่อสักครู่
  minute: "{0} minute"
  std-format: MM/dd/yyyy HH:mm:ss
  future-plain-text: อนาคต
  month: "{0} month"
  future: ในอีก {0}
  days: "{0} days"
command:
  reloading: '<green>Configuration reloaded. <yellow>Some changes may require reboot to affect. <newline><gray>(Notice: Reloading behavior has been changed after ********, we now only reload configuration but not whole plugin to ensure the server won''t crashed.)'
  description:
    buy: <yellow>Changes a shop to <light_purple>BUY<yellow> mode
    about: <yellow>แสดงข้อมูลของ QuickShop
    language: <yellow>เปลี่ยนภาษาที่ใช้อยู่
    purge: <yellow>Start the shop purge task in background
    paste: <yellow>Uploads server data to Pastebin
    title: <green>QuickShop help
    remove: <yellow>Removes the shop you're looking at
    ban: <yellow>Bans a player from the shop
    empty: <yellow>ลบของทุกอย่างจากร้าน
    alwayscounting: <yellow>Set if shop always counting container even is unlimited
    setowner: <yellow>Changes the ownership of a shop.
    reload: <yellow>Reloads the config.yml of QuickShop
    freeze: <yellow>Disable or Enable shop trading
    price: <yellow>Changes the buy/sell price of a shop
    find: <yellow>Locates the nearest shop of a specific type.
    create: <yellow>Creates a new shop from the targeted chest
    lock: <yellow>Switch the shop's lock status
    currency: <yellow>Set or remove the currency setting of the shop
    removeworld: <yellow>Remove ALL shops in a specified world
    info: <yellow>Show QuickShop statistics
    owner: <yellow>Changes the ownership of a shop.
    amount: <yellow>To set item amount (Useful when having chat issues)
    item: <yellow>Change shop item of a shop
    debug: <yellow>Enables Developer mode
    unlimited: <yellow>Gives a shop unlimited stock.
    sell: <yellow>Changes a shop to <aqua>SELL<yellow> mode
    fetchmessage: <yellow>Show unread shop messages
    staff: <yellow>Manage your shop staff
    clean: <yellow>Removes all (loaded) shops without any stock
    refill: <yellow>Adds a given number of items to a shop
    help: <yellow>Shows QuickShop help
    removeall: <yellow>Remove ALL shops of a specified player
    unban: <yellow>Unbans a player from the shop
    transfer: <yellow>Transfer someone's ALL shops to other
    transferall: <yellow>Transfer someone's ALL shops to other
    transferownership: <yellow>Transfer the shop you're looking at to someone else
    size: <yellow>Change per bulk amount of a shop
    supercreate: <yellow>Create a shop while bypassing all protection checks
    taxaccount: <yellow>Set the tax account that shop using
    name: <yellow>Naming a shop to specific name
    toggledisplay: <yellow>Toggle the shop display item status
    permission: <yellow>Shop permission management
    lookup: <yellow>Manage lookup-able items table
    database: <yellow>View and maintain QuickShop database
    benefit: <yellow>Settings of divide benefits between shop owner and other players
    tag: <yellow>Add, remove or query tags of a shop
    suggestprice: <yellow>Suggest a recommend price for looking shop, based on other shops
    history: <yellow>View the history transactions of a shop
    sign: <yellow>Change the sign material of a shop
  bulk-size-not-set: '<red>Usage: /quickshop size \<amount>'
  no-type-given: '<red>Usage: /quickshop find \<item>'
  feature-not-enabled: This feature is not enabled in the config file.
  now-debuging: <green>เปิดใช้งานโหมด Developer และโหลด QuickShop ใหม่
  no-amount-given: <red>No amount provided. Use <green>/quickshop refill \<amount>
  no-owner-given: <red>ไม่มีเจ้าของใส่มา
  disabled: '<red>This command is disabled: <yellow>{0}'
  bulk-size-now: <green>Now trading <yellow>{0}x {1}
  toggle-always-counting:
    counting: <green>Shop is now always counting container even is unlimited
    not-counting: <green>Shop is now respect if shop is unlimited
  cleaning: <green>Removing shops without any stock...
  now-nolonger-debuging: <green>Successfully disabled Developer mode. Reloading QuickShop...
  toggle-unlimited:
    limited: <green>Shop is now limited
    unlimited: <green>Shop is now unlimited
  transfer-success-other: <green>Transferred <yellow>{0} {1}<green>'s shop(s) to <yellow>{2}
  no-trade-item: <green>Please hold a trade item to change in main hand
  wrong-args: <red>Invalid argument. Use <bold>/quickshop help</bold> to see a list of commands.
  some-shops-removed: <yellow>{0} <green>shop(s) removed
  new-owner: '<green>New owner: <yellow>{0}'
  format: <green>/{0} {1} <yellow>- {2}
  transfer-success: <green>Transferred <yellow>{0} <green>shop(s) to <yellow>{1}
  now-buying: <green>Now <light_purple>BUYING <yellow>{0}
  now-selling: <green>Now <aqua>SELLING <yellow>{0}
  cleaned: <green>Removed <yellow>{0}<green> shops.
  trade-item-now: <green>Now trading <yellow>{0}x {1}
  no-world-given: <red>Please specify a world name
  format-disabled: <red>/{0} {1} <gray>- {2}
  invalid-bulk-amount: <red>The given value {0} is larger than max stack size or lower than one
currency-not-support: <red>The economy plugin doesn't support the multi-economy feature.
trading-in-creative-mode-is-disabled: <red>คุณไม่สามารถแลกเปลี่ยนกับร้านค้านี้ได้ในขณะที่อยู่ในโหมดสร้างสรรค์
the-owner-cant-afford-to-buy-from-you: <red>This costs {0}, but the shop-owner only has {1}
you-cant-afford-shop-naming: <red>You can't afford shop naming, it costs {0} to naming.
inventory-error: |-
  <red>Failed to process the InventoryWrapper. Do you use an addon to re-bind the shop Inventory?
  Information: Exception={0}, InventoryWrapper={1}, WrapperType={2}, WrapperProvider={3}, SymbolLink={4}. Please contact the server administrators.
integrations-check-failed-create: <red>การดำเนินการ {0} ยกเลิกการสร้างร้านค้า
shop-out-of-space: <dark_purple>Your shop at {0}, {1}, {2} is now full.
admin-shop: AdminShop
no-anythings-in-your-hand: <red>ไม่มีอะไรอยู่ในมือของคุณ
no-permission: <red>คุณไม่ได้รับการอนุญาตให้ทำสิ่งนั้น!
chest-was-removed: <red>The chest was removed.
you-cant-afford-to-buy: <red>It costs {0}, but you only have {1}
shops-removed-in-world: <yellow>Total <aqua>{0}<yellow> shops has been deleted in world <aqua>{1}<yellow>.
display-turn-off: <green>Successfully turn off the shop display.
client-language-unsupported: <yellow>QuickShop doesn't support your client language, we're fallback to {0} locale now.
language-version: '63'
not-managed-shop: <red>You are not the owner or moderator of this Shop
shop-cannot-trade-when-freezing: <red>You cannot trade with this shop because it is frozen.
invalid-container: <red>Invalid container, You can only create the shop on block who have inventory.
permission:
  header: <green>Shop Permission Details
  header-player: <green>Shop Permission Details for {0}
  header-group: <green>Shop Permission Details for group {0}
  table: <gold><bold>{0}</bold>:</gold> <gray>{1}
  item:
    purchase: <yellow>Permission to allow users who have this to purchase the shop. (including buying and selling)
    show-information: <yellow>Permission to allow users who have this to see the shop information. (open shop info panel)
    preview-shop: <yellow>Permission to allow users who have this to preview the shop. (preview item)
    search: <yellow>Permission to allow users who have this to search the target shop. (remove permission will hide shop from search result)
    delete: <yellow>Permission to allow users who have this to delete the shop.
    receive-alert: <yellow>Permission to allow users who have this to receive alert message (e.g out-of-stock or new trade).
    access-inventory: <yellow>Permission to allow users who have this to access the shop inventory.
    ownership-transfer: <yellow>Permission to allow users who have this to transfer the shop ownership.
    management-permission: <yellow>Permission to allow users who have this to manage the groups permissions and edit user's group.
    toggle-display: <yellow>Permission to allow users who have this to toggle the shop display item.
    set-shoptype: <yellow>Permission to allow users who have this to set the shop type (switch buying or selling).
    set-price: <yellow>Permission to allow users who have this to set the shop price.
    set-item: <yellow>Permission to allow users who have this to set the shop item.
    set-stack-amount: <yellow>Permission to allow users who have this to set the shop stack amount.
    set-currency: <yellow>Permission to allow users who have this to set the shop currency.
    set-name: <yellow>Permission to allow users who have this to set the shop name.
    set-sign-type: <yellow>Change the sign material that attached on the shop.
    view-purchase-logs: <yellow>Permission to view shop purchase logs.
  group:
    everyone: <yellow>Default group for all users excepted shop owner.
    staff: <yellow>System group for shop staffs.
    administrator: <red>System group for administrators, the users in this group will have permissions almost same with shop owner.
invalid-group: <red>Invalid group name.
invalid-permission: <red>Invalid permission.
invalid-operation: <red>Invalid operation, only {0} are allowed.
player-no-group: <yellow>Player {0} not in any group in this shop.
player-in-group: <green>Player {0} is in group <aqua>{1}</aqua> in this shop.
permission-required: <red>You don't have permission {0} in this shop to do this.
no-permission-detailed: <red>You don't have permission <yellow>{0}</yellow> to do this.
paste-notice: "<yellow>Note: If you are creating a Paste for troubleshooting purposes, be sure to reproduce the error before creating the Paste as quickly as possible; we need the logs retained briefly in the buffer to troubleshoot. If you create a Paste too slowly or without first reproducing the error or restarted server, the Paste will record nothing and useless."
paste-uploading: <aqua>Please wait... Uploading the paste to pastebin......
paste-created: '<green>Paste created, click to open in browser: <yellow>{0}</yellow><br><red>Warning: <gray>Never send pastea to people you don''t trust.'
paste-created-local: |-
  <green>Paste created and saved to your local disk at {0}
  <red>Warning: <gray>Never send pastes to people you don't trust.
paste-created-local-failed: <red>Failed to save paste to your local disk, please check your disk.
paste-451: |-
  <gray><bold>TIPS:</bold> Your current country or region seems to have blocked the CloudFlare Workers service and QuickShop Paste may not be loaded properly.
  If the subsequent operation fails, try adding the --file argument to generate a local Paste: <dark_gray>/quickshop paste --file
paste-upload-failed: <red>Failed to upload paste to {0}, trying other pastebin provider...
paste-upload-failed-local: <red>Failed to upload paste, trying generate local paste...
command-incorrect: '<red>Command usage incorrect, type /quickshop help to check help. Usage: {0}.'
successfully-set-player-group: <green>Successfully set player {0} group to <aqua>{1}</aqua>.
successfully-unset-player-group: <green>Successfully remove player group in this shop.
successfully-set-player-permission: <green>Successfully set player {0} permission <aqua>{1}</aqua> in shop <aqua>{2}</aqua>.
lookup-item-created: <green>An item named <aqua>{0}</aqua> has been created in the lookup table. You can refer this item in configurations now.
lookup-item-exists: <red>An item named <yellow>{0}</yellow> already exists in the lookup table, delete it or pick another name.
lookup-item-not-found: <red>An item named <yellow>{0}</yellow> not exists.
lookup-item-name-illegal: <red>Item name illegal. Only alphanumeric characters and underscores are allowed.
lookup-item-name-regex: '<red>The name must match this regex: <aqua>{0}</aqua>.'
lookup-item-test-not-found: '<gold>Test: <yellow>The item in your hand not registered in lookup table.'
lookup-item-test-found: '<gold>Test: <green>The item in your hand was registered as name <aqua>{0}</aqua> in lookup table.'
lookup-item-removed: <green>The specified item <aqua>{0}</aqua> has been successfully removed from lookup table.
internal-error: <red>An internal error occurred, please contact the server administrator.
argument-cannot-be: <red>The argument <aqua>{0}</aqua> cannot be <yellow>{1}</yellow>.
argument-must-between: <red>The argument <aqua>{0}</aqua> value must between <yellow>{1}</yellow> and <yellow>{2}
invalid-percentage: <red>Percentage invalid, you must add '%' after the number of percentage.
display-fallback: |-
  <red>Due to an internal failure, a fallback message is being displayed.
  The value of this item may not be localized or processed correctly.
  Please contact the server administrator.
not-a-valid-time: |-
  <red>The string <yellow>{0}</yellow> not a valid timestamp, please enter a <aqua>Zulu Time (ISO 8601)</aqua> or <aqua>Unix Epoch Time in seconds</aqua>.
  <gold>Valid timestamp example: (for Sat, 17 Dec 2022 10:31:37 GMT)</gold><br><aqua>- <yellow>2022-12-17T10:31:37Z</yellow> <gray>(Zulu Time)</gray>
  - <yellow>1671273097</yellow> <gray>(Unix Epoch Time in seconds)</gray><br>
invalid-past-time: <red>You can't specify a time in the past.
debug:
  arguments-invalid: <red>Provided arguments <yellow>{0}</yellow> invalid.
  sign-located: '<green>Sign valid: <yellow>{0}</yellow>.'
  operation-missing: <red>You must specific an operation.
  operation-invalid: <red>You must specific an valid operation.
  invalid-base64-encoded-sql: <red>The SQL provided must be base64 encoded.
  warning-sql: |-
    <bold><red>Warning:</red></bold> <yellow>You're executing an SQL statement. This may corrupt your database or destroy any data in the database, even when it belongs to another plugin.
    <red>Don't confirm this if you don't trust who send you this.
  warning-sql-confirm: <yellow>To confirm this dangerous action, type <aqua>/quickshop debug database sql confirm {0}</aqua> in the next 60 seconds.
  warning-sql-confirm-hover: <yellow>Click to confirm this dangerous operation.
  sql-confirm-not-found: <yellow>Didn't found the operation that you given, it may invalid or expired.
  sql-executing: '<yellow>Executing SQL statement: <aqua>{0}'
  sql-completed: <green>Completed, {0} rows affected.
  sql-exception: <red>An error occurred while executing the SQL query, check the Console for details!
  sql-disabled: '<red>For security reason, SQL queries are disabled in this server, If you really need this feature, you can add the following flag to startup arguments: <aqua>{0}</aqua> with `true` value to enable it.'
  force-shop-reload: <yellow>Forcing reload all loaded shops...
  force-shop-reload-complete: <green>Forced <aqua>{0}</aqua> shops to be reloaded.
  force-shop-loader-reload: <yellow>Forcing shop-loader reloading...
  force-shop-loader-reload-unloading-shops: <yellow>Unloading <aqua>{0}</aqua> loaded shops...
  force-shop-loader-reload-unloading-shops-from-memory: <yellow>Removing <aqua>{0}</aqua> shops from memory...
  force-shop-loader-reload-reloading-shop-loader: <yellow>Re-calling shop-loader for reloading all shops from database...
  force-shop-loader-reload-complete: <green>Shop-loader has been reloaded all shops!
  toggle-shop-loaded-status: <aqua>Toggle this shop loaded status to <gold>{0}
  shop-internal-data: '<yellow>The internal data of this shop: </yellow>{0}'
  handler-list-not-valid-bukkit-event-class: <red>The provided class <yellow>{0}</yellow> is not a valid Bukkit event class.
  update-player-shops-signs-no-username-given: <red>You must provide a valid player username.
  update-player-shops-signs-create-async-task: <yellow>Creating async tasks for force updating signs...
  update-player-shops-player-selected: '<yellow>Player selected: <gold>{0}'
  update-player-shops-player-shops: <yellow>Total <gold>{0}</gold> shops waiting for updates.
  update-player-shops-per-tick-threshold: '<yellow>Max shops can be update per tick: <gold>{0}'
  update-player-shops-complete: <green>Task completed, Used <yellow>{0}ms</yellow> for updating.
  update-player-shops-task-started: <gold>The tasks has been started, please wait for it to complete.
  item-info-store-as-string: "<green>The store you looking at: <gold>{0}</gold> Hash: <white>{1}</white>"
  item-info-hand-as-string: "<green>The item in your hand: <gold>{0}</gold> Hash: <white>{1}</white>"
  item-matching-result: "<green>Hand2Store: <aqua>{0}</aqua>, Store2Hand: <aqua>{1}</aqua>"
  hikari-cp-size-tweak: "<green>HikariCP MaximumPoolSize and MinimumIdle was set to <white>{0}</white>"
  hikari-cp-testing: "<green>Please wait, testing HikariCP connection..."
  hikari-cp-working: "<green>Pass! HikariCP working well!"
  hikari-cp-not-working: "<red>Failed! The connection that returned by HikariCP is dead! (Not passed test in 1 second)"
  hikari-cp-timeout: "<red>HikariCP is timed out while getting a valid connection, please clean up all active queries to release connection resources."
  queries-stopped: "<green>Stopped <white>{0}</white> active queries."
  queries-dumping: "<yellow>Dumping active queries..."
  restart-database-manager: "<yellow>Restarting SQLManager..."
  restart-database-manager-clear-executors: "<yellow>Clearing executors..."
  restart-database-manager-unfinished-task: "<yellow>Unfinished task: <white>{0}</white>"
  restart-database-manager-unfinished-task-history-query: "<yellow>Unfinished task (History Query): <white>{0}</white>"
  restart-database-manager-reconnect: "<yellow>Re-launching SQLManager via initial sequence (via async executor)"
  restart-database-manager-done: "<green>Done!"
  property-incorrect: "<yellow>You must enter a (and only one) property key=value set. E.g   aaa=bbb"
  property-security-block: "<red>Request was rejected, for security reason, you can only change the property that startsWith <aqua>com.ghostchu.quickshop</aqua> or <aqua>quickshop</aqua>."
  property-removed: "<green>Removed property key <white>{0}</white>"
  property-changed: "<green>Property key <white>{0}</white> was changed from <white>{1}</white> to <white>{2}</white>"
  marked-as-dirty: "<green>Marked all stores as dirty status, they will be force save in next auto-save task. (Restart the server to force execute store saving task)"
  display-removed: "<green>Successfully removed <yellow>{0}</yellow> QuickShop display items/entities from the worlds."
database:
  scanning: <green>Scanning the isolated data in QuickShop databases, the database load may increase in scan progress, this might need a while...
  scanning-async: <yellow>Scanning the isolated data in QuickShop databases on async task thread, the database load may increase in scan progress, this might need a while, try again later.
  already-scanning: <red>An scan task has been started, please wait until it finished.
  trim-warning: <red><bold>Warning:</bold> <yellow>Backup your database before continuing the Database trim to avoid data loss. Once you're ready, execute <aqua>/quickshop database trim confirm</aqua> to continue.
  status: '<yellow>Status: {0}'
  status-good: <green>Good
  status-bad: <yellow>Maintenance Required
  isolated: '<yellow>Isolated Data:'
  isolated-data-ids: '<aqua>└<yellow> Data Records: <gold>{0}'
  isolated-shop-ids: '<aqua>└<yellow> Shop Indexes: <gold>{0}'
  isolated-logs: '<aqua>└<yellow> Logs: <gold>{0}'
  isolated-external-caches: '<aqua>└<yellow> External Caches: <gold>{0}'
  last-purge-time: <yellow>Last time of trim at {0}
  report-time: <yellow>Last time of scan at {0}
  auto-scan-alert: <yellow>The QuickShop database on this server require a maintenance. Found <gold>{0}</gold> isolated data wait for trimming.
  auto-trim: <green>Auto trim has been enabled on this server, no manually trim required.
  trim-complete: <green>Database trim completed, <yellow>{0}</yellow> isolated trimmed.
  auto-trim-started: <green>Auto trim has been started, please wait...
  trim-start: <green>Database trim started, please wait...
  trim-exception: <red>Database trim failed, an exception raised during database trimming, check the server console.
  generated-at: '<yellow>Generated at: <gold>{0}'
  purge-date: <red>You must given a date.
  purge-warning: <yellow>This operation will purge the history that stored in your database including shop creates/changes/deletes, purchases, transactions and system logs. Delete those data can free the disk space but all history metric will lost, and other plugins that depend on metrics will stop working. To continue execute this operation, execute `/quickshop database purgelogs \<before-days> confirm`
  purge-task-created: <green>Task created! Database are silent purging history records in background.
  purge-done-with-line: <green>Purge task completed, total <gold>{0}</gold> lines purged from database.
  purge-done-with-error: <red>Purge task failed, check the server console for details.
  purge-players-cache: <yellow>Please wait, purging players caches...
  purge-players-completed: |-
    <green>Successfully purged {0} players caches from both memory and database.
    <aqua>Note: You server performance may impact by this operation.
  purge-players-error: <red>Failed to purge players caches, please check the server console.
  suggestion:
    trim: <yellow>This database requires a trimming of isolated data. Execute <aqua>/quickshop database trim</aqua> to trim the database.
always-counting-removal-early-warning: <red>Always Counting feature is scheduled for removal, you shouldn't use it anymore and will stop work in future.
exporting-database: <green>Exporting database, please wait...
exporting-failed: <red>Failed to export database, please check the server console.
exported-database: <green>Database exported to <yellow>{0}</yellow>.
importing-not-found: <red>The <yellow>{0}</yellow> file not found, please check the file path.
importing-early-warning: |-
  <red><bold>Warning:</bold> <yellow>The backup will be imported into the current database. Any existing data will be purged and is lost forever unless you have a backup.
  <red>Are you sure to continue the import-procedure?</red> Type <aqua>/quickshop recovery confirm</aqua> to continue.
importing-database: <green>Importing database from backup, please wait...
importing-failed: <red>Failed to import database, please check the server console.
imported-database: <green>Database imported from <yellow>{0}</yellow>.
transfer-sent: <green>Shops transfer request sent to <yellow>{0}</yellow>.
transfer-request: <yellow>Player <aqua>{0}</aqua> requested to transfer their shops to you. Did you want accept the transfer?
transfer-single-request: <yellow>Player <aqua>{0}</aqua> would like to transfer a shop to you. Do you want to accept the request?
transfer-ask: |-
  <gold>Type <red>/quickshop transfer accept</red> to accept or <red>/quickshop transfer deny</red> to deny.
  The request will expire after <red>{0}</red> seconds.
transferall-ask: |-
  <gold>Type <red>/quickshop transferall accept</red> to accept or <red>/quickshop transferall deny</red> to deny.
  The request will expire after <red>{0}</red> seconds.
transfer-single-ask: |-
  <gold>Type <red>/quickshop transferownership accept</red> to accept or <red>/quickshop transferownership deny</red> to deny.
  The request will expire after <red>{0}</red> seconds.
transfer-accepted-fromside: <green>Player <aqua>{0}</aqua> accepted your shop transfer request.
transfer-accepted-toside: <green>You accepted <aqua>{0}</aqua>'s transfer request.
transfer-rejected-fromside: <red>Player <aqua>{0}</aqua> rejected your shop transfer request.
transfer-rejected-toside: <red>You rejected <aqua>{0}</aqua>'s shop transfer request.
transfer-no-pending-operation: <red>You don't have pending transfer request yet.
transfer-no-self: <red>You can't transfer your shops to yourself.
benefit-overflow: <red>The sum of all benefits cannot be greater than or equal to 100%.
benefit-exists: <red>Target player already in benefits list on this shop.
benefit-removed: <red>Target player has been removed from shop benefits.
benefit-added: <green>Player <aqua>{0}</aqua> has been added into shop benefits!
benefit-updated: <green>Player <aqua>{0}</aqua>'s benefits has been updated!
benefit-query: <green>This shop have <yellow>{0}</yellow> players in benefits list!
benefit-query-list: <yellow> - </yellow><white>Player <gold>{0}</gold>, Benefit <gold>{1}%
tag-added: '<green>Successfully added <aqua>#{0}</aqua> to this shop!'
tag-add-duplicate: '<red>The tag <aqua>#{0}</aqua> already exists at this shop!'
tag-removed: '<green>Successfully removed <aqua>#{0}</aqua> from this shop!'
tag-remove-not-exists: 'The tag <aqua>#{0}</aqua> not exists at this shop!'
tag-cleared: <green>Successfully cleared all tags from this shop!
tag-shops-cleared: '<green>Successfully cleared <aqua>#{0}</aqua> from all your tagged shops!'
tag-query: '<green>This shop have <yellow>{0}</yellow> tags:'
tag-query-listing: '<yellow> - <aqua>#{0}'
tag-query-no-tag: <red>This shop have no tags.
tag-query-shops: '<green>This tag contains <yellow>{0}</yellow> shops:'
tag-query-shops-listing: <yellow> - <aqua>{0}
batch-operations-based-on-tags-no-failure: <green>Successfully batch processed <yellow>{0}</yellow> shops.
batch-operations-based-on-tags-have-failure: <yellow>Total {0} shops in batch processing successfully completed, but <red>{1}</red> requests not completed.
batch-operations-based-on-tags-have-failure-with-reason: '<yellow>Total {0} shops in batch processing successfully completed, but <red>{1}</red> requests not completed. Reason: <gold>{2}'
batch-operations-based-on-tags-have-failure-error-messages-may-appears-above: <gray>The error messages will send to you above this message in the chat.
addon:
  towny:
    commands:
      town: <yellow>Set or unset a shop to a Town shop
      nation: <yellow>Set or unset a shop to a Nation shop
    make-shop-owned-by-town: <green>You have made the shop owned by town <yellow>{0}</yellow>.
    make-shop-no-longer-owned-by-town: <green>You have reset shop ownership, it now transfer back to original shop owner.
    make-shop-owned-by-nation: <green>You have made the shop owned by nation <yellow>{0}</yellow>.
    make-shop-no-longer-owned-by-nation: <green>You have reset shop ownership, it now transfer back to original shop owner.
    shop-owning-changing-notice: <gray>This shop now owned by a town/nation, original shop owner has been automatically added into administrator list, modify or add any new owner / staff by using command /quickshop permission
    target-shop-already-is-town-shop: <red>The target shop is already owned by a town.
    target-shop-already-is-nation-shop: <red>The target shop is already owned by a nation.
    target-shop-not-in-town-region: <red>The target shop is not in the town.
    target-shop-not-in-nation-region: <red>The target shop is not in the nation.
    item-not-allowed: <red>This shop's item are not allowed used by town/nation shop, pick another one!
    operation-disabled-due-shop-status: <red>This shop operation has been disabled due this already is a town/nation shop.
    plot-type-disallowed: <red>You can't create town/nation shop on this type of plot.
    flags:
      own: <red>You can only create shops at your owned shop type towny plot.
      modify: <red>You didn't have build permission on this towny plot.
      shop-type: <red>You must create shop on a shop type towny plot.
  residence:
    creation-flag-denied: <red>You don't have permission to create shops in this residence.
    trade-flag-denied: <red>You don't have permission to purchase in this residence.
    you-cannot-create-shop-in-wildness: <red>You can't create shop in wildness.
  griefprevention:
    creation-denied: <red>You don't have permission to create shops in this claim.
    trade-denied: <red>You don't have permission to purchase shops in this claim.
  lands:
    world-not-enabled: <red>You are not allowed to create or purchase in this world.
    creation-denied: <red>You don't have permission to create shops in this Land area.
  plotsquared:
    no-plot-whitelist-creation: <red>You cannot create shop outside of Plot.
    no-plot-whitelist-trade: <red>You cannot purchase shop outside of Plot.
    creation-denied: <red>You don't have permission to create shops in this Plot.
    trade-denied: <red>You don't have permission to purchase shops in this Plot.
    flag:
      create: Create QuickShop-Hikari shops
      trade: Purchase QuickShop-Hikari shops
  superiorskyblock:
    owner-create-only: <red>Only island owner can create shop there.
    owner-member-create-only: <red>Only island owner or member can create shop there.
  worldguard:
    creation-flag-test-failed: <red>You don't have permission create shop in this WorldGuard area.
    trade-flag-test-failed: <red>You don't have permission trade shop in this WorldGuard area.
    reached-per-region-amount-limit: "<red>You have reached the maximum amount of shops in this region."
  discord:
    __to_translators: 'To translators: `addon.discord.discord-messages` use a special variable in strings, %%LANG_KEY%% instead of QuickShop''s style {LANG_INDEX}, do not translate anything inside double ''%'' section. BUT for any other section texts, it respect the normal QuickShop Text System syntax ({0},{1} etc.) This may be difficult to understand because two different variable forms are used in the same program at the same time, but trust me, it is reasonable. (you don''t need translate this string, it''s a comment)'
    __to_users: Like the QuickShop Text System, Discord addon will also auto-detect user language and use user's language to send Discord message, it following the QuickShop-Hikari's language system settings.
    __to_message_designer: 'Design your Discord message with GUI: https://glitchii.github.io/embedbuilder/, then copy the JSON code and paste it into the translation and here we go!'
    discord-enabled: <aqua>Successfully <green>enabled</green> your QuickShop discord messages, now you can receive the shop messages from Discord.
    discord-disabled: <aqua>Successfully <red>disabled</red> your QuickShop discord messages, now you won't receive the shop messages from Discord anymore.
    discord-not-integrated: <red>You hadn't link your Discord yet! Please link your Discord account first!
    feature-enabled-for-user: <aqua>You have <green>enabled</green> <gold>{0}</gold> notification.
    feature-disabled-for-user: <aqua>You have <red>disabled</red> <gold>{0}</gold> notification.
    link-help: <yellow>This server using <gold>{0}</gold> for Discord driver, please use <green>{0}</green> to link your Discord account.
    save-notifaction-exception: <red>An error occurred while saving your Discord notifaction settings, please contact the server administrator.
    feature-status-changed: <green>Successfully set the notifaction <aqua>{0}</aqua> status to <gold>{1}
    commands:
      discord:
        description: <yellow>Managing your QuickShop Discord settings
    discord-messages:
      sold-to-your-shop: |
        {
         "embed":
           {
             "title": ":inbox_tray: Somebody sold items to your shop",
             "description": "The player %%purchase.name%% sold x%%purchase.amount%% %%shop.item.name%% to your shop.",
             "color": 52084,
             "author": {
               "name": "QuickShop-Hikari",
               "icon_url": ""
             },
             "thumbnail": {
               "url": ""
             },
             "image": {
               "url": ""
             },
             "footer": {
               "text": "QuickShop-Hikari Discord Notification",
               "icon_url": ""
             },
             "fields": [
               {
                 "name": "Shop",
                 "value": "%%shop.display-name%%",
                 "inline": false
               },
               {
                 "name": "Purchaser",
                 "value": "%%purchase.name%%",
                 "inline": true
               },
               {
                 "name": "Item",
                 "value": "%%shop.item.name%%",
                 "inline": true
               },
               {
                 "name": "Amount",
                 "value": "%%purchase.amount%% (x%%shop.stacking-amount%%)",
                 "inline": true
               },
               {
                 "name": "Your paid",
                 "value": "%%purchase.balance-formatted%%",
                 "inline": true
               },
               {
                 "name": "Taxes",
                 "value": "%%purchase.taxes-formatted%%",
                 "inline": true
               }
             ]
           }
        }
      bought-from-your-shop: |
        {
           "embed":
             {
               "title": ":outbox_tray: Somebody bought items from shop",
               "description": "The player %%purchase.name%% bought x%%purchase.amount%% %%shop.item.name%% from your shop.",
               "color": 52084,
               "author": {
                   "name": "QuickShop-Hikari",
                   "icon_url": ""
               },
               "thumbnail": {
                   "url": ""
               },
               "image": {
                   "url": ""
               },
               "footer": {
                   "text": "QuickShop-Hikari Discord Notification",
                   "icon_url": ""
               },
               "fields": [
                   {
                       "name": "Shop",
                       "value": "%%shop.display-name%%",
                       "inline": false
                   },
                   {
                       "name": "Purchaser",
                       "value": "%%purchase.name%%",
                       "inline": true
                   },
                   {
                       "name": "Item",
                       "value": "%%shop.item.name%%",
                       "inline": true
                   },
                   {
                       "name": "Amount",
                       "value": "%%purchase.amount%%(x%%shop.stacking-amount%%)",
                       "inline": true
                   },
                   {
                       "name": "Your earn",
                       "value": "%%purchase.balance-formatted%%",
                       "inline": true
                   },
                   {
                       "name": "Taxes",
                       "value": "%%purchase.taxes-formatted%%",
                       "inline": true
                   }
               ]
           }
         }
      mod-shop-purchase: |
        {
        "embed":
          {
              "title": ":dollar: Somebody purchased in a shop",
              "description": "The player %%purchase.name%% purchase x%%purchase.amount%% %%shop.item.name%% from the shop.",
              "color": 6061450,
              "author": {
                  "name": "QuickShop-Hikari",
                  "icon_url": ""
              },
              "thumbnail": {
                  "url": ""
              },
              "image": {
                  "url": ""
              },
              "footer": {
                  "text": "QuickShop-Hikari Discord Notification",
                  "icon_url": ""
              },
              "fields": [
                  {
                      "name": "Shop",
                      "value": "%%shop.display-name%%",
                      "inline": false
                  },
                  {
                      "name": "Purchaser",
                      "value": "%%purchase.name%%",
                      "inline": true
                  },
                  {
                      "name": "Item",
                      "value": "%%shop.item.name%%",
                      "inline": true
                  },
                  {
                      "name": "Amount",
                      "value": "%%purchase.amount%% (x%%shop.stacking-amount%%)",
                      "inline": true
                  },
                  {
                      "name": "Balance",
                      "value": "%%purchase.balance-formatted%%",
                      "inline": true
                  },
                  {
                      "name": "Taxes",
                      "value": "%%purchase.taxes-formatted%%",
                      "inline": true
                  }
              ]
          }
        }
      out-of-space: |
        {
        "embed":
          {
              "title": ":mailbox_with_mail: Your shop has run out of space",
              "description": "Your shop inventory is full of items!\\nYou need empty your shop container to free the space then you can continue to accept new items.",
              "color": 16065893,
              "author": {
                  "name": "QuickShop-Hikari",
                  "icon_url": ""
              },
              "thumbnail": {
                  "url": ""
              },
              "image": {
                  "url": ""
              },
              "footer": {
                  "text": "QuickShop-Hikari Discord Notification",
                  "icon_url": ""
              },
              "fields": [
                  {
                      "name": "Shop",
                      "value": "%%shop.display-name%%",
                      "inline": false
                  }
              ]
          }
        }
      out-of-stock: |
        {
            "embed":
                {
                    "title": ":mailbox_with_no_mail: Your shop has run out of stock",
                    "description": "Your shop inventory is empty now!\\nYou need to refill the shop container with items to continue selling items!",
                    "color": 16065893,
                    "author": {
                        "name": "QuickShop-Hikari",
                        "icon_url": ""
                    },
                    "thumbnail": {
                        "url": ""
                    },
                    "image": {
                        "url": ""
                    },
                    "footer": {
                        "text": "QuickShop-Hikari Discord Notification",
                        "icon_url": ""
                    },
                    "fields": [
                        {
                            "name": "Shop",
                            "value": "%%shop.display-name%%",
                            "inline": false
                        }
                    ]
                }
        }
      mod-shop-created: |
        {
          "embed": {
            "title": ":classical_building: A new shop has been created",
            "description": "Player created a new shop on your server!",
            "color": 6061450,
            "author": {
              "name": "QuickShop-Hikari",
              "icon_url": ""
            },
            "thumbnail": {
              "url": ""
            },
            "image": {
              "url": ""
            },
            "footer": {
              "text": "QuickShop-Hikari Discord Notification",
              "icon_url": ""
            },
            "fields": [
              {
                "name": "Shop",
                "value": "%%shop.display-name%%",
                "inline": false
              },
              {
                "name": "Owner",
                "value": "%%shop.owner.name%%",
                "inline": true
              },
              {
                "name": "Item",
                "value": "%%shop.item.name%%",
                "inline": true
              },
              {
                "name": "Amount",
                "value": "%%shop.stacking-amount%%",
                "inline": true
              },
              {
                "name": "Type",
                "value": "%%shop.type%%",
                "inline": true
              }
            ]
          }
        }
      mod-remove-shop: |
        {
            "embed": {
                "title": ":recycle: A shop was removed from this server",
                "description": "A shop was removed from this server.",
                "color": 6061450,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "Reason",
                        "value": "%%delete.reason%%",
                        "inline": false
                    }
                ]
            }
        }
      shop-transfer-to-you: |
        {
            "embed": {
                "title": ":placard: A shop was transfer to you",
                "description": "A shop transfer to you from another player.",
                "color": 16172079,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "From",
                        "value": "%%transfer.from%%",
                        "inline": false
                    }
                ]
            }
        }
      mod-shop-transfer: |
        {
            "embed": {
                "title": ":placard: A shop was transferred",
                "description": "A shop transfer to a player from another player.",
                "color": 6061450,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "From",
                        "value": "%%transfer.from%%",
                        "inline": true
                    },
                    {
                        "name": "To",
                        "value": "%%transfer.to%%",
                        "inline": true
                    }
                ]
            }
        }
      shop-price-changed: |
        {
            "embed": {
                "title": ":money_with_wings: Your shop price was changed",
                "description": "You or your shop staff changed your shop price.",
                "color": 16172079,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "From",
                        "value": "%%change-price.from%%",
                        "inline": true
                    },
                    {
                        "name": "To",
                        "value": "%%change-price.to%%",
                        "inline": true
                    }
                ]
            }
        }
      mod-shop-price-changed: |
        {
            "embed": {
                "title": ":money_with_wings: A shop price has been changed",
                "description": "A shop changed its price settings.",
                "color": 6061450,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": true
                    },
                    {
                        "name": "Owner",
                        "value": "%%shop.owner.name%%",
                        "inline": true
                    },
                    {
                        "name": "",
                        "value": "",
                        "inline": true
                    },
                    {
                        "name": "From",
                        "value": "%%change-price.from%%",
                        "inline": true
                    },
                    {
                        "name": "To",
                        "value": "%%change-price.to%%",
                        "inline": true
                    }
                ]
            }
        }
      shop-permission-changed: |
        {
            "embed": {
                "title": ":closed_lock_with_key: Your shop permission settings has been changed",
                "description": "One of your shop's permission setting has been changed.",
                "color": 15879747,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "Player",
                        "value": "%%change-permission.player%%",
                        "inline": true
                    },
                    {
                        "name": "Assign to group",
                        "value": "%%change-permission.to-group%%",
                        "inline": true
                    },
                    {
                        "name": "",
                        "value": "",
                        "inline": true
                    },
                    {
                        "name": "Permission granted (Inherit from group)",
                        "value": "```\\n%%change-permission.perms-list%%\\n```",
                        "inline": false
                    }
                ]
            }
        }
    field:
      player: Player
      item: Item
      amount: Amount
      balance: Balance
      balance-after-tax: Balance (after tax)
      account: Your account balance
      taxes: Taxes
      cost: Cost
  discount:
    commands:
      discount:
        description: <yellow>Apply a discount code or manage your discount codes.
    tab-complete:
      discount:
        general:
          code: \<code>
        create:
          rate: |
            Command Hint:
            Argument: \<rate>
            Description: The actual percentage or money you will earn
            Input `30%` = price * 0.3
            Input `50` = price - 50
          max-usage: |
            Command Hint:
            Argument: [max-usage]
            Description: The times that code can be used
            `-1` for unlimited.
          threshold: |
            Command Hint:
            Argument: [threshold]
            Description: The minimum price that code can be applied
            `-1` for unlimited
          expired: |
            Command Hint
            Argument: [expired]
            Description: The code expired time.
            `-1` for unlimited duration.
            Accept both Zulu time and UNIX timestamp in seconds.
            Zulu Example: 2022-12-17T10:31:37Z
            UNIX Example: 1671273097
    discount-code-already-exists: <red>Sorry, the name of your discount code already in use.
    invalid-discount-code-regex: '<red>The discount code must matches the regex: <yellow>{0}'
    invalid-discount-code: <red>This discount code are invalid.
    discount-code-added: <green>Your discount code <yellow>{0}</yellow> has been added into shop <aqua>{1}
    discount-code-removed: <green>Your discount code <yellow>{0}</yellow> has been removed from shop <aqua>{1}
    invalid-code-type: <red>The code type <yellow>{0}</yellow> are invalid.
    invalid-usage-restriction: <red>The usage restriction <yellow>{0}</yellow> are invalid.
    invalid-threshold-restriction: <red>The threshold restriction <yellow>{0}</yellow> are invalid.
    invalid-effect-scope: <red>The scope <yellow>{0}</yellow> are invalid.
    invalid-expire-time: <red>You can't specify a time in the past.
    invalid-discount-rate: <red>The discount rate <yellow>{0}</yellow> are invalid, it can be a fixed number or percentage.
    discount-code-expired: <red>Ouch! Your discount code <yellow>{0}</yellow> has been expired!
    discount-code-installed: <green>You have installed discount code <gold>{0}</gold>, the discount code will be automatically applied to the available purchase in all the following purchases during this session. To uninstall discount code, execute <aqua>/quickshop discount uninstall {0}</aqua>.
    discount-code-uninstalled: <green>You have uninstalled your discount code.
    discount-code-query-nothing: <red>You hadn't install a discount code yet!
    discount-code-query: '<green>You are using discount code: <yellow>{0}</yellow>.'
    discount-code-applicable: '<#bcef26>Your discount code <bold><yellow>{0}</yellow></bold> are <bold>applicable</bold> in this shop!'
    discount-code-applicable-with-threshold: '<#bcef26>Your discount code <bold><yellow>{0}</yellow></bold> are <bold>applicable</bold> in this shop, but only your purchase cost over <yellow>{1}</yellow> in single transaction.'
    discount-code-not-applicable: <red>Your discount code <bold><yellow>{0}</yellow></bold> are <bold>not applicable</bold> in this shop!
    discount-code-reach-the-limit: <red>You have reach the usage limit of discount code <bold><yellow>{0}</yellow></bold>, discount are not applied.
    discount-code-no-permission: <red>You didn't have permission to use any discount code in this shop!
    discount-code-has-been-expired: <red>Your discount has been expired!
    discount-code-config-shop-added: <green>Successfully to add this shop into your discount code allow list.
    discount-code-config-shop-add-failure: <red>This shop already in your discount code allow list.
    discount-code-config-shop-removed: <green>Successfully to remove this shop from your discount code allow list.
    discount-code-config-shop-remove-failure: <red>This shop not exists in your discount code allow list.
    discount-code-config-expire: <green>Successfully changed your code expire time.
    discount-code-config-applied: <green>Config the discount code successfully!
    discount-code-created-successfully: |
      <green>Your discount code <yellow>{0}</yellow> has been successfully created!
      <gold>Scope: <aqua>{1}</aqua></gold>.
      <gold>Share with others: <#bcef26>{2}</#bcef26></gold>.
      <yellow>For <gray>Specific Shops</gray> scope only: To add shops to your discount code allow list, please looking an quickshop and execute <aqua>{3}</aqua> command.
      You can use <aqua>/quickshop discount config {0}</aqua> to edit your discount code anytime.
    discount-code-under-threshold: <red>The discount not applied to your purchase because the total value under the discount code threshold <yellow>{0}</yellow>.
    percentage-off: '<bold><#bcef26>-{0}%'
    fixed-off: '<bold><#bcef26>-{0}'
    discount-code-list: '<gold>Listing your discount codes:'
    discount-code-applied-in-purchase: '<#bcef26>Your discount code <yellow>{0}</yellow> has been applied to this purchase and you saved <gold>{1}</gold>!'
    scope:
      this-shop: '{0}'
      your-shops-owned: all your shops (owned)
      your-shops-managed: all your shops (managed)
      server: whole server
    code-type:
      SERVER_ALL_SHOPS: All shops on this server
      PLAYER_ALL_SHOPS: All shops that created by code owner
      SPECIFIC_SHOPS: Specific shops
    discount-code-details: |-
      <gold>Code: <yellow>{0}</yellow>
      Creator: <yellow>{1}</yellow>
      Applied to: <yellow>{2}</yellow>
      Remaining usage: <yellow>{3}</yellow>
      Expired on: <yellow>{4}</yellow>
      Threshold: <yellow>{5}</yellow>
      Discount: <yellow>{6}</yellow><br>
  list:
    commands:
      list: <yellow>Listing all shops owned by your or specific player
    table-prefix: '<yellow><green>{0}</green>''s shops <gray>(Total {1})</gray>: '
    table-prefix-pageable: '<yellow><green>{0}</green>''s shops <gray>(Page {1}/{2})</gray>: '
    entry: <yellow><hover:show_text:'<yellow>{1}</yellow><br><gray>{2} {3},{4},{5}</gray><br><aqua>Price <yellow>{6}</yellow> - <light_purple>x{7}</light_purple> <yellow>{8}</yellow><br><green>{9}'>{0}. <aqua>{1}
  shopitemonly:
    message: <red>You can't put non-shop items into shop container, all non-shop items will dropped at your location.
  limited:
    commands:
      limit: <yellow>Set a limit that restrict the purchase that player can make in period
    titles:
      title: <green>Purchase Success
      subtitle: <aqua>You can also purchase <gold>{0}</gold> more in this shop
    reach-the-limit: <red>You have reached the limit in this shop. You can also purchase <green>{0}</green> items, but you trying purchase <yellow>{1}</yellow> items.
    success-reset: <green>Reset the restriction of this shop successfully
    success-remove: <green>Successfully remove the all restriction of this shop
    success-setup: <green>Restriction settings for this shop were saved successfully
    trade-limit-reached-cancel-reason: <red>Reached the restriction of this shop
    remains-limits: '<gold>Your remaining purchase quota in this shop: <yellow>{0}'
    remains-limits-before-reset-period-note: '<gold>This shop will reset their purchase quota every: <yellow>{0}'
  dynmap:
    markerset-title: QuickShop-Hikari Shops
    marker-name: '{0} @ {2} @ {3} for x{4} @ {5}'
    marker-description: |
      Name: {0}
      Owner: {1}
      Item: {2}
      Price: {3} for x{4} item(s)
      Type: {5}
      Unlimited: {6}
      Location: {7}
  bluemap:
    markerset-title: QuickShop-Hikari Shops
    marker-name: '{0} @ {2} @ {3} for x{4} @ {5}'
    marker-description: |
      Name: {0}
      Owner: {1}
      Item: {2}
      Price: {3} for x{4} item(s)
      Type: {5}
      Unlimited: {6}
      Location: {7}
  chestprotect:
    protection-exists: <red>This region already protected by ChestProtect and you don't have permission to create shops at here.
    shops-exsts: <red>The region that you want to protect contains other player's shops and you don't have permission to access them.
  displaycontrol:
    toggle: |-
      <green>Successfully toggle your QuickShop display showcasing to <aqua>{0}</aqua>.
      <yellow>You may need re-join to take effect.
    toggle-exception: <red>Failed to toggle your display showcase settings due an internal error, please contact server administrator.
    command:
      displaycontrol: <yellow>Toggle your QuickShop display showcasing
  reremake-migrator:
    commands:
      migratefromreremake: Migrate QuickShop-Reremake's data to QuickShop-Hikari
    server-not-empty: "<red>No players are allowed to be online on the server during the conversion progress, please enable server whitelist or maintenance mode."
    starting-convert-progress: "<gold>Starting the convert progress, <red>DO NOT SHUTDOWN YOUR SERVER!</red>"
    executing: "<gold>Executing migrate component <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
    completed: "<green>Done! Migrate completed, please restart your server and remove QuickShop-Reremake from plugins directory."
    join_blocking_converting: "<red>[QuickShop-Hikari Migrator] This server is performing a data conversion and you cannot join the server at this time, try again later!"
    join_blocking_finished: "<red>[QuickShop-Hikari Migrator] This server has just completed a data conversion and is waiting for a reboot to apply the changes, try again later!"
    failed: "<red>Migration progress exited with error, please check the server console."
    modules:
      config:
        copy-values: "<yellow>Copying values (total {0} entries)..."
        copying-value: "<gray> - Copying <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        migrate-price-restriction: "<yellow>Migrating price-restriction related settings..."
        migrate-price-restriction-entry: "<gray> - Migrating <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
      shop:
        start-migrate: "<yellow>Migrating shops (total {0} entries)..."
        migrate-entry: "<gray> - Migrating <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        unloading-reremake: "<gold>Unloading Reremake to avoid data overwrite..."
        register-entry: "<gray> - Registering <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        save-entry: "<gray> - Saving <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        saving-shops: "<yellow>Saving <gold>{0}</gold> shops, this may need a while™ (the time based on shops amounts)..."
        conflict: "<gray> - CONFLICT > Detects a conflict between a Reremake store and an existing Hikari store location, taking a predefined behavior: <dark_gray>{0}</dark_gray>"
      translation:
        start-migrate: "<yellow>Migrating translation files...</yellow>"
        copy-values: "<yellow>Copying values in locale <gold>{0}</gold> (total {1} entries)..."
        migrate-entry: "<gray> - Migrating <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        copying-value: "<gray> - Copying <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
      shop-logs:
        start-migrate: "<yellow>Migrating shop logs (details in console), please wait...</yellow>"
        extract-history-files: "<gray>Please wait for us to unzip and append the history logs..."
        filter-history-files: "<gray>Please wait for us to filter the history log..."
        filtered-history-files: "<gray>Filtered {0} lines out of queue."
        import-entry: "<gray> - Migrating <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
compat:
  advancedchests:
    created: <green>You're successfully created a shop on AdvancedChests container!
    permission-denied: <red>Sorry! You don't have the permission to create a shop on a AdvancedChests container!
suite:
  dynmap:
    title: '{0} - {1} - {2}'
    description: |-
      Item: {0}
      Owner: {1}
      Type: {2} {3}
      Price: {4}
      Location: {5} {6}, {7}, {8}
      Space: {9}
      Stock: {10}
  limited:
    command-description: <yellow>Set a limit that restrict the purchase that player can make in period.
    reach-the-quota-limit: <red>You have reach the purchase limit in this shop ({0}/{1}).
    quota-reset-countdown: <yellow>The quota in this shop will reset in {0}.
    quota-reset-player-successfully: <green>The quota for player {0} in this shop has been reset successfully.
    quota-reset-everybody-successfully: <green>The quota for everyone in this shop has been reset successfully.
    quota-setup: <green>The purchase restriction now apply to this shop!
    quota-remove: <green>The purchase restriction now removed from this shop!
    subtitles:
      title: <green>Purchase Success
      subtitle: <aqua>You can purchase <yellow>{0}</yellow> more in this shop
  list:
    command-description: <yellow>List all shops owned by yourself or another player.
    table-prefix: <yellow>You owned total <aqua>{0}</aqua> shops on this server.
    entry-syntax: <aqua>- <yellow>{0}
    entry-syntax-fallback-name: '<yellow>Item:{0} X:{1}, Y:{2}, Z:{3}, World: {4}'
    hover-text: |-
      <yellow>{0}
      <gray>{1} {2}, {3}, {4}
      <aqua>{5} - <yellow>{6}
      {7} items each stack.
  shopitemonly:
    message: <red>You can't put non-shop items into shop container, all non-shop items will dropped at your location.
compatibility:
  elitemobs:
    soulbound-disallowed: You can't trade item which have EliteMobs Soulbound enchantment.
internet-paste-forbidden-privacy-reason: "<red>Failed! According your privacy setting, QuickShop-Hikari are not allowed upload your paste to Internet, turn on DIAGNOSTIC permission in privacy setting in config.yml or use <aqua>/quickshop paste --file</aqua> instead."
no-sign-type-given: "<red>You need give a sign material, the sign materials that available on this server: {0}"
sign-type-invalid: "<red>The type <yellow>{0}</yellow> not a valid sign material."
delete-controlpanel-button-confirm: "<red>Do you really want remove this shop? Click the <bold>[Remove Shop]</bold> button again in {0} seconds to confirm."
cannot-suggest-price: "<red>Sorry, For the time being, no more people are trading the same item as you and there is not enough data to generate a suggested price."
price-suggest: "<green>Based on data from <aqua>{0}</aqua> stores, the highest priced store is priced at <light_purple>{1}</light_purple>, the lowest priced store is priced at <light_purple>{2}</light_purple>, the average price is <light_purple>{3}</light_purple>, and the median price is <light_purple>{4}</light_purple>. <newline><yellow>It's recommended that you set your price around <gold>{5}</gold>.</yellow>"
suggest-wait: "<green>Please wait... Calculating recommend price."
history:
  shop:
    gui-title: "View purchase history"
    header-icon-multiple-shop: "<white>The query result across {0} shops</white>"
    header-icon-description:
      - "<white>Type: <yellow>{0}</yellow></white>"
      - "<white>Owner: <yellow>{1}</yellow></white>"
      - "<white>Item: <yellow>{2}</yellow></white>"
      - "<white>Price: <yellow>{3} <aqua>x{4}</aqua></yellow></white>"
      - "<white>Location: <yellow>{5}</yellow></white>"
    log-icon-title: "<reset><green>Time: {0}</green>"
    log-icon-description:
      - "<white>Purchaser: <yellow>{0}</yellow></white>"
      - "<white>Item: <yellow>{1} <aqua>x{2}</aqua></yellow></white>"
      - "<white>Balance: <yellow>{3}</yellow></white>"
      - "<white>Tax: <yellow>{4}</yellow></white>"
    log-icon-description-with-store-name:
      - "<white>Store: <yellow>{0}</yellow></white>"
      - "<white>Purchaser: <yellow>{1}</yellow></white>"
      - "<white>Item: <yellow>{2} <aqua>x{3}</aqua></yellow></white>"
      - "<white>Balance: <yellow>{4}</yellow></white>"
      - "<white>Tax: <yellow>{5}</yellow></white>"
    query-icon: "<gray>Please wait, querying...</gray>"
    previous-page: "<white><< Previous Page</white>"
    next-page: "<white>Next Page >></white>"
    current-page: "<white>Page {0}</white>"
    summary-icon-title: "<green>Shop Summary"
    recent-purchases: "<white>Recent <aqua>{0}</aqua> purchases: <yellow>{1}</yellow></white>"
    recent-purchase-balance: "<white>Recent <aqua>{0}</aqua> turnover: <yellow>{1}</yellow></white>"
    total-purchases: "<white>Total purchases: <yellow>{0}</yellow></white>"
    total-balances: "<white>Total turnover: <yellow>{0}</yellow></white>"
    total-unique-purchasers: "<white>Total unique purchasers: <yellow>{0}</yellow></white>"
    top-n-valuable-customers-title: "<gold>Top {0} valuable customers</gold>"
    top-n-valuable-customers-entry: "<white>- <yellow>{0}</yellow> <aqua>{1}</aqua></white>"
    no-result: "<white>No result</white>"
about:
  text:
    - "<b><aqua>QuickShop <yellow>{0}</yellow></aqua></b>"
    - "<aqua>Version <yellow>>></yellow> <green>{1}</green></aqua>"
    - "<aqua>Release <yellow>>></yellow> <green>{2}</green></aqua>"
    - "<aqua>Developers <yellow>>></yellow> <color:#218bff><hover:show_text:'<yellow>Click to check contributors'><click:open_url:'https://github.com/Ghost-chu/QuickShop-Hikari/graphs/contributors'>[View contributors on GitHub]</click></hover></color></aqua>"
    - "<aqua>Localized members <gold>({4})</gold>: <yellow>>></yellow> <green>{5}</green></aqua>"
    - "  <yellow>+ <color:#43A047><hover:show_text:'<yellow>Click to open Crowdin translate page'><click:open_url:'https://crowdin.com/project/quickshop-hikari'>[Help translate on Crowdin]</click></hover></color></yellow>"
    - "<aqua>Donation Key <yellow>>></yellow> <green>{6}</green></aqua>"
    - "<gold>Powered by community</gold> <red>Made with ❤</red>"
  valid-donation-key: "<color:#00AFF1>Bound to <color:#F9C23C>{0}</color></color>"
  invalid-donation-key: "<gray>Invalid donation key</gra>"
  kofi-thanks: "<gold>Special thanks to those who support QuickShop on Ko-fi :)</gold>"
history-command-leave-blank: "<Leave blank for seeing store>"
shop-information-not-shown-due-an-internal-error: "<red>An internal error occurred. The store information panel may be displayed incompletely, please contact the server administrator."
