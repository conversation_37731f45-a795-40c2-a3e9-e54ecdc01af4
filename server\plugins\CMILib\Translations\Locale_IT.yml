# Full color code support and some variables
# Keep in mind that variables wont work for some lines, when it will for anothers :)
# Just keep them where there are now and everything will be ok :)
# Some lines can have global variables set. For player who will be effected. In example /heal Zrips then Zrips data will be used
# [serverName] to show server name
# [playerName] to show target player name
# [playerDisplayName] to show target player display name
# [lvl] to show target player level
# [exp] to show target player total exp
# [hp] to show target player health
# [maxHp] to show target player max health
# [hunger] to show target player hunger level
# [gameMode] to show target player gamemode
# [prefix] to show target player prefix if possible
# [suffix] to show target player suffix if possible
# Sender is console or player who performs command. In example Zrips performs /heal Zhax then Zrips data will be used
# [senderName] to show Sender player name
# [senderDisplayName] to show Sender player display name
# [senderLvl] to show Sender player level
# [senderExp] to show Sender player total exp
# [senderHp] to show Sender player health
# [senderMaxHp] to show Sender player max health
# [senderHunger] to show Sender player hunger level
# [senderGameMode] to show Sender player gamemode
# [senderPrefix] to show Sender player prefix if possible
# [senderSuffix] to show Sender player suffix if possible
# Source is player which is being used for extra info. In example Zrips performs /tp Zhax Zrips then Zhax data will be used as its location is being taken for new player location
# [sourceName] to show source player name
# [sourceDisplayName] to show source player display name
# [sourceLvl] to show source player level
# [sourceExp] to show source player total exp
# [sourceHp] to show source player health
# [sourceMaxHp] to show source player max health
# [sourceHunger] to show source player hunger level
# [sourceGameMode] to show source player gamemode
# [sourcePrefix] to show source player prefix if possible
# [sourceSuffix] to show source player suffix if possible
# ***********************************************
# Some lines supports option to send them to custom places, like action bar, title, sub title or even create JSON/clickable messages
# If line starts with !toast! then player will get toast message (advancement popup, only 1.12 and up). Some extra variables can be used to define type and icon. example: !toast! -t:goal -icon:paper Hello world!
# If line starts with !actionbar! then player will get action bar message defined after this variable
# If line starts with !actionbar:[seconds]! then player will get action bar message for defined amount of time
# If line starts with !broadcast! then everyone will receive message. You can add extra !toast! !actionbar! or !title! to send message for everyone to specific place, in example !broadcast!!title!
# If line starts with !customtext:[cTextName]! then custom text will be taken by name provided and shown for player. In case its used after !broadcast! then everyone who is online will get this custom text message
# If line starts with !title! then player will get title message defined after this variable, in addition it can contain !subtitle! which will add subtitle message
# If line starts with !bosbar:[name]-[timer]! then player will get bossbar message defined after this variable, in addition you can define how long this message will be visible. You need to define bossbar name which can be anything you want, but lines with same name will override each other to prevent stacking
# To include clickable messages: <T>Text</T><H>Hover text</H><C>command</C><SC>Suggested text</SC>
# <T> and </T> required, other is optional
# Use /n to break line
# To have more than one JSON message use <Next>
# <C> performs command as a player who clicked
# <CC> performs command from console once
# <CCI> performs command from console every time player clicks text
# <URL> includes url
info:
  # Use !prefix! in any locale line to automatically include this prefix
  prefix: '&e[&aCMI&e] '
  NoPermission: '&cNon hai il permesso!'
  CantHavePermission: '&cNon hai questo permesso!'
  WrongGroup: '&cYou are in wrong group for this!'
  NoPlayerPermission: '&c[playerName] non ha il permesso per: [permission]'
  Ingame: '&cNon puoi usarlo in game!'
  NoInformation: '&cInformazioni non trovate!'
  Console: '&6Server'
  FromConsole: '&cPuoi usarlo solo da console!'
  NotOnline: '&cQuesto giocatore non è online!'
  NobodyOnline: '&cNon c''è nessuno online!'
  Same: '&cNon è possibile aprire il proprio inventario per modificarlo!'
  cantLoginWithDifCap: '&cNon è possibile entrare nel server con caratteri modificati!
    Vecchio nome: &e[oldName]&c. Attuale: &e[currentName]'
  Searching: '&eCercando i dati dei giocatori, aspetta, può prendere alcuni minuti!'
  NoPlayer: '&cNon è stato trovato un giocatore con questo nick!'
  NoCommand: '&cNon c''è alcun comando con questo nome!'
  NoCommandWhileSleeping: '&cCan''t perform commands while sleeping!'
  cantFindCommand: '&5Non è possibile trovare il comando &7[%1]&5, intendevi &7[%2]&5?'
  nolocation: '&4Can''t find suitable location'
  PurgeNotEnabled: '&cLa funzione di purge non è abilitata da config!'
  FeatureNotEnabled: '&cQuesta feature non è abilitata!'
  TeamManagementDisabled: '&7This feature will have limited functionalaty while DisableTeamManagement
    is set to true!'
  ModuleNotEnabled: '&cQuesto modulo non è abilitato!'
  versionNotSupported: '&cLa versione del server non supporta questa feature'
  bungeeNoGo: '&cThis feature will not work on bungee network based servers'
  clickToTeleport: '&eClicca per teletrasportarti'
  UseMaterial: '&4Please use material names!'
  IncorrectMaterial: '&4Incorrect material name!'
  UseInteger: '&4Gentilmente usa numeri!'
  UseBoolean: '&4Gentilmente usa True o False!'
  NoLessThan: '&4Il numero non può essere sotto [amount]!'
  NoMoreThan: '&4Il valore non può essere superiore a [amount]'
  NoGameMode: '&cGentilmente usa 0/1/2/3 o Survival/Creative/Adventure/Spectator o
    s/c/a/sp!'
  NoWorld: '&4Non è possibile trovare un mondo con questo nome!'
  IncorrectLocation: '&4Posizione definita in modo errato!'
  NameChange: '&6[playerDisplayName] &eloggato, anche conosciuto come: &6[namelist]'
  Cooldowns: '&eQuesto comando è in cooldown per altri &6[time]'
  specializedCooldowns: '&eComandi in cooldown, gentilmente aspetta &6[time]'
  specializedRunning: '&eCommand still running, please wait &6[time]'
  CooldownOneTime: '&eQuesto comando può essere usato una sola volta!'
  WarmUp:
    canceled: '&eComando cancellato poichè ti sei mosso'
    counter: '!actionbar!&6--> &e[time] &6<--'
    DontMove: '!title!!subtitle!&6Non ti muovere!'
    Boss:
      DontMove: '&4Don''t move for &7[autoTimeLeft] &4seconds!'
      WaitFor: '&4Wait for &7[autoTimeLeft] &4seconds!'
  Spawner: '&r[type] Spawner'
  FailedSpawnerMine: '!actionbar!&cFailed to mine spawner. &7[percent]% &cdrop chance'
  ClickSpawner: '!actionbar!&7[percent]% &eDrop chance'
  Elevator:
    created: '&eCrea ascensore con i cartelli'
  CantPlaceSpawner: '&eNon è possibile piazzare due spawner così vicini (&6[range]&e)'
  ChunksLoading: '&eI dati dei chunk del mondo sono in caricamento. Aspetta un pochetto
    e ritenta.'
  ShulkerBox: Shulker Box
  CantUseNonEncrypted: '!actionbar!&cCommands on this item are not encrypted. Can''t
    use them!'
  CantDecode: '!actionbar!&cCan''t decode message/command. Key file contains wrong
    key for this task. Inform server administration about this'
  Show: '&eMostra'
  Remove: '&cRimuovi'
  Back: '&eIndietro'
  Forward: '&eInoltrare'
  Update: '&eAggiorna'
  Save: '&eSalva'
  Delete: '&cElimina'
  Click: '&eClicca'
  Preview: '&ePreview'
  PasteOld: '&eIncolla vecchio'
  ClickToPaste: '&eClicca per incollare nella chat'
  CantTeleportWorld: '&eNon ti puoi teletrasportare in questo mondo'
  CantTeleportNoWorld: '&cTarget world doesn''t exist. Teleportation canceled'
  CantTeleport: '&eNon puoi teletrasportarti perchà© hai troppi oggetti limitati.
    Scorrere su questa linea per vedere la quantità  massima di oggetti consentiti.'
  ClickToConfirmDelete: '&eClick to confirm removal of &6[na