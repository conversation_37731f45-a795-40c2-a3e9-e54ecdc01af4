luckperms.logs.actionlog-prefix=KAYIT
luckperms.logs.verbose-prefix=AYRINTI
luckperms.logs.export-prefix=DIŞA AKTAR
luckperms.commandsystem.available-commands=Kullanılabilir komutları görüntülemek için {0} kullanın
luckperms.commandsystem.command-not-recognised=Komut geçersiz
luckperms.commandsystem.no-permission=Bu komutu kullanmak için yetkiniz yok\!
luckperms.commandsystem.no-permission-subcommands=Alt komutları kullanmak için izniniz yok
luckperms.commandsystem.already-executing-command=Başka bir komut çalıştırıldığı için bitmesi bekleniyor...
luckperms.commandsystem.usage.sub-commands-header=Alt Komutlar
luckperms.commandsystem.usage.usage-header=Komut Kullanımı
luckperms.commandsystem.usage.arguments-header=Argümanlar
luckperms.first-time.no-permissions-setup=Hiçbir yetki ayarlanmamış gibi gözüküyor\!
luckperms.first-time.use-console-to-give-access=Oyunda LuckPerms komutlarını kullanmadan önce, konsoldan kendinize yetki vermeniz gerekmektedir
luckperms.first-time.console-command-prompt=Konsolunuzu açınız ve çalıştırınız
luckperms.first-time.next-step=Bunu tamamladıktan sonra, yetki ve grupları tanımlamaya başlayabilirsin
luckperms.first-time.wiki-prompt=Nereden başlayacağınızı bilmiyor musun? Buraya göz at\: {0}
luckperms.login.try-again=Lütfen daha sonra tekrar deneyiniz
luckperms.login.loading-database-error=Yetki verilerini yüklerken bir veritabanı hatası oluştu
luckperms.login.server-admin-check-console-errors=Eğer sunucu yöneticisiyseniz, hataları görmek için konsolu kontrol ediniz
luckperms.login.server-admin-check-console-info=Daha fazla bilgi için konsolu kontrol edin
luckperms.login.data-not-loaded-at-pre=Giriş öncesi aşamada kullanıcınız için yetki verileri yüklenmedi
luckperms.login.unable-to-continue=devam edilemiyor
luckperms.login.craftbukkit-offline-mode-error=bu hata muhtemelen CraftBukkit ve online-mode ayarı arasındaki bir çelişkiden kaynaklanıyor
luckperms.login.unexpected-error=Yetkileriniz ayarlanırken beklenmeyen bir hata oluştu.
luckperms.opsystem.disabled=Standart OP sistemi bu sunucuda deaktif
luckperms.opsystem.sponge-warning=Bir yetki eklentisi yüklediğiniz zaman Sponge izin kontrolleri üzerinde hiçbir etkisi olmadığını lütfen unutmayın, kullanıcı verilerini doğrudan düzenlemelisiniz
luckperms.duration.unit.years.plural={0} yıl
luckperms.duration.unit.years.singular={0} yıl
luckperms.duration.unit.years.short={0}y
luckperms.duration.unit.months.plural={0} ay
luckperms.duration.unit.months.singular={0} ay
luckperms.duration.unit.months.short={0}ay
luckperms.duration.unit.weeks.plural={0} hafta
luckperms.duration.unit.weeks.singular={0} hafta
luckperms.duration.unit.weeks.short={0}h
luckperms.duration.unit.days.plural={0} gün
luckperms.duration.unit.days.singular={0} gün
luckperms.duration.unit.days.short={0}g
luckperms.duration.unit.hours.plural={0} saat
luckperms.duration.unit.hours.singular={0} saat
luckperms.duration.unit.hours.short={0}s
luckperms.duration.unit.minutes.plural={0} dakika
luckperms.duration.unit.minutes.singular={0} dakika
luckperms.duration.unit.minutes.short={0}d
luckperms.duration.unit.seconds.plural={0} saniye
luckperms.duration.unit.seconds.singular={0} saniye
luckperms.duration.unit.seconds.short={0}sn
luckperms.duration.since={0} önce
luckperms.command.misc.invalid-code=Geçersiz kod
luckperms.command.misc.response-code-key=yanıt kodu
luckperms.command.misc.error-message-key=mesaj
luckperms.command.misc.bytebin-unable-to-communicate=Bytebin ile iletişim kurulamıyor
luckperms.command.misc.webapp-unable-to-communicate=Web uygulaması ile iletişim kurulamıyor
luckperms.command.misc.check-console-for-errors=Hatalar için konsolu kontrol ediniz
luckperms.command.misc.file-must-be-in-data={0} dosyası, veri dizininin doğrudan alt öğesi olmalıdır
luckperms.command.misc.wait-to-finish=Lütfen bitmesini bekleyin ve tekrar deneyin
luckperms.command.misc.invalid-priority={0} geçersiz bir öncelik
luckperms.command.misc.expected-number=Bir sayı bekleniyordu
luckperms.command.misc.date-parse-error={0} tarihi ayrıştırılamadı
luckperms.command.misc.date-in-past-error=Geçmişteki bir tarihi ayarlayamazsınız\!
luckperms.command.misc.page=sayfa {0} / {1}
luckperms.command.misc.page-entries={0} girdi
luckperms.command.misc.none=Yok
luckperms.command.misc.loading.error.unexpected=Beklenmedik bir hata oluştu
luckperms.command.misc.loading.error.user=Kullanıcı yüklenmedi
luckperms.command.misc.loading.error.user-specific={0} Adlı oyuncu yüklenemiyor
luckperms.command.misc.loading.error.user-not-found={0} Adında bir oyuncu bulunamadı
luckperms.command.misc.loading.error.user-save-error={0} Adlı oyuncunun verileri kayıt edilirken bir sorun oluştu
luckperms.command.misc.loading.error.user-not-online={0} Adlı oyuncu aktif değil
luckperms.command.misc.loading.error.user-invalid="{0}" geçerli bir kullanıcı adı veya UUID değil.
luckperms.command.misc.loading.error.user-not-uuid={0} uuid''sine sahip bir oyuncu bulunamadı
luckperms.command.misc.loading.error.group=Gruplar yüklenemedi
luckperms.command.misc.loading.error.all-groups=Tüm gruplar yüklenemiyor
luckperms.command.misc.loading.error.group-not-found={0} Adında bir grup bulunamadı
luckperms.command.misc.loading.error.group-save-error={0} Adlı grubun verileri kayıt edilirken bir sorun oluştu
luckperms.command.misc.loading.error.group-invalid={0}, geçerli bir grup adı değil
luckperms.command.misc.loading.error.track=Parça yüklenmedi
luckperms.command.misc.loading.error.all-tracks=Tüm parçalar yüklenemiyor
luckperms.command.misc.loading.error.track-not-found={0}, adlı parça yüklenemedi
luckperms.command.misc.loading.error.track-save-error={0}, parça verileri kaydedilirken bir hata oluştu
luckperms.command.misc.loading.error.track-invalid={0} geçerli bir parça adı değil
luckperms.command.editor.no-match=Düzenleyici açılamıyor, istenen türle eşleşen nesne yok
luckperms.command.editor.start=Yeni bir editör oturumu hazırlanıyor, lütfen bekleyin...
luckperms.command.editor.url=Düzenleyiciyi açmak için aşağıdaki bağlantıya tıklayın
luckperms.command.editor.unable-to-communicate=Editör ile iletişim kurulamıyor
luckperms.command.editor.apply-edits.success=Web düzenleyici verileri {0} {1} için başarıyla uygulandı
luckperms.command.editor.apply-edits.success-summary={0} {1} ve {2} {3}
luckperms.command.editor.apply-edits.success.additions=eklemeler
luckperms.command.editor.apply-edits.success.additions-singular=ekleme
luckperms.command.editor.apply-edits.success.deletions=silme işlemleri
luckperms.command.editor.apply-edits.success.deletions-singular=silme
luckperms.command.editor.apply-edits.no-changes=Web editördeki hiçbir değişiklik uygulanmadı, döndürülen veriler herhangi bir düzenleme içermiyordu
luckperms.command.editor.apply-edits.unknown-type=Belirtilen nesne türüne düzenleme uygulanamıyor
luckperms.command.editor.apply-edits.unable-to-read=Verilen kod kullanılarak veri okunamıyor
luckperms.command.search.searching.permission={0} ile oyuncular ve gruplar aranıyor
luckperms.command.search.searching.inherit={0} ''dan devralan oyuncular ve gruplar aranıyor
luckperms.command.search.result={0} girdisinden, {1} kişi ve {2} grup bulundu.
luckperms.command.search.result.default-notice=Not\: Varsayılan grubun üyelerini ararken, başka izinlere sahip olmayan çevrimdışı oyuncular gösterilmeyecektir\!
luckperms.command.search.showing-users=Kullanıcı girişleri gösteriliyor
luckperms.command.search.showing-groups=Grup girişleri gösteriliyor
luckperms.command.tree.start=Yetki ağacı oluşturuluyor, lütfen bekleyin...
luckperms.command.tree.empty=Ağaç oluşturulamadı, hiçbir sonuç bulunamadı
luckperms.command.tree.url=Yetki Ağacı Bağlantısı
luckperms.command.verbose.invalid-filter={0} geçerli bir ayrıntı filtresi değil
luckperms.command.verbose.enabled={0} ile eşleşen kontroller için ayrıntılı günlük kaydı {1}
luckperms.command.verbose.command-exec={0}''yı {1} komutunu çalıştırmak için zorla ve tüm değişimleri raporlanıyor...
luckperms.command.verbose.off=Ayrıntılı Kayıtlar {0}
luckperms.command.verbose.command-exec-complete=Komut çalıştırma tamamlandı
luckperms.command.verbose.command.no-checks=Komutun çalıştırılması tamamlandı ama hiçbir yetki denetimi gerçekleştirilmedi
luckperms.command.verbose.command.possibly-async=Bunun nedeni, eklentinin komutları arkaplanda (async) çalıştırması olabilir
luckperms.command.verbose.command.try-again-manually=Bu şekilde yapılan kontrolleri algılamak için yine de verbose kullanabilirsiniz
luckperms.command.verbose.enabled-recording={0} ile eşleşen kontroller için ayrıntılı günlük kaydı {1}
luckperms.command.verbose.uploading={0} için ayrıntılı kayıt, sonuçlar yükleniyor...
luckperms.command.verbose.url=Ayrıntılı Kayıt Sonuç Bağlantısı
luckperms.command.verbose.enabled-term=etkin
luckperms.command.verbose.disabled-term=devre dışı
luckperms.command.verbose.query-any=HERHANGİ
luckperms.command.info.running-plugin=Çalışıyor
luckperms.command.info.platform-key=Platform
luckperms.command.info.server-brand-key=Sunucu Yazılımı
luckperms.command.info.server-version-key=Sunucu Sürümü
luckperms.command.info.storage-key=Depolama
luckperms.command.info.storage-type-key=Tür
luckperms.command.info.storage.meta.split-types-key=Türler
luckperms.command.info.storage.meta.ping-key=Gecikme
luckperms.command.info.storage.meta.connected-key=Bağlandı
luckperms.command.info.storage.meta.file-size-key=Dosya Boyutu
luckperms.command.info.extensions-key=Uzantılar
luckperms.command.info.messaging-key=Mesajlaşma
luckperms.command.info.instance-key=Durumlar
luckperms.command.info.static-contexts-key=Statik Şartlar
luckperms.command.info.online-players-key=Çevrimiçi oyuncular
luckperms.command.info.online-players-unique={0} eşsiz
luckperms.command.info.uptime-key=Çalışma Süresi
luckperms.command.info.local-data-key=Yerel Veri
luckperms.command.info.local-data={0} kullanıcı, {1} grup, {2} aşama
luckperms.command.generic.create.success={0} başarıyla oluşturuldu
luckperms.command.generic.create.error={0} oluşturulurken bir hata oluştu
luckperms.command.generic.create.error-already-exists={0} zaten var\!
luckperms.command.generic.delete.success={0} başarıyla silindi
luckperms.command.generic.delete.error={0} silinirken bir hata oluştu
luckperms.command.generic.delete.error-doesnt-exist={0} mevcut değil\!
luckperms.command.generic.rename.success={0}''in ismi {1} olarak değiştirildi
luckperms.command.generic.clone.success={0}, {1} olarak klonlandı
luckperms.command.generic.info.parent.title=Ana Gruplar
luckperms.command.generic.info.parent.temporary-title=Geçici Ana Gruplar
luckperms.command.generic.info.expires-in=bitiş süresi
luckperms.command.generic.info.inherited-from=Buradan devralındı
luckperms.command.generic.info.inherited-from-self=kendisi
luckperms.command.generic.show-tracks.title={0}''ın aşamaları
luckperms.command.generic.show-tracks.empty={0} hiç bir aşamaya sahip değil
luckperms.command.generic.clear.node-removed={0} nod silindi
luckperms.command.generic.clear.node-removed-singular={0} düğüm kaldırıldı
luckperms.command.generic.clear={0} yetkileri {1} de temizlendi
luckperms.command.generic.permission.info.title={0}''ın Yetkileri
luckperms.command.generic.permission.info.empty={0} herhangi bir izni yok
luckperms.command.generic.permission.info.click-to-remove=Nodu {0}''dan silmek için tıklayın
luckperms.command.generic.permission.check.info.title={0} için yetki bilgileri
luckperms.command.generic.permission.check.info.directly={0} isimli grup ya da kullanıcı {1} yetkisine sahip ve {3} bölgesinde {2} olarak ayarlandı
luckperms.command.generic.permission.check.info.inherited={0} isimli grup ya da kullanıcı {1} yetkisini {3} isimli grup ya da kullanıcıdan {4} bölgesinde {2} durumunda aldı
luckperms.command.generic.permission.check.info.not-directly={0} isimli grup ya da kullanıcı için {1} yetkisi ayarlanmamıştır
luckperms.command.generic.permission.check.info.not-inherited={0} isimli grup ya da kullanıcı {1}''den yetki almadı
luckperms.command.generic.permission.check.result.title={0} için yetki kontrolü
luckperms.command.generic.permission.check.result.result-key=Sonuç
luckperms.command.generic.permission.check.result.processor-key=İşlemci
luckperms.command.generic.permission.check.result.cause-key=Çünkü
luckperms.command.generic.permission.check.result.context-key=Bağlam
luckperms.command.generic.permission.set={2} isimli kisi icin {0} yetkisi {3} bolgesinde {1} olarak ayarlandi
luckperms.command.generic.permission.already-has={0} isimli kisi {2} bolgesinde zaten {1} yetkisine sahip
luckperms.command.generic.permission.set-temp={0} yetkisi {2} icin {3} sureligine {4} bolgesinde {1} olarak ayarlandi
luckperms.command.generic.permission.already-has-temp={0} isimli kiside {1} yetkisi zaten var ve sureli olarak {2} bolgesinde ayarlanmis
luckperms.command.generic.permission.unset={1} isimli kisi icin {0} yetkisinin ayari {2} bolgesinde kaldirildi
luckperms.command.generic.permission.doesnt-have={0} isimli kisi {2} bolgesinde ayarlanmis bir {1} yetkisine sahip degil
luckperms.command.generic.permission.unset-temp={1} icin gecici olan {0} yetkisinin ayari {2} bolgesinde kaldirildi
luckperms.command.generic.permission.subtract={0} yetkisi {2} icin {3} sureligine {4} bolgesinde {1} olarak ayarlandi. {5} oncekinden daha az
luckperms.command.generic.permission.doesnt-have-temp={0} isimli kisi {2} bolgesinde sureli olarak ayarlanmis bir {1} yetkisine sahip degil
luckperms.command.generic.permission.clear={0}''in yetkileri {1} bolgesinde temizlendi
luckperms.command.generic.parent.info.title={0} isimli kisinin rolleri
luckperms.command.generic.parent.info.empty={0} isimli kisinin tanimlanmis herhangi bir rolu yok
luckperms.command.generic.parent.info.click-to-remove={0} isimli kisiden bu rolu silmek icin tiklayin
luckperms.command.generic.parent.add={0} isimli kisi artik {2} bolgesinde {1} rolunun yetkilerini kapsiyor
luckperms.command.generic.parent.add-temp={0} isimli kisi artik {3} bolgesinde {1} rolunun yetkilerini {2} sureligine kapsiyor
luckperms.command.generic.parent.set={0} isimli kisinin mevcut tum rolleri temizlendi ve artik sadece {2} bolgesinde {1} rolunun yetkilerini kapsiyor
luckperms.command.generic.parent.set-track={0} isimli kisinin {1} rol paletinde mevcut olan tum rolleri temizlendi ve artik sadece {3} bolgesinde {2} rolunun yetkilerini kapsiyor
luckperms.command.generic.parent.remove={0} isimli kisi artik {2} bolgesinde {1} rolunun yetkilerini kapsamiyor
luckperms.command.generic.parent.remove-temp={0} isimli kisi artik {2} bolgesinde {1} rolunun yetkilerini gecici olarak kapsamiyor
luckperms.command.generic.parent.subtract={0} isimli kisi {3} bolgesinde {1} rolunun yetkilerini {2} sureligine kapsayacak, {4} öncekinden daha az
luckperms.command.generic.parent.clear={0} isimli kisinin rolleri {1} bolgesinde temizlendi
luckperms.command.generic.parent.clear-track={0} isimli kisinin {1} rol paletindeki rolleri {2} bolgesinde temizlendi
luckperms.command.generic.parent.already-inherits={0} isimli kisi {2} bolgesinde {1} rolunu zaten kapsiyor
luckperms.command.generic.parent.doesnt-inherit={0} isimli kisi {2} bolgesinde {1} rolunu kapsamiyor
luckperms.command.generic.parent.already-temp-inherits={0} isimli kisi {2} bolgesinde {1} rolunu zaten gecici olarak kapsiyor
luckperms.command.generic.parent.doesnt-temp-inherit={0} isimli kisi artik {2} bolgesinde {1} rolunu kapsamiyor
luckperms.command.generic.chat-meta.info.title-prefix={0} isimli kisinin prefixleri
luckperms.command.generic.chat-meta.info.title-suffix={0} isimli kisinin suffixleri
luckperms.command.generic.chat-meta.info.none-prefix={0} isimli kisi hicbir prefixe sahip degil
luckperms.command.generic.chat-meta.info.none-suffix={0} isimli kisi hicbir suffixe sahip degil
luckperms.command.generic.chat-meta.info.click-to-remove={1} isimli kisiden {0} yetkisini silmek icin tikla
luckperms.command.generic.chat-meta.already-has={0} isimli kisi {4} bolgesinde {3} oncelikle ayarli {2} {1} ine zaten sahip
luckperms.command.generic.chat-meta.already-has-temp={0} isimli kisi {4} bolgesinde gecici olarak {3} oncelikle ayarli {2} {1} ine zaten sahip
luckperms.command.generic.chat-meta.doesnt-have={0} isimli kisi {4} bolgesinde {3} oncelikle ayarli {2} {1} ine sahip degil
luckperms.command.generic.chat-meta.doesnt-have-temp={0} isimli kisi {4} bolgesinde, gecici {3} oncelikle ayarli {2} {1} ine sahip degil
luckperms.command.generic.chat-meta.add={0} isimli kisi artik {4} bolgesinde {3} oncelikle ayarli {2} {1} ine sahip
luckperms.command.generic.chat-meta.add-temp={0} isimli kisi artik {5} bolgesinde {4} sureligine {3} oncelikle ayarli {2} {1} ine sahip
luckperms.command.generic.chat-meta.remove={0} isimli kisinin {3} oncelikle ayarli {2} {1} i {4} bolgesinde silindi
luckperms.command.generic.chat-meta.remove-bulk={0} isimli kisinin {2} oncelikle ayarli tum {1} leri {3} bolgesinde silindi
luckperms.command.generic.chat-meta.remove-temp={0} isimli kisinin {3} oncelikle ayarli, gecici {2} {1} i {4} bolgesinde silindi
luckperms.command.generic.chat-meta.remove-temp-bulk={0} isimli kisinin {2} oncelikle ayarli tum gecici {1} leri {3} bolgesinde silindi
luckperms.command.generic.meta.info.title={0} isimli kisinin metasi
luckperms.command.generic.meta.info.none={0} isimli kisi herhangi bir metaya sahip degil
luckperms.command.generic.meta.info.click-to-remove={0}''dan meta''yı silmek için tıklayın
luckperms.command.generic.meta.already-has={0} zaten {3} şartlarında meta olarak {1}, {2}''ye sahip
luckperms.command.generic.meta.already-has-temp={0} zaten {3} şartlarında geçici olarak meta {1}, {2}''ye sahip
luckperms.command.generic.meta.doesnt-have={0} isimli kisi {2} bolgesinde ayarlanmis bir {1} meta anahtarina sahip degil
luckperms.command.generic.meta.doesnt-have-temp={0} isimli kisi {2} bolgesinde sureli olarak ayarlanmis bir {1} meta anahtarina sahip degil
luckperms.command.generic.meta.set={2} isimli kisi icin {0} meta anahtari {3} bolgesinde {1} olarak ayarlandi
luckperms.command.generic.meta.set-temp={0} meta anahtari {2} icin {3} sureligine {4} bolgesinde {1} olarak ayarlandi
luckperms.command.generic.meta.unset={1} isimli kisi icin {0} meta anahtarinin ayari {2} bolgesinde kaldirildi
luckperms.command.generic.meta.unset-temp={1} icin gecici olan {0} meta anahtarinin ayari {2} bolgesinde kaldirildi
luckperms.command.generic.meta.clear={0} isimli kisinin eslesen meta turu {1}, {2} bolgesinde kaldirildi
luckperms.command.generic.contextual-data.title=İçeriksel veri
luckperms.command.generic.contextual-data.mode.key=mod
luckperms.command.generic.contextual-data.mode.server=sunucu
luckperms.command.generic.contextual-data.mode.active-player=aktif oyuncu
luckperms.command.generic.contextual-data.contexts-key=Bolgeler
luckperms.command.generic.contextual-data.prefix-key=Prefix
luckperms.command.generic.contextual-data.suffix-key=Suffix
luckperms.command.generic.contextual-data.primary-group-key=Birincil grup
luckperms.command.generic.contextual-data.meta-key=Meta
luckperms.command.generic.contextual-data.null-result=Yok
luckperms.command.user.info.title=Oyuncu bilgisi
luckperms.command.user.info.uuid-key=UUID
luckperms.command.user.info.uuid-type-key=turu
luckperms.command.user.info.uuid-type.mojang=mojang
luckperms.command.user.info.uuid-type.not-mojang=çevrimdışı
luckperms.command.user.info.status-key=Durum
luckperms.command.user.info.status.online=Aktif
luckperms.command.user.info.status.offline=Cevrimdisi
luckperms.command.user.removegroup.error-primary=Bir kullanıcıyı birincil grubundan çıkaramazsınız
luckperms.command.user.primarygroup.not-member={0} isimli kisi {1} grubunun bir uyesi degil, simdi ekleniyor
luckperms.command.user.primarygroup.already-has={0} isimli kisinin birincil grubu zaten {1} olarak ayarlanmis
luckperms.command.user.primarygroup.warn-option=Uyari\: Bu sunucu tarafindan kullanilan birincil grup hesaplama methodu ({0}) bu degisikligi yansitmayabilir
luckperms.command.user.primarygroup.set={0} isimli kisinin birincil grubu {1} olarak ayarlandi
luckperms.command.user.track.error-not-contain-group={0} isimli kisi {1} rol paletindeki bir grupta degil
luckperms.command.user.track.unsure-which-track=Hangi rol paletinin kullanilacagindan emin degilim, lutfen bir arguman ile belirt
luckperms.command.user.track.missing-group-advice=Ya grup olustur ya da rol paletinden kaldirip tekrar dene
luckperms.command.user.promote.added-to-first={0} isimli kisi {1} rol paletindeki hicbir grupta degil. Dolayisiyla {3} bolgesinde ilk grup olan {2} grubuna eklendi
luckperms.command.user.promote.not-on-track={0} isimli kisi {1} rol paletindeki hicbir grupta degil. Dolayisiyla terfi edilmedi
luckperms.command.user.promote.success={0} isimli kisi {1} rol paleti boyunca {2} grubundan {3} grubuna {4} bolgesinde terfi ediliyor
luckperms.command.user.promote.end-of-track={0} rol paletinin sonuna gelindi, {1} isimli kisi artik terfi edilemiyor
luckperms.command.user.promote.next-group-deleted={0} rol paletinin sonraki grubu mevcut degil
luckperms.command.user.promote.unable-to-promote=Kullanıcı terfi edilemiyor
luckperms.command.user.demote.success={0} isimli kisi {1} rol paleti boyunca {2} grubundan {3} grubuna {4} bolgesinde tenzil ediliyor
luckperms.command.user.demote.end-of-track={0} rol paletinin sonuna gelindi ve {1} isimli kisiden {2} grubu silindi
luckperms.command.user.demote.end-of-track-not-removed={0} rol paletinin sonuna gelindi ama {1} isimli kisi rol paletinin ilk grubundan silinmedi
luckperms.command.user.demote.previous-group-deleted={0} rol paletinin onceki grubu mevcut degil
luckperms.command.user.demote.unable-to-demote=Kullanıcı tenzil edilemiyor
luckperms.command.group.list.title=Gruplar
luckperms.command.group.delete.not-default=Varsayilan grubu silemezsin
luckperms.command.group.info.title=Grup Bilgisi
luckperms.command.group.info.display-name-key=Gorunen Isim
luckperms.command.group.info.weight-key=Ağırlık
luckperms.command.group.setweight.set={1} grubu icin onceligi {0} yap
luckperms.command.group.setdisplayname.doesnt-have={0} grubuna henuz gorunen bir isim ayarlanmamis
luckperms.command.group.setdisplayname.already-has={0} isimli grup zaten {1} gorunen ismine sahip
luckperms.command.group.setdisplayname.already-in-use={0} gorunen ismi halihazirda {1} tarafindan kullaniliyor
luckperms.command.group.setdisplayname.set={1} grubunun gorunen ismi {2} bolgesinde {0} olarak ayarlandi
luckperms.command.group.setdisplayname.removed={0} grubunun gorunen ismi {1} bolgesinde kaldirildi
luckperms.command.track.list.title=Rol paletleri
luckperms.command.track.path.empty=Yok
luckperms.command.track.info.showing-track=Rol paleti gosteriliyor
luckperms.command.track.info.path-property=Hedef yol
luckperms.command.track.clear={0} isimli rol paletinin gruplari temizlendi
luckperms.command.track.append.success={0} isimli grup {1} rol gruplarına eklendi
luckperms.command.track.insert.success={0} grubu {1} rol paletine ve {2} pozisyonuna yerlestirildi
luckperms.command.track.insert.error-number=Sayi bekleniyordu ama yerine gelen {0}
luckperms.command.track.insert.error-invalid-pos={0} pozisyonuna yerlestirilemedi
luckperms.command.track.insert.error-invalid-pos-reason=gecersiz pozisyon
luckperms.command.track.remove.success={0} grubu {1} rol paletinden kaldirildi
luckperms.command.track.error-empty={0} bos oldugu icin kullanilamaz, en az bir group icermeli
luckperms.command.track.error-multiple-groups={0} isimli kisi bu rol paletinde birden fazla grubun uyesi
luckperms.command.track.error-ambiguous=Konumu belirlenemedi
luckperms.command.track.already-contains={0} zaten {1} degerini kapsiyor
luckperms.command.track.doesnt-contain={0}, {1} degerini kapsamiyor
luckperms.command.log.load-error=Log yuklenemedi
luckperms.command.log.invalid-page=Gecersiz sayfa numarasi
luckperms.command.log.invalid-page-range=Lutfen {0} ile {1} arasinda bir deger girin
luckperms.command.log.empty=Gosterilecek hicbir log girdisi yok
luckperms.command.log.notify.error-console=Konsol icin bildirimler acip kapanamaz
luckperms.command.log.notify.enabled-term=Etkinleşti
luckperms.command.log.notify.disabled-term=Devre dışı edildi
luckperms.command.log.notify.changed-state={0} log ciktilari
luckperms.command.log.notify.already-on=Zaten bildirim aliyorsun
luckperms.command.log.notify.already-off=Su anda bildirim almiyorsun
luckperms.command.log.notify.invalid-state=Bilinmeyen sart. Beklenen {0} ya da {1}
luckperms.command.log.show.search={0} Sorgusu icin son eylemler gosteriliyor
luckperms.command.log.show.recent=Son eylemler gosteriliyor
luckperms.command.log.show.by={0} Tarafindan son eylemler gosteriliyor
luckperms.command.log.show.history={0} {1} icin gecmis gosteriliyor
luckperms.command.export.error-term=Hata
luckperms.command.export.already-running=Baska bir disa aktarma islemi zaten calisiyor
luckperms.command.export.file.already-exists={0} Dosyasi zaten mevcut
luckperms.command.export.file.not-writable={0} Dosyasi yazilabilir degil
luckperms.command.export.file.success={0} Basarili bir sekilde disari aktarildi
luckperms.command.export.file-unexpected-error-writing=Dosya yazilirken beklenmeyen bir hata oluştu
luckperms.command.export.web.export-code=Disa aktarma kodu
luckperms.command.export.web.import-command-description=Ice aktarim icin istenen komudu kullanin
luckperms.command.import.term=Ice aktar
luckperms.command.import.error-term=Hata
luckperms.command.import.already-running=Baska bir ice aktarma islemi zaten calisiyor
luckperms.command.import.file.doesnt-exist={0} dosyasi mevcut degil
luckperms.command.import.file.not-readable={0} Dosyasi okunabilir degil
luckperms.command.import.file.unexpected-error-reading=Ice aktarma dosyasi okunurken beklenmeyen bir hata olustu
luckperms.command.import.file.correct-format=bu dogru bir format mi?
luckperms.command.import.web.unable-to-read=Verilen kod kullanilarak veri okunamiyor
luckperms.command.import.progress.percent={0}% tamamlandi
luckperms.command.import.progress.operations={0}/{1} operasyonlar tamamlandi
luckperms.command.import.starting=Ice aktarma islemi baslatiliyor
luckperms.command.import.completed=TAMAMLANDI
luckperms.command.import.duration={0} saniye surdu
luckperms.command.bulkupdate.must-use-console=Bulk Update komudu sadece konsol tarafindan kullanilabilir
luckperms.command.bulkupdate.invalid-data-type=Gecersiz tur, beklenen {0}
luckperms.command.bulkupdate.invalid-constraint=Gecersiz kisitlama {0}
luckperms.command.bulkupdate.invalid-constraint-format=Kisitlamalar {0} formatinin icinde olmali
luckperms.command.bulkupdate.invalid-comparison=Gecersiz karsilastirma operatoru {0}
luckperms.command.bulkupdate.invalid-comparison-format=Bunlardan biriyle tamamlayin\: {0}
luckperms.command.bulkupdate.queued=Bulk update operasyonu siraya alindi
luckperms.command.bulkupdate.confirm=Guncellemeyi yapmak icin {0} komudunu calistir
luckperms.command.bulkupdate.unknown-id={0} kod numarali operasyon mevcut degil ya da suresi gecmis
luckperms.command.bulkupdate.starting=Bulk update calisiyor
luckperms.command.bulkupdate.success=Bulk update basariyla tamamlandi
luckperms.command.bulkupdate.success.statistics.nodes=Etkilenen toplam yetki
luckperms.command.bulkupdate.success.statistics.users=Etkilenen toplam kullanıcı
luckperms.command.bulkupdate.success.statistics.groups=Etkilenen toplam grup
luckperms.command.bulkupdate.failure=Bulk update basarisiz oldu, hatalar icin konsolu kontrol edin
luckperms.command.update-task.request=Guncelleme istegi alindi, lutfen bekleyin
luckperms.command.update-task.complete=Guncelleme basarili
luckperms.command.update-task.push.attempting=Su anda diger sunuculara aktarma deneniyor
luckperms.command.update-task.push.complete=Diger sunucular basariyle {0} ile bilgilendirildi
luckperms.command.update-task.push.error=Degisiklikler diger sunuculara aktarilirken bir hata olustu
luckperms.command.update-task.push.error-not-setup=Mesaj servisi ayarlanmamis sunuculara degisiklikler aktarilamaz
luckperms.command.reload-config.success=Ayar dosyasi basariyla yeniden yuklendi
luckperms.command.reload-config.restart-note=bazi ayarlar sadece sunucu tekrar baslatildiginda uygulanir
luckperms.command.translations.searching=Mevcut ceviriler araniyor, lutfen bekleyin...
luckperms.command.translations.searching-error=Mevcut cevirilerin listesi alinamadi
luckperms.command.translations.installed-translations=Indirilen Ceviriler
luckperms.command.translations.available-translations=Mevcut Ceviriler
luckperms.command.translations.percent-translated={0}% cevrildi
luckperms.command.translations.translations-by=tarafindan
luckperms.command.translations.installing=Ceviriler indiriliyor, lutfen bekleyin...
luckperms.command.translations.download-error={0} cevirisi indirilemiyor
luckperms.command.translations.installing-specific={0} Dili Indiriliyor...
luckperms.command.translations.install-complete=Indirme Tamamlandi
luckperms.command.translations.download-prompt=Topluluk tarafindan saglanan bu cevirilerin guncel surumlerini indirmek icin {0} komudunu kullan
luckperms.command.translations.download-override-warning=Bu diller icin yaptigin herhangi bir degisiklik ustune yazilacak. Lutfen bunu not et
luckperms.usage.user.description=Kullanicilari yonetebilecegin LuckPerms komutlari (LuckPerms''deki bir ''kullanici'' sadece bir oyuncudur, UUID ya da kullanici adiyla ifade edilir)
luckperms.usage.group.description=Gruplari yonetebilecegin LuckPerms komutlari. Gruplar sadece kullanicilara verilen yetki atama koleksiyonlaridir. Yeni gruplar ''creategroup'' komudu kullanilarak olusturulabilir.
luckperms.usage.track.description=Rol paletlerini yonetebilecegin LuckPerms komutlari. Rol paletleri kullanicilari terfi veya tenzil etmek icin kullanilan siralanmis grup koleksiyonlaridir.
luckperms.usage.log.description=Log fonksiyonunu yonetebilecegin LuckPerms komutlari.
luckperms.usage.sync.description=Eklentinin hafizasinda saklanan tum veriyi yeniden yukler ve tespit edilen herhangi bir degisikligi uygular.
luckperms.usage.info.description=Eklentinin aktif durumu hakkinda genel bilgiyi gosterir.
luckperms.usage.editor.description=Yeni bir web editor oturumu olusturur
luckperms.usage.editor.argument.type=editore yuklenen turler. (''all'', ''users'' ya da ''groups'')
luckperms.usage.editor.argument.filter=kullanici girdilerini filtreleme izni
luckperms.usage.verbose.description=Eklentinin gereksiz ayrintili yetkileri bulmaya calistigi izleme sistemini kontrol eder.
luckperms.usage.verbose.argument.action=loga kaydetmeyi etkinlestirme/devre disi birakma veya loga kaydedilen ciktiyi yukleme
luckperms.usage.verbose.argument.filter=eslesen girdilere karsi filtre
luckperms.usage.verbose.argument.commandas=calistirilacak oyuncu/komut
luckperms.usage.tree.description=LuckPerms tarafindan bilinen tum yetkilerin agac gorunumunu olusturur (siralanmis liste hiyerarsisi).
luckperms.usage.tree.argument.scope=agacin koku. Tum yetkileri belirtmek icin "." kullanin
luckperms.usage.tree.argument.player=kontrol edilecek cevrimici oyuncunun ismi
luckperms.usage.search.description=Spesifik bir yetki ile tüm kullanıcıları ya da grupları arar
luckperms.usage.search.argument.permission=aranan yetki
luckperms.usage.search.argument.page=goruntulenecek sayfa
luckperms.usage.network-sync.description=Senkronizasyon saklama ile degisir ve ag uzerindeki diger tum sunuculara aynisini yapar
luckperms.usage.import.description=(Onceden olusturulmus) bir disa aktarim dosyasindan veriyi ice aktarir
luckperms.usage.import.argument.file=ice aktarilacak dosya
luckperms.usage.import.argument.replace=var olan veriyi birlestirmek yerine tekrar koyun
luckperms.usage.import.argument.upload=onceki disa aktarmadaki tum veriyi yukle
luckperms.usage.export.description=Tum yetki verisini ''disa aktarim'' dosyasi olarak disa aktar. Daha sonra tekrar ice aktarilabilir.
luckperms.usage.export.argument.file=disa aktarilacak dosya
luckperms.usage.export.argument.without-users=disa aktarimdan kullanicilari haric tut
luckperms.usage.export.argument.without-groups=disa aktarimdan gruplari haric tut
luckperms.usage.export.argument.upload=Tum yetki verisini webeditore yukle. Daha sonra tekrar ice aktarilabilir.
luckperms.usage.reload-config.description=Bazi yapilandirma ayarlarini tekrar yukle
luckperms.usage.bulk-update.description=Tum verideki bulk change sorgularini calistirir
luckperms.usage.bulk-update.argument.data-type=degisen verinin turu. (''all'', ''users'' ya da ''groups'')
luckperms.usage.bulk-update.argument.action=veride uygulanan eylem. (''update'' ya da ''delete'')
luckperms.usage.bulk-update.argument.action-field=uzerinde eylem yapilacak alan. Sadece ''guncelleme'' icin gerekli. (''permission'', ''server'' ya da ''world'')
luckperms.usage.bulk-update.argument.action-value=yerine gelecek deger. Sadece ''guncelleme'' icin gerekli.
luckperms.usage.bulk-update.argument.constraint=guncelleme icin kisitlamalar gereklidir
luckperms.usage.translations.description=Cevirileri yonet
luckperms.usage.translations.argument.install=cevirileri indirmek icin alt komut
luckperms.usage.apply-edits.description=Web editor tarafindan yapilan yetki degisikliklerini uygular
luckperms.usage.apply-edits.argument.code=veri icin ozgun kod
luckperms.usage.apply-edits.argument.target=veriyi kim uygulayacak
luckperms.usage.create-group.description=Yeni bir grup olustur
luckperms.usage.create-group.argument.name=grubun ismi
luckperms.usage.create-group.argument.weight=grubun onceligi
luckperms.usage.create-group.argument.display-name=grubun gorunen ismi
luckperms.usage.delete-group.description=Bir grubu sil
luckperms.usage.delete-group.argument.name=grubun ismi
luckperms.usage.list-groups.description=Platformdaki tum gruplari listele
luckperms.usage.create-track.description=Yeni bir rol paleti olustur
luckperms.usage.create-track.argument.name=rol paletinin ismi
luckperms.usage.delete-track.description=Bir rol paleti sil
luckperms.usage.delete-track.argument.name=rol paletinin ismi
luckperms.usage.list-tracks.description=Platformdaki tum rol paletlerini listele
luckperms.usage.user-info.description=Kullanici hakkinda bilgi verir
luckperms.usage.user-switchprimarygroup.description=Kullanicinin birincil grubunu degistirir
luckperms.usage.user-switchprimarygroup.argument.group=degistirilecek grup
luckperms.usage.user-promote.description=Rol paleti ustunden kullaniciyi terfi eder
luckperms.usage.user-promote.argument.track=kullanicinin terfi edilecegi rol paleti
luckperms.usage.user-promote.argument.context=kullanicinin terfi edilecegi bolgeler
luckperms.usage.user-promote.argument.dont-add-to-first=sadece rol paletinde olan bir kullaniciyi terfi edebilirsin
luckperms.usage.user-demote.description=Rol paleti ustunden kullaniciyi tenzil eder
luckperms.usage.user-demote.argument.track=kullanicinin tenzil edilecegi rol paleti
luckperms.usage.user-demote.argument.context=kullanicinin tenzil edilecegi bolgeler
luckperms.usage.user-demote.argument.dont-remove-from-first=kullanicinin ilk gruptan silinmesini onler
luckperms.usage.user-clone.description=Kullaniciyi klonla
luckperms.usage.user-clone.argument.user=klonlanacak kullanicinin ismi/uuid si
luckperms.usage.group-info.description=Grup hakkinda bilgi verir
luckperms.usage.group-listmembers.description=Bu grubu kapsayan kullanicilari/gruplari gosterir
luckperms.usage.group-listmembers.argument.page=goruntulenecek sayfa
luckperms.usage.group-setweight.description=Gruplarin onceligini ayarla
luckperms.usage.group-setweight.argument.weight=ayarlanacak oncelik
luckperms.usage.group-set-display-name.description=Grubun gorunen ismini ayarla
luckperms.usage.group-set-display-name.argument.name=ayarlanacak isim
luckperms.usage.group-set-display-name.argument.context=ismin ayarlanacagi bolgeler
luckperms.usage.group-rename.description=Gruba yeniden isim ver
luckperms.usage.group-rename.argument.name=yeni isim
luckperms.usage.group-clone.description=Grup klonla
luckperms.usage.group-clone.argument.name=klonlanacak grubun ismi
luckperms.usage.holder-editor.description=Web yetki editorunu acar
luckperms.usage.holder-showtracks.description=Bu rol paletinde olan objeleri listeler
luckperms.usage.holder-clear.description=Tum yetkileri, rolleri ve metayi siler
luckperms.usage.holder-clear.argument.context=filtrelenecek bolgeler
luckperms.usage.permission.description=Yetkileri duzenle
luckperms.usage.parent.description=Kapsayicilari duzenle
luckperms.usage.meta.description=Metadata degerlerini duzenle
luckperms.usage.permission-info.description=Bir objenin sahip oldugu yetkileri listeler
luckperms.usage.permission-info.argument.page=goruntulenecek sayfa
luckperms.usage.permission-info.argument.sort-mode=girdiler nasıl siralanir
luckperms.usage.permission-set.description=Obje icin bir yetki ayarlar
luckperms.usage.permission-set.argument.node=ayarlanacak yetki
luckperms.usage.permission-set.argument.value=yetkinin degeri
luckperms.usage.permission-set.argument.context=yetkinin eklenecegi bolgeler
luckperms.usage.permission-unset.description=Obje icin bir yetki ayarlamasini kaldirir
luckperms.usage.permission-unset.argument.node=ayari kaldirilacak yetki
luckperms.usage.permission-unset.argument.context=yetkinin silinecegi bolgeler
luckperms.usage.permission-settemp.description=Obje icin gecici bir yetki ayarlar
luckperms.usage.permission-settemp.argument.node=ayarlanacak yetki
luckperms.usage.permission-settemp.argument.value=yetkinin degeri
luckperms.usage.permission-settemp.argument.duration=yetkinin suresi dolana kadar gececek sure
luckperms.usage.permission-settemp.argument.temporary-modifier=gecici yetki nasil uygulanmali
luckperms.usage.permission-settemp.argument.context=yetkinin eklenecegi bolgeler
luckperms.usage.permission-unsettemp.description=Obje icin gecici bir yetki ayarlamasini kaldirir
luckperms.usage.permission-unsettemp.argument.node=ayari kaldirilacak yetki
luckperms.usage.permission-unsettemp.argument.duration=cikma suresi
luckperms.usage.permission-unsettemp.argument.context=yetkinin silinecegi bolgeler
luckperms.usage.permission-check.description=Bir objenin belli bir yetkiye sahip olup olmadigini kontrol eder
luckperms.usage.permission-check.argument.node=kontrol edilecek yetki
luckperms.usage.permission-clear.des