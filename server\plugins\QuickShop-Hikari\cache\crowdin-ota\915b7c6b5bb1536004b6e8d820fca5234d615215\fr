break-shop-use-supertool: <yellow>Tu as cassé la boutique en utilisant le super outil.
fee-charged-for-price-change: <green>Vous payez <red>{0}<green> pour changer le prix.
not-allowed-to-create: <red>Vous ne pouvez pas créer un shop ici.
disabled-in-this-world: <red>QuickShop est désactivé dans ce monde
how-much-to-trade-for: <green>Entrez dans le chat, combien vous souhaitez échanger <yellow>{1}x {0}<green>.
client-language-changed: <green>QuickShop a détecté que votre paramètre de langue client a été modifié, nous utilisons maintenant la locale {0} pour vous.
shops-backingup: Création d'une sauvegarde de la boutique à partir de la base de données...
_comment: Bonjour, traducteur ! Si vous éditez ceci sur Github ou depuis le code source, rendez-vous plutôt sur https://crowdin.com/project/quickshop-hikari.
unlimited-shop-owner-changed: <yellow>Ce propriétaire de magasin illimité a été remplacé par {0}.
bad-command-usage-detailed: '<red>Arguments de commande incorrects ! Paramètres acceptés : <gray>{0}'
thats-not-a-number: <red>Nombre invalide.
shop-name-disallowed: <red>The shop name <yellow>{0} <red>is disallowed. Pick another one!
console-only-danger: <red>Cette commande est dangereuse pour que seule la console puisse l'exécuter.
not-a-number: <red>Vous ne pouvez saisir qu'un nombre, votre entrée était {0}.
not-looking-at-valid-shop-block: <red>Could not find a block for creating shop. You need to look at one.
shop-removed-cause-ongoing-fee: <red>Ta boutique aux coordonnées {0} a été supprimé car tu n'avais plus assez d'argent pour la garder!
tabcomplete:
  amount: '[quantité]'
  item: '[item]'
  price: '[prix]'
  name: '[name]'
  range: '[distance]'
  currency: '[Type de monnaie]'
  percentage: '[percentage%]'
taxaccount-unset: <green>Le compte de taxe de cette boutique suit maintenant le paramètre global du serveur.
blacklisted-item: <red>Vous ne pouvez pas vendre cet objet car il se trouve sur la liste noire
command-type-mismatch: <red>Cette commande ne peut être exécutée que par <aqua>{0}.
server-crash-warning: '<red>Le serveur peut planter après l’éxécution de la commande /qs reload si vous remplacez ou supprimez le fichier Jar du plugin QuickShop pendant l’exécution du serveur.'
you-cant-afford-to-change-price: <red>Cela coûte {0} de changer le prix de ta boutique.
safe-mode: <red>QuickShop est à présent en mode sans échec, vous ne pouvez pas ouvrir ce conteneur, veuillez contacter l'administrateur du serveur pour corriger les erreurs.
forbidden-vanilla-behavior: <red>L'opération est indisponible car elle n'est pas compatible avec la version vanilla
shop-out-of-space-name: <dark_purple>Votre boutique {0} est pleine !
paste-disabled: |-
  <red>Paste functionality has been disabled! You cannot request technical support. <newline>Reason: {0}
quick-fill:
  entry-own: ' <yellow>- <red>[own] <aqua>{0} <light_purple>{1}'
  hover:
    - '<yellow>Nom : <aqua>{0}'
    - '<yellow>Propriétaire : <aqua>{0}'
    - '<yellow>Type : <aqua>{0}'
    - '<yellow>Prix : <aqua>{0}'
    - '<yellow>Item : <aqua>{0}'
    - '<yellow>Emplacement : <aqua>{0}'
  hover-arg-filled:
    - '<yellow>Nom : <aqua>{name}'
    - '<yellow>Propriétaire : <aqua>{owner}'
    - '<yellow>Type : <aqua>{type}'
    - '<yellow>Prix : <aqua>{price}'
    - '<yellow>Item : <aqua>{item}'
    - '<yellow>Emplacement : <aqua>{location}'
  header: '<yellow>You have multiple shops with name "<green>{0}<yellow>", choose one to continue:'
  entry-normal: ' <yellow>- <aqua>{0} <light_purple>{1}'
  entry-cooperation: ' <yellow> <red> [co] <aqua> {0} <light_purple> {1}'
3rd-plugin-build-check-failed-admin: '<gray>(Administrateur) <light_purple>{0}<dark_gray> a refusé les vérifications de permissions. Si ce n’est pas prévu, essayez d’ajouter <light_purple>{1} <gray>à la liste noire de l’auditeur. Guide de configuration : https ://ghost-chu.github.io/QuickShop-Hikari-Documents/docs/modules/shops/protection-checker'
average-price-nearby: '<green>Prix moyen à proximité : <yellow>{0}'
inventory-check-global-alert: "<red>[Vérification de l'inventaire] <gray>Attention ! Trouvé un élément d'affichage QuickShop <gold>{2}</gold> dans l'inventaire à <aqua>{0}</aqua> <dark_gray>[{1}]</dark_gray>, ce qui ne devrait pas se produire, cela signifie généralement que quelqu'un exploite de manière malveillante l'exploit pour dupliquer l'élément d'affichage."
digits-reach-the-limit: <red>Vous avez atteint les limites de décimales dans le prix.
currency-unset: <green>La boutique a été supprimée avec succès. Utiliser les paramètres par défaut maintenant.
you-cant-create-shop-in-there: <red>Tu n'as pas la permission de créer une boutique à cet emplacement!
no-pending-action: <red>Tu n'as aucune action en attente
refill-success: <green>Remplissage de la boutique effectué avec succès
failed-to-paste: <red>Erreur lors de l'upload des données vers PasteBin, verifier la connection internet et rééssayez. (Voir la console)
shop-out-of-stock-name: <dark_purple>Votre boutique {0} est à court de {1} !
shop-name-invalid: <red>The shop name <yellow>{0} <red>is invalid. Pick another one!
how-many-buy-stack: <yellow>Enter in chat, how many bulks you wish to <aqua>BUY<green>. There are <yellow>{0}<green> items in each bulk and you can buy <yellow>{1}<green> bulks. Enter <aqua>{2}<green> in chat to buy all.
exceeded-maximum: <red>La valeur maximal à ne pas dépasser en Java.
unlimited-shop-owner-keeped: '<yellow>Attention : Le propriétaire de la boutique est toujours propriétaire de la boutique illimitée, vous devez re-définir nouveau propriétaire de la boutique par vous-même.'
no-enough-money-to-keep-shops: <red>Tu n'as plus assez d'argent pour garder tes boutiques! Tous tes coffres ont été supprimés...
3rd-plugin-build-check-failed: Un plugin tiers <bold>{0}<reset><red> a refusé les vérifications de permission, avez-vous les droits pour paramétrer ici ?
not-a-integer: <red>Vous devez entrer un nombre entier, votre entrée était {0}.
translation-country: 'Langue: Français (fr_FR)'
buying-more-than-selling: '<red>ATTENTION: Vous êtes entrain d''acheter des objets plus cher que vous ne les vendez!'
purchase-failed: '<red>Echec de l''achat : Erreur interne, veuillez contacter un administrateur du serveur.'
denied-put-in-item: <red>Vous ne pouvez pas mettre cet objet dans votre boutique!
shop-has-changed: <red>La boutique que vous essayez d'utiliser à changée depuis que vous avez cliqué dessus!
flush-finished: <green>Messages communiqués avec succès
no-price-given: <red>Merci de donner un nombre/chiffre valide.
shop-already-owned: <red>Ceci est déjà une boutique.
backup-success: <green>Sauvegarde réussie.
not-looking-at-shop: <red>Impossible de trouver un QuickShop. Vous devez en regarder un.
you-cant-afford-a-new-shop: <red>La création d'une boutique coûte {0}.
success-created-shop: <red>Boutique crée avec succès.
shop-creation-cancelled: <red>Création de la boutique annulée.
shop-owner-self-trade: <yellow>Vous négociez avec vous en magasin, vous ne pourrez donc pas gagner de solde.
purchase-out-of-space: <red>Cette boutique est à court d'espace, contactez le propriétaire ou le personnel de la boutique pour vider la boutique.
reloading-status:
  success: <green>Rechargement effectué sans erreurs.
  scheduled: <green>Rechargement terminé. <gray>(Certaines modifications nécessitent un certain temps)
  require-restart: <green>Rechargement terminé. <yellow>(Certaines modifications nécessitent un redémarrage du serveur pour prendre effet)
  failed: <red>Rechargement échoué, vérifiez la console du serveur
player-bought-from-your-store-tax: <green>{0} a acheté {1} {2} dans ta boutique, et tu as reçu {3} ({4} de taxes).
not-enough-space: <red>Il ne vous reste que {0} place(s) restante(s) pour ceci!
shop-name-success: <green>Le nom de votre boutique a bien été défini à <yellow>{0}<green>.
shop-staff-added: <green> {0} a été ajouté avec succès comme membre du personnel pour votre boutique.
shop-staff-empty: <yellow>Ce magasin n’a pas de personnel.
shops-recovering: Récupération des boutiques à partir de la sauvegarde...
virtual-player-component-hover: "<gray>Ceci est un joueur virtuel.\n<gray>Se réfère à un compte système de ce nom avec le même nom.</gray>\n<green>UUUID : <yellow>{0}</yellow></green>\n<green>Identifiant : <yellow>{1}</yellow></green>\n<green>Afficher en tant que : <yellow>{2}</yellow></green>"
real-player-component-hover: "<gray>Il s’agit d’un vrai joueur.\n<green>UUID : <yellow>{0}</yellow></green>\n<green>Nom d’utilisateur : <yellow>{1}</yellow></green>\n<green>Afficher en tant que : <yellow>{2}</yellow></green>\n<gray>Si vous souhaitez utiliser un compte de système virtuel du même nom, ajoutez <dark_gray>\"[]\"</dark_gray> des deux côtés du nom d’utilisateur : <dark_gray>[{1}]</dark_gray>."
menu:
  sell-tax: <green>Tu as payé<yellow>{0} <green>en taxes.
  owner: '<green>Propriétaire: {0}'
  preview: <aqua>[Aperçu de l'article]
  enchants: <dark_purple>Enchantements
  sell-tax-self: <green>Vous n'avez pas payé de taxes parce que vous possédez cette boutique.
  shop-information: '<green>Informations sur la boutique :'
  item: '<green>Objet: <yellow>{0}'
  price-per: <green>Prix unitaire de <yellow>{0} <green>- <yellow>{1}
  item-name-and-price: <yellow>{0} {1} <green>pour <yellow>{2}
  item-name-and-price-tax: <yellow>{0} {1} <green>for</green> {2} <gray>(<green>{3}</green> in taxes)</gray>
  successful-purchase: '<green>Achat réussi :'
  price-per-stack: <green>Prix unitaire de <yellow>{2}x {0} - {1}
  stored-enchants: <dark_purple>Enchantements stockés
  item-holochat-error: <red>[Erreur]
  this-shop-is-selling: <green>Cette boutique <light_purple>VEND<green> des objets.
  shop-stack: '<green>Quantité par lot : <yellow>{0}'
  space: '<green>Espace restant: <yellow>{0}'
  effects: <green>Effets
  damage-percent-remaining: <yellow>{0}% <green>restant.
  item-holochat-data-too-large: <red>[Error] Item NBT is too large for showing
  stock: '<green>Stock restant<yellow>{0}'
  this-shop-is-buying: <green>Cette boutique <light_purple>ACHETE<green> des objets.
  successfully-sold: '<green>Vente réussie :'
  total-value-of-chest: '<green>Valeur totale du coffre: <yellow>{0}'
currency-not-exists: <red>Impossible de trouver le mode actuel que vous voulez définir, cela est peut-être dû à une mauvaise orthographe ou ce mode n'est pas disponible dans ce monde.
no-nearby-shop: <red>Aucune boutique à proximité ne correspond à {0}.
translation-author: 'Ghost_chu, Andre_601'
integrations-check-failed-trade: <red>Intégration {0} à refusé la création de la boutique
shop-transaction-failed: <red>Sorry, but an internal error occurred while processing your purchase. The purchase has been cancelled and any operation has been rollback. Please contact the server administrators if it keeping occurred.
success-change-owner-to-server: <green>Le propriétaire de la boutique à été définit au Serveur avec succès.
shop-name-not-found: <red>The shop named <yellow>{0} <red>not exists.
shop-name-too-long: <red>Ce nom de boutique est trop long (longueur max. {0}), veuillez en choisir un autre !
metric:
  header-player: '<yellow>Les transactions de la boutique {0} sont {1} {2}:'
  action-hover: <yellow>{0}
  price-hover: Total {0}, incluant {1} de taxes.
  unknown: <gray>(inconnu)
  undefined: <gray>(sans nom)
  no-results: <red>Aucune transaction.
  action-description:
    DELETE: <yellow>Player deleted a shop. And refunded the shop creation fee to owner if possible.
    ONGOING_FEE: <yellow>Player paid the ongoing fee because the payment period.
    PURCHASE_BUYING_SHOP: <yellow>Le joueur a acheté des objets dans une boutique de vente.
    CREATE: <yellow>Le joueur a créé une boutique.
    PURCHASE_SELLING_SHOP: <yellow>Le joueur a acheté des objets dans une boutique de vente
    PURCHASE: <yellow>Objet acheté avec une boutique
  query-argument: 'Argument de requête : {0}'
  amount-hover: <yellow>{0}x
  header-shop: '<yellow>Les transactions de la boutique {0} sont {1} {2}:'
  player-hover: |-
    <yellow>{0}<newline><gold>UUID: <gray>{1}
  looking-up: <yellow>Recherche métrique en cours, veuillez patienter...
  tax-hover: <yellow>{0} taxes
  header-global: '<yellow>Les transactions de la boutique {0} sont {1} :'
  na: <gray>N/A
  transaction-count: <yellow>{0} au total
  shop-hover: |-
    <yellow>{0}<newline><gold>Pos: <gray>{1} {2} {3}, World: {4}<newline><gold>Owner: <gray>{5}<newline><gold>Shop Type: <gray>{6}<newline><gold>Item: <gray>{7}<newline><gold>Price: <gray>{8}
  time-hover: '<yellow>Heure: {0}'
  amount-stack-hover: <yellow>{0}x stack
permission-denied-3rd-party: '<red>Permission refusée: plugin tiers [{0}].'
you-dont-have-that-many-items: <red>Vous ne possédez que {0} {1} dans votre inventaire.
complete: <green>Action effectuée
translate-not-completed-yet-url: 'La traduction de la langue {0} a terminé {1}, Voulez-vous nous aider à améliorer la traduction ? Cliquez ici : {2}'
success-removed-shop: <green>Boutique supprimée avec succès.
currency-set: <green>Le mode de la boutique a été définie avec succès à {0}.
shop-purged-start: <green>La purge a commencé, vérifiez la console pour plus de détails.
economy-transaction-failed: <red>Sorry, but an internal error occurred while processing your transaction. The transaction has been cancelled and any economy operation has been rollback. Please contact the server administrators if it keeping occurred.
nothing-to-flush: <green>Aucun nouveau message.
no-price-change: <red>Le prix ne changera pas!
edition-confilct: QuicShop-Hikari with QuickShop-Reremake installed may conflict each other, uninstall one of them.
inventory-unavailable: |-
  <red>This shop InventoryWrapper not exists or invalid. Did you using addon to re-binding the shop Inventory? Information: InventoryWrapper={0}, WrapperProvider={1}, SymbolLink={2}. Please contact the server administrators.
file-test: Ceci est un fichier texte de test. Nous l'utilisons pour tester si les messages sont cassé. Vous pouvez le remplir avec n'importe quel "easter eggs" que vous aimez ici :)
unknown-player: <red>Le joueur cible n'existe pas, merci de vérifier le pseudo que tu as entré.
player-offline: <red>Le joueur spécifié est actuellement hors-ligne.
player-profile-format: <aqua>{0}</aqua><gold>(<yellow>{1}</yellow>)</gold>
shop-type:
  selling: VENDRE
  buying: ACHETER
language:
  qa-issues: '<yellow>Problèmes d''assurance qualité : <aqua>{0}%'
  code: '<yellow>Nom: <gold>{0}'
  approval-progress: '<yellow>Progression de l''Approbation : <aqua>{0}%'
  translate-progress: '<yellow>Progression de la traduction : <aqua>{0}%'
  name: '<yellow>Nom: <gold>{0}'
  help-us: <green>[Aidez-nous à améliorer la qualité de la traduction]
warn-to-paste: |-
  <yellow>Assemblage de données et upload vers PasteBin, cela peut prendre un moment. <red><bold>Attention :<red> Les données sont gardées publiques pendant UNE semaine, cela peut faire fuiter la config de votre serveur, assurez vous de la partager à des gens de confiances.
how-many-sell-stack: <green>Enter in chat, how many bulk you wish to <light_purple>SELL<green>. There are <yellow>{0}<green> items per bulk and you can sell <yellow>{1}<green> bulks. Enter <aqua>{2}<green> in chat to sell all.
updatenotify:
  buttontitle: '[Mettre à jour]'
  onekeybuttontitle: '[Mise à jour instantanée]'
  label:
    github: '[GitHub]'
    ore: '[Ore]'
    lts: '[LTS]'
    nbtapi: '[+NBTAPI]'
    qualityverifyed: '[Qualité]'
    master: '[Master]'
    unstable: '[Instable]'
    paper: '[+Paper Optimized]'
    stable: '[Stable]'
    spigotmc: '[SpigotMC]'
    papermc: '[PaperMC]'
    bukkitdev: '[BukkitDev]'
    polymart: '[Polymart]'
    modrinth: '[Modrinth]'
    basic: '[Basic]'
  list:
    - '{0} a été publiée. Vous utilisez encore la version {1} !'
    - Boum! Nouvelle mise à jour {0} disponible!
    - Surprise! La mise à jour {0} est sortie. Vous êtes actuellement sur la version {1}
    - Visiblement vous avez besoin d'une mise à jour... La version {0} a été publiée !
    - Oups! La version {0} a déjà été publiée... Vous êtes sur la version {1} !
    - Je vous jure, QuickShop a été mis à jour vers la version {0}, pourquoi vous ne l'avez pas mis à jour?
    - Réparer et ré... Désolé mais {0} est sortie !
    - Err! Non, ceci n'est pas une erreur. {0} viens d'être publiée !
    - OH MON DIEU ! {0} est disponible ! Pourquoi utilisez vous encore {1} ?
    - 'Nouveauté de la journée: QuickShop a été mis à jour en {0}'
    - Plugin K.I.A, vous devez mettre à jour {0}!
    - Mise à jour de {0} activée. Sauvegardez la mise à jour !
    - Il y a une mise à jour commandant, {0} vient de sortir!
    - Regarde mon style --- {0} mise à jour, tu as toujours la version {1}
    - Ahhhhhhh! Nouvelle mise à jour {0}!
    - Qu'est-ce que tu penses? {0} Une nouvelle mise à jour publiée!
    - Docteur, QuickShop a une nouvelle mise à jour {0}! Vous devriez mettre à jour ~
    - Ko~ko~da~yo~ QuickShop a une nouvelle mise à jour {0}~
    - Paimon souhaite te dire que QuickShop a une nouvelle mise à jour {0}!
  remote-disable-warning: '<red>Cette version de QuickShop est marquée comme désactivée par le serveur distant, ce qui signifie que cette version peut avoir de sérieux problèmes, obtenez plus en profondeur des détails sur notre page SpigotMC : {0}. Cette alerte continuera d''apparaître jusqu''à ce que vous passiez à une version stable, mais elle n''affectera pas les performances de votre serveur.'
purchase-out-of-stock: <red>Cette boutique est en rupture de stock, contactez le propriétaire ou le personnel de la boutique pour recharger le stock.
nearby-shop-entry: '<green>- Info:{0} <green>Prix:<aqua>{1} <green>x:<aqua>{2} <green>y:<aqua>{3} <green>z:<aqua>{4} <green>distance: <aqua>{5} <green>bloc(s)'
chest-title: Boutique QuickShop
console-only: <red>Cette commande ne peut être exécutée que par la console.
failed-to-put-sign: <red>Il n'y a pas assez de place autour de la boutique pour placer un panneau d'information.
shop-name-unset: <red>Le nom de cette boutique a été supprimé
shop-nolonger-freezed: <green>Vous avez dégelé la boutique. C'est de retour à la normale!
no-permission-build: <red>Tu ne peux pas créer une boutique ici.
tableformat:
  left_half_line: <dark_purple>+--------------------
  right_half_line: <dark_purple>--------------------+
  full_line: <dark_purple>+---------------------------------------------------+
  left_begin: '<dark_purple>| '
quickshop-gui-preview: Aperçu de l'interface QuickShop
translate-not-completed-yet-click: La traduction de la langue {0} a terminé {1}, Voulez-vous nous aider à améliorer la traduction ? Cliquez ici !
taxaccount-invalid: <red>Le compte que vous avez défini est invalide, veuillez entrer un nom de joueur ou un uuid (avec les tirets) valide.
player-bought-from-your-store: <red>{0} a acheté {1} {2} dans ta boutique, et tu as reçu ou payé {3} ( de taxes.
reached-maximum-can-create: '<red>Tu as atteint ton nombre de coffre de boutiques maximal : {0}/{1}!'
reached-maximum-create-limit: <red>Vous avez atteint le nombre maximum de boutiques que vous pouvez créer
translation-version: 'Support Version: Hikari'
no-double-chests: <red>Vous ne pouvez pas créer de boutique avec un double coffre.
price-too-cheap: <red>Le prix doit être supérieur à <yellow>${0}
shop-not-exist: <red>Il n'y a aucune boutique.
bad-command-usage: <red>Arguments de commande incorrects !
cleanghost-warning: <yellow>This command will purge <red>all</red> shops if the shop is corrupted, was created in not allowed worlds, is selling/buying not allowed items or <red><bold>EXISTS IN A UNLOADED WORLD</bold></red>. Make sure to create a full backup of your shop data first and use <aqua>/qs cleanghost confirm</aqua> to continue.
cleanghost-starting: <green>Début de la vérification des boutiques fantômes (blocs de conteneur manquants). Toutes les boutiques inexistantes seront supprimées...
cleanghost-deleting: <yellow>Une boutique corrompue <aqua>{0}</aqua> a été trouvée, car {1}, la marquer pour supprimer...
cleanghost-deleted: <green>Total <yellow>{0}</yellow> boutiques ont été supprimées.
shop-purchase-cancelled: <red>Achat de la boutique annulé.
bypassing-lock: <red>Contournement d'un verrou de QuickShop!
bungee-cross-server-msg: '<yellow>QuickShop CSM: <green>{0}'
saved-to-path: Le fichier de sauvegarde a été sauvegardé dans {0}.
shop-now-freezed: <green>Vous avez gelé la boutique. Personne ne peut échanger avec cette boutique maintenant!
price-is-now: <green>Le nouveau prix de la boutique est <yellow>{0}
shops-arent-locked: <red>Souvenez-vous que les boutiques ne sont pas protégées contre le pillage! Tu dois les verrouiller ou protéger ta zone pour éviter le vol!
that-is-locked: <red>Cette boutique est verouillée.
shop-has-no-space: <red>Cette boutique n'a de la place que pour {0} de plus {1}.
safe-mode-admin: '<red><bold>WARNING: <red>The QuickShop on this server now running under safe-mode, no features will working, please type <yellow>/qs <red> command to check any errors.'
shop-stock-too-low: <red>Il ne reste que {0} {1} dans cette boutique!
world-not-exists: <red>Le monde <yellow>{0}<red> n'existe pas
how-many-sell: <green>Enter in chat, how much you wish to <light_purple>SELL<green>. You can sell <yellow>{0}<green>. Enter <aqua>{1}<green> in chat, to sell all.
shop-freezed-at-location: <yellow>Votre boutique {0} à {1} a été gelée!
translation-contributors: 'Contributeurs : Timtower, Netherfoam, KaiNoMood, Mgazul, JackThePicken, Andre_601, brunopaiva15, HiiRaZ, WAXIE'
empty-success: <green>Vider la boutique avec succès
taxaccount-set: <green>Le compte de taxe de cette boutique a été défini à <yellow>{0}
support-disable-reason:
  hot-reload: <yellow>Unsupported hot reloaded, restart the server and check again.
  outdated: <yellow>This version of QuickShop already outdated, up-to-date before requesting support!
  bad-hosts: |-
    <yellow>This server HOSTS has been modified and QuickShop some functions require connect to Mojang API correctly. Fix the HOSTS before asking support, Windows at C:\windows\system32\drivers\etc\hosts, Linux at /etc/hosts.
  privacy: <yellow>This server running under cracked (offline) mode. If you are running bukkit server under proxy and online-mode=true on proxy, please configure the proxy related settings correctly.
  modified: <yellow>File integrity check failed, this QuickShop build has been modified by others.
  consolespamfix-installed: <yellow>ConsoleSpamFix installed, it will hidden exception details, temporarily disable it during asking support.
  authlib-injector-detected: <yellow>This server running under 3rd-party authlib provider like authlib-injector.
  unsupported-server-software: <yellow>Unsupported server software, Any modded hybrid server software is unsupported including MCPC, Cauldron, CatServer, Mohist, Magma, Fukkit, Cardboard etc.
supertool-is-disabled: <red>Le super-outil est désactivé, impossible de détruire une boutique avec.
unknown-owner: Inconnu
restricted-prices: '<red>Prix restreints {0}: min {1}, max {2}'
nearby-shop-this-way: '"<green>Shop is {0} blocks away from you.'
owner-bypass-check: <yellow>Vérifications passées. Echange réussi ! (Tu es propriétaire de la boutique!)
april-rick-and-roll-easter-egg: "<hover:show_text:'Cliquez pour ouvrir dans le navigateur et avoir des récompenses limités !'><click:open_url:'https://www.youtube.com/watch?v=dQw4w9WgXcQ'><green>Cliquez ici pour obtenir vos récompenses limitées dans le temps fournies par le développeur QuickShop-Hikari !</green></click></hover>"
signs:
  item-right: ''
  out-of-space: Espace insuffisant
  unlimited: Illimité
  stack-selling: 'Racheter : {0}'
  stack-price: '{0} par {1} x {2}'
  status-unavailable: <red>
  out-of-stock: Rupture de stock
  stack-buying: Revendre {0}
  freeze: Echange désactivé
  price: '{0} l''unité'
  buying: Revendre {0}
  header: '{1}{0}'
  header-available: <green>{0}
  header-unavailable: <red>{0}
  selling: 'Racheter : {0}'
  status-available: <green>
  item-left: ''
negative-amount: <red>Vous ne pouvez pas échanger une quantité négative
display-turn-on: <green>Activation réussie de l'affichage de la boutique.
shop-staff-deleted: <green> {0} a été ajouté avec succès comme membre du personnel pour votre boutique.
nearby-shop-header: '<green>À proximité de la boutique correspondante <aqua>{0}<green>:'
backup-failed: Impossible de sauvegarder la base de données, vérifiez la console pour plus de détails.
shop-staff-cleared: <green>Tous les gérants de ta boutique ont été supprimés avec succès.
price-too-high: <red>Le prix est trop haut! Tu ne peux pas mettre un prix supérieur à {0}.
plugin-cancelled: '<red>Operation cancelled, Reason: {0}'
player-sold-to-your-store: <green>{0} à vendu {1} {2} dans votre boutique.
shop-out-of-stock: <dark_purple>Votre boutique aux coordonnées {0}, {1}, {2}, est à court de {3}!
how-many-buy: <green>Enter in chat, how many you wish to <aqua>BUY<green>. You can buy <yellow>{0}<green>. Enter <aqua>{1}<green> to buy them all.
language-info-panel:
  help: 'Aidez-nous : '
  code: 'Code : '
  name: 'Langue: '
  progress: 'Progression : '
  translate-on-crowdin: '[Traduit sur Crowdin]'
item-not-exist: <red>L'objet <yellow>{0} <red>n'existe pas, veuillez vérifier votre orthographe.
shop-creation-failed: <red>La création de la boutique a échoué, veuillez contacter l'administrateur du serveur.
inventory-space-full: <red>Your inventory space remains can only put <green>{1}x <red>items into it, try empty the your inventory!
no-creative-break: <red>Vous ne pouvez pas détruire les boutiques des autres joueurs en mode créatif, basculez en mode survie ou essayez d'utiliser le super-outil {0} à la place.
booleanformat:
  success: <green>✔
  failed: <red>✘
controlpanel:
  stack: '<green>Per bulk amount: <aqua>{0} <yellow>[<light_purple><bold>Change<yellow>]'
  price-hover: <yellow>Cliquez pour définir un nouveau prix à la boutique.
  remove: <red><bold>[Supprimer la boutique]
  mode-buying-hover: <yellow>Cliquez pour changer la boutique en mode VENTE.
  empty: '<green>Empty: Remove all items <yellow>[<light_purple><bold>OK<yellow>]'
  stack-hover: <yellow>Cliquez pour définir la quantité d'objets par vrac. Réglez sur 1 pour un comportement normal.
  alwayscounting-hover: <yellow>Click to toggle if the shop is always counting container.
  alwayscounting: '<green>Always counting: {0} <yellow>[<light_purple><bold>Change<yellow>]'
  setowner: '<green>Owner: <aqua>{0} <yellow>[<light_purple><bold>Change<yellow>]'
  freeze: '<yellow>Freeze mode: <aqua>{0} <yellow>[<light_purple><bold>Toggle<yellow>]'
  price: '<green>Price: <aqua>{0} <yellow>[<light_purple><bold>Change<yellow>]'
  currency-hover: <yellow>Cliquez pour définir ou supprimer le mode utilisée par cette boutique
  lock: '<yellow>Shop lock: <aqua>{0} <yellow>[<light_purple><bold>Toggle<yellow>]'
  mode-selling: '<green>Mode du shop: <aqua>VENTE <yellow>[<light_purple><bold>Changer<yellow>]'
  currency: '<green>Currency: <aqua>{0} <yellow>[<light_purple><bold>Set<yellow>]'
  setowner-hover: <yellow>Cliquez pour changer le propriétaire.
  mode-buying: '<green>Shop mode: <aqua>Buying <yellow>[<light_purple><bold>Change<yellow>]'
  item: '<green>Shop Item: {0} <yellow>[<light_purple><bold>Change<yellow>]'
  unlimited: '<green>Unlimited: {0} <yellow>[<light_purple><bold>Change<yellow>]'
  unlimited-hover: <yellow>Cliquez pour définir le shop comme illimité.
  refill-hover: <yellow>Cliquez pour réapprovisionner le shop.
  remove-hover: <yellow>Cliquez pour supprimer cette boutique.
  toggledisplay-hover: <yellow>Activer/désactiver l'affichage de l'item de la boutique
  refill: '<green>Refill: Refill the items <yellow>[<light_purple><bold>OK<yellow>]'
  freeze-hover: <yellow>Activer/désactiver le statut de blocage de la boutique.
  lock-hover: <yellow>Activer/Désactiver la protection de verrouillage de la boutique.
  item-hover: <yellow>Cliquez pour changer l'objet de la boutique
  infomation: '<green>Panneau de contrôle de la boutique :'
  mode-selling-hover: <yellow>Cliquez pour changer la boutique en mode ACHAT.
  empty-hover: <yellow>Cliquez pour vider tout les objets du shop.
  toggledisplay: '<green>Display-Item: <aqua>{0} <yellow>[<light_purple><bold>Toggle</bold></light_purple>]'
  history: '<green>Historique : <yellow>[<bold><light_purple>Voir</light_purple></bold>]</yellow>'
  history-hover: <yellow>Cliquez pour afficher les journaux de l’historique de la boutique
timeunit:
  behind: derrière
  week: "{0} semaine"
  weeks: "{0} semaines"
  year: "{0} année"
  before: avant
  scheduled: programmé
  years: "{0} années"
  scheduled-in: programmé en {0}
  second: "{0} seconde"
  std-past-format: 'il y a {5}{4}{3}{2}{1}{0}'
  std-time-format: HH:mm:ss
  seconds: "{0} secondes"
  hour: "{0} heure"
  scheduled-at: programmé à {0}
  after: après
  day: "{0} jour"
  recent: récent
  between: entre
  hours: "{0} heures"
  months: "{0} mois"
  longtimeago: il y a longtemps
  between-format: entre {0} et {1}
  minutes: "{0} minutes"
  justnow: maintenant
  minute: "{0} minute"
  std-format: MM/dd/yyyy HH:mm:ss
  future-plain-text: future
  month: "{0} month"
  future: in {0}
  days: "{0} days"
command:
  reloading: '<green>Configuration rechargée. <yellow>Certains changements peuvent nécessiter un redémarrage pour être appliqués.<newline><gray>(Note : Le comportement du rechargement a été modifié après la ********, nous rechargeons désormais uniquement la configuration plutôt que l''ensemble du plugin, afin de garantir que le serveur ne plante pas.)'
  description:
    buy: <yellow>Transforme la boutique en mode <light_purple>ACHAT
    about: <yellow>Afficher l'aide QuickShop
    language: Modifier la langue actuelle
    purge: <yellow>Lancer la purge des boutiques en arrière-plan
    paste: <yellow>Téléchargement automatique des données du serveur vers Pastebin
    title: <green>Aide QuickShop
    remove: <yellow>Supprime la boutique que vous regardez
    ban: <yellow>Banni un joueur de la boutique
    empty: <yellow>Retire tout le stock d'un coffre de shop
    alwayscounting: <yellow>Set if shop always counting container even is unlimited
    setowner: <yellow>Modifie le propriétaire d'une boutique.
    reload: <yellow>Recharge le fichier config.yml pour QuickShop
    freeze: <yellow>Désactiver ou activer le trading en magasin
    price: <yellow>Change le prix d'achat/vente d'une de tes boutiques
    find: <yellow>Localise le coffre de shop le plus proche d'un type spécifique.
    create: <yellow>Crée une nouvelle boutique au coffre cible
    lock: <yellow>Changer l'état de verrouillage de la boutique
    currency: <yellow>Définir ou supprimer les paramètres de devise de la boutique
    removeworld: <yellow>Supprime TOUTES les boutiques dans un monde spécifié
    info: <yellow>Afficher les statistiques QuickShop
    owner: <yellow>Modifie le propriétaire d'une boutique.
    amount: <yellow>Pour définir le montant de l'objet (utilisé lors d'un problème de chat)
    item: <yellow>Changer l'article d'une boutique
    debug: <yellow>Active le mode développeur
    unlimited: <yellow>Donne à une boutique un stock illimité.
    sell: <yellow>Transforme la boutique en mode <aqua>VENTE
    fetchmessage: <yellow>Récupère les messages non lus de la boutique
    staff: <yellow>Gérer les gérants de ta boutique
    clean: <yellow>Supprime tous les coffres (chargés) ayant 0 de stock
    refill: <yellow>Ajoute un nombre donné d'objets à un shop.
    help: <yellow>Afficher l'aide QuickShop
    removeall: <yellow>Supprimer toutes les boutiques d'un joueur spécifié
    unban: <yellow>Débannir un joueur de la boutique
    transfer: <yellow>Transférer TOUS les shops d'un joueur vers un autre
    transferall: <yellow>Transférer TOUS les shops d'un joueur vers un autre
    transferownership: <yellow>Transfer the shop you're looking at to someone else
    size: <yellow>Changez par quantité en vrac d'une boutique
    supercreate: <yellow>Créer une boutique en contournant tous les contrôles de protection
    taxaccount: <yellow>Définir le compte de taxe que la boutique utilise
    name: <yellow>Naming a shop to specific name
    toggledisplay: <yellow>Activer/désactiver l'affichage de l'item de la boutique
    permission: <yellow>Shop permission management
    lookup: <yellow>Manage lookup-able items table
    database: <yellow>View and maintain QuickShop database
    benefit: <yellow>Settings of divide benefits between shop owner and other players
    tag: <yellow>Add, remove or query tags of a shop
    suggestprice: <yellow>Suggest a recommend price for looking shop, based on other shops
    history: <yellow>View the history transactions of a shop
    sign: <yellow>Change the sign material of a shop
  bulk-size-not-set: '<red>Utilisation: /qs size <quantité>'
  no-type-given: '<red>Utilisation: /qs find <objet>'
  feature-not-enabled: Cette fonctionnalité n'est pas activée dans le fichier de configuration.
  now-debuging: <green>Passage réussi en mode développeur. Actualisation de QuickShop...
  no-amount-given: <red>Aucun montant donné. Utilisez <green>/qs refill <quantité><red>
  no-owner-given: <red>Aucun propriétaire indiqué
  disabled: '<red>Cette commande est désactivée : <yellow>{0}'
  bulk-size-now: <green>Négocier <yellow>{0}x {1}
  toggle-always-counting:
    counting: <green>Shop is now always counting container even is unlimited
    not-counting: <green>Shop is now respect if shop is unlimited
  cleaning: <green>Suppression des boutiques sans stock...
  now-nolonger-debuging: <green>Passage réussi en mode développeur. Actualisation de QuickShop...
  toggle-unlimited:
    limited: <green>Ce shop est désormais limité
    unlimited: <green>Ce shop est désormais illimité
  transfer-success-other: <green>Transfert du shop <yellow>{0} <green>à <yellow>{1}
  no-trade-item: <green>Veuillez tenir un objet d'échange pour le changer dans la main principale
  wrong-args: <red>Commande invalide. Utilisez <bold>/qs help <red>pour voir une liste de commandes.
  some-shops-removed: <yellow>{0} <green>shop(s) supprimé(s)
  new-owner: '<green>Nouveau propriétaire: <yellow>{0}'
  format: <green>{0} {1} <yellow>- {2}
  transfer-success: <green>Transfert du shop <yellow>{0} <green>à <yellow>{1}
  now-buying: <light_purple>ACHAT de <yellow>{0}
  now-selling: <aqua>VEND <green>actuellement des <yellow>{0}
  cleaned: <green>Les boutiques de <yellow>{0}<green> ont été supprimés.
  trade-item-now: '<green>Négociation en cours : <yellow>{0}x {1}'
  no-world-given: <red>Veuillez entrer un nom de monde
  format-disabled: <red>{0} {1} <gray>- {2}
  invalid-bulk-amount: <red>La valeur donnée {0} est plus grande que la taille maximale de la pile ou inférieure à 1
currency-not-support: <red>Le plugin d'économie ne supporte pas la fonction de multi-économie.
trading-in-creative-mode-is-disabled: <red>Vous ne pouvez pas utiliser une boutique en mode créatif.
the-owner-cant-afford-to-buy-from-you: <red>Cela coûte {0} mais le propriétaire n'a plus que {1}
you-cant-afford-shop-naming: <red>You can't afford shop naming, it costs {0} to naming.
inventory-error: |-
  <red>Failed to processing the InventoryWrapper. Did you using addon to re-binding the shop Inventory? Information: Exception={0}, InventoryWrapper={1}, WrapperType={2}, WrapperProvider={3}, SymbolLink={4}. Please contact the server administrators.
integrations-check-failed-create: <red>Intégration {0} à refusé la création de la boutique
shop-out-of-space: <dark_purple>Votre boutique aux coordonnées {0}, {1}, {2} est pleine.
admin-shop: Boutique Admin
no-anythings-in-your-hand: <red>Vous n'avez aucun objet dans votre main.
no-permission: <red>Vous n'avez pas la permission de faire ceci.
chest-was-removed: <red>Le coffre a été supprimé.
you-cant-afford-to-buy: <red>Cette transaction coûte {0}, mais vous ne disposez que de {1}
shops-removed-in-world: <yellow>La totalité <aqua>{0}<yellow> des boutiques a été supprimée dans le monde <aqua>{1}<yellow>.
display-turn-off: Désactivation réussie de l'affichage de la boutique.
client-language-unsupported: <yellow>QuickShop ne prend pas en charge la langue de votre client, nous sommes maintenant passés à la locale {0}.
language-version: '63'
not-managed-shop: <red>Vous n'êtes pas le propriétaire ou le modérateur de cette boutique
shop-cannot-trade-when-freezing: <red>Vous ne pouvez pas échanger avec cette boutique car elle est gelée.
invalid-container: <red>Invalid container, You can only create the shop on block who have inventory.
permission:
  header: <green>Shop Permission Details
  header-player: <green>Shop Permission Details for {0}
  header-group: <green>Shop Permission Details for group {0}
  table: <gold><b>{0}</b>:</gold> <gray>{1}</gray>
  item:
    purchase: <yellow>Permission qui autorise les utilisateurs qui l'ont à acheter la boutique. (y compris l'achat et la vente)
    show-information: <yellow>Permission to allow users who have this to see the shop information. (open shop info panel)
    preview-shop: <yellow>Permission to allow users who have this to preview the shop. (preview item)
    search: <yellow>Permission to allow users who have this to search the target shop. (remove permission will hide shop from search result)
    delete: <yellow>Permission to allow users who have this to delete the shop.
    receive-alert: <yellow>Permission to allow users who have this to receive alert message (e.g out-of-stock or new trade).
    access-inventory: <yellow>Permission to allow users who have this to access the shop inventory.
    ownership-transfer: <yellow>Permission to allow users who have this to transfer the shop ownership.
    management-permission: <yellow>Permission to allow users who have this to manage the groups permissions and edit user's group.
    toggle-display: <yellow>Permission to allow users who have this to toggle the shop display item.
    set-shoptype: <yellow>Permission to allow users who have this to set the shop type (switch buying or selling).
    set-price: <yellow>Permission to allow users who have this to set the shop price.
    set-item: <yellow>Permission to allow users who have this to set the shop item.
    set-stack-amount: <yellow>Permission to allow users who have this to set the shop stack amount.
    set-currency: <yellow>Permission to allow users who have this to set the shop currency.
    set-name: <yellow>Permission to allow users who have this to set the shop name.
    set-sign-type: <yellow>Change the sign material that attached on the shop.
    view-purchase-logs: <yellow>Permission to view shop purchase logs.
  group:
    everyone: <yellow>Default group for all users excepted shop owner.
    staff: <yellow>System group for shop staffs.
    administrator: <red>System group for administrators, the users in this group will have permissions almost same with shop owner.
invalid-group: <red>Invalid group name.
invalid-permission: <red>Invalid permission.
invalid-operation: <red>Invalid operation, only {0} are allowed.
player-no-group: <yellow>Player {0} not in any group in this shop.
player-in-group: <green>Player {0} is in group <aqua>{1}</aqua> in this shop.
permission-required: <red>You don't have permission {0} in this shop to do this.
no-permission-detailed: <red>You don't have permission <yellow>{0}</yellow> to do this.
paste-notice: "<yellow>Note: If you are creating a Paste for troubleshooting purposes, be sure to reproduce the error before creating the Paste as quickly as possible; we need the logs retained briefly in the buffer to troubleshoot. If you create a Paste too slowly or without first reproducing the error or restarted server, the Paste will record nothing and useless."
paste-uploading: <aqua>Please wait... Uploading the paste to pastebin......
paste-created: '<green>Paste created, click to open in browser: <yellow>{0}</yellow><newline><red>Warning: <gray>Never send paste to who you don''t trust.'
paste-created-local: |-
  <green>Paste created and saved to your local disk at: {0}<newline><red>Warning: <gray>Never send paste to who you don't trust.
paste-created-local-failed: <red>Failed to save paste to your local disk, please check your disk.
paste-451: |-
  <gray><b>TIPS: </b> Your current country or region seems to have blocked the CloudFlare Workers service and QuickShop Paste may not be loaded properly.<newline><gray>If the subsequent operation fails, try adding the --file additional parameter to generate a local Paste: <dark_gray>/qs paste --file</dark_gray>
paste-upload-failed: <red>Failed to upload paste to {0}, trying other pastebin provider...
paste-upload-failed-local: <red>Failed to upload paste, trying generate local paste...
command-incorrect: '<red>Command usage incorrect, type /qs help to check help. Usage: {0}.'
successfully-set-player-group: <green>Successfully set player {0} group to <aqua>{1}</aqua>.
successfully-unset-player-group: <green>Successfully remove player group in this shop.
successfully-set-player-permission: <green>Successfully set player {0} permission <aqua>{1}</aqua> in shop <aqua>{2}</aqua>.
lookup-item-created: <green>An item named <aqua>{0}</aqua> has been created in the lookup table. You can refer this item in configurations now.
lookup-item-exists: <red>An item named <yellow>{0}</yellow> already exists in the lookup table, delete it or pick another name.
lookup-item-not-found: <red>An item named <yellow>{0}</yellow> not exists.
lookup-item-name-illegal: <red>Item name illegal. Only alphanumeric characters and underscores are allowed.
lookup-item-name-regex: '<red>The name must match this regex: <aqua>{0}</aqua>.'
lookup-item-test-not-found: '<gold>Test: <yellow>The item in your hand not registered in lookup table.'
lookup-item-test-found: '<gold>Test: <green>The item in your hand was registered as name <aqua>{0}</aqua> in lookup table.'
lookup-item-removed: <green>The specified item <aqua>{0}</aqua> has been successfully removed from lookup table.
internal-error: <red>An internal error occurred, please contact the server administrator.
argument-cannot-be: <red>The argument <aqua>{0}</aqua> cannot be <yellow>{1}</yellow>.
argument-must-between: <red>The argument <aqua>{0}</aqua> value must between <yellow>{1}</yellow> and <yellow>{2}</yellow>
invalid-percentage: <red>Percentage invalid, you must add '%' after the number of percentage.
display-fallback: |-
  <red>Due to an internal failure, a fallback message is being displayed.
  The value of this item may not be localized or processed correctly.
  Please contact the server administrator.
not-a-valid-time: |-
  <red>The string <yellow>{0}</yellow> not a valid timestamp, please enter a <aqua>Zulu Time (ISO 8601)</aqua> or <aqua>Unix Epoch Time in seconds</aqua>.
  <gold>Valid timestamp example: (for Sat, 17 Dec 2022 10:31:37 GMT)</gold>
  <aqua>-<aqua> <yellow>2022-12-17T10:31:37Z</yellow> <grey>(Zulu Time)</grey>
  <aqua>-<aqua> <yellow>1671273097</yellow> <grey>(Unix Epoch Time in seconds)</grey>
invalid-past-time: <red>You can't specify a time in the past.
debug:
  arguments-invalid: <red>Provided arguments <yellow>{0}</yellow> invalid.
  sign-located: '<green>Sign valid: <yellow>{0}</yellow>.'
  operation-missing: <red>You must specific an operation.
  operation-invalid: <red>You must specific an valid operation.
  invalid-base64-encoded-sql: <red>The SQL provided must be base64 encoded.
  warning-sql: |-
    <red><bold>Warning:</bold></red><yellow>You're executing a SQL statement, this may corrupt your database or destroy any data in database even it belongs to other plugins.
    <red>Don't confirm if you don't trust who send you this command.
  warning-sql-confirm: <yellow>To confirm this dangerous operation, please type <aqua>/qs debug database sql confirm {0}</aqua> in 60 seconds.
  warning-sql-confirm-hover: <yellow>Click to confirm this dangerous operation.
  sql-confirm-not-found: <yellow>Didn't found the operation that you given, it may invalid or expired.
  sql-executing: '<yellow>Executing SQL statement: <aqua>{0}</aqua>'
  sql-completed: <green>Completed, {0} rows affected.
  sql-exception: <red>An error occurred while executing the SQL query, check the Console for details!
  sql-disabled: '<red>For security reason, SQL queries are disabled in this server, If you really need this feature, you can add the following flag to startup arguments: <aqua>{0}</aqua> with `true` value to enable it.'
  force-shop-reload: <yellow>Forcing reload all loaded shops...
  force-shop-reload-complete: <green>A Forcé <aqua>{0}</aqua> boutiques à recharger.
  force-shop-loader-reload: <yellow>Forcing shop-loader reloading...
  force-shop-loader-reload-unloading-shops: <yellow>Déchargement de <aqua>{0}</aqua> boutiques chargées...
  force-shop-loader-reload-unloading-shops-from-memory: <yellow>Retrait de <aqua>{0}</aqua> boutiques de la mémoire...
  force-shop-loader-reload-reloading-shop-loader: <yellow>Rappel de shop-loader pour recharger tous les magasins à partir de la base de données...
  force-shop-loader-reload-complete: <green>Le chargeur de boutiques a rechargé toutes les boutiques !
  toggle-shop-loaded-status: <aqua>Toggle this shop loaded status to <gold>{0}</gold>
  shop-internal-data: '<yellow>The internal data of this shop: <reset>{0}</reset>'
  handler-list-not-valid-bukkit-event-class: <red>La classe fournie <yellow>{0}</yellow> n’est pas une classe d’événement Bukkit valide.
  update-player-shops-signs-no-username-given: <red>Vous devez fournir un nom d’utilisateur de joueur valide.
  update-player-shops-signs-create-async-task: <yellow>Création de tâches asynchrones pour forcer la mise à jour des panneaux...
  update-player-shops-player-selected: '<yellow>Player selected: <gold>{0}</gold>'
  update-player-shops-player-shops: <yellow>Total <gold>{0}</gold> boutiques en attente de mises à jour.
  update-player-shops-per-tick-threshold: '<yellow>Max shops can be update per tick: <gold>{0}</gold>'
  update-player-shops-complete: <green>Tâche terminée, <yellow>{0}ms</yellow> utilisée pour la mise à jour.
  update-player-shops-task-started: <gold>Les tâches ont été lancées, veuillez attendre qu’elles soient terminées.
  item-info-store-as-string: "<green>Le magasin que vous regardez : <gold>{0}</gold> Hash : <white>{1}</white>"
  item-info-hand-as-string: "<green>L’objet que vous avez en main : <gold>{0}</gold> Hash : <white>{1}</white>"
  item-matching-result: "<green>Hand2Store : <aqua>{0}</aqua>, Store2Hand: <aqua>{1}</aqua>"
  hikari-cp-size-tweak: "<green>HikariCP MaximumPoolSize et MinimumIdle ont été définis sur <white>{0}</white>"
  hikari-cp-testing: "<green>Veuillez patienter, tester la connexion HikariCP..."
  hikari-cp-working: "<green>Passer! HikariCP fonctionne bien !"
  hikari-cp-not-working: "<red>Raté! La connexion qui a été rétablie par HikariCP est morte ! (Non réussi le test en 1 seconde)"
  hikari-cp-timeout: "<red>HikariCP est expiré lors de l’obtention d’une connexion valide, veuillez nettoyer toutes les requêtes actives pour libérer les ressources de connexion."
  queries-stopped: "<green>Arrêté <white>{0}</white> requêtes actives."
  queries-dumping: "<yellow>Vidage des requêtes actives..."
  restart-database-manager: "<yellow>Redémarrage de SQLManager..."
  restart-database-manager-clear-executors: "<yellow>Dégagement des exécuteurs testamentaires..."
  restart-database-manager-unfinished-task: "<yellow>Tâche inachevée : <white>{0}</white>"
  restart-database-manager-unfinished-task-history-query: "<yellow>Tâche inachevée (requête d’historique) : <white>{0}</white>"
  restart-database-manager-reconnect: "<yellow>Relancement de SQLManager via la séquence initiale (via l’exécuteur asynchrone)"
  restart-database-manager-done: "<green>Terminé !"
  property-incorrect: "<yellow>Vous devez entrer une (et une seule) propriété key=value set. Par exemple aaa=bbb"
  property-security-block: "<red>La demande a été rejetée, pour des raisons de sécurité, vous ne pouvez modifier que la propriété qui commence par <aqua>com.ghostchu.quickshop</aqua> ou <aqua>Achat rapide</aqua>."
  property-removed: "<green>Clé de propriété supprimée <white>{0}</white>"
  property-changed: "<green>Clé de propriété <white>{0}</white> a été remplacé par <white>{1}</white> À <white>{2}</white>"
  marked-as-dirty: "<green>Marqué tous les magasins comme étant en mauvais état, ils seront forcés à enregistrer dans la prochaine tâche de sauvegarde automatique. (Redémarrez le serveur pour forcer l’exécution de la tâche d’enregistrement du magasin)"
  display-removed: "<green>Supprimé avec succès <yellow>{0}</yellow> QuickShop affiche les éléments/entités des mondes."
database:
  scanning: <green>Scanning the isolated data in QuickShop databases, the database load may increase in scan progress, this might need a while...
  scanning-async: <yellow>Scanning the isolated data in QuickShop databases on async task thread, the database load may increase in scan progress, this might need a while, try again later.
  already-scanning: <red>An scan task has been started, please wait until it finished.
  trim-warning: '<yellow><red><bold>Warning: </red>Backup your database before continue database trim to avoid the data loss. Once you get ready, execute <aqua>/qs database trim confirm</aqua> to continue.'
  status: '<yellow>Statut : {0}'
  status-good: <green>Bon
  status-bad: <yellow>Maintenance Required
  isolated: '<yellow>Données isolées :'
  isolated-data-ids: '<yellow><aqua>└</aqua> Data Records: <gold>{0}</gold>'
  isolated-shop-ids: '<yellow><aqua>└</aqua> Shop Indexes: <gold>{0}</gold>'
  isolated-logs: '<yellow><aqua>└</aqua> Logs: <gold>{0}</gold>'
  isolated-external-caches: '<yellow><aqua>└</aqua> External Caches: <gold>{0}</gold>'
  last-purge-time: <yellow>Dernière coupe à {0}
  report-time: <yellow>Dernière heure de l’analyse à {0}
  auto-scan-alert: <yellow>The QuickShop database on this server require a maintenance. Found <gold>{0}</gold> isolated data wait for trimming.
  auto-trim: <green>Auto trim has been enabled on this server, no manually trim required.
  trim-complete: <green>Database trim completed, <yellow>{0}</yellow> isolated trimmed.
  auto-trim-started: <green>Auto trim has been started, please wait...
  trim-start: <green>Database trim started, please wait...
  trim-exception: <red>Database trim failed, an exception raised during database trimming, check the server console.
  generated-at: '<yellow>Generated at: <gold>{0}</gold>'
  purge-date: <red>Vous devez donner une date.
  purge-warning: <yellow>This operation will purge the history that stored in your database including shop creates/changes/deletes, purchases, transactions and system logs. Delete those data can free the disk space but all history metric will lost, and other plugins that depend on metrics will stop working. To continue execute this operation, execute `/qs database purgelogs <before-days> confirm`
  purge-task-created: <green>Tâche créée ! La base de données purge silencieusement des enregistrements d’historique en arrière-plan.
  purge-done-with-line: <green>Purge tâche terminée, total <gold>{0}</gold> lignes purgées de la base de données.
  purge-done-with-error: <red>La purge de la tâche a échoué, consultez la console du serveur pour plus de détails.
  purge-players-cache: <yellow>Veuillez patienter, purger les caches des joueurs...
  purge-players-completed: |-
    <green>Successfully purged {0} players caches from both memory and database.<newline><aqua>Note: You server performance may impact by this operation.
  purge-players-error: <red>Impossible de purger les caches des lecteurs, veuillez vérifier la console du serveur.
  suggestion:
    trim: <yellow>This database require a isolated data trim, execute command <aqua>/qs database trim</aqua> to trim the database.
always-counting-removal-early-warning: <red>Always Counting feature is scheduled for removal, you shouldn't use it anymore and will stop work in future.
exporting-database: <green>Exportation de la base de données, veuillez patienter...
exporting-failed: <red>Impossible d'exporter la base de données, veuillez vérifier la console du serveur.
exported-database: <green>Base de données exportée vers <yellow>{0}</yellow>.
importing-not-found: <red>The <yellow>{0}</yellow> file not found, please check the file path.
importing-early-warning: |-
  <yellow><red><bold>Warning: </bold></red>The backup will be imported to the current database, any exists data will be purged and permanently lost if you don't have a backup.<newline><red>Are you sure to continue the import progress?</red><yellow>Type <aqua>/qs recovery confirm</aqua> to continue.
importing-database: <green>Importation de la base de données depuis la sauvegarde, veuillez patienter...
importing-failed: <red>Impossible d'importer la base de données, veuillez vérifier la console du serveur.
imported-database: <green>Base de données importée de <yellow>{0}</yellow>.
transfer-sent: <green>Shops transfer request sent to <yellow>{0}</yellow>.
transfer-request: <yellow>Player <aqua>{0}</aqua> requested to transfer their shops to you. Did you want accept the transfer?
transfer-single-request: <yellow>Le joueur <aqua>{0}</aqua> aimerait vous transférer une boutique. Voulez-vous accepter la demande ?
transfer-ask: |-
  <gold>Type <red>/qs transfer accept</red> to accept.</gold><newline><gold>Type <red>/qs transfer deny</red> to deny.</gold><newline><gold>Request will expire after <red>{0}</red> seconds.
transferall-ask: |-
  <gold>Tapez <red>/quickshop transferall accept</red> pour accepter ou <red>/quickshop transferall deny</red> pour refuser.
  La demande expirera après <red>{0}</red> secondes.
transfer-single-ask: |-
  <gold>Tapez <red>/quickshop transferownership accept</red> pour accepter ou <red>/quickshop transferownership deny</red> pour refuser.
  La demande expirera après <red>{0}</red> secondes.
transfer-accepted-fromside: <green>Player <aqua>{0}</aqua> accepted your shop transfer request.
transfer-accepted-toside: <green>You accepted <aqua>{0}</aqua>'s transfer request.
transfer-rejected-fromside: <red>Player <aqua>{0}</aqua> rejected your shop transfer request.
transfer-rejected-toside: <red>You rejected <aqua>{0}</aqua>'s shop transfer request.
transfer-no-pending-operation: <red>You don't have pending transfer request yet.
transfer-no-self: <red>Vous ne pouvez pas transférer vos boutiques à vous-même.
benefit-overflow: <red>La somme de tous les bénéfices ne peut être supérieure à 100 %.
benefit-exists: <red>Ce joueur est déjà bénéficiaire de cette boutique.
benefit-removed: <red>Ce joueur a été supprimé des bénéficiaires de cette boutique.
benefit-added: <green>Le joueur <aqua>{0}</aqua> a été ajouté aux bénéficiaires de la boutique !
benefit-updated: <green>Les bénéfices de <aqua>{0}</aqua> ont été mis à jour !
benefit-query: <green>Cette boutique a <yellow>{0}</yellow> joueurs dans la liste des bénéficiaires !
benefit-query-list: <yellow> - </yellow><white>Player <gold>{0}</gold>, Benefit <gold>{1}%</gold></white>
tag-added: '<aqua>#{0}</aqua> <green>a été ajouté à cette boutique !'
tag-add-duplicate: '<red>The tag <aqua>#{0}</aqua> already exists at this shop!'
tag-removed: '<green>Successfully removed <aqua>#{0}</aqua> from this shop!'
tag-remove-not-exists: 'The tag <aqua>#{0}</aqua> not exists at this shop!'
tag-cleared: <green>Successfully cleared all tags from this shop!
tag-shops-cleared: '<green>Successfully cleared <aqua>#{0}</aqua> from all your tagged shops!'
tag-query: '<green>This shop have <yellow>{0}</yellow> tags:'
tag-query-listing: '<yellow> - <aqua>#{0}</aqua>'
tag-query-no-tag: <red>This shop have no tags.
tag-query-shops: '<green>This tag contains <yellow>{0}</yellow> shops:'
tag-query-shops-listing: <yellow> - <aqua>{0}</aqua>
batch-operations-based-on-tags-no-failure: <green>Successfully batch processed <yellow>{0}</yellow> shops.
batch-operations-based-on-tags-have-failure: <yellow>Total <yellow>{0}</yellow> shops in batch processing successfully completed, but <red>{1}</red> requests not completed.
batch-operations-based-on-tags-have-failure-with-reason: '<yellow>Total <yellow>{0}</yellow> shops in batch processing successfully completed, but <red>{1}</red> requests not completed. Reason: <gold>{2}</gold>'
batch-operations-based-on-tags-have-failure-error-messages-may-appears-above: <gray>The error messages will send to you above this message in the chat.
addon:
  towny:
    commands:
      town: <yellow>Set or unset a shop to a Town shop
      nation: <yellow>Set or unset a shop to a Nation shop
    make-shop-owned-by-town: <green>You have made the shop owned by town <yellow>{0}</yellow>.
    make-shop-no-longer-owned-by-town: <green>You have reset shop ownership, it now transfer back to original shop owner.
    make-shop-owned-by-nation: <green>You have made the shop owned by nation <yellow>{0}</yellow>.
    make-shop-no-longer-owned-by-nation: <green>You have reset shop ownership, it now transfer back to original shop owner.
    shop-owning-changing-notice: <grey>This shop now owned by a town/nation, original shop owner has been automatically added into administrator list, modify or add any new owner / staff by using command /qs permission
    target-shop-already-is-town-shop: <red>The target shop is already owned by a town.
    target-shop-already-is-nation-shop: <red>The target shop is already owned by a nation.
    target-shop-not-in-town-region: <red>The target shop is not in the town.
    target-shop-not-in-nation-region: <red>The target shop is not in the nation.
    item-not-allowed: <red>This shop's item are not allowed used by town/nation shop, pick another one!
    operation-disabled-due-shop-status: <red>This shop operation has been disabled due this already is a town/nation shop.
    plot-type-disallowed: <red>You can't create town/nation shop on this type of plot.
    flags:
      own: <red>You can only create shops at your owned shop type towny plot.
      modify: <red>You didn't have build permission on this towny plot.
      shop-type: <red>You must create shop on a shop type towny plot.
  residence:
    creation-flag-denied: <red>You don't have permission to create shops in this residence.
    trade-flag-denied: <red>You don't have permission to purchase in this residence.
    you-cannot-create-shop-in-wildness: <red>You can't create shop in wildness.
  griefprevention:
    creation-denied: <red>You don't have permission to create shops in this claim.
    trade-denied: <red>You don't have permission to purchase shops in this claim.
  lands:
    world-not-enabled: <red>You are not allowed to create or purchase in this world.
    creation-denied: <red>You don't have permission to create shops in this Land area.
  plotsquared:
    no-plot-whitelist-creation: <red>You cannot create shop outside of Plot.
    no-plot-whitelist-trade: <red>You cannot purchase shop outside of Plot.
    creation-denied: <red>You don't have permission to create shops in this Plot.
    trade-denied: <red>You don't have permission to purchase shops in this Plot.
    flag:
      create: Create QuickShop-Hikari shops
      trade: Purchase QuickShop-Hikari shops
  superiorskyblock:
    owner-create-only: <red>Only island owner can create shop there.
    owner-member-create-only: <red>Only island owner or member can create shop there.
  worldguard:
    creation-flag-test-failed: <red>You don't have permission create shop in this WorldGuard area.
    trade-flag-test-failed: <red>You don't have permission trade shop in this WorldGuard area.
    reached-per-region-amount-limit: "<red>You have reached the maximum amount of shops in this region."
  discord:
    __to_translators: 'To translators: `addon.discord.discord-messages` use a special variable in strings, %%LANG_KEY%% instead of QuickShop''s style {LANG_INDEX}, do not translate anything inside double ''%'' section. BUT for any other section texts, it respect the normal QuickShop Text System syntax ({0},{1} etc.) This may be difficult to understand because two different variable forms are used in the same program at the same time, but trust me, it is reasonable. (you don''t need translate this string, it''s a comment)'
    __to_users: Like the QuickShop Text System, Discord addon will also auto-detect user language and use user's language to send Discord message, it following the QuickShop-Hikari's language system settings.
    __to_message_designer: 'Design your Discord message with GUI: https://glitchii.github.io/embedbuilder/, then copy the JSON code and paste it into the translation and here we go!'
    discord-enabled: <aqua>Successfully <green>enabled</green> your QuickShop discord messages, now you can receive the shop messages from Discord.
    discord-disabled: <aqua>Successfully <red>disabled</red> your QuickShop discord messages, now you won't receive the shop messages from Discord anymore.
    discord-not-integrated: <red>You hadn't link your Discord yet! Please link your Discord account first!
    feature-enabled-for-user: <aqua>You have <green>enabled</green> <gold>{0}</gold> notification.
    feature-disabled-for-user: <aqua>You have <red>disabled</red> <gold>{0}</gold> notification.
    link-help: <yellow>This server using <gold>{0}</gold> for Discord driver, please use <green>{0}</green> to link your Discord account.
    save-notifaction-exception: <red>An error occurred while saving your Discord notifaction settings, please contact the server administrator.
    feature-status-changed: <green>Successfully set the notifaction <aqua>{0}</aqua> status to <gold>{1}</gold>
    commands:
      discord:
        description: <yellow>Managing your QuickShop Discord settings
    discord-messages:
      sold-to-your-shop: |
        {
         "embed":
           {
             "title": ":inbox_tray: Somebody sold items to your shop",
             "description": "The player %%purchase.name%% sold x%%purchase.amount%% %%shop.item.name%% to your shop.",
             "color": 52084,
             "author": {
               "name": "QuickShop-Hikari",
               "icon_url": ""
             },
             "thumbnail": {
               "url": ""
             },
             "image": {
               "url": ""
             },
             "footer": {
               "text": "QuickShop-Hikari Discord Notification",
               "icon_url": ""
             },
             "fields": [
               {
                 "name": "Shop",
                 "value": "%%shop.display-name%%",
                 "inline": false
               },
               {
                 "name": "Purchaser",
                 "value": "%%purchase.name%%",
                 "inline": true
               },
               {
                 "name": "Item",
                 "value": "%%shop.item.name%%",
                 "inline": true
               },
               {
                 "name": "Amount",
                 "value": "%%purchase.amount%% (x%%shop.stacking-amount%%)",
                 "inline": true
               },
               {
                 "name": "Your paid",
                 "value": "%%purchase.balance-formatted%%",
                 "inline": true
               },
               {
                 "name": "Taxes",
                 "value": "%%purchase.taxes-formatted%%",
                 "inline": true
               }
             ]
           }
        }
      bought-from-your-shop: |
        {
           "embed":
             {
               "title": ":outbox_tray: Somebody bought items from shop",
               "description": "The player %%purchase.name%% bought x%%purchase.amount%% %%shop.item.name%% from your shop.",
               "color": 52084,
               "author": {
                   "name": "QuickShop-Hikari",
                   "icon_url": ""
               },
               "thumbnail": {
                   "url": ""
               },
               "image": {
                   "url": ""
               },
               "footer": {
                   "text": "QuickShop-Hikari Discord Notification",
                   "icon_url": ""
               },
               "fields": [
                   {
                       "name": "Shop",
                       "value": "%%shop.display-name%%",
                       "inline": false
                   },
                   {
                       "name": "Purchaser",
                       "value": "%%purchase.name%%",
                       "inline": true
                   },
                   {
                       "name": "Item",
                       "value": "%%shop.item.name%%",
                       "inline": true
                   },
                   {
                       "name": "Amount",
                       "value": "%%purchase.amount%%(x%%shop.stacking-amount%%)",
                       "inline": true
                   },
                   {
                       "name": "Your earn",
                       "value": "%%purchase.balance-formatted%%",
                       "inline": true
                   },
                   {
                       "name": "Taxes",
                       "value": "%%purchase.taxes-formatted%%",
                       "inline": true
                   }
               ]
           }
         }
      mod-shop-purchase: |
        {
        "embed":
          {
              "title": ":dollar: Somebody purchased in a shop",
              "description": "The player %%purchase.name%% purchase x%%purchase.amount%% %%shop.item.name%% from the shop.",
              "color": 6061450,
              "author": {
                  "name": "QuickShop-Hikari",
                  "icon_url": ""
              },
              "thumbnail": {
                  "url": ""
              },
              "image": {
                  "url": ""
              },
              "footer": {
                  "text": "QuickShop-Hikari Discord Notification",
                  "icon_url": ""
              },
              "fields": [
                  {
                      "name": "Shop",
                      "value": "%%shop.display-name%%",
                      "inline": false
                  },
                  {
                      "name": "Purchaser",
                      "value": "%%purchase.name%%",
                      "inline": true
                  },
                  {
                      "name": "Item",
                      "value": "%%shop.item.name%%",
                      "inline": true
                  },
                  {
                      "name": "Amount",
                      "value": "%%purchase.amount%% (x%%shop.stacking-amount%%)",
                      "inline": true
                  },
                  {
                      "name": "Balance",
                      "value": "%%purchase.balance-formatted%%",
                      "inline": true
                  },
                  {
                      "name": "Taxes",
                      "value": "%%purchase.taxes-formatted%%",
                      "inline": true
                  }
              ]
          }
        }
      out-of-space: |
        {
        "embed":
          {
              "title": ":mailbox_with_mail: Your shop has run out of space",
              "description": "Your shop inventory is full of items!\nYou need empty your shop container to free the space then you can continue to accept new items.",
              "color": 16065893,
              "author": {
                  "name": "QuickShop-Hikari",
                  "icon_url": ""
              },
              "thumbnail": {
                  "url": ""
              },
              "image": {
                  "url": ""
              },
              "footer": {
                  "text": "QuickShop-Hikari Discord Notification",
                  "icon_url": ""
              },
              "fields": [
                  {
                      "name": "Shop",
                      "value": "%%shop.display-name%%",
                      "inline": false
                  }
              ]
          }
        }
      out-of-stock: |
        {
            "embed":
                {
                    "title": ":mailbox_with_no_mail: Your shop has run out of stock",
                    "description": "Your shop inventory is empty now!\nYou need to refill the shop container with items to continue selling items!",
                    "color": 16065893,
                    "author": {
                        "name": "QuickShop-Hikari",
                        "icon_url": ""
                    },
                    "thumbnail": {
                        "url": ""
                    },
                    "image": {
                        "url": ""
                    },
                    "footer": {
                        "text": "QuickShop-Hikari Discord Notification",
                        "icon_url": ""
                    },
                    "fields": [
                        {
                            "name": "Shop",
                            "value": "%%shop.display-name%%",
                            "inline": false
                        }
                    ]
                }
        }
      mod-shop-created: |
        {
          "embed": {
            "title": ":classical_building: A new shop has been created",
            "description": "Player created a new shop on your server!",
            "color": 6061450,
            "author": {
              "name": "QuickShop-Hikari",
              "icon_url": ""
            },
            "thumbnail": {
              "url": ""
            },
            "image": {
              "url": ""
            },
            "footer": {
              "text": "QuickShop-Hikari Discord Notification",
              "icon_url": ""
            },
            "fields": [
              {
                "name": "Shop",
                "value": "%%shop.display-name%%",
                "inline": false
              },
              {
                "name": "Owner",
                "value": "%%shop.owner.name%%",
                "inline": true
              },
              {
                "name": "Item",
                "value": "%%shop.item.name%%",
                "inline": true
              },
              {
                "name": "Amount",
                "value": "%%shop.stacking-amount%%",
                "inline": true
              },
              {
                "name": "Type",
                "value": "%%shop.type%%",
                "inline": true
              }
            ]
          }
        }
      mod-remove-shop: |
        {
            "embed": {
                "title": ":recycle: A shop was removed from this server",
                "description": "A shop was removed from this server.",
                "color": 6061450,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "Reason",
                        "value": "%%delete.reason%%",
                        "inline": false
                    }
                ]
            }
        }
      shop-transfer-to-you: |
        {
            "embed": {
                "title": ":placard: A shop was transfer to you",
                "description": "A shop transfer to you from another player.",
                "color": 16172079,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "From",
                        "value": "%%transfer.from%%",
                        "inline": false
                    }
                ]
            }
        }
      mod-shop-transfer: |
        {
            "embed": {
                "title": ":placard: A shop was transferred",
                "description": "A shop transfer to a player from another player.",
                "color": 6061450,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "From",
                        "value": "%%transfer.from%%",
                        "inline": true
                    },
                    {
                        "name": "To",
                        "value": "%%transfer.to%%",
                        "inline": true
                    }
                ]
            }
        }
      shop-price-changed: |
        {
            "embed": {
                "title": ":money_with_wings: Your shop price was changed",
                "description": "You or your shop staff changed your shop price.",
                "color": 16172079,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "From",
                        "value": "%%change-price.from%%",
                        "inline": true
                    },
                    {
                        "name": "To",
                        "value": "%%change-price.to%%",
                        "inline": true
                    }
                ]
            }
        }
      mod-shop-price-changed: |
        {
            "embed": {
                "title": ":money_with_wings: A shop price has been changed",
                "description": "A shop changed its price settings.",
                "color": 6061450,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": true
                    },
                    {
                        "name": "Owner",
                        "value": "%%shop.owner.name%%",
                        "inline": true
                    },
                    {
                        "name": "",
                        "value": "",
                        "inline": true
                    },
                    {
                        "name": "From",
                        "value": "%%change-price.from%%",
                        "inline": true
                    },
                    {
                        "name": "To",
                        "value": "%%change-price.to%%",
                        "inline": true
                    }
                ]
            }
        }
      shop-permission-changed: |
        {
            "embed": {
                "title": ":closed_lock_with_key: Your shop permission settings has been changed",
                "description": "One of your shop's permission setting has been changed.",
                "color": 15879747,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "Player",
                        "value": "%%change-permission.player%%",
                        "inline": true
                    },
                    {
                        "name": "Assign to group",
                        "value": "%%change-permission.to-group%%",
                        "inline": true
                    },
                    {
                        "name": "",
                        "value": "",
                        "inline": true
                    },
                    {
                        "name": "Permission granted (Inherit from group)",
                        "value": "```\n%%change-permission.perms-list%%\n```",
                        "inline": false
                    }
                ]
            }
        }
    field:
      player: Player
      item: Item
      amount: Amount
      balance: Balance
      balance-after-tax: Balance (after tax)
      account: Your account balance
      taxes: Taxes
      cost: Cost
  discount:
    commands:
      discount:
        description: <yellow>Apply a discount code or manage your discount codes.
    tab-complete:
      discount:
        general:
          code: <code>
        create:
          rate: |
            Command Hint:
            Argument: <rate>
            Description: The actual percentage or money you will earn
            Input `30%` = price * 0.3
            Input `50` = price - 50
          max-usage: |
            Command Hint:
            Argument: [max-usage]
            Description: The times that code can be used
            `-1` for unlimited.
          threshold: |
            Command Hint:
            Argument: [threshold]
            Description: The minimum price that code can be applied
            `-1` for unlimited
          expired: |
            Command Hint
            Argument: [expired]
            Description: The code expired time.
            `-1` for unlimited duration.
            Accept both Zulu time and UNIX timestamp in seconds.
            Zulu Example: 2022-12-17T10:31:37Z
            UNIX Example: 1671273097
    discount-code-already-exists: <red>Sorry, the name of your discount code already in use.
    invalid-discount-code-regex: '<red>The discount code must matches the regex: <yellow>{0}</yellow>'
    invalid-discount-code: <red>This discount code are invalid.
    discount-code-added: <green>Your discount code <yellow>{0}</yellow> has been added into shop <aqua>{1}</aqua>
    discount-code-removed: <green>Your discount code <yellow>{0}</yellow> has been removed from shop <aqua>{1}</aqua>
    invalid-code-type: <red>The code type <yellow>{0}</yellow> are invalid.
    invalid-usage-restriction: <red>The usage restriction <yellow>{0}</yellow> are invalid.
    invalid-threshold-restriction: <red>The threshold restriction <yellow>{0}</yellow> are invalid.
    invalid-effect-scope: <red>The scope <yellow>{0}</yellow> are invalid.
    invalid-expire-time: <red>You can't specify a time in the past.
    invalid-discount-rate: <red>The discount rate <yellow>{0}</yellow> are invalid, it can be a fixed number or percentage.
    discount-code-expired: <red>Ouch! Your discount code <yellow>{0}</yellow> has been expired!
    discount-code-installed: <green>You have installed discount code <gold>{0}</gold>, the discount code will be automatically applied to the available purchase in all the following purchases during this session. To uninstall discount code, execute <aqua>/qs discount uninstall {0}</aqua>.
    discount-code-uninstalled: <green>You have uninstalled your discount code.
    discount-code-query-nothing: <red>You hadn't install a discount code yet!
    discount-code-query: '<green>You are using discount code: <yellow>{0}</yellow>.'
    discount-code-applicable: '<color:#bcef26>Your discount code <yellow><bold>{0}</bold></yellow> are <bold>applicable</bold> in this shop!'
    discount-code-applicable-with-threshold: '<color:#bcef26>Your discount code <yellow><bold>{0}</bold></yellow> are <bold>applicable</bold> in this shop, but only your purchase cost over <yellow>{1}</yellow> in single transaction.'
    discount-code-not-applicable: <red>Your discount code <yellow><bold>{0}</bold></yellow> are <bold>not applicable</bold> in this shop!
    discount-code-reach-the-limit: <red>You have reach the usage limit of discount code <yellow><bold>{0}</bold></yellow>, discount are not applied.
    discount-code-no-permission: <red>You didn't have permission to use any discount code in this shop!
    discount-code-has-been-expired: <red>Your discount has been expired!
    discount-code-config-shop-added: <green>Successfully to add this shop into your discount code allow list.
    discount-code-config-shop-add-failure: <red>This shop already in your discount code allow list.
    discount-code-config-shop-removed: <green>Successfully to remove this shop from your discount code allow list.
    discount-code-config-shop-remove-failure: <red>This shop not exists in your discount code allow list.
    discount-code-config-expire: <green>Successfully changed your code expire time.
    discount-code-config-applied: <green>Config the discount code successfully!
    discount-code-created-successfully: |
      <green>Your discount code <yellow>{0}</yellow> has been successfully created!
      <gold>Scope: <aqua>{1}</aqua></gold>.
      <gold>Share with others: <color:#bcef26>{2}</color:#bcef26></gold>.
      <yellow>For <gray>Specific Shops</gray> scope only: To add shops to your discount code allow list, please looking an quickshop and execute <aqua>{3}</aqua> command.
      <yellow>You can use <aqua>/qs discount config {0}</aqua> to edit your discount code anytime.
    discount-code-under-threshold: <red>The discount not applied to your purchase because the total value under the discount code threshold <yellow>{0}</yellow>.
    percentage-off: '<bold><color:#bcef26>-{0}%</color:#bcef26></bold>'
    fixed-off: '<bold><color:#bcef26>-{0}</color:#bcef26></bold>'
    discount-code-list: '<gold>Listing your discount codes:</gold>'
    discount-code-applied-in-purchase: '<color:#bcef26>Your discount code <yellow>{0}</yellow> has been applied to this purchase and you saved <gold>{1}</gold>!'
    scope:
      this-shop: '{0}'
      your-shops-owned: all your shops (owned)
      your-shops-managed: all your shops (managed)
      server: whole server
    code-type:
      SERVER_ALL_SHOPS: All shops on this server
      PLAYER_ALL_SHOPS: All shops that created by code owner
      SPECIFIC_SHOPS: Specific shops
    discount-code-details: |-
      <gold>Code: <yellow>{0}</yellow>
      <gold>Creator: <yellow>{1}</yellow>
      <gold>Applied to: <yellow>{2}</yellow>
      <gold>Remaining usage: <yellow>{3}</yellow>
      <gold>Expired on: <yellow>{4}</yellow>
      <gold>Threshold: <yellow>{5}</yellow>
      <gold>Discount: <yellow>{6}</yellow>
  list:
    commands:
      list: <yellow>Listing all shops owned by your or specific player
    table-prefix: '<yellow><green>{0}</green>''s shops <gray>(Total {1})</gray>: '
    table-prefix-pageable: '<yellow><green>{0}</green>''s shops <gray>(Page {1}/{2})</gray>: '
    entry: "<hover:show_text:'<yellow>{1}</yellow>\n<gray>{2} {3},{4},{5}</gray>\n<aqua>Price <yellow>{6}</yellow> - <light_purple>x{7}</light_purple> <yellow>{8}</yellow>\n<green>{9}'><yellow>{0}. <aqua>{1}</aqua></hover>"
  shopitemonly:
    message: <red>You can't put non-shop items into shop container, all non-shop items will dropped at your location.
  limited:
    commands:
      limit: <yellow>Set a limit that restrict the purchase that player can make in period
    titles:
      title: <green>Purchase Success
      subtitle: <aqua>You can also purchase <gold>{0}</gold> more in this shop
    reach-the-limit: <red>You have reached the limit in this shop. You can also purchase <green>{0}</green> items, but you trying purchase <yellow>{1}</yellow> items.
    success-reset: <green>Reset the restriction of this shop successfully
    success-remove: <green>Successfully remove the all restriction of this shop
    success-setup: <green>Restriction settings for this shop were saved successfully
    trade-limit-reached-cancel-reason: <red>Reached the restriction of this shop
    remains-limits: '<gold>Your remaining purchase quota in this shop: <yellow>{0}</yellow>'
    remains-limits-before-reset-period-note: '<gold>This shop will reset their purchase quota every: <yellow>{0}</yellow>'
  dynmap:
    markerset-title: QuickShop-Hikari Shops
    marker-name: '{0} @ {2} @ {3} for x{4} @ {5}'
    marker-description: |
      Name: {0}
      Owner: {1}
      Item: {2}
      Price: {3} for x{4} item(s)
      Type: {5}
      Unlimited: {6}
      Location: {7}
  bluemap:
    markerset-title: QuickShop-Hikari Shops
    marker-name: '{0} @ {2} @ {3} for x{4} @ {5}'
    marker-description: |
      Name: {0}
      Owner: {1}
      Item: {2}
      Price: {3} for x{4} item(s)
      Type: {5}
      Unlimited: {6}
      Location: {7}
  chestprotect:
    protection-exists: <red>This region already protected by ChestProtect and you don't have permission to create shops at here.
    shops-exsts: <red>The region that you want to protect contains other player's shops and you don't have permission to access them.
  displaycontrol:
    toggle: |-
      <green>Successfully toggle your QuickShop display showcasing to <aqua>{0}</aqua>.
      <yellow>You may need re-join to take effect.
    toggle-exception: <red>Failed to toggle your display showcase settings due an internal error, please contact server administrator.
    command:
      displaycontrol: <yellow>Toggle your QuickShop display showcasing
  reremake-migrator:
    commands:
      migratefromreremake: Migrate QuickShop-Reremake's data to QuickShop-Hikari
    server-not-empty: "<red>No players are allowed to be online on the server during the conversion progress, please enable server whitelist or maintenance mode."
    starting-convert-progress: "<gold>Starting the convert progress, <red>DO NOT SHUTDOWN YOUR SERVER!</red>"
    executing: "<gold>Executing migrate component <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
    completed: "<green>Done! Migrate completed, please restart your server and remove QuickShop-Reremake from plugins directory."
    join_blocking_converting: "<red>[QuickShop-Hikari Migrator] This server is performing a data conversion and you cannot join the server at this time, try again later!"
    join_blocking_finished: "<red>[QuickShop-Hikari Migrator] This server has just completed a data conversion and is waiting for a reboot to apply the changes, try again later!"
    failed: "<red>Migration progress exited with error, please check the server console."
    modules:
      config:
        copy-values: "<yellow>Copying values (total {0} entries)..."
        copying-value: "<gray> - Copying <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        migrate-price-restriction: "<yellow>Migrating price-restriction related settings..."
        migrate-price-restriction-entry: "<gray> - Migrating <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
      shop:
        start-migrate: "<yellow>Migrating shops (total {0} entries)..."
        migrate-entry: "<gray> - Migrating <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        unloading-reremake: "<gold>Unloading Reremake to avoid data overwrite..."
        register-entry: "<gray> - Registering <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        save-entry: "<gray> - Saving <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        saving-shops: "<yellow>Saving <gold>{0}</gold> shops, this may need a while™ (the time based on shops amounts)..."
        conflict: "<gray> - CONFLICT > Detects a conflict between a Reremake store and an existing Hikari store location, taking a predefined behavior: <dark_gray>{0}</dark_gray>"
      translation:
        start-migrate: "<yellow>Migrating translation files...</yellow>"
        copy-values: "<yellow>Copying values in locale <gold>{0}</gold> (total {1} entries)..."
        migrate-entry: "<gray> - Migrating <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        copying-value: "<gray> - Copying <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
      shop-logs:
        start-migrate: "<yellow>Migrating shop logs (details in console), please wait...</yellow>"
        extract-history-files: "<gray>Please wait for us to unzip and append the history logs..."
        filter-history-files: "<gray>Please wait for us to filter the history log..."
        filtered-history-files: "<gray>Filtered {0} lines out of queue."
        import-entry: "<gray> - Migrating <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
compat:
  advancedchests:
    created: <green>You're successfully created a shop on AdvancedChests container!
    permission-denied: <red>Sorry! You don't have the permission to create a shop on a AdvancedChests container!
suite:
  dynmap:
    title: '{0} - {1} - {2}'
    description: |-
      Item: {0}
      Owner: {1}
      Type: {2} {3}
      Price: {4}
      Location: {5} {6}, {7}, {8}
      Space: {9}
      Stock: {10}
  limited:
    command-description: <yellow>Set a limit that restrict the purchase that player can make in period.
    reach-the-quota-limit: <red>You have reach the purchase limit in this shop ({0}/{1}).
    quota-reset-countdown: <yellow>The quota in this shop will reset in {0}.
    quota-reset-player-successfully: <green>The quota for player {0} in this shop has been reset successfully.
    quota-reset-everybody-successfully: <green>The quota for everyone in this shop has been reset successfully.
    quota-setup: <green>The purchase restriction now apply to this shop!
    quota-remove: <green>The purchase restriction now removed from this shop!
    subtitles:
      title: <green>Purchase Success
      subtitle: <aqua>You can purchase <yellow>{0}</yellow> more in this shop
  list:
    command-description: <yellow>List all shops owned by yourself or another player.
    table-prefix: <yellow>You owned total <aqua>{0}</aqua> shops on this server.
    entry-syntax: <aqua>- <yellow>{0}
    entry-syntax-fallback-name: '<yellow>Item:{0} X:{1}, Y:{2}, Z:{3}, World: {4}'
    hover-text: |-
      <yellow>{0}
      <gray>{1} {2}, {3}, {4}
      <aqua>{5} - <yellow>{6}
      <yellow>{7} items each stack.
  shopitemonly:
    message: <red>You can't put non-shop items into shop container, all non-shop items will dropped at your location.
compatibility:
  elitemobs:
    soulbound-disallowed: You can't trade item which have EliteMobs Soulbound enchantment.
internet-paste-forbidden-privacy-reason: "<red>Failed! According your privacy setting, QuickShop-Hikari are not allowed upload your paste to Internet, turn on DIAGNOSTIC permission in privacy setting in config.yml or use <aqua>/quickshop paste --file</aqua> instead."
no-sign-type-given: "<red>You need give a sign material, the sign materials that available on this server: {0}"
sign-type-invalid: "<red>The type <yellow>{0}</yellow> not a valid sign material."
delete-controlpanel-button-confirm: "<red>Do you really want remove this shop? Click the <bold>[Remove Shop]</bold> button again in {0} seconds to confirm."
cannot-suggest-price: "<red>Sorry, For the time being, no more people are trading the same item as you and there is not enough data to generate a suggested price."
price-suggest: "<green>Based on data from <aqua>{0}</aqua> stores, the highest priced store is priced at <light_purple>{1}</light_purple>, the lowest priced store is priced at <light_purple>{2}</light_purple>, the average price is <light_purple>{3}</light_purple>, and the median price is <light_purple>{4}</light_purple>. <newline><yellow>It's recommended that you set your price around <gold>{5}</gold>.</yellow>"
suggest-wait: "<green>Please wait... Calculating recommend price."
history:
  shop:
    gui-title: "View purchase history"
    header-icon-multiple-shop: "<white>The query result across {0} shops</white>"
    header-icon-description:
      - "<white>Type: <yellow>{0}</yellow></white>"
      - "<white>Owner: <yellow>{1}</yellow></white>"
      - "<white>Item: <yellow>{2}</yellow></white>"
      - "<white>Price: <yellow>{3} <aqua>x{4}</aqua></yellow></white>"
      - "<white>Location: <yellow>{5}</yellow></white>"
    log-icon-title: "<reset><green>Time: {0}</green>"
    log-icon-description:
      - "<white>Purchaser: <yellow>{0}</yellow></white>"
      - "<white>Item: <yellow>{1} <aqua>x{2}</aqua></yellow></white>"
      - "<white>Balance: <yellow>{3}</yellow></white>"
      - "<white>Tax: <yellow>{4}</yellow></white>"
    log-icon-description-with-store-name:
      - "<white>Store: <yellow>{0}</yellow></white>"
      - "<white>Purchaser: <yellow>{1}</yellow></white>"
      - "<white>Item: <yellow>{2} <aqua>x{3}</aqua></yellow></white>"
      - "<white>Balance: <yellow>{4}</yellow></white>"
      - "<white>Tax: <yellow>{5}</yellow></white>"
    query-icon: "<gray>Please wait, querying...</gray>"
    previous-page: "<white><< Previous Page</white>"
    next-page: "<white>Next Page >></white>"
    current-page: "<white>Page {0}</white>"
    summary-icon-title: "<green>Shop Summary"
    recent-purchases: "<white>Recent <aqua>{0}</aqua> purchases: <yellow>{1}</yellow></white>"
    recent-purchase-balance: "<white>Recent <aqua>{0}</aqua> turnover: <yellow>{1}</yellow></white>"
    total-purchases: "<white>Total purchases: <yellow>{0}</yellow></white>"
    total-balances: "<white>Total turnover: <yellow>{0}</yellow></white>"
    total-unique-purchasers: "<white>Total unique purchasers: <yellow>{0}</yellow></white>"
    top-n-valuable-customers-title: "<gold>Top {0} valuable customers</gold>"
    top-n-valuable-customers-entry: "<white>- <yellow>{0}</yellow> <aqua>{1}</aqua></white>"
    no-result: "<white>No result</white>"
about:
  text:
    - "<b><aqua>QuickShop <yellow>{0}</yellow></aqua></b>"
    - "<aqua>Version <yellow>>></yellow> <green>{1}</green></aqua>"
    - "<aqua>Release <yellow>>></yellow> <green>{2}</green></aqua>"
    - "<aqua>Developers <yellow>>></yellow> <color:#218bff><hover:show_text:'<yellow>Click to check contributors'><click:open_url:'https://github.com/Ghost-chu/QuickShop-Hikari/graphs/contributors'>[View contributors on GitHub]</click></hover></color></aqua>"
    - "<aqua>Localized members <gold>({4})</gold>: <yellow>>></yellow> <green>{5}</green></aqua>"
    - "  <yellow>+ <color:#43A047><hover:show_text:'<yellow>Click to open Crowdin translate page'><click:open_url:'https://crowdin.com/project/quickshop-hikari'>[Help translate on Crowdin]</click></hover></color></yellow>"
    - "<aqua>Donation Key <yellow>>></yellow> <green>{6}</green></aqua>"
    - "<gold>Powered by community</gold> <red>Made with ❤</red>"
  valid-donation-key: "<color:#00AFF1>Bound to <color:#F9C23C>{0}</color></color>"
  invalid-donation-key: "<gray>Invalid donation key</gra>"
  kofi-thanks: "<gold>Special thanks to those who support QuickShop on Ko-fi :)</gold>"
history-command-leave-blank: "<Leave blank for seeing store>"
shop-information-not-shown-due-an-internal-error: "<red>An internal error occurred. The store information panel may be displayed incompletely, please contact the server administrator."
