# https://github.com/NEZNAMY/TAB/wiki/Feature-guide:-Header-&-Footer
header-footer:
  enabled: true
  header:
    - <#FFFFFF>&m                                                </#FFFF00>
    - '&3&l服务器名称'
    - '&7&l>> %animation:Welcome%&3 &l%player%&7&l! &7&l<<'
    - '&7在线玩家: &f%online%'
    - '&6在线管理员: &e%staffonline%'
    - ''
  footer:
    - '%animation:time%'
    - '&2延迟: %ping%'
    - '&7&l 已用内存: %memory-used% MB / %memory-max% MB'
    - ''
    - '&7访问我们的网站 %animation:web%'
    - <#FFFFFF>&m                                                </#FFFF00>
  disable-condition: '%world%=disabledworld'
  per-world:
    world1:
      header:
        - 行尸のMC世界
      footer:
        - header/footer 和前缀/后缀的示例
    world2;world3:
      header:
        - 兴趣使然，不忘初心。
        - world2 和 world3
  per-server:
    server1:
      header:
        - 兴趣使然，不忘初心。 

# https://github.com/NEZNAMY/TAB/wiki/Feature-guide:-Tablist-name-formatting
tablist-name-formatting:
  enabled: true
  anti-override: true
  disable-condition: '%world%=disabledworld' 

# https://github.com/NEZNAMY/TAB/wiki/Feature-guide:-Nametags
scoreboard-teams:
  enabled: true
  enable-collision: true
  invisible-nametags: false
  anti-override: true 
  # https://github.com/NEZNAMY/TAB/wiki/Feature-guide:-Sorting-players-in-tablist
  sorting-types:
    - GROUPS:owner,admin,mod,helper,builder,vip,default
    - PLACEHOLDER_A_TO_Z:%player%
  case-sensitive-sorting: true
  can-see-friendly-invisibles: false
  disable-condition: '%world%=disabledworld' 

# https://github.com/NEZNAMY/TAB/wiki/Feature-guide:-Playerlist-Objective
playerlist-objective:
  enabled: true
  value: '%ping%'
  fancy-value: '&7延迟: %ping%'
  disable-condition: '%world%=disabledworld' 

# https://github.com/NEZNAMY/TAB/wiki/Feature-guide:-Belowname
belowname-objective:
  enabled: false
  value: '%health%'
  title: '&c生命值'
  fancy-value: '&c%health%'
  fancy-value-default: NPC
  disable-condition: '%world%=disabledworld' 

# https://github.com/NEZNAMY/TAB/wiki/Feature-guide:-Spectator-fix
prevent-spectator-effect:
  enabled: false 

# https://github.com/NEZNAMY/TAB/wiki/Feature-guide:-Bossbar
bossbar:
  enabled: false
  toggle-command: /bossbar
  remember-toggle-choice: false
  hidden-by-default: false
  bars:
    ServerInfo:
      style: PROGRESS  # for 1.9+: PROGRESS, NOTCHED_6, NOTCHED_10, NOTCHED_12, NOTCHED_20
      color: '%animation:barcolors%' # for 1.9+: BLUE, GREEN, PINK, PURPLE, RED, WHITE, YELLOW
      progress: '100' # in %
      text: '&f网站: &bzourou.top'

# https://github.com/NEZNAMY/TAB/wiki/Feature-guide:-Scoreboard
scoreboard:
  enabled: true
  toggle-command: /sb
  remember-toggle-choice: false
  hidden-by-default: false
  use-numbers: true
  static-number: 0
  delay-on-join-milliseconds: 0
  scoreboards:
    scoreboard:
      scoreboard-1.20.3+:
          title: "<#E0B11E>行尸のMC世界</#FF0000>"
          display-condition: '%player-version-id%>=765;%bedrock%=false'  # 仅对使用 1.20.3+ 且不是基岩版的玩家显示
          lines:
            - '&7%date%'
            - '%animation:MyAnimation1%'
            - '&6在线:'
            - '* &e在线&7:||%online%'
            - '* &e当前世界&7:||%worldonline%'
            - ''
            - '&6个人信息:'
            - '* &b等级&7:||%group%'
            - '* &b延迟&7:||%ping%&8ms'
            - '* &b世界&7:||%world%'
            - '%animation:MyAnimation1%'
    scoreboard:
      title: "<#E0B11E>TianServer</#FF0000>"
      lines:
        - '&7%date%'
        - '%animation:MyAnimation1%'
        - '&6在线:'
        - '* &e在线人数&7: &f%online%'
        - '* &e当前世界&7: &f%worldonline%'
        - ''
        - '&6个人信息:'
        - '* &b等级&7: &f%group%'
        - '* &b延迟&7: &f%ping%&8ms'
        - '* &b世界&7: &f%world%'
        - '%animation:MyAnimation1%' 

# https://github.com/NEZNAMY/TAB/wiki/Feature-guide:-Layout
layout:
  enabled: false
  direction: COLUMNS
  default-skin: mineskin:383747683
  enable-remaining-players-text: true
  remaining-players-text: '... 还有 %s 人'
  empty-slot-ping-value: 1000
  layouts:
    default:
      fixed-slots:
        - '1|&3网站&f:'
        - 2|&bzourou.top
        - '3|&8&m                       '
        - '4|&3姓名&f:'
        - 5|&b%player%
        - '7|&3等级&f:'
        - '8|等级: %group%'
        - '10|&3世界&f:'
        - 11|&b%world%
        - '13|&3时间&f:'
        - 14|&b%time%
        - '21|&3Teamspeak&f:'
        - 22|&bts.zourou.top
        - '23|&8&m                       '
        - '41|&3商店&f:'
        - 42|&bzourou.top
        - '43|&8&m                       '
      groups:
        staff:
          condition: permission:tab.staff
          slots:
            - 24-40
        players:
          slots:
            - 44-80 

# https://github.com/NEZNAMY/TAB/wiki/Feature-guide:-Ping-Spoof
ping-spoof:
  enabled: false
  value: 0 
placeholders:
  date-format: dd.MM.yyyy
  time-format: '[HH:mm:ss / h:mm a]'
  time-offset: 0
  register-tab-expansion: false 

# https://github.com/NEZNAMY/TAB/wiki/Feature-guide:-Placeholder-output-replacements
placeholder-output-replacements:
  '%essentials_vanished%':
    yes: '&7| 隐身'
    no: '' 

# https://github.com/NEZNAMY/TAB/wiki/Feature-guide:-Conditional-placeholders
conditions:
  nick:  # 使用它与 %condition:nick%
    conditions:
      - '%player%=%essentials_nickname%'
    yes: '%player%'
    no: ~%essentials_nickname%
placeholderapi-refresh-intervals:
  default-refresh-interval: 500
  '%server_uptime%': 1000
  '%server_tps_1_colored%': 1000
  '%server_unique_joins%': 5000
  '%player_health%': 200
  '%player_ping%': 1000
  '%vault_prefix%': 1000
  '%rel_factionsuuid_relation_color%': 1000 

# 通过权限节点分配组，而不是从权限插件获取它们
assign-groups-by-permissions: false 

# 如果上面的选项为真，则所有组基于权限获取，并且列表中更高的一个用作主组
# 警告！这不是排序列表，与在选项卡中排序玩家无关！
primary-group-finding-list:
  - 拥有者
  - 管理员
  - 版主
  - 助手
  - 默认 

# 刷新间隔（以毫秒为单位）：
# - 条件/排序中的权限检查
# - 从权限插件获取的组用于排序/每组属性
# - 从权限插件获取数据的前缀/后缀占位符
permission-refresh-interval: 1000 

# 解锁额外的控制台消息
debug: false

# https://github.com/NEZNAMY/TAB/wiki/MySQL
mysql:
  enabled: false
  host: 127.0.0.1
  port: 3306
  database: tab
  username: user
  password: password
  useSSL: true 

########################################################################
# 仅限 BUKKIT - 以下部分仅适用于后端安装 #
########################################################################

# https://github.com/NEZNAMY/TAB/wiki/Feature-guide:-Per-world-playerlist
per-world-playerlist:
  enabled: false 
  # 拥有 tab.staff 的玩家将始终看到所有玩家
  allow-bypass-permission: false
  # 这些世界中的玩家将始终看到所有玩家
  ignore-effect-in-worlds:
    - 忽略的世界
    - build
  shared-playerlist-world-groups:
    lobby:
      - lobby1
      - lobby2
    minigames:
      - paintball
      - bedwars 

#####################################################################
# 仅限 PROXY - 以下部分仅适用于代理安装 #
#####################################################################

# https://github.com/NEZNAMY/TAB/wiki/Feature-guide:-Global-playerlist
global-playerlist:
  enabled: false
  display-others-as-spectators: false
  display-vanished-players-as-spectators: true
  isolate-unlisted-servers: false
  update-latency: false
  spy-servers:
    - spyserver1
    - spyserver2
  server-groups:
    lobbies:
      - lobby1
      - lobby2
    group2:
      - server1
      - server2 

# 从后端服务器而不是代理获取权限和组
use-bukkit-permissions-manager: false

# 有时服务器可能在选项卡中使用离线 UUID，而不是在线，例如禁用 waterfall 的选项卡重写选项
# 如果您发现选项卡格式化不起作用，请切换此选项（设置为相反值）
# 仅影响启用在线模式的代理
use-online-uuid-in-tablist: true

# 如果启用并且找到RedisBungee插件，则钩子启用以与所有代理上的玩家一起工作
enable-redisbungee-support: true
