luckperms.logs.actionlog-prefix=JOURNAL
luckperms.logs.verbose-prefix=VB
luckperms.logs.export-prefix=EXPORTATION
luckperms.commandsystem.available-commands=Utilisez {0} pour afficher les commandes disponibles
luckperms.commandsystem.command-not-recognised=Commande non reconnue
luckperms.commandsystem.no-permission=Vous n''avez pas la permission d''utiliser cette commande \!
luckperms.commandsystem.no-permission-subcommands=Vous n''avez pas la permission d''utiliser les sous-commandes
luckperms.commandsystem.already-executing-command=Une autre commande est en cours d''exécution, en attente de sa fin d''exécution...
luckperms.commandsystem.usage.sub-commands-header=Sous-commandes
luckperms.commandsystem.usage.usage-header=Utilisation de la commande
luckperms.commandsystem.usage.arguments-header=Arguments
luckperms.first-time.no-permissions-setup=Il semble qu’aucune permission n’ait encore été configurée \!
luckperms.first-time.use-console-to-give-access=Avant de pouvoir utiliser l''une des commandes de LuckPerms en jeu, vous devez utiliser la console pour vous donner les accès
luckperms.first-time.console-command-prompt=Ouvrez votre console et exécutez
luckperms.first-time.next-step=Après avoir fait ceci, vous pouvez commencer à définir vos permissions et vos groupes
luckperms.first-time.wiki-prompt=Vous ne savez pas par où commencer ? Cliquez ici \: {0}
luckperms.login.try-again=Veuillez réessayer ultérieurement
luckperms.login.loading-database-error=Une erreur de base de données est survenue lors du chargement des données de permissions
luckperms.login.server-admin-check-console-errors=Si vous êtes un administrateur du serveur, veuillez vérifier la console pour toute erreur
luckperms.login.server-admin-check-console-info=Veuillez vérifier la console du serveur pour plus d''informations
luckperms.login.data-not-loaded-at-pre=Les données des permissions de votre utilisateur n''ont pas été chargées pendant l''étape de pré-connexion
luckperms.login.unable-to-continue=impossible de continuer
luckperms.login.craftbukkit-offline-mode-error=ceci est probablement dû à un conflit entre CraftBukkit et le paramètre online-mode
luckperms.login.unexpected-error=Une erreur inattendue s''est produite lors de la configuration de vos données de permission
luckperms.opsystem.disabled=Le système vanilla d''OP est désactivé sur ce serveur
luckperms.opsystem.sponge-warning=Veuillez noter que le statut d''opérateur n''a aucun effet sur les vérifications de permission de Sponge lorsqu''un plugin de permissions est installé, vous devez directement éditer les données utilisateur
luckperms.duration.unit.years.plural={0} ans
luckperms.duration.unit.years.singular={0} an
luckperms.duration.unit.years.short={0} a
luckperms.duration.unit.months.plural={0} mois
luckperms.duration.unit.months.singular={0} mois
luckperms.duration.unit.months.short={0} mo
luckperms.duration.unit.weeks.plural={0} semaines
luckperms.duration.unit.weeks.singular={0} semaine
luckperms.duration.unit.weeks.short={0} sem
luckperms.duration.unit.days.plural={0} jours
luckperms.duration.unit.days.singular={0} jour
luckperms.duration.unit.days.short={0} j
luckperms.duration.unit.hours.plural={0} heures
luckperms.duration.unit.hours.singular={0} heure
luckperms.duration.unit.hours.short={0} h
luckperms.duration.unit.minutes.plural={0} minutes
luckperms.duration.unit.minutes.singular={0} minute
luckperms.duration.unit.minutes.short={0} min
luckperms.duration.unit.seconds.plural={0} secondes
luckperms.duration.unit.seconds.singular={0} seconde
luckperms.duration.unit.seconds.short={0} s
luckperms.duration.since=il y a {0}
luckperms.command.misc.invalid-code=Code invalide
luckperms.command.misc.response-code-key=code de réponse
luckperms.command.misc.error-message-key=message
luckperms.command.misc.bytebin-unable-to-communicate=Impossible de communiquer avec bytebin
luckperms.command.misc.webapp-unable-to-communicate=Impossible de communiquer avec l''application web
luckperms.command.misc.check-console-for-errors=Vérifiez les erreurs dans la console
luckperms.command.misc.file-must-be-in-data=Le fichier {0} doit être placé directement dans le répertoire de données
luckperms.command.misc.wait-to-finish=Veuillez attendre la fin et réessayer
luckperms.command.misc.invalid-priority=Priorité invalide {0}
luckperms.command.misc.expected-number=Un nombre était attendu
luckperms.command.misc.date-parse-error=Impossible de déterminer la date {0}
luckperms.command.misc.date-in-past-error=Vous ne pouvez pas choisir une date dans le passé \!
luckperms.command.misc.page=page {0} sur {1}
luckperms.command.misc.page-entries={0} entrées
luckperms.command.misc.none=Aucun
luckperms.command.misc.loading.error.unexpected=Une erreur inattendue est survenue
luckperms.command.misc.loading.error.user=Utilisateur non chargé
luckperms.command.misc.loading.error.user-specific=Impossible de charger l''utilisateur cible {0}
luckperms.command.misc.loading.error.user-not-found=Un utilisateur pour {0} n''a pas pu être trouvé
luckperms.command.misc.loading.error.user-save-error=Une erreur s''est produite lors de la sauvegarde des données utilisateur de {0}
luckperms.command.misc.loading.error.user-not-online=L''utilisateur {0} n’est pas connecté
luckperms.command.misc.loading.error.user-invalid={0} n''est pas un pseudonyme/uuid valide
luckperms.command.misc.loading.error.user-not-uuid=L''utilisateur cible {0} n''est pas un uuid valide
luckperms.command.misc.loading.error.group=Groupe non chargé
luckperms.command.misc.loading.error.all-groups=Impossible de charger tous les groupes
luckperms.command.misc.loading.error.group-not-found=Il n''y a aucun groupe nommé {0}
luckperms.command.misc.loading.error.group-save-error=Une erreur s''est produite lors de la sauvegarde des données de groupe de {0}
luckperms.command.misc.loading.error.group-invalid={0} n''est pas un nom de groupe valide
luckperms.command.misc.loading.error.track=Track non chargée
luckperms.command.misc.loading.error.all-tracks=Impossibles de charger toutes les tracks
luckperms.command.misc.loading.error.track-not-found=Impossible de trouver une track nommée {0}
luckperms.command.misc.loading.error.track-save-error=Une erreur s''est produite lors de la sauvegarde des données de track de {0}
luckperms.command.misc.loading.error.track-invalid={0} n''est pas un nom de track valide
luckperms.command.editor.no-match=Impossible d''ouvrir l''éditeur, aucun objet ne correspond au type souhaité
luckperms.command.editor.start=Préparation d''une nouvelle session d''éditeur, veuillez patienter...
luckperms.command.editor.url=Cliquez sur le lien ci-dessous pour ouvrir l''éditeur
luckperms.command.editor.unable-to-communicate=Impossible de communiquer avec l''éditeur
luckperms.command.editor.apply-edits.success=Les données de l''éditeur web ont été appliquées à {0} {1} avec succès
luckperms.command.editor.apply-edits.success-summary={0} {1} et {2} {3}
luckperms.command.editor.apply-edits.success.additions=ajouts
luckperms.command.editor.apply-edits.success.additions-singular=ajout
luckperms.command.editor.apply-edits.success.deletions=suppressions
luckperms.command.editor.apply-edits.success.deletions-singular=suppression
luckperms.command.editor.apply-edits.no-changes=Aucune modification n''a été appliquée depuis l''éditeur web, les données retournées ne contiennent aucune modification
luckperms.command.editor.apply-edits.unknown-type=Impossible d''appliquer la modification au type d''objet spécifié
luckperms.command.editor.apply-edits.unable-to-read=Impossible de lire les données en utilisant le code donné
luckperms.command.search.searching.permission=Recherche des utilisateurs et des groupes avec {0}
luckperms.command.search.searching.inherit=Recherche des utilisateurs et des groupes qui héritent de {0}
luckperms.command.search.result={0} entrées trouvées de {1} utilisateurs et {2} groupes
luckperms.command.search.result.default-notice=Remarque \: lors de la recherche des membres dans le groupe par défaut, les joueurs hors ligne qui n''ont pas d''autres permissions ne seront pas affichés \!
luckperms.command.search.showing-users=Affichage des entrées utilisateur
luckperms.command.search.showing-groups=Affichage des entrées du groupe
luckperms.command.tree.start=Génération de l''arborescence des permissions, veuillez patienter...
luckperms.command.tree.empty=Impossible de générer l''arborescence, aucun résultat n''a été trouvé
luckperms.command.tree.url=URL de l''arborescence des permissions
luckperms.command.verbose.invalid-filter={0} n''est pas un filtre détaillé valide
luckperms.command.verbose.enabled=Journalisation détaillée de {0} pour les vérifications correspondant à {1}
luckperms.command.verbose.command-exec=Execution de la commande {1} par {0} et rapport de toutes les vérifications effectuées...
luckperms.command.verbose.off=Journalisation détaillée {0}
luckperms.command.verbose.command-exec-complete=Exécution de la commande terminée
luckperms.command.verbose.command.no-checks=L''exécution de la commande est terminée, mais aucune vérification de permission n''a été effectuée
luckperms.command.verbose.command.possibly-async=Cela peut être dû au fait que le plugin exécute les commandes en arrière-plan (async)
luckperms.command.verbose.command.try-again-manually=Vous pouvez toujours utiliser le verbe manuellement  pour détecter les contrôles effectués de cette manière
luckperms.command.verbose.enabled-recording=Enregistrement détaillé de {0} pour les vérifications correspondant à {1}
luckperms.command.verbose.uploading=Journalisation détaillée {0}, envoie des résultats...
luckperms.command.verbose.url=URL des résultats détaillés
luckperms.command.verbose.enabled-term=activé
luckperms.command.verbose.disabled-term=desactivé
luckperms.command.verbose.query-any=TOUS
luckperms.command.info.running-plugin=Utilisation de
luckperms.command.info.platform-key=Plateforme
luckperms.command.info.server-brand-key=Marque du serveur
luckperms.command.info.server-version-key=Version du serveur
luckperms.command.info.storage-key=Stockage
luckperms.command.info.storage-type-key=Type
luckperms.command.info.storage.meta.split-types-key=Types
luckperms.command.info.storage.meta.ping-key=Ping
luckperms.command.info.storage.meta.connected-key=Connecté
luckperms.command.info.storage.meta.file-size-key=Taille du fichier
luckperms.command.info.extensions-key=Extensions
luckperms.command.info.messaging-key=Message
luckperms.command.info.instance-key=Instance
luckperms.command.info.static-contexts-key=Contextes statiques
luckperms.command.info.online-players-key=Joueurs connectés
luckperms.command.info.online-players-unique={0} uniques
luckperms.command.info.uptime-key=Temps de fonctionnement
luckperms.command.info.local-data-key=Données Locales
luckperms.command.info.local-data={0} utilisateurs, {1} groupes, {2} tracks
luckperms.command.generic.create.success={0} a été créé avec succès
luckperms.command.generic.create.error=Une erreur est survenue lors de la création de {0}
luckperms.command.generic.create.error-already-exists={0} existe déjà \!
luckperms.command.generic.delete.success={0} a été supprimé avec succès
luckperms.command.generic.delete.error=Une erreur est survenue lors de la suppression de {0}
luckperms.command.generic.delete.error-doesnt-exist={0} n''existe pas \!
luckperms.command.generic.rename.success={0} a été renommé avec succès en {1}
luckperms.command.generic.clone.success={0} a été cloné avec succès vers {1}
luckperms.command.generic.info.parent.title=Groupes Parents
luckperms.command.generic.info.parent.temporary-title=Groupes Parents Temporaires
luckperms.command.generic.info.expires-in=expire dans
luckperms.command.generic.info.inherited-from=hérité de
luckperms.command.generic.info.inherited-from-self=soi
luckperms.command.generic.show-tracks.title=Tracks de {0}
luckperms.command.generic.show-tracks.empty={0} n''est sur aucune track
luckperms.command.generic.clear.node-removed={0} nœuds ont été supprimés
luckperms.command.generic.clear.node-removed-singular={0} nœud a été supprimé
luckperms.command.generic.clear=Les nœuds de {0} ont été effacés dans le contexte {1}
luckperms.command.generic.permission.info.title=Permissions de {0}
luckperms.command.generic.permission.info.empty={0} n’a pas de permission définie
luckperms.command.generic.permission.info.click-to-remove=Cliquez pour retirer ce nœud de {0}
luckperms.command.generic.permission.check.info.title=Informations de permission pour {0}
luckperms.command.generic.permission.check.info.directly={0} a la permission {1} définie à {2} dans le contexte {3}
luckperms.command.generic.permission.check.info.inherited={0} hérite de {1} défini à {2} depuis {3} dans le contexte {4}
luckperms.command.generic.permission.check.info.not-directly={0} n''a pas de {1} défini
luckperms.command.generic.permission.check.info.not-inherited={0} n''hérite pas de {1}
luckperms.command.generic.permission.check.result.title=Vérification des permissions pour {0}
luckperms.command.generic.permission.check.result.result-key=Résultat
luckperms.command.generic.permission.check.result.processor-key=Processeur
luckperms.command.generic.permission.check.result.cause-key=Cause
luckperms.command.generic.permission.check.result.context-key=Contexte
luckperms.command.generic.permission.set={0} à été défini sur {1} pour {2} dans le contexte {3}
luckperms.command.generic.permission.already-has={0} a déjà {1} de défini dans le contexte {2}
l