luckperms.logs.actionlog-prefix=REGISTRO
luckperms.logs.verbose-prefix=VB
luckperms.logs.export-prefix=EXPORTAR
luckperms.commandsystem.available-commands=Usa {0} para ver los comandos disponibles
luckperms.commandsystem.command-not-recognised=Comando desconocido
luckperms.commandsystem.no-permission=No tienes permiso para usar este comando\!
luckperms.commandsystem.no-permission-subcommands=No tienes permiso para usar ningún subcomando
luckperms.commandsystem.already-executing-command=Se está ejecutando otro comando. Esperando a que termine...
luckperms.commandsystem.usage.sub-commands-header=Sub Comandos
luckperms.commandsystem.usage.usage-header=Uso del Comando
luckperms.commandsystem.usage.arguments-header=Argumentos
luckperms.first-time.no-permissions-setup=¡Parece que aún no se ha configurado ningún permiso\!
luckperms.first-time.use-console-to-give-access=Antes de que puedas usar cualquier commando de LuckPerms en el juego, necesitas usar la consola para darte acceso a ti mismo
luckperms.first-time.console-command-prompt=Abre tu consola y ejecuta
luckperms.first-time.next-step=Después de haber hecho esto, puedes comenzar a definir tu asignación de permisos y grupos
luckperms.first-time.wiki-prompt=¿No sabes por dónde empezar? Chequea aquí\: {0}
luckperms.login.try-again=Por favor inténtalo de nuevo más tarde
luckperms.login.loading-database-error=Se ha producido un error en la base de datos al cargar los datos de permisos
luckperms.login.server-admin-check-console-errors=Si eres un administrador del servidor, por favor revisa la consola en busca de errores
luckperms.login.server-admin-check-console-info=Por favor, comprueba la consola del servidor para más información
luckperms.login.data-not-loaded-at-pre=Los datos de permisos para su usuario no fueron cargados durante la etapa de pre-inicio de sesión
luckperms.login.unable-to-continue=no se puede continuar
luckperms.login.craftbukkit-offline-mode-error=esto es probablemente debido a un conflicto entre CraftBukkit y la configuración del modo en línea
luckperms.login.unexpected-error=Se produjo un error inesperado al configurar sus datos de permisos
luckperms.opsystem.disabled=El sistema OP vanilla está deshabilitado en este servidor
luckperms.opsystem.sponge-warning=Por favor, ten en cuenta que el estado de Operador del Servidor no tiene efecto en los chequeos de permisos en Sponge cuando un plugin de permisos está instalado, debes editar los datos de usuario directamente
luckperms.duration.unit.years.plural={0} años
luckperms.duration.unit.years.singular={0} año
luckperms.duration.unit.years.short={0}a
luckperms.duration.unit.months.plural={0} meses
luckperms.duration.unit.months.singular={0} mes
luckperms.duration.unit.months.short={0}m
luckperms.duration.unit.weeks.plural={0} semanas
luckperms.duration.unit.weeks.singular={0} semana
luckperms.duration.unit.weeks.short={0}sem
luckperms.duration.unit.days.plural={0} días
luckperms.duration.unit.days.singular={0} día
luckperms.duration.unit.days.short={0}d
luckperms.duration.unit.hours.plural={0} horas
luckperms.duration.unit.hours.singular={0} hora
luckperms.duration.unit.hours.short={0}h
luckperms.duration.unit.minutes.plural={0} minutos
luckperms.duration.unit.minutes.singular={0} minuto
luckperms.duration.unit.minutes.short={0}min
luckperms.duration.unit.seconds.plural={0} segundos
luckperms.duration.unit.seconds.singular={0} segundo
luckperms.duration.unit.seconds.short={0}s
luckperms.duration.since=hace {0}
luckperms.command.misc.invalid-code=Código no válido
luckperms.command.misc.response-code-key=código de respuesta
luckperms.command.misc.error-message-key=mensaje
luckperms.command.misc.bytebin-unable-to-communicate=No se puede comunicar con bytebin
luckperms.command.misc.webapp-unable-to-communicate=No se puede comunicar con la aplicación web
luckperms.command.misc.check-console-for-errors=Comprueba la consola en busca de errores
luckperms.command.misc.file-must-be-in-data=El archivo {0} debe ser un hijo directo del directorio de datos
luckperms.command.misc.wait-to-finish=Por favor, espere a que termine y vuelva a intentarlo
luckperms.command.misc.invalid-priority=Prioridad inválida {0}
luckperms.command.misc.expected-number=Se esperaba un número
luckperms.command.misc.date-parse-error=No se pudo analizar la fecha {0}
luckperms.command.misc.date-in-past-error=¡No puedes establecer una fecha en el pasado\!
luckperms.command.misc.page=página {0} de {1}
luckperms.command.misc.page-entries={0} entradas
luckperms.command.misc.none=Ninguno
luckperms.command.misc.loading.error.unexpected=Ocurrió un error inesperado
luckperms.command.misc.loading.error.user=Usuario no cargado
luckperms.command.misc.loading.error.user-specific=No se puede cargar el usuario {0}
luckperms.command.misc.loading.error.user-not-found=No se pudo encontrar un usuario para {0}
luckperms.command.misc.loading.error.user-save-error=Hubo un error al guardar los datos de usuario para {0}
luckperms.command.misc.loading.error.user-not-online=Usuario {0} no está conectado
luckperms.command.misc.loading.error.user-invalid={0} no es un uuid/nombre de usuario válido
luckperms.command.misc.loading.error.user-not-uuid=El usuario objetivo {0} no es un uuid válido
luckperms.command.misc.loading.error.group=Grupo no cargado
luckperms.command.misc.loading.error.all-groups=No se pueden cargar todos los grupos
luckperms.command.misc.loading.error.group-not-found=Un grupo llamado {0} no pudo ser encontrado
luckperms.command.misc.loading.error.group-save-error=Hubo un error mientras se guardaban los datos del grupo para {0}
luckperms.command.misc.loading.error.group-invalid={0} no es un nombre de grupo válido
luckperms.command.misc.loading.error.track=Escalera no cargada
luckperms.command.misc.loading.error.all-tracks=No se pueden cargar todas las escaleras
luckperms.command.misc.loading.error.track-not-found=La escalera llamada {0} no pudo ser encontrada
luckperms.command.misc.loading.error.track-save-error=Hubo un error mientras se guardaban los datos de la escalera para {0}
luckperms.command.misc.loading.error.track-invalid={0} no es un nombre válido de una escalera
luckperms.command.editor.no-match=No se puede abrir el editor, no hay objetos que coincidan con el tipo deseado
luckperms.command.editor.start=Preparando una nueva sesión del editor, por favor espere...
luckperms.command.editor.url=Haga clic en el enlace de abajo para abrir el editor
luckperms.command.editor.unable-to-communicate=No se puede comunicar con el editor
luckperms.command.editor.apply-edits.success=Los datos del editor web se aplicaron a {0} {1} con éxito
luckperms.command.editor.apply-edits.success-summary={0} {1} y {2} {3}
luckperms.command.editor.apply-edits.success.additions=adiciones
luckperms.command.editor.apply-edits.success.additions-singular=adición
luckperms.command.editor.apply-edits.success.deletions=eliminaciones
luckperms.command.editor.apply-edits.success.deletions-singular=eliminación
luckperms.command.editor.apply-edits.no-changes=No se aplicaron cambios desde el editor web, los datos devueltos no contienen ninguna edición
luckperms.command.editor.apply-edits.unknown-type=No se puede aplicar la edición al tipo de objeto especificado
luckperms.command.editor.apply-edits.unable-to-read=No se pueden leer los datos con el código dado
luckperms.command.search.searching.permission=Buscando usuarios y grupos con {0}
luckperms.command.search.searching.inherit=Buscando usuarios y grupos que heredan de {0}
luckperms.command.search.result=Se encontraron {0} entradas de {1} usuarios y {2} grupos
luckperms.command.search.result.default-notice=Nota\: al buscar miembros del grupo por defecto, ¡los jugadores desconectados sin otros permisos no serán mostrados\!
luckperms.command.search.showing-users=Mostrando entradas de usuario
luckperms.command.search.showing-groups=Mostrando entradas de grupo
luckperms.command.tree.start=Generando árbol de permisos, por favor espere...
luckperms.command.tree.empty=No se puede generar el árbol, no se han encontrado resultados
luckperms.command.tree.url=URL del árbol de permisos
luckperms.command.verbose.invalid-filter={0} no es un filtro verboso válido
luckperms.command.verbose.enabled=Registro verboso {0} para las verificaciones que coinciden con {1}
luckperms.command.verbose.command-exec=Obligando a {0} a ejecutar el comando {1} y reportando todas las verificaciones realizadas...
luckperms.command.verbose.off=Registro verboso {0}
luckperms.command.verbose.command-exec-complete=Ejecución del comando completa
luckperms.command.verbose.command.no-checks=Ejecución de comandos completada, pero no se realizaron comprobaciones de permiso
luckperms.command.verbose.command.possibly-async=Esto puede ser porque el plugin ejecuta comandos en segundo plano (asíncrono)
luckperms.command.verbose.command.try-again-manually=Aún puedes usar la información detallada manualmente para detectar las comprobaciones realizadas de esta manera
luckperms.command.verbose.enabled-recording=Grabación verbosa {0} para chequeos que coinciden con {1}
luckperms.command.verbose.uploading=Registro verboso {0}, subiendo resultados...
luckperms.command.verbose.url=URL de resultados verbosos
luckperms.command.verbose.enabled-term=habilitado
luckperms.command.verbose.disabled-term=deshabilitado
luckperms.command.verbose.query-any=CUALQUIER
luckperms.command.info.running-plugin=Ejecutando
luckperms.command.info.platform-key=Plataforma
luckperms.command.info.server-brand-key=Nombre del servidor
luckperms.command.info.server-version-key=Versión del Servidor
luckperms.command.info.storage-key=Almacenamiento
luckperms.command.info.storage-type-key=Tipo
luckperms.command.info.storage.meta.split-types-key=Tipos
luckperms.command.info.storage.meta.ping-key=Latencia
luckperms.command.info.storage.meta.connected-key=Conectado
luckperms.command.info.storage.meta.file-size-key=Tamaño del Archivo
luckperms.command.info.extensions-key=Extensiones
luckperms.command.info.messaging-key=Mensajería
luckperms.command.info.instance-key=Instancia
luckperms.command.info.static-contexts-key=Contextos estáticos
luckperms.command.info.online-players-key=Jugadores en línea
luckperms.command.info.online-players-unique={0} único
luckperms.command.info.uptime-key=Tiempo de actividad
luckperms.command.info.local-data-key=Datos locales
luckperms.command.info.local-data={0} usuarios, {1} grupos, {2} escaleras
luckperms.command.generic.create.success={0} fue creado con éxito
luckperms.command.generic.create.error=Hubo un error al crear {0}
luckperms.command.generic.create.error-already-exists=¡{0} ya existe\!
luckperms.command.generic.delete.success={0} fue eliminado con éxito
luckperms.command.generic.delete.error=Hubo un error al eliminar {0}
luckperms.command.generic.delete.error-doesnt-exist=¡{0} no existe\!
luckperms.command.generic.rename.success={0} fue renombrado con éxito a {1}
luckperms.command.generic.clone.success={0} fue clonado con éxito en {1}
luckperms.command.generic.info.parent.title=Grupos Padres
luckperms.command.generic.info.parent.temporary-title=Grupos Padre Temporales
luckperms.command.generic.info.expires-in=expira en
luckperms.command.generic.info.inherited-from=heredado de
luckperms.command.generic.info.inherited-from-self=sí mismo
luckperms.command.generic.show-tracks.title=Escaleras de {0}
luckperms.command.generic.show-tracks.empty={0} no está en ninguna escalera
luckperms.command.generic.clear.node-removed={0} nodos fueron eliminados
luckperms.command.generic.clear.node-removed-singular={0} nodo fue eliminado
luckperms.command.generic.clear=Los nodos de {0} fueron borrados en el contexto {1}
luckperms.command.generic.permission.info.title=Permisos de {0}
luckperms.command.generic.permission.info.empty={0} no tiene ningún permiso establecido
luckperms.command.generic.permission.info.click-to-remove=Haga clic para eliminar este nodo de {0}
luckperms.command.generic.permission.check.info.title=Información de permisos para {0}
luckperms.command.generic.permission.check.info.directly={0} tiene el permiso {1} establecido en {2} en el contexto {3}
luckperms.command.generic.permission.check.info.inherited={0} hereda {1} establecido a {2} de {3} en el contexto {4}
luckperms.command.generic.permission.check.info.not-directly={0} no tiene {1} establecido
luckperms.command.generic.permission.check.info.not-inherited={0} no hereda {1}
luckperms.command.generic.permission.check.result.title=Comprobación de permisos para {0}
luckperms.command.generic.permission.check.result.result-key=Resultado
luckperms.command.generic.permission.check.result.processor-key=Procesador
luckperms.command.generic.permission.check.result.cause-key=Causa
luckperms.command.generic.permission.check.result.context-key=Contexto
luckperms.command.generic.permission.set={0} establecido en {1} para {2} en el contexto {3}
luckperms.command.generic.permission.already-has={0} ya tiene {1} establecido en el contexto {2}
luckperms.command.generic.permission.set-temp={0} establecido en {1} para {2} por una duración de {3} en contexto {4}
luckperms.command.generic.permission.already-has-temp={0} ya tiene {1} establecido temporalmente en el contexto {2}
luckperms.command.generic.permission.unset={0} fue desestablecido para {1} en el contexto {2}
luckperms.command.generic.permission.doesnt-have={0} no tiene {1} establecido en el contexto {2}
luckperms.command.generic.permission.unset-temp=Permiso temporal {0} desestablecido para {1} en el contexto {2}
luckperms.command.generic.permission.subtract={0} establecido en {1} para {2} por una duración de {3} en el contexto {4}. {5} menos que antes
luckperms.command.generic.permission.doesnt-have-temp={0} no tiene {1} establecido temporalmente en el contexto {2}
luckperms.command.generic.permission.clear=Los permisos de {0} fueron borrados en el contexto {1}
luckperms.command.generic.parent.info.title=Padres de {0}
luckperms.command.generic.parent.info.empty={0} no tiene ningún padre definido
luckperms.command.generic.parent.info.click-to-remove=Haga clic para eliminar este padre de {0}
luckperms.command.generic.parent.add={0} ahora hereda permisos de {1} en el contexto {2}
luckperms.command.generic.parent.add-temp={0} ahora hereda permisos de {1} por una duración de {2} en el contexto {3}
luckperms.command.generic.parent.set={0} tuvo sus grupos padres existentes eliminados, y ahora solo hereda {1} en el contexto {2}
luckperms.command.generic.parent.set-track={0} tuvo sus grupos padres existentes en la escalera {1} eliminados, y ahora solo hereda {2} en el contexto {3}
luckperms.command.generic.parent.remove={0} ya no hereda permisos de {1} en el contexto {2}
luckperms.command.generic.parent.remove-temp={0} ya no hereda temporalmente permisos de {1} en el contexto {2}
luckperms.command.generic.parent.subtract={0} heredará permisos de {1} por una duración de {2} en el contexto {3}, {4} menos que antes
luckperms.command.generic.parent.clear=Los padres de {0} fueron eliminados en el contexto {1}
luckperms.command.generic.parent.clear-track=Los padres de {0} en la escalera {1} fueron eliminados en el contexto {2}
luckperms.command.generic.parent.already-inherits={0} ya hereda de {1} en el contexto {2}
luckperms.command.generic.parent.doesnt-inherit={0} no hereda de {1} en el contexto {2}
luckperms.command.generic.parent.already-temp-inherits={0} ya hereda temporalmente de {1} en el contexto {2}
luckperms.command.generic.parent.doesnt-temp-inherit={0} no hereda temporalmente de {1} en el contexto {2}
luckperms.command.generic.chat-meta.info.title-prefix=Prefijos de {0}
luckperms.command.generic.chat-meta.info.title-suffix=Sufijos de {0}
luckperms.command.generic.chat-meta.info.none-prefix={0} no tiene prefijos
luckperms.command.generic.chat-meta.info.none-suffix={0} no tiene sufijos
luckperms.command.generic.chat-meta.info.click-to-remove=Haga clic para eliminar este {0} de {1}
luckperms.command.generic.chat-meta.already-has={0} ya tiene {1} {2} establecido a una prioridad de {3} en el contexto {4}
luckperms.command.generic.chat-meta.already-has-temp={0} ya tiene {1} {2} establecido temporalmente a una prioridad de {3} en el contexto {4}
luckperms.command.generic.chat-meta.doesnt-have={0} no tiene {1} {2} establecido a una prioridad de {3} en el contexto {4}
luckperms.command.generic.chat-meta.doesnt-have-temp={0} no tiene {1} {2} establecido temporalmente a una prioridad de {3} en el contexto {4}
luckperms.command.generic.chat-meta.add={0} tuvo {1} {2} establecido a una prioridad de {3} en el contexto {4}
luckperms.command.generic.chat-meta.add-temp={0} tuvo {1} {2} establecido a una prioridad de {3} por una duración de {4} en el contexto {5}
luckperms.command.generic.chat-meta.remove={0} tuvo {1} {2} a una prioridad de {3} eliminado en el contexto {4}
luckperms.command.generic.chat-meta.remove-bulk={0} tuvo todos {1} en prioridad {2} eliminados en el contexto {3}
luckperms.command.generic.chat-meta.remove-temp={0} tuvo un temporal {1} {2} en prioridad {3} eliminado en el contexto {4}
luckperms.command.generic.chat-meta.remove-temp-bulk={0} tuvo todos los {1} temporales en prioridad {2} eliminados en el contexto {3}
luckperms.command.generic.meta.info.title=Meta de {0}
luckperms.command.generic.meta.info.none={0} no tiene meta
luckperms.command.generic.meta.info.click-to-remove=Haga clic para eliminar este nodo meta de {0}
luckperms.command.generic.meta.already-has={0} ya tiene la clave meta {1} establecida en {2} en el contexto {3}
luckperms.command.generic.meta.already-has-temp={0} ya tiene la clave meta {1} establecida temporalmente en {2} en el contexto {3}
luckperms.command.generic.meta.doesnt-have={0} no tiene clave meta {1} establecida en el contexto {2}
luckperms.command.generic.meta.doesnt-have-temp={0} no tiene clave meta {1} establecida temporalmente en el contexto {2}
luckperms.command.generic.meta.set=Establecer clave meta {0} a {1} para {2} en contexto {3}
luckperms.command.generic.meta.set-temp=Establecer clave meta {0} a {1} para {2} por una duración de {3} en el contexto {4}
luckperms.command.generic.meta.unset=Desestablecer clave meta {0} para {1} en el contexto {2}
luckperms.command.generic.meta.unset-temp=Desestablecer clave meta temporal {0} para {1} en el contexto {2}
luckperms.command.generic.meta.clear=La meta de {0} que coincide con el tipo {1} fue borrada en el contexto {2}
luckperms.command.generic.contextual-data.title=Datos Contextuales
luckperms.command.generic.contextual-data.mode.key=modo
luckperms.command.generic.contextual-data.mode.server=servidor
luckperms.command.generic.contextual-data.mode.active-player=jugador activo
luckperms.command.generic.contextual-data.contexts-key=Contextos
luckperms.command.generic.contextual-data.prefix-key=Prefijo
luckperms.command.generic.contextual-data.suffix-key=Sufijo
luckperms.command.generic.contextual-data.primary-group-key=Grupo Primario
luckperms.command.generic.contextual-data.meta-key=Meta
luckperms.command.generic.contextual-data.null-result=Ninguno
luckperms.command.user.info.title=Información del Usuario
luckperms.command.user.info.uuid-key=UUID
luckperms.command.user.info.uuid-type-key=tipo
luckperms.command.user.info.uuid-type.mojang=mojang
luckperms.command.user.info.uuid-type.not-mojang=fuera de línea
luckperms.command.user.info.status-key=Estado
luckperms.command.user.info.status.online=En Línea
luckperms.command.user.info.status.offline=Fuera de Línea
luckperms.command.user.removegroup.error-primary=No puedes eliminar un usuario de su grupo primario
luckperms.command.user.primarygroup.not-member={0} no era miembro de {1}, agregándolos ahora
luckperms.command.user.primarygroup.already-has={0} ya tiene a {1} establecido como su grupo primario
luckperms.command.user.primarygroup.warn-option=Advertencias\: El método de cálculo de grupo primario siendo usado por este servidor ({0}) puede no reflejar este cambio
luckperms.command.user.primarygroup.set=El grupo primario de {0} fue establecido a {1}
luckperms.command.user.track.error-not-contain-group={0} no está ya en ningún grupo de {1}
luckperms.command.user.track.unsure-which-track=Inseguro de qué pista usar, por favor especifíquelo como argumento
luckperms.command.user.track.missing-group-advice=O cree el grupo, o elimínelo de la escalera e intente de nuevo
luckperms.command.user.promote.added-to-first={0} no está en ningún grupo en {1}, así que fueron añadidos al primer grupo, {2} en el contexto {3}
luckperms.command.user.promote.not-on-track={0} no está en ningún grupo en {1}, así que no fue promovido
luckperms.command.user.promote.success=Promoviendo {0} a lo largo de la pista {1} de {2} a {3} en el contexto {4}
luckperms.command.user.promote.end-of-track=Se alcanzó el final de la pista {0}, no es posible promover {1}
luckperms.command.user.promote.next-group-deleted=El siguiente grupo en la pista, {0}, ya no existe
luckperms.command.user.promote.unable-to-promote=No se puede promover al usuario
luckperms.command.user.demote.success=Descendiendo a {0} sobre la escalera {1} de {2} a {3} en el contexto {4}
luckperms.command.user.demote.end-of-track=Se alcanzó el final de la escalera {0}, así que {1} fue eliminado de {2}
luckperms.command.user.demote.end-of-track-not-removed=Se alcanzó el final de la escalera {0}, pero {1} fue eliminado del primer grupo
luckperms.command.user.demote.previous-group-deleted=El grupo anterior en la escalera, {0}, ya no existe
luckperms.command.user.demote.unable-to-demote=No se puede descender al usuario
luckperms.command.group.list.title=Grupos
luckperms.command.group.delete.not-default=No se puede eliminar el grupo predeterminado
luckperms.command.group.info.title=Información del grupo
luckperms.command.group.info.display-name-key=Nombre a mostrar
luckperms.command.group.info.weight-key=Peso
luckperms.command.group.setweight.set=Establecer peso en {0} para el grupo {1}
luckperms.command.group.setdisplayname.doesnt-have={0} no tiene un nombre para mostrar establecido
luckperms.command.group.setdisplayname.already-has={0} ya tiene un nombre para mostrar de {1}
luckperms.command.group.setdisplayname.already-in-use=El nombre para mostrar {0} ya está siendo usado por {1}
luckperms.command.group.setdisplayname.set=Establecer nombre para mostrar a {0} para el grupo {1} en el contexto {2}
luckperms.command.group.setdisplayname.removed=Se eliminó el nombre para mostrar del grupo {0} en el contexto {1}
luckperms.command.track.list.title=Escaleras
luckperms.command.track.path.empty=Ninguno
luckperms.command.track.info.showing-track=Mostrando Escalera
luckperms.command.track.info.path-property=Ruta
luckperms.command.track.clear=La escalera de grupos {0} fue limpiada
luckperms.command.track.append.success=El grupo {0} se añadió a la escalera {1}
luckperms.command.track.insert.success=El grupo {0} fue insertado en la escalera {1} en la posición {2}
luckperms.command.track.insert.error-number=Se esperaba un número pero en su lugar se recibió\: {0}
luckperms.command.track.insert.error-invalid-pos=Imposible insertar en la posición {0}
luckperms.command.track.insert.error-invalid-pos-reason=posición inválida
luckperms.command.track.remove.success=El grupo {0} fue quitado de la escalera {1}
luckperms.command.track.error-empty={0} no puede ser usado ya que está vacío o contiene solo un grupo
luckperms.command.track.error-multiple-groups={0} es miembro de múltiples grupos en esta escalera
luckperms.command.track.error-ambiguous=No se puede determinar su ubicación
luckperms.command.track.already-contains={0} ya contiene a {1}
luckperms.command.track.doesnt-contain={0} no contiene a {1}
luckperms.command.log.load-error=No se pudo cargar el registro
luckperms.command.log.invalid-page=Número de página inválido
luckperms.command.log.invalid-page-range=Por favor, introduzca un valor entre {0} y {1}
luckperms.command.log.empty=No hay entradas de registro que mostrar
luckperms.command.log.notify.error-console=No se pueden alternar las notificaciones para la consola
luckperms.command.log.notify.enabled-term=Habilitado
luckperms.command.log.notify.disabled-term=Deshabilitado
luckperms.command.log.notify.changed-state={0} salida de registro
luckperms.command.log.notify.already-on=Ya está recibiendo notificaciones
luckperms.command.log.notify.already-off=Actualmente no estás recibiendo notificaciones
luckperms.command.log.notify.invalid-state=Estado desconocido. Esperando {0} o {1}
luckperms.command.log.show.search=Mostrando acciones recientes para la consulta {0}
luckperms.command.log.show.recent=Mostrando acciones recientes
luckperms.command.log.show.by=Mostrando acciones recientes de {0}
luckperms.command.log.show.history=Mostrando historial de {0} {1}
luckperms.command.export.error-term=Error
luckperms.command.export.already-running=Otro proceso de exportación ya se está ejecutando
luckperms.command.export.file.already-exists=El archivo {0} ya existe
luckperms.command.export.file.not-writable=No se puede escribir en el archivo {0}
luckperms.command.export.file.success=Exportado con éxito a {0}
luckperms.command.export.file-unexpected-error-writing=Se ha producido un error inesperado al escribir el archivo
luckperms.command.export.web.export-code=Código de exportación
luckperms.command.export.web.import-command-description=Utilice el siguiente comando para importar
luckperms.command.import.term=Importación
luckperms.command.import.error-term=Error
luckperms.command.import.already-running=Otro proceso de importación ya se está ejecutando
luckperms.command.import.file.doesnt-exist=El archivo {0} no existe
luckperms.command.import.file.not-readable=El archivo {0} no se puede leer
luckperms.command.import.file.unexpected-error-reading=Se ha producido un error inesperado al leer del archivo de importación
luckperms.command.import.file.correct-format=¿es el formato correcto?
luckperms.command.import.web.unable-to-read=No se pueden leer los datos con el código dado
luckperms.command.import.progress.percent={0}% completado
luckperms.command.import.progress.operations={0}/{1} operaciones completadas
luckperms.command.import.starting=Comenzando el proceso de importación
luckperms.command.import.completed=COMPLETADO
luckperms.command.import.duration=tomó {0} segundos
luckperms.command.bulkupdate.must-use-console=El comando de actualización en masa sólo puede utilizarse desde la consola
luckperms.command.bulkupdate.invalid-data-type=Tipo inválido, se esperaba {0}
luckperms.command.bulkupdate.invalid-constraint=Restricción inválida {0}
luckperms.command.bulkupdate.invalid-constraint-format=Las restricciones deben estar en el formato {0}
luckperms.command.bulkupdate.invalid-comparison=Operador de comparación inválido {0}
luckperms.command.bulkupdate.invalid-comparison-format=Se esperaba uno de los siguientes\: {0}
luckperms.command.bulkupdate.queued=Operación de actualización en masa fue puesta en la cola
luckperms.command.bulkupdate.confirm=Ejecuta {0} para ejecutar la actualización
luckperms.command.bulkupdate.unknown-id=La operación con id {0} no existe o ha expirado
luckperms.command.bulkupdate.starting=Ejecutando actualización en masa
luckperms.command.bulkupdate.success=Actualización en masa completada con éxito
luckperms.command.bulkupdate.success.statistics.nodes=Total de nodos afectados
luckperms.command.bulkupdate.success.statistics.users=Total de usuarios afectados
luckperms.command.bulkupdate.success.statistics.groups=Total de grupos afectados
luckperms.command.bulkupdate.failure=Falló la actualización en masa, comprueba la consola en busca de errores
luckperms.command.update-task.request=Una tarea de actualización ha sido solicitada, por favor espere
luckperms.command.update-task.complete=Tarea de actualización completada
luckperms.command.update-task.push.attempting=Intentando enviar a otros servidores
luckperms.command.update-task.push.complete=Otros servidores fueron notificados a través de {0} con éxito
luckperms.command.update-task.push.error=Error al enviar cambios a otros servidores
luckperms.command.update-task.push.error-not-setup=No se pueden enviar cambios a otros servidores ya que no se ha configurado un servicio de mensajería
luckperms.command.reload-config.success=El archivo de configuración fue recargado
luckperms.command.reload-config.restart-note=algunas opciones sólo se aplicarán después de que el servidor se haya reiniciado
luckperms.command.translations.searching=Buscando traducciones disponibles, por favor espere...
luckperms.command.translations.searching-error=No se pudo obtener una lista de traducciones disponibles
luckperms.command.translations.installed-translations=Traducciones Instaladas
luckperms.command.translations.available-translations=Traducciones Disponibles
luckperms.command.translations.percent-translated=‎{0}% traducido
luckperms.command.translations.translations-by=por
luckperms.command.translations.installing=Instalando traducciones, por favor espere...
luckperms.command.translations.download-error=No se puede descargar la traducción para {0}
luckperms.command.translations.installing-specific=Instalando idioma {0}...
luckperms.command.translations.install-complete=Instalación completa
luckperms.command.translations.download-prompt=Use {0} para descargar e instalar versiones actualizadas de estas traducciones proporcionadas por la comunidad
luckperms.command.translations.download-override-warning=Tenga en cuenta que esto anulará cualquier cambio que haya hecho para estos idiomas
luckperms.usage.user.description=Un conjunto de comandos para administrar usuarios dentro de LuckPerms. (Un ''usuario'' en LuckPerms es sólo un jugador, y puede referirse a un UUID o nombre de usuario)
luckperms.usage.group.description=Un conjunto de comandos para administrar grupos dentro de LuckPerms. Los grupos son sólo colecciones de asignaciones de permisos que se pueden dar a los usuarios. Los nuevos grupos se hacen utilizando el comando ''creategroup''.
luckperms.usage.track.description=Un conjunto de comandos para administrar escaleras dentro de LuckPerms. Las escaleras son una colección ordenada de grupos que pueden ser usadas para definir ascensos y descensos.
luckperms.usage.log.description=Un conjunto de comandos para administrar la funcionalidad de registro dentro de LuckPerms.
luckperms.usage.sync.description=Recarga todos los datos del almacenamiento del plugin en memoria, y aplica cualquier cambio que se detecte.
luckperms.usage.info.description=Imprime información general sobre la instancia activa del plugin.
luckperms.usage.editor.description=Crea una nueva sesión del editor web
luckperms.usage.editor.argument.type=los tiposa cargar en el editor. (''todos'', ''usuarios'' o ''grupos'')
luckperms.usage.editor.argument.filter=permiso para filtrar entradas de usuario por
luckperms.usage.verbose.description=Controla el sistema de monitorización verboso de comprobación de permisos.
luckperms.usage.verbose.argument.action=ya sea para activar/desactivar el registro, o para cargar la salida del registro
luckperms.usage.verbose.argument.filter=el filtro contra el cual coincidir las entradas
luckperms.usage.verbose.argument.commandas=el jugador/comando a ejecutar
luckperms.usage.tree.description=Genera una vista de árbol (jerarquía de lista ordenada) de todos los permisos conocidos a LuckPerms.
luckperms.usage.tree.argument.scope=la raíz del árbol. especifique "." para incluir todos los permisos
luckperms.usage.tree.argument.player=el nombre de un jugador en línea contra el que comprobar
luckperms.usage.search.description=Busca por todos los usuarios/grupos con un permiso específico
luckperms.usage.search.argument.permission=el permiso a buscar
luckperms.usage.search.argument.page=la página a ver
luckperms.usage.network-sync.description=Sincroniza cambios con el almacenamiento y solicita a todos los demás servidores que hagan lo mismo
luckperms.usage.import.description=Importa datos de un archivo de exportación (previamente creado)
luckperms.usage.import.argument.file=el archivo a importar
luckperms.usage.import.argument.replace=reemplazar datos existentes en lugar de combinar
luckperms.usage.import.argument.upload=sube los datos de un exporte previo
luckperms.usage.export.description=Exporta todos los datos de permisos a un archivo de ''exportación''. Puede volver a importarse más adelante.
luckperms.usage.export.argument.file=el archivo a exportar
luckperms.usage.export.argument.without-users=excluir usuarios en la exportación
luckperms.usage.export.argument.without-groups=excluye los grupos del exporte
luckperms.usage.export.argument.upload=Sube todos los datos de los permisos al webeditor. Puede ser re-importado mas adelante.
luckperms.usage.reload-config.description=Recarga algunas opciones de configuración
luckperms.usage.bulk-update.description=Ejecuta consultas de cambio masivo en todos los datos
luckperms.usage.bulk-update.argument.data-type=el tipo de datos a cambiar. (''todos'', ''usuarios'' o ''grupos'')
luckperms.usage.bulk-update.argument.action=la acción a realizar en los datos. (''actualizar'' o ''eliminar'')
luckperms.usage.bulk-update.argument.action-field=el campo sobre el que actuar. sólo es requerido para ''actualizar''. (''permiso'', ''servidor'' o ''mundo'')
luckperms.usage.bulk-update.argument.action-value=el valor con el que reemplazar. sólo es requerido para ''actualizar''.
luckperms.usage.bulk-update.argument.constraint=las restricciones requeridas para la actualización
luckperms.usage.translations.description=Gestionar traducciones
luckperms.usage.translations.argument.install=subcomando para instalar traducciones
luckperms.usage.apply-edits.description=Aplica cambios en los permisos hechos desde el editor web
luckperms.usage.apply-edits.argument.code=el código único para los datos
luckperms.usage.apply-edits.argument.target=a quién aplicar los datos
luckperms.usage.create-group.description=Crea un nuevo grupo
luckperms.usage.create-group.argument.name=el nombre del grupo
luckperms.usage.create-group.argument.weight=el peso del grupo
luckperms.usage.create-group.argument.display-name=el nombre a mostrar del grupo
luckperms.usage.delete-group.description=Elimina un grupo
luckperms.usage.delete-group.argument.name=el nombre del grupo
luckperms.usage.list-groups.description=Lista todos los grupos de la plataforma
luckperms.usage.create-track.description=Crea una nueva escalera
luckperms.usage.create-track.argument.name=el nombre de la escalera
luckperms.usage.delete-track.description=Elimina una escalera
luckperms.usage.delete-track.argument.name=el nombre de la escalera
luckperms.usage.list-tracks.description=Lista todas las escaleras en la plataforma
luckperms.usage.user-info.description=Muestra información acerca del usuario
luckperms.usage.user-switchprimarygroup.description=Cambia el grupo primario del usuario
luckperms.usage.user-switchprimarygroup.argument.group=el grupo al cual cambiar
luckperms.usage.user-promote.description=Asciende al usuario en una escalera
luckperms.usage.user-promote.argument.track=la escalera en la que ascender al usuario
luckperms.usage.user-promote.argument.context=los contextos en los que promover al usuario
luckperms.usage.user-promote.argument.dont-add-to-first=sólo asciende al usuario is ya está en la escalera
luckperms.usage.user-demote.description=Desciende al usuario en una escalera
luckperms.usage.user-demote.argument.track=la escalera en la que descender al usuario
luckperms.usage.user-demote.argument.context=los contextos en los que descender al usuario
luckperms.usage.user-demote.argument.dont-remove-from-first=impedir que el usuario sea eliminado del primer grupo
luckperms.usage.user-clone.description=Clona el usuario
luckperms.usage.user-clone.argument.user=el nombre/uuid del usuario al que clonar
luckperms.usage.group-info.description=Da información sobre el grupo
luckperms.usage.group-listmembers.description=Muestra los usuarios/grupos que heredan de este grupo
luckperms.usage.group-listmembers.argument.page=la página a ver
luckperms.usage.group-setweight.description=Establece el peso del grupo
luckperms.usage.group-setweight.argument.weight=el peso a establecer
luckperms.usage.group-set-display-name.description=Establece el nombre para mostrar del grupo
luckperms.usage.group-set-display-name.argument.name=el nombre a establecer
luckperms.usage.group-set-display-name.argument.context=los contextos en los que establecer el nombre
luckperms.usage.group-rename.description=Renombra el grupo
luckperms.usage.group-rename.argument.name=el nuevo nombre
luckperms.usage.group-clone.description=Clona el grupo
luckperms.usage.group-clone.argument.name=el nombre del grupo al cual clonar
luckperms.usage.holder-editor.description=Abre el editor de permisos web
luckperms.usage.holder-showtracks.description=Lista las escaleras en las que el objeto se encuentra
luckperms.usage.holder-clear.description=Elimina todos los permisos, padres y meta
luckperms.usage.holder-clear.argument.context=los contextos por los que filtrar
luckperms.usage.permission.description=Editar permisos
luckperms.usage.parent.description=Editar herencias
luckperms.usage.meta.description=Editar valores de metadata
luckperms.usage.permission-info.description=Muestra los nodos de permiso que el objeto tiene
luckperms.usage.permission-info.argument.page=la página a ver
luckperms.usage.permission-info.argument.sort-mode=cómo ordenar las entradas
luckperms.usage.permission-set.description=Establece un permiso para el objeto
luckperms.usage.permission-set.argument.node=el nodo de permiso a establecer
luckperms.usage.permission-set.argument.value=el valor del nodo
luckperms.usage.permission-set.argument.context=los contextos en los que agregar el permiso
luckperms.usage.permission-unset.description=Desestablece un permiso para el objeto
luckperms.usage.permission-unset.argument.node=el nodo de permiso a desestablecer
luckperms.usage.permission-unset.argument.context=los contextos en los que quitar el permiso
luckperms.usage.permission-settemp.description=Establece un permiso para el objeto temporalmente
luckperms.usage.permission-settemp.argument.node=el nodo de permiso a establecer
luckperms.usage.permission-settemp.argument.value=el valor del nodo
luckperms.usage.permission-settemp.argument.duration=la duración hasta que el nodo de permiso expire
luckperms.usage.permission-settemp.argument.temporary-modifier=cómo se debe aplicar el permiso temporal
luckperms.usage.permission-settemp.argument.context=los contextos en los que agregar el permiso
luckperms.usage.permission-unsettemp.description=Desestablece un permiso temporal para el objeto
luckperms.usage.permission-unsettemp.argument.node=el nodo de permiso a desestablecer
luckperms.usage.permission-unsettemp.argument.duration=la duración a restar
luckperms.usage.permission-unsettemp.argument.context=los contextos en los que quitar el permiso
luckperms.usage.permission-check.description=Comprueba si el objeto tiene un cierto nodo de permiso
luckperms.usage.permission-check.argument.node=el nodo de permiso para comprobar
luckperms.usage.permission-clear.description=Borra todos los permisos
luckperms.usage.permission-clear.argument.context=los contextos por los que filtrar
luckperms.usage.parent-info.description=Lista los grupos de los que este objeto hereda
luckperms.usage.parent-info.argument.page=la página a ver
luckperms.usage.parent-info.argument.sort-mode=cómo ordenar las entradas
luckperms.usage.parent-set.description=Elimina todos los demás grupos que el objeto ya hereda y agrega el dado
luckperms.usage.parent-set.argument.group=el grupo a establecer
luckperms.usage.parent-set.argument.context=los contextos en los que establecer el grupo
luckperms.usage.parent-add.description=Establece otro grupo al objeto del cual heredar los permisos
luckperms.usage.parent-add.argument.group=el grupo del cual heredar
luckperms.usage.parent-add.argument.context=los contextos en los que heredar el grupo
luckperms.usage.parent-remove.description=Elimina una regla de herencia previamente establecida
luckperms.usage.parent-remove.argument.group=el grupo a eliminar
luckperms.usage.parent-remove.argument.context=los contextos en los que eliminar el grupo
luckperms.usage.parent-set-track.description=Elimina todos los demás grupos que el objeto ya hereda en la escalera dada y le agrega el grupo especificado
luckperms.usage.parent-set-track.argument.track=la escalera en la cual establecer
luckperms.usage.parent-set-track.argument.group=el grupo a establecer, o un número relacionado a la posición del grupo en la escalera especificada
luckperms.usage.parent-set-track.argument.context=los contextos en los que establecer el grupo
luckperms.usage.parent-add-temp.description=Establece otro grupo para el objeto para heredar permisos temporalmente
luckperms.usage.parent-add-temp.argument.group=el grupo del cual heredar
luckperms.usage.parent-add-temp.argument.duration=la duración de la membresía del grupo
luckperms.usage.parent-add-temp.argument.temporary-modifier=cómo se debe aplicar el permiso temporal
luckperms.usage.parent-add-temp.argument.context=los contextos en los que heredar el grupo
luckperms.usage.parent-remove-temp.description=Elimina una regla de herencia temporal previamente establecida
luckperms.usage.parent-remove-temp.argument.group=el grupo a eliminar
luckperms.usage.parent-remove-temp.argument.duration=la duración a restar
luckperms.usage.parent-remove-temp.argument.context=los contextos en los que eliminar el grupo
luckperms.usage.parent-clear.description=Elimina todos los padres
luckperms.usage.parent-clear.argument.context=los contextos por los que filtrar
luckperms.usage.parent-clear-track.description=Limpia todos los grupos padres sobre la escalera dada
luckperms.usage.parent-clear-track.argument.track=la escalera en la cual eliminar
luckperms.usage.parent-clear-track.argument.context=los contextos por los que filtrar
luckperms.usage.meta-info.description=Muestra todos los meta de chat
luckperms.usage.meta-set.description=Establece un valor meta
luckperms.usage.meta-set.argument.key=la clave a establecer
luckperms.usage.meta-set.argument.value=el valor a establecer
luckperms.usage.meta-set.argument.context=los contextos en los que agregar el par meta
luckperms.usage.meta-unset.description=Desestablece un valor meta
luckperms.usage.meta-unset.argument.key=la clave a quitar
luckperms.usage.meta-unset.argument.context=los contextos en los que quitar el par meta
luckperms.usage.meta-settemp.description=Establece un valor meta temporalmente
luckperms.usage.meta-settemp.argument.key=la clave a establecer
luckperms.usage.meta-settemp.argument.value=el valor a establecer
luckperms.usage.meta-settemp.argument.duration=la duración hasta que el valor meta expire
luckperms.usage.meta-settemp.argument.context=los contextos en los que agregar el par meta
luckperms.usage.meta-unsettemp.description=Desestablece un valor meta temporal
luckperms.usage.meta-unsettemp.argument.key=la clave a quitar
luckperms.usage.meta-unsettemp.argument.context=los contextos en los que quitar el par meta
luckperms.usage.meta-addprefix.description=Añade un prefijo
luckperms.usage.meta-addprefix.argument.priority=la prioridad a la que añadir el prefijo
luckperms.usage.meta-addprefix.argument.prefix=la cadena de prefijo
luckperms.usage.meta-addprefix.argument.context=los contextos en los que agregar el prefijo
luckperms.usage.meta-addsuffix.description=Añade un sufijo
luckperms.usage.meta-addsuffix.argument.priority=la prioridad a la que añadir el sufijo
luckperms.usage.meta-addsuffix.argument.suffix=la cadena de sufijo
luckperms.usage.meta-addsuffix.argument.context=los contextos en los que agregar el sufijo
luckperms.usage.meta-setprefix.description=Establece un prefijo
luckperms.usage.meta-setprefix.argument.priority=la prioridad a la que establecer el prefijo
luckperms.usage.meta-setprefix.argument.prefix=la cadena de prefijo
luckperms.usage.meta-setprefix.argument.context=los contextos en los que establecer el prefijo
luckperms.usage.meta-setsuffix.description=Establece un sufijo
luckperms.usage.meta-setsuffix.argument.priority=la prioridad a la que establecer el sufijo
luckperms.usage.meta-setsuffix.argument.suffix=la cadena de sufijo
luckperms.usage.meta-setsuffix.argument.context=los contextos en los que establecer el sufijo
luckperms.usage.meta-removeprefix.description=Elimina un prefijo
luckperms.usage.meta-removeprefix.argument.priority=la prioridad a la que quitar el prefijo
luckperms.usage.meta-removeprefix.argument.prefix=la cadena de prefijo
luckperms.usage.meta-removeprefix.argument.context=los contextos en los que quitar el prefijo
luckperms.usage.meta-removesuffix.description=Elimina un sufijo
luckperms.usage.meta-removesuffix.argument.priority=la prioridad a la que quitar el sufijo
luckperms.usage.meta-removesuffix.argument.suffix=la cadena de sufijo
luckperms.usage.meta-removesuffix.argument.context=los contextos en los que quitar el sufijo
luckperms.usage.meta-addtemp-prefix.description=Añade un prefijo temporalmente
luckperms.usage.meta-addtemp-prefix.argument.priority=la prioridad a la que añadir el prefijo
luckperms.usage.meta-addtemp-prefix.argument.prefix=la cadena de prefijo
luckperms.usage.meta-addtemp-prefix.argument.duration=la duración hasta que el prefijo expire
luckperms.usage.meta-addtemp-prefix.argument.context=los contextos en los que agregar el prefijo
luckperms.usage.meta-addtemp-suffix.description=Añade un sufijo temporalmente
luckperms.usage.meta-addtemp-suffix.argument.priority=la prioridad a la que añadir el sufijo
luckperms.usage.meta-addtemp-suffix.argument.suffix=la cadena de sufijo
luckperms.usage.meta-addtemp-suffix.argument.duration=la duración hasta que el sufijo expire
luckperms.usage.meta-addtemp-suffix.argument.context=los contextos en los que agregar el sufijo
luckperms.usage.meta-settemp-prefix.description=Establece un prefijo temporalmente
luckperms.usage.meta-settemp-prefix.argument.priority=la prioridad a la que establecer el prefijo
luckperms.usage.meta-settemp-prefix.argument.prefix=la cadena de prefijo
luckperms.usage.meta-settemp-prefix.argument.duration=la duración hasta que el prefijo expire
luckperms.usage.meta-settemp-prefix.argument.context=los contextos en los que establecer el prefijo
luckperms.usage.meta-settemp-suffix.description=Establece un sufijo temporalmente
luckperms.usage.meta-settemp-suffix.argument.priority=la prioridad a la que establecer el sufijo
luckperms.usage.meta-settemp-suffix.argument.suffix=la cadena de sufijo
luckperms.usage.meta-settemp-suffix.argument.duration=la duración hasta que el sufijo expire
luckperms.usage.meta-settemp-suffix.argument.context=los contextos en los que establecer el sufijo
luckperms.usage.meta-removetemp-prefix.description=Elimina un prefijo temporal
luckperms.usage.meta-removetemp-prefix.argument.priority=la prioridad a la que quitar el prefijo
luckperms.usage.meta-removetemp-prefix.argument.prefix=la cadena de prefijo
luckperms.usage.meta-removetemp-prefix.argument.context=los contextos en los que quitar el prefijo
luckperms.usage.meta-removetemp-suffix.description=Elimina un sufijo temporal
luckperms.usage.meta-removetemp-suffix.argument.priority=la prioridad a la que quitar el sufijo
luckperms.usage.meta-removetemp-suffix.argument.suffix=la cadena de sufijo
luckperms.usage.meta-removetemp-suffix.argument.context=los contextos en los que quitar el sufijo
luckperms.usage.meta-clear.description=Elimina todos los meta
luckperms.usage.meta-clear.argument.type=el tipo de meta a eliminar
luckperms.usage.meta-clear.argument.context=los contextos por los que filtrar
luckperms.usage.track-info.description=Da información sobre la escalera
luckperms.usage.track-editor.description=Abre el editor de permisos web
luckperms.usage.track-append.description=Agrega un grupo al final de la escalera
luckperms.usage.track-append.argument.group=el grupo a añadir
luckperms.usage.track-insert.description=Inserta un grupo en una posición dada a lo largo de la escalera
luckperms.usage.track-insert.argument.group=el grupo a insertar
luckperms.usage.track-insert.argument.position=la posición en la que insertar el grupo (la primera posición en la escalera es 1)
luckperms.usage.track-remove.description=Elimina un grupo de la escalera
luckperms.usage.track-remove.argument.group=el grupo a eliminar
luckperms.usage.track-clear.description=Limpia los grupos en la escalera
luckperms.usage.track-rename.description=Renombra la escalera
luckperms.usage.track-rename.argument.name=el nuevo nombre
luckperms.usage.track-clone.description=Clona la escalera
luckperms.usage.track-clone.argument.name=el nombre de la escalera a clonar sobre
luckperms.usage.log-recent.description=Ver acciones recientes
luckperms.usage.log-recent.argument.user=el nombre/uuid del usuario a filtrar
luckperms.usage.log-recent.argument.page=el número de página a ver
luckperms.usage.log-search.description=Buscar una entrada en el registro
luckperms.usage.log-search.argument.query=la consulta a buscar
luckperms.usage.log-search.argument.page=el número de página a ver
luckperms.usage.log-notify.description=Alternar notificaciones de registro
luckperms.usage.log-notify.argument.toggle=si para activar o desactivar
luckperms.usage.log-user-history.description=Ver historial de un usuario
luckperms.usage.log-user-history.argument.user=el nombre/uuid del usuario
luckperms.usage.log-user-history.argument.page=el número de página a ver
luckperms.usage.log-group-history.description=Ver historial de un grupo
luckperms.usage.log-group-history.argument.group=el nombre del grupo
luckperms.usage.log-group-history.argument.page=el número de página a ver
luckperms.usage.log-track-history.description=Ver historial de la escalera
luckperms.usage.log-track-history.argument.track=el nombre de la escalera
luckperms.usage.log-track-history.argument.page=el número de página a ver
luckperms.usage.sponge.description=Editar datos adicionales de Sponge
luckperms.usage.sponge.argument.collection=la colección a consultar
luckperms.usage.sponge.argument.subject=el sujeto a modificar
luckperms.usage.sponge-permission-info.description=Muestra información sobre los permisos del sujeto
luckperms.usage.sponge-permission-info.argument.contexts=los contextos por los que filtrar
luckperms.usage.sponge-permission-set.description=Establece un permiso para el Sujeto
luckperms.usage.sponge-permission-set.argument.node=el nodo de permiso a establecer
luckperms.usage.sponge-permission-set.argument.tristate=el valor al que asignar el permiso
luckperms.usage.sponge-permission-set.argument.contexts=los contextos en los que establecer el permiso
luckperms.usage.sponge-permission-clear.description=Limpia los permisos del Sujeto
luckperms.usage.sponge-permission-clear.argument.contexts=los contextos en los que borrar los permisos
luckperms.usage.sponge-parent-info.description=Muestra información sobre los padres del sujeto
luckperms.usage.sponge-parent-info.argument.contexts=los contextos por los que filtrar
luckperms.usage.sponge-parent-add.description=Añade un padre al Sujeto
luckperms.usage.sponge-parent-add.argument.collection=la colección de sujetos donde el Sujeto padre está
luckperms.usage.sponge-parent-add.argument.subject=el nombre del Sujeto padre
luckperms.usage.sponge-parent-add.argument.contexts=los contextos en los que agregar el padre
luckperms.usage.sponge-parent-remove.description=Elimina un padre del Sujeto
luckperms.usage.sponge-parent-remove.argument.collection=la colección de sujetos donde el Sujeto padre está
luckperms.usage.sponge-parent-remove.argument.subject=el nombre del Sujeto padre
luckperms.usage.sponge-parent-remove.argument.contexts=los contextos en los que quitar el padre
luckperms.usage.sponge-parent-clear.description=Elimina a los padres del Sujeto
luckperms.usage.sponge-parent-clear.argument.contexts=los contextos en los que quitar a los padres
luckperms.usage.sponge-option-info.description=Muestra información sobre las opciones del sujeto
luckperms.usage.sponge-option-info.argument.contexts=los contextos por los que filtrar
luckperms.usage.sponge-option-set.description=Establece una opción para el Sujeto
luckperms.usage.sponge-option-set.argument.key=la clave a establecer
luckperms.usage.sponge-option-set.argument.value=el valor al que establecer la clave
luckperms.usage.sponge-option-set.argument.contexts=los contextos en los que establecer la opción
luckperms.usage.sponge-option-unset.description=Desestablece una opción para el Sujeto
luckperms.usage.sponge-option-unset.argument.key=la clave a quitar
luckperms.usage.sponge-option-unset.argument.contexts=los contextos en los que quitar la clave
luckperms.usage.sponge-option-clear.description=Elimina a las opciones del Sujeto
luckperms.usage.sponge-option-clear.argument.contexts=los contextos en los que quitar a las opciones
