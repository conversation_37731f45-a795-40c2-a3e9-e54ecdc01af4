 #  -------------------------------------------------------------
 #  BedrockPlayerSupport Config File | Made by DongShaoNB
 #  Docs: https://docs.bps.dsnb.cc
 #  GitHub: https://github.com/DongShaoNB/BedrockPlayerSupport
 #  -------------------------------------------------------------
plugin:
   # 语言
   # Language
  language: 'zh_CN'
   # 启动服务器时检测更新
   # Check update when starting the server
  check-update: true
   # 填写正在使用的基础插件
   # 可选值: auto/cmi/essentialsx/huskhomes/advancedteleport/sunlight/none
   # auto为自动检测 none为没有基础插件(即关闭功能)
   # Fill in the basic plugins currently in use
   # Optional value: auto/cmi/essentialsx/huskhomes/advancedteleport/sunlight/none
   # auto is for automatic detection, none for no basic plugin (i.e. disable function)
  basic: 'auto'
   # 填写正在使用的登录插件
   # 可选值: auto/authme/catseedlogin/nexauth/other/none
   # auto 为自动检测, other 为其他登录插件, none 为没有登录插件(即关闭功能)
   # 当使用其他登录插件时 无法使用自动注册功能 只能使用自动登录功能
   # Fill in the login plugin currently in use
   # Optional value: auto/authme/catseedlogin/nexauth/other/none
   # auto is for automatic detection, other is for other login plugins. none is for no login plugin (i.e. disable function)
   # When using other login plugins, the automatic registration function cannot be used, and only the automatic login function can be used
  auth: 'auto'
   # 启用表单支持 PlaceholderAPI
   # 启用后可以在表单内使用 PlaceholderAPI
   # Enable form support PlaceholderAPI
   # Enable to use PlaceholderAPI within form
  support-papi: true

form:
  teleport:
     # 启用基岩版TP表单(/tpgui)
     # Enable bedrock TP form (/tpgui)
    enable: true
    receive:
       # 启用基岩版接收传送表单(玩家收到传送请求时)
       # Enable bedrock receive teleport form (Player receives teleport request)
      enable: true

     # 启用跨服模式(仅基础插件为 HuskHomes 时可用)
     # 启用后传送表单支持跨服传送
     # Enable cross server mode (only available when the basic plugin is HuskHomes)
     # Enable support cross server teleport of form
    cross-server: false

  kit:
     # 启用基岩版礼包表单(/kitgui)
     # 只有 CMI / EssentialsX / SunLight 可用
     # Enable bedrock kit form (/kitgui)
     # Only CMI / EssentialsX / SunLight is available
    enable: true

  home:
     # 启用基岩版家表单(/homegui)
     # Enable bedrock home form (/homegui)
    enable: true

  warp:
     # 启用基岩版传送点表单(/warpgui)
     # Enable bedrock warp form (/warpgui)
    enable: true

  msg:
     # 启用基岩版消息表单(/msggui)
     # Enable bedrock msg form (/msggui)
    enable: true

  phome:
     # 启用基岩版公共家表单(/phomegui)
     # 仅基础插件为 HuskHomes 时可用
     # Enable bedrock public home form (/phomegui)
     # Only available when the basic plugin is HuskHomes
    enable: true

  back:
     # 启用基岩版返回死亡点表单(玩家死亡复活后自动打开表单)
     # Enable bedrock back death location form (Automatically open form after player's death and respawn)
    enable: true
     # 返回死亡点命令
     # 部分插件会用 /dback 或 其他命令, 请在此处替换
     # The command of back death location
     # If the command of back death location is /dback or other command, please replace it here
    command: '/back'
     # 返回死亡点表单打开延迟时间(单位: 刻, 20刻 = 1秒)
     # 默认为 20 刻, 但部分服务器加载世界较慢, 20刻不足以加载完世界
     # 若复活后表单没有打开, 请尝试调高此值
     # The open delay time of back death location form
     # The default value is 20 ticks, but some servers load the world slowly, 20 ticks is not enough to load the world completely
     # If the form does not open after player respawn, please try increasing this value
    open-delay-time: 20


general:
  join-commands:
     # 启用基岩版进入服务器时执行命令
     # 可用变量: %playerName% 玩家名
     # Enable bedrock player join commands
     # Available variables: %playerName% player name
    enable: false
     # 基岩版进入服务器时执行的命令
     # 可用变量: %playerName% 玩家名
     # 格式: [执行方] 命令
     # 控制台执行: [CONSOLE] say 欢迎基岩版玩家 %playerName%
     # 玩家执行: [PLAYER] me 我是基岩版玩家 %playerName%
     # The command executed when the bedrock player joins the server
     # Available variables: %playerName% player name
     # Format: [Executor] command
     # Console execution: [CONSOLE] say Welcome Bedrock Player %playerName%
     # Player execution: [PLAYER] me I'm a Bedrock Player %playerName%
    commands:
      - '[CONSOLE] say Welcome Bedrock Player %playerName%'
      - '[PLAYER] me I''m a Bedrock Player %playerName%'

  quit-commands:
     # 启用基岩版退出服务器时执行命令
     # 可用变量: %playerName% 玩家名
     # Enable bedrock player quit commands
     # Available variables: %playerName% player name
    enable: false
     # 基岩版退出服务器时执行的命令
     # 可用变量: %playerName% 玩家名
     # 格式: [执行方] 命令
     # 控制台执行: [CONSOLE] say 基岩版玩家 %playerName% 退出了服务器
     # 玩家执行: [PLAYER] me 我是基岩版玩家 %playerName%
     # The command executed when the bedrock player quit the server
     # Available variables: %playerName% player name
     # Format: [Executor] command
     # Console execution: [CONSOLE] say Bedrock Player %playerName% quit the server
     # Player execution: [PLAYER] me I'm a Bedrock Player %playerName%
    commands:
      - '[CONSOLE] say Bedrock Player %playerName% quit the server'
      - '[PLAYER] me I''m a Bedrock Player %playerName%'


auth:
  login:
     # 启用基岩版玩家自动登录功能
     # Enable bedrock player automatic login function
    enable: true
     # 自动登录命令(控制台发送)
     # 验证插件设置为 other 时会调用此处命令
     # 在此处填写你使用的登录插件的强制登录命令
     # 可用变量: %playerName% 玩家名
     # Automatic login command (send by console)
     # When the auth plugin is set to other, this command will be sent
     # Fill in the force login command for the auth plugin you are using here
     # Available variable: %playerName% player name
    command: 'forcelogin %playerName%'

  register:
     # 随机的密码的长度
     # The length of random password
    password-length: 16
     # 启用基岩版玩家自动注册功能
     # Enable bedrock player automatic register function
    enable: false


