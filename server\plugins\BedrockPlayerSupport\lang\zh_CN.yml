 #  -------------------------------------------------------------
 #  BedrockPlayerSupport Language File | Made by DongShaoNB
 #  Crowdin: https://crowdin.com/project/mcbps
 #  MiniMessage: https://docs.advntr.dev/minimessage/format.html
 #  GitHub: https://github.com/DongShaoNB/BedrockPlayerSupport
 #  -------------------------------------------------------------
 #  语言文件默认简体中文。如果需要其他语言，请访问 Crowdin
 #  我们使用 MiniMessage 格式，请查看 MiniMessage 格式文档修改语言
 #  The language file default to Chinese Simplified. If you need another language, please visit Crowdin
 #  We use MiniMessage format, Please refer to the MiniMessage format document to modify the language file
form:
  teleport:
     # 传送表单的选择传送玩家提示语
     # Text of choose teleport player in teleport form
    choose-player: '<green>请选择要传送的玩家'
     # 传送表单的选择传送类型提示语
     # Text of choose teleport type in teleport form
    choose-type: '<green>请选择传送类型'
     # 传送表单的标题
     # Title of the teleport form
    title: '<gold>传送表单'

  received-teleport:
    tpahere:
       # 玩家请求你传送到他的表单的描述文本
       # 可用变量: %requesterName% 请求者名字
       # Description text of the player requests you to teleport to him form
       # Available variable: %requesterName% requester name
      text: '玩家 %requesterName% 请求你传送到他的位置'
       # 玩家请求你传送到他的表单的标题
       # Title of the player requests you to teleport to him form
      title: '<green>玩家请求你传送到他的位置 <white>(TPAHERE)'

     # 传送表单的 '接受' 按钮文本
     # Text of the 'Accept' button in teleport form
    accept: '<green>同意'
    tpa:
       # 玩家请求传送到你的表单的标题
       # Title of the player requests to teleport to you form
      title: '<green>玩家请求传送到你的位置 <white>(TPA)'
       # 玩家请求传送到你的表单的描述文本
       # 可用变量: %requesterName% 请求者名字
       # Description text of the player requests to teleport to you form
       # Available variable: %requesterName% requester name
      text: '玩家 %requesterName% 请求传送到你的位置'

     # 传送表单的 '拒绝' 按钮文本
     # Text of the 'Deny' button in teleport form
    deny: '<red>拒绝'

  msg:
     # 消息表单的输入消息提示语
     # Text of input message in msg form
    input-message: '<green>请填写要发送的消息'
     # 消息表单的选择接收消息玩家提示语
     # Text of choose receive message player in msg form
    choose-player: '<green>请选择接收消息的玩家'
     # 消息表单的标题
     # Title of the msg form
    title: '<gold>消息表单'

  home:
     # 家表单的 '回家' 按钮文本
     # Text of the 'GoHome' button in home form
    gohome-button: '<gold>回家'
     # 家表单的 '设置家' 按钮文本
     # Text of the 'SetHome' button in home form
    sethome-button: '<green>设置家'
     # 家表单的 '删除家' 按钮文本
     # Text of the 'DelHome' button in home form
    delhome-button: '<red>删除家'
     # 家表单的标题
     # Title of the home form
    title: '<gold>我的家'

  phome:
     # 公共家表单的标题
     # Title of the public home form
    title: '<gold>公共家'

  gohome:
     # 回家表单的标题
     # Title of the go home form
    title: '<gold>回家表单'

  back:
     # 返回死亡点表单的标题
     # Title of the back death location form
    title: '<gold>返回死亡点表单'
     # 返回死亡点表单的 '是' 按钮文本
     # Text of the 'YES' button in back death location form
    button-yes: '<green>是'
     # 返回死亡点表单的描述文本
     # Text of the back death location form
    text: '是否返回上个死亡点'
     # 返回死亡点表单的 '否' 按钮文本
     # Text of the 'NO' button in back death location form
    button-no: '<red>否'

  sethome:
     # 设置家表单的标题
     # Title of the set home form
    title: '<gold>设置家表单'
     # 设置家表单的描述文本
     # Text of the set home form
    text: '<gold>家的名称'

  warp:
     # 传送点表单的标题
     # Title of the warp form
    title: '<gold>传送点表单'

  delhome:
     # 删除家表单的标题
     # Title of the delete home form
    title: '<gold>删除家表单'

  kit:
     # 礼包表单的标题
     # Title of the kit form
    title: '<gold>礼包表单'


message:
   # 玩家使用表单命令但没有其他在线玩家的错误提示
   # Error of player use form command but no other online player
  no-other-online-player: '<red>当前没有其他玩家在线!'
   # 自动注册成功后发送给玩家的消息
   # 可用变量: %password% 密码
   # Text of successful automatic register after send to player
   # Available variable: %password% password
  register-successfully: '<green>检测到你是基岩版玩家, 已自动注册! 密码为 %password%, 使用 /changepassword 命令修改密码'
   # 自动登录成功后发送给玩家的消息
   # Text of successful automatic login after send to player
  login-successfully: '<green>检测到你是基岩版玩家, 已自动登录!'
   # 不是基岩版的玩家使用表单命令的错误提示
   # Error of player use form command but is not bedrock player
  not-bedrock-player: '<red>你不是基岩版玩家!'

plugin:
   # 插件重载成功后发送给命令发送者的消息
   # 可用变量: %time% 重载耗时(毫秒)
   # Text of successful reload plugin after send to command sender
   # Available variable: %time% reload time(millisecond)
  reload-successfully: '<green>插件重载成功, 耗时: %time% 毫秒'

