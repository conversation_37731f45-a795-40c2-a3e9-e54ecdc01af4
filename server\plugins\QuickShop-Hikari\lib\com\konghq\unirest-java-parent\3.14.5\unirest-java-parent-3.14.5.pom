<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.konghq</groupId>
    <artifactId>unirest-java-parent</artifactId>
    <packaging>pom</packaging>
    <version>3.14.5</version>
    <name>unirest</name>
    <description>Parent pom for unirest packages</description>
    <url>http://github.com/Kong/unirest-java/</url>

    <licenses>
        <license>
            <name>MIT</name>
            <url>http://opensource.org/licenses/MIT</url>
            <distribution>repo</distribution>
        </license>
    </licenses>

    <modules>
        <module>unirest</module>
        <module>object-mapper-gson</module>
        <module>object-mapper-jackson</module>
        <module>unirest-mocks</module>
        <module>unirest-bdd-tests</module>
        <!--<module>bad-maven-test</module>-->
    </modules>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <jackson.version>2.14.3</jackson.version>
        <main.dir>${project.basedir}</main.dir>
        <junit.version>5.9.0</junit.version>
        <mockito.version>4.8.0</mockito.version>
        <maven-checkstyle-plugin.version>3.1.2</maven-checkstyle-plugin.version>
    </properties>

    <scm>
        <url>https://github.com/Kong/unirest-java</url>
        <connection>scm:git:**************:Kong/unirest-java.git</connection>
        <developerConnection>scm:git:**************:Kong/unirest-java.git</developerConnection>
        <tag>v3.14.5</tag>
    </scm>

    <developers>
        <developer>
            <id>Ryber</id>
            <name>Ryan Bergman</name>
            <url>https://github.com/ryber</url>
            <roles>
                <role>Maintainer</role>
            </roles>
        </developer>
    </developers>

    <distributionManagement>
        <snapshotRepository>
            <id>ossrh</id>
            <url>https://oss.sonatype.org/content/repositories/snapshots</url>
        </snapshotRepository>
        <repository>
            <id>ossrh</id>
            <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
        </repository>
    </distributionManagement>

    <profiles>
        <profile>
            <id>ossrh</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-gpg-plugin</artifactId>
                        <version>1.6</version>
                        <executions>
                            <execution>
                                <id>sign-artifacts</id>
                                <phase>verify</phase>
                                <goals>
                                    <goal>sign</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

    <build>
        <plugins>
            <plugin>
                <groupId>com.mycila.maven-license-plugin</groupId>
                <artifactId>maven-license-plugin</artifactId>
                <version>1.8.0</version>
                <configuration>
                    <!-- Template location -->
                    <header>${main.dir}/build/fileHeader.txt</header>
                    <strictCheck>true</strictCheck>
                    <excludes>
                        <exclude>**/*.scss</exclude>
                        <exclude>**/*.b64</exclude>
                        <exclude>**/*.svg</exclude>
                        <exclude>**/Gemfile</exclude>
                        <exclude>.java-version</exclude>
                        <exclude>.java-version</exclude>
                        <exclude>**/*.js</exclude>
                        <exclude>**/*.json</exclude>
                        <exclude>**/*.p12</exclude>
                        <exclude>**/*.pem</exclude>
                        <exclude>**/*.html</exclude>
                        <exclude>**/*.xml</exclude>
                        <exclude>**/*.txt</exclude>
                        <exclude>**/*.log</exclude>
                        <exclude>**/*.md</exclude>
                        <exclude>**/*.yml</exclude>
                        <exclude>.idea/**</exclude>
                        <exclude>.github/**</exclude>
                        <exclude>**/*.scssc</exclude>
                        <exclude>**/.jekyll-metadata</exclude>
                        <exclude>.git*</exclude>
                        <exclude>LICENSE</exclude>
                        <exclude>Gemfile.lock</exclude>
                    </excludes>
                </configuration>
                <executions>
                    <execution>
                        <phase>validate</phase>
                        <goals>
                            <goal>format</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>3.1.0</version>
                <configuration>
                    <archive>
                        <manifestEntries>
                            <Built-By>Unirest</Built-By>
                        </manifestEntries>
                    </archive>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-checkstyle-plugin</artifactId>
                <version>${maven-checkstyle-plugin.version}</version>
                <dependencies>
                    <dependency>
                        <groupId>com.puppycrawl.tools</groupId>
                        <artifactId>checkstyle</artifactId>
                        <version>8.36.2</version>
                    </dependency>
                </dependencies>
                <executions>
                    <execution>
                        <phase>compile</phase>
                        <id>auto-checkstyle</id>
                        <goals>
                            <goal>checkstyle</goal>
                            <goal>check</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <configLocation>build/checkstyle.xml</configLocation>
                    <suppressionsLocation>build/suppressions.xml</suppressionsLocation>
                    <logViolationsToConsole>true</logViolationsToConsole>
                    <consoleOutput>true</consoleOutput>
                    <linkXRef>false</linkXRef>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.22.2</version>
                <configuration>
                    <argLine>-Duser.timezone=UTC ${itCoverageAgent}</argLine>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-failsafe-plugin</artifactId>
                <version>2.22.2</version>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.8</version>
                <executions>
                    <execution>
                        <id>default-prepare-agent</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                        <configuration>
                            <propertyName>itCoverageAgent</propertyName>
                        </configuration>
                    </execution>
                    <execution>
                        <id>default-report</id>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>default-check</id>
                        <goals>
                            <goal>check</goal>
                        </goals>
                        <configuration>
                            <rules>
                                <rule>
                                    <element>BUNDLE</element>
                                    <limits>
                                        <limit implementation="org.jacoco.report.check.Limit">
                                            <counter>COMPLEXITY</counter>
                                            <value>COVEREDRATIO</value>
                                            <minimum>0.40</minimum>
                                        </limit>
                                    </limits>
                                </rule>
                            </rules>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-release-plugin</artifactId>
                <version>2.5.3</version>
                <configuration>
                    <arguments>maven.test.skip=true</arguments>
                    <tagNameFormat>v@{project.version}</tagNameFormat>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.sonatype.plugins</groupId>
                <artifactId>nexus-staging-maven-plugin</artifactId>
                <version>1.6.13</version>
                <extensions>true</extensions>
                <configuration>
                    <serverId>ossrh</serverId>
                    <nexusUrl>https://oss.sonatype.org/</nexusUrl>
                    <autoReleaseAfterClose>true</autoReleaseAfterClose>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <fork>true</fork>
                    <meminitial>128m</meminitial>
                    <maxmem>512m</maxmem>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>2.2.1</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <version>3.2.0</version>
                <configuration>
                    <source>8</source>
                </configuration>
                <executions>
                    <execution>
                        <id>attach-javadocs</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
                <version>1.4.1</version>
                <dependencies>
                    <dependency>
                        <groupId>org.sonatype.ossindex.maven</groupId>
                        <artifactId>ossindex-maven-enforcer-rules</artifactId>
                        <version>3.1.0</version>
                    </dependency>
                </dependencies>
                <executions>
                    <execution>
                        <id>vulnerability-checks</id>
                        <phase>validate</phase>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                        <configuration>
                            <rules>
                                <banVulnerable implementation="org.sonatype.ossindex.maven.enforcer.BanVulnerableDependencies">
                                    <scope>runtime</scope>
                                    <excludeVulnerabilityIds>
                                        <!-- no fix yet: Jackson -->
                                        <exclude>sonatype-2022-6438</exclude>
                                    </excludeVulnerabilityIds>
                                </banVulnerable>
                            </rules>
                        </configuration>
                    </execution>
                    <execution>
                        <id>enforce-versions</id>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                        <configuration>
                            <rules>
                                <requireMavenVersion>
                                    <!--at least maven 3.2-->
                                    <version>[3.2,)</version>
                                </requireMavenVersion>
                                <requireJavaVersion>
                                    <version>1.8</version>
                                </requireJavaVersion>
                            </rules>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>

        <pluginManagement>
            <plugins>
                <plugin>
                    <!-- Configured in pluginManagement instead of plugins, because we do not want a shaded parent POM -->
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-shade-plugin</artifactId>
                    <version>3.2.4</version>
                    <executions>
                        <execution>
                            <phase>package</phase>
                            <goals>
                                <goal>shade</goal>
                            </goals>
                            <configuration>
                                <!-- Will output 2 jars: the original, and the shaded one -->
                                <shadedArtifactAttached>true</shadedArtifactAttached>

                                <!-- final name of the shaded jar will be ${project.artifactId}-standalone -->
                                <shadedClassifierName>standalone</shadedClassifierName>

                                <relocations>
                                    <relocation>
                                        <!-- Apache HttpClients -->
                                        <pattern>org.apache.</pattern>
                                        <shadedPattern>unirest.shaded.org.apache.</shadedPattern>
                                    </relocation>
                                    <relocation>
                                        <!-- Jackson -->
                                        <pattern>com.fasterxml.jackson.</pattern>
                                        <shadedPattern>unirest.shaded.com.fasterxml.jackson.</shadedPattern>
                                    </relocation>
                                    <relocation>
                                        <!-- Google GSON -->
                                        <pattern>com.google.</pattern>
                                        <shadedPattern>unirest.shaded.com.google.</shadedPattern>
                                    </relocation>
                                </relocations>

                                <!-- NOTE: Any dependencies of the project will not show up in the standalone pom.
                                This means that if those dependencies are not properly relocated and there is a class-loading conflict,
                                user would not be able to figure out where the conflicting dependency is. -->
                                <createDependencyReducedPom>true</createDependencyReducedPom>

                            </configuration>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>


    <dependencies>
        <!-- common testing dependencies -->
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <version>${mockito.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-junit-jupiter</artifactId>
            <version>${mockito.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
            <version>${junit.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-engine</artifactId>
            <version>${junit.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-params</artifactId>
            <version>${junit.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.skyscreamer</groupId>
            <artifactId>jsonassert</artifactId>
            <version>1.5.1</version>
            <scope>test</scope>
        </dependency>
        <!-- https://mvnrepository.com/artifact/com.google.guava/guava -->
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>32.1.1-jre</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <reporting>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-checkstyle-plugin</artifactId>
                <version>${maven-checkstyle-plugin.version}</version>
                <reportSets>
                    <reportSet>
                        <reports>
                            <report>checkstyle</report>
                        </reports>
                    </reportSet>
                </reportSets>
            </plugin>
        </plugins>
    </reporting>

</project>