# GrimAC主要配置
# 源代码链�?: https://github.com/MWHunter/Grim
# 版权�? DefineOutside �? 贡献者所有，基于GPLv3�?源协议�??
# 修改过的二进制文件，或带有复制的Grim代码的插件，必须是私有仓库，不得公开售卖此插件�??
# 中文翻译提供 nuym ,如有错误请提�? Issues 并@�?

alerts:
  # 是否在控制台显示作弊消息�?
  print-to-console: true
  # 子服之间是否共享作弊消息�?
  # 如果正在使用Velocity，你必须在Velocity的配置中启用` bungee-plugin-message-channel `�?
  proxy:
    # 作弊消息是否应该发�?�到连接到代理的子服?
    send: false
    # 此服务器是否接收来自其他子服的作弊消息？
    receive: false

verbose:
  print-to-console: false

client-brand:
  # 这意味着如果客户端型号匹配以下正则表达式，它将不会将其客户端版本显示在聊天栏�?
  ignored-clients:
    - "^vanilla$"
    - "^fabric$"
    - "^lunarclient:v\\d+\\.\\d+\\.\\d+-\\d{4}$"
    - "^Feather Fabric$"

spectators:
  # 把所有拥�? grim.spectartor 权限的人都隐藏起来，不管他是否观察玩家�??
  hide-regardless: false
  # 观察者这些世界不会隐�?
  allowed-worlds:
    - ""

# 在我们让他们超时之前，玩家应该有多长时间�? 此处�?60 �?60s
max-transaction-time: 60

Simulation:
  # 当玩家合法时，我们应该将总优势乘以多�?
  # 这是默认配置的样子（x �? = seconds ，y �? = 1/1000 方块�?: https://www.desmos.com/calculator/d4ufgxrxer
  setback-decay-multiplier: 0.999
  # 我们应该为玩家的移动创建多大的偏移量�?
  # 从可能的移动中以方块为单位测�?
  # 我们通过另一种方式来计算 Optifine，但将其降低�? 0.001 会降�? FastMath
  # 如果此补偿不起作用，则反作弊记录此玩家作�?
  threshold: 0.001
  # 在玩家受到回弹之前，1 ticks 的违规行为有多大�?
  # -1 关闭
  immediate-setback-threshold: 0.1
  # 在我们开始遭遇回弹之前，我们的优势有多大?
  # -1 关闭
  max-advantage: 1
  # 在默认配置的50秒后，玩家将�?4块跑�?1�?
  # 这是为了防止玩家收集过多的违规行为，并且永远无法清除�?有的违规行为
  # 这是默认配置的样子（x �? = seconds ，y �? = 1/1000 方块�?: https://www.desmos.com/calculator/4lovswdarj
  max-ceiling: 4

# �?查玩家是否穿�?
Phase:
  setbackvl: 1 # 错误的方块可以允许爬墙，加上这个�?查是相对稳定�?
  decay: 0.005

AirLiquidPlace:
  cancelvl: 0

FabricatedPlace:
  cancelvl: 5

FarPlace:
  cancelvl: 5

PositionPlace:
  cancelvl: 5

RotationPlace:
  cancelvl: 5

# Prediction-based NoSlow �?�?
# 在这里解释错误的服务器代�?......即使在发送垃圾数据包时右键单击和副手按钮也很分辨
# 比其他反作弊更稳定，但请报告任何错误......我在这里修复了大量的代码问题�?
NoSlowA:
  # 有多少偏移量是作�?
  # 标志 0.03-0.2 �? NoSlow �?�?
  threshold: 0.001
  # 到达多少VL时回�?
  setbackvl: 5
  # 当玩家使用一个物品并被它减慢时衰减多少VL
  decay: 0.05

Knockback:
  # 当玩家合法时，我们应该将总优势乘以多�?
  setback-decay-multiplier: 0.999
  # 我们应该为玩家的移动创建多大的偏移量�?
  # 从可能的移动中以方块为单位测�?
  threshold: 0.001
  # 在玩家受到回弹之前，1 ticks 的违规行为有多大�?
  # -1 关闭
  immediate-setback-threshold: 0.1
  # 在我们开始遭遇回弹之前，我们的优势有多大?
  # -1 关闭
  max-advantage: 1
  # 这是为了防止玩家收集过多的违规行为，并且永远无法清除�?有的违规行为
  max-ceiling: 4


Explosion:
  threshold: 0.001
  setbackvl: 3

TimerA:
  setbackvl: 10
  # 玩家卡顿时可以累积以供以后使用的毫秒�?
  # 如果设置得太高，可能会允�? 1.8 快�?�使�?/快�?�治�?/快�?�弓箭绕过，120 毫秒似乎是一个很好的平衡
  drift: 120
  # Ping at which the check will start to limit timer balance, to prevent abuse.
  # Can cause some setbacks for legitimate players but only if they are over this ping threshold.
  # -1 to disable
  ping-abuse-limit-threshold: 1000

NegativeTimer:
  # Number of milliseconds lost while moving before we should start flagging
  drift: 1200

# �? TimerA 相同的检查方法，但�?�用于坐�?
TimerVehicle:
  # 实体1.0005 �?�?
  setbackvl: 10

EntityControl:
  setbackvl: 25

Reach:
  # 我们应该将碰撞箱扩大多少�? 0.0005 应该�?测到 3.0005+ reach
  #
  # �? 1.9-1.18.1（不�? 1.18.2）或某些客户�?/服务器组合中存在 0.03 距离的增加，因为
  # 协议更改和限制�?? 对于 1.8 服务器上�? 1.7/1.8 客户端，此检查功能最为强大�??
  threshold: 0.0005
  # 我们应该取消我们知道不可能的命中吗？
  # 3.00-3.03 命中可能会�?�过，但仍会被标记，因为数据包顺序限�?
  block-impossible-hits: true
  # 这将在每个ticks结束时发送一个额外的数据包，以检查作弊的可能�?
  # 这会注入服务器的连接列表以在服务器刷新之前发送最终数据包
  # 启用这将增加�?有玩家的带宽使用
  # 这不会降低整体服务器性能
  # 启用此功能将捕获更多作弊者�??
  # 禁用此功能仍会捕获作弊�?�，不会导致误报
  # 除非你是专注�? 1.8 PvP 的服务器，否则不建议使用这个额外的数据包
  enable-post-packet: false

exploit:
  allow-sprint-jumping-when-using-elytra: true
  # 该�?�项可在发生鬼块时重新同步玩家，从�?�减轻玩家在鬼块上的位置�?
  allow-building-on-ghostblocks: true
  distance-to-check-if-ghostblocks: 2

# 启用在加入时注入 netty 的日志插件以调试兼容性问�?
debug-pipeline-on-join: false

# 启用实验性检�?
experimental-checks: false

# Grim有时会取消非法的数据包，比如用timer，在�?秒钟内取消了数个数据包后，我们应该踢掉这个玩家？
# 我们认为是应该的，因为有些数据包限制器并不计算被Grim取消的数据包�?
packet-spam-threshold: 100

config-version: 10
