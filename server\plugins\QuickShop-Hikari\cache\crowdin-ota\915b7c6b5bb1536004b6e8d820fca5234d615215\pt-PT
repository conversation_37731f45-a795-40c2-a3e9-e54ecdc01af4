break-shop-use-supertool: <yellow>Você pode quebrar a loja usando a SuperTool.
fee-charged-for-price-change: <green>Você pagou <red>{0}<green> para mudar o valor.
not-allowed-to-create: <red>Você não pode criar uma loja aqui.
disabled-in-this-world: <red>O QuickShop está desativado neste mundo
how-much-to-trade-for: <green>Digite no chat, por quanto você deseja vender <yellow>{1}x {0}<green>.
client-language-changed: <green>O plugin detectou sua linguagem, agora estamos usando a linguagem {0} com você.
shops-backingup: Criando backup de lojas no banco de dados...
_comment: Ol<PERSON>, tradutor! Se você está editando isto no GitHub ou através do código-fonte, você deve ir em https://crowdin.com/project/quickshop-hikari.
unlimited-shop-owner-changed: <yellow>Este proprietário de loja ilimitado foi alterado para {0}.
bad-command-usage-detailed: '<red>Argumentos de comando inválidos! Aceita os seguintes parâmetros: <gray>{0}'
thats-not-a-number: <red>Numero inválido
shop-name-disallowed: <red>The shop name <yellow>{0} <red>is disallowed. Pick another one!
console-only-danger: <red>Este é um comando perigoso, de modo que apenas o Console possa executá-lo.
not-a-number: <red>Você só pode inserir um número, sua entrada foi {0}.
not-looking-at-valid-shop-block: <red>Não conseguimos achar um bloco para criar a loja. Você precisa olhar para um.
shop-removed-cause-ongoing-fee: <red>Sua loja em {0} foi removida! Você não tem dinheiro suficiente para mantê-la!
tabcomplete:
  amount: '[quantidade]'
  item: '[item]'
  price: '[preço]'
  name: '[name]'
  range: '[distância]'
  currency: 'Nome da Moeda'
  percentage: '[percentage%]'
taxaccount-unset: <green>A taxa dessa loja agora está seguindo a taxa global.
blacklisted-item: <red>Você não pode vender este item porque ele está na lista negra
command-type-mismatch: <red>Este comando só pode ser executado por<aqua>{0}.
server-crash-warning: '<red>O servidor pode falhar após executar o comando /qs reload se você substituir/delete o arquivo Jar do plugin QuickShop enquanto o servidor é executado.'
you-cant-afford-to-change-price: <red>Custa {0} para alterar o preço na sua loja.
safe-mode: <red>QuickShop agora está em modo seguro, você não pode abrir esta loja, por favor entre em contato com o administrador do servidor para corrigir os erros.
forbidden-vanilla-behavior: <red>A operação é proibida porque não é compatível com o comportamento de baunilha
shop-out-of-space-name: <dark_purple>Sua loja {0} está cheia!
paste-disabled: |-
  <red>Paste functionality has been disabled! You cannot request technical support. <newline>Reason: {0}
quick-fill:
  entry-own: ' <yellow>- <red>[own] <aqua>{0} <light_purple>{1}'
  hover:
    - '<yellow>Nome: <aqua>{0}'
    - '<yellow>Proprietário: <aqua>{0}'
    - '<yellow>Tipo: <aqua>{0}'
    - '<yellow>Preço: <aqua>{0}'
    - '<yellow>Item: <aqua>{0}'
    - '<yellow>Localização: <aqua>{0}'
  hover-arg-filled:
    - '<yellow>Nome: <aqua>{name}'
    - '<yellow>Proprietário: <aqua>{owner}'
    - '<yellow>Tipo: <aqua>{type}'
    - '<yellow>Preço: <aqua>{price}'
    - '<yellow>Item: <aqua>{item}'
    - '<yellow>Localização: <aqua>{location}'
  header: '<yellow>You have multiple shops with name "<green>{0}<yellow>", choose one to continue:'
  entry-normal: ' <yellow>- <aqua>{0} <light_purple>{1}'
  entry-cooperation: ' <yellow>- <red>[co] <aqua>{0} <light_purple>{1}'
3rd-plugin-build-check-failed-admin: '<gray>(AdminOnly) <light_purple>{0}<dark_gray> denied the permission checks. If this not expected, try adding <light_purple>{1} <gray>to the listener blacklist. Configuration Guide:https://ghost-chu.github.io/QuickShop-Hikari-Documents/docs/modules/shops/protection-checker'
average-price-nearby: '<green>Preço médio: <yellow>{0}'
inventory-check-global-alert: "<red>[Inventory Check] <gray>Warning! Found a QuickShop display item <gold>{2}</gold> in inventory at <aqua>{0}</aqua> <dark_gray>[{1}]</dark_gray>, which it shouldn't happened, This usually means that someone is maliciously exploiting the exploit to duplicating the display item."
digits-reach-the-limit: <red>Você atingiu os limites de números no preço.
currency-unset: <green>Moeda da loja removida com sucesso. Usando as configurações padrão agora.
you-cant-create-shop-in-there: <red>Você não tem permissão para criar uma loja neste local.
no-pending-action: <red>Você não tem nenhuma ação pendente
refill-success: <green>Re-estocado com sucesso
failed-to-paste: <red>Falha ao carregar os dados no Pastebin. Verifique sua conexão com a internet e tente novamente. (Veja o console para detalhes)
shop-out-of-stock-name: <dark_purple>Sua loja em {0}, {1}, {2} ficou sem {3}!
shop-name-invalid: <red>The shop name <yellow>{0} <red>is invalid. Pick another one!
how-many-buy-stack: <green>Digite no chat, quantos packs você deseja <aqua>COMPRAR<green>. Tem <yellow>{0}<green> itens em cada pack. Você pode comprar <yellow>{1}<green> packs. Digite <aqua>{2}<green> para comprar todos.
exceeded-maximum: <red>O valor excedeu o valor máximo em Java.
unlimited-shop-owner-keeped: '<yellow>Atenção: O proprietário da loja ainda possui um proprietário ilimitado de loja, você precisa redefinir o dono da nova loja por conta própria.'
no-enough-money-to-keep-shops: <red>Você não tinha dinheiro suficiente para manter suas lojas! Todas as lojas foram removidas...
3rd-plugin-build-check-failed: <red>3rd plugin <bold>{0}<reset><red> negou as verificações de permissão, você configurou as permissões?
not-a-integer: <red>Você precisa inserir um número inteiro, sua entrada era {0}.
translation-country: 'Zona do idioma: Português Brasileiro (pt_BR)'
buying-more-than-selling: '<red>CUIDADO: Você está comprando itens por mais do que os está vendendo!'
purchase-failed: '<red>A compra falhou: Erro interno, entre em contato com o administrador do servidor.'
denied-put-in-item: '&CVocê não pode colocar este item na sua loja!'
shop-has-changed: <red>A loja que você tentou usar mudou desde que você clicou nela!
flush-finished: <green>Todas as mensagens foram limpas.
no-price-given: <red>Por favor, insira um preço válido.
shop-already-owned: <red>Isso já é uma loja.
backup-success: <green>Backup bem sucedido.
not-looking-at-shop: Não conseguimos encontrar uma QuickShop. Você precisa estar olhando para uma.
you-cant-afford-a-new-shop: <red>Custa {0} para criar uma nova loja.
success-created-shop: <red>Shop criado.
shop-creation-cancelled: <red>Criação de loja cancelada.
shop-owner-self-trade: <yellow>Você pode fazer trading consigo mesmo para não ganhar nenhum saldo.
purchase-out-of-space: <red>Essa loja está cheia, Chame o dono ou alguém da equipe caso for loja de admin para limpar.
reloading-status:
  success: <green>Recarregar concluído sem quaisquer erros.
  scheduled: <green>Reload completed. <gray>(Some changes required a while to affect)
  require-restart: <green>Reload completed. <yellow>(Some changes require server restart to affect)
  failed: <red>Recarregar falhou, verifique o console do servidor
player-bought-from-your-store-tax: <green>{0} comprou {1} {2} de sua loja e você ganhou {3} ({4} de impostos).
not-enough-space: <red>Você só tem espaço para mais {0}!
shop-name-success: <green>Defina o nome da loja com sucesso para <yellow>{0}<green>.
shop-staff-added: <green>Adicionado com sucesso {0} como membro da equipe da sua loja.
shop-staff-empty: <yellow>This shop have no staff members.
shops-recovering: Recuperando lojas do backup...
virtual-player-component-hover: "<gray>This is a virtual player.\n<gray>Refers to a system account of this name with the same name.</gray>\n<green>UUID: <yellow>{0}</yellow></green>\n<green>Username: <yellow>{1}</yellow></green>\n<green>Display as: <yellow>{2}</yellow></green>"
real-player-component-hover: "<gray>This is a real exists player.\n<green>UUID: <yellow>{0}</yellow></green>\n<green>Username: <yellow>{1}</yellow></green>\n<green>Display as: <yellow>{2}</yellow></green>\n<gray>If you wish to use a virtual system account with the same name, add <dark_gray>\"[]\"</dark_gray> to the both side of the username: <dark_gray>[{1}]</dark_gray>."
menu:
  sell-tax: <green>Você pagou <yellow>{0} <green>em impostos.
  owner: '<green>Proprietário: {0}'
  preview: <aqua>[Visualização do item]
  enchants: <dark_purple>Encantamentos
  sell-tax-self: <green>Você não pagou impostos porque é dono desta loja.
  shop-information: '<green>Informações da loja:'
  item: '<green>Item: <yellow>{0}'
  price-per: <green>Preço de <yellow>{0} <green>- <yellow>{1}
  item-name-and-price: <yellow>{0} {1} <green>por <yellow>{2}
  item-name-and-price-tax: <yellow>{0} {1} <green>para</green> {2} <gray>(<green>{3}</green> em impostos)</gray>
  successful-purchase: '<green>Comprado com sucesso:'
  price-per-stack: <green>Preço de <yellow>{2}x {0} - {1}
  stored-enchants: <dark_purple>Encantamentos armazenados
  item-holochat-error: <red>[Erro]
  this-shop-is-selling: <green>Esta loja está <aqua>VENDENDO<green> itens.
  shop-stack: '<green>Quantidade em lote: <yellow>{0}'
  space: '<green>Espaço: <yellow>{0}'
  effects: <green>Efeitos
  damage-percent-remaining: <yellow>{0}% <green>Restante.
  item-holochat-data-too-large: <red>[Error] Item NBT is too large for showing
  stock: '<green>Estoque <yellow>{0}'
  this-shop-is-buying: <green>Esta loja está <light_purple>COMPRANDO<green> itens.
  successfully-sold: '<green>Vendido com sucesso:'
  total-value-of-chest: '<green>Valor total do baú: <yellow>{0}'
currency-not-exists: <red>Não foi possível encontrar a moeda que deseja definir. Talvez a ortografia esteja errada ou a moeda não esteja disponível neste mundo.
no-nearby-shop: <red>Nenhuma loja próxima com {0}.
translation-author: 'Ghost_chu, Andre_601'
integrations-check-failed-trade: <red>Integração {0} negou a negociação da loja
shop-transaction-failed: <red>Sorry, but an internal error occurred while processing your purchase. The purchase has been cancelled and any operation has been rollback. Please contact the server administrators if it keeping occurred.
success-change-owner-to-server: Proprietário da loja definido como Server.
shop-name-not-found: <red>The shop named <yellow>{0} <red>not exists.
shop-name-too-long: <red>This shop name is too long (max length {0}), please pick another one!
metric:
  header-player: '<yellow>{0}''s {1} {2} Transações:'
  action-hover: <yellow>{0}
  price-hover: <yellow>Total {0}, Incluindo {1} impostos.
  unknown: <gray>(unknown)
  undefined: <gray>(noname)
  no-results: <red>Nenhuma transação encontrada.
  action-description:
    DELETE: <yellow>O jogador excluiu uma loja. E reembolsou a taxa de criação da loja ao proprietário, se possível.
    ONGOING_FEE: <yellow>O jogador pagou a taxa em andamento porque o período de pagamento.
    PURCHASE_BUYING_SHOP: <yellow>Jogador vendeu alguns itens para uma loja de compras.
    CREATE: <yellow>Jogador criou uma loja.
    PURCHASE_SELLING_SHOP: <yellow>Jogador comprou alguns itens de uma loja de vendas
    PURCHASE: <yellow>Item comprado em uma loja
  query-argument: 'Argumento de consulta: {0}'
  amount-hover: <yellow>{0}x
  header-shop: '<yellow>{0}''s {1} {2} Transações:'
  player-hover: |-
    <yellow>{0}<newline><gold>UUID: <gray>{1}
  looking-up: <yellow>Realizando uma pesquisa métrica, aguarde...
  tax-hover: <yellow>{0} impostos
  header-global: '<yellow>O servidor {0} {1} transações:'
  na: <gray>N/A
  transaction-count: Total de <yellow>{0}
  shop-hover: |-
    <yellow>{0}<newline><gold>Pos: <gray>{1} {2} {3},<gray> World: {4}<newline><gold>Owner: <gray>{5}<newline><gold>Shop Type: <gray>{6}<newline><gold>Item: <gray>{7}<newline><gold>Preço: <gray>{8}<gray>
  time-hover: '<yellow>Hora: {0}'
  amount-stack-hover: <yellow>{0}x pilha
permission-denied-3rd-party: '<red>Permissão negada: Plugin de terceiros [{0}].'
you-dont-have-that-many-items: <red>Você só tem {0} de {1}.
complete: <green>Completo!
translate-not-completed-yet-url: 'A tradução do idioma {0} ainda não foi concluída por {1}. Você quer nos ajudar a melhorar a tradução? Acesse: {2}'
success-removed-shop: <green>Loja removida.
currency-set: <green>Moeda da loja definida com sucesso para {0}.
shop-purged-start: <green>Limpeza de Lojas iniciada, verifique o console para mais detalhes.
economy-transaction-failed: <red>Sorry, but an internal error occurred while processing your transaction. The transaction has been cancelled and any economy operation has been rollback. Please contact the server administrators if it keeping occurred.
nothing-to-flush: <green>Você não tem novas mensagens da loja.
no-price-change: <red>Isso não resultaria em uma alteração de preço!
edition-confilct: QuicShop-Hikari with QuickShop-Reremake installed may conflict each other, uninstall one of them.
inventory-unavailable: |-
  <red>This shop InventoryWrapper not exists or invalid. Did you using addon to re-binding the shop Inventory? Information: InventoryWrapper={0}, WrapperProvider={1}, SymbolLink={2}. Please contact the server administrators.
file-test: Este é um arquivo de texto de teste. Nós o usamos para testar se as mensagens são enviadas. filho está quebrado. Você pode preenchê-lo com quaisquer ovos de Páscoa que goste aqui :)
unknown-player: <red>Esse jogador não existe, verifique o nick que você digitou.
player-offline: <red>The target player currently is offline.
player-profile-format: <aqua>{0}</aqua><gold>(<yellow>{1}</yellow>)</gold>
shop-type:
  selling: SELLING
  buying: BUYING
language:
  qa-issues: '<yellow>Problemas de garantia de qualidade: <aqua>{0}%'
  code: '<yellow>Código: <gold>{0}'
  approval-progress: '<yellow>Progresso de aprovação: <aqua>{0}%'
  translate-progress: '<yellow>Progresso da tradução: <aqua>{0}%'
  name: '<yellow>Nome: <gold>{0}'
  help-us: <green>[Ajude-nos a melhorar a qualidade da tradução]
warn-to-paste: |-
  <yellow>Coletando dados e enviando-os para Pastebin, isso pode demorar um pouco. <red><bold>Cuidado: <red>Os dados são mantidos públicos por uma semana! Pode vazar a configuração do servidor e outras informações confidenciais. Envie-o apenas para equipe/desenvolvedores confiáveis.
how-many-sell-stack: <green>Digite no chat, quantos packs você deseja <aqua>VENDER<green>. Tem <yellow>{0}<green> itens em cada pack. Você pode vender <yellow>{1}<green> packs. Digite <aqua>{2}<green> para vender todos.
updatenotify:
  buttontitle: '[Atualizar agora]'
  onekeybuttontitle: '[Atualização em um clique]'
  label:
    github: '[GitHub]'
    ore: '[Ore]'
    lts: '[LTS]'
    nbtapi: '[+NBTAPI]'
    qualityverifyed: '[Qualidade]'
    master: '[Master]'
    unstable: '[Instável]'
    paper: '[Otimizado para +Papel]'
    stable: '[Estável]'
    spigotmc: '[SpigotMC]'
    papermc: '[PaperMC]'
    bukkitdev: '[BukkitDev]'
    polymart: '[Polymart]'
    modrinth: '[Modrinth]'
    basic: '[Básico]'
  list:
    - '{0} foi liberado, você continua usando {1}!'
    - Boom! Nova atualização {0} chegando, atualize!
    - Surpresa! {0} saiu, você está em {1}
    - Parece que você precisa atualizar, {0} foi lançado!
    - Ooops! {0} foi liberado, você está em {1}!
    - Eu prometo, QS foi atualizado para {0}, por que você ainda não atualizou?
    - Corrigindo e re... Desculpe {0} foi lançado!
    - Erro! Não, isso não é um erro, {0} foi lançado!
    - MEU! {0} saiu! Por que você ainda está usando {1}?
    - 'Notícias de Hoje: O QuickShop foi atualizado para {0}!'
    - Plugin K.I.A, Você deveria atualizar para {0}!
    - Atualização {0} foi lançada. Salve a atualização!
    - Há uma atualização comandante, {0} acabou de sair!
    - Olhe meu estilo---{0} atualizado. Você ainda está usando {1}
    - Ahhhhhhh! Nova atualização {0}! Atualize!
    - O que cê tá esperando? {0} foi lançado! Atualize!
    - Doutor, o QuickShop tem uma nova atualização {0}! Você deve atualizar~
    - Ko~ko~da~yo~ O QuickShop tem uma nova atualização {0}~
    - Paimon quer lhe dizer que a QuickShop tem uma nova atualização {0}!
  remote-disable-warning: '<red>Esta versão do QuickShop está marcada como desabilitada pelo servidor remoto o que significa que esta versão pode ter problemas sérios, obtenha detalhes da nossa página do SpigotMC: {0}. Este aviso continuará a aparecer até que você mude para uma versão estável, mas isso não afetará a performance do seu servidor.'
purchase-out-of-stock: <red>Essa loja está sem itens no estoque, Chame o dono ou alguém da equipe caso for lojas de admin para re-estocar.
nearby-shop-entry: '<green>- Infoormação:{0} <green>Preço:<aqua>{1} <green>x:<aqua>{2} <green>y:<aqua>{3} <green>z:<aqua>{4} <green>distância: <aqua>{5} <green>bloco(s)'
chest-title: Loja QuickShop
console-only: <red>Este comando só pode ser executado pelo Console.
failed-to-put-sign: <red>Não há espaço suficiente na loja para colocar a placa de informações.
shop-name-unset: <red>Nome da loja removido agora
shop-nolonger-freezed: <green>Você descongelou a loja, de volta ao normal!
no-permission-build: <red>Você não pode construir uma loja aqui.
tableformat:
  left_half_line: <dark_purple>+--------------------
  right_half_line: <dark_purple>--------------------+
  full_line: <dark_purple>+---------------------------------------------------+
  left_begin: '<dark_purple>| '
quickshop-gui-preview: Pré-visualização de itens do QuickShop GUI
translate-not-completed-yet-click: A tradução do idioma {0} ainda não foi concluída por {1}. Você quer nos ajudar a melhorar a tradução? Clique aqui!
taxaccount-invalid: <red>A conta não é inválida, por favor insira um nome de jogador ou uuid(com traços).
player-bought-from-your-store: <red>{0} comprou {1} {2} da sua loja e você pagou {3} em impostos.
reached-maximum-can-create: <red>Você já criou o máximo de lojas! ({0}/{1})
reached-maximum-create-limit: <red>Você atingiu o número máximo de lojas que você pode criar
translation-version: 'Versão de suporte: Hikari'
no-double-chests: <red>Você não pode criar a loja com baú duplo.
price-too-cheap: <red>O preço precisa ser maior do que <yellow>${0}
shop-not-exist: <red>Não há uma loja.
bad-command-usage: <red>Argumentos inválidos de comando!
cleanghost-warning: <yellow>This command will purge <red>all</red> shops if the shop is corrupted, was created in not allowed worlds, is selling/buying not allowed items or <red><bold>EXISTS IN A UNLOADED WORLD</bold></red>. Make sure to create a full backup of your shop data first and use <aqua>/qs cleanghost confirm</aqua> to continue.
cleanghost-starting: <green>Starting to check for ghost shops (missing container blocks). All non-existing shops will be removed...
cleanghost-deleting: <yellow>Found a corrupted shop <aqua>{0}</aqua> because {1}, mark it to delete...
cleanghost-deleted: <green>Total <yellow>{0}</yellow> shops have been deleted.
shop-purchase-cancelled: <red>Compra da loja cancelada.
bypassing-lock: <red>Ignorarando um bloqueio da loja!
bungee-cross-server-msg: '<yellow>QuickShop CSM: <green>{0}'
saved-to-path: O Backup foi salvo em {0}.
shop-now-freezed: <green>Você congelou a loja, portanto ninguém pode negociar com esta loja agora!
price-is-now: <green>O novo preço desta loja é <yellow>{0}
shops-arent-locked: <red>Lembre-se, loja NÃO são protegidas contra roubo! Se você quer para os ladões, tranque-os com LWC, Lockette, etc!
that-is-locked: <red>Essa loja está bloqueada.
shop-has-no-space: <red>A loja só tem espaço para mais {0} de {1}.
safe-mode-admin: '<red><bold>AVISO: <red>O QuickShop neste servidor agora rodando em modo seguro, nenhum recurso funcionará, por favor digite o comando <yellow>/qs <red> para verificar quaisquer erros.'
shop-stock-too-low: <red>A loja possui somente {0} {1} restantes!
world-not-exists: <red>O mundo <yellow>{0}<red> não existe
how-many-sell: <green>Digite no chat, quantos você deseja <aqua>VENDER<green>. Você pode vender <yellow>{0}<green>. Digite <aqua>{1}<green> para vender todos.
shop-freezed-at-location: <yellow>A sua loja {0} em {1} foi congelada!
translation-contributors: 'Contribuidores: Torre de Timor, Netherfoam, KaiNoMood, Mgazul, JackTheChicken e Andre_601'
empty-success: <green>Esvaziando loja com sucesso
taxaccount-set: <green>A taxa da loja foi setada para <yellow>{0}
support-disable-reason:
  hot-reload: <yellow>Unsupported hot reloaded, restart the server and check again.
  outdated: <yellow>This version of QuickShop already outdated, up-to-date before requesting support!
  bad-hosts: |-
    <yellow>This server HOSTS has been modified and QuickShop some functions require connect to Mojang API correctly. Fix the HOSTS before asking support, Windows at C:\windows\system32\drivers\etc\hosts, Linux at /etc/hosts.
  privacy: <yellow>This server running under cracked (offline) mode. If you are running bukkit server under proxy and online-mode=true on proxy, please configure the proxy related settings correctly.
  modified: <yellow>File integrity check failed, this QuickShop build has been modified by others.
  consolespamfix-installed: <yellow>ConsoleSpamFix installed, it will hidden exception details, temporarily disable it during asking support.
  authlib-injector-detected: <yellow>This server running under 3rd-party authlib provider like authlib-injector.
  unsupported-server-software: <yellow>Unsupported server software, Any modded hybrid server software is unsupported including MCPC, Cauldron, CatServer, Mohist, Magma, Fukkit, Cardboard etc.
supertool-is-disabled: <red>Super-ferramenta foi desativada, não será possível quebrar nenhuma loja.
unknown-owner: Desconhecido
restricted-prices: '<red>Preço restrito para {0}: Mínimo de {1}, máximo de {2}'
nearby-shop-this-way: <green>A loja está a {0} blocos de você.
owner-bypass-check: <yellow>Ignorado todas as verificações, operação bem sucedida! (Agora <newline>você é o dono da loja!)
april-rick-and-roll-easter-egg: "<hover:show_text:'Click to open in browser for time limited rewards!'><click:open_url:'https://www.youtube.com/watch?v=dQw4w9WgXcQ'><green>Click here to acquire your time limited rewards that provided by QuickShop-Hikari developer!</green></click></hover>"
signs:
  item-right: ''
  out-of-space: Sem espaço
  unlimited: Ilimitado
  stack-selling: Vendendo {0}
  stack-price: '{0} por {1}x {2}'
  status-unavailable: <red>
  out-of-stock: Sem Estoque
  stack-buying: Comprando {0}
  freeze: Trading desabilitado
  price: '{0} cada'
  buying: Comprando {0}
  header: '{1}{0}'
  header-available: <green>{0}
  header-unavailable: <red>{0}
  selling: Vendendo {0}
  status-available: <green>
  item-left: ''
negative-amount: <red>Você não pode trocar com valores negativos
display-turn-on: <green>Você ativou o display de item.
shop-staff-deleted: <green>Removido com sucesso {0} como membro da equipe da sua loja.
nearby-shop-header: '<green>Procurando loja com item <aqua>{0}<green>:'
backup-failed: Não é possível fazer backup do banco de dados, verifique o console para detalhes.
shop-staff-cleared: <green>Todos os membros da equipe de sua loja foram removidos.
price-too-high: <red>O preço da loja é muito alto! Você não pode criar uma que tenha o preço superior a {0}.
plugin-cancelled: '<red>Operation cancelled, Reason: {0}'
player-sold-to-your-store: <green>{0} vendeu {1} {2} à sua loja.
shop-out-of-stock: <dark_purple>Sua loja em {0}, {1}, {2} ficou sem {3}!
how-many-buy: <green>Digite no chat, quantos você deseja <aqua>COMPRAR<green>. Você pode comprar <yellow>{0}<green>. Digite <aqua>{1}<green> para comprar todos.
language-info-panel:
  help: 'Nos Ajude: '
  code: 'Código: '
  name: 'Idioma: '
  progress: 'Progresso: '
  translate-on-crowdin: '[Traduza no Crowdin]'
item-not-exist: <red>O item <yellow>{0} <red>não existe, por favor, verifique a ortografia.
shop-creation-failed: A criação da <red>Shop falhou, por favor, contate o administrador do servidor.
inventory-space-full: <red>Your inventory space remains can only put <green>{1}x <red>items into it, try empty the your inventory!
no-creative-break: <red>Você não pode quebrar lojas de outros jogadores no modo criativo, mude para o modo sobrevivência ou tente usar a super-ferramenta {0}.
booleanformat:
  success: <green>✔
  failed: <red>✘
controlpanel:
  stack: '<green>Por valor em massa: <aqua>{0} <yellow>[<light_purple><bold>Alterar<yellow>]'
  price-hover: <yellow>Clique para definir um novo preço para o item.
  remove: <red><bold>[Remover Loja]
  mode-buying-hover: <yellow>Clique para trocar a loja para o modo VENDA.
  empty: '<green>Vazio: Remover todos os itens <yellow>[<light_purple><bold>OK<yellow>]'
  stack-hover: <yellow>Clique para definir o valor do pacote. Deixe 1 para comportamento normal.
  alwayscounting-hover: <yellow>Click to toggle if the shop is always counting container.
  alwayscounting: '<green>Sempre contando: {0} <yellow>[<light_purple><bold>Altere<yellow>]'
  setowner: '<green>Proprietário: <aqua>{0} <yellow>[<light_purple><bold>Mudar<yellow>]'
  freeze: '<yellow>Modo de bloqueio: <aqua>{0} <yellow>[<light_purple><bold>Toggle<yellow>]'
  price: '<green>Preço: <aqua>{0} <yellow>[<light_purple><bold>Mudar<yellow>]'
  currency-hover: <yellow>Clique para definir ou remover a moeda que esta loja está usando
  lock: '<yellow>Bloqueio de Loja: <aqua>{0} <yellow>[<light_purple><bold>Toggle<yellow>]'
  mode-selling: '<green>Modo da loja: <yellow>VENDA <yellow>[<light_purple><bold>Mudar<yellow>]'
  currency: '<green>Moeda: <aqua>{0} <yellow>[<light_purple><bold>Definir<yellow>]'
  setowner-hover: <yellow>Clique para alterar o proprietário.
  mode-buying: '<green>Modo da loja: <aqua>COMPRA <yellow>[<light_purple><bold>Mudar<yellow>]'
  item: '<green>Comprar item: {0} <yellow>[<light_purple><bold>Mudar<yellow>]'
  unlimited: '<green>Estoque ilimitado: {0} <yellow>[<light_purple><bold>Mudar<yellow>]'
  unlimited-hover: <yellow>Clique para alternar se a loja é ilimitada.
  refill-hover: <yellow>Clique para re-estocar a loja.
  remove-hover: <yellow>Clique para remover esta loja.
  toggledisplay-hover: <yellow>Ativar o display de itens nessa loja
  refill: '<green>Repreencher: Recarregue os itens <yellow>[<light_purple><bold>OK<yellow>]'
  freeze-hover: <yellow>Alterna o status de congelamento da loja.
  lock-hover: <yellow>Enable/Disable proteção de bloqueio de loja
  item-hover: <yellow>Clique para mudar o item da loja
  infomation: '<green>Painel de Controle da Loja:'
  mode-selling-hover: <yellow>Clique para trocar a loja para o modo de COMPRA.
  empty-hover: <yellow>Clique para limpar o inventário da loja.
  toggledisplay: '<green>Item de Exibição: <aqua>{0} <yellow>[<light_purple><bold>Ativar/Desativar<yellow>]'
  history: '<green>History: <yellow>[<bold><light_purple>View</light_purple></bold>]</yellow>'
  history-hover: <yellow>Click to view shop history logs
timeunit:
  behind: atras de
  week: "{0} week"
  weeks: "{0} weeks"
  year: "{0} year"
  before: antes de
  scheduled: agendado
  years: "{0} years"
  scheduled-in: Agendado para {0}
  second: "{0} second"
  std-past-format: '{5}{4}{3}{2}{1}{0}atrás'
  std-time-format: HH:mm:ss
  seconds: "{0} seconds"
  hour: "{0} hour"
  scheduled-at: agendado em {0}
  after: após
  day: "{0} day"
  recent: recente
  between: entre
  hours: "{0} hours"
  months: "{0} months"
  longtimeago: há muito tempo atrás
  between-format: entre {0} e {1}
  minutes: "{0} minutes"
  justnow: neste momento
  minute: "{0} minute"
  std-format: MM/dd/yyyy HH:mm:ss
  future-plain-text: futuro
  month: "{0} month"
  future: em {0}
  days: "{0} days"
command:
  reloading: '<green>Configuração recarregada. <yellow>Algumas alterações podem exigir reinicialização para afetar. <newline><gray>(Aviso: Recarregar o comportamento após 4..9.10 agora só recarregamos a configuração mas não todo o plugin para garantir que o servidor não falhe.)'
  description:
    buy: <yellow>Converte uma loja para o modo de <aqua><bold>COMPRA<reset>
    about: <yellow>Mostrar info do QuickShop
    language: <yellow>Alterar o idioma atualmente utilizado
    purge: <yellow>Iniciar a tarefa de limpar lojas em segundo plano
    paste: <yellow>Carrega os dados do servidor para o Pastebin
    title: <green>Ajuda do QuickShop
    remove: <yellow>Remove a loja que você está olhando
    ban: <yellow>Bane um jogador da loja
    empty: <yellow>Remove todos os itens de uma loja
    alwayscounting: <yellow>Definir se a loja sempre contando o contêiner, mesmo que seja ilimitado
    setowner: <yellow>Altera o proprietário de uma loja.
    reload: <yellow>Recarrega o config.yml do QuickShop
    freeze: <yellow>Desativar ou Ativar o comércio de lojas
    price: <yellow>Altera o preço de compra/venda de uma loja
    find: <yellow>Localiza a loja mais próxima de um tipo específico.
    create: <yellow>Cria uma nova loja a partir do baú alvo
    lock: <yellow>Alterne o status de bloqueio da loja
    currency: <yellow>Definir ou remover as configurações de moeda da loja
    removeworld: <yellow>Remover TODAS as lojas em um mundo específico
    info: <yellow>Mostra estatísticas do QuickShop
    owner: <yellow>Altera o proprietário de uma loja.
    amount: <yellow>Para definir a quantidade do item (Útil quando tiver problemas de chat)
    item: <yellow>Trocar item da loja de uma loja
    debug: <yellow>Ativa o modo desenvolvedor
    unlimited: <yellow>Dá um estoque ilimitado para a loja.
    sell: <yellow>Converte uma loja para o modo de <yellow><bold>VENDA<reset>
    fetchmessage: <yellow>Mostrar mensagens da loja não lidas
    staff: <yellow>Gerenciar a sua equipe na loja
    clean: <yellow>Remove todas as lojas (carregadas) sem estoque
    refill: <yellow>Adiciona um dado número de itens a uma loja
    help: <yellow>Mostrar ajuda do QuickShop
    removeall: <yellow>Remover TODAS as lojas de um jogador
    unban: <yellow>Desbane um jogador da loja
    transfer: <yellow>Transfira TODAS as lojas de alguém para outra
    transferall: <yellow>Transfira TODAS as lojas de alguém para outra
    transferownership: <yellow>Transfer the shop you're looking at to someone else
    size: <yellow>Altera a quantidade por pacote da loja
    supercreate: <yellow>Cria uma loja ignorando todas as verificações de proteção
    taxaccount: <yellow>Definir a conta de taxa que a loja usa
    name: <yellow>Nomear uma loja para um nome específico
    toggledisplay: <yellow>Ativar o display de itens nessa loja
    permission: <yellow>Gerenciamento de permissões da loja
    lookup: <yellow>Gerenciar tabela de itens pesquisáveis
    database: <yellow>Ver e manter o banco de dados da Loja Rápida
    benefit: <yellow>Configurações de divisão de benefícios entre o dono da loja e outros jogadores
    tag: <yellow>Add, remove or query tags of a shop
    suggestprice: <yellow>Suggest a recommend price for looking shop, based on other shops
    history: <yellow>View the history transactions of a shop
    sign: <yellow>Change the sign material of a shop
  bulk-size-not-set: '<red>Use: /qs find <amount>'
  no-type-given: '<red>Use: /qs find <item>'
  feature-not-enabled: Esse recurso não está ativado no arquivo de configuração.
  now-debuging: <green>Modo de desenvolvedor <red>ativado <green>com sucesso. Recarregando QuickShop...
  no-amount-given: <red>Nenhuma quantidade fornecida. Use <green>/qs refill <amount><red>
  no-owner-given: <red>Nenhum proprietário informado
  disabled: '<red>Este comando está desativado: <yellow>{0}'
  bulk-size-now: <green>Agora negociando <yellow>{0}x {1}
  toggle-always-counting:
    counting: <green>Shop is now always counting container even is unlimited
    not-counting: <green>Shop agora é respeitado se a loja é ilimitada
  cleaning: <green>Removendo lojas sem estoque...
  now-nolonger-debuging: <green>Modo de Desenvolvedor <red>desabilitado <green>com sucesso. Recarregando QuickShop...
  toggle-unlimited:
    limited: <green>Loja agora é limitada
    unlimited: <green>Loja agora é ilimitada
  transfer-success-other: <green>Transferido <yellow>{0} lojas de {1}<green> para <yellow>{2}
  no-trade-item: <green>Por favor, segure um item de troca para mudar na mão principal
  wrong-args: <red>Argumento inválido. Use <bold>/qs help <red>para ver uma lista de comandos.
  some-shops-removed: <yellow>Loja(s) {0} <green>removida(s)
  new-owner: '<green>Novo proprietário: <yellow>{0}'
  format: <green>/{0} {1} <yellow>- {2}
  transfer-success: <green>Transferido <yellow>{0} <green>shop(s) para <yellow>{1}
  now-buying: <green>Agora <light_purple>COMPRANDO <yellow>{0}
  now-selling: <green>Agora <aqua>VENDENDO <yellow>{0}
  cleaned: <green>Removido <yellow>{0}<green> lojas.
  trade-item-now: <green>Agora negociando <yellow>{0}x {1}
  no-world-given: <red>Por favor, especifique um nome do mundo
  format-disabled: <red>/{0} {1} <gray>- {2}
  invalid-bulk-amount: <red>O valor {0} é maior que o tamanho máximo da pilha ou menor que um
currency-not-support: <red>O plugin de economia não suporta o recurso de multi-economia.
trading-in-creative-mode-is-disabled: <red>Você não pode negociar com a loja no modo criativo.
the-owner-cant-afford-to-buy-from-you: <red>Isto custa {0}, mas o dono da loja só tem {1}
you-cant-afford-shop-naming: <red>Você não pode permitir o nome de lojas, custa {0} para ser nomeado.
inventory-error: |-
  <red>Failed to processing the InventoryWrapper. Did you using addon to re-binding the shop Inventory? Information: Exception={0}, InventoryWrapper={1}, WrapperType={2}, WrapperProvider={3}, SymbolLink={4}. Please contact the server administrators.
integrations-check-failed-create: <red>Integração {0} negou a criação da loja
shop-out-of-space: <dark_purple>A sua loja em {0}, {1}, {2} está cheia.
admin-shop: AdminShop
no-anythings-in-your-hand: <red>Não há nada na sua mão.
no-permission: <red>Você não tem permissão para fazer isso.
chest-was-removed: <red>O baú foi removido.
you-cant-afford-to-buy: <red>Isto custa {0}, mas você só tem {1}
shops-removed-in-world: <yellow>Total <aqua>{0}<yellow> lojas foi excluído no mundo <aqua>{1}<yellow>.
display-turn-off: <green>Você desativou o display de item.
client-language-unsupported: <yellow>O plugin não suporta sua linguagem, voltamos a usar a linguagem {0} com você.
language-version: '63'
not-managed-shop: <red>Você não é o proprietário ou moderador desta loja
shop-cannot-trade-when-freezing: <red>Você não pode negociar com esta loja porque ela está congelada.
invalid-container: <red>Recipiente inválido, você só pode criar a loja no bloco que tem inventário.
permission:
  header: <green>Detalhes de permissão da loja
  header-player: <green>Detalhes de permissão da loja para {0}
  header-group: <green>Detalhes de permissão da loja para o grupo {0}
  table: <gold><b>{0}</b>:</gold> <gray>{1}</gray>
  item:
    purchase: <yellow>Permissão para permitir que os usuários que tem isso para comprar a loja. (incluindo comprar e vender)
    show-information: <yellow>Permissão para permitir que usuários que têm isso para visualizar as informações da loja. (painel de informações da loja)
    preview-shop: <yellow>Permissão para permitir que usuários que tem esta permissão para pré-visualizar a loja. (pré-visualização)
    search: <yellow>Permissão para permitir que usuários que têm esta permissão para pesquisar na loja desejada. (Remover permissão irá ocultar a loja do resultado da pesquisa)
    delete: <yellow>Permissão para permitir que usuários que tem esta permissão para excluir a loja.
    receive-alert: <yellow>Permissão para permitir que os usuários que tem isso para receber mensagens de alertas (por exemplo, fora do estoque ou novas negociações).
    access-inventory: <yellow>Permissão para permitir que os usuários que têm isto acessem o inventário da loja.
    ownership-transfer: <yellow>Permissão para permitir que os usuários que têm isso para transferir o proprietário da loja.
    management-permission: <yellow>Permissão para permitir que usuários que têm isso para gerenciar as permissões dos grupos e editar o grupo de usuários.
    toggle-display: <yellow>Permissão para permitir que usuários que tem isso para alternar o item de exibição de loja.
    set-shoptype: <yellow>Permissão para permitir que usuários que tem isso para definir o tipo da loja (compra ou venda de bruxa).
    set-price: <yellow>Permissão para permitir que os usuários que tem isso para definir o preço da loja.
    set-item: <yellow>Permissão para permitir que usuários que tem isso para definir o item da loja.
    set-stack-amount: <yellow>Permissão para permitir que usuários que têm isto para definir o valor da pilha na loja.
    set-currency: <yellow>Permissão para permitir que usuários que têm isso para configurar a moeda de loja.
    set-name: <yellow>Permissão para permitir que usuários que têm isso para definir o nome da loja.
    set-sign-type: <yellow>Change the sign material that attached on the shop.
    view-purchase-logs: <yellow>Permission to view shop purchase logs.
  group:
    everyone: <yellow>Grupo padrão para todos os usuários, exceto para proprietários de lojas.
    staff: <yellow>Grupo de sistema para funcionários das lojas.
    administrator: <red>Grupo de sistemas para administradores, os usuários deste grupo terão permissões quase iguais com o proprietário da loja.
invalid-group: <red>Nome de grupo inválido.
invalid-permission: <red>Permissão inválida.
invalid-operation: <red>Operação inválida, somente {0} são permitidos.
player-no-group: <yellow>Jogador {0} não está em nenhum grupo nesta loja.
player-in-group: <green>O jogador {0} está no grupo <aqua>{1}</aqua> desta loja.
permission-required: <red>Você não tem permissão de {0} nesta loja para fazer isso.
no-permission-detailed: <red>Você não tem permissão para <yellow>{0}</yellow> para fazer isso.
paste-notice: "<yellow>Note: If you are creating a Paste for troubleshooting purposes, be sure to reproduce the error before creating the Paste as quickly as possible; we need the logs retained briefly in the buffer to troubleshoot. If you create a Paste too slowly or without first reproducing the error or restarted server, the Paste will record nothing and useless."
paste-uploading: <aqua>Aguarde... Enviando a colagem para colar......
paste-created: '<green>Colar criado, clique para abrir no navegador: <yellow>{0}</yellow><newline><red>Aviso: <gray>Nunca envie colar para quem você não confia.'
paste-created-local: |-
  <green>Colar criado, clique para abrir no navegador: <yellow>{0}</yellow><newline><red>Aviso: <gray>Nunca envie colar para quem você não confia.
paste-created-local-failed: <red>Não foi possível salvar a pasta em seu disco local. Verifique o seu disco.
paste-451: |-
  <gray><b>DICA: </b> Seu país ou região parece ter bloqueado o serviço de funcionários da CloudFlare e a colagem do QuickShop pode não ser carregada corretamente.<newline><gray>Se a operação subsequente falhar, tente adicionar o parâmetro adicional --file para gerar um colar local: <dark_gray>/qs colar --file</dark_gray>
paste-upload-failed: <red>Falha ao carregar a pasta para {0}, tentar outro provedor de colagem...
paste-upload-failed-local: <red>Falha ao enviar, tentando gerar a colagem local...
command-incorrect: '<red>Uso do comando incorreto, digite /qs ajuda para verificar a ajuda. Uso: {0}.'
successfully-set-player-group: <green>O grupo {0} do jogador foi definido com sucesso para <aqua>{1}</aqua>.
successfully-unset-player-group: <green>O grupo de jogadores foi removido com sucesso nesta loja.
successfully-set-player-permission: <green>Permissão {0} de jogador <aqua>{1}</aqua> definida com sucesso na loja <aqua>{2}</aqua>.
lookup-item-created: <green>Um item chamado <aqua>{0}</aqua> foi criado na tabela de pesquisa. Você pode consultar este item nas configurações agora.
lookup-item-exists: <red>Um item chamado <yellow>{0}</yellow> já existe na tabela de pesquisas, exclua-o ou escolha outro nome.
lookup-item-not-found: <red>Um item chamado <yellow>{0}</yellow> não existe.
lookup-item-name-illegal: <red>Nome do item ilegal. Apenas caracteres alfanuméricos e sublinhados são permitidos.
lookup-item-name-regex: '<red>O nome deve corresponder a este regex: <aqua>{0}</aqua>.'
lookup-item-test-not-found: '<gold>Teste: <yellow>O item da mão não está registrado na tabela de pesquisa.'
lookup-item-test-found: '<gold>Teste: <green>O item da mão foi registrado como nome <aqua>{0}</aqua> na tabela de pesquisa.'
lookup-item-removed: <green>O item especificado <aqua>{0}</aqua> foi removido com sucesso da tabela de pesquisa.
internal-error: <red>Um erro interno ocorreu, entre em contato com o administrador do servidor.
argument-cannot-be: <red>O argumento <aqua>{0}</aqua> não pode ser <yellow>{1}</yellow>.
argument-must-between: <red>O valor do argumento <aqua>{0}</aqua> deve ser entre <yellow>{1}</yellow> e <yellow>{2}</yellow>
invalid-percentage: <red>Porcentagem inválida, você deve adicionar '%' após o número de porcentagem.
display-fallback: |-
  <red>Due to an internal failure, a fallback message is being displayed.
  The value of this item may not be localized or processed correctly.
  Please contact the server administrator.
not-a-valid-time: |-
  <red>A string não <yellow>{0}</yellow> um carimbo de data/hora válido, insira um <aqua>Zulu Time (ISO 8601)</aqua> ou <aqua>Unix Epoch Time em segundos</aqua>.
  <gold>Exemplo de carimbo de data/hora válido: (para Sat, 17 Dec 2022 10:31:37 GMT)</gold>
  <aqua>-<aqua> <yellow>2022-12-17T10:31:37Z</yellow> <grey>(Zulu Time)</grey>
  <aqua>-<aqua> <yellow>1671273097</yellow> <grey>(Unix Epoch Time em segundos)</grey>
invalid-past-time: <red>Você não pode especificar um horário no passado.
debug:
  arguments-invalid: <red>Provided arguments <yellow>{0}</yellow> invalid.
  sign-located: '<green>Sign valid: <yellow>{0}</yellow>.'
  operation-missing: <red>You must specific an operation.
  operation-invalid: <red>You must specific an valid operation.
  invalid-base64-encoded-sql: <red>The SQL provided must be base64 encoded.
  warning-sql: |-
    <red><bold>Warning:</bold></red><yellow>You're executing a SQL statement, this may corrupt your database or destroy any data in database even it belongs to other plugins.
    <red>Don't confirm if you don't trust who send you this command.
  warning-sql-confirm: <yellow>To confirm this dangerous operation, please type <aqua>/qs debug database sql confirm {0}</aqua> in 60 seconds.
  warning-sql-confirm-hover: <yellow>Click to confirm this dangerous operation.
  sql-confirm-not-found: <yellow>Didn't found the operation that you given, it may invalid or expired.
  sql-executing: '<yellow>Executando declaração SQL: <aqua>{0}</aqua>'
  sql-completed: <green>Completed, {0} rows affected.
  sql-exception: <red>An error occurred while executing the SQL query, check the Console for details!
  sql-disabled: '<red>For security reason, SQL queries are disabled in this server, If you really need this feature, you can add the following flag to startup arguments: <aqua>{0}</aqua> with `true` value to enable it.'
  force-shop-reload: <yellow>Forcing reload all loaded shops...
  force-shop-reload-complete: <green>Forced <aqua>{0}</aqua> shops to be reloaded.
  force-shop-loader-reload: <yellow>Forcing shop-loader reloading...
  force-shop-loader-reload-unloading-shops: <yellow>Unloading <aqua>{0}</aqua> loaded shops...
  force-shop-loader-reload-unloading-shops-from-memory: <yellow>Removing <aqua>{0}</aqua> shops from memory...
  force-shop-loader-reload-reloading-shop-loader: <yellow>Re-calling shop-loader for reloading all shops from database...
  force-shop-loader-reload-complete: <green>Shop-loader has been reloaded all shops!
  toggle-shop-loaded-status: <aqua>Toggle this shop loaded status to <gold>{0}</gold>
  shop-internal-data: '<yellow>The internal data of this shop: <reset>{0}</reset>'
  handler-list-not-valid-bukkit-event-class: <red>The provided class <yellow>{0}</yellow> is not a valid Bukkit event class.
  update-player-shops-signs-no-username-given: <red>You must provide a valid player username.
  update-player-shops-signs-create-async-task: <yellow>Creating async tasks for force updating signs...
  update-player-shops-player-selected: '<yellow>Player selected: <gold>{0}</gold>'
  update-player-shops-player-shops: <yellow>Total <gold>{0}</gold> shops waiting for updates.
  update-player-shops-per-tick-threshold: '<yellow>Max shops can be update per tick: <gold>{0}</gold>'
  update-player-shops-complete: <green>Task completed, Used <yellow>{0}ms</yellow> for updating.
  update-player-shops-task-started: <gold>The tasks has been started, please wait for it to complete.
  item-info-store-as-string: "<green>The store you looking at: <gold>{0}</gold> Hash: <white>{1}</white>"
  item-info-hand-as-string: "<green>The item in your hand: <gold>{0}</gold> Hash: <white>{1}</white>"
  item-matching-result: "<green>Hand2Store: <aqua>{0}</aqua>, Store2Hand: <aqua>{1}</aqua>"
  hikari-cp-size-tweak: "<green>HikariCP MaximumPoolSize and MinimumIdle was set to <white>{0}</white>"
  hikari-cp-testing: "<green>Please wait, testing HikariCP connection..."
  hikari-cp-working: "<green>Pass! HikariCP working well!"
  hikari-cp-not-working: "<red>Failed! The connection that returned by HikariCP is dead! (Not passed test in 1 second)"
  hikari-cp-timeout: "<red>HikariCP is timed out while getting a valid connection, please clean up all active queries to release connection resources."
  queries-stopped: "<green>Stopped <white>{0}</white> active queries."
  queries-dumping: "<yellow>Dumping active queries..."
  restart-database-manager: "<yellow>Restarting SQLManager..."
  restart-database-manager-clear-executors: "<yellow>Clearing executors..."
  restart-database-manager-unfinished-task: "<yellow>Unfinished task: <white>{0}</white>"
  restart-database-manager-unfinished-task-history-query: "<yellow>Unfinished task (History Query): <white>{0}</white>"
  restart-database-manager-reconnect: "<yellow>Re-launching SQLManager via initial sequence (via async executor)"
  restart-database-manager-done: "<green>Done!"
  property-incorrect: "<yellow>You must enter a (and only one) property key=value set. E.g   aaa=bbb"
  property-security-block: "<red>Request was rejected, for security reason, you can only change the property that startsWith <aqua>com.ghostchu.quickshop</aqua> or <aqua>quickshop</aqua>."
  property-removed: "<green>Removed property key <white>{0}</white>"
  property-changed: "<green>Property key <white>{0}</white> was changed from <white>{1}</white> to <white>{2}</white>"
  marked-as-dirty: "<green>Marked all stores as dirty status, they will be force save in next auto-save task. (Restart the server to force execute store saving task)"
  display-removed: "<green>Successfully removed <yellow>{0}</yellow> QuickShop display items/entities from the worlds."
database:
  scanning: <green>Verificando os dados isolados nos bancos de dados QuickShop, o carregamento do banco de dados pode aumentar em progresso de verificação, isso pode demorar um pouco...
  scanning-async: <yellow>Scanning the isolated data in QuickShop databases on async task thread, the database load may increase in scan progress, this might need a while, try again later.
  already-scanning: <red>An scan task has been started, please wait until it finished.
  trim-warning: '<yellow><red><bold>Aviso: </red>Faça backup do seu banco de dados antes de continuar a limpeza do banco de dados para evitar a perda dos dados. Uma vez que estiver pronto, execute <aqua>/qs banco de dados aparar confirmação</aqua> para continuar.'
  status: '<yellow>Estado: {0}'
  status-good: <green>Bom
  status-bad: <yellow>Manutenção necessária
  isolated: '<yellow>Dados isolados:'
  isolated-data-ids: '<yellow><aqua>+</aqua> Registros de Dados: <gold>{0}</gold>'
  isolated-shop-ids: '<yellow><aqua>+</aqua> Índices da loja: <gold>{0}</gold>'
  isolated-logs: '<yellow><aqua>「</aqua> Logs: <gold>{0}</gold>'
  isolated-external-caches: '<yellow><aqua>「</aqua> Caixas Externas: <gold>{0}</gold>'
  last-purge-time: <yellow>Última vez que aparar as {0}
  report-time: <yellow>Última verificação em {0}
  auto-scan-alert: <yellow>O banco de dados da Loja Rápida neste servidor requer uma manutenção. Encontrado <gold>{0}</gold> dados isolados aguardando desmontagem.
  auto-trim: <green>O corte automático foi ativado neste servidor, não é necessário recortar manualmente.
  trim-complete: <green>Banco de dados aparado concluído, <yellow>{0}</yellow> isolado recortado.
  auto-trim-started: <green>Auto trim has been started, please wait...
  trim-start: <green>Database trim started, please wait...
  trim-exception: <red>Database trim failed, an exception raised during database trimming, check the server console.
  generated-at: '<yellow>Gerado em: <gold>{0}</gold>'
  purge-date: <red>Você deve informar uma data.
  purge-warning: <yellow>This operation will purge the history that stored in your database including shop creates/changes/deletes, purchases, transactions and system logs. Delete those data can free the disk space but all history metric will lost, and other plugins that depend on metrics will stop working. To continue execute this operation, execute `/qs database purgelogs <before-days> confirm`
  purge-task-created: <green>Task created! Database are silent purging history records in background.
  purge-done-with-line: <green>Purge task completed, total <gold>{0}</gold> lines purged from database.
  purge-done-with-error: <red>Purge task failed, check the server console for details.
  purge-players-cache: <yellow>Please wait, purging players caches...
  purge-players-completed: |-
    <green>Successfully purged {0} players caches from both memory and database.<newline><aqua>Note: You server performance may impact by this operation.
  purge-players-error: <red>Failed to purge players caches, please check the server console.
  suggestion:
    trim: <yellow>This database require a isolated data trim, execute command <aqua>/qs database trim</aqua> to trim the database.
always-counting-removal-early-warning: <red>Always Counting feature is scheduled for removal, you shouldn't use it anymore and will stop work in future.
exporting-database: <green>Exportando banco de dados, aguarde...
exporting-failed: <red>Falha ao importar banco de dados, por favor, verifique o console do servidor.
exported-database: <green>Banco de dados exportado para <yellow>{0}</yellow>.
importing-not-found: <red>The <yellow>{0}</yellow> file not found, please check the file path.
importing-early-warning: |-
  <yellow><red><bold>Warning: </bold></red>The backup will be imported to the current database, any exists data will be purged and permanently lost if you don't have a backup.<newline><red>Are you sure to continue the import progress?</red><yellow>Type <aqua>/qs recovery confirm</aqua> to continue.
importing-database: <green>Importando banco de dados do backup, aguarde...
importing-failed: <red>Falha ao importar banco de dados, por favor, verifique o console do servidor.
imported-database: <green>Banco de dados importado de <yellow>{0}</yellow>.
transfer-sent: <green>Shops transfer request sent to <yellow>{0}</yellow>.
transfer-request: <yellow>Player <aqua>{0}</aqua> requested to transfer their shops to you. Did you want accept the transfer?
transfer-single-request: <yellow>Player <aqua>{0}</aqua> would like to transfer a shop to you. Do you want to accept the request?
transfer-ask: |-
  <gold>Type <red>/qs transfer accept</red> to accept.</gold><newline><gold>Type <red>/qs transfer deny</red> to deny.</gold><newline><gold>Request will expire after <red>{0}</red> seconds.
transferall-ask: |-
  <gold>Type <red>/quickshop transferall accept</red> to accept or <red>/quickshop transferall deny</red> to deny.
  The request will expire after <red>{0}</red> seconds.
transfer-single-ask: |-
  <gold>Type <red>/quickshop transferownership accept</red> to accept or <red>/quickshop transferownership deny</red> to deny.
  The request will expire after <red>{0}</red> seconds.
transfer-accepted-fromside: <green>Player <aqua>{0}</aqua> accepted your shop transfer request.
transfer-accepted-toside: <green>You accepted <aqua>{0}</aqua>'s transfer request.
transfer-rejected-fromside: <red>Player <aqua>{0}</aqua> rejected your shop transfer request.
transfer-rejected-toside: <red>You rejected <aqua>{0}</aqua>'s shop transfer request.
transfer-no-pending-operation: <red>You don't have pending transfer request yet.
transfer-no-self: <red>You can't transfer your shops to yourself.
benefit-overflow: <red>The sum of all benefits cannot be greater than or equal to 100%.
benefit-exists: <red>Target player already in benefits list on this shop.
benefit-removed: <red>Target player has been removed from shop benefits.
benefit-added: <green>Player <aqua>{0}</aqua> has been added into shop benefits!
benefit-updated: <green>Player <aqua>{0}</aqua>'s benefits has been updated!
benefit-query: <green>This shop have <yellow>{0}</yellow> players in benefits list!
benefit-query-list: <yellow> - </yellow><white>Player <gold>{0}</gold>, Benefit <gold>{1}%</gold></white>
tag-added: '<green>Successfully added <aqua>#{0}</aqua> to this shop!'
tag-add-duplicate: '<red>The tag <aqua>#{0}</aqua> already exists at this shop!'
tag-removed: '<green>Successfully removed <aqua>#{0}</aqua> from this shop!'
tag-remove-not-exists: 'The tag <aqua>#{0}</aqua> not exists at this shop!'
tag-cleared: <green>Successfully cleared all tags from this shop!
tag-shops-cleared: '<green>Successfully cleared <aqua>#{0}</aqua> from all your tagged shops!'
tag-query: '<green>This shop have <yellow>{0}</yellow> tags:'
tag-query-listing: '<yellow> - <aqua>#{0}</aqua>'
tag-query-no-tag: <red>This shop have no tags.
tag-query-shops: '<green>This tag contains <yellow>{0}</yellow> shops:'
tag-query-shops-listing: <yellow> - <aqua>{0}</aqua>
batch-operations-based-on-tags-no-failure: <green>Successfully batch processed <yellow>{0}</yellow> shops.
batch-operations-based-on-tags-have-failure: <yellow>Total <yellow>{0}</yellow> shops in batch processing successfully completed, but <red>{1}</red> requests not completed.
batch-operations-based-on-tags-have-failure-with-reason: '<yellow>Total <yellow>{0}</yellow> shops in batch processing successfully completed, but <red>{1}</red> requests not completed. Reason: <gold>{2}</gold>'
batch-operations-based-on-tags-have-failure-error-messages-may-appears-above: <gray>The error messages will send to you above this message in the chat.
addon:
  towny:
    commands:
      town: <yellow>Set or unset a shop to a Town shop
      nation: <yellow>Set or unset a shop to a Nation shop
    make-shop-owned-by-town: <green>You have made the shop owned by town <yellow>{0}</yellow>.
    make-shop-no-longer-owned-by-town: <green>You have reset shop ownership, it now transfer back to original shop owner.
    make-shop-owned-by-nation: <green>You have made the shop owned by nation <yellow>{0}</yellow>.
    make-shop-no-longer-owned-by-nation: <green>You have reset shop ownership, it now transfer back to original shop owner.
    shop-owning-changing-notice: <grey>This shop now owned by a town/nation, original shop owner has been automatically added into administrator list, modify or add any new owner / staff by using command /qs permission
    target-shop-already-is-town-shop: <red>The target shop is already owned by a town.
    target-shop-already-is-nation-shop: <red>The target shop is already owned by a nation.
    target-shop-not-in-town-region: <red>The target shop is not in the town.
    target-shop-not-in-nation-region: <red>The target shop is not in the nation.
    item-not-allowed: <red>This shop's item are not allowed used by town/nation shop, pick another one!
    operation-disabled-due-shop-status: <red>This shop operation has been disabled due this already is a town/nation shop.
    plot-type-disallowed: <red>You can't create town/nation shop on this type of plot.
    flags:
      own: <red>You can only create shops at your owned shop type towny plot.
      modify: <red>You didn't have build permission on this towny plot.
      shop-type: <red>You must create shop on a shop type towny plot.
  residence:
    creation-flag-denied: <red>You don't have permission to create shops in this residence.
    trade-flag-denied: <red>You don't have permission to purchase in this residence.
    you-cannot-create-shop-in-wildness: <red>You can't create shop in wildness.
  griefprevention:
    creation-denied: <red>You don't have permission to create shops in this claim.
    trade-denied: <red>You don't have permission to purchase shops in this claim.
  lands:
    world-not-enabled: <red>You are not allowed to create or purchase in this world.
    creation-denied: <red>You don't have permission to create shops in this Land area.
  plotsquared:
    no-plot-whitelist-creation: <red>You cannot create shop outside of Plot.
    no-plot-whitelist-trade: <red>You cannot purchase shop outside of Plot.
    creation-denied: <red>You don't have permission to create shops in this Plot.
    trade-denied: <red>You don't have permission to purchase shops in this Plot.
    flag:
      create: Create QuickShop-Hikari shops
      trade: Purchase QuickShop-Hikari shops
  superiorskyblock:
    owner-create-only: <red>Only island owner can create shop there.
    owner-member-create-only: <red>Only island owner or member can create shop there.
  worldguard:
    creation-flag-test-failed: <red>You don't have permission create shop in this WorldGuard area.
    trade-flag-test-failed: <red>You don't have permission trade shop in this WorldGuard area.
    reached-per-region-amount-limit: "<red>You have reached the maximum amount of shops in this region."
  discord:
    __to_translators: 'To translators: `addon.discord.discord-messages` use a special variable in strings, %%LANG_KEY%% instead of QuickShop''s style {LANG_INDEX}, do not translate anything inside double ''%'' section. BUT for any other section texts, it respect the normal QuickShop Text System syntax ({0},{1} etc.) This may be difficult to understand because two different variable forms are used in the same program at the same time, but trust me, it is reasonable. (you don''t need translate this string, it''s a comment)'
    __to_users: Like the QuickShop Text System, Discord addon will also auto-detect user language and use user's language to send Discord message, it following the QuickShop-Hikari's language system settings.
    __to_message_designer: 'Design your Discord message with GUI: https://glitchii.github.io/embedbuilder/, then copy the JSON code and paste it into the translation and here we go!'
    discord-enabled: <aqua>Successfully <green>enabled</green> your QuickShop discord messages, now you can receive the shop messages from Discord.
    discord-disabled: <aqua>Successfully <red>disabled</red> your QuickShop discord messages, now you won't receive the shop messages from Discord anymore.
    discord-not-integrated: <red>You hadn't link your Discord yet! Please link your Discord account first!
    feature-enabled-for-user: <aqua>You have <green>enabled</green> <gold>{0}</gold> notification.
    feature-disabled-for-user: <aqua>You have <red>disabled</red> <gold>{0}</gold> notification.
    link-help: <yellow>This server using <gold>{0}</gold> for Discord driver, please use <green>{0}</green> to link your Discord account.
    save-notifaction-exception: <red>An error occurred while saving your Discord notifaction settings, please contact the server administrator.
    feature-status-changed: <green>Successfully set the notifaction <aqua>{0}</aqua> status to <gold>{1}</gold>
    commands:
      discord:
        description: <yellow>Managing your QuickShop Discord settings
    discord-messages:
      sold-to-your-shop: |
        {
         "embed":
           {
             "title": ":inbox_tray: Somebody sold items to your shop",
             "description": "The player %%purchase.name%% sold x%%purchase.amount%% %%shop.item.name%% to your shop.",
             "color": 52084,
             "author": {
               "name": "QuickShop-Hikari",
               "icon_url": ""
             },
             "thumbnail": {
               "url": ""
             },
             "image": {
               "url": ""
             },
             "footer": {
               "text": "QuickShop-Hikari Discord Notification",
               "icon_url": ""
             },
             "fields": [
               {
                 "name": "Shop",
                 "value": "%%shop.display-name%%",
                 "inline": false
               },
               {
                 "name": "Purchaser",
                 "value": "%%purchase.name%%",
                 "inline": true
               },
               {
                 "name": "Item",
                 "value": "%%shop.item.name%%",
                 "inline": true
               },
               {
                 "name": "Amount",
                 "value": "%%purchase.amount%% (x%%shop.stacking-amount%%)",
                 "inline": true
               },
               {
                 "name": "Your paid",
                 "value": "%%purchase.balance-formatted%%",
                 "inline": true
               },
               {
                 "name": "Taxes",
                 "value": "%%purchase.taxes-formatted%%",
                 "inline": true
               }
             ]
           }
        }
      bought-from-your-shop: |
        {
           "embed":
             {
               "title": ":outbox_tray: Somebody bought items from shop",
               "description": "The player %%purchase.name%% bought x%%purchase.amount%% %%shop.item.name%% from your shop.",
               "color": 52084,
               "author": {
                   "name": "QuickShop-Hikari",
                   "icon_url": ""
               },
               "thumbnail": {
                   "url": ""
               },
               "image": {
                   "url": ""
               },
               "footer": {
                   "text": "QuickShop-Hikari Discord Notification",
                   "icon_url": ""
               },
               "fields": [
                   {
                       "name": "Shop",
                       "value": "%%shop.display-name%%",
                       "inline": false
                   },
                   {
                       "name": "Purchaser",
                       "value": "%%purchase.name%%",
                       "inline": true
                   },
                   {
                       "name": "Item",
                       "value": "%%shop.item.name%%",
                       "inline": true
                   },
                   {
                       "name": "Amount",
                       "value": "%%purchase.amount%%(x%%shop.stacking-amount%%)",
                       "inline": true
                   },
                   {
                       "name": "Your earn",
                       "value": "%%purchase.balance-formatted%%",
                       "inline": true
                   },
                   {
                       "name": "Taxes",
                       "value": "%%purchase.taxes-formatted%%",
                       "inline": true
                   }
               ]
           }
         }
      mod-shop-purchase: |
        {
        "embed":
          {
              "title": ":dollar: Somebody purchased in a shop",
              "description": "The player %%purchase.name%% purchase x%%purchase.amount%% %%shop.item.name%% from the shop.",
              "color": 6061450,
              "author": {
                  "name": "QuickShop-Hikari",
                  "icon_url": ""
              },
              "thumbnail": {
                  "url": ""
              },
              "image": {
                  "url": ""
              },
              "footer": {
                  "text": "QuickShop-Hikari Discord Notification",
                  "icon_url": ""
              },
              "fields": [
                  {
                      "name": "Shop",
                      "value": "%%shop.display-name%%",
                      "inline": false
                  },
                  {
                      "name": "Purchaser",
                      "value": "%%purchase.name%%",
                      "inline": true
                  },
                  {
                      "name": "Item",
                      "value": "%%shop.item.name%%",
                      "inline": true
                  },
                  {
                      "name": "Amount",
                      "value": "%%purchase.amount%% (x%%shop.stacking-amount%%)",
                      "inline": true
                  },
                  {
                      "name": "Balance",
                      "value": "%%purchase.balance-formatted%%",
                      "inline": true
                  },
                  {
                      "name": "Taxes",
                      "value": "%%purchase.taxes-formatted%%",
                      "inline": true
                  }
              ]
          }
        }
      out-of-space: |
        {
        "embed":
          {
              "title": ":mailbox_with_mail: Your shop has run out of space",
              "description": "Your shop inventory is full of items!\nYou need empty your shop container to free the space then you can continue to accept new items.",
              "color": 16065893,
              "author": {
                  "name": "QuickShop-Hikari",
                  "icon_url": ""
              },
              "thumbnail": {
                  "url": ""
              },
              "image": {
                  "url": ""
              },
              "footer": {
                  "text": "QuickShop-Hikari Discord Notification",
                  "icon_url": ""
              },
              "fields": [
                  {
                      "name": "Shop",
                      "value": "%%shop.display-name%%",
                      "inline": false
                  }
              ]
          }
        }
      out-of-stock: |
        {
            "embed":
                {
                    "title": ":mailbox_with_no_mail: Your shop has run out of stock",
                    "description": "Your shop inventory is empty now!\nYou need to refill the shop container with items to continue selling items!",
                    "color": 16065893,
                    "author": {
                        "name": "QuickShop-Hikari",
                        "icon_url": ""
                    },
                    "thumbnail": {
                        "url": ""
                    },
                    "image": {
                        "url": ""
                    },
                    "footer": {
                        "text": "QuickShop-Hikari Discord Notification",
                        "icon_url": ""
                    },
                    "fields": [
                        {
                            "name": "Shop",
                            "value": "%%shop.display-name%%",
                            "inline": false
                        }
                    ]
                }
        }
      mod-shop-created: |
        {
          "embed": {
            "title": ":classical_building: A new shop has been created",
            "description": "Player created a new shop on your server!",
            "color": 6061450,
            "author": {
              "name": "QuickShop-Hikari",
              "icon_url": ""
            },
            "thumbnail": {
              "url": ""
            },
            "image": {
              "url": ""
            },
            "footer": {
              "text": "QuickShop-Hikari Discord Notification",
              "icon_url": ""
            },
            "fields": [
              {
                "name": "Shop",
                "value": "%%shop.display-name%%",
                "inline": false
              },
              {
                "name": "Owner",
                "value": "%%shop.owner.name%%",
                "inline": true
              },
              {
                "name": "Item",
                "value": "%%shop.item.name%%",
                "inline": true
              },
              {
                "name": "Amount",
                "value": "%%shop.stacking-amount%%",
                "inline": true
              },
              {
                "name": "Type",
                "value": "%%shop.type%%",
                "inline": true
              }
            ]
          }
        }
      mod-remove-shop: |
        {
            "embed": {
                "title": ":recycle: A shop was removed from this server",
                "description": "A shop was removed from this server.",
                "color": 6061450,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "Reason",
                        "value": "%%delete.reason%%",
                        "inline": false
                    }
                ]
            }
        }
      shop-transfer-to-you: |
        {
            "embed": {
                "title": ":placard: A shop was transfer to you",
                "description": "A shop transfer to you from another player.",
                "color": 16172079,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "From",
                        "value": "%%transfer.from%%",
                        "inline": false
                    }
                ]
            }
        }
      mod-shop-transfer: |
        {
            "embed": {
                "title": ":placard: A shop was transferred",
                "description": "A shop transfer to a player from another player.",
                "color": 6061450,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "From",
                        "value": "%%transfer.from%%",
                        "inline": true
                    },
                    {
                        "name": "To",
                        "value": "%%transfer.to%%",
                        "inline": true
                    }
                ]
            }
        }
      shop-price-changed: |
        {
            "embed": {
                "title": ":money_with_wings: Your shop price was changed",
                "description": "You or your shop staff changed your shop price.",
                "color": 16172079,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "From",
                        "value": "%%change-price.from%%",
                        "inline": true
                    },
                    {
                        "name": "To",
                        "value": "%%change-price.to%%",
                        "inline": true
                    }
                ]
            }
        }
      mod-shop-price-changed: |
        {
            "embed": {
                "title": ":money_with_wings: A shop price has been changed",
                "description": "A shop changed its price settings.",
                "color": 6061450,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": true
                    },
                    {
                        "name": "Owner",
                        "value": "%%shop.owner.name%%",
                        "inline": true
                    },
                    {
                        "name": "",
                        "value": "",
                        "inline": true
                    },
                    {
                        "name": "From",
                        "value": "%%change-price.from%%",
                        "inline": true
                    },
                    {
                        "name": "To",
                        "value": "%%change-price.to%%",
                        "inline": true
                    }
                ]
            }
        }
      shop-permission-changed: |
        {
            "embed": {
                "title": ":closed_lock_with_key: Your shop permission settings has been changed",
                "description": "One of your shop's permission setting has been changed.",
                "color": 15879747,
                "author": {
                    "name": "QuickShop-Hikari",
                    "icon_url": ""
                },
                "thumbnail": {
                    "url": ""
                },
                "image": {
                    "url": ""
                },
                "footer": {
                    "text": "QuickShop-Hikari Discord Notification",
                    "icon_url": ""
                },
                "fields": [
                    {
                        "name": "Shop",
                        "value": "%%shop.display-name%%",
                        "inline": false
                    },
                    {
                        "name": "Player",
                        "value": "%%change-permission.player%%",
                        "inline": true
                    },
                    {
                        "name": "Assign to group",
                        "value": "%%change-permission.to-group%%",
                        "inline": true
                    },
                    {
                        "name": "",
                        "value": "",
                        "inline": true
                    },
                    {
                        "name": "Permission granted (Inherit from group)",
                        "value": "```\n%%change-permission.perms-list%%\n```",
                        "inline": false
                    }
                ]
            }
        }
    field:
      player: Player
      item: Item
      amount: Amount
      balance: Balance
      balance-after-tax: Balance (after tax)
      account: Your account balance
      taxes: Taxes
      cost: Cost
  discount:
    commands:
      discount:
        description: <yellow>Apply a discount code or manage your discount codes.
    tab-complete:
      discount:
        general:
          code: <code>
        create:
          rate: |
            Command Hint:
            Argument: <rate>
            Description: The actual percentage or money you will earn
            Input `30%` = price * 0.3
            Input `50` = price - 50
          max-usage: |
            Command Hint:
            Argument: [max-usage]
            Description: The times that code can be used
            `-1` for unlimited.
          threshold: |
            Command Hint:
            Argument: [threshold]
            Description: The minimum price that code can be applied
            `-1` for unlimited
          expired: |
            Command Hint
            Argument: [expired]
            Description: The code expired time.
            `-1` for unlimited duration.
            Accept both Zulu time and UNIX timestamp in seconds.
            Zulu Example: 2022-12-17T10:31:37Z
            UNIX Example: 1671273097
    discount-code-already-exists: <red>Sorry, the name of your discount code already in use.
    invalid-discount-code-regex: '<red>The discount code must matches the regex: <yellow>{0}</yellow>'
    invalid-discount-code: <red>This discount code are invalid.
    discount-code-added: <green>Your discount code <yellow>{0}</yellow> has been added into shop <aqua>{1}</aqua>
    discount-code-removed: <green>Your discount code <yellow>{0}</yellow> has been removed from shop <aqua>{1}</aqua>
    invalid-code-type: <red>The code type <yellow>{0}</yellow> are invalid.
    invalid-usage-restriction: <red>The usage restriction <yellow>{0}</yellow> are invalid.
    invalid-threshold-restriction: <red>The threshold restriction <yellow>{0}</yellow> are invalid.
    invalid-effect-scope: <red>The scope <yellow>{0}</yellow> are invalid.
    invalid-expire-time: <red>Você não pode especificar um horário no passado.
    invalid-discount-rate: <red>The discount rate <yellow>{0}</yellow> are invalid, it can be a fixed number or percentage.
    discount-code-expired: <red>Ouch! Your discount code <yellow>{0}</yellow> has been expired!
    discount-code-installed: <green>You have installed discount code <gold>{0}</gold>, the discount code will be automatically applied to the available purchase in all the following purchases during this session. To uninstall discount code, execute <aqua>/qs discount uninstall {0}</aqua>.
    discount-code-uninstalled: <green>You have uninstalled your discount code.
    discount-code-query-nothing: <red>You hadn't install a discount code yet!
    discount-code-query: '<green>You are using discount code: <yellow>{0}</yellow>.'
    discount-code-applicable: '<color:#bcef26>Your discount code <yellow><bold>{0}</bold></yellow> are <bold>applicable</bold> in this shop!'
    discount-code-applicable-with-threshold: '<color:#bcef26>Your discount code <yellow><bold>{0}</bold></yellow> are <bold>applicable</bold> in this shop, but only your purchase cost over <yellow>{1}</yellow> in single transaction.'
    discount-code-not-applicable: <red>Your discount code <yellow><bold>{0}</bold></yellow> are <bold>not applicable</bold> in this shop!
    discount-code-reach-the-limit: <red>You have reach the usage limit of discount code <yellow><bold>{0}</bold></yellow>, discount are not applied.
    discount-code-no-permission: <red>You didn't have permission to use any discount code in this shop!
    discount-code-has-been-expired: <red>Your discount has been expired!
    discount-code-config-shop-added: <green>Successfully to add this shop into your discount code allow list.
    discount-code-config-shop-add-failure: <red>This shop already in your discount code allow list.
    discount-code-config-shop-removed: <green>Successfully to remove this shop from your discount code allow list.
    discount-code-config-shop-remove-failure: <red>This shop not exists in your discount code allow list.
    discount-code-config-expire: <green>Successfully changed your code expire time.
    discount-code-config-applied: <green>Config the discount code successfully!
    discount-code-created-successfully: |
      <green>Your discount code <yellow>{0}</yellow> has been successfully created!
      <gold>Scope: <aqua>{1}</aqua></gold>.
      <gold>Share with others: <color:#bcef26>{2}</color:#bcef26></gold>.
      <yellow>For <gray>Specific Shops</gray> scope only: To add shops to your discount code allow list, please looking an quickshop and execute <aqua>{3}</aqua> command.
      <yellow>You can use <aqua>/qs discount config {0}</aqua> to edit your discount code anytime.
    discount-code-under-threshold: <red>The discount not applied to your purchase because the total value under the discount code threshold <yellow>{0}</yellow>.
    percentage-off: '<bold><color:#bcef26>-{0}%</color:#bcef26></bold>'
    fixed-off: '<bold><color:#bcef26>-{0}</color:#bcef26></bold>'
    discount-code-list: '<gold>Listing your discount codes:</gold>'
    discount-code-applied-in-purchase: '<color:#bcef26>Your discount code <yellow>{0}</yellow> has been applied to this purchase and you saved <gold>{1}</gold>!'
    scope:
      this-shop: '{0}'
      your-shops-owned: all your shops (owned)
      your-shops-managed: all your shops (managed)
      server: whole server
    code-type:
      SERVER_ALL_SHOPS: All shops on this server
      PLAYER_ALL_SHOPS: All shops that created by code owner
      SPECIFIC_SHOPS: Specific shops
    discount-code-details: |-
      <gold>Code: <yellow>{0}</yellow>
      <gold>Creator: <yellow>{1}</yellow>
      <gold>Applied to: <yellow>{2}</yellow>
      <gold>Remaining usage: <yellow>{3}</yellow>
      <gold>Expired on: <yellow>{4}</yellow>
      <gold>Threshold: <yellow>{5}</yellow>
      <gold>Discount: <yellow>{6}</yellow>
  list:
    commands:
      list: <yellow>Listing all shops owned by your or specific player
    table-prefix: '<yellow><green>{0}</green>''s shops <gray>(Total {1})</gray>: '
    table-prefix-pageable: '<yellow><green>{0}</green>''s shops <gray>(Page {1}/{2})</gray>: '
    entry: "<hover:show_text:'<yellow>{1}</yellow>\n<gray>{2} {3},{4},{5}</gray>\n<aqua>Price <yellow>{6}</yellow> - <light_purple>x{7}</light_purple> <yellow>{8}</yellow>\n<green>{9}'><yellow>{0}. <aqua>{1}</aqua></hover>"
  shopitemonly:
    message: <red>You can't put non-shop items into shop container, all non-shop items will dropped at your location.
  limited:
    commands:
      limit: <yellow>Set a limit that restrict the purchase that player can make in period
    titles:
      title: <green>Purchase Success
      subtitle: <aqua>You can also purchase <gold>{0}</gold> more in this shop
    reach-the-limit: <red>You have reached the limit in this shop. You can also purchase <green>{0}</green> items, but you trying purchase <yellow>{1}</yellow> items.
    success-reset: <green>Reset the restriction of this shop successfully
    success-remove: <green>Successfully remove the all restriction of this shop
    success-setup: <green>Restriction settings for this shop were saved successfully
    trade-limit-reached-cancel-reason: <red>Reached the restriction of this shop
    remains-limits: '<gold>Your remaining purchase quota in this shop: <yellow>{0}</yellow>'
    remains-limits-before-reset-period-note: '<gold>This shop will reset their purchase quota every: <yellow>{0}</yellow>'
  dynmap:
    markerset-title: QuickShop-Hikari Shops
    marker-name: '{0} @ {2} @ {3} for x{4} @ {5}'
    marker-description: |
      Name: {0}
      Owner: {1}
      Item: {2}
      Price: {3} for x{4} item(s)
      Type: {5}
      Unlimited: {6}
      Location: {7}
  bluemap:
    markerset-title: QuickShop-Hikari Shops
    marker-name: '{0} @ {2} @ {3} for x{4} @ {5}'
    marker-description: |
      Name: {0}
      Owner: {1}
      Item: {2}
      Price: {3} for x{4} item(s)
      Type: {5}
      Unlimited: {6}
      Location: {7}
  chestprotect:
    protection-exists: <red>This region already protected by ChestProtect and you don't have permission to create shops at here.
    shops-exsts: <red>The region that you want to protect contains other player's shops and you don't have permission to access them.
  displaycontrol:
    toggle: |-
      <green>Successfully toggle your QuickShop display showcasing to <aqua>{0}</aqua>.
      <yellow>You may need re-join to take effect.
    toggle-exception: <red>Failed to toggle your display showcase settings due an internal error, please contact server administrator.
    command:
      displaycontrol: <yellow>Toggle your QuickShop display showcasing
  reremake-migrator:
    commands:
      migratefromreremake: Migrate QuickShop-Reremake's data to QuickShop-Hikari
    server-not-empty: "<red>No players are allowed to be online on the server during the conversion progress, please enable server whitelist or maintenance mode."
    starting-convert-progress: "<gold>Starting the convert progress, <red>DO NOT SHUTDOWN YOUR SERVER!</red>"
    executing: "<gold>Executing migrate component <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
    completed: "<green>Done! Migrate completed, please restart your server and remove QuickShop-Reremake from plugins directory."
    join_blocking_converting: "<red>[QuickShop-Hikari Migrator] This server is performing a data conversion and you cannot join the server at this time, try again later!"
    join_blocking_finished: "<red>[QuickShop-Hikari Migrator] This server has just completed a data conversion and is waiting for a reboot to apply the changes, try again later!"
    failed: "<red>Migration progress exited with error, please check the server console."
    modules:
      config:
        copy-values: "<yellow>Copying values (total {0} entries)..."
        copying-value: "<gray> - Copying <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        migrate-price-restriction: "<yellow>Migrating price-restriction related settings..."
        migrate-price-restriction-entry: "<gray> - Migrating <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
      shop:
        start-migrate: "<yellow>Migrating shops (total {0} entries)..."
        migrate-entry: "<gray> - Migrating <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        unloading-reremake: "<gold>Unloading Reremake to avoid data overwrite..."
        register-entry: "<gray> - Registering <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        save-entry: "<gray> - Saving <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        saving-shops: "<yellow>Saving <gold>{0}</gold> shops, this may need a while™ (the time based on shops amounts)..."
        conflict: "<gray> - CONFLICT > Detects a conflict between a Reremake store and an existing Hikari store location, taking a predefined behavior: <dark_gray>{0}</dark_gray>"
      translation:
        start-migrate: "<yellow>Migrating translation files...</yellow>"
        copy-values: "<yellow>Copying values in locale <gold>{0}</gold> (total {1} entries)..."
        migrate-entry: "<gray> - Migrating <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
        copying-value: "<gray> - Copying <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
      shop-logs:
        start-migrate: "<yellow>Migrating shop logs (details in console), please wait...</yellow>"
        extract-history-files: "<gray>Please wait for us to unzip and append the history logs..."
        filter-history-files: "<gray>Please wait for us to filter the history log..."
        filtered-history-files: "<gray>Filtered {0} lines out of queue."
        import-entry: "<gray> - Migrating <aqua>{0}</aqua> <dark_gray>({1}/{2})</dark_gray>..."
compat:
  advancedchests:
    created: <green>You're successfully created a shop on AdvancedChests container!
    permission-denied: <red>Sorry! You don't have the permission to create a shop on a AdvancedChests container!
suite:
  dynmap:
    title: '{0} - {1} - {2}'
    description: |-
      Item: {0}
      Owner: {1}
      Type: {2} {3}
      Price: {4}
      Location: {5} {6}, {7}, {8}
      Space: {9}
      Stock: {10}
  limited:
    command-description: <yellow>Set a limit that restrict the purchase that player can make in period.
    reach-the-quota-limit: <red>You have reach the purchase limit in this shop ({0}/{1}).
    quota-reset-countdown: <yellow>The quota in this shop will reset in {0}.
    quota-reset-player-successfully: <green>The quota for player {0} in this shop has been reset successfully.
    quota-reset-everybody-successfully: <green>The quota for everyone in this shop has been reset successfully.
    quota-setup: <green>The purchase restriction now apply to this shop!
    quota-remove: <green>The purchase restriction now removed from this shop!
    subtitles:
      title: <green>Purchase Success
      subtitle: <aqua>You can purchase <yellow>{0}</yellow> more in this shop
  list:
    command-description: <yellow>List all shops owned by yourself or another player.
    table-prefix: <yellow>You owned total <aqua>{0}</aqua> shops on this server.
    entry-syntax: <aqua>- <yellow>{0}
    entry-syntax-fallback-name: '<yellow>Item:{0} X:{1}, Y:{2}, Z:{3}, World: {4}'
    hover-text: |-
      <yellow>{0}
      <gray>{1} {2}, {3}, {4}
      <aqua>{5} - <yellow>{6}
      <yellow>{7} items each stack.
  shopitemonly:
    message: <red>You can't put non-shop items into shop container, all non-shop items will dropped at your location.
compatibility:
  elitemobs:
    soulbound-disallowed: You can't trade item which have EliteMobs Soulbound enchantment.
internet-paste-forbidden-privacy-reason: "<red>Failed! According your privacy setting, QuickShop-Hikari are not allowed upload your paste to Internet, turn on DIAGNOSTIC permission in privacy setting in config.yml or use <aqua>/quickshop paste --file</aqua> instead."
no-sign-type-given: "<red>You need give a sign material, the sign materials that available on this server: {0}"
sign-type-invalid: "<red>The type <yellow>{0}</yellow> not a valid sign material."
delete-controlpanel-button-confirm: "<red>Do you really want remove this shop? Click the <bold>[Remove Shop]</bold> button again in {0} seconds to confirm."
cannot-suggest-price: "<red>Sorry, For the time being, no more people are trading the same item as you and there is not enough data to generate a suggested price."
price-suggest: "<green>Based on data from <aqua>{0}</aqua> stores, the highest priced store is priced at <light_purple>{1}</light_purple>, the lowest priced store is priced at <light_purple>{2}</light_purple>, the average price is <light_purple>{3}</light_purple>, and the median price is <light_purple>{4}</light_purple>. <newline><yellow>It's recommended that you set your price around <gold>{5}</gold>.</yellow>"
suggest-wait: "<green>Please wait... Calculating recommend price."
history:
  shop:
    gui-title: "View purchase history"
    header-icon-multiple-shop: "<white>The query result across {0} shops</white>"
    header-icon-description:
      - "<white>Type: <yellow>{0}</yellow></white>"
      - "<white>Owner: <yellow>{1}</yellow></white>"
      - "<white>Item: <yellow>{2}</yellow></white>"
      - "<white>Price: <yellow>{3} <aqua>x{4}</aqua></yellow></white>"
      - "<white>Location: <yellow>{5}</yellow></white>"
    log-icon-title: "<reset><green>Time: {0}</green>"
    log-icon-description:
      - "<white>Purchaser: <yellow>{0}</yellow></white>"
      - "<white>Item: <yellow>{1} <aqua>x{2}</aqua></yellow></white>"
      - "<white>Balance: <yellow>{3}</yellow></white>"
      - "<white>Tax: <yellow>{4}</yellow></white>"
    log-icon-description-with-store-name:
      - "<white>Store: <yellow>{0}</yellow></white>"
      - "<white>Purchaser: <yellow>{1}</yellow></white>"
      - "<white>Item: <yellow>{2} <aqua>x{3}</aqua></yellow></white>"
      - "<white>Balance: <yellow>{4}</yellow></white>"
      - "<white>Tax: <yellow>{5}</yellow></white>"
    query-icon: "<gray>Please wait, querying...</gray>"
    previous-page: "<white><< Previous Page</white>"
    next-page: "<white>Next Page >></white>"
    current-page: "<white>Page {0}</white>"
    summary-icon-title: "<green>Shop Summary"
    recent-purchases: "<white>Recent <aqua>{0}</aqua> purchases: <yellow>{1}</yellow></white>"
    recent-purchase-balance: "<white>Recent <aqua>{0}</aqua> turnover: <yellow>{1}</yellow></white>"
    total-purchases: "<white>Total purchases: <yellow>{0}</yellow></white>"
    total-balances: "<white>Total turnover: <yellow>{0}</yellow></white>"
    total-unique-purchasers: "<white>Total unique purchasers: <yellow>{0}</yellow></white>"
    top-n-valuable-customers-title: "<gold>Top {0} valuable customers</gold>"
    top-n-valuable-customers-entry: "<white>- <yellow>{0}</yellow> <aqua>{1}</aqua></white>"
    no-result: "<white>No result</white>"
about:
  text:
    - "<b><aqua>QuickShop <yellow>{0}</yellow></aqua></b>"
    - "<aqua>Version <yellow>>></yellow> <green>{1}</green></aqua>"
    - "<aqua>Release <yellow>>></yellow> <green>{2}</green></aqua>"
    - "<aqua>Developers <yellow>>></yellow> <color:#218bff><hover:show_text:'<yellow>Click to check contributors'><click:open_url:'https://github.com/Ghost-chu/QuickShop-Hikari/graphs/contributors'>[View contributors on GitHub]</click></hover></color></aqua>"
    - "<aqua>Localized members <gold>({4})</gold>: <yellow>>></yellow> <green>{5}</green></aqua>"
    - "  <yellow>+ <color:#43A047><hover:show_text:'<yellow>Click to open Crowdin translate page'><click:open_url:'https://crowdin.com/project/quickshop-hikari'>[Help translate on Crowdin]</click></hover></color></yellow>"
    - "<aqua>Donation Key <yellow>>></yellow> <green>{6}</green></aqua>"
    - "<gold>Powered by community</gold> <red>Made with ❤</red>"
  valid-donation-key: "<color:#00AFF1>Bound to <color:#F9C23C>{0}</color></color>"
  invalid-donation-key: "<gray>Invalid donation key</gra>"
  kofi-thanks: "<gold>Special thanks to those who support QuickShop on Ko-fi :)</gold>"
history-command-leave-blank: "<Leave blank for seeing store>"
shop-information-not-shown-due-an-internal-error: "<red>An internal error occurred. The store information panel may be displayed incompletely, please contact the server administrator."
