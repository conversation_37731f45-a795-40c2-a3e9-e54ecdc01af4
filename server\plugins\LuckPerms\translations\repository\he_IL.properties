luckperms.logs.actionlog-prefix=יומן
luckperms.logs.verbose-prefix=VB
luckperms.logs.export-prefix=יצוא
luckperms.commandsystem.available-commands=תשתמש ב- {0} כדי לראות פקודות זמינות
luckperms.commandsystem.command-not-recognised=הפקודה לא מוכרת
luckperms.commandsystem.no-permission=אין לך הרשאות להשתמש בפקודה הזאת\!
luckperms.commandsystem.no-permission-subcommands=אין לך הרשאות להשתמש בפקודות משנה כלשהן
luckperms.commandsystem.already-executing-command=פקודה אחרת מתבצעת באותו הזמן, מחכה שתסתיים...
luckperms.commandsystem.usage.sub-commands-header=פקודות משנה
luckperms.commandsystem.usage.usage-header=שימוש בפקודה
luckperms.commandsystem.usage.arguments-header=ויכוחים
luckperms.first-time.no-permissions-setup=זה נראה שעדיין לא הוגדרו הרשאות\!
luckperms.first-time.use-console-to-give-access=לפני שתוכל להשתמש באחת מפקודות ה- LuckPerms במשחק, עליך להשתמש בקונסולה כדי להעניק לעצמך גישה
luckperms.first-time.console-command-prompt=פתח את המסוף שלך והפעל
luckperms.first-time.next-step=לאחר שתעשה זאת, תוכל להתחיל להגדיר את ההרשאות והקבוצות שלך
luckperms.first-time.wiki-prompt=לא יודע איפה להתחיל? בדוק כאן\: {0}
luckperms.login.try-again=אנא נסה שוב מאוחר יותר
luckperms.login.loading-database-error=אירעה שגיאת מסד נתונים בעת טעינת נתוני ההרשאות
luckperms.login.server-admin-check-console-errors=אם אתה מנהל השרת, בדוק אם קיימת שגיאה בקונסולה
luckperms.login.server-admin-check-console-info=אנא עיין בקונסולה של השרת למידע נוסף
luckperms.login.data-not-loaded-at-pre=נתוני ההרשאות עבור המשתמש שלך לא נטענו בשלב הכניסה מראש
luckperms.login.unable-to-continue=לא מסוגל להמשיך
luckperms.login.craftbukkit-offline-mode-error=זה ככל הנראה בגלל התנגשות בין CraftBukkit לבין ההגדרה במצב מקוון
luckperms.login.unexpected-error=אירעה שגיאה לא צפויה בעת הגדרת נתוני ההרשאות שלך
luckperms.opsystem.disabled=מערכת OP הרגילה מושבתת בשרת זה
luckperms.opsystem.sponge-warning=שים לב שלמצב מנהל שרת אין השפעה על הרשאות Sponge כאשר מותקן תוסף הרשאה, עליך לערוך את נתוני המשתמש ישירות
luckperms.duration.unit.years.plural={0} שנים
luckperms.duration.unit.years.singular={0} שנה
luckperms.duration.unit.years.short=שנה{0}
luckperms.duration.unit.months.plural={0} חודשים
luckperms.duration.unit.months.singular={0} חודש
luckperms.duration.unit.months.short=חודש{0}
luckperms.duration.unit.weeks.plural={0} שבועות
luckperms.duration.unit.weeks.singular={0} שבוע
luckperms.duration.unit.weeks.short=שבועות{0}
luckperms.duration.unit.days.plural={0} ימים
luckperms.duration.unit.days.singular={0} ימים
luckperms.duration.unit.days.short=יום{0}
luckperms.duration.unit.hours.plural={0} שעות
luckperms.duration.unit.hours.singular={0} שעה
luckperms.duration.unit.hours.short=שעה{0}
luckperms.duration.unit.minutes.plural={0} דקות
luckperms.duration.unit.minutes.singular={0} דקה
luckperms.duration.unit.minutes.short=דק''{0}
luckperms.duration.unit.seconds.plural={0} שניות
luckperms.duration.unit.seconds.singular={0} שנייה
luckperms.duration.unit.seconds.short=שנ''{0}
luckperms.duration.since={0} לפני
luckperms.command.misc.invalid-code=קוד אינו תקין
luckperms.command.misc.response-code-key=תגובת הקוד
luckperms.command.misc.error-message-key=הודעה
luckperms.command.misc.bytebin-unable-to-communicate=אין אפשרות לתקשר עם bytebin
luckperms.command.misc.webapp-unable-to-communicate=לא ניתן לתקשר עם אפליקציית האינטרנט
luckperms.command.misc.check-console-for-errors=תבדוק את הקונסולה בשביל לראות שגיאות
luckperms.command.misc.file-must-be-in-data=הקובץ {0} חייב חיבור ישיר לספריית הנתונים
luckperms.command.misc.wait-to-finish=המתן עד שיסיים ונסה שוב
luckperms.command.misc.invalid-priority=עדיפות לא חוקית {0}
luckperms.command.misc.expected-number=ציפיתי למספר
luckperms.command.misc.date-parse-error=לא יכול לנתח תאריך {0}
luckperms.command.misc.date-in-past-error=לא ניתן לקבוע תאריך בעבר\!
luckperms.command.misc.page=עמוד {0} מתוך\: {1}
luckperms.command.misc.page-entries={0} ערכים
luckperms.command.misc.none=אף אחד
luckperms.command.misc.loading.error.unexpected=שגיאה לא צפויה התרחשה
luckperms.command.misc.loading.error.user=המשתמש לא נטען
luckperms.command.misc.loading.error.user-specific=לא יכול לטעון את משתמש היעד {0}
luckperms.command.misc.loading.error.user-not-found=לא נמצא משתמש עבור {0}
luckperms.command.misc.loading.error.user-save-error=אירעה שגיאה בעת שמירת נתוני המשתמש עבור {0}
luckperms.command.misc.loading.error.user-not-online=משתמש {0} לא מחובר
luckperms.command.misc.loading.error.user-invalid={0} אינו שם משתמש/משתמש מזהה ייחודי בתוקף
luckperms.command.misc.loading.error.user-not-uuid=משתמש היעד {0} אינו משתמש ייחודי בתוקף
luckperms.command.misc.loading.error.group=קבוצה לא נטענה
luckperms.command.misc.loading.error.all-groups=לא מסוגל לטעון את כל הקבוצות
luckperms.command.misc.loading.error.group-not-found=קבוצה בשם {0} לא נמצאה
luckperms.command.misc.loading.error.group-save-error=אירעה שגיאה בשמירת נתוני הקבוצה עבור {0}
luckperms.command.misc.loading.error.group-invalid={0} אינו שם קבוצה בתוקף
luckperms.command.misc.loading.error.track=המסלול לא נטען
luckperms.command.misc.loading.error.all-tracks=לא נטען לטעון את כל המסלולים
luckperms.command.misc.loading.error.track-not-found=מסלול בשם {0} לא נמצא
luckperms.command.misc.loading.error.track-save-error=אירעה שגיאה בעת שמירת נתוני המסלול עבור {0}
luckperms.command.misc.loading.error.track-invalid={0} אינו מסלול שם חוקי
luckperms.command.editor.no-match=לא ניתן לפתוח את העורך, אין אובייקטים שתואמים את הסוג הרצוי
luckperms.command.editor.start=מכין עורך חדש, נא המתן...
luckperms.command.editor.url=לחץ על הקישור למטה כדי לפתוח את העורך
luckperms.command.editor.unable-to-communicate=לא יכול לתקשר עם העורך
luckperms.command.editor.apply-edits.success=נתוני עורך האינטרנט הוחלו על {0} {1} בהצלחה
luckperms.command.editor.apply-edits.success-summary={0} {1} ו {2} {3}
luckperms.command.editor.apply-edits.success.additions=תוספות
luckperms.command.editor.apply-edits.success.additions-singular=תוספת
luckperms.command.editor.apply-edits.success.deletions=מחיקות
luckperms.command.editor.apply-edits.success.deletions-singular=מחיקה
luckperms.command.editor.apply-edits.no-changes=לא הוחלו שינוים מעורך האינטרנט, הנתונים שהוחזרו לא הכילו עריכות כלשהן
luckperms.command.editor.apply-edits.unknown-type=לא ניתן להחיל עריכה על סוג האובייקט שצוין
luckperms.command.editor.apply-edits.unable-to-read=לא ניתן לקרוא את הנתונים באמצעות הקוד הנתון
luckperms.command.search.searching.permission=מחפש משתמשים וקבוצות עם {0}
luckperms.command.search.searching.inherit=מחפש משתמשים וקבוצות שעוברים בירושה מ {0}
luckperms.command.search.result=נמצא {0} כניסות מ {1} משתמשים ו {2} קבוצות
luckperms.command.search.result.default-notice=הערה\: כשמחפשים חברים בקבוצת ברירת המחדל, לא יוצגו שחקנים לא מקוונים ללא הרשאות אחרות\!
luckperms.command.search.showing-users=מציג רשומות משתמשים
luckperms.command.search.showing-groups=מציג רשומות קבוצתיות
luckperms.command.tree.start=יוצר עץ הרשאות, נא להמתין...
luckperms.command.tree.empty=לא ניתן ליצור תצוגת עץ, לא תוצאות נמצאו
luckperms.command.tree.url=קישור עץ הרשאות
luckperms.command.verbose.invalid-filter={0} אינו מסנן פילטר מלולי
luckperms.command.verbose.enabled=רישום מילולי {0} לבדיקות תואמות {1}
luckperms.command.verbose.command-exec=מכריח את {0} לבצע פקודה {1} ומדווח על כל הבדיקות שנוצרו...
luckperms.command.verbose.off=רישום מילולי {0}
luckperms.command.verbose.command-exec-complete=ביצוע הפקודה הושלם
luckperms.command.verbose.command.no-checks=ביצוע הפקודה הושלם, אך לא בוצעו בדיקות הרשאות
luckperms.command.verbose.command.possibly-async=ייתכן שהסיבה לכך היא שהתוסף מריץ פקודות ברקע (אסינכרון)
luckperms.command.verbose.command.try-again-manually=אתה עדיין יכול להשתמש במילים באופן ידני כדי לזהות בדיקות שנעשו כך
luckperms.command.verbose.enabled-recording=הקלטה מילולית {0} לבדיקות תואמות {1}
luckperms.command.verbose.uploading=רישום מילולי {0}, מעלה תוצאות...
luckperms.command.verbose.url=כתובת אתר של תוצאות מילוליות
luckperms.command.verbose.enabled-term=מופעל
luckperms.command.verbose.disabled-term=מופסק
luckperms.command.verbose.query-any=כל דבר
luckperms.command.info.running-plugin=רץ
luckperms.command.info.platform-key=פלטפורמה
luckperms.command.info.server-brand-key=מותג שרת
luckperms.command.info.server-version-key=גרסת השרת
luckperms.command.info.storage-key=אחסון
luckperms.command.info.storage-type-key=סוג
luckperms.command.info.storage.meta.split-types-key=סוגים
luckperms.command.info.storage.meta.ping-key=זמן תגובה
luckperms.command.info.storage.meta.connected-key=מחובר
luckperms.command.info.storage.meta.file-size-key=גודל הקובץ
luckperms.command.info.extensions-key=תוספים
luckperms.command.info.messaging-key=הודעות
luckperms.command.info.instance-key=מקרה
luckperms.command.info.static-contexts-key=הקשרים סטטיים
luckperms.command.info.online-players-key=שחקנים מחוברים
luckperms.command.info.online-players-unique={0} ייחודי
luckperms.command.info.uptime-key=זמן פעילות
luckperms.command.info.local-data-key=נתונים מקומיים
luckperms.command.info.local-data={0} משתמשים, {1} קבוצות, {2} מסלולים
luckperms.command.generic.create.success={0} נוצר בהצלחה
luckperms.command.generic.create.error=אירעה שגיאה בעת יצירת {0}
luckperms.command.generic.create.error-already-exists={0} כבר קיים\!
luckperms.command.generic.delete.success={0} נמחק בהצלחה
luckperms.command.generic.delete.error=אירעה שגיאה בעת מחיקת {0}
luckperms.command.generic.delete.error-doesnt-exist={0} אינו קיים\!
luckperms.command.generic.rename.success=השם של {0} שונה בהצלחה ל {1}
luckperms.command.generic.clone.success=השם של {0} שוכפל בהצלחה ל {1}
luckperms.command.generic.info.parent.title=קבוצות הורים
luckperms.command.generic.info.parent.temporary-title=קבוצות הורה זמנית
luckperms.command.generic.info.expires-in=יפוג בתוך
luckperms.command.generic.info.inherited-from=ירושה מ
luckperms.command.generic.info.inherited-from-self=עצמי
luckperms.command.generic.show-tracks.title={0} מסלולים
luckperms.command.generic.show-tracks.empty={0} לא נמצא בשום מסלול
luckperms.command.generic.clear.node-removed=הוסרו {0} פרטים
luckperms.command.generic.clear.node-removed-singular=הוסר {0} פריט
luckperms.command.generic.clear=ההרשאות של {0} נוקו בהקשר {1}
luckperms.command.generic.permission.info.title=ההרשאות של {0}
luckperms.command.generic.permission.info.empty=אין ל {0} הרשאות מוגדרות
luckperms.command.generic.permission.info.click-to-remove=לחץ להסיר את ההרשאה מ {0}
luckperms.command.generic.permission.check.info.title=רשות מידע בשביל {0}
luckperms.command.generic.permission.check.info.directly={0} יש הרשאה {1} מכוון ל {2} בהקשר {3}
luckperms.command.generic.permission.check.info.inherited={0} מוריש מ {1} מכוון ל {2} מ {3} בהקשר {4}
luckperms.command.generic.permission.check.info.not-directly={0} אין {1} מכוון
luckperms.command.generic.permission.check.info.not-inherited={0} לא יורש מ {1}
luckperms.command.generic.permission.check.result.title=רשות בדיקה בשביל {0}
luckperms.command.generic.permission.check.result.result-key=תוצאה
luckperms.command.generic.permission.check.result.processor-key=מעבד
luckperms.command.generic.permission.check.result.cause-key=סיבה
luckperms.command.generic.permission.check.result.context-key=הקשר
luckperms.command.generic.permission.set=הגדר {0} ל {1} עבור {2} בהקשר {3}
luckperms.command.generic.permission.already-has={0} כבר יש {1} הוגדר בהקשר {2}
luckperms.command.generic.permission.set-temp=הגדר {0} ל- {1} למשך {2} למשך זמן {3} בהקשר {4}
luckperms.command.generic.permission.already-has-temp={0} כבר יש {1} מוגדר באופן זמני בהקשר ל {2}
luckperms.command.generic.permission.unset=ביטול ההגדרה {0} עבור {1} בהקשר {2}
luckperms.command.generic.permission.doesnt-have={0} לא מוגדר {1} בהקשר {2}
luckperms.command.generic.permission.unset-temp=בטל את ההרשאה זמנית {0} עבור {1} בהקשר {2}
luckperms.command.generic.permission.subtract=הגדר {0} ל {1} למשך {2} למשך זמן {3} בהקשר {4}, {5} פחות מבעבר
luckperms.command.generic.permission.doesnt-have-temp={0} לא מוגדר {1} לא הוגדר זמנית בהקשר {2}
luckperms.command.generic.permission.clear=ההרשאות של {0} נוקו בהקשר {1}
luckperms.command.generic.parent.info.title=ההורים של {0}
luckperms.command.generic.parent.info.empty=אין ל {0} הרשאות מוגדרות
luckperms.command.generic.parent.info.click-to-remove=לחץ להסיר את ההורה הזה מ {0}
luckperms.command.generic.parent.add={0} יורש כעת הרשאות מ {1} בהקשר {2}
luckperms.command.generic.parent.add-temp={0} עכשיו יורש רשות מ {1} למשך זמן של {2} בהקשר {3}
luckperms.command.generic.parent.set={0} אם קבוצות ההורים הקיימות שלהם נוקו עכשיו יורשות רק {1} בהקשר {2}
luckperms.command.generic.parent.set-track=אם {0} היה מקבוצת הורים על מסלול {1} נוקו, עכשיו רק יורש {2} בהקשר {3}
luckperms.command.generic.parent.remove={0} כבר לא יורש רשות מ {1} בהקשר {2}
luckperms.command.generic.parent.remove-temp={0} כבר לא יורש באופן זמני מ {1} בהקשר {2}
luckperms.command.generic.parent.subtract={0} ירש רשות מ {1} למשך זמן של {2} בהקשר {3},{4} פחות מבעבר
luckperms.command.generic.parent.clear=ההורים של {0} נוקו בהקשר {1}
luckperms.command.generic.parent.clear-track=ההורים של {0} על מסלול {1} נוקו בהקשר {2}
luckperms.command.generic.parent.already-inherits={0} כבר יורש מ {1} בהקשר {2}
luckperms.command.generic.parent.doesnt-inherit={0} לא יורש מ {1} בהקשר {2}
luckperms.command.generic.parent.already-temp-inherits={0} כבר יורש זמנית מ {1} בהקשר {2}
luckperms.command.generic.parent.doesnt-temp-inherit={0} אינו יורש זמנית מ {1} בהקשר {2}
luckperms.command.generic.chat-meta.info.title-prefix=קידומות של ה{0}
luckperms.command.generic.chat-meta.info.title-suffix=סיומות של ה{0}
luckperms.command.generic.chat-meta.info.none-prefix=ל{0} אין הקידומות
luckperms.command.generic.chat-meta.info.none-suffix=ל{0} אין הסיומות
luckperms.command.generic.chat-meta.info.click-to-remove=לחץ על להסיר את {0} מ- {1}
luckperms.command.generic.chat-meta.already-has={0} כבר יש {1} {2} מוגדר בעדיפות {3} בהקשר {4}
luckperms.command.generic.chat-meta.already-has-temp={0} כבר יש {1} {2} מוגדר בעדיפות {3} בהקשר {4}
luckperms.command.generic.chat-meta.doesnt-have={0} אין {1} {2} מוגדר בעדיפות של {3} בהקשר {4}
luckperms.command.generic.chat-meta.doesnt-have-temp={0} אין {1} {2} מוגדר באופן זמני עדיפות שת {3} בהקשר {4}
luckperms.command.generic.chat-meta.add=ל-{0} הוגדרה {1} {2} בעדיפות של {3} בהקשר {4}
luckperms.command.generic.chat-meta.add-temp={0} היה {1} {2} הוגדר בעדיפות של {3} למשך של {4} בקשר של {5}
luckperms.command.generic.chat-meta.remove=ל-{0} היה {1} {2} בעדיפות של {3} בהקשר {4}
luckperms.command.generic.chat-meta.remove-bulk=ל-{0} היה כל {1} {2} הוסר בהקשר של {3}
luckperms.command.generic.chat-meta.remove-temp=ל-{0} היה {1} {2} בעדיפות של {3} שהוסר בהקשר של {4}
luckperms.command.generic.chat-meta.remove-temp-bulk=ל-{0} היה כולם זמניים {1} בעדיפות של {2} שהוסר בהקשר של {3}
luckperms.command.generic.meta.info.title=מטא של {0}
luckperms.command.generic.meta.info.none=ל- {0} אין מטא
luckperms.command.generic.meta.info.click-to-remove=לחץ כדי להסיר מטא צומת זה מ {0}
luckperms.command.generic.meta.already-has=ל {0} כבר הוגדר מפתח מטא {1} ל{2} בהקשר ל{3}
luckperms.command.generic.meta.already-has-temp=ל {0} כבר יש מפתח מטא {1} מוגדר באופן זמני ל{2} בהקשר ל {3}
luckperms.command.generic.meta.doesnt-have=ל {0} אין מפתח מטא {1} מוגדר בהקשר ל {2}
luckperms.command.generic.meta.doesnt-have-temp=ל {0} אין מפתח מטא {1} מוגדר באופן זמני בהקשר ל {2}
luckperms.command.generic.meta.set=הגדר מפתח מטא {0} ל {1} עבור {2} בהקשר ל {3}
luckperms.command.generic.meta.set-temp=הגדר מפתח מטא {0} עד {1} עבור {2} למשך {3} בהקשר ל {4}
luckperms.command.generic.meta.unset=בטל את הגדרת מפתח המטא {0} עבור {1} בהקשר {2}
luckperms.command.generic.meta.unset-temp=בטל את ההגדרה של מפתח מטא זמני {0} עבור {1} בהקשר ל {2}
luckperms.command.generic.meta.clear=סוג התאמת המטא {1} של {0} נוקה בהקשר {2}
luckperms.command.generic.contextual-data.title=נתונים הקשרים
luckperms.command.generic.contextual-data.mode.key=מצב
luckperms.command.generic.contextual-data.mode.server=שרת
luckperms.command.generic.contextual-data.mode.active-player=שחקן פעיל
luckperms.command.generic.contextual-data.contexts-key=הקשרים
luckperms.command.generic.contextual-data.prefix-key=קידומת
luckperms.command.generic.contextual-data.suffix-key=סיומת
luckperms.command.generic.contextual-data.primary-group-key=קבוצה ראשית
luckperms.command.generic.contextual-data.meta-key=מטא
luckperms.command.generic.contextual-data.null-result=אין תוצאות
luckperms.command.user.info.title=פרטי משתמש
luckperms.command.user.info.uuid-key=מזהה ייחודי למשתמש
luckperms.command.user.info.uuid-type-key=סוג
luckperms.command.user.info.uuid-type.mojang=מוג''אנג
luckperms.command.user.info.uuid-type.not-mojang=לא מחובר
luckperms.command.user.info.status-key=מצב
luckperms.command.user.info.status.online=מחובר
luckperms.command.user.info.status.offline=לא מחובר
luckperms.command.user.removegroup.error-primary=אינך יכול להסיר את המשתמש מהקבוצה הראשית שלו
luckperms.command.user.primarygroup.not-member={0} לא היה חבר ב-{1}, מוסיף עכשיו
luckperms.command.user.primarygroup.already-has=ל-{0} כבר הוגדרה {1} כקבוצה הראשית שלו
luckperms.command.user.primarygroup.warn-option=אזהרה\: ייתכן ששיטת חישוב הקבוצה הראשית שבה משתמש שרת זה ({0}) לא תשקף את השינוי הזה
luckperms.command.user.primarygroup.set=הקבוצה הראשית של {0} הוגדרה ל-{1}
luckperms.command.user.track.error-not-contain-group={0} לא נמצא כבר באף קבוצה ב-{1}
luckperms.command.user.track.unsure-which-track=לא בטוח באיזה סולם להשתמש, נא לציין ארגומנט
luckperms.command.user.track.missing-group-advice=צור את הקבוצה או הסר אותה מהסולם ונסה שוב
luckperms.command.user.promote.added-to-first={0} אינו באף קבוצה ב-{1}, אז הם נוספו לקבוצה הראשונה, {2} בהקשר ל {3}
luckperms.command.user.promote.not-on-track={0} אינו באף אחת מהקבוצות ב-{1}, ולכן לא קודם
luckperms.command.user.promote.success=קידום של {0} לאורך מסלול {1} מ-{2} ל-{3} בהקשר ל {4}
luckperms.command.user.promote.end-of-track={0} הגיע לסוף הסולם, לא ניתן לקדם את {1}
luckperms.command.user.promote.next-group-deleted=הקבוצה הבאה במסלול, {0}, כבר לא קיימת
luckperms.command.user.promote.unable-to-promote=אין אפשרות לעלות בדרגה את המשתמש
luckperms.command.user.demote.success=הורדת {0} לאורך הסולם {1} מ-{2} ל-{3} בהקשר ל {4}
luckperms.command.user.demote.end-of-track=הגיע לסוף סולם {0}, אז {1} הוסר מ-{2}
luckperms.command.user.demote.end-of-track-not-removed=סוף סולם {0}, אך {1} לא הוסר מהקבוצה הראשונה
luckperms.command.user.demote.previous-group-deleted=הקבוצה הקודמת בסולם, {0}, אינה קיימת יותר
luckperms.command.user.demote.unable-to-demote=אין אפשרות להוריד בדרגה את המשתמש
luckperms.command.group.list.title=קבוצות
luckperms.command.group.delete.not-default=לא ניתן למחוק את קבוצת ברירת המחדל
luckperms.command.group.info.title=פרטי הקבוצה
luckperms.command.group.info.display-name-key=שם תצוגה
luckperms.command.group.info.weight-key=משקל
luckperms.command.group.setweight.set=הגדר את המשקל ל- {0} לקבוצה {1}
luckperms.command.group.setdisplayname.doesnt-have=ל- {0} אין שם תצוגה
luckperms.command.group.setdisplayname.already-has=ל- {0} כבר יש שם תצוגה של {1}
luckperms.command.group.setdisplayname.already-in-use=שם התצוגה {0} כבר נמצא בשימוש על ידי {1}
luckperms.command.group.setdisplayname.set=הגדר את שם התצוגה ל- {0} עבור קבוצה {1} בהקשר {2}
luckperms.command.group.setdisplayname.removed=הוסר שם התצוגה לקבוצה {0} בהקשר {1}
luckperms.command.track.list.title=מסלולים
luckperms.command.track.path.empty=אף אחד
luckperms.command.track.info.showing-track=מראה מסלול
luckperms.command.track.info.path-property=נתיב
luckperms.command.track.clear=מסלול הקבוצות של {0} נמחק
luckperms.command.track.append.success=הקבוצה {0} צורפה לסולם {1}
luckperms.command.track.insert.success=הקבוצה {0} נוספה לסולם {1} במיקום {2}
luckperms.command.track.insert.error-number=מספר צפוי אך במקום זאת קיבל\: {0}
luckperms.command.track.insert.error-invalid-pos=לא ניתן להכניס במיקום {0}
luckperms.command.track.insert.error-invalid-pos-reason=מיקום לא נכון
luckperms.command.track.remove.success=הקבוצה {0} הוסרה מסולם {1}
luckperms.command.track.error-empty=לא ניתן להשתמש ב-{0} מכיוון שהוא ריק או מכיל רק קבוצה אחת
luckperms.command.track.error-multiple-groups={0} הוא חבר במספר קבוצות בסולם הזה
luckperms.command.track.error-ambiguous=לא ניתן לקבוע את מיקומם
luckperms.command.track.already-contains={0} כבר מכיל {1}
luckperms.command.track.doesnt-contain={0} אינו מכיל {1}
luckperms.command.log.load-error=לא ניתן לטעון את יומן הרישום
luckperms.command.log.invalid-page=מספר עמוד לא חוקי
luckperms.command.log.invalid-page-range=אנא הכניסו ערך בין {0} ל-{1}
luckperms.command.log.empty=היומן ריק, אין מה להראות
luckperms.command.log.notify.error-console=לא יכול להפעיל הודעות לקונסולה
luckperms.command.log.notify.enabled-term=מופעל
luckperms.command.log.notify.disabled-term=מופסק
luckperms.command.log.notify.changed-state={0} פלט רישום
luckperms.command.log.notify.already-on=אתה כבר מקבל התראות
luckperms.command.log.notify.already-off=אינך מקבל כעת התראות
luckperms.command.log.notify.invalid-state=מצב לא ידוע. אמור להיות {0} או {1}
luckperms.command.log.show.search=מראה פעולות אחרונות בעבור שאילתא {0}
luckperms.command.log.show.recent=הצגת פעולות אחרונות
luckperms.command.log.show.by=הצגת פעולות אחרונות על-ידי {0}
luckperms.command.log.show.history=מייצג היסטוריה עבור {0} {1}
luckperms.command.export.error-term=שגיאה
luckperms.command.export.already-running=תהליך יצוא נוסף כבר פועל
luckperms.command.export.file.already-exists=קובץ {0} כבר קיים
luckperms.command.export.file.not-writable=קובץ {0} אינו ניתן לכתיבה
luckperms.command.export.file.success=יוצא בהצלחה אל {0}
luckperms.command.export.file-unexpected-error-writing=אירעה שגיאה בלתי צפויה במהלך הכתיבה לקובץ
luckperms.command.export.web.export-code=קוד ייצוא
luckperms.command.export.web.import-command-description=השתמש בפקודה הבאה כדי לייבא
luckperms.command.import.term=ייבוא
luckperms.command.import.error-term=שגיאה
luckperms.command.import.already-running=תהליך ייבוא נוסף כבר פועל
luckperms.command.import.file.doesnt-exist=הקובץ {0} לא קיים
luckperms.command.import.file.not-readable=קובץ {0} אינו ניתן לקריאה
luckperms.command.import.file.unexpected-error-reading=אירעה שגיאה לא צפויה במהלך הקריאה מקובץ הייבוא
luckperms.command.import.file.correct-format=האם זה הפורמט הנכון?
luckperms.command.import.web.unable-to-read=לא ניתן לקרוא את הנתונים באמצעות הקוד נתון
luckperms.command.import.progress.percent={0} הושלם
luckperms.command.import.progress.operations={0}/{1} פעולות הושלמו
luckperms.command.import.starting=מתחיל תהליך ייבוא
luckperms.command.import.completed=הושלם
luckperms.command.import.duration=לקח {0} שניות
luckperms.command.bulkupdate.must-use-console=ניתן להשתמש בפקודת העדכון בכמות גדולה רק מהקונסול
luckperms.command.bulkupdate.invalid-data-type=סוג אינו תקין, ציפה ל{0}
luckperms.command.bulkupdate.invalid-constraint=אילוץ לא חוקי {0}
luckperms.command.bulkupdate.invalid-constraint-format=האילוצים צריכים להיות בפורמט {0}
luckperms.command.bulkupdate.invalid-comparison=אופרטור השוואה לא חוקי {0}
luckperms.command.bulkupdate.invalid-comparison-format=צפוי לאחד מהבאים\: {0}
luckperms.command.bulkupdate.queued=פעולת עדכון בבת אחת נמצאת בתור
luckperms.command.bulkupdate.confirm=הרץ{0} כדי לבצע את העדכון
luckperms.command.bulkupdate.unknown-id=הפעולה עם המזהה {0} אינה קיימת או שפג תוקפו
luckperms.command.bulkupdate.starting=מריץ עדכון בכמות גדולה
luckperms.command.bulkupdate.success=העדכון בכמות גדולה הושלם בהצלחה
luckperms.command.bulkupdate.success.statistics.nodes=סך כל הקשרים המושפעים
luckperms.command.bulkupdate.success.statistics.users=משתמשים מושפעות לחלוטין
luckperms.command.bulkupdate.success.statistics.groups=קבוצות מושפעות לחלוטין
luckperms.command.bulkupdate.failure=עדכון בכמות גדולה נכשל, בדוק אם יש שגיאות בקונסולה
luckperms.command.update-task.request=התבקשה משימת עדכון, אנא המתינו
luckperms.command.update-task.complete=עדכון המשימה הושלם
luckperms.command.update-task.push.attempting=כעת מנסה לדחוף לשרתים אחרים
luckperms.command.update-task.push.complete=שרתים אחרים קיבלו הודעה דרך {0} בהצלחה
luckperms.command.update-task.push.error=שגיאה בעת דחיפה של שינויים לשרתים אחרים
luckperms.command.update-task.push.error-not-setup=לא ניתן לדחוף שינויים לשרתים אחרים מכיוון ששירות הודעות לא הוגדר
luckperms.command.reload-config.success=קובץ התצורה נטען מחדש
luckperms.command.reload-config.restart-note=אפשרויות מסוימות יחולו רק לאחר הפעלה מחדש של השרת
luckperms.command.translations.searching=מחפש תרגומים זמינים, אנא המתינו...
luckperms.command.translations.searching-error=לא ניתן להשיג רשימה של תרגומים זמינים
luckperms.command.translations.installed-translations=תרגומים מותקנים
luckperms.command.translations.available-translations=תרגומים זמינים
luckperms.command.translations.percent-translated={0}% תורגם
luckperms.command.translations.translations-by=על ידי
luckperms.command.translations.installing=מוריד תרגומים, נא לחכות...
luckperms.command.translations.download-error=לא ניתן להוריד תרגום בשביל {0}
luckperms.command.translations.installing-specific=מתקין שפה {0}...
luckperms.command.translations.install-complete=ההתקנה הושלמה
luckperms.command.translations.download-prompt=השתמש ב-{0} כדי להוריד ולהתקין גרסאות מעודכנות של תרגומים אלה שסופקו על ידי הקהילה
luckperms.command.translations.download-override-warning=שימו לב שזה יבטל את כל השינויים שביצעת עבור שפות אלה
luckperms.usage.user.description=קבוצה של פקודות לניהול משתמשים בתוך LuckPerms. (''משתמש'' ב-LuckPerms הוא רק שחקן, ויכול להתייחס ל-UUID או לשם משתמש)
luckperms.usage.group.description=קבוצה של פקודות לניהול קבוצות בתוך LuckPerms. קבוצות הן רק אוספים של הקצאות הרשאות שניתן לתת למשתמשים. קבוצות חדשות נוצרות באמצעות הפקודה ''צור קבוצה''.
luckperms.usage.track.description=קבוצה של פקודות לניהול מסלולים בתוך LuckPerms. מסלולים הם אוסף מסודר של קבוצות שניתן להשתמש בהן להגדרת קידום והורדות.
luckperms.usage.log.description=קבוצה של פקודות לניהול פונקציונליות הרישום בתוך LuckPerms.
luckperms.usage.sync.description=טוען מחדש את כל הנתונים מאחסון הפלאגין לזיכרון, ומחיל את כל השינויים שיזוהו.
luckperms.usage.info.description=מדפיס מידע כללי על מופע הפלאגין הפעיל.
luckperms.usage.editor.description=יוצר הפעלה חדשה של עורך אתרים
luckperms.usage.editor.argument.type=הסוגים לטעינה לעורך. (''הכל'', ''משתמשים'' או ''קבוצות'')
luckperms.usage.editor.argument.filter=הרשאה לסנן ערכים של משתמשים לפי
luckperms.usage.verbose.description=שולט במערכת ניטור בדיקת ההרשאות המילולית של הפלאגין.
luckperms.usage.verbose.argument.action=האם להפעיל/לבטל רישום או להעלות את הפלט הרשום
luckperms.usage.verbose.argument.filter=המסנן בשביל להתאים ערכים
luckperms.usage.verbose.argument.commandas=המשתמש/פקודה לרוץ
luckperms.usage.tree.description=יוצר תצוגת תצוגת עץ (סדר רשימות מסודרת) של כל ההרשאות הידועות ל- LuckPerms.
luckperms.usage.tree.argument.scope=השורש של העץ. לפרט "." לכלול את כל ההרשאות
luckperms.usage.tree.argument.player=שם של שחקן מקוון שאפשר לבדוק מולו
luckperms.usage.search.description=מחפש את כל המשתמשים/הקבוצות עם הרשאה ספציפית
luckperms.usage.search.argument.permission=ההרשאה לחיפוש
luckperms.usage.search.argument.page=הדף לצפייה
luckperms.usage.network-sync.description=סנכרן שינויים עם האחסון ובקש שכל שאר השרתים ברשת יעשו את אותו הדבר
luckperms.usage.import.description=מייבא נתונים מקובץ ייצוא (שנוצר בעבר)
luckperms.usage.import.argument.file=הקובץ לייבוא מ
luckperms.usage.import.argument.replace=החלף נתונים קיימים במקום מיזוג
luckperms.usage.import.argument.upload=העלה את הנתונים מיצוא קודם
luckperms.usage.export.description=מייצא את כל נתוני ההרשאות לקובץ ''ייצוא''. ניתן לייבא מחדש במועד מאוחר יותר.
luckperms.usage.export.argument.file=הקובץ לייצוא ל
luckperms.usage.export.argument.without-users=לא לכלול את המשתמשים מהיצוא
luckperms.usage.export.argument.without-groups=לא לכלות את הקבוצות מהיצוא
luckperms.usage.export.argument.upload=לעלות את כל נתוני ההרשאות לעורך אינטרנט. ניתן לייבא מחדש במועד מאוחר יותר.
luckperms.usage.reload-config.description=טען מחדש כמה מאפשריות התצורה
luckperms.usage.bulk-update.description=בצע שאילתות שינויים בכמות גדולה על כל הנתונים
luckperms.usage.bulk-update.argument.data-type=סוג הנתונים המשתנים. (''הכל'', ''משתמשים'' או ''קבוצות'')
luckperms.usage.bulk-update.argument.action=הפעולה שיש לבצע בנתונים. (''עדכן'' או ''מחק'')
luckperms.usage.bulk-update.argument.action-field=התחום לפעול בו. נדרש רק עבור ''עדכון''. (''רשות'', ''שרת'' או ''עולם'')
luckperms.usage.bulk-update.argument.action-value=הערך שיש להחליף בו. נדרש רק עבור ''עדכון''.
luckperms.usage.bulk-update.argument.constraint=האילוצים הנדרשים לעדכון
luckperms.usage.translations.description=נהל תרגומים
luckperms.usage.translations.argument.install=תת-פקודה להתקנת תרגומים
luckperms.usage.apply-edits.description=מחיל שינויים בהרשאות שנעשו מעורך האינטרנט
luckperms.usage.apply-edits.argument.code=הקוד הייחודי לנתונים
luckperms.usage.apply-edits.argument.target=על מי להחיל את הנתונים
luckperms.usage.create-group.description=יצירת קבוצה חדשה
luckperms.usage.create-group.argument.name=השם של הקבוצה
luckperms.usage.create-group.argument.weight=משקל הקבוצה
luckperms.usage.create-group.argument.display-name=שם התצוגה של הקבוצה
luckperms.usage.delete-group.description=מחיקת קבוצה
luckperms.usage.delete-group.argument.name=השם של הקבוצה
luckperms.usage.list-groups.description=רשום את כל הקבוצות בפלטפורמה
luckperms.usage.create-track.description=יצירת מסלול חדש
luckperms.usage.create-track.argument.name=השם של המסלול
luckperms.usage.delete-track.description=מחיקת המסלול
luckperms.usage.delete-track.argument.name=השם של המסלול
luckperms.usage.list-tracks.description=רשום את כל המסלולים בפלטפורמה
luckperms.usage.user-info.description=מראה מידע על המשתמש
luckperms.usage.user-switchprimarygroup.description=מחליף את הקבוצה הראשית של המשתמש
luckperms.usage.user-switchprimarygroup.argument.group=הקבוצה לעבור אליה
luckperms.usage.user-promote.description=מקדם את הסולם של המשתמש
luckperms.usage.user-promote.argument.track=הסולם לקידום המשתמש למעלה
luckperms.usage.user-promote.argument.context=ההקשרים לקידום המשתמש
luckperms.usage.user-promote.argument.dont-add-to-first=לקדם את המשתמש רק אם הוא כבר על סולם
luckperms.usage.user-demote.description=מוריד את המשתמש בסולם
luckperms.usage.user-demote.argument.track=הסולם להורדת המשתמש למטה
luckperms.usage.user-demote.argument.context=ההקשרים להורדת המשתמש
luckperms.usage.user-demote.argument.dont-remove-from-first=למנוע את הסרת המשתמש מהקבוצה הראשונה
luckperms.usage.user-clone.description=לשכפל את המשתמש
luckperms.usage.user-clone.argument.user=השם/uuid של המשתמש שיש לשכפל עליו
luckperms.usage.group-info.description=נותן מידע על הקבוצה
luckperms.usage.group-listmembers.description=הצג את המשתמשים/הקבוצות שיורשים מקבוצה זו
luckperms.usage.group-listmembers.argument.page=העמוד לצפייה
luckperms.usage.group-setweight.description=הגדר את משקל הקבוצות
luckperms.usage.group-setweight.argument.weight=את המשקל להגדיר
luckperms.usage.group-set-display-name.description=הגדר את שם התצוגה של הקבוצות
luckperms.usage.group-set-display-name.argument.name=את השם להגדיר
luckperms.usage.group-set-display-name.argument.context=ההקשרים להגדרת השם
luckperms.usage.group-rename.description=שנה את שם הקבוצה
luckperms.usage.group-rename.argument.name=השם החדש
luckperms.usage.group-clone.description=לשכפל את הקבוצה
luckperms.usage.group-clone.argument.name=השם של הקבוצה לשכפל ל
luckperms.usage.holder-editor.description=פותח את העורך אתר הרשאות
luckperms.usage.holder-showtracks.description=מפרט את הרצועות שהאובייקט נמצא בהן
luckperms.usage.holder-clear.description=מסיר את כל ההרשאות, קבוצות אם והמטא
luckperms.usage.holder-clear.argument.context=ההקשרים שיש לסנן לפיהם
luckperms.usage.permission.description=ערוך הרשאות
luckperms.usage.parent.description=עריכת ירושות
luckperms.usage.meta.description=ערוך ערכי נתוני מטא
luckperms.usage.permission-info.description=מפרט את צמתי ההרשאה שיש לאובייקט
luckperms.usage.permission-info.argument.page=הדף לצפייה
luckperms.usage.permission-info.argument.sort-mode=כיצד למיין את הערכים
luckperms.usage.permission-set.description=מגדיר הרשאה עבור האובייקט
luckperms.usage.permission-set.argument.node=צומת ההרשאה להגדיר
luckperms.usage.permission-set.argument.value=הערך של הקשר
luckperms.usage.permission-set.argument.context=ההקשרים להוספת ההרשאה ב
luckperms.usage.permission-unset.description=מבטל את הגדרת ההרשאה עבור האובייקט
luckperms.usage.permission-unset.argument.node=את קשר ההרשאה לבטל
luckperms.usage.permission-unset.argument.context=ההקשרים שבהם יש להסיר את ההרשאה
luckperms.usage.permission-settemp.description=מגדיר הרשאה עבור האובייקט באופן זמני
luckperms.usage.permission-settemp.argument.node=להגדיר קשר להרשאה
luckperms.usage.permission-settemp.argument.value=הערך של הקשר
luckperms.usage.permission-settemp.argument.duration=משך הזמן עד שיפוג קשר ההרשאה
luckperms.usage.permission-settemp.argument.temporary-modifier=כיצד יש להחיל את ההרשאה הזמנית
luckperms.usage.permission-settemp.argument.context=ההקשרים להוספת ההרשאה
luckperms.usage.permission-unsettemp.description=מבטל הרשאה זמנית עבור האובייקט
luckperms.usage.permission-unsettemp.argument.node=את קשר ההרשאה לבטל
luckperms.usage.permission-unsettemp.argument.duration=משך ההפחתה
luckperms.usage.permission-unsettemp.argument.context=ההקשרים שבהם יש להסיר את ההרשאה
luckperms.usage.permission-check.description=בודק אם לאובייקט יש קשר הרשאה מסוים
luckperms.usage.permission-check.argument.node=לבדוק קשר ההרשאה
luckperms.usage.permission-clear.description=מנקה את כל ההרשאות
luckperms.usage.permission-clear.argument.context=ההקשרים שיש לסנן לפיהם
luckperms.usage.parent-info.description=מפרט את הקבוצות שמהן אובייקט זה יורש
luckperms.usage.parent-info.argument.page=הדף לצפייה
luckperms.usage.parent-info.argument.sort-mode=כיצד למיין את הערכים
luckperms.usage.parent-set.description=מסיר את כל הקבוצות האחרות שהאובייקט כבר יורש ומוסיף אותן לזו הנתונה
luckperms.usage.parent-set.argument.group=הקבוצה להגדיר
luckperms.usage.parent-set.argument.context=ההקשרים שבהם יש להגדיר את הקבוצה
luckperms.usage.parent-add.description=מגדיר קבוצה נוספת עבור האובייקט שיורש ממנו הרשאות
luckperms.usage.parent-add.argument.group=הקבוצה לרשת ממנה
luckperms.usage.parent-add.argument.context=ההקשרים שבהם יש לרשת את הקבוצה
luckperms.usage.parent-remove.description=מסיר כלל ירושה שהוגדר בעבר
luckperms.usage.parent-remove.argument.group=הקבוצה להסיר
luckperms.usage.parent-remove.argument.context=ההקשרים שבהם יש להסיר את הקבוצה
luckperms.usage.parent-set-track.description=מסיר את כל שאר הקבוצות שהאובייקט יורש מהן כבר במסלול הנתון ומוסיף אותן לזו הנתונה
luckperms.usage.parent-set-track.argument.track=המסלול שצריך לצאת אליו
luckperms.usage.parent-set-track.argument.group=הקבוצה שיש להגדיר אליה, או מספר המתייחס למיקום הקבוצה במסלול הנתון
luckperms.usage.parent-set-track.argument.context=ההקשרים שבהם יש להגדיר את הקבוצה
luckperms.usage.parent-add-temp.description=מגדיר קבוצה אחרת עבור האובייקט שיורש ממנו הרשאות באופן זמני
luckperms.usage.parent-add-temp.argument.group=הקבוצה לרשת ממנה
luckperms.usage.parent-add-temp.argument.duration=משך החברות בקבוצה
luckperms.usage.parent-add-temp.argument.temporary-modifier=כיצד יש להחיל את ההרשאה הזמנית
luckperms.usage.parent-add-temp.argument.context=ההקשרים שבהם יש לרשת את הקבוצה
luckperms.usage.parent-remove-temp.description=מסיר כלל ירושה זמני שהוגדר בעבר
luckperms.usage.parent-remove-temp.argument.group=הקבוצה שיש להסיר
luckperms.usage.parent-remove-temp.argument.duration=משך ההפחתה
luckperms.usage.parent-remove-temp.argument.context=ההקשרים שבהם יש להסיר את הקבוצה
luckperms.usage.parent-clear.description=מנקה את כל קבוצות האם
luckperms.usage.parent-clear.argument.context=ההקשרים שיש לסנן לפיהם
luckperms.usage.parent-clear-track.description=מנקה את כל קבוצות האם בסולם נתון
luckperms.usage.parent-clear-track.argument.track=הסולם להסרה
luckperms.usage.parent-clear-track.argument.context=ההקשרים שיש לסנן לפיהם
luckperms.usage.meta-info.description=מציג את כל המטא של הצ''אט
luckperms.usage.meta-set.description=מגדיר ערך מטא
luckperms.usage.meta-set.argument.key=המפתח להגדרה
luckperms.usage.meta-set.argument.value=הערך שיש להגדיר
luckperms.usage.meta-set.argument.context=ההקשרים להוספת המטא צמד
luckperms.usage.meta-unset.description=מבטל הגדרת ערך מטא
luckperms.usage.meta-unset.argument.key=מפתח ההגדרה לביטול
luckperms.usage.meta-unset.argument.context=ההקשרים שבהם יש להסיר את צמד המטא
luckperms.usage.meta-settemp.description=מגדיר ערך מטא באופן זמני
luckperms.usage.meta-settemp.argument.key=המפתח להגדרה
luckperms.usage.meta-settemp.argument.value=הערך להגדרה
luckperms.usage.meta-settemp.argument.duration=משך הזמן עד לפקיעת ערך המטא
luckperms.usage.meta-settemp.argument.context=ההקשרים להוספת המטא צמד
luckperms.usage.meta-unsettemp.description=מבטל הגדרה של מטא ערך זמני
luckperms.usage.meta-unsettemp.argument.key=המפתח לביטול
luckperms.usage.meta-unsettemp.argument.context=ההקשרים שבהם יש להסיר את צמד המטא
luckperms.usage.meta-addprefix.description=הוספת קידומת
luckperms.usage.meta-addprefix.argument.priority=העדיפות להוסיף את הקידומת ב
luckperms.usage.meta-addprefix.argument.prefix=מחרוזת הקידומת
luckperms.usage.meta-addprefix.argument.context=ההקשרים להוספת הקידומת
luckperms.usage.meta-addsuffix.description=הוספת סיומת
luckperms.usage.meta-addsuffix.argument.priority=העדיפות להוסיף את הסיומת ב
luckperms.usage.meta-addsuffix.argument.suffix=מחרוזת הסיומת
luckperms.usage.meta-addsuffix.argument.context=ההקשרים להוספת הסיומת
luckperms.usage.meta-setprefix.description=הגדרת קידומת
luckperms.usage.meta-setprefix.argument.priority=העדיפות להגדיר את הקידומת ב
luckperms.usage.meta-setprefix.argument.prefix=מחרוזת הקידומת
luckperms.usage.meta-setprefix.argument.context=ההקשרים שבהם יש להגדיר את הקידומת
luckperms.usage.meta-setsuffix.description=הגדרת סיומת
luckperms.usage.meta-setsuffix.argument.priority=העדיפות להגדיר את הסיומת ב
luckperms.usage.meta-setsuffix.argument.suffix=מחרוזת הסיומת
luckperms.usage.meta-setsuffix.argument.context=ההקשרים שבהם יש להגדיר את הסיומת
luckperms.usage.meta-removeprefix.description=הסרת קידומת
luckperms.usage.meta-removeprefix.argument.priority=העדיפות להסיר את הקידומת ב
luckperms.usage.meta-removeprefix.argument.prefix=מחרוזת הקידומת
luckperms.usage.meta-removeprefix.argument.context=ההקשרים שבהם יש להסיר את הקידומת
luckperms.usage.meta-removesuffix.description=מסיר סיומת
luckperms.usage.meta-removesuffix.argument.priority=העדיפות להסיר את הסיומת ב
luckperms.usage.meta-removesuffix.argument.suffix=מחרוזת הסיומת
luckperms.usage.meta-removesuffix.argument.context=ההקשרים שבהם יש להסיר את הסיומת
luckperms.usage.meta-addtemp-prefix.description=מוסיף קידומת באופן זמני
luckperms.usage.meta-addtemp-prefix.argument.priority=העדיפות להוסיף את הקידומת ב
luckperms.usage.meta-addtemp-prefix.argument.prefix=מחרוזת הקידומת
luckperms.usage.meta-addtemp-prefix.argument.duration=משך הזמן עד לפקיעת הקידומת
luckperms.usage.meta-addtemp-prefix.argument.context=ההקשרים להוספת הקידומת
luckperms.usage.meta-addtemp-suffix.description=מוסיף סיומת זמנית
luckperms.usage.meta-addtemp-suffix.argument.priority=העדיפות להוסיף את הסיומת ב
luckperms.usage.meta-addtemp-suffix.argument.suffix=מחרוזת הסיומת
luckperms.usage.meta-addtemp-suffix.argument.duration=משך הזמן עד לפקיעת הסיומת
luckperms.usage.meta-addtemp-suffix.argument.context=ההקשרים להוספת הסיומת
luckperms.usage.meta-settemp-prefix.description=מגדיר קידומת באופן זמני
luckperms.usage.meta-settemp-prefix.argument.priority=העדיפות להגדיר את הקידומת ב
luckperms.usage.meta-settemp-prefix.argument.prefix=מחרוזת הקידומת
luckperms.usage.meta-settemp-prefix.argument.duration=משך הזמן עד לפקיעת הקידומת
luckperms.usage.meta-settemp-prefix.argument.context=ההקשרים שבהם יש להגדיר את הקידומת
luckperms.usage.meta-settemp-suffix.description=מגדיר סיומת זמנית
luckperms.usage.meta-settemp-suffix.argument.priority=העדיפות להגדיר את הסיומת ב
luckperms.usage.meta-settemp-suffix.argument.suffix=מחרוזת הסיומת
luckperms.usage.meta-settemp-suffix.argument.duration=משך הזמן עד לפקיעת הסיומת
luckperms.usage.meta-settemp-suffix.argument.context=ההקשרים שבהם יש להגדיר את הסיומת
luckperms.usage.meta-removetemp-prefix.description=מסיר קידומת זמנית
luckperms.usage.meta-removetemp-prefix.argument.priority=העדיפות להסיר את הקידומת ב
luckperms.usage.meta-removetemp-prefix.argument.prefix=מחרוזת הקידומת
luckperms.usage.meta-removetemp-prefix.argument.context=ההקשרים שבהם יש להסיר את הקידומת
luckperms.usage.meta-removetemp-suffix.description=מסיר סיומת זמנית
luckperms.usage.meta-removetemp-suffix.argument.priority=העדיפות להסיר את הסיומת ב
luckperms.usage.meta-removetemp-suffix.argument.suffix=מחרוזת הסיומת
luckperms.usage.meta-removetemp-suffix.argument.context=ההקשרים שבהם יש להסיר את הסיומת
luckperms.usage.meta-clear.description=מנקה את כל המטא
luckperms.usage.meta-clear.argument.type=סוג המטא שיש להסיר
luckperms.usage.meta-clear.argument.context=ההקשרים שיש לסנן לפיהם
luckperms.usage.track-info.description=נותן מידע על הסולם
luckperms.usage.track-editor.description=פותח את עורך הרשאות האינטרנט
luckperms.usage.track-append.description=מוסיף קבוצה לסוף הסולם
luckperms.usage.track-append.argument.group=הקבוצה שיש לצרף
luckperms.usage.track-insert.description=הוספת קבוצה במיקום נתון לאורך הסולם
luckperms.usage.track-insert.argument.group=הקבוצה להכניס
luckperms.usage.track-insert.argument.position=המיקום שבו יש להוסיף את הקבוצה (המיקום הראשון בסולם הוא 1)
luckperms.usage.track-remove.description=מסיר קבוצה מהסולם
luckperms.usage.track-remove.argument.group=הקבוצה שיש להסיר
luckperms.usage.track-clear.description=מנקה את הקבוצות על השולם
luckperms.usage.track-rename.description=שנה את שם המסלול
luckperms.usage.track-rename.argument.name=השם החדש
luckperms.usage.track-clone.description=לשכפל את המסלול
luckperms.usage.track-clone.argument.name=השם של המסלול לשכפל ל
luckperms.usage.log-recent.description=צפה בפעולות האחרונות
luckperms.usage.log-recent.argument.user=השם / הוראות המשתמש לסנן לפי
luckperms.usage.log-recent.argument.page=מספר העמוד לצפייה
luckperms.usage.log-search.description=חפש ביומן ערך
luckperms.usage.log-search.argument.query=השאילתה לפיה יש לחפש
luckperms.usage.log-search.argument.page=מספר העמוד לצפייה
luckperms.usage.log-notify.description=החלף הודעות ביומן
luckperms.usage.log-notify.argument.toggle=האם להפעיל או לכבות
luckperms.usage.log-user-history.description=צפה היסטוריה של המשתמש
luckperms.usage.log-user-history.argument.user=השם / הוראות המשתמש של משתמש
luckperms.usage.log-user-history.argument.page=מספר העמוד לצפייה
luckperms.usage.log-group-history.description=צפה היסטוריה של הקבוצה
luckperms.usage.log-group-history.argument.group=השם של הקבוצה
luckperms.usage.log-group-history.argument.page=מספר העמוד לצפייה
luckperms.usage.log-track-history.description=צפה היסטוריה של המסלול
luckperms.usage.log-track-history.argument.track=השם של המסלול
luckperms.usage.log-track-history.argument.page=מספר העמוד לצפייה
luckperms.usage.sponge.description=ערוך נתוני ספונג'' נוספים
luckperms.usage.sponge.argument.collection=האוסף לשאילתה
luckperms.usage.sponge.argument.subject=הנושא שיש לשנות
luckperms.usage.sponge-permission-info.description=מציג מידע על ההרשאות של הנושא
luckperms.usage.sponge-permission-info.argument.contexts=ההקשרים שיש לסנן לפיהם
luckperms.usage.sponge-permission-set.description=מגדיר הרשאה לנושא
luckperms.usage.sponge-permission-set.argument.node=להגדיר קשר ההרשאה
luckperms.usage.sponge-permission-set.argument.tristate=הערך שאליו יש להגדיר את ההרשאה
luckperms.usage.sponge-permission-set.argument.contexts=ההקשרים שבהם יש להגדיר את ההרשאה
luckperms.usage.sponge-permission-clear.description=מנקה את הרשאות הנושאים
luckperms.usage.sponge-permission-clear.argument.contexts=ההקשרים שבהם יש לנקות הרשאות
luckperms.usage.sponge-parent-info.description=מציג מידע על קבוצות אם הנבדק
luckperms.usage.sponge-parent-info.argument.contexts=ההקשרים שיש לסנן לפיהם
luckperms.usage.sponge-parent-add.description=מוסיף קבוצת אם לנושא
luckperms.usage.sponge-parent-add.argument.collection=אוסף הנושאים שבו נמצאת קבוצת האם
luckperms.usage.sponge-parent-add.argument.subject=שם הנבדק של קבוצת האם
luckperms.usage.sponge-parent-add.argument.contexts=ההקשרים להוספת קבוצת אם
luckperms.usage.sponge-parent-remove.description=מסיר קבוצת אם מהנושא
luckperms.usage.sponge-parent-remove.argument.collection=אוסף הנושאים שבו נמצא קבצות האם בתוך הנושא
luckperms.usage.sponge-parent-remove.argument.subject=שם הנבדק האב
luckperms.usage.sponge-parent-remove.argument.contexts=ההקשרים שבהם יש להסיר את קבוצת האם
luckperms.usage.sponge-parent-clear.description=מנקה את קבוצות האם הנבדקות
luckperms.usage.sponge-parent-clear.argument.contexts=ההקשרים לנקות את קבוצות האם
luckperms.usage.sponge-option-info.description=מציג מידע על אפשרויות הנושא
luckperms.usage.sponge-option-info.argument.contexts=ההקשרים שיש לסנן לפיהם
luckperms.usage.sponge-option-set.description=מגדיר אפשרות עבור הנושא
luckperms.usage.sponge-option-set.argument.key=המפתח להגדרה
luckperms.usage.sponge-option-set.argument.value=הערך שאליו יש להגדיר את המפתח
luckperms.usage.sponge-option-set.argument.contexts=ההקשרים שבהם ניתן להגדיר את האפשרות
luckperms.usage.sponge-option-unset.description=בטל את האפשרות עבור הנושא
luckperms.usage.sponge-option-unset.argument.key=מפתח ההגדרה לביטול
luckperms.usage.sponge-option-unset.argument.contexts=ההקשרים שבהם יש לבטל את הגדרת המפתח ב-
luckperms.usage.sponge-option-clear.description=ניקוי אפשרויות הנושאים
luckperms.usage.sponge-option-clear.argument.contexts=ההקשרים לניקוי אפשרויות ב
