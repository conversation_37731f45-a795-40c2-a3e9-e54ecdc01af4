luckperms.logs.actionlog-prefix=लॉग
luckperms.logs.verbose-prefix=वीबी
luckperms.logs.export-prefix=निर्यात करना
luckperms.commandsystem.available-commands=उपलब्ध आदेशों को देखने के लिए {0} का उपयोग करें
luckperms.commandsystem.command-not-recognised=आज्ञा पहचाना नहीं गया
luckperms.commandsystem.no-permission=आपको इस आदेश का उपयोग करने की अनुमति नहीं है\!
luckperms.commandsystem.no-permission-subcommands=आपको किसी उप कमांड का उपयोग करने की अनुमति नहीं है
luckperms.commandsystem.already-executing-command=एक और आदेश निष्पादित किया जा रहा है, इसके समाप्त होने की प्रतीक्षा कर रहा है...
luckperms.commandsystem.usage.sub-commands-header=उप आदेश
luckperms.commandsystem.usage.usage-header=कमांड उपयोग
luckperms.commandsystem.usage.arguments-header=बहस
luckperms.first-time.no-permissions-setup=ऐसा लगता है कि अभी तक कोई अनुमति सेट नहीं की गई है\!
luckperms.first-time.use-console-to-give-access=इससे पहले कि आप गेम में किसी भी LuckPerms कमांड का उपयोग कर सकें, आपको स्वयं को एक्सेस देने के लिए कंसोल का उपयोग करने की आवश्यकता है
luckperms.first-time.console-command-prompt=अपना कंसोल खोलें और चलाएं
luckperms.first-time.next-step=ऐसा करने के बाद, आप अपने अनुमति असाइनमेंट और समूहों को परिभाषित करना शुरू कर सकते हैं।
luckperms.first-time.wiki-prompt=पता नहीं कहाँ से शुरू करें? यहां देखें\: {0}
luckperms.login.try-again=बाद में पुन\: प्रयास करें
luckperms.login.loading-database-error=अनुमति डेटा लोड करते समय एक डेटाबेस त्रुटि उत्पन्न हुई
luckperms.login.server-admin-check-console-errors=यदि आप एक सर्वर व्यवस्थापक हैं, तो कृपया किसी भी त्रुटि के लिए कंसोल की जाँच करें
luckperms.login.server-admin-check-console-info=अधिक जानकारी के लिए कृपया सर्वर कंसोल की जाँच करें
luckperms.login.data-not-loaded-at-pre=आपके उपयोगकर्ता के लिए अनुमति डेटा प्री-लॉगिन चरण के दौरान लोड नहीं किया गया था
luckperms.login.unable-to-continue=जारी रखने में असमर्थ
luckperms.login.craftbukkit-offline-mode-error=यह संभवतः क्राफ्टबुककिट और ऑनलाइन-मोड सेटिंग के बीच विरोध के कारण है
luckperms.login.unexpected-error=आपका मेसेज भेजने एक अप्रत्याशित त्रुटि हुई है.
luckperms.opsystem.disabled=इस सर्वर पर वैनिला ओपी सिस्टम अक्षम है
luckperms.opsystem.sponge-warning=कृपया ध्यान दें कि अनुमति प्लगइन स्थापित होने पर सर्वर ऑपरेटर की स्थिति स्पंज अनुमति जांच पर कोई प्रभाव नहीं डालती है, आपको सीधे उपयोगकर्ता डेटा संपादित करना होगा
luckperms.duration.unit.years.plural={0} वर्ष
luckperms.duration.unit.years.singular={0} वर्ष
luckperms.duration.unit.years.short={0}y
luckperms.duration.unit.months.plural={0} महीनों
luckperms.duration.unit.months.singular={0} महीनों
luckperms.duration.unit.months.short={0}मो
luckperms.duration.unit.weeks.plural={0} सप्ताह
luckperms.duration.unit.weeks.singular={0}/सप्ताह
luckperms.duration.unit.weeks.short={0}वू
luckperms.duration.unit.days.plural={0} दिन
luckperms.duration.unit.days.singular={0} दिन
luckperms.duration.unit.days.short={0}डी
luckperms.duration.unit.hours.plural={0} घंटे
luckperms.duration.unit.hours.singular={0} घंटे
luckperms.duration.unit.hours.short={0}एच
luckperms.duration.unit.minutes.plural={0} मिनट
luckperms.duration.unit.minutes.singular={0} मिनट
luckperms.duration.unit.minutes.short={0}एम
luckperms.duration.unit.seconds.plural={0} सेकंड
luckperms.duration.unit.seconds.singular={0} दूसरी
luckperms.duration.unit.seconds.short={0}एम
luckperms.duration.since={0} पहले
luckperms.command.misc.invalid-code=अमान्य कोड
luckperms.command.misc.response-code-key=रेस्पोंस कोड
luckperms.command.misc.error-message-key=मैसेज
luckperms.command.misc.bytebin-unable-to-communicate=बाइटबिन के साथ संवाद करने में असमर्थ
luckperms.command.misc.webapp-unable-to-communicate=वेब ऐप के साथ संवाद नहीं कर सकता
luckperms.command.misc.check-console-for-errors=त्रुटियों के लिए कंसोल देखें
luckperms.command.misc.file-must-be-in-data=फ़ाइल {0} डेटा निर्देशिका का प्रत्यक्ष चाइल्ड होना चाहिए
luckperms.command.misc.wait-to-finish=कृपया इसके समाप्त होने की प्रतीक्षा करें और पुनः प्रयास करें
luckperms.command.misc.invalid-priority=अमान्य प्राथमिकता {0}
luckperms.command.misc.expected-number=अपेक्षित संख्या
luckperms.command.misc.date-parse-error=तारीख {0} को पार्स नहीं किया जा सका
luckperms.command.misc.date-in-past-error=आप अतीत में कोई तिथि निर्धारित नहीं कर सकते हैं\!
luckperms.command.misc.page={1} का पृष्ठ {0}
luckperms.command.misc.page-entries={0} प्रविष्टियां
luckperms.command.misc.none=कोई भी नहीं
luckperms.command.misc.loading.error.unexpected=एक अप्रत्याशित गड़बड़ी हुई है\!
luckperms.command.misc.loading.error.user=उपयोगकर्ता लोड नहीं हुआ
luckperms.command.misc.loading.error.user-specific=लक्षित उपयोगकर्ता लोड करने में असमर्थ {0}
luckperms.command.misc.loading.error.user-not-found={0} नामक कंप्यूटर नहीं मिल सका।
luckperms.command.misc.loading.error.user-save-error={0} के लिए उपयोगकर्ता डेटा सहेजते समय एक त्रुटि हुई थी
luckperms.command.misc.loading.error.user-not-online=उपयोगकर्ता {0} ऑनलाइन नहीं है
luckperms.command.misc.loading.error.user-invalid=''{0}'' कोई मान्य उपयोगकर्ता नाम नहीं है।
luckperms.command.misc.loading.error.user-not-uuid=लक्षित उपयोगकर्ता {0} मान्य uuid नहीं है
luckperms.command.misc.loading.error.group=समूह लोड नहीं हुआ
luckperms.command.misc.loading.error.all-groups=सभी समूहों को लोड करने में असमर्थ
luckperms.command.misc.loading.error.group-not-found={0} नाम का एक समूह नहीं मिला
luckperms.command.misc.loading.error.group-save-error={0} के लिए समूह डेटा सहेजते समय एक त्रुटि हुई
luckperms.command.misc.loading.error.group-invalid={0} एक मान्य समय नहीं है
luckperms.command.misc.loading.error.track=समूह लोड नहीं हुआ
luckperms.command.misc.loading.error.all-tracks=सभी समूहों को लोड करने में असमर्थ
luckperms.command.misc.loading.error.track-not-found={0} नाम का एक समूह नहीं मिला
luckperms.command.misc.loading.error.track-save-error={0} के लिए समूह डेटा सहेजते समय एक त्रुटि हुई
luckperms.command.misc.loading.error.track-invalid={0} एक मान्य समय नहीं है
luckperms.command.editor.no-match=संपादक खोलने में असमर्थ, कोई भी वस्तु वांछित प्रकार से मेल नहीं खाती
luckperms.command.editor.start=एक नया संपादक सत्र तैयार कर रहा है, कृपया प्रतीक्षा करें...
luckperms.command.editor.url=संपादक के पास जाने के लिए कृपया नीचे दिए गए लिंक पर क्लिक करें
luckperms.command.editor.unable-to-communicate=वेब ऐप के साथ संवाद नहीं कर सकता
luckperms.command.editor.apply-edits.success=वेब संपादक डेटा को {0} {1} पर सफलतापूर्वक लागू किया गया
luckperms.command.editor.apply-edits.success-summary={0} {1} और {2} {3}
luckperms.command.editor.apply-edits.success.additions=अतिरिक्त\:
luckperms.command.editor.apply-edits.success.additions-singular=अतिरिक्त\:
luckperms.command.editor.apply-edits.success.deletions=मिटाना
luckperms.command.editor.apply-edits.success.deletions-singular=मिटाना
luckperms.command.editor.apply-edits.no-changes=वेब संपादक से कोई परिवर्तन लागू नहीं किया गया था, लौटाए गए डेटा में नहीं था और इसमें कोई संपादन नहीं था
luckperms.command.editor.apply-edits.unknown-type=निर्दिष्ट वस्तु प्रकार में संपादन लागू करने में असमर्थ
luckperms.command.editor.apply-edits.unable-to-read=दिए गए कोड का उपयोग करके डेटा पढ़ने में असमर्थ
luckperms.command.search.searching.permission={0} वाले उपयोगकर्ताओं और समूहों को खोजना
luckperms.command.search.searching.inherit={0} वाले उपयोगकर्ताओं और समूहों को खोजना
luckperms.command.search.result={1} उपयोगकर्ताओं और {2} समूहों से {0} प्रविष्टियां मिलीं
luckperms.command.search.result.default-notice=नोट\: डिफ़ॉल्ट समूह के सदस्यों की खोज करते समय, बिना किसी अन्य अनुमति वाले ऑफ़लाइन खिलाड़ी नहीं दिखाए जाएंगे\!
luckperms.command.search.showing-users=प्रविष्टियाँ दिखा रहा है
luckperms.command.search.showing-groups=प्रविष्टियाँ दिखा रहा है
luckperms.command.tree.start=अनुमति ट्री जनरेट कर रहा है, कृपया प्रतीक्षा करें...
luckperms.command.tree.empty=पेड़ नहीं मिला, कोई परिणाम नहीं मिला
luckperms.command.tree.url=अनुमति ट्री URL
luckperms.command.verbose.invalid-filter={0} मान्य वर्बोज़ फ़िल्टर नहीं है
luckperms.command.verbose.enabled=मिलान वाले चेकों के लिए वर्बोज़ लॉगिंग {0} {1}
luckperms.command.verbose.command-exec={0} को आदेश {1} निष्पादित करने के लिए बाध्य करना और किए गए सभी चेकों की रिपोर्ट करना...
luckperms.command.verbose.off=वर्बोज़ लॉगिंग {0}
luckperms.command.verbose.command-exec-complete=कमांड निष्पादन पूर्ण
luckperms.command.verbose.command.no-checks=कमांड का निष्पादन पूरा हुआ, लेकिन कोई अनुमति जांच नहीं की गई
luckperms.command.verbose.command.possibly-async=ऐसा इसलिए हो सकता है क्योंकि प्लगइन बैकग्राउंड में कमांड चलाता है (async)
luckperms.command.verbose.command.try-again-manually=आप अभी भी इस तरह से किए गए चेक का पता लगाने के लिए मैन्युअल रूप से वर्बोज़ का उपयोग कर सकते हैं
luckperms.command.verbose.enabled-recording=मिलान वाले चेकों के लिए वर्बोज़ लॉगिंग {0} {1}
luckperms.command.verbose.uploading=वर्बोज़ लॉगिंग {0}, परिणाम अपलोड कर रहा है...
luckperms.command.verbose.url=वर्बोज़ परिणाम URL
luckperms.command.verbose.enabled-term=सक्षम
luckperms.command.verbose.disabled-term=विकलांग
luckperms.command.verbose.query-any=कोई
luckperms.command.info.running-plugin=चल रहे ऐप्लिकेशन
luckperms.command.info.platform-key=मंच (Automatic Translation)
luckperms.command.info.server-brand-key=सर्वर ब्रांड
luckperms.command.info.server-version-key=सर्वर संस्करण
luckperms.command.info.storage-key=संग्रहण
luckperms.command.info.storage-type-key=प्रकार
luckperms.command.info.storage.meta.split-types-key=प्रकार
luckperms.command.info.storage.meta.ping-key=पिंग
luckperms.command.info.storage.meta.connected-key=कनेक्ट हो गया
luckperms.command.info.storage.meta.file-size-key=फ़ाइल आकार\:
luckperms.command.info.extensions-key=एक्सटेनशन
luckperms.command.info.messaging-key=संदेश
luckperms.command.info.instance-key=इंस्टेंसेस
luckperms.command.info.static-contexts-key=स्थिर संदर्भ
luckperms.command.info.online-players-key=ऑनलाइन खिलाड़ियों
luckperms.command.info.online-players-unique={0} अद्वितीय
luckperms.command.info.uptime-key=अपटाइम
luckperms.command.info.local-data-key=स्थानीय डाटा
luckperms.command.info.local-data={0} उपयोगकर्ता, {1} समूह, {2} ट्रैक
luckperms.command.generic.create.success={0} स्नैपशॉट सफलतापूर्वक बनाया गया
luckperms.command.generic.create.error={0} को इनस्टॉल करते समय कोई त्रुटि हुई।
luckperms.command.generic.create.error-already-exists={0} पहले ही अस्तित्वमान है।
luckperms.command.generic.delete.success={0} स्नैपशॉट सफलतापूर्वक बनाया गया
luckperms.command.generic.delete.error={0} को इनस्टॉल करते समय कोई त्रुटि हुई।
luckperms.command.generic.delete.error-doesnt-exist={0} मौजूद नहीं है
luckperms.command.generic.rename.success={0} का सफलतापूर्वक नाम बदलकर {1} कर दिया गया
luckperms.command.generic.clone.success={0} का सफलतापूर्वक नाम बदलकर {1} कर दिया गया
luckperms.command.generic.info.parent.title=पेरेन्ट समूह
luckperms.command.generic.info.parent.temporary-title=अस्थायी अभिभावक समूह
luckperms.command.generic.info.expires-in=समाप्ति\:
luckperms.command.generic.info.inherited-from=से प्रवेश करता है
luckperms.command.generic.info.inherited-from-self=स्वयं
luckperms.command.generic.show-tracks.title={0} के ट्रैक
luckperms.command.generic.show-tracks.empty={0} किसी ट्रैक पर नहीं है
luckperms.command.generic.clear.node-removed={0} नोड्स हटा दिए गए थे\nसंदर्भ\:
luckperms.command.generic.clear.node-removed-singular={0} नोड हटा दिया गया
luckperms.command.generic.clear={0} के नोड्स को संदर्भ {1} में साफ़ कर दिया गया था
luckperms.command.generic.permission.info.title={0} की अनुमति
luckperms.command.generic.permission.info.empty={0} के पास कोई अनुमति सेट नहीं है
luckperms.command.generic.permission.info.click-to-remove=इस नोड को {0} से हटाएं
luckperms.command.generic.permission.check.info.title={0} के लिए अनुमति जानकारी
luckperms.command.generic.permission.check.info.directly={0} ने {3} के संदर्भ में {1} को {2} पर सेट किया है
luckperms.command.generic.permission.check.info.inherited={0} इनहेरिट करता है {1} संदर्भ {4} में {3} से {2} पर सेट
luckperms.command.generic.permission.check.info.not-directly={0} में {1} सेट नहीं है
luckperms.command.generic.permission.check.info.not-inherited={0} इनहेरिट नहीं करते {1}
luckperms.command.generic.permission.check.result.title={0} के लिए अनुमति की जाँच करना
luckperms.command.generic.permission.check.result.result-key=नतीजे
luckperms.command.generic.permission.check.result.processor-key=प्रोसेसर
luckperms.command.generic.permission.check.result.cause-key=कारण
luckperms.command.generic.permission.check.result.context-key=प्रसंग
luckperms.command.generic.permission.set={3} के संदर्भ में {2} के लिए {0} से {1} पर सेट करें
luckperms.command.generic.permission.already-has={0} पहले से ही {1} संदर्भ में सेट है {2}
luckperms.command.generic.permission.set-temp=संदर्भ {4} पर {3} के समय के लिए {2} के लिए {0} से {1} पर सेट करें
luckperms.command.generic.permission.already-has-temp={0} यह पहले से ही {1} अस्थायी रूप से संदर्भ में सेट है {2}
luckperms.command.generic.permission.unset=संदर्भ {2} में {1} के लिए {0} को अनसेट करें
luckperms.command.generic.permission.doesnt-have={0} पहले से ही {1} संदर्भ में सेट है {2}
luckperms.command.generic.permission.unset-temp={1} के संदर्भ में {2} के लिए अस्थायी अनुमति {0} को अनसेट करें
luckperms.command.generic.permission.subtract=संदर्भ {4} पर {3} के समय के लिए {2} के लिए {0} से {1} पर सेट करें
luckperms.command.generic.permission.doesnt-have-temp={0} यह पहले से ही {1} अस्थायी रूप से संदर्भ में सेट है {2}
luckperms.command.generic.permission.clear={0} के नोड्स को संदर्भ {1} में साफ़ कर दिया गया था
luckperms.command.generic.parent.info.title={0}''s अभिभावक
luckperms.command.generic.parent.info.empty={0} के पास कोई अनुमति सेट नहीं है
luckperms.command.generic.parent.info.click-to-remove=इस नोड को {0} से हटाएं
luckperms.command.generic.parent.add={0} अब {1} के संदर्भ में {2} से अनुमतियां प्राप्त करता है
luckperms.command.generic.parent.add-temp={0} अब {1} से {2} की अवधि के लिए संदर्भ {3} की अनुमतियां प्राप्त करता है
luckperms.command.generic.parent.set={0} ने अपने मौजूदा मूल समूहों को साफ़ कर दिया था, और अब केवल {1} संदर्भ {2} में विरासत में मिला है
luckperms.command.generic.parent.set-track={0} ने अपने मौजूदा मूल समूहों को ट्रैक पर {1} साफ़ कर दिया था, और अब केवल {2} संदर्भ {3} में विरासत में मिला है
luckperms.command.generic.parent.remove={0} अब {1} से {2} संदर्भ में अनुमतियां प्राप्त नहीं करता है
